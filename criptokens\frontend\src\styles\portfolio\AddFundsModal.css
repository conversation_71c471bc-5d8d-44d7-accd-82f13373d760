/* Estilos para el modal de añadir fondos */

.add-funds-modal {
  background: var(--bg-dark);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  border: var(--border-light);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-bright);
}

.close-button {
  background: transparent;
  border: none;
  color: var(--text-dim);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.close-button:hover {
  color: var(--text-bright);
}

.modal-content {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-medium);
}

.amount-input-group {
  display: flex;
  gap: 0.5rem;
}

.amount-input {
  flex: 1;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  color: var(--text-bright);
  font-size: 0.9375rem;
}

.currency-select {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  color: var(--text-bright);
  font-size: 0.9375rem;
  cursor: pointer;
}

.payment-methods {
  margin-bottom: 1.5rem;
}

.payment-methods h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-normal);
}

.payment-option:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.payment-option input[type="radio"] {
  accent-color: var(--primary);
}

.payment-option label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: var(--text-medium);
  font-weight: 500;
  width: 100%;
}

.payment-option i {
  font-size: 1.25rem;
  color: var(--text-bright);
}

.error-message {
  background-color: rgba(255, 0, 0, 0.1);
  color: var(--error);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 0, 0, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.cancel-button,
.add-button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.cancel-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.add-button {
  background: var(--gradient-primary);
  color: var(--text-bright);
  border: none;
  box-shadow: var(--shadow-sm);
}

.add-button:hover:not(:disabled) {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-2px);
}

.add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-button.submitting {
  opacity: 0.7;
  position: relative;
}

/* Estilos para el mensaje de éxito */
.success-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.success-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #00c853;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.success-message h3 {
  color: var(--text-bright);
  margin: 0.5rem 0;
}

.success-message p {
  color: var(--text-medium);
  margin: 0.5rem 0;
}

/* Estilos para mostrar el balance actual */
.current-balance {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  margin-bottom: 1.5rem;
}

.balance-label {
  color: var(--text-medium);
  font-size: 0.9rem;
}

.balance-value {
  color: var(--text-bright);
  font-weight: bold;
  font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .add-funds-modal {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header,
  .modal-content {
    padding: 1.25rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .cancel-button,
  .add-button {
    width: 100%;
  }
}
