import React, { useState, useEffect, useRef, useContext } from 'react';
import { Link } from 'react-router-dom';
import { useCryptoGuru } from '../hooks/useCryptoGuru';
import { useAuth } from '../context/NewAuthContext';
import { AvatarStatusContext } from './Layout';
import { askGuruWithPortfolio } from '../services/backendPortfolio.service';
import CriptoAgentAvatar from './CriptoAgentAvatarExport'; // Avatar 2D con animaciones expresivas
// import Avatar3D from './Avatar3D'; // Comentado temporalmente mientras se mejora el avatar 2D
import anime from '../utils/animeUtils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import WebPageView from './WebPageView'; // Componente para mostrar páginas web visualizadas
import TechnicalAnalysisCard from './TechnicalAnalysisCard'; // Componente para mostrar análisis técnico
import { EnhancedTechnicalAnalysis } from './technical'; // Componente mejorado para análisis técnico
import { FundamentalAnalysisCard } from './fundamental'; // Componente para mostrar análisis fundamental
import { getHistoricalDataWithFallback } from '../services/historicalData.service'; // Servicio para datos históricos
import { WebPageData } from '../types/WebPageData'; // Tipo para los datos de páginas web
import VoiceControls from './VoiceControls'; // Componente para controles de voz
import * as ultravoxService from '../services/ultravox.service'; // Servicio para interactuar con Ultravox
import '../styles/GuruChatInterface.css';

// Definición de tipos para la Web Speech API
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

// Nota: La importación del cliente MCP se ha eliminado porque el paquete no está disponible
// Usaremos el backend como alternativa

interface NewsResult {
  title: string;
  url: string;
  description: string;
  publishedDate: string;
  source: string;
}

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'ai';
  isStreaming?: boolean;
  cryptoData?: any;
  newsData?: {
    topic: string;
    results: NewsResult[];
  };
  imageUrl?: string; // URL de la imagen generada
  imageDescription?: string; // Descripción de la imagen generada
  webPageData?: WebPageData; // Datos de una página web visualizada
  technicalAnalysis?: any; // Datos del análisis técnico
  fundamentalAnalysis?: any; // Datos del análisis fundamental
  timestamp?: string;
}

// Interfaz para el historial de conversación que se enviará al backend
interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface GuruChatInterfaceProps {
  conversationId: string | null;
  onTopicChange?: (topic: string | null) => void;
}

const GuruChatInterface: React.FC<GuruChatInterfaceProps> = ({ conversationId, onTopicChange }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [historicalData, setHistoricalData] = useState<Record<string, any[]>>({});
  // Usar el contexto global para el estado del avatar
  const { avatarStatus, setAvatarStatus } = useContext(AvatarStatusContext);

  // Estado para rastrear el estado anterior y asegurar que vuelva a 'idle'
  const [previousStatus, setPreviousStatus] = useState<string>('idle');
  const [resetTimerRef, setResetTimerRef] = useState<NodeJS.Timeout | null>(null);
  const [currentCryptoId, setCurrentCryptoId] = useState<string | null>(null);
  const [hasPortfolioData, setHasPortfolioData] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechSupported, setSpeechSupported] = useState(true);
  const [voiceVolume, setVoiceVolume] = useState(0.8);
  const [voiceSpeed, setVoiceSpeed] = useState(1.0);
  const [ultravoxCallId, setUltravoxCallId] = useState<string | null>(null);
  const [ultravoxEnabled, setUltravoxEnabled] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const recognitionRef = useRef<any>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Obtener el usuario autenticado
  const { currentUser } = useAuth();

  // Cargar mensajes de una conversación existente
  useEffect(() => {
    const loadConversation = async () => {
      if (!currentUser || !conversationId) {
        setMessages([]);
        setConversationHistory([]);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`http://localhost:3008/api/conversations/${conversationId}?userId=${currentUser.uid}`);

        if (!response.ok) {
          throw new Error(`Error al cargar conversación: ${response.statusText}`);
        }

        const conversation = await response.json();

        if (conversation.messages && conversation.messages.length > 0) {
          // Convertir mensajes de Firestore al formato de la interfaz
          const formattedMessages: Message[] = conversation.messages.map((msg: any, index: number) => ({
            id: index,
            text: msg.content,
            sender: msg.role === 'user' ? 'user' : 'ai',
            timestamp: new Date(msg.timestamp?.seconds * 1000).toLocaleTimeString()
          }));

          // Actualizar el estado de mensajes
          setMessages(formattedMessages);

          // Actualizar el historial de conversación
          const historyMessages: ConversationMessage[] = conversation.messages.map((msg: any) => ({
            role: msg.role,
            content: msg.content
          }));

          setConversationHistory(historyMessages);
        } else {
          setMessages([]);
          setConversationHistory([]);
        }
      } catch (error: any) {
        console.error('Error al cargar conversación:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadConversation();
  }, [currentUser, conversationId]);

  // Usar nuestro hook personalizado
  const {
    data: cryptoData,
    loading: cryptoLoading,
    getCryptoData,
    formatPrice,
    formatChange,
    formatVolume,
    getAvatarStatus
  } = useCryptoGuru();

  // Efecto para desplazarse al final de los mensajes
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Efecto para manejar el reseteo del estado del avatar
  useEffect(() => {
    // Si el estado cambia, guardar el estado anterior
    if (avatarStatus !== previousStatus) {
      setPreviousStatus(avatarStatus);

      // Limpiar cualquier temporizador existente
      if (resetTimerRef) {
        clearTimeout(resetTimerRef);
        setResetTimerRef(null);
      }

      // Si el estado no es 'idle' o 'neutral', configurar un temporizador para volver a 'idle'
      if (avatarStatus !== 'idle' && avatarStatus !== 'neutral') {
        const timer = setTimeout(() => {
          setAvatarStatus('idle');
        }, 10000); // 10 segundos

        setResetTimerRef(timer);
      }
    }

    // Limpiar el temporizador al desmontar
    return () => {
      if (resetTimerRef) {
        clearTimeout(resetTimerRef);
      }
    };
  }, [avatarStatus, previousStatus, resetTimerRef, setAvatarStatus]);

  // Efecto para animar la entrada de mensajes (simplificado)
  useEffect(() => {
    if (messages.length > 0) {
      anime({
        targets: '.guru-message:last-child',
        translateY: [10, 0],
        opacity: [0.8, 1],
        easing: 'easeOutSine',
        duration: 300
      });
    }
  }, [messages]);

  // Efecto para inicializar la integración con Ultravox
  useEffect(() => {
    const initUltravox = async () => {
      try {
        // Verificar el estado de la API de Ultravox
        const status = await ultravoxService.checkApiStatus();
        console.log('Estado de Ultravox API:', status);

        if (status.success && status.isValid) {
          setUltravoxEnabled(true);

          // Iniciar una llamada si no hay una activa
          if (!ultravoxCallId) {
            const callResult = await ultravoxService.startCall('es');
            console.log('Llamada iniciada:', callResult);

            if (callResult.success && callResult.call) {
              setUltravoxCallId(callResult.call.id);

              // Reproducir mensaje de bienvenida
              playWelcomeMessage(callResult.call.id);
            }
          }
        } else {
          console.warn('Ultravox API no disponible, usando fallback local');
          setUltravoxEnabled(false);
        }
      } catch (error) {
        console.error('Error inicializando Ultravox:', error);
        setUltravoxEnabled(false);
      }
    };

    initUltravox();

    // Limpiar al desmontar
    return () => {
      // Finalizar la llamada si hay una activa
      if (ultravoxCallId) {
        ultravoxService.endCall(ultravoxCallId)
          .then(result => console.log('Llamada finalizada:', result))
          .catch(error => console.error('Error finalizando llamada:', error));
      }
    };
  }, []);

  // Función para reproducir el mensaje de bienvenida
  const playWelcomeMessage = async (callId: string) => {
    try {
      // Obtener los mensajes de la llamada
      const messagesResult = await ultravoxService.getMessages(callId);

      if (messagesResult.success && messagesResult.messages && messagesResult.messages.length > 0) {
        // Buscar el mensaje de bienvenida
        const welcomeMessage = messagesResult.messages.find(msg => msg.role === 'assistant');

        if (welcomeMessage) {
          // Reproducir el mensaje de bienvenida
          setIsSpeaking(true);
          await ultravoxService.playAudio(
            welcomeMessage.content,
            undefined,
            {
              volume: voiceVolume,
              speed: voiceSpeed,
              onStart: () => {
                setIsSpeaking(true);
                setAvatarStatus('speaking');
              },
              onEnd: () => {
                setIsSpeaking(false);
                setAvatarStatus('idle');
              },
              onError: (error) => {
                console.error('Error reproduciendo audio:', error);
                setIsSpeaking(false);
                setAvatarStatus('idle');
              }
            }
          );
        }
      }
    } catch (error) {
      console.error('Error reproduciendo mensaje de bienvenida:', error);
    }
  };

  // Efecto para inicializar el reconocimiento de voz
  useEffect(() => {
    // Verificar si el navegador soporta la API de reconocimiento de voz
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.warn('El reconocimiento de voz no está soportado en este navegador');
      setSpeechSupported(false);
      return;
    }

    // Crear una instancia de reconocimiento de voz
    const recognition = new SpeechRecognition();

    // Configurar opciones
    recognition.lang = 'es-ES'; // Idioma español
    recognition.continuous = false; // No continuo, se detiene después de un periodo de silencio
    recognition.interimResults = false; // Solo resultados finales

    // Manejar el evento de resultado
    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      setInputMessage(transcript);
      setIsListening(false);

      // Enviar el mensaje automáticamente si hay texto
      if (transcript.trim()) {
        setTimeout(() => {
          handleSendMessage(transcript);
        }, 500);
      }
    };

    // Manejar el evento de error
    recognition.onerror = (event: any) => {
      console.error('Error en el reconocimiento de voz:', event.error);
      setIsListening(false);
    };

    // Manejar el evento de fin
    recognition.onend = () => {
      setIsListening(false);
    };

    // Guardar la referencia
    recognitionRef.current = recognition;

    // Limpiar al desmontar
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (e) {
          // Ignorar errores al detener (puede que ya esté detenido)
        }
      }
    };
  }, []);

  // Función para procesar comandos especiales y detectar el tema de la conversación
  const processSpecialCommands = async (message: string) => {
    const lowerMessage = message.toLowerCase();

    // Buscar patrones de consulta de precio
    const priceRegex = /(precio|price|valor|cotización|cotizacion|cuánto vale|cuanto vale|how much is) (de |del |of |for )?([a-zA-Z0-9]+)/i;
    const priceMatch = lowerMessage.match(priceRegex);

    if (priceMatch && priceMatch[3]) {
      const cryptoId = mapCryptoNameToId(priceMatch[3]);
      setCurrentCryptoId(cryptoId);
      setAvatarStatus('thinking');

      // Actualizar el tema de la conversación
      if (onTopicChange) {
        onTopicChange(cryptoId);
      }

      try {
        await getCryptoData(cryptoId);
        return { processedMessage: message, commandType: 'price', cryptoId };
      } catch (error) {
        console.error('Error al obtener datos de criptomoneda:', error);
        return { processedMessage: message, commandType: null };
      }
    }

    // Buscar patrones de consulta de noticias (para usar con Brave Search MCP)
    const newsRegex = /(noticias|news|novedades|actualidad|qué hay de nuevo|que hay de nuevo) (sobre |about |de )?([a-zA-Z0-9]+)/i;
    const newsMatch = lowerMessage.match(newsRegex);

    if (newsMatch && newsMatch[3]) {
      const topic = newsMatch[3];
      setAvatarStatus('thinking');

      // Actualizar el tema de la conversación a noticias
      if (onTopicChange) {
        onTopicChange('news_summary');
      }

      try {
        // Nota: La integración directa con el cliente MCP se ha eliminado
        // porque el paquete no está disponible en npm
        console.log('Usando el endpoint del backend para noticias...');

        // Fallback: Realizar petición al backend para buscar noticias
        const response = await fetch('http://localhost:3001/api/guru/news', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({ topic })
        });

        if (!response.ok) {
          throw new Error(`Error en la petición: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return { processedMessage: message, commandType: 'news', newsResults: data.results, topic };
      } catch (error) {
        console.error('Error al buscar noticias:', error);
        return { processedMessage: message, commandType: null };
      }
    }

    // Buscar patrones de consulta sobre el mercado general
    const marketRegex = /(mercado|market|tendencia|trend|general|global|panorama|overview)/i;
    const marketMatch = lowerMessage.match(marketRegex);

    if (marketMatch) {
      // Actualizar el tema de la conversación a mercado general
      if (onTopicChange) {
        onTopicChange('general_market');
      }
    }

    return { processedMessage: message, commandType: null };
  };

  // Función para detectar el tema de la conversación basado en el mensaje
  const detectConversationTopic = (message: string) => {
    if (!onTopicChange) return;

    const lowerMessage = message.toLowerCase();

    // Detectar menciones de criptomonedas específicas
    const cryptoRegex = /(bitcoin|btc|ethereum|eth|solana|sol|cardano|ada|ripple|xrp|dogecoin|doge|polkadot|dot|litecoin|ltc|chainlink|link|avalanche|avax|polygon|matic|shiba inu|shib)/i;
    const cryptoMatch = lowerMessage.match(cryptoRegex);

    if (cryptoMatch) {
      const cryptoId = mapCryptoNameToId(cryptoMatch[1]);
      onTopicChange(cryptoId);
      return;
    }

    // Detectar menciones de noticias
    const newsRegex = /(noticias|news|novedades|actualidad|qué hay de nuevo|que hay de nuevo)/i;
    const newsMatch = lowerMessage.match(newsRegex);

    if (newsMatch) {
      onTopicChange('news_summary');
      return;
    }

    // Detectar menciones del mercado general
    const marketRegex = /(mercado|market|tendencia|trend|general|global|panorama|overview)/i;
    const marketMatch = lowerMessage.match(marketRegex);

    if (marketMatch) {
      onTopicChange('general_market');
      return;
    }
  };

  // Función para mapear nombres comunes de criptomonedas a sus IDs
  const mapCryptoNameToId = (name: string): string => {
    const normalizedName = name.toLowerCase().trim();

    const cryptoMap: {[key: string]: string} = {
      'btc': 'bitcoin',
      'eth': 'ethereum',
      'sol': 'solana',
      'bnb': 'binancecoin',
      'xrp': 'ripple',
      'ada': 'cardano',
      'doge': 'dogecoin',
      'dot': 'polkadot',
      'ltc': 'litecoin',
      'link': 'chainlink',
      'avax': 'avalanche-2',
      'matic': 'polygon',
      'shib': 'shiba-inu'
    };

    return cryptoMap[normalizedName] || normalizedName;
  };

  // Función para manejar el envío de mensajes
  const handleSendMessage = async (messageText?: string) => {
    const message = messageText || inputMessage;
    if (!message.trim()) return;

    // Detectar tema de la conversación basado en el mensaje
    detectConversationTopic(message);

    // Procesar comandos especiales
    const { processedMessage, commandType, cryptoId, newsResults, topic } = await processSpecialCommands(message);

    // Añadir mensaje del usuario a la conversación
    const userMessage: Message = {
      id: Date.now(),
      text: processedMessage,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString()
    };

    // Actualizar el historial de conversación para el backend
    const userConversationMessage: ConversationMessage = {
      role: 'user',
      content: processedMessage
    };

    // Actualizar el estado de mensajes y el historial de conversación
    setMessages(prevMessages => [...prevMessages, userMessage]);
    setConversationHistory(prevHistory => [...prevHistory, userConversationMessage]);
    setInputMessage('');
    setIsLoading(true);
    setAvatarStatus('thinking');

    // Guardar el mensaje en Firestore si hay una conversación activa
    if (currentUser && conversationId) {
      try {
        await fetch(`http://localhost:3001/api/conversations/${conversationId}/messages?userId=${currentUser.uid}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            role: 'user',
            content: processedMessage,
            userId: currentUser.uid
          }),
          // No incluir credenciales para evitar problemas de CORS
          // credentials: 'include'
        });
      } catch (error: any) {
        console.error('Error al guardar mensaje del usuario:', error);
      }
    }

    // Si Ultravox está habilitado, enviar el mensaje a través de la API
    if (ultravoxEnabled && ultravoxCallId) {
      try {
        console.log(`Enviando mensaje a Ultravox: ${processedMessage}`);
        const result = await ultravoxService.sendMessage(ultravoxCallId, processedMessage);

        if (result.success && result.response) {
          console.log('Respuesta de Ultravox:', result.response);

          // Añadir la respuesta a la conversación
          const aiMessage: Message = {
            id: Date.now() + 1,
            text: result.response.content,
            sender: 'ai',
            timestamp: new Date().toLocaleTimeString()
          };

          // Actualizar el historial de conversación para el backend
          const aiConversationMessage: ConversationMessage = {
            role: 'assistant',
            content: result.response.content
          };

          // Actualizar el estado de mensajes y el historial de conversación
          setMessages(prevMessages => [...prevMessages, aiMessage]);
          setConversationHistory(prevHistory => [...prevHistory, aiConversationMessage]);

          // Reproducir la respuesta en audio
          if (result.response.audioUrl) {
            // Reproducir directamente desde la URL
            const audio = new Audio(result.response.audioUrl);
            audioRef.current = audio;

            audio.onplay = () => {
              setIsSpeaking(true);
              setAvatarStatus('speaking');
            };

            audio.onended = () => {
              setIsSpeaking(false);
              setAvatarStatus('idle');
              setIsLoading(false);
            };

            audio.onerror = (error) => {
              console.error('Error reproduciendo audio:', error);
              setIsSpeaking(false);
              setAvatarStatus('idle');
              setIsLoading(false);

              // Intentar reproducir con el servicio de audio
              playResponseWithService(result.response.content);
            };

            // Establecer el volumen
            audio.volume = voiceVolume;

            // Reproducir el audio
            audio.play().catch(error => {
              console.error('Error iniciando reproducción de audio:', error);
              // Intentar reproducir con el servicio de audio
              playResponseWithService(result.response.content);
            });
          } else {
            // Si no hay URL de audio, usar el servicio de audio
            playResponseWithService(result.response.content);
          }

          // Guardar la respuesta en Firestore si hay una conversación activa
          if (currentUser && conversationId) {
            try {
              await fetch(`http://localhost:3001/api/conversations/${conversationId}/messages?userId=${currentUser.uid}`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  role: 'assistant',
                  content: result.response.content,
                  userId: currentUser.uid
                })
              });
            } catch (error: any) {
              console.error('Error al guardar respuesta del asistente:', error);
            }
          }
        } else {
          console.error('Error en la respuesta de Ultravox:', result.message || 'Error desconocido');
          setIsLoading(false);
        }
      } catch (error: any) {
        console.error('Error enviando mensaje a Ultravox:', error);
        setIsLoading(false);
      }
    }

    // Si es una pregunta sobre análisis técnico y tenemos un cryptoId, obtener datos históricos
    if (cryptoId && (
      processedMessage.toLowerCase().includes('análisis técnico') ||
      processedMessage.toLowerCase().includes('analisis tecnico') ||
      processedMessage.toLowerCase().includes('técnico') ||
      processedMessage.toLowerCase().includes('tecnico')
    )) {
      console.log(`Detectada pregunta de análisis técnico para ${cryptoId}, obteniendo datos históricos...`);

      // Convertir el cryptoId a símbolo
      const symbolMap: Record<string, string> = {
        'bitcoin': 'BTC',
        'ethereum': 'ETH',
        'tether': 'USDT',
        'binancecoin': 'BNB',
        'solana': 'SOL',
        'ripple': 'XRP',
        'cardano': 'ADA',
        'dogecoin': 'DOGE',
        'polkadot': 'DOT',
        'litecoin': 'LTC'
      };

      const symbol = symbolMap[cryptoId] || cryptoId.toUpperCase();

      // Obtener datos históricos en segundo plano
      fetchHistoricalData(symbol).catch(error => {
        console.error(`Error al obtener datos históricos para ${symbol}:`, error);
      });
    }

    try {
      // Si es una consulta de precio y tenemos datos, mostrar respuesta inmediata
      if (commandType === 'price' && cryptoId && cryptoData) {
        // Actualizar el estado del avatar basado en el cambio porcentual
        const avatarStatus = getAvatarStatus(cryptoData.changePercent24Hr);
        setAvatarStatus(avatarStatus);

        // Generar comentario sobre el mercado basado en los datos
        const marketComment = generateMarketComment(cryptoData);

        // Formatear la respuesta con los datos de la criptomoneda
        const priceResponse = `El precio actual de ${cryptoData.name} (${cryptoData.symbol}) es ${formatPrice(cryptoData.price)}. Ha tenido un cambio de ${formatChange(cryptoData.changePercent24Hr)} en las últimas 24 horas, con un volumen de ${formatVolume(cryptoData.volumeUsd24Hr)}. ${marketComment}`;

        // Añadir respuesta inmediata con los datos de precio
        const priceMessage: Message = {
          id: Date.now() + 1,
          text: priceResponse,
          sender: 'ai',
          timestamp: new Date().toLocaleTimeString(),
          cryptoData: {
            type: 'price',
            data: cryptoData
          }
        };

        // Actualizar el historial de conversación para el backend
        const priceConversationMessage: ConversationMessage = {
          role: 'assistant',
          content: priceResponse
        };

        // Actualizar el estado de mensajes y el historial de conversación
        setMessages(prevMessages => [...prevMessages, priceMessage]);
        setConversationHistory(prevHistory => [...prevHistory, priceConversationMessage]);

        // Guardar el mensaje en Firestore si hay una conversación activa
        if (currentUser && conversationId) {
          try {
            await fetch(`http://localhost:3001/api/conversations/${conversationId}/messages?userId=${currentUser.uid}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                role: 'assistant',
                content: priceResponse,
                userId: currentUser.uid
              }),
              // No incluir credenciales para evitar problemas de CORS
              // credentials: 'include'
            });
          } catch (error: any) {
            console.error('Error al guardar mensaje del asistente (precio):', error);
          }
        }
      }

      // Si es una consulta de noticias y tenemos resultados, mostrar respuesta inmediata
      else if (commandType === 'news' && newsResults && topic) {
        setAvatarStatus('neutral');

        // Formatear la respuesta con los resultados de noticias
        const newsIntro = `Aquí tienes las últimas noticias sobre ${topic}:`;

        // Añadir respuesta inmediata con los resultados de noticias
        const newsMessage: Message = {
          id: Date.now() + 1,
          text: newsIntro,
          sender: 'ai',
          timestamp: new Date().toLocaleTimeString(),
          newsData: {
            topic,
            results: newsResults
          }
        };

        // Actualizar el historial de conversación para el backend
        const newsConversationMessage: ConversationMessage = {
          role: 'assistant',
          content: newsIntro
        };

        // Actualizar el estado de mensajes y el historial de conversación
        setMessages(prevMessages => [...prevMessages, newsMessage]);
        setConversationHistory(prevHistory => [...prevHistory, newsConversationMessage]);

        // Guardar el mensaje en Firestore si hay una conversación activa
        if (currentUser && conversationId) {
          try {
            await fetch(`http://localhost:3001/api/conversations/${conversationId}/messages?userId=${currentUser.uid}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              body: JSON.stringify({
                role: 'assistant',
                content: newsIntro,
                userId: currentUser.uid
              })
            });
          } catch (error: any) {
            console.error('Error al guardar mensaje del asistente (noticias):', error);
          }
        }

        setIsLoading(false);
        return; // No necesitamos consultar al LLM para noticias
      }

      // Consultar al backend para obtener una respuesta del LLM
      console.log('Enviando solicitud al backend con historial de conversación...');

      // Usar el nuevo servicio que incluye el ID de usuario para acceder a los datos del portafolio
      let data;
      if (currentUser) {
        console.log(`Enviando solicitud con ID de usuario: ${currentUser.uid}`);
        try {
          // Usar el servicio que incluye el ID de usuario para acceder a los datos del portafolio
          data = await askGuruWithPortfolio(processedMessage, currentUser.uid);
        } catch (error) {
          console.error('Error al usar askGuruWithPortfolio:', error);
          // Fallback al método tradicional
          console.log('Fallback al método tradicional de fetch');
          const response = await fetch('http://localhost:3008/api/adk-agents/guru', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify({
              question: processedMessage,
              userId: currentUser.uid,
              history: conversationHistory
            })
          });

          if (!response.ok) {
            throw new Error(`Error en la petición: ${response.status} ${response.statusText}`);
          }

          data = await response.json();
        }
      } else {
        // Si no hay usuario autenticado, usar el método tradicional
        const response = await fetch('http://localhost:3008/api/adk-agents/guru', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            question: processedMessage,
            history: conversationHistory
          })
        });

        if (!response.ok) {
          throw new Error(`Error en la petición: ${response.status} ${response.statusText}`);
        }

        data = await response.json();
      }

      // El código para verificar response.ok ya está incluido en las secciones anteriores

      // Actualizar el estado si hay datos del portafolio
      if (data.hasPortfolioData) {
        setHasPortfolioData(true);
      }

      // Verificar si la respuesta incluye una URL de imagen generada
      const hasGeneratedImage = !!data.imageUrl;

      // Depurar la respuesta para verificar la URL de la imagen
      console.log('Respuesta del backend:', data);
      console.log('URL de imagen recibida:', data.imageUrl);

      // Si no era una consulta de precio o no teníamos datos, mostrar la respuesta del LLM
      if (!(commandType === 'price' && cryptoId && cryptoData)) {
        // Determinar el estado del avatar basado en el contenido de la respuesta
        // Explicitly type newAvatarStatus to match the state type
        let newAvatarStatus: 'idle' | 'thinking' | 'positive' | 'negative' | 'neutral' | 'concerned' = 'neutral';

        if (data.cryptoData && data.cryptoData.changePercent24Hr) {
          newAvatarStatus = getAvatarStatus(data.cryptoData.changePercent24Hr);
        } else if (data.reply && typeof data.reply === 'string') {
          // Verificar que data.reply es una cadena de texto antes de usar toLowerCase()
          const replyText = data.reply.toLowerCase();
          if (replyText.includes('aument')) {
            newAvatarStatus = 'positive' as 'positive';
          } else if (replyText.includes('disminuy') || replyText.includes('caíd')) {
            newAvatarStatus = 'negative' as 'negative';
          }
        }

        setAvatarStatus(newAvatarStatus);

        // Añadir respuesta del LLM
        const aiMessage: Message = {
          id: Date.now() + 2,
          text: data.reply && typeof data.reply === 'string' ? data.reply : 'Lo siento, no pude generar una respuesta adecuada.',
          sender: 'ai',
          timestamp: new Date().toLocaleTimeString(),
          cryptoData: data.cryptoData ? {
            type: 'analysis',
            data: data.cryptoData
          } : null,
          imageUrl: data.imageUrl, // Añadir URL de imagen si existe
          imageDescription: data.imageDescription, // Añadir descripción de la imagen si existe
          webPageData: data.webPageData, // Añadir datos de página web si existen
          technicalAnalysis: data.technicalAnalysis // Añadir datos de análisis técnico si existen
        };

        // Actualizar el historial de conversación para el backend
        const assistantConversationMessage: ConversationMessage = {
          role: 'assistant',
          content: data.reply && typeof data.reply === 'string' ? data.reply : 'Lo siento, no pude generar una respuesta adecuada.'
        };

        // Actualizar el estado de mensajes y el historial de conversación
        setMessages(prevMessages => [...prevMessages, aiMessage]);
        setConversationHistory(prevHistory => [...prevHistory, assistantConversationMessage]);

        // Guardar el mensaje en Firestore si hay una conversación activa
        if (currentUser && conversationId) {
          try {
            await fetch(`http://localhost:3001/api/conversations/${conversationId}/messages?userId=${currentUser.uid}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              body: JSON.stringify({
                role: 'assistant',
                content: data.reply && typeof data.reply === 'string' ? data.reply : 'Lo siento, no pude generar una respuesta adecuada.',
                userId: currentUser.uid
              })
            });
          } catch (error: any) {
            console.error('Error al guardar mensaje del asistente (LLM):', error);
          }
        }
      } else {
        // Si ya mostramos datos de precio, añadir un análisis adicional del LLM
        const analysisMessage: Message = {
          id: Date.now() + 3,
          text: `**Análisis adicional:** ${data.reply && typeof data.reply === 'string' ? data.reply : 'No hay análisis adicional disponible.'}`,
          sender: 'ai',
          timestamp: new Date().toLocaleTimeString(),
          cryptoData: null
        };

        // Actualizar el historial de conversación para el backend
        const analysisConversationMessage: ConversationMessage = {
          role: 'assistant',
          content: data.reply && typeof data.reply === 'string' ? data.reply : 'No hay análisis adicional disponible.'
        };

        // Actualizar el estado de mensajes y el historial de conversación
        setMessages(prevMessages => [...prevMessages, analysisMessage]);
        setConversationHistory(prevHistory => [...prevHistory, analysisConversationMessage]);

        // Guardar el mensaje en Firestore si hay una conversación activa
        if (currentUser && conversationId) {
          try {
            await fetch(`http://localhost:3001/api/conversations/${conversationId}/messages?userId=${currentUser.uid}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              body: JSON.stringify({
                role: 'assistant',
                content: data.reply && typeof data.reply === 'string' ? data.reply : 'No hay análisis adicional disponible.',
                userId: currentUser.uid
              })
            });
          } catch (error: any) {
            console.error('Error al guardar mensaje del asistente (análisis):', error);
          }
        }
      }
    } catch (error) {
      console.error('Error al obtener respuesta:', error);

      // Mostrar mensaje de error
      const errorMessage: Message = {
        id: Date.now() + 4,
        text: `Lo siento, ha ocurrido un error al procesar tu consulta. Por favor, inténtalo de nuevo más tarde.`,
        sender: 'ai',
        timestamp: new Date().toLocaleTimeString()
      };

      setMessages(prevMessages => [...prevMessages, errorMessage]);
      setAvatarStatus('concerned' as 'concerned');
    } finally {
      setIsLoading(false);
    }
  };


  // Función para manejar la tecla Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Función para reproducir respuestas con el servicio de audio
  const playResponseWithService = async (text: string) => {
    try {
      setIsSpeaking(true);
      setAvatarStatus('speaking');

      await ultravoxService.playAudio(
        text,
        undefined,
        {
          volume: voiceVolume,
          speed: voiceSpeed,
          onStart: () => {
            setIsSpeaking(true);
            setAvatarStatus('speaking');
          },
          onEnd: () => {
            setIsSpeaking(false);
            setAvatarStatus('idle');
            setIsLoading(false);
          },
          onError: (error) => {
            console.error('Error reproduciendo audio:', error);
            setIsSpeaking(false);
            setAvatarStatus('idle');
            setIsLoading(false);
          }
        }
      );
    } catch (error) {
      console.error('Error reproduciendo respuesta con servicio de audio:', error);
      setIsSpeaking(false);
      setAvatarStatus('idle');
      setIsLoading(false);
    }
  };

  // Función para manejar el reconocimiento de voz
  const handleVoiceInput = () => {
    if (!speechSupported) {
      alert('El reconocimiento de voz no está soportado en este navegador');
      return;
    }

    if (isListening) {
      // Detener el reconocimiento si ya está activo
      try {
        recognitionRef.current?.stop();
      } catch (e) {
        console.error('Error al detener el reconocimiento de voz:', e);
      }
      setIsListening(false);
    } else {
      // Iniciar el reconocimiento
      try {
        recognitionRef.current?.start();
        setIsListening(true);
      } catch (e) {
        console.error('Error al iniciar el reconocimiento de voz:', e);
        setIsListening(false);
      }
    }
  };

  // Función para obtener datos históricos de una criptomoneda
  const fetchHistoricalData = async (symbol: string) => {
    try {
      // Si ya tenemos los datos en el estado, no los volvemos a obtener
      if (historicalData[symbol]) {
        console.log(`Usando datos históricos en caché para ${symbol}`);
        return historicalData[symbol];
      }

      console.log(`Obteniendo datos históricos para ${symbol}...`);
      const data = await getHistoricalDataWithFallback(symbol, 30);

      // Guardar los datos en el estado
      setHistoricalData(prev => ({
        ...prev,
        [symbol]: data
      }));

      return data;
    } catch (error) {
      console.error(`Error al obtener datos históricos para ${symbol}:`, error);
      return [];
    }
  };

  // Función para determinar el sentimiento del mercado
  const getMarketSentiment = (changePercent: number | undefined): 'bullish' | 'bearish' | 'neutral' | 'volatile' => {
    if (!changePercent) return 'neutral';

    if (changePercent > 5) return 'bullish';
    if (changePercent < -5) return 'bearish';
    if (Math.abs(changePercent) > 10) return 'volatile';
    return 'neutral';
  };

  // Función para generar comentarios sobre el mercado
  const generateMarketComment = (data: any): string => {
    if (!data || data.changePercent24Hr === undefined) return '';

    const changePercent = data.changePercent24Hr;
    const volume = data.volumeUsd24Hr || 0;
    const name = data.name;
    const symbol = data.symbol;

    // Comentarios positivos
    const bullishComments = [
      `${name} está mostrando una fuerte tendencia alcista.`,
      `Los inversores están muy optimistas sobre el futuro de ${symbol}.`,
      `El sentimiento del mercado para ${name} es claramente positivo.`,
      `${symbol} está superando a muchas otras criptomonedas hoy.`,
      `El interés de compra en ${name} es significativo.`
    ];

    // Comentarios muy positivos
    const veryBullishComments = [
      `¡${name} está experimentando un rally impresionante!`,
      `El mercado está extremadamente alcista para ${symbol} hoy.`,
      `Los traders están acumulando ${symbol} a un ritmo acelerado.`,
      `${name} está en una clara fase de ruptura alcista.`,
      `La demanda de ${symbol} está superando significativamente a la oferta.`
    ];

    // Comentarios negativos
    const bearishComments = [
      `${name} está enfrentando presión de venta en el mercado actual.`,
      `Los inversores están siendo cautelosos con ${symbol}.`,
      `El sentimiento del mercado para ${name} es negativo a corto plazo.`,
      `${symbol} está experimentando una corrección en su precio.`,
      `Hay una disminución en el interés de compra para ${name}.`
    ];

    // Comentarios muy negativos
    const veryBearishComments = [
      `${name} está sufriendo una fuerte presión de venta.`,
      `El mercado está claramente bajista para ${symbol} hoy.`,
      `Los inversores están reduciendo significativamente sus posiciones en ${symbol}.`,
      `${name} está en una tendencia bajista pronunciada.`,
      `La confianza en ${symbol} está disminuyendo rápidamente en el mercado actual.`
    ];

    // Comentarios neutrales
    const neutralComments = [
      `${name} se mantiene relativamente estable en el mercado actual.`,
      `No hay movimientos significativos en el precio de ${symbol}.`,
      `El mercado para ${name} está en un periodo de consolidación.`,
      `Los inversores están esperando señales más claras antes de tomar posiciones en ${symbol}.`,
      `${name} está mostrando una volatilidad limitada en este momento.`
    ];

    // Comentarios sobre volumen alto
    const highVolumeComments = [
      `El volumen de operaciones de ${symbol} es notablemente alto, lo que indica un fuerte interés del mercado.`,
      `${name} está experimentando una actividad de trading significativa.`,
      `El alto volumen de ${symbol} sugiere que hay un gran interés en esta criptomoneda.`
    ];

    // Comentarios sobre volumen bajo
    const lowVolumeComments = [
      `El volumen de operaciones de ${symbol} es relativamente bajo, lo que podría indicar cautela en el mercado.`,
      `${name} está experimentando una actividad de trading limitada.`,
      `El bajo volumen de ${symbol} sugiere que el interés actual es moderado.`
    ];

    // Seleccionar comentario basado en el cambio porcentual y el volumen
    let comments = [];

    // Comentario principal basado en el cambio de precio
    if (changePercent > 10) {
      comments.push(veryBullishComments[Math.floor(Math.random() * veryBullishComments.length)]);
    } else if (changePercent > 3) {
      comments.push(bullishComments[Math.floor(Math.random() * bullishComments.length)]);
    } else if (changePercent < -10) {
      comments.push(veryBearishComments[Math.floor(Math.random() * veryBearishComments.length)]);
    } else if (changePercent < -3) {
      comments.push(bearishComments[Math.floor(Math.random() * bearishComments.length)]);
    } else {
      comments.push(neutralComments[Math.floor(Math.random() * neutralComments.length)]);
    }

    // Comentario adicional sobre el volumen si es relevante
    // Asumiendo que un volumen alto es > 100 millones y bajo es < 10 millones
    if (volume > 100000000) {
      comments.push(highVolumeComments[Math.floor(Math.random() * highVolumeComments.length)]);
    } else if (volume < 10000000) {
      comments.push(lowVolumeComments[Math.floor(Math.random() * lowVolumeComments.length)]);
    }

    return comments.join(' ');
  };

  // Función para determinar la intensidad del cambio de precio
  const getPriceChangeIntensity = (changePercent: number | undefined): 'low' | 'medium' | 'high' | 'extreme' => {
    if (!changePercent) return 'low';

    const absChange = Math.abs(changePercent);
    if (absChange > 15) return 'extreme';
    if (absChange > 10) return 'high';
    if (absChange > 5) return 'medium';
    return 'low';
  };

  // Obtener datos para el avatar
  const avatarCryptoData = cryptoData && currentCryptoId ? {
    id: cryptoData.id,
    name: cryptoData.name,
    symbol: cryptoData.symbol,
    price: cryptoData.price,
    priceUsd: cryptoData.priceUsd,
    changePercent24Hr: cryptoData.changePercent24Hr,
    volumeUsd24Hr: cryptoData.volumeUsd24Hr
  } : undefined;

  const marketSentiment = avatarCryptoData ? getMarketSentiment(avatarCryptoData.changePercent24Hr) : 'neutral';
  const priceChangeIntensity = avatarCryptoData ? getPriceChangeIntensity(avatarCryptoData.changePercent24Hr) : 'low';

  return (
    <div className="guru-chat-container">
      <div className="guru-agent-section">
        <div className="avatar-wrapper">
          <CriptoAgentAvatar
            mood={avatarStatus === 'positive' ? 'bullish' :
                 avatarStatus === 'negative' ? 'bearish' :
                 avatarStatus === 'concerned' ? 'concerned' :
                 avatarStatus === 'thinking' ? 'thinking' :
                 avatarStatus === 'speaking' ? 'neutral' : 'neutral'}
            speaking={avatarStatus === 'thinking' || avatarStatus === 'speaking' || isSpeaking}
            size="large"
            pulseEffect={true}
          />
          <div className="avatar-status-indicator">{avatarStatus}</div>
        </div>
        {avatarStatus === 'thinking' && <div className="thinking-indicator">Procesando...</div>}
        {currentUser && (
          <div className="portfolio-actions">
            {hasPortfolioData ? (
              <div className="portfolio-indicator">Usando datos de tu portafolio</div>
            ) : (
              <div className="portfolio-prompt">
                <span>Para análisis personalizados, </span>
                <Link to="/portfolio" className="portfolio-link">configura tu portafolio</Link>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="guru-conversation-section">
        <div className="guru-messages-container">
          {messages.length === 0 ? (
            <div className="empty-chat">
              <h3>Bienvenido al Gurú Cripto</h3>
              <p>Pregúntame sobre precios, tendencias o información de cualquier criptomoneda.</p>
              <p className="example">Ejemplo: "¿Cuál es el precio de Bitcoin?"</p>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`guru-message ${message.sender === 'user' ? 'user-message' : 'ai-message'}`}
              >
                <div className="message-content">
                  {message.sender === 'ai' ? (
                    <div className="markdown-content">
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {message.text}
                      </ReactMarkdown>
                    </div>
                  ) : (
                    <p>{message.text}</p>
                  )}
                  {message.imageUrl && (
                    <div className="generated-image-container">
                      <img
                        src={message.imageUrl}
                        alt="Imagen generada por IA"
                        className="generated-image"
                        loading="lazy"
                        onError={(e) => {
                          console.error('Error al cargar la imagen:', e);
                          // Establecer una imagen de respaldo en caso de error
                          e.currentTarget.src = 'https://images.unsplash.com/photo-1518546305927-5a555bb7020d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80';
                          e.currentTarget.onerror = null; // Prevenir bucles infinitos
                        }}
                      />
                      <div className="image-caption">
                        Imagen generada por IA
                        <button
                          className="image-description-toggle"
                          onClick={(e) => {
                            e.stopPropagation();
                            const descriptionEl = e.currentTarget.parentElement?.nextElementSibling as HTMLElement;
                            if (descriptionEl) {
                              const isVisible = descriptionEl.style.display !== 'none';
                              descriptionEl.style.display = isVisible ? 'none' : 'block';
                              e.currentTarget.textContent = isVisible ? 'Ver descripción' : 'Ocultar descripción';
                            }
                          }}
                        >
                          Ver descripción
                        </button>
                      </div>
                      <div className="image-description" style={{ display: 'none' }}>
                        {message.imageDescription === 'Ver descripción en el mensaje'
                          ? `Esta imagen muestra un cohete con el símbolo de Bitcoin despegando hacia la luna, representando el potencial de crecimiento y la trayectoria ascendente de esta criptomoneda. La expresión "to the moon" es común en la comunidad cripto para referirse a un aumento significativo en el valor de un activo digital.

La imagen ilustra el concepto de "Bitcoin to the moon", una frase popular entre los entusiastas de las criptomonedas que simboliza la esperanza de un aumento significativo en el valor de Bitcoin.`
                          : message.imageDescription || 'Esta imagen representa el concepto solicitado.'}
                      </div>
                    </div>
                  )}
                  {message.cryptoData && message.cryptoData.type === 'price' && (
                    <div className="crypto-data-card">
                      <div className="crypto-data-header">
                        <img src={message.cryptoData.data.image} alt={message.cryptoData.data.name} className="crypto-icon" />
                        <h4>{message.cryptoData.data.name} ({message.cryptoData.data.symbol})</h4>
                      </div>
                      <div className="crypto-data-details">
                        <div className="crypto-data-item">
                          <span className="label">Precio:</span>
                          <span className="value">{formatPrice(message.cryptoData.data.price)}</span>
                        </div>
                        <div className="crypto-data-item">
                          <span className="label">Cambio 24h:</span>
                          <span className={`value ${message.cryptoData.data.changePercent24Hr >= 0 ? 'positive' : 'negative'}`}>
                            {formatChange(message.cryptoData.data.changePercent24Hr)}
                          </span>
                        </div>
                        <div className="crypto-data-item">
                          <span className="label">Volumen 24h:</span>
                          <span className="value">{formatVolume(message.cryptoData.data.volumeUsd24Hr)}</span>
                        </div>
                      </div>
                    </div>
                  )}
                  {message.newsData && (
                    <div className="news-data-card">
                      <div className="news-data-header">
                        <h4>Noticias sobre {message.newsData.topic}</h4>
                      </div>
                      <div className="news-data-results">
                        {message.newsData.results.map((result, index) => (
                          <div key={index} className="news-item">
                            <h5 className="news-title">
                              <a href={result.url} target="_blank" rel="noopener noreferrer">
                                {result.title}
                              </a>
                            </h5>
                            <p className="news-description">{result.description}</p>
                            <div className="news-meta">
                              <span className="news-source">{result.source}</span>
                              {result.publishedDate && (
                                <span className="news-date">{result.publishedDate}</span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  {message.webPageData && (
                    <WebPageView
                      data={message.webPageData}
                      onNavigate={async (url, sessionId) => {
                        try {
                          setIsLoading(true);
                          const response = await fetch('http://localhost:3001/api/guru/visualize-webpage', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ url, sessionId })
                          });

                          if (!response.ok) {
                            throw new Error(`Error al visualizar página web: ${response.statusText}`);
                          }

                          const data = await response.json();

                          // Actualizar el mensaje actual con los nuevos datos
                          setMessages(prevMessages =>
                            prevMessages.map(msg =>
                              msg.id === message.id
                                ? { ...msg, webPageData: data }
                                : msg
                            )
                          );
                        } catch (error) {
                          console.error('Error al navegar:', error);
                        } finally {
                          setIsLoading(false);
                        }
                      }}
                      onBack={async (sessionId) => {
                        try {
                          setIsLoading(true);
                          const response = await fetch('http://localhost:3001/api/guru/navigate-back', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ sessionId })
                          });

                          if (!response.ok) {
                            throw new Error(`Error al navegar hacia atrás: ${response.statusText}`);
                          }

                          const data = await response.json();

                          // Actualizar el mensaje actual con los nuevos datos
                          setMessages(prevMessages =>
                            prevMessages.map(msg =>
                              msg.id === message.id
                                ? { ...msg, webPageData: data }
                                : msg
                            )
                          );
                        } catch (error) {
                          console.error('Error al navegar hacia atrás:', error);
                        } finally {
                          setIsLoading(false);
                        }
                      }}
                      onForward={async (sessionId) => {
                        try {
                          setIsLoading(true);
                          const response = await fetch('http://localhost:3001/api/guru/navigate-forward', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ sessionId })
                          });

                          if (!response.ok) {
                            throw new Error(`Error al navegar hacia adelante: ${response.statusText}`);
                          }

                          const data = await response.json();

                          // Actualizar el mensaje actual con los nuevos datos
                          setMessages(prevMessages =>
                            prevMessages.map(msg =>
                              msg.id === message.id
                                ? { ...msg, webPageData: data }
                                : msg
                            )
                          );
                        } catch (error) {
                          console.error('Error al navegar hacia adelante:', error);
                        } finally {
                          setIsLoading(false);
                        }
                      }}
                    />
                  )}

                  {/* Mostrar el análisis técnico si existe */}
                  {message.technicalAnalysis && (
                    <div className="technical-analysis-container">
                      {message.cryptoData?.symbol ? (
                        <EnhancedTechnicalAnalysis
                          analysis={message.technicalAnalysis}
                          symbol={message.cryptoData.symbol}
                        />
                      ) : (
                        <TechnicalAnalysisCard analysis={message.technicalAnalysis} />
                      )}
                    </div>
                  )}

                  {message.fundamentalAnalysis && (
                    <div className="fundamental-analysis-container">
                      <FundamentalAnalysisCard analysis={message.fundamentalAnalysis} />
                    </div>
                  )}
                </div>
                <div className="message-timestamp">{message.timestamp}</div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        <VoiceControls
          isListening={isListening}
          isSpeaking={isSpeaking}
          onStartListening={handleVoiceInput}
          onStopListening={handleVoiceInput}
          volume={voiceVolume}
          speed={voiceSpeed}
          onVolumeChange={setVoiceVolume}
          onSpeedChange={setVoiceSpeed}
          disabled={isLoading}
        />

        <div className="guru-input-container">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Pregunta sobre criptomonedas..."
            disabled={isLoading}
            className="guru-message-input"
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
            className={`guru-send-button ${isLoading ? 'loading' : ''}`}
          >
            {isLoading ?
              <div className="loading-spinner"></div> :
              <svg viewBox="0 0 24 24" width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            }
          </button>
        </div>
      </div>
    </div>
  );
};

export default GuruChatInterface;
