const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Directorios
const rootDir = __dirname;
const frontendDir = path.join(rootDir, 'criptokens', 'frontend');
const mcpServerDir = path.join(rootDir, 'crypto-mcp-server');
const playwrightMcpServerDir = path.join(rootDir, 'playwright-mcp-server');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para iniciar un proceso
function startProcess(name, command, args, cwd, color) {
  log(`Iniciando ${name}...`, colors.bright + color);

  const process = spawn(command, args, {
    cwd,
    shell: true,
    stdio: 'pipe',
  });

  process.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${color}[${name}] ${line}${colors.reset}`);
      }
    });
  });

  process.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${colors.red}[${name} ERROR] ${line}${colors.reset}`);
      }
    });
  });

  process.on('close', (code) => {
    if (code !== 0) {
      log(`${name} se ha detenido con código ${code}`, colors.red);
    } else {
      log(`${name} se ha detenido correctamente`, colors.green);
    }
  });

  return process;
}

// Verificar que los directorios existen
if (!fs.existsSync(frontendDir)) {
  log(`El directorio frontend no existe: ${frontendDir}`, colors.red);
  process.exit(1);
}

if (!fs.existsSync(mcpServerDir)) {
  log(`El directorio del servidor MCP no existe: ${mcpServerDir}`, colors.red);
  process.exit(1);
}

if (!fs.existsSync(playwrightMcpServerDir)) {
  log(`El directorio del servidor Playwright MCP no existe: ${playwrightMcpServerDir}`, colors.red);
  process.exit(1);
}

// Iniciar el frontend
const frontendProcess = startProcess(
  'Frontend',
  'npm',
  ['run', 'dev'],
  frontendDir,
  colors.cyan
);

// Iniciar el servidor MCP
const mcpServerProcess = startProcess(
  'MCP Server',
  'node',
  ['http-server.js'],
  mcpServerDir,
  colors.magenta
);

// Iniciar el servidor Playwright MCP
const playwrightMcpServerProcess = startProcess(
  'Playwright MCP Server',
  'npm',
  ['run', 'dev'],
  playwrightMcpServerDir,
  colors.yellow
);

// Manejar la terminación del script
process.on('SIGINT', () => {
  log('\nDeteniendo todos los procesos...', colors.yellow);

  frontendProcess.kill();
  mcpServerProcess.kill();
  playwrightMcpServerProcess.kill();

  setTimeout(() => {
    log('Todos los procesos han sido detenidos', colors.green);
    process.exit(0);
  }, 1000);
});

log('Todos los servicios han sido iniciados. Presiona Ctrl+C para detener.', colors.green + colors.bright);
