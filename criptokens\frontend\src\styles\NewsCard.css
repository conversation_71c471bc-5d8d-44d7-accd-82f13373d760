.news-card {
  display: flex;
  flex-direction: column;
  background-color: var(--card-bg-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  cursor: pointer;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.news-image-container {
  height: 180px;
  overflow: hidden;
  position: relative;
}

.news-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.news-card:hover .news-image {
  transform: scale(1.05);
}

.news-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.news-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-description {
  font-size: 0.9rem;
  color: var(--text-secondary-color);
  margin-bottom: 12px;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  font-size: 0.8rem;
}

.news-source {
  font-weight: 600;
  color: var(--primary-color);
}

.news-date {
  color: var(--text-tertiary-color);
}

/* Estilos para los indicadores de sentimiento */
.sentiment-positive {
  border-left: 3px solid #16c784;
}

.sentiment-negative {
  border-left: 3px solid #ea3943;
}

.sentiment-neutral {
  border-left: 3px solid #7a7f99;
}

/* Responsive */
@media (max-width: 768px) {
  .news-image-container {
    height: 140px;
  }
  
  .news-title {
    font-size: 1rem;
  }
  
  .news-description {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
  }
}
