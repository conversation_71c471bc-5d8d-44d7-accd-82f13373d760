// Servicio para obtener datos de criptomonedas desde el servidor MCP
import { PLACEHOLDER_IMAGE } from '../utils/imageUtils';

// URL base del servidor MCP desde las variables de entorno
const MCP_SERVER_URL = import.meta.env.VITE_MCP_SERVER_URL || 'http://localhost:3101';

// Interfaz para los datos de una criptomoneda
export interface CryptoData {
  id: string;
  name: string;
  symbol: string;
  price: number;
  price_change_24h: number;
  market_cap: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  image: string;
  last_updated: string;
}

// Interfaz para los datos históricos
export interface HistoricalData {
  prices: [number, number][];
  market_caps: [number, number][];
  total_volumes: [number, number][];
}

// Función para obtener el precio y detalles de una criptomoneda
export const getCryptoPrice = async (cryptoId: string): Promise<CryptoData> => {
  try {
    // Verificar si el servidor MCP está disponible
    const isMcpAvailable = await checkMcpServerAvailability();

    if (isMcpAvailable) {
      // Usar el servidor MCP
      const response = await fetch(`${MCP_SERVER_URL}/tools/getCryptoPrice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cryptoId
        })
        // No incluir credenciales para evitar problemas de CORS
        // credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Error al obtener datos de ${cryptoId}: ${response.statusText}`);
      }

      const result = await response.json();
      const data = JSON.parse(result.content[0].text);
      return data;
    } else {
      // Usar datos simulados como fallback
      console.warn('Servidor MCP no disponible. Usando datos simulados.');
      return getSimulatedCryptoPrice(cryptoId);
    }
  } catch (error: any) {
    console.error(`Error al obtener precio de ${cryptoId}:`, error);
    // Usar datos simulados como fallback en caso de error
    console.warn('Error al conectar con el servidor MCP. Usando datos simulados.');
    return getSimulatedCryptoPrice(cryptoId);
  }
};

// Función para obtener las principales criptomonedas
export const getTopCryptocurrencies = async (limit: number = 10, page: number = 1): Promise<CryptoData[]> => {
  try {
    // Verificar si el servidor MCP está disponible
    const isMcpAvailable = await checkMcpServerAvailability();

    if (isMcpAvailable) {
      // Usar el servidor MCP
      const response = await fetch(`${MCP_SERVER_URL}/tools/getTopCryptocurrencies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          limit,
          page
        })
        // No incluir credenciales para evitar problemas de CORS
        // credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Error al obtener las principales criptomonedas: ${response.statusText}`);
      }

      const result = await response.json();
      const data = JSON.parse(result.content[0].text);
      return data;
    } else {
      // Usar datos simulados como fallback
      console.warn('Servidor MCP no disponible. Usando datos simulados.');
      return getSimulatedTopCryptocurrencies(limit);
    }
  } catch (error: any) {
    console.error('Error al obtener las principales criptomonedas:', error);
    // Usar datos simulados como fallback en caso de error
    console.warn('Error al conectar con el servidor MCP. Usando datos simulados.');
    return getSimulatedTopCryptocurrencies(limit);
  }
};

// Función para obtener datos históricos de una criptomoneda
export const getCryptoHistoricalData = async (cryptoId: string, days: number = 7): Promise<HistoricalData> => {
  try {
    // Verificar si el servidor MCP está disponible
    const isMcpAvailable = await checkMcpServerAvailability();

    if (isMcpAvailable) {
      // Usar el servidor MCP
      const response = await fetch(`${MCP_SERVER_URL}/tools/getCryptoHistoricalData`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cryptoId,
          days
        })
        // No incluir credenciales para evitar problemas de CORS
        // credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Error al obtener datos históricos de ${cryptoId}: ${response.statusText}`);
      }

      const result = await response.json();
      const data = JSON.parse(result.content[0].text);
      return data;
    } else {
      // Usar datos simulados como fallback
      console.warn('Servidor MCP no disponible. Usando datos históricos simulados.');
      return getSimulatedHistoricalData(cryptoId, days);
    }
  } catch (error: any) {
    console.error(`Error al obtener datos históricos de ${cryptoId}:`, error);
    // Usar datos simulados como fallback en caso de error
    console.warn('Error al conectar con el servidor MCP. Usando datos históricos simulados.');
    return getSimulatedHistoricalData(cryptoId, days);
  }
};

// Función para buscar criptomonedas
export const searchCryptocurrencies = async (query: string): Promise<any> => {
  try {
    // Verificar si el servidor MCP está disponible
    const isMcpAvailable = await checkMcpServerAvailability();

    if (isMcpAvailable) {
      // Usar el servidor MCP
      const response = await fetch(`${MCP_SERVER_URL}/tools/searchCryptocurrencies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query
        })
        // No incluir credenciales para evitar problemas de CORS
        // credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Error al buscar criptomonedas: ${response.statusText}`);
      }

      const result = await response.json();
      const data = JSON.parse(result.content[0].text);
      return data;
    } else {
      // Usar datos simulados como fallback
      console.warn('Servidor MCP no disponible. Usando resultados de búsqueda simulados.');
      return getSimulatedSearchResults(query);
    }
  } catch (error: any) {
    console.error('Error al buscar criptomonedas:', error);
    // Usar datos simulados como fallback en caso de error
    console.warn('Error al conectar con el servidor MCP. Usando resultados de búsqueda simulados.');
    return getSimulatedSearchResults(query);
  }
};

// Función para verificar si el servidor MCP está disponible
const checkMcpServerAvailability = async (): Promise<boolean> => {
  try {
    // Intentar conectarse al servidor MCP
    const response = await fetch(`${MCP_SERVER_URL}/tools`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      }
      // No incluir credenciales para evitar problemas de CORS
      // credentials: 'include'
    });

    return response.ok;
  } catch (error) {
    console.error("Error al verificar disponibilidad del servidor MCP:", error);
    return false;
  }
};

// Funciones para generar datos simulados (fallback)

// Datos simulados para precios de criptomonedas
const getSimulatedCryptoPrice = (cryptoId: string): any => {
  const cryptoData: Record<string, any> = {
    'bitcoin': {
      id: 'bitcoin',
      name: 'Bitcoin',
      symbol: 'BTC',
      price: 61245.32,
      current_price: 61245.32,
      price_change_24h: 2.5,
      price_change_percentage_24h: 2.5,
      market_cap: 1200000000000,
      total_volume: 32000000000,
      high_24h: 62000,
      low_24h: 60000,
      image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 1,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 61245.32 * (0.9 + Math.random() * 0.2)) }
    },
    'ethereum': {
      id: 'ethereum',
      name: 'Ethereum',
      symbol: 'ETH',
      price: 3521.18,
      current_price: 3521.18,
      price_change_24h: -1.2,
      price_change_percentage_24h: -1.2,
      market_cap: 420000000000,
      total_volume: 15000000000,
      high_24h: 3600,
      low_24h: 3500,
      image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 2,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 3521.18 * (0.9 + Math.random() * 0.2)) }
    },
    'binancecoin': {
      id: 'binancecoin',
      name: 'BNB',
      symbol: 'BNB',
      price: 608.42,
      current_price: 608.42,
      price_change_24h: -0.8,
      price_change_percentage_24h: -0.8,
      market_cap: 95000000000,
      total_volume: 2000000000,
      high_24h: 615,
      low_24h: 600,
      image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 3,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 608.42 * (0.9 + Math.random() * 0.2)) }
    },
    'ripple': {
      id: 'ripple',
      name: 'XRP',
      symbol: 'XRP',
      price: 0.58,
      current_price: 0.58,
      price_change_24h: 1.5,
      price_change_percentage_24h: 1.5,
      market_cap: 31000000000,
      total_volume: 1200000000,
      high_24h: 0.59,
      low_24h: 0.57,
      image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 4,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 0.58 * (0.9 + Math.random() * 0.2)) }
    },
    'cardano': {
      id: 'cardano',
      name: 'Cardano',
      symbol: 'ADA',
      price: 0.45,
      current_price: 0.45,
      price_change_24h: 0.3,
      price_change_percentage_24h: 0.3,
      market_cap: 16000000000,
      total_volume: 500000000,
      high_24h: 0.46,
      low_24h: 0.44,
      image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 5,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 0.45 * (0.9 + Math.random() * 0.2)) }
    }
  };

  // Si la criptomoneda solicitada existe en nuestros datos simulados, la devolvemos
  if (cryptoData[cryptoId]) {
    return cryptoData[cryptoId];
  }

  // Si no, devolvemos datos genéricos
  const price = Math.random() * 1000;
  const priceChange = (Math.random() * 10) - 5;
  return {
    id: cryptoId,
    name: cryptoId.charAt(0).toUpperCase() + cryptoId.slice(1),
    symbol: cryptoId.substring(0, 3).toUpperCase(),
    price: price,
    current_price: price,
    price_change_24h: priceChange,
    price_change_percentage_24h: priceChange,
    market_cap: Math.random() * 10000000000,
    total_volume: Math.random() * 1000000000,
    high_24h: price * 1.05,
    low_24h: price * 0.95,
    image: PLACEHOLDER_IMAGE,
    last_updated: new Date().toISOString(),
    market_cap_rank: Math.floor(Math.random() * 100) + 10,
    sparkline_in_7d: { price: Array(7).fill(0).map(() => price * (0.9 + Math.random() * 0.2)) }
  };
};

// Datos simulados para las principales criptomonedas
const getSimulatedTopCryptocurrencies = (limit: number): any[] => {
  const topCryptos = [
    getSimulatedCryptoPrice('bitcoin'),
    getSimulatedCryptoPrice('ethereum'),
    getSimulatedCryptoPrice('binancecoin'),
    getSimulatedCryptoPrice('ripple'),
    getSimulatedCryptoPrice('cardano'),
    {
      id: 'solana',
      name: 'Solana',
      symbol: 'SOL',
      price: 142.35,
      current_price: 142.35,
      price_change_24h: 3.2,
      price_change_percentage_24h: 3.2,
      market_cap: 60000000000,
      total_volume: 2500000000,
      high_24h: 145,
      low_24h: 138,
      image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 6,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 142.35 * (0.9 + Math.random() * 0.2)) }
    },
    {
      id: 'polkadot',
      name: 'Polkadot',
      symbol: 'DOT',
      price: 7.82,
      current_price: 7.82,
      price_change_24h: -0.5,
      price_change_percentage_24h: -0.5,
      market_cap: 10000000000,
      total_volume: 400000000,
      high_24h: 7.9,
      low_24h: 7.7,
      image: 'https://assets.coingecko.com/coins/images/12171/large/polkadot.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 7,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 7.82 * (0.9 + Math.random() * 0.2)) }
    },
    {
      id: 'dogecoin',
      name: 'Dogecoin',
      symbol: 'DOGE',
      price: 0.12,
      current_price: 0.12,
      price_change_24h: 1.8,
      price_change_percentage_24h: 1.8,
      market_cap: 17000000000,
      total_volume: 800000000,
      high_24h: 0.125,
      low_24h: 0.118,
      image: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 8,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 0.12 * (0.9 + Math.random() * 0.2)) }
    },
    {
      id: 'avalanche',
      name: 'Avalanche',
      symbol: 'AVAX',
      price: 35.42,
      current_price: 35.42,
      price_change_24h: 2.1,
      price_change_percentage_24h: 2.1,
      market_cap: 12000000000,
      total_volume: 600000000,
      high_24h: 36,
      low_24h: 34.5,
      image: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 9,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 35.42 * (0.9 + Math.random() * 0.2)) }
    },
    {
      id: 'chainlink',
      name: 'Chainlink',
      symbol: 'LINK',
      price: 15.78,
      current_price: 15.78,
      price_change_24h: -0.3,
      price_change_percentage_24h: -0.3,
      market_cap: 9000000000,
      total_volume: 350000000,
      high_24h: 16,
      low_24h: 15.5,
      image: 'https://assets.coingecko.com/coins/images/877/large/chainlink-new-logo.png',
      last_updated: new Date().toISOString(),
      market_cap_rank: 10,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => 15.78 * (0.9 + Math.random() * 0.2)) }
    }
  ];

  return topCryptos.slice(0, limit);
};

// Datos históricos simulados
const getSimulatedHistoricalData = (cryptoId: string, days: number): HistoricalData => {
  const prices: [number, number][] = [];
  const market_caps: [number, number][] = [];
  const total_volumes: [number, number][] = [];

  // Generar datos para cada día
  const now = Date.now();
  const dayInMs = 24 * 60 * 60 * 1000;

  // Precio base según la criptomoneda
  let basePrice = 0;
  switch (cryptoId) {
    case 'bitcoin':
      basePrice = 60000;
      break;
    case 'ethereum':
      basePrice = 3500;
      break;
    case 'binancecoin':
      basePrice = 600;
      break;
    default:
      basePrice = 100;
  }

  // Generar datos para cada día
  for (let i = days; i >= 0; i--) {
    const timestamp = now - (i * dayInMs);

    // Precio con variación aleatoria
    const priceVariation = (Math.random() * 0.1) - 0.05; // -5% a +5%
    const price = basePrice * (1 + priceVariation);

    // Market cap basado en el precio
    const marketCap = price * 1000000;

    // Volumen basado en el precio
    const volume = price * 10000;

    prices.push([timestamp, price]);
    market_caps.push([timestamp, marketCap]);
    total_volumes.push([timestamp, volume]);
  }

  return {
    prices,
    market_caps,
    total_volumes
  };
};

// Resultados de búsqueda simulados
const getSimulatedSearchResults = (query: string): any => {
  const allCryptos = [
    { id: 'bitcoin', name: 'Bitcoin', symbol: 'btc', market_cap_rank: 1 },
    { id: 'ethereum', name: 'Ethereum', symbol: 'eth', market_cap_rank: 2 },
    { id: 'binancecoin', name: 'BNB', symbol: 'bnb', market_cap_rank: 3 },
    { id: 'ripple', name: 'XRP', symbol: 'xrp', market_cap_rank: 4 },
    { id: 'cardano', name: 'Cardano', symbol: 'ada', market_cap_rank: 5 },
    { id: 'solana', name: 'Solana', symbol: 'sol', market_cap_rank: 6 },
    { id: 'polkadot', name: 'Polkadot', symbol: 'dot', market_cap_rank: 7 },
    { id: 'dogecoin', name: 'Dogecoin', symbol: 'doge', market_cap_rank: 8 },
    { id: 'avalanche', name: 'Avalanche', symbol: 'avax', market_cap_rank: 9 },
    { id: 'chainlink', name: 'Chainlink', symbol: 'link', market_cap_rank: 10 }
  ];

  // Filtrar por la consulta
  const lowercaseQuery = query.toLowerCase();
  const filteredCoins = allCryptos.filter(coin =>
    coin.id.includes(lowercaseQuery) ||
    coin.name.toLowerCase().includes(lowercaseQuery) ||
    coin.symbol.includes(lowercaseQuery)
  );

  return {
    coins: filteredCoins,
    exchanges: [],
    icos: [],
    categories: [],
    nfts: []
  };
};
