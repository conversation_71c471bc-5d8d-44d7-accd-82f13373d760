import React, { useState, useEffect } from 'react';
import { usePortfolio } from '../../hooks/usePortfolio';
import { formatDate, formatNumber } from '../../utils/formatters';
import '../../styles/portfolio/PortfolioTransactions.css';

const PortfolioTransactions: React.FC = () => {
  const { transactions, getTransactionHistoryData } = usePortfolio();
  const [filteredTransactions, setFilteredTransactions] = useState<any[]>([]);
  const [filter, setFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: '',
    end: ''
  });

  useEffect(() => {
    const loadTransactions = async () => {
      await getTransactionHistoryData();
    };

    loadTransactions();
  }, [getTransactionHistoryData]);

  useEffect(() => {
    // Aplicar filtros a las transacciones
    let filtered = [...transactions];

    // Filtrar por texto
    if (filter) {
      filtered = filtered.filter(
        (tx) =>
          tx.assetName.toLowerCase().includes(filter.toLowerCase()) ||
          tx.assetSymbol.toLowerCase().includes(filter.toLowerCase()) ||
          tx.type.toLowerCase().includes(filter.toLowerCase())
      );
    }

    // Filtrar por tipo
    if (typeFilter !== 'all') {
      filtered = filtered.filter((tx) => tx.type === typeFilter);
    }

    // Filtrar por rango de fechas
    if (dateRange.start) {
      const startDate = new Date(dateRange.start);
      filtered = filtered.filter((tx) => new Date(tx.date) >= startDate);
    }
    if (dateRange.end) {
      const endDate = new Date(dateRange.end);
      endDate.setHours(23, 59, 59, 999); // Establecer al final del día
      filtered = filtered.filter((tx) => new Date(tx.date) <= endDate);
    }

    // Ordenar por fecha (más reciente primero)
    filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    setFilteredTransactions(filtered);
  }, [transactions, filter, typeFilter, dateRange]);

  // Manejar la exportación de transacciones a CSV
  const handleExportCSV = () => {
    if (filteredTransactions.length === 0) return;

    // Crear contenido CSV
    const headers = ['Fecha', 'Tipo', 'Activo', 'Cantidad', 'Precio', 'Valor Total', 'Notas'];
    const csvContent = [
      headers.join(','),
      ...filteredTransactions.map((tx) => {
        return [
          formatDate(new Date(tx.date)),
          tx.type,
          `${tx.assetSymbol} (${tx.assetName})`,
          tx.amount,
          tx.price,
          tx.amount * tx.price,
          tx.notes || ''
        ].join(',');
      })
    ].join('\n');

    // Crear y descargar el archivo
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `transacciones_${formatDate(new Date())}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Renderizar mensaje si no hay transacciones
  if (transactions.length === 0) {
    return (
      <div className="empty-transactions-message">
        <p>No hay transacciones registradas en tu portafolio.</p>
        <p>Las transacciones se registrarán automáticamente cuando añadas o elimines activos.</p>
      </div>
    );
  }

  return (
    <div className="portfolio-transactions">
      <div className="transactions-filters">
        <div className="search-container">
          <input
            type="text"
            placeholder="Buscar transacción..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-container">
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="type-filter"
          >
            <option value="all">Todos los tipos</option>
            <option value="buy">Compra</option>
            <option value="sell">Venta</option>
            <option value="deposit">Depósito</option>
            <option value="withdrawal">Retiro</option>
          </select>

          <div className="date-range">
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
              className="date-input"
              placeholder="Fecha inicio"
            />
            <span>a</span>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
              className="date-input"
              placeholder="Fecha fin"
            />
          </div>

          <button className="export-button" onClick={handleExportCSV}>
            Exportar CSV
          </button>
        </div>
      </div>

      <div className="transactions-table-container">
        <table className="transactions-table">
          <thead>
            <tr>
              <th>Fecha</th>
              <th>Tipo</th>
              <th>Activo</th>
              <th>Cantidad</th>
              <th>Precio</th>
              <th>Valor Total</th>
              <th>Notas</th>
            </tr>
          </thead>
          <tbody>
            {filteredTransactions.map((tx, index) => (
              <tr key={index} className={`transaction-type-${tx.type.toLowerCase()}`}>
                <td>{formatDate(new Date(tx.date))}</td>
                <td className="transaction-type">
                  <span className={`type-badge ${tx.type.toLowerCase()}`}>
                    {tx.type === 'buy' && 'Compra'}
                    {tx.type === 'sell' && 'Venta'}
                    {tx.type === 'deposit' && 'Depósito'}
                    {tx.type === 'withdrawal' && 'Retiro'}
                  </span>
                </td>
                <td className="asset-info">
                  <span className="asset-symbol">{tx.assetSymbol}</span>
                  <span className="asset-name">{tx.assetName}</span>
                </td>
                <td>{formatNumber(tx.amount)}</td>
                <td>${formatNumber(tx.price)}</td>
                <td>${formatNumber(tx.amount * tx.price)}</td>
                <td className="transaction-notes">{tx.notes || '-'}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredTransactions.length === 0 && (
        <div className="no-results-message">
          <p>No se encontraron transacciones con los filtros aplicados.</p>
        </div>
      )}
    </div>
  );
};

export default PortfolioTransactions;
