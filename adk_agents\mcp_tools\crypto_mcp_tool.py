"""
Crypto MCP Tool for ADK Agents

This module provides a tool for ADK agents to interact with the Crypto MCP server.
"""
import os
from typing import Dict, Any, List, Optional
from .base_mcp_tool import BaseMcpTool

class CryptoMcpTool(BaseMcpTool):
    """Tool for interacting with the Crypto MCP server."""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the Crypto MCP tool.
        
        Args:
            base_url: Base URL of the Crypto MCP server (optional)
        """
        super().__init__("crypto", base_url)
    
    async def get_price(self, symbol: str, currency: str = "usd") -> Dict[str, Any]:
        """
        Get the current price of a cryptocurrency.
        
        Args:
            symbol: Symbol of the cryptocurrency (e.g., "BTC", "ETH")
            currency: Currency to get the price in (default: "usd")
            
        Returns:
            Price information
        """
        return await self.execute_tool("getPrices", {
            "symbols": [symbol],
            "currency": currency
        })
    
    async def get_prices(self, symbols: List[str], currency: str = "usd") -> Dict[str, Any]:
        """
        Get the current prices of multiple cryptocurrencies.
        
        Args:
            symbols: List of cryptocurrency symbols
            currency: Currency to get the prices in (default: "usd")
            
        Returns:
            Price information for multiple cryptocurrencies
        """
        return await self.execute_tool("getPrices", {
            "symbols": symbols,
            "currency": currency
        })
    
    async def get_historical_data(self, symbol: str, days: int, interval: str = "daily") -> Dict[str, Any]:
        """
        Get historical price data for a cryptocurrency.
        
        Args:
            symbol: Symbol of the cryptocurrency
            days: Number of days of historical data
            interval: Interval of the data (daily, hourly, minutely)
            
        Returns:
            Historical price data
        """
        return await self.execute_tool("getHistoricalData", {
            "symbol": symbol,
            "days": days,
            "interval": interval
        })
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get market data for a cryptocurrency.
        
        Args:
            symbol: Symbol of the cryptocurrency
            
        Returns:
            Market data
        """
        return await self.execute_tool("getMarketData", {
            "symbol": symbol
        })
    
    async def get_trending_coins(self, limit: int = 10) -> Dict[str, Any]:
        """
        Get trending cryptocurrencies.
        
        Args:
            limit: Maximum number of cryptocurrencies to return
            
        Returns:
            Trending cryptocurrencies
        """
        return await self.execute_tool("getTrendingCoins", {
            "limit": limit
        })
    
    async def search_coins(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search for cryptocurrencies by name or symbol.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            Search results
        """
        return await self.execute_tool("searchCoins", {
            "query": query,
            "limit": limit
        })
    
    async def get_coin_info(self, id: str) -> Dict[str, Any]:
        """
        Get detailed information about a cryptocurrency.
        
        Args:
            id: ID of the cryptocurrency
            
        Returns:
            Detailed information
        """
        return await self.execute_tool("getCoinInfo", {
            "id": id
        })
