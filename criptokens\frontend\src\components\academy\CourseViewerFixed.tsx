import React, { useState, useEffect } from 'react';
import '../../styles/academy/CourseViewer.css';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
}

interface Quiz {
  id: string;
  title: string;
  questions: Question[];
}

interface Lesson {
  id: string;
  title: string;
  content: string;
  videoUrl?: string;
  duration: number;
}

interface Module {
  id: string;
  title: string;
  description: string;
  lessons: Lesson[];
  quiz?: Quiz;
}

interface Resource {
  id: string;
  title: string;
  type: string;
  url: string;
}

interface Instructor {
  name: string;
  bio: string;
  avatar: string;
}

interface Review {
  id: string;
  user: string;
  rating: number;
  comment: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
  duration: string;
  modules: Module[];
  resources: Resource[];
  instructor: Instructor;
  reviews: Review[];
}

interface CourseViewerProps {
  courseId: string;
}

const CourseViewer: React.FC<CourseViewerProps> = ({ courseId }) => {
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeModule, setActiveModule] = useState<string>('');
  const [activeLesson, setActiveLesson] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'content' | 'resources' | 'discussion'>('content');
  const [quizMode, setQuizMode] = useState<boolean>(false);
  const [quizAnswers, setQuizAnswers] = useState<Record<string, number>>({});
  const [quizSubmitted, setQuizSubmitted] = useState<boolean>(false);
  const [quizScore, setQuizScore] = useState<number>(0);
  const [progress, setProgress] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // En una implementación real, esto cargaría el curso desde una API
    // Por ahora, simulamos cargando desde un archivo local
    const loadCourse = async () => {
      try {
        setLoading(true);
        // En una implementación real, esto sería una llamada a la API
        const courseData = await import(`../../data/courses/${courseId}.json`);
        setCourse(courseData);
        
        // Establecer el primer módulo y lección como activos por defecto
        if (courseData.modules && courseData.modules.length > 0) {
          setActiveModule(courseData.modules[0].id);
          
          if (courseData.modules[0].lessons && courseData.modules[0].lessons.length > 0) {
            setActiveLesson(courseData.modules[0].lessons[0].id);
          }
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Error loading course:', error);
        setLoading(false);
      }
    };
    
    loadCourse();
  }, [courseId]);

  const handleModuleClick = (moduleId: string) => {
    setActiveModule(moduleId);
    const module = course?.modules.find(m => m.id === moduleId);
    if (module && module.lessons && module.lessons.length > 0) {
      setActiveLesson(module.lessons[0].id);
    }
    setQuizMode(false);
    setQuizSubmitted(false);
  };

  const handleLessonClick = (lessonId: string) => {
    setActiveLesson(lessonId);
    setQuizMode(false);
    
    // Marcar la lección como completada
    setProgress(prev => ({
      ...prev,
      [lessonId]: true
    }));
  };

  const handleQuizClick = () => {
    setQuizMode(true);
    setQuizSubmitted(false);
    setQuizAnswers({});
  };

  const handleQuizAnswerChange = (questionId: string, answerIndex: number) => {
    setQuizAnswers(prev => ({
      ...prev,
      [questionId]: answerIndex
    }));
  };

  const handleQuizSubmit = () => {
    const currentModule = course?.modules.find(m => m.id === activeModule);
    if (!currentModule || !currentModule.quiz) return;
    
    let correctAnswers = 0;
    currentModule.quiz.questions.forEach(question => {
      if (quizAnswers[question.id] === question.correctAnswer) {
        correctAnswers++;
      }
    });
    
    const score = Math.round((correctAnswers / currentModule.quiz.questions.length) * 100);
    setQuizScore(score);
    setQuizSubmitted(true);
    
    // Si el puntaje es mayor a 70%, marcar el módulo como completado
    if (score >= 70) {
      setProgress(prev => ({
        ...prev,
        [activeModule]: true
      }));
    }
  };

  const getActiveLesson = () => {
    const module = course?.modules.find(m => m.id === activeModule);
    if (!module) return null;
    
    return module.lessons.find(l => l.id === activeLesson);
  };

  const getModuleProgress = (moduleId: string) => {
    const module = course?.modules.find(m => m.id === moduleId);
    if (!module) return 0;
    
    const totalLessons = module.lessons.length;
    if (totalLessons === 0) return 0;
    
    const completedLessons = module.lessons.filter(lesson => progress[lesson.id]).length;
    return Math.round((completedLessons / totalLessons) * 100);
  };

  const getTotalProgress = () => {
    if (!course) return 0;
    
    const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0);
    if (totalLessons === 0) return 0;
    
    const completedLessons = Object.values(progress).filter(Boolean).length;
    return Math.round((completedLessons / totalLessons) * 100);
  };

  if (loading) {
    return <div className="course-loading">Cargando curso...</div>;
  }

  if (!course) {
    return <div className="course-error">No se pudo cargar el curso.</div>;
  }

  const currentLesson = getActiveLesson();
  const currentModule = course.modules.find(m => m.id === activeModule);

  return (
    <div className="course-viewer">
      <div className="course-sidebar">
        <div className="course-info">
          <h2>{course.title}</h2>
          <div className="course-meta">
            <span className="course-level">{course.level}</span>
            <span className="course-duration">{course.duration}</span>
          </div>
          <div className="course-progress">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${getTotalProgress()}%` }}
              ></div>
            </div>
            <span className="progress-text">{getTotalProgress()}% completado</span>
          </div>
        </div>
        
        <div className="course-modules">
          {course.modules.map(module => (
            <div 
              key={module.id} 
              className={`module-item ${activeModule === module.id ? 'active' : ''}`}
            >
              <div 
                className="module-header" 
                onClick={() => handleModuleClick(module.id)}
              >
                <h3>{module.title}</h3>
                <div className="module-progress">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${getModuleProgress(module.id)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              
              {activeModule === module.id && (
                <div className="module-lessons">
                  {module.lessons.map(lesson => (
                    <div 
                      key={lesson.id} 
                      className={`lesson-item ${activeLesson === lesson.id ? 'active' : ''} ${progress[lesson.id] ? 'completed' : ''}`}
                      onClick={() => handleLessonClick(lesson.id)}
                    >
                      <span className="lesson-title">{lesson.title}</span>
                      <span className="lesson-duration">{lesson.duration} min</span>
                    </div>
                  ))}
                  
                  {module.quiz && (
                    <div 
                      className={`lesson-item quiz ${quizMode ? 'active' : ''} ${progress[module.id] ? 'completed' : ''}`}
                      onClick={handleQuizClick}
                    >
                      <span className="lesson-title">{module.quiz.title}</span>
                      <span className="lesson-type">Quiz</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      
      <div className="course-content">
        <div className="content-tabs">
          <button 
            className={`tab-button ${activeTab === 'content' ? 'active' : ''}`}
            onClick={() => setActiveTab('content')}
          >
            Contenido
          </button>
          <button 
            className={`tab-button ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => setActiveTab('resources')}
          >
            Recursos
          </button>
          <button 
            className={`tab-button ${activeTab === 'discussion' ? 'active' : ''}`}
            onClick={() => setActiveTab('discussion')}
          >
            Discusión
          </button>
        </div>
        
        {activeTab === 'content' && (
          <div className="lesson-content">
            {quizMode ? (
              <div className="quiz-container">
                <h2>{currentModule?.quiz?.title}</h2>
                
                {quizSubmitted ? (
                  <div className="quiz-results">
                    <div className={`quiz-score ${quizScore >= 70 ? 'pass' : 'fail'}`}>
                      <h3>Tu puntuación: {quizScore}%</h3>
                      {quizScore >= 70 ? (
                        <p>¡Felicidades! Has aprobado este módulo.</p>
                      ) : (
                        <p>No has alcanzado la puntuación mínima. Revisa el material e intenta nuevamente.</p>
                      )}
                    </div>
                    <button 
                      className="quiz-button retry"
                      onClick={() => {
                        setQuizSubmitted(false);
                        setQuizAnswers({});
                      }}
                    >
                      Intentar nuevamente
                    </button>
                    <button 
                      className="quiz-button continue"
                      onClick={() => {
                        // Ir al siguiente módulo si existe
                        const currentIndex = course.modules.findIndex(m => m.id === activeModule);
                        if (currentIndex < course.modules.length - 1) {
                          handleModuleClick(course.modules[currentIndex + 1].id);
                        }
                      }}
                    >
                      Continuar
                    </button>
                  </div>
                ) : (
                  <>
                    <div className="quiz-questions">
                      {currentModule?.quiz?.questions.map((question, index) => (
                        <div key={question.id} className="quiz-question">
                          <h3>Pregunta {index + 1}: {question.question}</h3>
                          <div className="quiz-options">
                            {question.options.map((option, optionIndex) => (
                              <label key={optionIndex} className="quiz-option">
                                <input 
                                  type="radio" 
                                  name={question.id} 
                                  checked={quizAnswers[question.id] === optionIndex}
                                  onChange={() => handleQuizAnswerChange(question.id, optionIndex)}
                                />
                                <span>{option}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                    <button 
                      className="quiz-submit"
                      onClick={handleQuizSubmit}
                      disabled={currentModule?.quiz?.questions.some(q => quizAnswers[q.id] === undefined)}
                    >
                      Enviar respuestas
                    </button>
                  </>
                )}
              </div>
            ) : currentLesson ? (
              <>
                <h2>{currentLesson.title}</h2>
                {currentLesson.videoUrl && (
                  <div className="lesson-video">
                    <div className="video-placeholder">
                      <i className="fas fa-play-circle"></i>
                      <span>Video no disponible en la vista previa</span>
                    </div>
                  </div>
                )}
                <div className="lesson-text">
                  {currentLesson.content.split('\n\n').map((paragraph, index) => (
                    <p key={index}>{paragraph}</p>
                  ))}
                </div>
                <div className="lesson-navigation">
                  <button 
                    className="nav-button prev"
                    disabled={!currentModule?.lessons[0] || currentModule.lessons[0].id === activeLesson}
                    onClick={() => {
                      const currentIndex = currentModule?.lessons.findIndex(l => l.id === activeLesson);
                      if (currentIndex !== undefined && currentIndex > 0) {
                        handleLessonClick(currentModule.lessons[currentIndex - 1].id);
                      }
                    }}
                  >
                    Lección anterior
                  </button>
                  <button 
                    className="nav-button next"
                    onClick={() => {
                      const currentIndex = currentModule?.lessons.findIndex(l => l.id === activeLesson);
                      if (currentIndex !== undefined && currentIndex < currentModule.lessons.length - 1) {
                        handleLessonClick(currentModule.lessons[currentIndex + 1].id);
                      } else if (currentModule?.quiz) {
                        handleQuizClick();
                      }
                    }}
                  >
                    {currentModule?.lessons.findIndex(l => l.id === activeLesson) === currentModule?.lessons.length - 1 && currentModule.quiz
                      ? 'Ir al quiz'
                      : 'Siguiente lección'
                    }
                  </button>
                </div>
              </>
            ) : (
              <div className="no-lesson-selected">
                <p>Selecciona una lección para comenzar.</p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'resources' && (
          <div className="resources-content">
            <h2>Recursos del Curso</h2>
            <div className="resources-list">
              {course.resources.map(resource => (
                <div key={resource.id} className="resource-item">
                  <div className="resource-icon">
                    {resource.type === 'document' && <i className="fas fa-file-pdf"></i>}
                    {resource.type === 'video' && <i className="fas fa-video"></i>}
                    {resource.type === 'link' && <i className="fas fa-link"></i>}
                    {resource.type === 'glossary' && <i className="fas fa-book"></i>}
                    {resource.type === 'tools' && <i className="fas fa-tools"></i>}
                  </div>
                  <div className="resource-info">
                    <h3>{resource.title}</h3>
                    <span className="resource-type">{resource.type}</span>
                  </div>
                  <a href={resource.url} target="_blank" rel="noopener noreferrer" className="resource-link">
                    Ver recurso
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {activeTab === 'discussion' && (
          <div className="discussion-content">
            <h2>Foro de Discusión</h2>
            <div className="discussion-placeholder">
              <i className="fas fa-comments"></i>
              <p>El foro de discusión estará disponible próximamente.</p>
            </div>
          </div>
        )}
      </div>
      
      <div className="course-info-sidebar">
        <div className="instructor-info">
          <h3>Instructor</h3>
          <div className="instructor-profile">
            <div className="instructor-avatar">
              <img src={course.instructor.avatar} alt={course.instructor.name} />
            </div>
            <div className="instructor-details">
              <h4>{course.instructor.name}</h4>
              <p>{course.instructor.bio}</p>
            </div>
          </div>
        </div>
        
        <div className="course-reviews">
          <h3>Opiniones del Curso</h3>
          <div className="reviews-list">
            {course.reviews.map(review => (
              <div key={review.id} className="review-item">
                <div className="review-header">
                  <span className="review-user">{review.user}</span>
                  <div className="review-rating">
                    {[...Array(5)].map((_, i) => (
                      <i 
                        key={i} 
                        className={`fas fa-star ${i < review.rating ? 'filled' : ''}`}
                      ></i>
                    ))}
                  </div>
                </div>
                <p className="review-comment">{review.comment}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseViewer;
