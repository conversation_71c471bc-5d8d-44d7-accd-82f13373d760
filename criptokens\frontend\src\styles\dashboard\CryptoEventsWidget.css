.crypto-events-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.crypto-events-widget:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.events-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.events-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.tab-button {
  flex: 1;
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.05);
}

.tab-button.active {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
}

.event-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  border-radius: 10px;
  background-color: var(--color-surface-light);
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.events-content {
  padding: 0.75rem;
  overflow-y: auto;
  flex: 1;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.event-item {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--border-radius-md);
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.event-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
  border-color: var(--border-color-hover);
}

.event-type-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.event-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.5rem;
}

.event-title {
  margin: 0;
  font-size: 0.95rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.event-date {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  white-space: nowrap;
}

.event-project {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  object-fit: contain;
}

.project-name {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.event-description {
  margin: 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.event-link {
  align-self: flex-end;
  color: var(--color-primary);
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s ease;
  margin-top: 0.25rem;
}

.event-link:hover {
  text-decoration: underline;
}

.event-link i {
  font-size: 0.7rem;
}

.empty-events {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-tertiary);
}

.empty-events i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-events p {
  margin: 0;
  font-size: 0.9rem;
}

/* Esqueleto de carga */
.crypto-events-widget.loading .skeleton-loading {
  width: 100%;
  height: 200px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .events-content {
    padding: 0.5rem;
  }
  
  .event-item {
    padding: 0.625rem;
  }
  
  .event-type-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }
  
  .event-title {
    font-size: 0.9rem;
  }
  
  .event-description {
    font-size: 0.8rem;
  }
}
