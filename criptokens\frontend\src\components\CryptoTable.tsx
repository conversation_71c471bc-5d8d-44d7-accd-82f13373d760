import { useState, useEffect, useMemo } from 'react';
import { getTopCryptocurrencies } from '../services/api';
import '../styles/CryptoTable.css';

// Datos simulados para desarrollo
const mockCryptoData = [
  {
    id: 'bitcoin',
    rank: 1,
    name: 'Bitcoin',
    symbol: 'BTC',
    price: 85050.32,
    priceChange24h: 0.43,
    marketCap: '1.6T',
    volume24h: '42.3B',
    sparkline: [84500, 84800, 85200, 84900, 85100, 85050]
  },
  {
    id: 'ethereum',
    rank: 2,
    name: 'Ethereum',
    symbol: 'ETH',
    price: 1605.78,
    priceChange24h: 1.43,
    marketCap: '193B',
    volume24h: '12.7B',
    sparkline: [1590, 1610, 1600, 1620, 1605, 1606]
  },
  {
    id: 'ripple',
    rank: 3,
    name: 'XRP',
    symbol: 'XRP',
    price: 2.09,
    priceChange24h: 1.63,
    marketCap: '112B',
    volume24h: '5.2B',
    sparkline: [2.05, 2.08, 2.12, 2.10, 2.09, 2.09]
  },
  {
    id: 'cardano',
    rank: 4,
    name: 'Cardano',
    symbol: 'ADA',
    price: 0.89,
    priceChange24h: -0.32,
    marketCap: '31B',
    volume24h: '1.8B',
    sparkline: [0.90, 0.88, 0.87, 0.89, 0.88, 0.89]
  },
  {
    id: 'solana',
    rank: 5,
    name: 'Solana',
    symbol: 'SOL',
    price: 178.45,
    priceChange24h: 2.87,
    marketCap: '78B',
    volume24h: '3.9B',
    sparkline: [173, 175, 177, 180, 179, 178]
  }
];

interface CryptoTableProps {
  onSelectCrypto?: (cryptoId: string) => void;
}

const CryptoTable = ({ onSelectCrypto }: CryptoTableProps) => {
  const [cryptoData, setCryptoData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'ascending' | 'descending' } | null>(null);

  // Cargar datos desde la API de CoinGecko
  useEffect(() => {
    const fetchCryptoData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Intentar obtener datos reales de la API
        const data = await getTopCryptocurrencies(10);

        // Transformar los datos al formato que espera nuestro componente
        const formattedData = data.map((coin: any, index: number) => ({
          id: coin.id,
          rank: index + 1,
          name: coin.name,
          symbol: coin.symbol.toUpperCase(),
          image: coin.image,
          price: coin.current_price,
          priceChange24h: coin.price_change_percentage_24h,
          marketCap: formatMarketCap(coin.market_cap),
          volume24h: formatMarketCap(coin.total_volume),
          sparkline: coin.sparkline_in_7d?.price || []
        }));

        setCryptoData(formattedData);
      } catch (err) {
        console.error('Error al cargar datos de criptomonedas:', err);
        setError('No se pudieron cargar los datos. Usando datos simulados.');
        // Fallback a datos simulados en caso de error
        setCryptoData(mockCryptoData);
      } finally {
        setIsLoading(false);
      }
    };

    // Función para formatear valores grandes (B, M, etc.)
    const formatMarketCap = (value: number): string => {
      if (value >= 1e12) return (value / 1e12).toFixed(2) + 'T';
      if (value >= 1e9) return (value / 1e9).toFixed(2) + 'B';
      if (value >= 1e6) return (value / 1e6).toFixed(2) + 'M';
      if (value >= 1e3) return (value / 1e3).toFixed(2) + 'K';
      return value.toString();
    };

    fetchCryptoData();
  }, []);

  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';

    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }

    setSortConfig({ key, direction });
  };

  const sortedData = useMemo(() => {
    if (!sortConfig) return cryptoData;

    return [...cryptoData].sort((a, b) => {
      if (a[sortConfig.key as keyof typeof a] < b[sortConfig.key as keyof typeof b]) {
        return sortConfig.direction === 'ascending' ? -1 : 1;
      }
      if (a[sortConfig.key as keyof typeof a] > b[sortConfig.key as keyof typeof b]) {
        return sortConfig.direction === 'ascending' ? 1 : -1;
      }
      return 0;
    });
  }, [cryptoData, sortConfig]);

  // Función para renderizar el mini gráfico (sparkline)
  const renderSparkline = (data: number[]) => {
    if (!data || data.length < 2) return null;

    const min = Math.min(...data);
    const max = Math.max(...data);
    const range = max - min;

    // Si todos los valores son iguales, dibujamos una línea recta
    if (range === 0) {
      return (
        <svg className="sparkline" viewBox="0 0 100 30" preserveAspectRatio="none">
          <line x1="0" y1="15" x2="100" y2="15" stroke="#888" strokeWidth="2" />
        </svg>
      );
    }

    const points = data.map((value, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - ((value - min) / range) * 100;
      return `${x},${y}`;
    }).join(' ');

    return (
      <svg className="sparkline" viewBox="0 0 100 30" preserveAspectRatio="none">
        <polyline
          points={points}
          fill="none"
          stroke={data[0] < data[data.length - 1] ? "#00C853" : "#FF3D00"}
          strokeWidth="2"
        />
      </svg>
    );
  };

  if (isLoading) {
    return <div className="crypto-table loading">Cargando criptomonedas...</div>;
  }

  if (error) {
    return (
      <div className="crypto-table-container">
        <h2>Principales Criptomonedas</h2>
        <div className="crypto-table-error">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="crypto-table-container">
      <h2>Principales Criptomonedas</h2>
      <div className="crypto-table-wrapper">
        <table className="crypto-table">
          <thead>
            <tr>
              <th onClick={() => requestSort('rank')}>#</th>
              <th onClick={() => requestSort('name')}>Nombre</th>
              <th onClick={() => requestSort('price')}>Precio</th>
              <th onClick={() => requestSort('priceChange24h')}>24h</th>
              <th>Gráfico 7d</th>
              <th onClick={() => requestSort('marketCap')}>Cap. Mercado</th>
              <th onClick={() => requestSort('volume24h')}>Volumen 24h</th>
            </tr>
          </thead>
          <tbody>
            {sortedData.map((crypto) => (
              <tr
                key={crypto.id}
                onClick={() => onSelectCrypto && onSelectCrypto(crypto.id)}
                className="crypto-table-row"
              >
                <td>{crypto.rank}</td>
                <td className="crypto-name-cell">
                  <div className="crypto-name">
                    {crypto.image && <img src={crypto.image} alt={crypto.name} className="crypto-icon" />}
                    <div>
                      <span className="crypto-fullname">{crypto.name}</span>
                      <span className="crypto-symbol">{crypto.symbol}</span>
                    </div>
                  </div>
                </td>
                <td>${typeof crypto.price === 'number' ? crypto.price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : crypto.price}</td>
                <td className={crypto.priceChange24h >= 0 ? 'positive' : 'negative'}>
                  {crypto.priceChange24h >= 0 ? '▲' : '▼'}{Math.abs(crypto.priceChange24h).toFixed(2)}%
                </td>
                <td className="sparkline-cell">
                  {renderSparkline(crypto.sparkline)}
                </td>
                <td>${crypto.marketCap}</td>
                <td>${crypto.volume24h}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CryptoTable;
