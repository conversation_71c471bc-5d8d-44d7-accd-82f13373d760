import { useState, useEffect, useCallback } from 'react';
import { getCryptoPrice } from '../services/crypto.service';

// Interfaz para los datos de criptomonedas
export interface CryptoGuruData {
  id: string;
  name: string;
  symbol: string;
  price: number;
  priceUsd: number;
  changePercent24Hr: number;
  volumeUsd24Hr: number;
  image: string;
  last_updated: string;
}

// Hook para obtener datos de criptomonedas con información enriquecida
export const useCryptoGuru = (cryptoId?: string) => {
  const [data, setData] = useState<CryptoGuruData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCryptoData = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const cryptoData = await getCryptoPrice(id);

      setData({
        id: cryptoData.id,
        name: cryptoData.name,
        symbol: cryptoData.symbol,
        price: cryptoData.price,
        priceUsd: cryptoData.priceUsd || cryptoData.price,
        changePercent24Hr: cryptoData.changePercent24Hr || cryptoData.price_change_24h,
        volumeUsd24Hr: cryptoData.volumeUsd24Hr || cryptoData.total_volume,
        image: cryptoData.image,
        last_updated: cryptoData.last_updated
      });
    } catch (err: any) {
      console.error(`Error al obtener datos de ${id}:`, err);
      setError(err.message || 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, []);

  // Cargar datos si se proporciona un cryptoId
  useEffect(() => {
    if (cryptoId) {
      fetchCryptoData(cryptoId);
    }
  }, [cryptoId, fetchCryptoData]);

  // Función para obtener datos de una criptomoneda específica
  const getCryptoData = async (id: string) => {
    await fetchCryptoData(id);
    return data;
  };

  // Función para formatear el precio
  const formatPrice = (price: number) => {
    if (price >= 1000) {
      return `$${price.toLocaleString('en-US', { maximumFractionDigits: 2 })}`;
    } else if (price >= 1) {
      return `$${price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `$${price.toLocaleString('en-US', { minimumFractionDigits: 4, maximumFractionDigits: 6 })}`;
    }
  };

  // Función para formatear el cambio porcentual
  const formatChange = (change: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  };

  // Función para formatear el volumen
  const formatVolume = (volume: number) => {
    if (volume >= 1_000_000_000) {
      return `$${(volume / 1_000_000_000).toFixed(2)}B`;
    } else if (volume >= 1_000_000) {
      return `$${(volume / 1_000_000).toFixed(2)}M`;
    } else if (volume >= 1_000) {
      return `$${(volume / 1_000).toFixed(2)}K`;
    } else {
      return `$${volume.toFixed(2)}`;
    }
  };

  // Determinar el estado del avatar basado en el cambio porcentual
  const getAvatarStatus = (change: number): 'idle' | 'thinking' | 'positive' | 'negative' | 'neutral' | 'concerned' => {
    if (change > 5) return 'positive';
    if (change < -5) return 'negative';
    if (change > 2) return 'positive';
    if (change < -2) return 'concerned';
    if (change > 0) return 'neutral';
    if (change < 0) return 'concerned';
    return 'neutral';
  };

  return {
    data,
    loading,
    error,
    getCryptoData,
    formatPrice,
    formatChange,
    formatVolume,
    getAvatarStatus
  };
};
