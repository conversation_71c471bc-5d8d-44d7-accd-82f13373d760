import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/NewAuthContext';
import {
  getCryptoMarketData,
  getLastUpdateTime,
  getPriceAlerts,
  createPriceAlert,
  updatePriceAlert,
  deletePriceAlert,
  getAlertNotifications,
  markNotificationAsRead
} from '../services/cryptoDataService';

// Hook para obtener datos del mercado de criptomonedas
export const useCryptoMarketData = () => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Obtener los datos del mercado
      const marketData = await getCryptoMarketData();
      setData(marketData || []);

      // Obtener la última fecha de actualización
      const updateTime = await getLastUpdateTime();
      setLastUpdated(updateTime);
    } catch (err: any) {
      console.error('Error al obtener datos del mercado:', err);
      setError(err.message || 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();

    // Configurar un intervalo para actualizar los datos cada 5 minutos
    const intervalId = setInterval(fetchData, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, [fetchData]);

  return { data, loading, error, lastUpdated, refresh: fetchData };
};

// Hook para gestionar alertas de precio
export const usePriceAlerts = () => {
  const { user } = useAuth();
  const [alerts, setAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAlerts = useCallback(async () => {
    if (!user) {
      setAlerts([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const userAlerts = await getPriceAlerts(user.uid);
      setAlerts(userAlerts);
    } catch (err: any) {
      console.error('Error al obtener alertas:', err);
      setError(err.message || 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchAlerts();
  }, [fetchAlerts]);

  // Función para crear una nueva alerta
  const addAlert = async (alertData: any) => {
    if (!user) return null;

    try {
      const newAlert = await createPriceAlert(user.uid, alertData);
      setAlerts(prev => [...prev, newAlert]);
      return newAlert;
    } catch (err: any) {
      console.error('Error al crear alerta:', err);
      throw err;
    }
  };

  // Función para actualizar una alerta existente
  const updateAlert = async (alertId: string, updates: any) => {
    try {
      await updatePriceAlert(alertId, updates);
      setAlerts(prev =>
        prev.map(alert =>
          alert.id === alertId ? { ...alert, ...updates } : alert
        )
      );
      return true;
    } catch (err: any) {
      console.error('Error al actualizar alerta:', err);
      throw err;
    }
  };

  // Función para eliminar una alerta
  const removeAlert = async (alertId: string) => {
    try {
      await deletePriceAlert(alertId);
      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
      return true;
    } catch (err: any) {
      console.error('Error al eliminar alerta:', err);
      throw err;
    }
  };

  return {
    alerts,
    loading,
    error,
    addAlert,
    updateAlert,
    removeAlert,
    refresh: fetchAlerts
  };
};

// Hook para gestionar notificaciones de alertas
export const useAlertNotifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  const fetchNotifications = useCallback(async () => {
    if (!user) {
      setNotifications([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const userNotifications = await getAlertNotifications(user.uid);
      setNotifications(userNotifications);

      // Calcular el número de notificaciones no leídas
      const unread = userNotifications.filter(notification => !notification.read).length;
      setUnreadCount(unread);
    } catch (err: any) {
      console.error('Error al obtener notificaciones:', err);
      setError(err.message || 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchNotifications();

    // Configurar un intervalo para verificar nuevas notificaciones cada minuto
    const intervalId = setInterval(fetchNotifications, 60 * 1000);

    return () => clearInterval(intervalId);
  }, [fetchNotifications]);

  // Función para marcar una notificación como leída
  const markAsRead = async (notificationId: string) => {
    try {
      await markNotificationAsRead(notificationId);

      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, read: true, readAt: new Date().toISOString() }
            : notification
        )
      );

      setUnreadCount(prev => Math.max(0, prev - 1));

      return true;
    } catch (err: any) {
      console.error('Error al marcar notificación como leída:', err);
      throw err;
    }
  };

  return {
    notifications,
    loading,
    error,
    unreadCount,
    markAsRead,
    refresh: fetchNotifications
  };
};
