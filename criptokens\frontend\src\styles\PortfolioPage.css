.portfolio-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-darkest);
  color: var(--text-medium);
}

.portfolio-page-header {
  background: var(--gradient-primary);
  color: var(--text-bright);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  margin: 1rem;
}

.portfolio-header-content h1 {
  font-size: 2rem;
  margin: 0;
  font-weight: 700;
}

.portfolio-subtitle {
  margin-top: 0.5rem;
  opacity: 0.9;
  font-size: 1rem;
}

.portfolio-header-actions {
  display: flex;
  gap: 1rem;
}

.portfolio-guru-link,
.back-to-dashboard {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-normal);
}

.portfolio-guru-link {
  background: var(--gradient-primary);
  color: var(--text-bright);
  box-shadow: var(--shadow-sm);
}

.portfolio-guru-link:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-2px);
}

.back-to-dashboard {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.back-to-dashboard:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.guru-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.portfolio-page-content {
  padding: 1rem;
  flex: 1;
  overflow-y: auto;
}

.portfolio-login-prompt {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-md);
  max-width: 500px;
  margin: 0 auto;
  border: var(--border-light);
}

.portfolio-login-prompt p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  color: var(--text-medium);
}

.login-button {
  display: inline-block;
  background: var(--gradient-primary);
  color: var(--text-bright);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.login-button:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .portfolio-page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .portfolio-header-actions {
    width: 100%;
  }

  .portfolio-guru-link,
  .back-to-dashboard {
    flex: 1;
    justify-content: center;
  }
}
