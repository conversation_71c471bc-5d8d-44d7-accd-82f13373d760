/**
 * Script para detener todos los servicios de Criptokens
 */

const { exec } = require('child_process');
const os = require('os');

// Puertos que queremos liberar
const ports = [3001, 3101, 3102, 3103, 5173, 5174, 5175, 5176, 5177];

console.log('Deteniendo todos los servicios de Criptokens...');

// Función para ejecutar comandos según el sistema operativo
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error al ejecutar comando: ${error.message}`);
        return resolve(false);
      }
      if (stderr) {
        console.error(`Error en la salida del comando: ${stderr}`);
      }
      console.log(stdout);
      resolve(true);
    });
  });
}

// Función principal
async function stopServices() {
  const platform = os.platform();

  for (const port of ports) {
    console.log(`Intentando liberar el puerto ${port}...`);

    if (platform === 'win32') {
      // Windows
      const findCommand = `netstat -ano | findstr :${port}`;

      exec(findCommand, async (error, stdout, stderr) => {
        if (error) {
          console.log(`No se encontraron procesos usando el puerto ${port}`);
          return;
        }

        const lines = stdout.trim().split('\n');
        for (const line of lines) {
          const parts = line.trim().split(/\s+/);
          if (parts.length > 4) {
            const pid = parts[parts.length - 1];
            console.log(`Encontrado proceso con PID ${pid} usando el puerto ${port}`);

            try {
              await executeCommand(`taskkill /F /PID ${pid}`);
              console.log(`Proceso con PID ${pid} detenido`);
            } catch (err) {
              console.error(`Error al detener el proceso con PID ${pid}: ${err.message}`);
            }
          }
        }
      });
    } else {
      // Linux/Mac
      const findCommand = `lsof -i :${port} -t`;

      exec(findCommand, async (error, stdout, stderr) => {
        if (error) {
          console.log(`No se encontraron procesos usando el puerto ${port}`);
          return;
        }

        const pids = stdout.trim().split('\n');
        for (const pid of pids) {
          if (pid) {
            console.log(`Encontrado proceso con PID ${pid} usando el puerto ${port}`);

            try {
              await executeCommand(`kill -9 ${pid}`);
              console.log(`Proceso con PID ${pid} detenido`);
            } catch (err) {
              console.error(`Error al detener el proceso con PID ${pid}: ${err.message}`);
            }
          }
        }
      });
    }
  }

  console.log('Esperando 5 segundos para asegurar que todos los procesos se detengan...');
  setTimeout(() => {
    console.log('Todos los servicios han sido detenidos.');
  }, 5000);
}

// Ejecutar la función principal
stopServices();
