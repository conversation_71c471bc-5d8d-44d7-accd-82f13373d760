/**
 * Cliente para comunicarse con el servidor Fetch MCP
 * Este servicio proporciona funciones para obtener contenido web en diferentes formatos
 */
const axios = require('axios');
require('dotenv').config();

// URL base del servidor Fetch MCP
const FETCH_MCP_URL = process.env.FETCH_MCP_URL || 'http://localhost:3104';

/**
 * Obtiene contenido HTML de una URL
 * @param {string} url - URL a obtener
 * @param {Object} headers - Cabeceras HTTP opcionales
 * @returns {Promise<string>} - Contenido HTML
 */
async function fetchHtml(url, headers = {}) {
  try {
    console.log(`Obteniendo HTML de ${url}...`);
    
    const response = await axios.post(`${FETCH_MCP_URL}/tools/fetch_html`, {
      params: {
        url,
        headers
      }
    });
    
    if (response.data && response.data.content && response.data.content[0]) {
      return response.data.content[0].text;
    }
    
    throw new Error('Formato de respuesta inesperado');
  } catch (error) {
    console.error(`Error al obtener HTML de ${url}:`, error.message);
    throw error;
  }
}

/**
 * Obtiene contenido JSON de una URL
 * @param {string} url - URL a obtener
 * @param {Object} headers - Cabeceras HTTP opcionales
 * @returns {Promise<Object>} - Contenido JSON parseado
 */
async function fetchJson(url, headers = {}) {
  try {
    console.log(`Obteniendo JSON de ${url}...`);
    
    const response = await axios.post(`${FETCH_MCP_URL}/tools/fetch_json`, {
      params: {
        url,
        headers
      }
    });
    
    if (response.data && response.data.content && response.data.content[0]) {
      return JSON.parse(response.data.content[0].text);
    }
    
    throw new Error('Formato de respuesta inesperado');
  } catch (error) {
    console.error(`Error al obtener JSON de ${url}:`, error.message);
    throw error;
  }
}

/**
 * Obtiene contenido de texto de una URL
 * @param {string} url - URL a obtener
 * @param {Object} headers - Cabeceras HTTP opcionales
 * @returns {Promise<string>} - Contenido de texto
 */
async function fetchText(url, headers = {}) {
  try {
    console.log(`Obteniendo texto de ${url}...`);
    
    const response = await axios.post(`${FETCH_MCP_URL}/tools/fetch_txt`, {
      params: {
        url,
        headers
      }
    });
    
    if (response.data && response.data.content && response.data.content[0]) {
      return response.data.content[0].text;
    }
    
    throw new Error('Formato de respuesta inesperado');
  } catch (error) {
    console.error(`Error al obtener texto de ${url}:`, error.message);
    throw error;
  }
}

/**
 * Obtiene contenido Markdown de una URL
 * @param {string} url - URL a obtener
 * @param {Object} headers - Cabeceras HTTP opcionales
 * @returns {Promise<string>} - Contenido Markdown
 */
async function fetchMarkdown(url, headers = {}) {
  try {
    console.log(`Obteniendo Markdown de ${url}...`);
    
    const response = await axios.post(`${FETCH_MCP_URL}/tools/fetch_markdown`, {
      params: {
        url,
        headers
      }
    });
    
    if (response.data && response.data.content && response.data.content[0]) {
      return response.data.content[0].text;
    }
    
    throw new Error('Formato de respuesta inesperado');
  } catch (error) {
    console.error(`Error al obtener Markdown de ${url}:`, error.message);
    throw error;
  }
}

/**
 * Verifica si el servidor Fetch MCP está disponible
 * @returns {Promise<boolean>} - true si está disponible, false en caso contrario
 */
async function checkFetchMcpAvailability() {
  try {
    await axios.get(`${FETCH_MCP_URL}/tools`, { timeout: 3000 });
    console.log('Servidor Fetch MCP disponible');
    return true;
  } catch (error) {
    console.error('Servidor Fetch MCP no disponible:', error.message);
    return false;
  }
}

module.exports = {
  fetchHtml,
  fetchJson,
  fetchText,
  fetchMarkdown,
  checkFetchMcpAvailability
};
