/**
 * Configuración de Socket.io para el servidor
 */
const socketIo = require('socket.io');

// Función para inicializar Socket.io
const initializeSocketIO = (server) => {
  // Configurar Socket.IO
  const io = socketIo(server, {
    cors: {
      origin: ['http://localhost:5173', 'http://localhost:5174'],
      methods: ['GET', 'POST'],
      credentials: true
    }
  });

  // Manejar conexiones de Socket.IO
  io.on('connection', (socket) => {
    console.log('Cliente conectado a Socket.IO:', socket.id);

    // Unirse a una sala específica para la llamada
    socket.on('join-call', (callId) => {
      socket.join(callId);
      console.log(`Cliente ${socket.id} unido a sala de llamada ${callId}`);
    });

    socket.on('disconnect', () => {
      console.log('Cliente desconectado de Socket.IO:', socket.id);
    });
  });

  // Hacer io disponible globalmente
  global.io = io;

  return io;
};

module.exports = { initializeSocketIO };
