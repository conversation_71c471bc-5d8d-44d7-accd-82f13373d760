/**
 * Funciones de utilidad para el sistema de agentes MCP
 * 
 * Este módulo proporciona funciones de utilidad generales
 * utilizadas en todo el sistema.
 */

const crypto = require('crypto');

/**
 * Genera un ID único
 * @param {string} [prefix=''] - Prefijo para el ID
 * @returns {string} ID único
 */
function generateId(prefix = '') {
  const randomBytes = crypto.randomBytes(8).toString('hex');
  const timestamp = Date.now().toString(36);
  return `${prefix}${timestamp}-${randomBytes}`;
}

/**
 * Espera un tiempo determinado
 * @param {number} ms - Tiempo a esperar en milisegundos
 * @returns {Promise<void>} Promesa que se resuelve después del tiempo especificado
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Ejecuta una función con reintentos
 * @param {Function} fn - Función a ejecutar
 * @param {Object} options - Opciones de reintento
 * @param {number} [options.maxRetries=3] - Número máximo de reintentos
 * @param {number} [options.initialDelay=1000] - Retraso inicial en ms
 * @param {number} [options.maxDelay=10000] - Retraso máximo en ms
 * @param {Function} [options.shouldRetry] - Función que determina si se debe reintentar
 * @returns {Promise<any>} Resultado de la función
 */
async function withRetry(fn, options = {}) {
  const maxRetries = options.maxRetries || 3;
  const initialDelay = options.initialDelay || 1000;
  const maxDelay = options.maxDelay || 10000;
  const shouldRetry = options.shouldRetry || (() => true);
  
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Verificar si debemos reintentar
      if (attempt >= maxRetries || !shouldRetry(error)) {
        throw error;
      }
      
      // Calcular retraso con backoff exponencial
      const delay = Math.min(initialDelay * Math.pow(2, attempt), maxDelay);
      
      // Añadir jitter (variación aleatoria)
      const jitter = Math.random() * 0.3 * delay;
      const finalDelay = delay + jitter;
      
      // Esperar antes de reintentar
      await sleep(finalDelay);
    }
  }
  
  // Este punto no debería alcanzarse, pero por si acaso
  throw lastError;
}

/**
 * Limita el número de promesas concurrentes
 * @param {Function[]} tasks - Array de funciones que devuelven promesas
 * @param {number} concurrency - Número máximo de promesas concurrentes
 * @returns {Promise<any[]>} Resultados de las promesas
 */
async function limitConcurrency(tasks, concurrency) {
  const results = [];
  const executing = [];
  
  for (const task of tasks) {
    const p = Promise.resolve().then(() => task());
    results.push(p);
    
    if (concurrency <= tasks.length) {
      const e = p.then(() => executing.splice(executing.indexOf(e), 1));
      executing.push(e);
      
      if (executing.length >= concurrency) {
        await Promise.race(executing);
      }
    }
  }
  
  return Promise.all(results);
}

/**
 * Formatea una fecha en formato legible
 * @param {Date} date - Fecha a formatear
 * @returns {string} Fecha formateada
 */
function formatDate(date) {
  return date.toLocaleString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * Trunca un texto a una longitud máxima
 * @param {string} text - Texto a truncar
 * @param {number} maxLength - Longitud máxima
 * @returns {string} Texto truncado
 */
function truncateText(text, maxLength = 100) {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Sanitiza un objeto para logging (elimina información sensible)
 * @param {Object} obj - Objeto a sanitizar
 * @param {string[]} [sensitiveKeys=['password', 'token', 'key', 'secret', 'credential']] - Claves sensibles
 * @returns {Object} Objeto sanitizado
 */
function sanitizeForLogging(obj, sensitiveKeys = ['password', 'token', 'key', 'secret', 'credential']) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  // Crear una copia para no modificar el original
  const sanitized = Array.isArray(obj) ? [...obj] : {...obj};
  
  // Recorrer todas las propiedades
  for (const key in sanitized) {
    if (Object.prototype.hasOwnProperty.call(sanitized, key)) {
      // Verificar si la clave es sensible
      const isSensitive = sensitiveKeys.some(sensitiveKey => 
        key.toLowerCase().includes(sensitiveKey.toLowerCase())
      );
      
      if (isSensitive && typeof sanitized[key] === 'string') {
        // Reemplazar el valor con asteriscos
        sanitized[key] = '********';
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        // Recursivamente sanitizar objetos anidados
        sanitized[key] = sanitizeForLogging(sanitized[key], sensitiveKeys);
      }
    }
  }
  
  return sanitized;
}

module.exports = {
  generateId,
  sleep,
  withRetry,
  limitConcurrency,
  formatDate,
  truncateText,
  sanitizeForLogging
};
