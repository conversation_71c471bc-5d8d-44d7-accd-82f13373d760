/* Estilos para el componente de distribución del portafolio */

.portfolio-distribution {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.portfolio-distribution h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1.25rem 0;
  color: var(--text-bright);
}

.chart-container {
  flex: 1;
  position: relative;
  min-height: 300px;
}

/* Estilos para la distribución estática */
.static-distribution {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 10px 0;
}

.asset-bar {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 4px;
}

.asset-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.asset-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.asset-name {
  font-size: 0.9rem;
  color: var(--text-bright);
}

.asset-bar-container {
  width: 100%;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.asset-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.asset-value {
  font-size: 0.8rem;
  color: var(--text-medium);
  text-align: right;
}

/* Estilos para el gráfico vacío */
.empty-chart {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.empty-chart-message,
.chart-loading,
.chart-error {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  padding: 2rem;
  text-align: center;
  min-height: 200px;
}

.empty-chart-message p,
.chart-error p {
  color: var(--text-dim);
  margin: 0.5rem 0;
}

.chart-error .error-details {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  max-width: 80%;
  word-break: break-word;
}

/* Responsive */
@media (max-width: 768px) {
  .portfolio-distribution {
    padding: 1.25rem;
  }

  .chart-container {
    min-height: 250px;
  }
}
