.guru-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, rgba(15, 17, 35, 0.9) 0%, rgba(28, 31, 55, 0.9) 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  position: relative;
}

/* Sección del agente */
.guru-agent-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 180px;
  position: relative;
  z-index: 2;
  padding: 20px;
  background: rgba(15, 17, 35, 0.5);
  border-bottom: 1px solid rgba(0, 242, 255, 0.2);
}

/* Contenedor para el Avatar */
.avatar-wrapper {
  width: 150px;
  height: 150px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar3d-container {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 242, 255, 0.3);
}

.avatar-status-indicator {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #00f2ff;
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 12px;
  text-transform: capitalize;
  z-index: 10;
}

.thinking-indicator {
  position: absolute;
  bottom: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: #00f2ff;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 14px;
  animation: blink 1.5s infinite;
}

.portfolio-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
}

.portfolio-indicator {
  background: rgba(123, 77, 255, 0.7);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(123, 77, 255, 0.3);
}

.portfolio-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #00ff9d;
  border-radius: 50%;
  margin-right: 8px;
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.7);
}

.portfolio-prompt {
  background: rgba(0, 242, 255, 0.2);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 242, 255, 0.3);
}

.portfolio-link {
  color: #00f2ff;
  font-weight: 600;
  text-decoration: none;
  margin-left: 5px;
  transition: all 0.2s ease;
}

.portfolio-link:hover {
  color: white;
  text-decoration: underline;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Sección de conversación */
.guru-conversation-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(15, 17, 35, 0.7);
  backdrop-filter: blur(10px);
  z-index: 2;
  overflow: hidden;
}

.guru-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 242, 255, 0.5) rgba(15, 17, 35, 0.5);
}

.guru-messages-container::-webkit-scrollbar {
  width: 6px;
}

.guru-messages-container::-webkit-scrollbar-track {
  background: rgba(15, 17, 35, 0.5);
}

.guru-messages-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 242, 255, 0.5);
  border-radius: 6px;
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  padding: 20px;
}

.empty-chat h3 {
  margin-bottom: 15px;
  color: #00f2ff;
  font-size: 24px;
}

.empty-chat p {
  margin-bottom: 10px;
  font-size: 16px;
  max-width: 400px;
}

.empty-chat .example {
  font-style: italic;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 20px;
  padding: 10px;
  background: rgba(0, 242, 255, 0.1);
  border-radius: 8px;
  border-left: 3px solid #00f2ff;
}

/* Mensajes */
.guru-message {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.guru-message.user-message {
  align-self: flex-end;
  background: linear-gradient(135deg, rgba(0, 242, 255, 0.2) 0%, rgba(0, 150, 255, 0.2) 100%);
  border: 1px solid rgba(0, 242, 255, 0.3);
  color: white;
}

.guru-message.ai-message {
  align-self: flex-start;
  background: linear-gradient(135deg, rgba(123, 77, 255, 0.2) 0%, rgba(77, 77, 255, 0.2) 100%);
  border: 1px solid rgba(123, 77, 255, 0.3);
  color: white;
}

.message-content {
  margin-bottom: 5px;
}

/* Estilos para el contenido Markdown */
.markdown-content {
  color: white;
  line-height: 1.5;
  width: 100%;
  overflow-wrap: break-word;
}

.markdown-content p {
  margin-bottom: 12px;
}

.markdown-content strong {
  font-weight: 700;
  color: #00f2ff;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  color: #00f2ff;
}

.markdown-content ul, .markdown-content ol {
  margin-left: 20px;
  margin-bottom: 12px;
}

.markdown-content li {
  margin-bottom: 6px;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content blockquote {
  border-left: 3px solid #00f2ff;
  padding-left: 10px;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
}

.markdown-content code {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 12px;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
}

.message-timestamp {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  text-align: right;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Tarjeta de datos de criptomoneda */
.crypto-data-card {
  margin-top: 15px;
  background: rgba(15, 17, 35, 0.7);
  border-radius: 10px;
  padding: 12px;
  border: 1px solid rgba(123, 77, 255, 0.3);
}

/* Contenedor de imagen generada */
.generated-image-container {
  margin-top: 15px;
  margin-bottom: 15px;
  background: rgba(15, 17, 35, 0.7);
  border-radius: 10px;
  padding: 12px;
  border: 1px solid rgba(0, 242, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.generated-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.image-caption {
  margin-top: 10px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-description-toggle {
  background: rgba(0, 242, 255, 0.2);
  border: none;
  color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  margin-left: 10px;
  transition: background-color 0.2s;
}

.image-description-toggle:hover {
  background: rgba(0, 242, 255, 0.4);
}

.image-description {
  margin-top: 10px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  white-space: pre-line;
  max-height: 200px;
  overflow-y: auto;
}

/* Tarjeta de noticias */
.news-data-card {
  margin-top: 15px;
  background: rgba(15, 17, 35, 0.7);
  border-radius: 10px;
  padding: 12px;
  border: 1px solid rgba(123, 77, 255, 0.3);
}

/* Estilos para la visualización de páginas web */
.webpage-view-container {
  margin-top: 15px;
  background: rgba(15, 17, 35, 0.7);
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(0, 242, 255, 0.3);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.crypto-data-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.crypto-icon {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  margin-right: 10px;
  border-radius: 50%;
  vertical-align: middle !important;
  object-fit: contain !important;
}

.crypto-data-header h4 {
  margin: 0;
  font-size: 16px;
  color: white;
}

.crypto-data-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.crypto-data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.crypto-data-item .label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.crypto-data-item .value {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.crypto-data-item .value.positive {
  color: #00ff9d;
}

.crypto-data-item .value.negative {
  color: #ff3a6e;
}

/* Estilos para noticias */
.news-data-header {
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.news-data-header h4 {
  margin: 0;
  font-size: 16px;
  color: white;
  text-transform: capitalize;
}

.news-data-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.news-item {
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid rgba(0, 242, 255, 0.5);
}

.news-title {
  margin: 0 0 8px 0;
  font-size: 15px;
}

.news-title a {
  color: #00f2ff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.news-title a:hover {
  color: #7b4dff;
  text-decoration: underline;
}

.news-description {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.news-source {
  font-weight: 600;
}

.news-date {
  font-style: italic;
}

/* Contenedor de entrada */
.guru-input-container {
  display: flex;
  padding: 15px;
  background: rgba(15, 17, 35, 0.8);
  border-top: 1px solid rgba(0, 242, 255, 0.2);
}

.guru-message-input {
  flex: 1;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 242, 255, 0.3);
  border-radius: 24px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.guru-message-input:focus {
  border-color: rgba(0, 242, 255, 0.7);
  box-shadow: 0 0 0 2px rgba(0, 242, 255, 0.2);
}

.guru-message-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.guru-voice-button {
  margin-left: 10px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #7b4dff 0%, #5a36b1 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(123, 77, 255, 0.3);
}

.guru-voice-button:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 15px rgba(123, 77, 255, 0.5);
}

.guru-voice-button:active {
  transform: scale(0.95);
}

.guru-voice-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  cursor: not-allowed;
  box-shadow: none;
}

.guru-voice-button.listening {
  background: linear-gradient(135deg, #ff3a6e 0%, #ff7676 100%);
  animation: pulse 1.5s infinite;
}

.guru-voice-button svg {
  width: 20px;
  height: 20px;
  stroke: white;
  stroke-width: 2;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 58, 110, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 58, 110, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 58, 110, 0);
  }
}

.guru-send-button {
  margin-left: 10px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #00f2ff 0%, #0084ff 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 242, 255, 0.3);
}

.guru-send-button:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 15px rgba(0, 242, 255, 0.5);
}

.guru-send-button:active {
  transform: scale(0.95);
}

.guru-send-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  cursor: not-allowed;
  box-shadow: none;
}

.guru-send-button svg {
  width: 20px;
  height: 20px;
  stroke: white;
  stroke-width: 2;
}

.guru-send-button.loading {
  background: linear-gradient(135deg, rgba(0, 242, 255, 0.5) 0%, rgba(0, 132, 255, 0.5) 100%);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Estilos para la página del Gurú Cripto */
.crypto-guru-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f1123 0%, #1c1f37 100%);
  color: white;
}

.guru-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(15, 17, 35, 0.8);
  border-bottom: 1px solid rgba(0, 242, 255, 0.2);
}

.guru-header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.portfolio-header-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(123, 77, 255, 0.2);
  border: 1px solid rgba(123, 77, 255, 0.3);
  border-radius: 20px;
  color: white;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.portfolio-header-link:hover {
  background: rgba(123, 77, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(123, 77, 255, 0.3);
}

.portfolio-header-link svg {
  color: rgba(123, 77, 255, 0.9);
}

.info-button, .voice-toggle-button {
  background: none;
  border: none;
  color: rgba(0, 242, 255, 0.7);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.info-button:hover, .voice-toggle-button:hover {
  color: #00f2ff;
  background: rgba(0, 242, 255, 0.1);
  transform: scale(1.1);
}

.voice-toggle-button.active {
  color: #00f2ff;
  background: rgba(0, 242, 255, 0.2);
  box-shadow: 0 0 10px rgba(0, 242, 255, 0.3);
}

.guru-info-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.guru-info-content {
  background: linear-gradient(135deg, rgba(15, 17, 35, 0.95) 0%, rgba(28, 31, 55, 0.95) 100%);
  border-radius: 16px;
  padding: 30px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 242, 255, 0.3);
  color: white;
}

.guru-info-content h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 24px;
  background: linear-gradient(90deg, #00f2ff, #7b4dff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.guru-info-content h3 {
  margin-top: 25px;
  margin-bottom: 10px;
  font-size: 18px;
  color: #00f2ff;
}

.guru-info-content p {
  margin-bottom: 15px;
  line-height: 1.6;
}

.guru-info-content ul {
  margin-bottom: 20px;
  padding-left: 20px;
}

.guru-info-content li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.brave-search-info {
  margin-top: 25px;
  padding: 15px;
  background: rgba(0, 242, 255, 0.1);
  border-radius: 10px;
  border-left: 3px solid #00f2ff;
}

.close-info-button {
  margin-top: 25px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #00f2ff 0%, #0084ff 100%);
  border: none;
  border-radius: 24px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  width: 100%;
}

.close-info-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 242, 255, 0.3);
}

.guru-header h1 {
  margin: 0;
  font-size: 28px;
  background: linear-gradient(90deg, #00f2ff, #7b4dff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #00f2ff;
  text-decoration: none;
  font-size: 16px;
  transition: all 0.3s ease;
}

.back-link:hover {
  color: #7b4dff;
  transform: translateX(-5px);
}

.guru-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 5px 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
}

.guru-status.online .status-dot {
  background-color: #00ff9d;
  box-shadow: 0 0 10px rgba(0, 255, 157, 0.7);
}

.guru-status.offline .status-dot {
  background-color: #ff3a6e;
  box-shadow: 0 0 10px rgba(255, 58, 110, 0.7);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.guru-content {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.guru-content .guru-chat-container {
  max-width: 900px;
  width: 100%;
  height: calc(100vh - 140px);
}

/* Estilos para el layout con sidebar */
.guru-layout {
  display: flex;
  width: 100%;
  height: calc(100vh - 140px);
  max-width: 1600px;
}

.guru-chat-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  margin-right: 20px;
}

.sidebar-toggle {
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  background: var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  transform: translateY(-50%) scale(1.1);
  background: var(--color-primary-dark);
}

.sidebar-toggle i {
  color: white;
  font-size: 12px;
}

/* Responsive */
@media (max-width: 1200px) {
  .guru-layout {
    max-width: 100%;
  }
}

@media (max-width: 992px) {
  .guru-chat-wrapper {
    margin-right: 10px;
  }
}

@media (max-width: 768px) {
  .guru-agent-section {
    height: 150px;
  }

  .guru-message {
    max-width: 90%;
  }

  .crypto-data-card {
    padding: 10px;
  }

  .crypto-data-header h4 {
    font-size: 14px;
  }

  .crypto-data-item .label,
  .crypto-data-item .value {
    font-size: 12px;
  }

  .guru-layout {
    flex-direction: column;
  }

  .guru-chat-wrapper {
    margin-right: 0;
    margin-bottom: 20px;
  }
}

/* Estilos para análisis técnico */
.technical-analysis-container {
  margin-top: 15px;
  width: 100%;
  max-width: 800px;
}

/* Estilos para análisis fundamental */
.fundamental-analysis-container {
  margin-top: 15px;
  width: 100%;
  max-width: 800px;
}
