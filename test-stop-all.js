/**
 * Script para probar la detención de procesos
 */
const { spawn } = require('child_process');

async function runTest() {
  console.log('Prueba del script stop_all.js:');
  
  // Iniciar un proceso simple en el puerto 12345 para probar
  const server = spawn('node', ['-e', `
    const http = require('http');
    const server = http.createServer((req, res) => {
      res.writeHead(200);
      res.end('Hello World');
    });
    server.listen(12345, () => {
      console.log('Servidor de prueba iniciado en el puerto 12345');
    });
  `], {
    stdio: 'pipe',
    shell: true
  });
  
  server.stdout.on('data', (data) => {
    console.log(`[Servidor de prueba] ${data.toString().trim()}`);
  });
  
  // Esperar a que el servidor se inicie
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Verificar que el puerto está en uso
  const { checkTcpPort } = require('./health-checker');
  const portInUse = await checkTcpPort(12345);
  console.log(`Puerto 12345 en uso: ${portInUse}`);
  
  if (!portInUse) {
    console.error('Error: El servidor de prueba no se inició correctamente.');
    process.exit(1);
  }
  
  // Ejecutar stop_all.js
  console.log('\nEjecutando stop_all.js...');
  
  const stopProcess = spawn('node', ['stop_all.js'], {
    stdio: 'inherit',
    shell: true
  });
  
  // Esperar a que stop_all.js termine
  await new Promise(resolve => {
    stopProcess.on('close', (code) => {
      console.log(`stop_all.js terminó con código ${code}`);
      resolve();
    });
  });
  
  // Verificar que el puerto ya no está en uso
  const portStillInUse = await checkTcpPort(12345);
  console.log(`\nPuerto 12345 en uso después de stop_all.js: ${portStillInUse}`);
  
  if (portStillInUse) {
    console.error('Error: stop_all.js no detuvo el proceso correctamente.');
    server.kill();
  } else {
    console.log('Prueba exitosa: stop_all.js detuvo el proceso correctamente.');
  }
}

runTest().catch(err => {
  console.error('Error en la prueba de stop_all.js:', err);
});
