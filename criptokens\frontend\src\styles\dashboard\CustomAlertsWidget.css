.custom-alerts-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.custom-alerts-widget:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.alerts-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.add-alert-button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.add-alert-button:hover {
  background-color: var(--color-primary);
  color: white;
  transform: scale(1.05);
}

.add-alert-panel {
  padding: 1rem;
  background-color: var(--color-surface-dark);
  border-bottom: 1px solid var(--border-color);
}

.alert-type-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.type-button {
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 0.5rem 0.75rem;
  color: var(--text-secondary);
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.type-button:hover {
  background-color: var(--color-surface);
  color: var(--text-primary);
}

.type-button.active {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.add-alert-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.cancel-button, .confirm-button {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.cancel-button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.confirm-button {
  background-color: var(--color-primary);
  border: 1px solid var(--color-primary);
  color: white;
}

.confirm-button:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-1px);
}

.alerts-content {
  padding: 0.75rem;
  overflow-y: auto;
  flex: 1;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--border-radius-md);
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.alert-item:hover {
  background-color: var(--color-surface-dark);
  border-color: var(--border-color-hover);
}

.alert-item.inactive {
  opacity: 0.6;
}

.alert-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.alert-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.alert-asset {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.asset-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  object-fit: contain;
}

.asset-name {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.asset-symbol {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  margin-left: 0.25rem;
}

.alert-type {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.alert-description {
  margin: 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.alert-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.toggle-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.toggle-track {
  width: 36px;
  height: 20px;
  background-color: var(--text-tertiary);
  border-radius: 10px;
  position: relative;
  transition: all 0.2s ease;
}

.toggle-thumb {
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.2s ease;
}

.toggle-button.active .toggle-track {
  background-color: var(--color-primary);
}

.toggle-button.active .toggle-thumb {
  left: calc(100% - 18px);
}

.delete-button {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.delete-button:hover {
  color: var(--color-negative);
  background-color: rgba(255, 58, 110, 0.1);
}

.empty-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-tertiary);
}

.empty-alerts i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-alerts p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
}

.create-alert-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-alert-button:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-1px);
}

/* Esqueleto de carga */
.custom-alerts-widget.loading .skeleton-loading {
  width: 100%;
  height: 200px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .alerts-content {
    padding: 0.5rem;
  }
  
  .alert-item {
    padding: 0.625rem;
    flex-wrap: wrap;
  }
  
  .alert-actions {
    width: 100%;
    justify-content: flex-end;
    margin-top: 0.5rem;
  }
  
  .alert-type-selector {
    flex-wrap: wrap;
  }
}
