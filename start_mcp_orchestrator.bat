@echo off
echo Starting MCP servers and Guru Cripto Orchestrator...
echo.

REM Start MCP servers in separate windows
start "MCP Crypto Server" cmd /c "node crypto-mcp-server.js"
timeout /t 2 > nul
start "MCP Brave Server" cmd /c "node brave-search-server.js"
timeout /t 2 > nul
start "MCP Playwright Server" cmd /c "node playwright-mcp-server.js"
timeout /t 2 > nul
start "Context7 MCP Server" cmd /c "npx -y @upstash/context7-mcp@latest"
timeout /t 2 > nul
start "OnChain MCP Server" cmd /c "cd onchain-mcp-server && npm install && node index.js"
timeout /t 5 > nul

REM Start Guru Cripto Orchestrator
echo Starting Guru Cripto Orchestrator...
python start_guru_orchestrator.py

echo.
echo All servers and the Guru Cripto Orchestrator are running.
echo Press any key to stop all servers...
pause > nul

REM Kill all servers
taskkill /FI "WINDOWTITLE eq MCP Crypto Server*" /F
taskkill /FI "WINDOWTITLE eq MCP Brave Server*" /F
taskkill /FI "WINDOWTITLE eq MCP Playwright Server*" /F
taskkill /FI "WINDOWTITLE eq Context7 MCP Server*" /F
taskkill /FI "WINDOWTITLE eq OnChain MCP Server*" /F

echo All servers stopped.
pause
