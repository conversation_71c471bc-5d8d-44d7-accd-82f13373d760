/* Estilos mejorados para el dashboard inteligente */
.intelligent-dashboard {
  width: 100%;
  max-width: 100%;
  padding: clamp(1rem, 2vw, 2rem);
  background-color: var(--color-background);
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: clamp(1.5rem, 2.5vw, 2.5rem);
  box-sizing: border-box;
}

/* Mejora del header con estadísticas globales */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(0.75rem, 1.25vw, 1.25rem);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  flex-wrap: wrap;
  gap: 1.25rem;
  border: 1px solid var(--border-color);
}

.global-stats {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(1rem, 1.5vw, 1.5rem);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: clamp(0.8rem, 1vw, 0.9rem);
  padding: 0.5rem 0.75rem;
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.stat-item:hover {
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.stat-label {
  color: var(--text-secondary);
  white-space: nowrap;
  font-weight: var(--font-weight-medium);
}

.stat-value {
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  white-space: nowrap;
}

.change {
  margin-left: 0.25rem;
  font-size: 0.75em;
  padding: 0.25rem 0.375rem;
  border-radius: 4px;
  font-weight: var(--font-weight-semibold);
}

.change.positive {
  color: var(--color-positive);
  background-color: rgba(0, 255, 157, 0.15);
}

.change.negative {
  color: var(--color-negative);
  background-color: rgba(255, 58, 110, 0.15);
}

.loading-stats, .error-stats {
  width: 100%;
  padding: 0.75rem;
  font-size: 0.9rem;
  text-align: center;
  border-radius: var(--border-radius-md);
}

.loading-stats {
  background-color: var(--color-surface);
  color: var(--text-secondary);
}

.error-stats {
  background-color: rgba(255, 58, 110, 0.1);
  color: var(--color-negative);
}

.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  min-width: 250px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-surface);
  border-radius: 8px;
  padding: 0.625rem 0.875rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 300px;
  box-shadow: var(--shadow-sm);
}

.search-bar:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 224, 255, 0.2);
}

.search-bar input {
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  width: 100%;
  font-size: 0.9rem;
}

.search-bar input::placeholder {
  color: var(--text-tertiary);
}

.search-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  color: var(--color-primary);
}

/* Contenido principal mejorado */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

/* Barra superior con resumen del mercado */
.dashboard-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  padding: 1rem 1.25rem;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
}

.market-summary-strip {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.market-summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.market-summary-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -1rem;
  top: 50%;
  transform: translateY(-50%);
  height: 70%;
  width: 1px;
  background-color: var(--border-color);
}

.market-summary-item .label {
  font-size: 0.85rem;
  color: var(--text-tertiary);
  font-weight: var(--font-weight-medium);
}

.market-summary-item .value {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
}

.market-summary-item .change {
  font-size: 0.8rem;
  font-weight: var(--font-weight-semibold);
}

.welcome-message {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: var(--font-weight-semibold);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}

/* Contenedor principal con dos columnas mejorado */
.dashboard-main-container {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: clamp(1.5rem, 2.5vw, 2.5rem);
}

/* Panel lateral mejorado */
.dashboard-sidebar {
  display: flex;
  flex-direction: column;
  gap: clamp(1.5rem, 2vw, 2rem);
}

/* Estilos mejorados para el widget del Guru */
.guru-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all 0.3s ease;
}

.guru-widget:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-color-hover);
  transform: translateY(-3px);
}

.guru-header {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  padding: 0.875rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.guru-header h3 {
  margin: 0;
  color: white;
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.guru-content {
  padding: 1.25rem;
  display: flex;
  gap: 1.25rem;
  align-items: center;
}

.guru-avatar-container {
  flex-shrink: 0;
}

.guru-quick-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.guru-action-button {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 0.625rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 0.95rem;
}

.guru-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 66, 202, 0.5);
}

.guru-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
}

.suggestion-chip {
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 0.375rem 0.875rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-chip:hover {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-2px);
}

/* Estilos mejorados para los indicadores de sentimiento */
.sentiment {
  font-weight: var(--font-weight-semibold);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-left: 0.25rem;
}

.sentiment.extreme_fear {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.15);
}

.sentiment.fear {
  color: #e67e22;
  background-color: rgba(230, 126, 34, 0.15);
}

.sentiment.neutral {
  color: #f1c40f;
  background-color: rgba(241, 196, 15, 0.15);
}

.sentiment.greed {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.15);
}

.sentiment.extreme_greed {
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.15);
}

/* Secciones horizontales y verticales mejoradas */
.dashboard-horizontal-sections {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: clamp(1.5rem, 2vw, 2rem);
  margin-bottom: 1.5rem;
}

.dashboard-vertical-sections {
  display: flex;
  flex-direction: column;
  gap: clamp(1.5rem, 2vw, 2rem);
}

/* Contenido principal del dashboard mejorado */
.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: clamp(1.5rem, 2vw, 2rem);
}

/* Secciones del dashboard mejoradas */
.dashboard-section {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  padding: clamp(1.25rem, 1.75vw, 1.75rem);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.dashboard-section:hover {
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: clamp(1rem, 1.75vw, 1.75rem);
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 80px;
  height: 3px;
  background-color: var(--color-primary);
}

.section-header h2 {
  font-size: clamp(1.1rem, 1.5vw, 1.35rem);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

/* Filtros mejorados */
.section-header.with-filters {
  flex-wrap: wrap;
  gap: 1rem;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 0.875rem;
}

.refresh-button, .view-all-button {
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
}

.refresh-button {
  color: var(--text-secondary);
}

.refresh-button:hover {
  color: var(--text-primary);
  background-color: var(--color-surface-light);
}

.refresh-button i {
  font-size: 0.95rem;
}

.view-all-button {
  color: var(--color-primary);
}

.view-all-button:hover {
  background-color: var(--color-primary-transparent);
  transform: translateX(2px);
}

.view-all-button i {
  font-size: 0.85rem;
}

.desktop-filters {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  margin-top: 0.5rem;
}

.time-filter, .category-filter {
  display: flex;
  gap: 0.375rem;
}

.time-filter button, .category-filter button {
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.375rem 0.75rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-filter button:hover, .category-filter button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.time-filter button.active, .category-filter button.active {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

/* Sección de tabla de criptomonedas */
.crypto-table-section {
  grid-column: span 12;
  margin-top: 1rem;
}

/* Estilos responsivos mejorados */
@media (max-width: 1200px) {
  .dashboard-main-container {
    grid-template-columns: 280px 1fr;
  }

  .market-summary-strip {
    gap: 1.5rem;
  }

  .dashboard-horizontal-sections {
    grid-template-columns: 1.5fr 1fr;
  }
}

@media (max-width: 992px) {
  .dashboard-top-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .market-summary-strip {
    width: 100%;
    justify-content: space-between;
  }

  .market-summary-item:not(:last-child)::after {
    display: none;
  }

  .welcome-message {
    width: 100%;
    text-align: left;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
  }

  .dashboard-main-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .dashboard-sidebar {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .guru-widget {
    grid-column: span 2;
  }

  .dashboard-horizontal-sections {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .dashboard-vertical-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .desktop-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    width: 100%;
  }

  .time-filter, .category-filter {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.375rem;
  }
}

@media (max-width: 768px) {
  .intelligent-dashboard {
    padding: 1rem;
    gap: 1.5rem;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }

  .global-stats {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.75rem;
    justify-content: flex-start;
    gap: 1.25rem;
  }

  .stat-item {
    flex-basis: auto;
  }

  .search-container {
    width: 100%;
    justify-content: flex-start;
  }

  .search-bar {
    max-width: 100%;
  }

  .dashboard-sidebar {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .guru-widget {
    grid-column: span 1;
  }

  .guru-content {
    flex-direction: column;
    text-align: center;
    gap: 1.25rem;
  }

  .guru-avatar-container {
    margin: 0 auto;
  }

  .desktop-filters {
    display: none;
  }

  .mobile-filter-button {
    display: flex;
  }

  .mobile-filters {
    display: flex;
  }

  .dashboard-main {
    gap: 1.5rem;
  }

  .dashboard-vertical-sections {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* Ajustar altura de widgets para evitar scroll excesivo */
  .news-section,
  .trends-section,
  .events-section,
  .alerts-section {
    max-height: 450px;
    overflow-y: auto;
  }
}

@media (max-width: 576px) {
  .intelligent-dashboard {
    padding: 0.75rem;
    gap: 1.25rem;
  }

  .dashboard-top-bar {
    padding: 0.75rem 1rem;
  }

  .market-summary-strip {
    flex-direction: column;
    gap: 0.75rem;
  }

  .market-summary-item {
    justify-content: space-between;
    width: 100%;
  }

  .dashboard-section {
    padding: 1rem;
  }

  .section-header h2 {
    font-size: 1.1rem;
  }

  /* Ajustar espaciado para pantallas pequeñas */
  .dashboard-main {
    gap: 1.25rem;
  }

  /* Simplificar la visualización en móviles muy pequeños */
  .dashboard-section {
    margin-bottom: 0.75rem;
  }

  /* Ajustar altura para evitar scroll excesivo */
  .news-section,
  .trends-section,
  .events-section,
  .alerts-section {
    max-height: 400px;
  }

  /* Ocultar elementos menos importantes en pantallas muy pequeñas */
  .section-header .actions {
    display: none;
  }
}
