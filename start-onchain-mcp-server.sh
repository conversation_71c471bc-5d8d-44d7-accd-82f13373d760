#!/bin/bash

# Script para iniciar OnChain MCP Server

echo -e "\033[36mIniciando OnChain MCP Server...\033[0m"
echo ""

# Cambiar al directorio del servidor
cd onchain-mcp-server

# Instalar dependencias si es necesario
if [ ! -d "node_modules" ]; then
    echo -e "\033[33mInstalando dependencias...\033[0m"
    npm install
fi

# Iniciar el servidor
echo -e "\033[32mIniciando servidor...\033[0m"
npm start

# Volver al directorio original
cd ..
