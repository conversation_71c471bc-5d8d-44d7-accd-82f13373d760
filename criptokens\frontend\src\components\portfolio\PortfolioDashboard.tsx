import React, { useState } from 'react';
import { usePortfolio } from '../../hooks/usePortfolio';
import { formatNumber, formatPercentage } from '../../utils/formatters';
import PortfolioSummary from './PortfolioSummary';
import PortfolioDistribution from './PortfolioDistribution';
import EnhancedDistributionChart from './EnhancedDistributionChart';
import PortfolioPerformance from './PortfolioPerformance';
import AdvancedPortfolioChart from './AdvancedPortfolioChart';
import PortfolioAssetsList from './PortfolioAssetsList';
import PortfolioTransactions from './PortfolioTransactions';
import PortfolioAnalysis from './PortfolioAnalysis';
import AddAssetModal from './AddAssetModal';
import AddFundsModal from './AddFundsModal';
import LoadingSpinner from '../common/LoadingSpinner';
import '../../styles/portfolio/PortfolioDashboard.css';

const PortfolioDashboard: React.FC = () => {
  const {
    portfolio,
    portfolioStats,
    isLoading,
    error,
    availableCryptos,
    userFunds,
    addAssetToPortfolio,
    removeAssetFromPortfolio,
    refreshPrices,
    addFunds,
    loadUserFunds,
    resetPortfolio
  } = usePortfolio();

  const [activeTab, setActiveTab] = useState<'overview' | 'assets' | 'transactions' | 'analysis'>('overview');
  const [showAddAssetModal, setShowAddAssetModal] = useState(false);
  const [showAddFundsModal, setShowAddFundsModal] = useState(false);

  // Manejar la apertura del modal de añadir fondos
  const handleOpenAddFundsModal = async () => {
    // Actualizar los fondos del usuario antes de abrir el modal
    await loadUserFunds();
    setShowAddFundsModal(true);
  };
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Manejar la actualización de precios
  const handleRefreshPrices = async () => {
    setIsRefreshing(true);
    await refreshPrices();
    setIsRefreshing(false);
  };

  // Manejar la adición de un activo
  const handleAddAsset = async (asset: any) => {
    await addAssetToPortfolio(asset);
    setShowAddAssetModal(false);
  };

  // Manejar la eliminación de un activo
  const handleRemoveAsset = async (assetId: string) => {
    if (window.confirm('¿Estás seguro de que deseas eliminar este activo de tu portafolio?')) {
      await removeAssetFromPortfolio(assetId);
    }
  };

  // Manejar la adición de fondos
  const handleAddFunds = async (amount: number, currency: string) => {
    try {
      console.log(`Intentando añadir fondos: ${amount} ${currency}`);
      // Ya no cerramos el modal aquí, lo hace el componente AddFundsModal
      return await addFunds(amount, currency);
    } catch (error) {
      console.error('Error al añadir fondos:', error);
      return false;
    }
  };

  if (isLoading) {
    return <LoadingSpinner message="Cargando tu portafolio..." />;
  }

  if (error) {
    return (
      <div className="portfolio-error">
        <h3>Error al cargar el portafolio</h3>
        <p>{error}</p>
        <div className="error-actions">
          <button onClick={() => refreshPrices()} className="retry-button">
            Actualizar datos
          </button>
          <button onClick={() => window.location.reload()} className="reload-button">
            Recargar página
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-dashboard">
      <div className="portfolio-dashboard-header">
        <div className="portfolio-dashboard-title">
          <h2>Mi Portafolio</h2>
          <div className="portfolio-funds-display">
            <span className="funds-label">Fondos disponibles:</span>
            <span className="funds-value">{userFunds.currency} {formatNumber(userFunds.balance)}</span>
            <button
              className="add-funds-button"
              onClick={handleOpenAddFundsModal}
            >
              Añadir Fondos
            </button>
          </div>
        </div>

        <div className="portfolio-dashboard-actions">
          <button
            className="refresh-button"
            onClick={handleRefreshPrices}
            disabled={isRefreshing || portfolio.length === 0}
          >
            {isRefreshing ? 'Actualizando...' : 'Actualizar Precios'}
          </button>
          <button
            className="add-asset-button"
            onClick={() => setShowAddAssetModal(true)}
          >
            Añadir Activo
          </button>
          <button
            className="reset-portfolio-button"
            onClick={() => {
              if (window.confirm('¿Estás seguro de que deseas reiniciar completamente tu portafolio? Esta acción eliminará todos tus activos, fondos y transacciones.')) {
                resetPortfolio();
              }
            }}
          >
            Reiniciar Portafolio
          </button>
        </div>
      </div>

      <div className="portfolio-dashboard-tabs">
        <button
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Resumen
        </button>
        <button
          className={`tab-button ${activeTab === 'assets' ? 'active' : ''}`}
          onClick={() => setActiveTab('assets')}
        >
          Activos
        </button>
        <button
          className={`tab-button ${activeTab === 'transactions' ? 'active' : ''}`}
          onClick={() => setActiveTab('transactions')}
        >
          Transacciones
        </button>
        <button
          className={`tab-button ${activeTab === 'analysis' ? 'active' : ''}`}
          onClick={() => setActiveTab('analysis')}
        >
          Análisis
        </button>
      </div>

      <div className="portfolio-dashboard-content">
        {activeTab === 'overview' && (
          <div className="portfolio-overview">
            <PortfolioSummary stats={portfolioStats} />

            <div className="portfolio-charts-container">
              <EnhancedDistributionChart portfolio={portfolio} availableCryptos={availableCryptos} />
              <AdvancedPortfolioChart portfolio={portfolio} />
            </div>

            {portfolio.length > 0 ? (
              <div className="portfolio-top-assets">
                <h3>Principales Activos</h3>
                <PortfolioAssetsList
                  portfolio={portfolio.slice(0, 5)}
                  availableCryptos={availableCryptos}
                  onRemoveAsset={handleRemoveAsset}
                  compact={true}
                />
                {portfolio.length > 5 && (
                  <button
                    className="view-all-button"
                    onClick={() => setActiveTab('assets')}
                  >
                    Ver todos los activos
                  </button>
                )}
              </div>
            ) : (
              <div className="empty-portfolio-message">
                <p>No tienes activos en tu portafolio.</p>
                <p>Haz clic en "Añadir Activo" para comenzar a construir tu portafolio.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'assets' && (
          <div className="portfolio-assets-tab">
            <h3>Mis Activos</h3>
            {portfolio.length > 0 ? (
              <PortfolioAssetsList
                portfolio={portfolio}
                availableCryptos={availableCryptos}
                onRemoveAsset={handleRemoveAsset}
                compact={false}
              />
            ) : (
              <div className="empty-portfolio-message">
                <p>No tienes activos en tu portafolio.</p>
                <p>Haz clic en "Añadir Activo" para comenzar a construir tu portafolio.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="portfolio-transactions-tab">
            <h3>Historial de Transacciones</h3>
            <PortfolioTransactions />
          </div>
        )}

        {activeTab === 'analysis' && (
          <div className="portfolio-analysis-tab">
            <PortfolioAnalysis
              portfolio={portfolio}
              availableCryptos={availableCryptos}
            />

            <div className="portfolio-guru-analysis">
              <h4>Análisis con Guru Cripto</h4>
              <p>Obtén un análisis detallado de tu portafolio con nuestro Guru Cripto.</p>
              <button
                className="guru-analysis-button"
                onClick={() => window.location.href = '/guru?analyze=portfolio'}
              >
                Analizar con Guru
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modales */}
      <AddAssetModal
        isOpen={showAddAssetModal}
        onClose={() => setShowAddAssetModal(false)}
        onAddAsset={handleAddAsset}
        availableCryptos={availableCryptos}
        userFunds={userFunds}
      />

      <AddFundsModal
        isOpen={showAddFundsModal}
        onClose={() => setShowAddFundsModal(false)}
        onAddFunds={handleAddFunds}
        currentBalance={userFunds.balance || 0}
        currentCurrency={userFunds.currency || 'USD'}
      />
    </div>
  );
};

export default PortfolioDashboard;
