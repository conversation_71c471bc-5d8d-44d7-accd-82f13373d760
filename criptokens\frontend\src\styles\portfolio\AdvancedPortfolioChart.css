/* Estilos para el componente de gráfico avanzado del portafolio */

.advanced-portfolio-chart {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.advanced-portfolio-chart h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-value-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.current-value {
  font-size: 1.25rem;
  font-weight: 600;
}

.percent-change {
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.current-value.positive,
.percent-change.positive {
  color: var(--success);
}

.current-value.negative,
.percent-change.negative {
  color: var(--error);
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.time-range-selector {
  display: flex;
  gap: 0.25rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 0.25rem;
}

.time-range-selector button {
  background: transparent;
  border: none;
  color: var(--text-dim);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all var(--transition-normal);
}

.time-range-selector button:hover {
  color: var(--text-bright);
  background: rgba(255, 255, 255, 0.05);
}

.time-range-selector button.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-bright);
}

.chart-options {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.moving-average-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-medium);
  font-size: 0.85rem;
  cursor: pointer;
}

.moving-average-toggle input {
  cursor: pointer;
}

.chart-container {
  flex: 1;
  position: relative;
  min-height: 200px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  padding: 1rem;
  overflow: hidden;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.horizontal-lines {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grid-line {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.05);
}

.chart-lines {
  position: absolute;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  bottom: 2rem;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.chart-line {
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chart-line.positive {
  stroke: var(--success);
}

.chart-line.negative {
  stroke: var(--error);
}

.chart-area {
  opacity: 0.1;
}

.chart-area.positive {
  fill: var(--success);
}

.chart-area.negative {
  fill: var(--error);
}

.moving-average-line {
  fill: none;
  stroke: var(--secondary);
  stroke-width: 1.5;
  stroke-dasharray: 3, 3;
}

.chart-labels {
  position: absolute;
  bottom: 0;
  left: 1rem;
  right: 1rem;
  height: 1.5rem;
  display: flex;
}

.date-label {
  position: absolute;
  font-size: 0.75rem;
  color: var(--text-dim);
  transform: translateX(-50%);
}

/* Estilos para el gráfico vacío */
.empty-chart {
  display: flex;
  flex-direction: column;
}

.empty-chart-message,
.chart-loading,
.chart-error {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  padding: 2rem;
  text-align: center;
  min-height: 200px;
  margin-top: 1rem;
}

.empty-chart-message p,
.chart-error p {
  color: var(--text-dim);
  margin: 0.5rem 0;
}

.error-details {
  font-size: 0.85rem;
  max-width: 80%;
  word-break: break-word;
}

/* Responsive */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .time-range-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .chart-options {
    width: 100%;
    justify-content: flex-end;
  }
}
