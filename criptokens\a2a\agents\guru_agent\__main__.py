import os
import logging
import click
from dotenv import load_dotenv
from ...common.server import A2AServer
from ...common.types import Agent<PERSON>ard, AgentCapabilities, AgentSkill, AgentProvider
from .task_manager import GuruAgentTaskManager
from .agent import GuruAgent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@click.command()
@click.option("--host", default="localhost", help="Host to bind the server to")
@click.option("--port", default=3200, help="Port to bind the server to")
def main(host, port):
    """Run the Guru Cripto agent server."""
    try:
        # Create agent capabilities
        capabilities = AgentCapabilities(streaming=True)
        
        # Create agent skills
        skills = [
            AgentSkill(
                id="crypto_prediction",
                name="Cryptocurrency Prediction",
                description="Predicts cryptocurrency price movements based on technical, sentiment, and on-chain analysis",
                tags=["crypto", "prediction", "analysis"],
                examples=[
                    "Predict Bitcoin price for the next week",
                    "What's your analysis for Ethereum?",
                    "Should I buy Solana now?"
                ],
                inputModes=["text"],
                outputModes=["text", "data"]
            ),
            AgentSkill(
                id="market_analysis",
                name="Market Analysis",
                description="Analyzes cryptocurrency market conditions",
                tags=["crypto", "market", "analysis"],
                examples=[
                    "How is the crypto market doing?",
                    "What's the current market sentiment?",
                    "Analyze current market conditions"
                ],
                inputModes=["text"],
                outputModes=["text", "data"]
            ),
            AgentSkill(
                id="investment_advice",
                name="Investment Advice",
                description="Provides cryptocurrency investment advice",
                tags=["crypto", "investment", "advice"],
                examples=[
                    "Should I invest in Bitcoin now?",
                    "What's a good crypto investment strategy?",
                    "How should I diversify my crypto portfolio?"
                ],
                inputModes=["text"],
                outputModes=["text", "data"]
            )
        ]
        
        # Create agent provider
        provider = AgentProvider(
            organization="Criptokens",
            url="https://criptokens.com"
        )
        
        # Create agent card
        agent_card = AgentCard(
            name="Guru Cripto",
            description="Comprehensive cryptocurrency analysis and prediction agent",
            url=f"http://{host}:{port}/",
            provider=provider,
            version="1.0.0",
            capabilities=capabilities,
            defaultInputModes=["text"],
            defaultOutputModes=["text", "data"],
            skills=skills
        )
        
        # Create agent and task manager
        agent = GuruAgent()
        task_manager = GuruAgentTaskManager(agent=agent)
        
        # Create and start server
        server = A2AServer(
            agent_card=agent_card,
            task_manager=task_manager,
            host=host,
            port=port
        )
        
        logger.info(f"Starting Guru Cripto agent server on http://{host}:{port}/")
        server.start()
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)

if __name__ == "__main__":
    main()
