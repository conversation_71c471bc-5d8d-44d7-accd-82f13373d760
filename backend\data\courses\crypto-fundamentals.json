{"id": "crypto-fundamentals", "title": "Fundamentos de Criptomonedas", "description": "Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.", "level": "beginner", "duration": "4 horas", "thumbnail": "/images/courses/crypto-fundamentals.jpg", "requirements": ["No se requieren conocimientos previos", "Interés en tecnología y finanzas", "Computadora con acceso a internet"], "objectives": ["Comprender qué son las criptomonedas y cómo funcionan", "Entender la tecnología blockchain y sus aplicaciones", "Aprender a crear y gestionar una wallet de criptomonedas", "Conocer los principales exchanges y cómo operar en ellos", "Identificar los riesgos y oportunidades en el mercado de criptomonedas"], "instructor": {"name": "<PERSON>", "bio": "Experto en criptomonedas con más de 5 años de experiencia en el sector. Fundador de CryptoEdu y asesor de múltiples proyectos blockchain.", "avatar": "https://randomuser.me/api/portraits/men/32.jpg"}, "modules": [{"id": "module-1", "title": "¿Qué son las Criptomonedas?", "description": "Introducción al concepto de criptomonedas y su lugar en el sistema financiero global.", "order": 1, "lessons": [{"id": "lesson-1-1", "title": "Definición y Características", "duration": 15, "order": 1, "content": "# Definición y Características de las Criptomonedas\n\nLas criptomonedas son activos digitales diseñados para funcionar como medio de intercambio que utiliza criptografía para asegurar las transacciones, controlar la creación de unidades adicionales y verificar la transferencia de activos.\n\n## Características principales\n\n- **Descentralización**: No están controladas por ningún banco central o gobierno\n- **Seguridad**: Utilizan criptografía avanzada para proteger las transacciones\n- **Transparencia**: Todas las transacciones son públicas y verificables\n- **Limitación**: Muchas tienen un suministro limitado (como Bitcoin con 21 millones)\n- **Pseudoanonimato**: Las transacciones no están vinculadas directamente a identidades reales\n\n## Diferencias con el dinero tradicional\n\n| Criptomonedas | Dinero Tradicional |\n|---------------|--------------------|\n| Descentralizadas | Centralizado |\n| Suministro limitado (en muchos casos) | Puede ser impreso sin límites |\n| Sin intermediarios | Requiere bancos y procesadores de pago |\n| Global por naturaleza | Limitado por fronteras nacionales |\n| Transacciones irreversibles | Las transacciones pueden ser revertidas |"}, {"id": "lesson-1-2", "title": "Historia de las Criptomonedas", "duration": 20, "order": 2, "content": "# Historia de las Criptomonedas\n\n## Orígenes y Desarrollo\n\nLa historia de las criptomonedas comienza mucho antes de Bitcoin, con los primeros conceptos de dinero digital y criptografía aplicada a las finanzas.\n\n### Precursores (1980s-1990s)\n\n- **DigiCash (1989)**: <PERSON><PERSON><PERSON> por <PERSON>, fue uno de los primeros intentos de crear dinero electrónico usando criptografía\n- **B-Money (1998)**: Propuesta por Wei Dai, introdujo la idea de crear dinero mediante la resolución de problemas computacionales\n- **Bit Gold (1998)**: <PERSON><PERSON><PERSON><PERSON> por <PERSON>, considerado el precursor directo de Bitcoin\n\n### El Nacimiento de Bitcoin (2008-2009)\n\n- **Octubre 2008**: <PERSON><PERSON> publica el whitepaper \"Bitcoin: Un Sistema de Efectivo Electrónico Peer-to-Peer\"\n- **3 de enero de 2009**: Se mina el bloque génesis de Bitcoin, marcando el inicio oficial de la blockchain\n- **Primera transacción**: <PERSON><PERSON> envía 10 BTC a Hal Finney\n\n### Evolución del Ecosistema (2010-2013)\n\n- **2010**: Primera compra con Bitcoin (dos pizzas por 10,000 BTC)\n- **2011**: Surgen las primeras altcoins como Namecoin y Litecoin\n- **2013**: El precio de Bitcoin supera los $1,000 por primera vez\n\n### La Era de la Innovación (2014-2017)\n\n- **2014**: Lanzamiento de Ethereum, introduciendo los contratos inteligentes\n- **2016**: Auge de las ICOs (Initial Coin Offerings)\n- **2017**: Bitcoin alcanza casi $20,000, captando la atención mundial\n\n### Maduración del Mercado (2018-Presente)\n\n- **2018-2019**: \"Invierno cripto\" con caída de precios y consolidación\n- **2020**: Instituciones comienzan a adoptar Bitcoin como reserva de valor\n- **2021**: Explosión de NFTs y finanzas descentralizadas (DeFi)\n- **2022-2023**: Regulación y mayor adopción institucional"}, {"id": "lesson-1-3", "title": "Tipos de Criptomonedas", "duration": 25, "order": 3, "content": "# Tipos de Criptomonedas\n\nEl ecosistema de criptomonedas ha evolucionado enormemente desde el lanzamiento de Bitcoin, dando lugar a miles de proyectos con diferentes propósitos y tecnologías.\n\n## Categorías Principales\n\n### Monedas (Currencies)\n\nDiseñadas principalmente como medio de intercambio y reserva de valor.\n\n- **Bitcoin (BTC)**: La primera y más conocida criptomoneda\n- **Litecoin (LTC)**: Diseñada para transacciones más rápidas y baratas\n- **Bitcoin Cash (BCH)**: Fork de Bitcoin con bloques más grandes\n- **<PERSON><PERSON> (XMR)**: Enfocada en privacidad y anonimato\n\n### Plataformas de Contratos Inteligentes\n\nPermiten la creación y ejecución de aplicaciones descentralizadas (dApps).\n\n- **Ethereum (ETH)**: La primera y más grande plataforma de contratos inteligentes\n- **Solana (SOL)**: Enfocada en alta velocidad y bajo costo\n- **Cardano (ADA)**: Desarrollada con un enfoque académico y revisión por pares\n- **Polkadot (DOT)**: Permite la interoperabilidad entre diferentes blockchains\n\n### Stablecoins\n\nDiseñadas para mantener un valor estable, generalmente vinculado a una moneda fiat.\n\n- **Tether (USDT)**: Respaldada por dólares estadounidenses\n- **USD Coin (USDC)**: Stablecoin regulada y auditada\n- **DAI**: Stablecoin descentralizada respaldada por criptoactivos\n\n### Tokens de Utilidad\n\nDiseñados para proporcionar acceso o funcionalidad dentro de un ecosistema específico.\n\n- **Binance Coin (BNB)**: Utilizado en el ecosistema de Binance\n- **Chainlink (LINK)**: Conecta contratos inteligentes con datos del mundo real\n\n### Tokens de Gobernanza\n\nOtorgan derechos de voto en la toma de decisiones de un protocolo.\n\n- **Uniswap (UNI)**: Gobernanza del exchange descentralizado Uniswap\n- **Compound (COMP)**: Gobernanza del protocolo de préstamos Compound\n\n### Tokens No Fungibles (NFTs)\n\nRepresentan activos únicos y no intercambiables.\n\n- Coleccionables digitales\n- Arte digital\n- Propiedad virtual en metaversos"}], "quiz": {"id": "quiz-module-1", "title": "Evaluación: Introducción a las Criptomonedas", "description": "Comprueba tu comprensión de los conceptos básicos de las criptomonedas", "passingScore": 70, "questions": [{"id": "q1-m1", "question": "¿Cuál de las siguientes NO es una característica de las criptomonedas?", "options": ["Descentralización", "<PERSON><PERSON><PERSON>", "Uso de criptografía", "Transparencia de transacciones"], "correctAnswer": 1, "explanation": "Las criptomonedas no están respaldadas por gobiernos o bancos centrales. <PERSON> <PERSON>, la descentralización y la independencia de autoridades centrales es una de sus características fundamentales."}, {"id": "q2-m1", "question": "¿Quién creó Bitcoin?", "options": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "correctAnswer": 1, "explanation": "Bitcoin fue creado por una persona o grupo bajo el seudónimo de <PERSON><PERSON>, cuya identidad real sigue siendo desconocida."}, {"id": "q3-m1", "question": "¿En qué año se publicó el whitepaper de Bitcoin?", "options": ["2006", "2008", "2010", "2013"], "correctAnswer": 1, "explanation": "El whitepaper de Bitcoin titulado 'Bitcoin: Un Sistema de Efectivo Electrónico Peer-to-Peer' fue publicado por <PERSON><PERSON> en octubre de 2008."}, {"id": "q4-m1", "question": "¿Cuál de las siguientes es una stablecoin?", "options": ["Bitcoin (BTC)", "Ethereum (ETH)", "Tether (USDT)", "Cardano (ADA)"], "correctAnswer": 2, "explanation": "Tether (USDT) es una stablecoin diseñada para mantener un valor estable vinculado al dólar estadounidense."}, {"id": "q5-m1", "question": "¿Qué característica diferencia a Monero de Bitcoin?", "options": ["Mayor velocidad de transacción", "Contratos inteligentes", "Mayor priva<PERSON>ad y anonimato", "Mayor capitalización de mercado"], "correctAnswer": 2, "explanation": "Monero (XMR) se diferencia principalmente por su enfoque en la privacidad y el anonimato, utilizando tecnologías como firmas de anillo y direcciones ocultas."}]}}], "resources": [{"id": "resource-1", "title": "Whitepaper de Bitcoin", "type": "document", "url": "https://bitcoin.org/bitcoin.pdf", "description": "El documento original escrito por <PERSON><PERSON> que describe el funcionamiento de Bitcoin."}, {"id": "resource-2", "title": "Glosario de Términos Cripto", "type": "article", "url": "https://academy.binance.com/es/glossary", "description": "Glosario completo de términos relacionados con criptomonedas y blockchain."}, {"id": "resource-3", "title": "Visualización de Blockchain", "type": "tool", "url": "https://www.blockchain.com/explorer", "description": "Herramienta para visualizar transacciones en tiempo real en la blockchain de Bitcoin."}], "reviews": [{"id": "review-1", "user": "<PERSON>", "rating": 5, "comment": "Excelente curso para principiantes. Explicaciones claras y ejemplos prácticos.", "date": "2023-05-15"}, {"id": "review-2", "user": "<PERSON>", "rating": 4, "comment": "<PERSON><PERSON> buen contenido, aunque me hubiera gustado más ejercicios prácticos.", "date": "2023-06-22"}], "createdAt": "2023-01-10", "updatedAt": "2023-07-15"}