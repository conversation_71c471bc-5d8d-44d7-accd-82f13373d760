# Script para iniciar OnChain MCP Server

Write-Host "Iniciando OnChain MCP Server..." -ForegroundColor Cyan
Write-Host ""

# Cambiar al directorio del servidor
Set-Location -Path "onchain-mcp-server"

# Instalar dependencias si es necesario
if (-not (Test-Path -Path "node_modules")) {
    Write-Host "Instalando dependencias..." -ForegroundColor Yellow
    npm install
}

# Iniciar el servidor
Write-Host "Iniciando servidor..." -ForegroundColor Green
npm start

# Volver al directorio original
Set-Location -Path ".."
