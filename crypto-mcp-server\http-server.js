import express from "express";
import cors from "cors";
import fetch from "node-fetch";

// URL base de la API de CoinMarketCap (requiere API key)
const COINMARKETCAP_API_URL = "https://pro-api.coinmarketcap.com/v1";

// API key para CoinMarketCap
const COINMARKETCAP_API_KEY = "37f9968e-6ab7-431f-80d7-0ac6686319f3";

// Sistema de caché simple
const cache = {
  data: {},
  timestamps: {},
  // Tiempo de caché en milisegundos (5 minutos)
  cacheDuration: 5 * 60 * 1000
};

// Control de tasa de solicitudes
const rateLimiter = {
  lastRequestTime: 0,
  // Tiempo mínimo entre solicitudes en milisegundos (2 segundos)
  minRequestInterval: 2000
};

// Función para obtener datos de la API de CoinMarketCap con caché y control de tasa
async function fetchFromCoinMarketCap(endpoint, params = {}) {
  try {
    // Construir la URL con los parámetros
    const url = new URL(`${COINMARKETCAP_API_URL}${endpoint}`);
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });

    const cacheKey = url.toString();

    // Verificar si tenemos datos en caché y si son válidos
    const now = Date.now();
    if (
      cache.data[cacheKey] &&
      cache.timestamps[cacheKey] &&
      (now - cache.timestamps[cacheKey]) < cache.cacheDuration
    ) {
      console.log(`Usando datos en caché para: ${cacheKey}`);
      return cache.data[cacheKey];
    }

    // Controlar la tasa de solicitudes
    const timeSinceLastRequest = now - rateLimiter.lastRequestTime;
    if (timeSinceLastRequest < rateLimiter.minRequestInterval) {
      const waitTime = rateLimiter.minRequestInterval - timeSinceLastRequest;
      console.log(`Esperando ${waitTime}ms antes de la siguiente solicitud...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    console.log(`Fetching data from CoinMarketCap: ${url.toString()}`);

    // Actualizar el tiempo de la última solicitud
    rateLimiter.lastRequestTime = Date.now();

    // Realizar la petición con la API key en los headers
    const response = await fetch(url.toString(), {
      timeout: 5000, // Timeout de 5 segundos
      headers: {
        'X-CMC_PRO_API_KEY': COINMARKETCAP_API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'Criptokens/1.0'
      }
    });

    // Verificar si la respuesta es exitosa
    if (!response.ok) {
      throw new Error(`Error en la petición a CoinMarketCap: ${response.status} ${response.statusText}`);
    }

    // Parsear la respuesta como JSON
    const data = await response.json();

    // Verificar si hay un error en la respuesta de la API
    if (data.status && data.status.error_code) {
      console.error(`Error de la API de CoinMarketCap: ${data.status.error_code} - ${data.status.error_message}`);
      throw new Error(`Error de la API de CoinMarketCap: ${data.status.error_message}`);
    }

    // Imprimir la respuesta para depuración
    console.log('Respuesta de CoinMarketCap:', JSON.stringify(data).substring(0, 300) + '...');

    // Guardar en caché
    cache.data[cacheKey] = data;
    cache.timestamps[cacheKey] = Date.now();

    return data;
  } catch (error) {
    console.error(`Error al obtener datos de CoinMarketCap:`, error);

    // Si hay un error, intentar usar datos en caché aunque estén caducados
    const cacheKey = new URL(`${COINMARKETCAP_API_URL}${endpoint}`).toString();
    if (cache.data[cacheKey]) {
      console.log(`Usando datos en caché caducados como fallback para: ${cacheKey}`);
      return cache.data[cacheKey];
    }

    // Si no hay datos en caché, generar datos simulados
    console.log(`Generando datos simulados para: ${endpoint}`);
    if (endpoint === '/cryptocurrency/listings/latest') {
      return generateMockListingsData();
    } else if (endpoint.includes('/cryptocurrency/quotes/latest')) {
      return generateMockQuoteData(params.id || params.slug || params.symbol);
    } else if (endpoint === '/cryptocurrency/info') {
      return generateMockInfoData(params.id || params.slug || params.symbol);
    } else if (endpoint === '/cryptocurrency/map') {
      return generateMockMapData();
    }

    // Si no se puede generar datos simulados, lanzar el error
    throw error;
  }
}

// Mantener la función original para compatibilidad
async function fetchFromCoinCap(endpoint, params = {}) {
  console.warn('fetchFromCoinCap está obsoleto. Usando fetchFromCoinGecko en su lugar.');
  // Mapear endpoints de CoinCap a CoinGecko
  let geckoEndpoint = '';
  let geckoParams = {};

  if (endpoint === '/assets') {
    geckoEndpoint = '/coins/markets';
    geckoParams = { vs_currency: 'usd', order: 'market_cap_desc', per_page: 100, page: 1, sparkline: true };
  } else if (endpoint.includes('/assets/') && endpoint.includes('/history')) {
    const cryptoId = endpoint.split('/')[2];
    geckoEndpoint = `/coins/${cryptoId}/market_chart`;
    geckoParams = { vs_currency: 'usd', days: 30 };
  } else if (endpoint.includes('/assets/')) {
    const cryptoId = endpoint.split('/')[2];
    geckoEndpoint = `/coins/${cryptoId}`;
    geckoParams = { localization: false, tickers: false, market_data: true, community_data: false, developer_data: false };
  }

  return fetchFromCoinGecko(geckoEndpoint, { ...geckoParams, ...params });
}

// Función para generar datos simulados de listados de criptomonedas (CoinMarketCap)
function generateMockListingsData() {
  const mockCryptos = [
    { id: 1, name: 'Bitcoin', symbol: 'BTC', slug: 'bitcoin', cmc_rank: 1, quote: { USD: { price: 85000, percent_change_24h: 2.5, market_cap: 1600000000000, volume_24h: 50000000000 } } },
    { id: 1027, name: 'Ethereum', symbol: 'ETH', slug: 'ethereum', cmc_rank: 2, quote: { USD: { price: 3500, percent_change_24h: 1.8, market_cap: 420000000000, volume_24h: 20000000000 } } },
    { id: 825, name: 'Tether', symbol: 'USDT', slug: 'tether', cmc_rank: 3, quote: { USD: { price: 1.0, percent_change_24h: 0.01, market_cap: 100000000000, volume_24h: 80000000000 } } },
    { id: 1839, name: 'BNB', symbol: 'BNB', slug: 'binancecoin', cmc_rank: 4, quote: { USD: { price: 600, percent_change_24h: -0.8, market_cap: 90000000000, volume_24h: 2000000000 } } },
    { id: 5426, name: 'Solana', symbol: 'SOL', slug: 'solana', cmc_rank: 5, quote: { USD: { price: 150, percent_change_24h: 3.2, market_cap: 65000000000, volume_24h: 3000000000 } } },
    { id: 52, name: 'XRP', symbol: 'XRP', slug: 'ripple', cmc_rank: 6, quote: { USD: { price: 0.6, percent_change_24h: -1.3, market_cap: 32000000000, volume_24h: 1500000000 } } },
    { id: 2010, name: 'Cardano', symbol: 'ADA', slug: 'cardano', cmc_rank: 7, quote: { USD: { price: 0.45, percent_change_24h: 0.7, market_cap: 16000000000, volume_24h: 500000000 } } },
    { id: 74, name: 'Dogecoin', symbol: 'DOGE', slug: 'dogecoin', cmc_rank: 8, quote: { USD: { price: 0.15, percent_change_24h: 4.2, market_cap: 21000000000, volume_24h: 1200000000 } } },
    { id: 6636, name: 'Polkadot', symbol: 'DOT', slug: 'polkadot', cmc_rank: 9, quote: { USD: { price: 7.8, percent_change_24h: -0.5, market_cap: 10000000000, volume_24h: 400000000 } } },
    { id: 2, name: 'Litecoin', symbol: 'LTC', slug: 'litecoin', cmc_rank: 10, quote: { USD: { price: 80, percent_change_24h: 1.1, market_cap: 6000000000, volume_24h: 350000000 } } }
  ];

  return {
    status: { timestamp: new Date().toISOString(), error_code: 0, error_message: null },
    data: mockCryptos
  };
}

// Función para generar datos simulados de cotizaciones (CoinMarketCap)
function generateMockQuoteData(identifier) {
  const mockQuotes = {
    'bitcoin': { id: 1, name: 'Bitcoin', symbol: 'BTC', slug: 'bitcoin', cmc_rank: 1, quote: { USD: { price: 85000, percent_change_24h: 2.5, market_cap: 1600000000000, volume_24h: 50000000000 } } },
    'ethereum': { id: 1027, name: 'Ethereum', symbol: 'ETH', slug: 'ethereum', cmc_rank: 2, quote: { USD: { price: 3500, percent_change_24h: 1.8, market_cap: 420000000000, volume_24h: 20000000000 } } },
    'tether': { id: 825, name: 'Tether', symbol: 'USDT', slug: 'tether', cmc_rank: 3, quote: { USD: { price: 1.0, percent_change_24h: 0.01, market_cap: 100000000000, volume_24h: 80000000000 } } },
    'binancecoin': { id: 1839, name: 'BNB', symbol: 'BNB', slug: 'binancecoin', cmc_rank: 4, quote: { USD: { price: 600, percent_change_24h: -0.8, market_cap: 90000000000, volume_24h: 2000000000 } } },
    'solana': { id: 5426, name: 'Solana', symbol: 'SOL', slug: 'solana', cmc_rank: 5, quote: { USD: { price: 150, percent_change_24h: 3.2, market_cap: 65000000000, volume_24h: 3000000000 } } }
  };

  // Buscar por slug, symbol o id
  let crypto = null;
  if (typeof identifier === 'string') {
    crypto = mockQuotes[identifier.toLowerCase()];
    if (!crypto) {
      // Buscar por símbolo
      const symbol = identifier.toUpperCase();
      crypto = Object.values(mockQuotes).find(c => c.symbol === symbol);
    }
  } else if (typeof identifier === 'number') {
    // Buscar por ID
    crypto = Object.values(mockQuotes).find(c => c.id === identifier);
  }

  // Si no encontramos la criptomoneda, generamos datos aleatorios
  if (!crypto) {
    const slug = typeof identifier === 'string' ? identifier.toLowerCase() : `crypto${Math.floor(Math.random() * 1000)}`;
    const symbol = typeof identifier === 'string' ? identifier.substring(0, 3).toUpperCase() : `C${Math.floor(Math.random() * 100)}`;
    const name = slug.charAt(0).toUpperCase() + slug.slice(1);

    crypto = {
      id: Math.floor(Math.random() * 10000),
      name: name,
      symbol: symbol,
      slug: slug,
      cmc_rank: Math.floor(Math.random() * 100 + 11),
      quote: {
        USD: {
          price: Math.random() * 100,
          percent_change_24h: Math.random() * 10 - 5,
          market_cap: Math.random() * 1000000000,
          volume_24h: Math.random() * 100000000
        }
      }
    };
  }

  // Formato de respuesta de CoinMarketCap
  const result = {};
  result[crypto.id] = crypto;

  return {
    status: { timestamp: new Date().toISOString(), error_code: 0, error_message: null },
    data: result
  };
}

// Función para generar datos simulados de información de criptomonedas (CoinMarketCap)
function generateMockInfoData(identifier) {
  const mockInfo = {
    'bitcoin': { id: 1, name: 'Bitcoin', symbol: 'BTC', slug: 'bitcoin', category: 'coin', description: 'Bitcoin (BTC) is a cryptocurrency launched in 2009. Bitcoin is a decentralized digital currency, without a central bank or single administrator, that can be sent from user to user on the peer-to-peer bitcoin network without the need for intermediaries.', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1.png', urls: { website: ['https://bitcoin.org/'], technical_doc: ['https://bitcoin.org/bitcoin.pdf'], source_code: ['https://github.com/bitcoin/bitcoin'] } },
    'ethereum': { id: 1027, name: 'Ethereum', symbol: 'ETH', slug: 'ethereum', category: 'coin', description: 'Ethereum (ETH) is a smart contract platform that enables developers to build decentralized applications (dapps) on its blockchain.', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png', urls: { website: ['https://ethereum.org/'], technical_doc: ['https://ethereum.org/whitepaper/'], source_code: ['https://github.com/ethereum/go-ethereum'] } },
    'tether': { id: 825, name: 'Tether', symbol: 'USDT', slug: 'tether', category: 'token', description: 'Tether (USDT) is a stablecoin pegged to the US Dollar. A stablecoin is a type of cryptocurrency whose value is tied to an outside asset, such as the US dollar or gold, to stabilize the price.', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/825.png', urls: { website: ['https://tether.to/'], technical_doc: ['https://tether.to/wp-content/uploads/2016/06/TetherWhitePaper.pdf'] } },
    'binancecoin': { id: 1839, name: 'BNB', symbol: 'BNB', slug: 'binancecoin', category: 'coin', description: 'BNB is the native token of the Binance Chain and Binance Smart Chain. It is used to pay for transaction fees on the Binance exchange and can be used to receive discounts when paying with BNB.', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1839.png', urls: { website: ['https://www.binance.com/'] } },
    'solana': { id: 5426, name: 'Solana', symbol: 'SOL', slug: 'solana', category: 'coin', description: 'Solana (SOL) is a high-performance blockchain supporting builders around the world creating crypto apps that scale today.', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png', urls: { website: ['https://solana.com/'], technical_doc: ['https://solana.com/solana-whitepaper.pdf'], source_code: ['https://github.com/solana-labs/solana'] } }
  };

  // Buscar por slug, symbol o id
  let crypto = null;
  if (typeof identifier === 'string') {
    crypto = mockInfo[identifier.toLowerCase()];
    if (!crypto) {
      // Buscar por símbolo
      const symbol = identifier.toUpperCase();
      crypto = Object.values(mockInfo).find(c => c.symbol === symbol);
    }
  } else if (typeof identifier === 'number') {
    // Buscar por ID
    crypto = Object.values(mockInfo).find(c => c.id === identifier);
  }

  // Si no encontramos la criptomoneda, generamos datos aleatorios
  if (!crypto) {
    const slug = typeof identifier === 'string' ? identifier.toLowerCase() : `crypto${Math.floor(Math.random() * 1000)}`;
    const symbol = typeof identifier === 'string' ? identifier.substring(0, 3).toUpperCase() : `C${Math.floor(Math.random() * 100)}`;
    const name = slug.charAt(0).toUpperCase() + slug.slice(1);

    crypto = {
      id: Math.floor(Math.random() * 10000),
      name: name,
      symbol: symbol,
      slug: slug,
      category: 'coin',
      description: `${name} is a cryptocurrency with the symbol ${symbol}.`,
      logo: `https://example.com/${slug}.png`,
      urls: { website: [`https://${slug}.org/`] }
    };
  }

  // Formato de respuesta de CoinMarketCap
  const result = {};
  result[crypto.id] = crypto;

  return {
    status: { timestamp: new Date().toISOString(), error_code: 0, error_message: null },
    data: result
  };
}

// Función para generar datos simulados del mapa de criptomonedas (CoinMarketCap)
function generateMockMapData() {
  const mockCryptos = [
    { id: 1, name: 'Bitcoin', symbol: 'BTC', slug: 'bitcoin', rank: 1, is_active: 1, first_historical_data: '2013-04-28T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 1027, name: 'Ethereum', symbol: 'ETH', slug: 'ethereum', rank: 2, is_active: 1, first_historical_data: '2015-08-07T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 825, name: 'Tether', symbol: 'USDT', slug: 'tether', rank: 3, is_active: 1, first_historical_data: '2015-02-25T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 1839, name: 'BNB', symbol: 'BNB', slug: 'binancecoin', rank: 4, is_active: 1, first_historical_data: '2017-07-25T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 5426, name: 'Solana', symbol: 'SOL', slug: 'solana', rank: 5, is_active: 1, first_historical_data: '2020-04-10T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 52, name: 'XRP', symbol: 'XRP', slug: 'ripple', rank: 6, is_active: 1, first_historical_data: '2013-08-04T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 2010, name: 'Cardano', symbol: 'ADA', slug: 'cardano', rank: 7, is_active: 1, first_historical_data: '2017-10-01T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 74, name: 'Dogecoin', symbol: 'DOGE', slug: 'dogecoin', rank: 8, is_active: 1, first_historical_data: '2013-12-15T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 6636, name: 'Polkadot', symbol: 'DOT', slug: 'polkadot', rank: 9, is_active: 1, first_historical_data: '2020-08-19T00:00:00.000Z', last_historical_data: new Date().toISOString() },
    { id: 2, name: 'Litecoin', symbol: 'LTC', slug: 'litecoin', rank: 10, is_active: 1, first_historical_data: '2013-04-28T00:00:00.000Z', last_historical_data: new Date().toISOString() }
  ];

  return {
    status: { timestamp: new Date().toISOString(), error_code: 0, error_message: null },
    data: mockCryptos
  };
}

// Función para generar datos históricos simulados (para compatibilidad)
function generateMockHistoricalData(cryptoId) {
  const now = Date.now();
  const data = [];

  // Obtener un precio base según la criptomoneda
  let basePrice = 100;
  if (cryptoId === 'bitcoin') basePrice = 85000;
  else if (cryptoId === 'ethereum') basePrice = 3500;
  else if (cryptoId === 'tether') basePrice = 1.0;
  else if (cryptoId === 'binancecoin') basePrice = 600;
  else if (cryptoId === 'solana') basePrice = 150;

  // Generar datos para los últimos 30 días
  for (let i = 30; i >= 0; i--) {
    const timestamp = now - (i * 24 * 60 * 60 * 1000);
    // Generar variación aleatoria del precio (-5% a +5%)
    const variation = (Math.random() * 0.1) - 0.05;
    const price = basePrice * (1 + variation);

    data.push({
      priceUsd: price.toString(),
      time: new Date(timestamp).toISOString(),
      date: new Date(timestamp).toISOString().split('T')[0]
    });
  }

  return { data };
}

// Función para obtener el precio de una criptomoneda desde CoinMarketCap
async function getCryptoPrice(cryptoId) {
  try {
    // Usar la API de CoinMarketCap para obtener datos detallados
    const data = await fetchFromCoinMarketCap('/cryptocurrency/quotes/latest', { slug: cryptoId, convert: 'USD' });

    // CoinMarketCap devuelve los datos en un formato diferente
    const cryptoData = data.data;
    const cryptoKey = Object.keys(cryptoData)[0];
    const crypto = cryptoData[cryptoKey];

    // Extraer los datos relevantes
    const price = crypto.quote.USD.price;
    const percentChange24h = crypto.quote.USD.percent_change_24h;
    const marketCap = crypto.quote.USD.market_cap;
    const volume24h = crypto.quote.USD.volume_24h;

    // Obtener información adicional como el logo
    let logoUrl = `https://s2.coinmarketcap.com/static/img/coins/64x64/${crypto.id}.png`;

    try {
      const infoData = await fetchFromCoinMarketCap('/cryptocurrency/info', { id: crypto.id });
      if (infoData.data && infoData.data[crypto.id] && infoData.data[crypto.id].logo) {
        logoUrl = infoData.data[crypto.id].logo;
      }
    } catch (infoError) {
      console.warn(`No se pudo obtener información adicional para ${cryptoId}:`, infoError);
    }

    return {
      id: crypto.slug,
      name: crypto.name,
      symbol: crypto.symbol,
      price: price,
      priceUsd: price,
      changePercent24Hr: percentChange24h,
      volumeUsd24Hr: volume24h,
      price_change_24h: percentChange24h,
      market_cap: marketCap,
      total_volume: volume24h,
      high_24h: price * (1 + Math.abs(percentChange24h) / 100), // Estimación
      low_24h: price * (1 - Math.abs(percentChange24h) / 100), // Estimación
      image: logoUrl,
      last_updated: new Date().toISOString(),
      // Datos adicionales para compatibilidad con el frontend
      market_data: {
        current_price: { usd: price },
        price_change_percentage_24h: percentChange24h,
        market_cap: { usd: marketCap },
        total_volume: { usd: volume24h },
        high_24h: { usd: price * (1 + Math.abs(percentChange24h) / 100) },
        low_24h: { usd: price * (1 - Math.abs(percentChange24h) / 100) }
      }
    };
  } catch (error) {
    console.error(`Error al obtener el precio de ${cryptoId}:`, error);
    throw error;
  }
}

// Función para obtener las principales criptomonedas
async function getTopCryptocurrencies(limit = 10, page = 1) {
  try {
    // Usar la API de CoinMarketCap para obtener las principales criptomonedas
    const data = await fetchFromCoinMarketCap('/cryptocurrency/listings/latest', {
      start: (page - 1) * limit + 1,
      limit: limit,
      convert: 'USD',
      sort: 'market_cap',
      sort_dir: 'desc'
    });

    // Transformar los datos para que sean compatibles con el frontend
    const formattedData = data.data.map(crypto => {
      const price = crypto.quote.USD.price;
      const percentChange24h = crypto.quote.USD.percent_change_24h;
      const marketCap = crypto.quote.USD.market_cap;
      const volume24h = crypto.quote.USD.volume_24h;

      // Crear datos de sparkline más realistas basados en el cambio porcentual
      const sparklinePrices = [];
      const baseChange = percentChange24h / 100; // Convertir a decimal

      // Generar 7 puntos de datos con una tendencia basada en el cambio porcentual actual
      for (let i = 0; i < 7; i++) {
        // Calcular un factor de variación que sigue la tendencia general
        const dayFactor = i / 6; // 0 para el primer día, 1 para el último
        const randomVariation = (Math.random() * 0.04) - 0.02; // Variación aleatoria de -2% a +2%
        const trendVariation = baseChange * dayFactor; // Variación basada en la tendencia

        // El precio más reciente es el actual, los anteriores siguen la tendencia inversa
        const variationFactor = (1 - dayFactor) * baseChange + randomVariation;
        sparklinePrices.push(price / (1 + variationFactor));
      }

      // Invertir el array para que el último precio sea el más reciente
      sparklinePrices.reverse();

      return {
        id: crypto.slug,
        name: crypto.name,
        symbol: crypto.symbol.toLowerCase(),
        current_price: price,
        price_change_percentage_24h: percentChange24h,
        market_cap: marketCap,
        total_volume: volume24h,
        circulating_supply: crypto.circulating_supply || 0,
        image: `https://s2.coinmarketcap.com/static/img/coins/64x64/${crypto.id}.png`,
        last_updated: crypto.last_updated || new Date().toISOString(),
        // Datos adicionales estimados
        ath: price * 1.5, // Estimación del ATH
        sparkline_in_7d: { price: sparklinePrices }
      };
    });

    return formattedData;
  } catch (error) {
    console.error('Error al obtener las principales criptomonedas:', error);
    throw error;
  }
}

// Función para obtener datos históricos de una criptomoneda
async function getCryptoHistoricalData(cryptoId, days = 7) {
  try {
    console.log(`Generando datos históricos simulados para ${cryptoId} (${days} días)...`);

    // CoinMarketCap Pro API no tiene un endpoint gratuito para datos históricos
    // Vamos a generar datos simulados basados en el precio actual y la tendencia del mercado

    // Primero, obtenemos el precio actual y otros datos
    const cryptoData = await getCryptoPrice(cryptoId);
    const basePrice = cryptoData.price;
    const baseMarketCap = cryptoData.market_cap;
    const baseVolume = cryptoData.total_volume;
    const currentChange = cryptoData.changePercent24Hr / 100; // Convertir a decimal

    // Obtener también datos del mercado general para simular la correlación
    const marketData = await getTopCryptocurrencies(3, 1);
    const marketTrend = marketData.reduce((sum, crypto) => sum + crypto.price_change_percentage_24h, 0) / marketData.length / 100;

    console.log(`Tendencia actual de ${cryptoId}: ${(currentChange * 100).toFixed(2)}%, Tendencia del mercado: ${(marketTrend * 100).toFixed(2)}%`);

    // Calculamos el intervalo de tiempo (usamos la fecha actual real)
    const now = Date.now();

    // Formatear los datos para que sean compatibles con el formato esperado
    const prices = [];
    const market_caps = [];
    const total_volumes = [];

    // Crear una serie de precios con tendencia y volatilidad realistas
    let currentPrice = basePrice;
    let currentMarketCap = baseMarketCap;
    let currentVolume = baseVolume;

    // Generar datos históricos simulados (del más reciente al más antiguo)
    for (let i = 0; i <= days; i++) {
      // El día 0 es hoy, el día 'days' es el más antiguo
      const timestamp = now - (i * 24 * 60 * 60 * 1000);

      if (i === 0) {
        // Para el día actual, usamos el precio actual
        prices.push([timestamp, currentPrice]);
        market_caps.push([timestamp, currentMarketCap]);
        total_volumes.push([timestamp, currentVolume]);
      } else {
        // Para días anteriores, calculamos precios basados en la tendencia inversa

        // Factores que influyen en el cambio de precio:
        // 1. Tendencia general (inversa a la actual para ser realista)
        // 2. Correlación con el mercado
        // 3. Volatilidad aleatoria (mayor para criptomonedas con mayor cambio porcentual)

        // Calcular la volatilidad basada en el cambio porcentual actual
        const volatility = Math.abs(currentChange) * 0.5 + 0.01; // Mínimo 1% de volatilidad

        // Calcular el cambio diario (combinación de tendencia y aleatoriedad)
        const trendComponent = -currentChange * 0.3; // Tendencia inversa suavizada
        const marketComponent = marketTrend * 0.2; // Correlación con el mercado
        const randomComponent = ((Math.random() * 2) - 1) * volatility; // Componente aleatorio

        const dailyChange = trendComponent + marketComponent + randomComponent;

        // Actualizar precios para el día anterior
        currentPrice = currentPrice / (1 + dailyChange);
        currentMarketCap = currentMarketCap / (1 + dailyChange);
        currentVolume = currentVolume * (0.9 + Math.random() * 0.2); // Volumen más aleatorio

        prices.push([timestamp, currentPrice]);
        market_caps.push([timestamp, currentMarketCap]);
        total_volumes.push([timestamp, currentVolume]);
      }
    }

    // Ordenar por timestamp (de más antiguo a más reciente)
    prices.sort((a, b) => a[0] - b[0]);
    market_caps.sort((a, b) => a[0] - b[0]);
    total_volumes.sort((a, b) => a[0] - b[0]);

    return {
      prices,
      market_caps,
      total_volumes
    };
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${cryptoId}:`, error);
    throw error;
  }
}

// Configurar el puerto
const PORT = process.env.PORT || 3101;

// Crear la aplicación Express
const app = express();

// Configurar CORS para permitir solicitudes desde cualquier origen
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
}));

// Configurar cabeceras CORS adicionales para todas las rutas
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Manejar las solicitudes OPTIONS (preflight)
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

// Configurar middleware para parsear JSON
app.use(express.json());

// Middleware para manejar errores
app.use((err, req, res, next) => {
  console.error('Error en el servidor:', err);
  res.status(500).json({
    content: [
      {
        type: 'text',
        text: `Error en el servidor: ${err.message}`
      }
    ]
  });
});

// Ruta para verificar si el servidor está en línea
app.get('/', (req, res) => {
  res.json({
    status: 'success',
    message: 'Servidor MCP de criptomonedas en línea'
  });
});

// Ruta para obtener las herramientas disponibles
app.get('/tools', (req, res) => {
  const tools = [
    {
      name: 'getCryptoPrice',
      description: 'Obtener el precio y detalles de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinMarketCap (ej: bitcoin, ethereum)'
      }
    },
    {
      name: 'getTopCryptocurrencies',
      description: 'Obtener las principales criptomonedas por capitalización de mercado',
      parameters: {
        limit: 'Número de criptomonedas a obtener (1-100, por defecto 10)',
        page: 'Página de resultados (por defecto 1)'
      }
    },
    {
      name: 'getCryptoHistoricalData',
      description: 'Obtener datos históricos de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinMarketCap (ej: bitcoin, ethereum)',
        days: 'Número de días de datos históricos (1-365, por defecto 7)'
      }
    },
    {
      name: 'searchCryptocurrencies',
      description: 'Buscar criptomonedas por nombre o símbolo',
      parameters: {
        query: 'Término de búsqueda (nombre o símbolo)'
      }
    }
  ];

  res.json({
    status: 'success',
    tools
  });
});

// Ruta para obtener el precio de una criptomoneda
app.post('/tools/getCryptoPrice', async (req, res) => {
  try {
    const { cryptoId } = req.body;

    if (!cryptoId) {
      return res.status(400).json({
        status: 'error',
        message: 'El parámetro cryptoId es obligatorio'
      });
    }

    console.log(`Obteniendo precio de ${cryptoId}...`);

    const data = await getCryptoPrice(cryptoId.toLowerCase());

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener el precio:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al obtener el precio: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para obtener las principales criptomonedas
app.post('/tools/getTopCryptocurrencies', async (req, res) => {
  try {
    const { limit = 10, page = 1 } = req.body;

    console.log(`Obteniendo top ${limit} criptomonedas (página ${page})...`);

    const data = await getTopCryptocurrencies(limit, page);

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener las principales criptomonedas:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al obtener las principales criptomonedas: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para obtener datos históricos de una criptomoneda
app.post('/tools/getCryptoHistoricalData', async (req, res) => {
  try {
    const { cryptoId, days = 7 } = req.body;

    if (!cryptoId) {
      return res.status(400).json({
        status: 'error',
        message: 'El parámetro cryptoId es obligatorio'
      });
    }

    console.log(`Obteniendo datos históricos de ${cryptoId} para ${days} días...`);

    const data = await getCryptoHistoricalData(cryptoId.toLowerCase(), days);

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener datos históricos:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al obtener datos históricos: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para buscar criptomonedas
app.post('/tools/searchCryptocurrencies', async (req, res) => {
  try {
    const { query } = req.body;

    if (!query) {
      return res.status(400).json({
        status: 'error',
        message: 'El parámetro query es obligatorio'
      });
    }

    console.log(`Buscando criptomonedas con el término "${query}"...`);

    try {
      // Usar la API de CoinMarketCap para buscar criptomonedas
      // CoinMarketCap tiene un endpoint de mapa que podemos usar para buscar
      const data = await fetchFromCoinMarketCap('/cryptocurrency/map');

      // Filtrar por nombre o símbolo
      const searchTerm = query.toLowerCase();
      const filteredCryptos = data.data.filter(crypto =>
        crypto.slug.toLowerCase().includes(searchTerm) ||
        crypto.name.toLowerCase().includes(searchTerm) ||
        crypto.symbol.toLowerCase().includes(searchTerm)
      );

      // Formatear los resultados para que sean compatibles con el formato esperado
      const formattedResults = {
        coins: filteredCryptos.map(crypto => ({
          id: crypto.slug,
          name: crypto.name,
          symbol: crypto.symbol.toLowerCase(),
          market_cap_rank: crypto.rank,
          thumb: `https://s2.coinmarketcap.com/static/img/coins/64x64/${crypto.id}.png`,
          large: `https://s2.coinmarketcap.com/static/img/coins/128x128/${crypto.id}.png`
        })),
        exchanges: [],
        icos: [],
        categories: [],
        nfts: []
      };

      res.json({
        content: [
          {
            type: 'text',
            text: JSON.stringify(formattedResults, null, 2)
          }
        ]
      });
    } catch (error) {
      console.error(`Error al buscar criptomonedas:`, error);

      // Generar resultados de búsqueda simulados
      const mockCryptos = generateMockMapData().data;
      const filteredCryptos = mockCryptos.filter(crypto =>
        crypto.slug.toLowerCase().includes(query.toLowerCase()) ||
        crypto.name.toLowerCase().includes(query.toLowerCase()) ||
        crypto.symbol.toLowerCase().includes(query.toLowerCase())
      );

      const formattedResults = {
        coins: filteredCryptos.map(crypto => ({
          id: crypto.slug,
          name: crypto.name,
          symbol: crypto.symbol.toLowerCase(),
          market_cap_rank: crypto.rank,
          thumb: `https://s2.coinmarketcap.com/static/img/coins/64x64/${crypto.id}.png`,
          large: `https://s2.coinmarketcap.com/static/img/coins/128x128/${crypto.id}.png`
        })),
        exchanges: [],
        icos: [],
        categories: [],
        nfts: []
      };

      res.json({
        content: [
          {
            type: 'text',
            text: JSON.stringify(formattedResults, null, 2)
          }
        ]
      });
    }
  } catch (error) {
    console.error(`Error al buscar criptomonedas:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al buscar criptomonedas: ${error.message}`
        }
      ]
    });
  }
});

// Iniciar el servidor Express
app.listen(PORT, () => {
  console.log(`Servidor de criptomonedas iniciado en http://localhost:${PORT}`);
});

// Exportar las funciones para que puedan ser utilizadas por el servidor MCP
export {
  getCryptoPrice,
  getTopCryptocurrencies,
  getCryptoHistoricalData
};
