import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/NewAuthContext';
import PortfolioDashboard from '../components/portfolio/PortfolioDashboard';
import '../styles/PortfolioPage.css';

const PortfolioPage: React.FC = () => {
  const { currentUser } = useAuth();

  return (
    <div className="portfolio-page">
      <header className="portfolio-page-header">
        <div className="portfolio-header-content">
          <h1>Mi Portafolio</h1>
          <p className="portfolio-subtitle">
            Gestiona tus inversiones en criptomonedas y analiza tu rendimiento
          </p>
        </div>
        <div className="portfolio-header-actions">
          <Link to="/guru" className="portfolio-guru-link">
            <span className="guru-icon">🧠</span>
            Analizar con Gurú Cripto
          </Link>
          <Link to="/" className="back-to-dashboard">
            Volver al Dashboard
          </Link>
        </div>
      </header>

      <div className="portfolio-page-content">
        {currentUser ? (
          <PortfolioDashboard />
        ) : (
          <div className="portfolio-login-prompt">
            <p>Debes iniciar sesión para ver tu portafolio.</p>
            <Link to="/login" className="login-button">
              Iniciar Sesión
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default PortfolioPage;
