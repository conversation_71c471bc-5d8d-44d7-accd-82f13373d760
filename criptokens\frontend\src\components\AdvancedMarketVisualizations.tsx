import React, { useState, useEffect, useRef } from 'react';
import anime from '../utils/animeUtils';

interface AdvancedMarketVisualizationsProps {
  cryptoData: any[];
  marketData: any;
  timeframe?: '1d' | '7d' | '30d' | '90d' | '365d';
}

const AdvancedMarketVisualizations: React.FC<AdvancedMarketVisualizationsProps> = ({
  cryptoData,
  marketData,
  timeframe = '7d'
}) => {
  const [activeVisualization, setActiveVisualization] = useState<string>('heatmap');
  const containerRef = useRef<HTMLDivElement>(null);
  const heatmapRef = useRef<HTMLDivElement>(null);
  const dominanceRef = useRef<HTMLDivElement>(null);
  const correlationRef = useRef<HTMLDivElement>(null);

  // Efecto para animar las visualizaciones cuando cambian
  useEffect(() => {
    if (containerRef.current) {
      anime({
        targets: containerRef.current,
        opacity: [0.7, 1],
        translateY: [10, 0],
        duration: 500,
        easing: 'easeOutQuad'
      });
    }
  }, [activeVisualization]);

  // Efecto para renderizar el heatmap
  useEffect(() => {
    if (heatmapRef.current && activeVisualization === 'heatmap' && cryptoData.length > 0) {
      renderHeatmap();
    }
  }, [cryptoData, activeVisualization]);

  // Efecto para renderizar el gráfico de dominancia
  useEffect(() => {
    if (dominanceRef.current && activeVisualization === 'dominance' && marketData) {
      renderDominanceChart();
    }
  }, [marketData, activeVisualization]);

  // Efecto para renderizar la matriz de correlación
  useEffect(() => {
    if (correlationRef.current && activeVisualization === 'correlation' && cryptoData.length > 0) {
      renderCorrelationMatrix();
    }
  }, [cryptoData, activeVisualization]);

  // Función para renderizar el heatmap
  const renderHeatmap = () => {
    if (!heatmapRef.current || cryptoData.length === 0) return;

    const container = heatmapRef.current;
    container.innerHTML = '';

    // Ordenar criptomonedas por capitalización de mercado
    const sortedCryptos = [...cryptoData].sort((a, b) => b.market_cap - a.market_cap).slice(0, 25);

    // Crear el contenedor de la cuadrícula
    const grid = document.createElement('div');
    grid.className = 'heatmap-grid';
    grid.style.display = 'grid';
    grid.style.gridTemplateColumns = `repeat(5, 1fr)`;
    grid.style.gridTemplateRows = `repeat(${Math.ceil(sortedCryptos.length / 5)}, 1fr)`;
    grid.style.gap = '10px';
    grid.style.width = '100%';
    grid.style.height = '100%';

    // Crear celdas para cada criptomoneda
    sortedCryptos.forEach(crypto => {
      const cell = document.createElement('div');
      cell.className = 'heatmap-cell';
      cell.style.borderRadius = '6px';
      cell.style.padding = '10px';
      cell.style.display = 'flex';
      cell.style.flexDirection = 'column';
      cell.style.justifyContent = 'space-between';

      // Determinar el color basado en el cambio de precio
      const priceChange = crypto.price_change_percentage_24h || 0;
      let backgroundColor;

      if (priceChange > 10) backgroundColor = 'rgba(0, 255, 157, 0.9)';
      else if (priceChange > 5) backgroundColor = 'rgba(0, 255, 157, 0.7)';
      else if (priceChange > 2) backgroundColor = 'rgba(0, 255, 157, 0.5)';
      else if (priceChange > 0) backgroundColor = 'rgba(0, 255, 157, 0.3)';
      else if (priceChange > -2) backgroundColor = 'rgba(255, 58, 110, 0.3)';
      else if (priceChange > -5) backgroundColor = 'rgba(255, 58, 110, 0.5)';
      else if (priceChange > -10) backgroundColor = 'rgba(255, 58, 110, 0.7)';
      else backgroundColor = 'rgba(255, 58, 110, 0.9)';

      cell.style.backgroundColor = backgroundColor;

      // Información de la criptomoneda
      const nameElement = document.createElement('div');
      nameElement.className = 'crypto-name';
      nameElement.style.fontWeight = 'bold';
      nameElement.style.fontSize = '14px';
      nameElement.textContent = crypto.symbol.toUpperCase();

      const priceElement = document.createElement('div');
      priceElement.className = 'crypto-price';
      priceElement.style.fontSize = '12px';
      priceElement.textContent = `$${crypto.current_price.toLocaleString()}`;

      const changeElement = document.createElement('div');
      changeElement.className = 'crypto-change';
      changeElement.style.fontSize = '12px';
      changeElement.style.fontWeight = 'bold';
      changeElement.textContent = `${priceChange >= 0 ? '+' : ''}${priceChange.toFixed(2)}%`;

      cell.appendChild(nameElement);
      cell.appendChild(priceElement);
      cell.appendChild(changeElement);

      // Añadir evento de hover
      cell.addEventListener('mouseenter', () => {
        anime({
          targets: cell,
          scale: 1.05,
          boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
          duration: 300,
          easing: 'easeOutQuad'
        });
      });

      cell.addEventListener('mouseleave', () => {
        anime({
          targets: cell,
          scale: 1,
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)',
          duration: 300,
          easing: 'easeOutQuad'
        });
      });

      grid.appendChild(cell);
    });

    container.appendChild(grid);
  };

  // Función para renderizar el gráfico de dominancia
  const renderDominanceChart = () => {
    if (!dominanceRef.current || !marketData) return;

    const container = dominanceRef.current;
    container.innerHTML = '';

    // Obtener datos de dominancia
    const dominanceData = marketData.market_cap_percentage || {};

    // Ordenar por dominancia
    const sortedDominance = Object.entries(dominanceData)
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 6);

    // Calcular el total para "otros"
    const topCoinsTotal = sortedDominance.reduce((sum, [, value]: any) => sum + value, 0);
    const othersValue = 100 - topCoinsTotal;

    if (othersValue > 0) {
      sortedDominance.push(['others', othersValue]);
    }

    // Crear el gráfico de pastel
    const svgNS = "http://www.w3.org/2000/svg";
    const svg = document.createElementNS(svgNS, "svg");
    svg.setAttribute("width", "100%");
    svg.setAttribute("height", "100%");
    svg.setAttribute("viewBox", "0 0 100 100");

    const centerX = 50;
    const centerY = 50;
    const radius = 40;

    // Colores para las diferentes criptomonedas
    const colors = [
      '#00e0ff', '#00a3ff', '#00ff9d', '#a3ff00', '#ffcc00', '#ff3a6e', '#9d00ff'
    ];

    let startAngle = 0;
    const legend = document.createElement('div');
    legend.className = 'dominance-legend';
    legend.style.display = 'flex';
    legend.style.flexWrap = 'wrap';
    legend.style.justifyContent = 'center';
    legend.style.marginTop = '10px';

    // Crear sectores del gráfico
    sortedDominance.forEach(([coin, percentage]: [string, number], index) => {
      const endAngle = startAngle + (percentage * 3.6); // 3.6 = 360 / 100

      // Calcular puntos del arco
      const x1 = centerX + radius * Math.cos(Math.PI * startAngle / 180);
      const y1 = centerY + radius * Math.sin(Math.PI * startAngle / 180);
      const x2 = centerX + radius * Math.cos(Math.PI * endAngle / 180);
      const y2 = centerY + radius * Math.sin(Math.PI * endAngle / 180);

      // Determinar si el arco es mayor a 180 grados
      const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

      // Crear el path para el sector
      const path = document.createElementNS(svgNS, "path");
      path.setAttribute("d", `
        M ${centerX} ${centerY}
        L ${x1} ${y1}
        A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}
        Z
      `);
      path.setAttribute("fill", colors[index % colors.length]);
      path.setAttribute("stroke", "#0f1123");
      path.setAttribute("stroke-width", "0.5");

      // Añadir tooltip al hover
      path.setAttribute("data-coin", coin);
      path.setAttribute("data-percentage", percentage.toFixed(2) + "%");

      path.addEventListener('mouseenter', (e) => {
        const target = e.target as SVGPathElement;
        anime({
          targets: target,
          transform: 'scale(1.05) translateX(2px) translateY(2px)',
          duration: 300,
          easing: 'easeOutQuad'
        });

        // Mostrar tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'dominance-tooltip';
        tooltip.style.position = 'absolute';
        tooltip.style.backgroundColor = 'rgba(15, 17, 35, 0.9)';
        tooltip.style.color = '#fff';
        tooltip.style.padding = '5px 10px';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.zIndex = '1000';
        tooltip.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        tooltip.innerHTML = `
          <div><strong>${coin.toUpperCase()}</strong></div>
          <div>${percentage.toFixed(2)}%</div>
        `;

        document.body.appendChild(tooltip);

        // Posicionar tooltip
        const rect = target.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 10}px`;

        target.addEventListener('mouseleave', () => {
          document.body.removeChild(tooltip);
        });
      });

      path.addEventListener('mouseleave', (e) => {
        const target = e.target as SVGPathElement;
        anime({
          targets: target,
          transform: 'scale(1) translateX(0) translateY(0)',
          duration: 300,
          easing: 'easeOutQuad'
        });
      });

      svg.appendChild(path);

      // Crear elemento de leyenda
      const legendItem = document.createElement('div');
      legendItem.className = 'legend-item';
      legendItem.style.display = 'flex';
      legendItem.style.alignItems = 'center';
      legendItem.style.margin = '5px 10px';

      const colorBox = document.createElement('div');
      colorBox.style.width = '12px';
      colorBox.style.height = '12px';
      colorBox.style.backgroundColor = colors[index % colors.length];
      colorBox.style.marginRight = '5px';
      colorBox.style.borderRadius = '2px';

      const coinName = document.createElement('span');
      coinName.textContent = `${coin.toUpperCase()}: ${percentage.toFixed(2)}%`;
      coinName.style.fontSize = '12px';

      legendItem.appendChild(colorBox);
      legendItem.appendChild(coinName);
      legend.appendChild(legendItem);

      startAngle = endAngle;
    });

    container.appendChild(svg);
    container.appendChild(legend);
  };

  // Función para renderizar la matriz de correlación
  const renderCorrelationMatrix = () => {
    if (!correlationRef.current || cryptoData.length < 5) return;

    const container = correlationRef.current;
    container.innerHTML = '';

    // Seleccionar las 5 principales criptomonedas por capitalización de mercado
    const topCryptos = [...cryptoData]
      .sort((a, b) => b.market_cap - a.market_cap)
      .slice(0, 5);

    // Crear matriz de correlación simulada (en un caso real, esto se calcularía con datos históricos)
    const correlationMatrix = [
      [1.00, 0.82, 0.65, 0.45, 0.30],
      [0.82, 1.00, 0.70, 0.55, 0.40],
      [0.65, 0.70, 1.00, 0.60, 0.50],
      [0.45, 0.55, 0.60, 1.00, 0.75],
      [0.30, 0.40, 0.50, 0.75, 1.00]
    ];

    // Crear tabla para la matriz
    const table = document.createElement('table');
    table.className = 'correlation-table';
    table.style.width = '100%';
    table.style.borderCollapse = 'separate';
    table.style.borderSpacing = '2px';

    // Crear encabezado
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    // Celda vacía en la esquina superior izquierda
    const emptyCell = document.createElement('th');
    emptyCell.style.width = '80px';
    emptyCell.style.height = '80px';
    headerRow.appendChild(emptyCell);

    // Encabezados de columnas
    topCryptos.forEach(crypto => {
      const th = document.createElement('th');
      th.style.padding = '8px';
      th.style.textAlign = 'center';

      if (crypto.image) {
        const img = document.createElement('img');
        img.src = crypto.image;
        img.alt = crypto.name;
        img.style.width = '24px';
        img.style.height = '24px';
        img.style.borderRadius = '50%';
        th.appendChild(img);
      }

      const symbol = document.createElement('div');
      symbol.textContent = crypto.symbol.toUpperCase();
      symbol.style.marginTop = '5px';
      symbol.style.fontSize = '12px';
      th.appendChild(symbol);

      headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Crear cuerpo de la tabla
    const tbody = document.createElement('tbody');

    topCryptos.forEach((crypto, rowIndex) => {
      const row = document.createElement('tr');

      // Encabezado de fila
      const th = document.createElement('th');
      th.style.padding = '8px';
      th.style.textAlign = 'center';

      if (crypto.image) {
        const img = document.createElement('img');
        img.src = crypto.image;
        img.alt = crypto.name;
        img.style.width = '24px';
        img.style.height = '24px';
        img.style.borderRadius = '50%';
        th.appendChild(img);
      }

      const symbol = document.createElement('div');
      symbol.textContent = crypto.symbol.toUpperCase();
      symbol.style.marginTop = '5px';
      symbol.style.fontSize = '12px';
      th.appendChild(symbol);

      row.appendChild(th);

      // Celdas de correlación
      correlationMatrix[rowIndex].forEach((correlation, colIndex) => {
        const td = document.createElement('td');
        td.style.padding = '10px';
        td.style.textAlign = 'center';
        td.style.borderRadius = '4px';

        // Determinar color basado en la correlación
        let backgroundColor;
        if (correlation === 1) {
          backgroundColor = 'rgba(0, 224, 255, 0.8)';
        } else if (correlation > 0.8) {
          backgroundColor = 'rgba(0, 224, 255, 0.7)';
        } else if (correlation > 0.6) {
          backgroundColor = 'rgba(0, 224, 255, 0.5)';
        } else if (correlation > 0.4) {
          backgroundColor = 'rgba(0, 224, 255, 0.3)';
        } else if (correlation > 0.2) {
          backgroundColor = 'rgba(0, 224, 255, 0.2)';
        } else {
          backgroundColor = 'rgba(0, 224, 255, 0.1)';
        }

        td.style.backgroundColor = backgroundColor;
        td.textContent = correlation.toFixed(2);

        row.appendChild(td);
      });

      tbody.appendChild(row);
    });

    table.appendChild(tbody);
    container.appendChild(table);

    // Añadir explicación
    const explanation = document.createElement('div');
    explanation.className = 'correlation-explanation';
    explanation.style.marginTop = '15px';
    explanation.style.fontSize = '12px';
    explanation.style.textAlign = 'center';
    explanation.innerHTML = `
      <p>La matriz de correlación muestra la relación entre los movimientos de precios de las principales criptomonedas.</p>
      <p>Un valor de 1.00 indica una correlación perfecta, mientras que valores cercanos a 0 indican poca correlación.</p>
    `;

    container.appendChild(explanation);
  };

  return (
    <div className="advanced-market-visualizations" ref={containerRef}>
      <div className="visualization-header">
        <h3>Visualizaciones Avanzadas de Mercado</h3>
        <div className="visualization-tabs">
          <button
            className={`tab-button ${activeVisualization === 'heatmap' ? 'active' : ''}`}
            onClick={() => setActiveVisualization('heatmap')}
          >
            Mapa de Calor
          </button>
          <button
            className={`tab-button ${activeVisualization === 'dominance' ? 'active' : ''}`}
            onClick={() => setActiveVisualization('dominance')}
          >
            Dominancia
          </button>
          <button
            className={`tab-button ${activeVisualization === 'correlation' ? 'active' : ''}`}
            onClick={() => setActiveVisualization('correlation')}
          >
            Correlación
          </button>
        </div>
      </div>

      <div className="visualization-content">
        {activeVisualization === 'heatmap' && (
          <div className="heatmap-container" ref={heatmapRef}>
            {cryptoData.length === 0 && (
              <div className="loading-message">Cargando datos para el mapa de calor...</div>
            )}
          </div>
        )}

        {activeVisualization === 'dominance' && (
          <div className="dominance-container" ref={dominanceRef}>
            {!marketData && (
              <div className="loading-message">Cargando datos de dominancia...</div>
            )}
          </div>
        )}

        {activeVisualization === 'correlation' && (
          <div className="correlation-container" ref={correlationRef}>
            {cryptoData.length < 5 && (
              <div className="loading-message">Cargando datos para la matriz de correlación...</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedMarketVisualizations;
