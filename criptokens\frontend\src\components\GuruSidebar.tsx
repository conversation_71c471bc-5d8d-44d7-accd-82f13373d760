import React, { useState, useEffect, useContext, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import CriptoAgentAvatar from './CriptoAgentAvatarExport';
import { AvatarStatusContext } from './Layout';
import { useTopCryptocurrencies } from '../hooks/useMcpClient';
import anime from '../utils/animeUtils';
import '../styles/GuruSidebar.css';

const GuruSidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { avatarStatus, setAvatarStatus } = useContext(AvatarStatusContext);
  const [marketSentiment, setMarketSentiment] = useState<string>('neutral');
  const [significantChange, setSignificantChange] = useState<string | null>(null);
  const { data: topCryptos, loading } = useTopCryptocurrencies(10);
  const particlesContainerRef = useRef<HTMLDivElement>(null);
  const isGuruPage = location.pathname === '/guru';

  // Efecto para analizar datos y determinar el estado del avatar
  useEffect(() => {
    if (!topCryptos || topCryptos.length === 0 || loading) return;

    // Analizar cambios en el mercado para determinar el sentimiento
    const btcData = topCryptos.find(c => c.id === 'bitcoin');
    const btcChange = btcData?.price_change_percentage_24h || 0;

    const marketAvgChange = topCryptos.slice(0, 10).reduce((sum, crypto) =>
      sum + (crypto.price_change_percentage_24h || 0), 0) / 10;

    // Lógica para determinar el sentimiento del mercado
    if (marketAvgChange > 5) {
      setMarketSentiment('bullish');
      setSignificantChange('El mercado está en fuerte tendencia alcista');
      setAvatarStatus('positive');
    } else if (marketAvgChange > 2) {
      setMarketSentiment('positive');
      setSignificantChange('El mercado muestra señales positivas');
      setAvatarStatus('positive');
    } else if (marketAvgChange < -5) {
      setMarketSentiment('bearish');
      setSignificantChange('El mercado está en fuerte tendencia bajista');
      setAvatarStatus('negative');
    } else if (marketAvgChange < -2) {
      setMarketSentiment('negative');
      setSignificantChange('El mercado muestra señales negativas');
      setAvatarStatus('concerned');
    } else if (Math.abs(btcChange) > 3) {
      setMarketSentiment('volatile');
      setSignificantChange('Bitcoin está mostrando volatilidad');
      setAvatarStatus('concerned');
    } else {
      setMarketSentiment('neutral');
      setSignificantChange('El mercado está estable');
      setAvatarStatus('neutral');
    }
  }, [topCryptos, loading, setAvatarStatus, isGuruPage]);

  // Efecto para inicializar y animar las partículas
  useEffect(() => {
    if (isGuruPage || !particlesContainerRef.current) return;

    // Detectar rendimiento del dispositivo
    const checkPerformance = () => {
      // Comprobar si es un dispositivo móvil o de baja potencia
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isLowPower = window.navigator.hardwareConcurrency ? window.navigator.hardwareConcurrency < 4 : false;

      return isMobile || isLowPower;
    };

    // Ajustar la calidad de las animaciones según el rendimiento
    const isLowPerformance = checkPerformance();

    // Limpiar partículas existentes
    particlesContainerRef.current.innerHTML = '';

    // Crear círculos concéntricos (orbitas)
    for (let i = 1; i <= 3; i++) {
      const orbitElement = document.createElement('div');
      orbitElement.className = `guru-orbit guru-orbit-${i}`;
      particlesContainerRef.current.appendChild(orbitElement);

      // Animar rotación de las orbitas (más lenta en dispositivos de bajo rendimiento)
      anime({
        targets: orbitElement,
        rotate: 360,
        duration: isLowPerformance ? 30000 + (i * 5000) : 20000 + (i * 5000),
        easing: 'linear',
        loop: true
      });
    }

    // Crear partículas (menos en dispositivos de bajo rendimiento)
    const numParticles = isLowPerformance ? 9 : 12;

    for (let i = 0; i < numParticles; i++) {
      // Crear elemento de partícula
      const particleElement = document.createElement('div');
      particleElement.className = `guru-particle ${avatarStatus}`;

      // Determinar en qué órbita estará la partícula (distribuir entre las 3 órbitas)
      const orbitIndex = i % 3;
      const orbitRadius = [70, 85, 100][orbitIndex]; // Radio de cada órbita

      // Posición inicial en la órbita
      const angle = (i * (360 / (numParticles / 3))) * (Math.PI / 180); // Distribuir partículas uniformemente
      const x = 50 + Math.cos(angle) * orbitRadius / 2; // 50% es el centro
      const y = 50 + Math.sin(angle) * orbitRadius / 2; // 50% es el centro

      particleElement.style.left = `${x}%`;
      particleElement.style.top = `${y}%`;

      // Añadir al contenedor
      particlesContainerRef.current.appendChild(particleElement);

      // Animar la partícula en órbita (más lenta en dispositivos de bajo rendimiento)
      anime({
        targets: particleElement,
        loop: true,
        duration: isLowPerformance ? 15000 + (orbitIndex * 2000) : 10000 + (orbitIndex * 2000),
        easing: 'linear',
        keyframes: [
          {
            translateX: Math.cos(angle) * orbitRadius / 2,
            translateY: Math.sin(angle) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI/2) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI/2) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI*3/2) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI*3/2) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI*2) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI*2) * orbitRadius / 2
          }
        ]
      });
    }

    // Limpiar al desmontar
    return () => {
      if (particlesContainerRef.current) {
        anime.remove(particlesContainerRef.current.childNodes);
        particlesContainerRef.current.innerHTML = '';
      }
    };
  }, [isGuruPage]);

  // Efecto para actualizar las partículas según el estado
  useEffect(() => {
    if (isGuruPage || !particlesContainerRef.current || particlesContainerRef.current.childNodes.length === 0) return;

    console.log('Actualizando partículas al estado:', avatarStatus);

    // Actualizar las partículas (no los círculos)
    const elements = Array.from(particlesContainerRef.current.childNodes);
    const particles = elements.filter(node => (node as HTMLElement).className.includes('guru-particle'));
    const orbits = elements.filter(node => (node as HTMLElement).className.includes('guru-orbit'));

    // Actualizar color de las órbitas con animación
    orbits.forEach((orbit: any, index) => {
      let orbitColor = '';
      switch (avatarStatus) {
        case 'positive':
          orbitColor = index % 2 === 0 ? 'rgba(0, 255, 157, 0.4)' : 'rgba(0, 242, 255, 0.4)';
          break;
        case 'negative':
          orbitColor = index % 2 === 0 ? 'rgba(255, 58, 110, 0.4)' : 'rgba(123, 77, 255, 0.4)';
          break;
        case 'concerned':
          orbitColor = index % 2 === 0 ? 'rgba(255, 204, 0, 0.4)' : 'rgba(255, 107, 61, 0.4)';
          break;
        case 'thinking':
          orbitColor = index % 2 === 0 ? 'rgba(123, 77, 255, 0.4)' : 'rgba(0, 242, 255, 0.4)';
          break;
        default: // neutral/idle
          orbitColor = index % 2 === 0 ? 'rgba(123, 77, 255, 0.4)' : 'rgba(0, 242, 255, 0.4)';
          break;
      }

      // Animar la transición de color de las órbitas
      anime({
        targets: orbit,
        borderColor: orbitColor,
        duration: 300,
        easing: 'easeOutSine'
      });
    });

    // Actualizar las partículas con animación
    particles.forEach((particle: any) => {
      // Actualizar clase
      particle.className = `guru-particle ${avatarStatus}`;

      // Animar la transición de las partículas
      anime({
        targets: particle,
        opacity: [0.5, 1],
        scale: [0.8, 1],
        duration: 300,
        easing: 'easeOutSine'
      });
    });
  }, [avatarStatus, isGuruPage]);



  // No renderizar nada si estamos en la página del Guru
  if (isGuruPage) {
    return null;
  }

  return (
    <div className="guru-sidebar" onClick={() => navigate('/guru')} data-sentiment={marketSentiment}>
      <div className="guru-avatar-container">
        <div className="guru-particles" ref={particlesContainerRef}></div>
        <div className="avatar-wrapper">
          <CriptoAgentAvatar
            mood={marketSentiment as any}
            size="medium"
            pulseEffect={true}
          />
        </div>
      </div>
    </div>
  );
};

export default GuruSidebar;
