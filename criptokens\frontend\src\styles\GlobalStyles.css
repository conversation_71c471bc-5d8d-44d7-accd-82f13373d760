/* ===== GLOBAL STYLES ===== */

:root {
  /* Color Palette */
  --primary: #00f2ff;
  --primary-dark: #00c2cc;
  --primary-light: #80f8ff;
  --secondary: #4657ce;
  --secondary-dark: #3645a5;
  --secondary-light: #8a94e2;
  
  --bg-dark: #0a0a1a;
  --bg-medium: #141432;
  --bg-light: #1e1e4a;
  
  --text-bright: #ffffff;
  --text-medium: #e0e0ff;
  --text-dim: #a0a0d0;
  
  --success: #00c853;
  --warning: #ffc107;
  --error: #ff5252;
  
  /* Gradients */
  --gradient-primary: linear-gradient(90deg, var(--secondary), var(--primary));
  --gradient-card: linear-gradient(135deg, rgba(20, 20, 50, 0.7), rgba(15, 15, 35, 0.7));
  --gradient-button: linear-gradient(90deg, var(--secondary), var(--primary));
  
  /* Shadows */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);
  --shadow-primary: 0 5px 15px rgba(0, 242, 255, 0.3);
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;
  
  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-xxl: 48px;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index layers */
  --z-base: 1;
  --z-above: 10;
  --z-modal: 100;
  --z-overlay: 1000;
}

/* Base Styles */
body {
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: var(--bg-dark);
  color: var(--text-medium);
  margin: 0;
  padding: 0;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  color: var(--text-bright);
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: var(--space-lg);
}

h2 {
  font-size: 2rem;
  margin-bottom: var(--space-md);
}

h3 {
  font-size: 1.5rem;
  margin-bottom: var(--space-md);
}

h4 {
  font-size: 1.25rem;
  margin-bottom: var(--space-sm);
}

p {
  margin-top: 0;
  margin-bottom: var(--space-md);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-light);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  outline: none;
  text-align: center;
}

.btn-primary {
  background: var(--gradient-button);
  color: var(--text-bright);
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 242, 255, 0.4);
}

.btn-secondary {
  background: rgba(70, 87, 206, 0.2);
  color: var(--text-medium);
  border: 1px solid rgba(70, 87, 206, 0.4);
}

.btn-secondary:hover {
  background: rgba(70, 87, 206, 0.3);
  color: var(--text-bright);
}

.btn-sm {
  padding: var(--space-xs) var(--space-md);
  font-size: 0.875rem;
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  font-size: 1.125rem;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  padding: 0;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Cards */
.card {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border: 1px solid rgba(64, 220, 255, 0.2);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(64, 220, 255, 0.4);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-md);
}

.card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-bright);
}

.card-body {
  color: var(--text-medium);
}

.card-footer {
  margin-top: var(--space-md);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* Forms */
.form-group {
  margin-bottom: var(--space-md);
}

.form-label {
  display: block;
  margin-bottom: var(--space-xs);
  color: var(--text-medium);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: rgba(30, 30, 60, 0.6);
  border: 1px solid rgba(64, 220, 255, 0.3);
  border-radius: var(--radius-md);
  color: var(--text-bright);
  font-size: 1rem;
  transition: all var(--transition-normal);
}

.form-control:focus {
  outline: none;
  border-color: rgba(64, 220, 255, 0.8);
  box-shadow: 0 0 0 3px rgba(0, 242, 255, 0.2);
}

.form-control::placeholder {
  color: rgba(160, 160, 208, 0.6);
}

/* Utilities */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }

.bg-gradient {
  background: var(--gradient-primary);
}

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }

.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 0.4; }
  100% { transform: scale(1); opacity: 0.8; }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-fadeIn { animation: fadeIn 0.5s ease-out forwards; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-spin { animation: spin 1s linear infinite; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 15, 45, 0.3);
}

::-webkit-scrollbar-thumb {
  background-color: rgba(64, 220, 255, 0.5);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(64, 220, 255, 0.7);
}

/* Responsive Breakpoints */
@media (max-width: 1200px) {
  :root {
    --space-xl: 24px;
    --space-xxl: 32px;
  }
}

@media (max-width: 768px) {
  :root {
    --space-lg: 16px;
    --space-xl: 20px;
    --space-xxl: 28px;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.25rem; }
}

@media (max-width: 480px) {
  :root {
    --space-md: 12px;
    --space-lg: 16px;
    --space-xl: 20px;
  }
  
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  
  .card { padding: var(--space-md); }
}
