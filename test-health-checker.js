/**
 * Script para probar el verificador de disponibilidad
 */
const { checkTcpPort, checkHttpEndpoint } = require('./health-checker');

async function runTests() {
  console.log('Pruebas del verificador de disponibilidad:');
  
  // Prueba de verificación de puerto TCP
  console.log('\nPrueba de verificación de puerto TCP:');
  
  // Probar un puerto que probablemente esté libre
  const port = 12345;
  const tcpResult = await checkTcpPort(port);
  console.log(`- Puerto ${port}: ${tcpResult ? 'En uso' : 'Libre'}`);
  
  // Probar un puerto que probablemente esté en uso (80 para HTTP)
  const httpPort = 80;
  const httpTcpResult = await checkTcpPort(httpPort);
  console.log(`- Puerto ${httpPort}: ${httpTcpResult ? 'En uso' : 'Libre'}`);
  
  // Prueba de verificación de endpoint HTTP
  console.log('\nPrueba de verificación de endpoint HTTP:');
  
  // Probar un endpoint que debería estar disponible
  const googleUrl = 'http://www.google.com';
  const httpResult = await checkHttpEndpoint(googleUrl);
  console.log(`- ${googleUrl}: ${httpResult ? 'Disponible' : 'No disponible'}`);
  
  // Probar un endpoint que probablemente no exista
  const invalidUrl = 'http://this-url-does-not-exist-12345.com';
  const invalidHttpResult = await checkHttpEndpoint(invalidUrl);
  console.log(`- ${invalidUrl}: ${invalidHttpResult ? 'Disponible' : 'No disponible'}`);
  
  console.log('\nPrueba exitosa: El verificador de disponibilidad funciona correctamente.');
}

runTests().catch(err => {
  console.error('Error en la prueba del verificador de disponibilidad:', err);
});
