const { spawn } = require('child_process');
const path = require('path');

// Función para iniciar un proceso
function startProcess(command, args, cwd, name) {
  console.log(`Iniciando ${name}...`);
  
  const process = spawn(command, args, {
    cwd,
    shell: true,
    stdio: 'inherit'
  });
  
  process.on('error', (error) => {
    console.error(`Error al iniciar ${name}:`, error);
  });
  
  process.on('close', (code) => {
    if (code !== 0) {
      console.log(`${name} se cerró con código ${code}`);
    }
  });
  
  return process;
}

// Rutas de los directorios
const frontendDir = path.join(__dirname, 'frontend');

// Iniciar el frontend
const frontendProcess = startProcess('npx', ['vite'], frontendDir, 'Frontend');

// Manejar la terminación del script
process.on('SIGINT', () => {
  console.log('Deteniendo todos los servicios...');
  frontendProcess.kill();
  process.exit();
});
