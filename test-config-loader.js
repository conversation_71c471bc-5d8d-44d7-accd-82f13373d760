/**
 * Script para probar el cargador de configuración
 */
const { loadConfig } = require('./config-loader');

try {
  const config = loadConfig('./config.json');
  console.log('Configuración cargada correctamente:');
  console.log('- Sistema:', config.system);
  console.log('- Python:', config.python);
  console.log('- Componentes:', config.components.length);
  
  // Verificar que los componentes tienen los campos necesarios
  for (const component of config.components) {
    console.log(`\nComponente: ${component.name}`);
    console.log('- ID:', component.id);
    console.log('- Comando:', component.cmd);
    console.log('- Argumentos:', component.args);
    console.log('- Directorio:', component.cwd);
    console.log('- Puerto:', component.port);
    console.log('- Dependencias:', component.dependencies);
    console.log('- Verificación de salud:', component.healthCheck.type);
  }
  
  console.log('\nPrueba exitosa: El cargador de configuración funciona correctamente.');
} catch (err) {
  console.error('Error en la prueba del cargador de configuración:', err);
}
