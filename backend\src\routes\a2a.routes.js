/**
 * Routes for A2A agents
 */
const express = require('express');
const router = express.Router();
const a2aService = require('../services/a2a.service');

/**
 * @route POST /api/a2a/guru/predict
 * @desc Get a prediction from the Guru Cripto A2A agent
 * @access Public
 */
router.post('/guru/predict', async (req, res) => {
  try {
    const { query, sessionId } = req.body;
    
    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }
    
    const prediction = await a2aService.getPrediction(query, sessionId);
    res.json(prediction);
  } catch (error) {
    console.error('Error getting prediction from Guru Cripto A2A agent:', error.message);
    res.status(500).json({ error: 'Error getting prediction' });
  }
});

/**
 * @route GET /api/a2a/guru/predict/stream
 * @desc Stream a prediction from the Guru Cripto A2A agent
 * @access Public
 */
router.get('/guru/predict/stream', async (req, res) => {
  try {
    const { query, sessionId } = req.query;
    
    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }
    
    // Set headers for SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    
    // Stream prediction
    await a2aService.streamPrediction(query, sessionId, (data) => {
      res.write(`data: ${JSON.stringify(data)}\n\n`);
      
      // End the response when the task is complete
      if (data.is_task_complete) {
        res.end();
      }
    });
  } catch (error) {
    console.error('Error streaming prediction from Guru Cripto A2A agent:', error.message);
    res.write(`data: ${JSON.stringify({ error: 'Error streaming prediction' })}\n\n`);
    res.end();
  }
});

/**
 * @route GET /api/a2a/technical/analyze
 * @desc Get technical analysis from the Technical Analysis A2A agent
 * @access Public
 */
router.get('/technical/analyze', async (req, res) => {
  try {
    const { crypto, timeframe, sessionId } = req.query;
    
    if (!crypto) {
      return res.status(400).json({ error: 'Cryptocurrency is required' });
    }
    
    const analysis = await a2aService.getTechnicalAnalysis(crypto, timeframe, sessionId);
    res.json(analysis);
  } catch (error) {
    console.error('Error getting technical analysis:', error.message);
    res.status(500).json({ error: 'Error getting technical analysis' });
  }
});

/**
 * @route GET /api/a2a/sentiment/analyze
 * @desc Get sentiment analysis from the Sentiment Analysis A2A agent
 * @access Public
 */
router.get('/sentiment/analyze', async (req, res) => {
  try {
    const { crypto, timeframe, sessionId } = req.query;
    
    if (!crypto) {
      return res.status(400).json({ error: 'Cryptocurrency is required' });
    }
    
    const analysis = await a2aService.getSentimentAnalysis(crypto, timeframe, sessionId);
    res.json(analysis);
  } catch (error) {
    console.error('Error getting sentiment analysis:', error.message);
    res.status(500).json({ error: 'Error getting sentiment analysis' });
  }
});

/**
 * @route GET /api/a2a/onchain/analyze
 * @desc Get on-chain analysis from the On-Chain Analysis A2A agent
 * @access Public
 */
router.get('/onchain/analyze', async (req, res) => {
  try {
    const { crypto, timeframe, sessionId } = req.query;
    
    if (!crypto) {
      return res.status(400).json({ error: 'Cryptocurrency is required' });
    }
    
    const analysis = await a2aService.getOnChainAnalysis(crypto, timeframe, sessionId);
    res.json(analysis);
  } catch (error) {
    console.error('Error getting on-chain analysis:', error.message);
    res.status(500).json({ error: 'Error getting on-chain analysis' });
  }
});

/**
 * @route GET /api/a2a/status
 * @desc Get status of A2A agents
 * @access Public
 */
router.get('/status', async (req, res) => {
  try {
    const status = await a2aService.getAgentsStatus();
    res.json(status);
  } catch (error) {
    console.error('Error getting A2A agents status:', error.message);
    res.status(500).json({ error: 'Error getting A2A agents status' });
  }
});

module.exports = router;
