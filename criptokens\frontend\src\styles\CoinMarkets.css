/* Estilos para los mercados de la criptomoneda */

.coin-markets-container {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.markets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.markets-header h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--text-primary);
}

.search-bar {
  position: relative;
  width: 100%;
  max-width: 300px;
}

.search-bar input {
  width: 100%;
  padding: 0.625rem 2.5rem 0.625rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
  font-size: 0.875rem;
  outline: none;
  transition: all 0.2s ease;
}

.search-bar input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-transparent);
}

.search-bar i {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

.markets-table-container {
  width: 100%;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-transparent) transparent;
}

.markets-table-container::-webkit-scrollbar {
  height: 6px;
}

.markets-table-container::-webkit-scrollbar-track {
  background: transparent;
}

.markets-table-container::-webkit-scrollbar-thumb {
  background-color: var(--color-primary-transparent);
  border-radius: 6px;
}

.markets-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 0.875rem;
}

.markets-table thead th {
  position: sticky;
  top: 0;
  background-color: var(--color-surface);
  padding: 0.75rem 1rem;
  text-align: left;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.markets-table thead th:hover {
  color: var(--text-primary);
  background-color: var(--color-surface-light);
}

.markets-table thead th.ascending::after {
  content: ' ↑';
  color: var(--color-primary);
}

.markets-table thead th.descending::after {
  content: ' ↓';
  color: var(--color-primary);
}

.markets-table tbody td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.markets-table tbody tr:hover td {
  background-color: var(--color-surface-light);
}

.markets-table tbody tr:last-child td {
  border-bottom: none;
}

/* Celdas específicas */
.exchange-cell {
  min-width: 180px;
}

.exchange-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.exchange-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: contain;
}

.exchange-logo-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
}

.exchange-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.pair-cell {
  min-width: 120px;
  color: var(--text-secondary);
}

.price-cell {
  min-width: 100px;
  font-weight: var(--font-weight-semibold);
}

.volume-cell {
  min-width: 120px;
  color: var(--text-secondary);
}

.trust-cell {
  min-width: 120px;
}

.trust-score {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.trust-score i {
  font-size: 0.625rem;
}

.trust-score-5 i {
  color: var(--color-positive);
}

.trust-score-4 i {
  color: #4CAF50;
}

.trust-score-3 i {
  color: #FFC107;
}

.trust-score-2 i {
  color: #FF9800;
}

.trust-score-1 i {
  color: var(--color-negative);
}

.trust-score-unknown {
  color: var(--text-tertiary);
}

.actions-cell {
  min-width: 80px;
  text-align: center;
}

.trade-button {
  padding: 0.375rem 0.75rem;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border-radius: 4px;
  font-size: 0.75rem;
  text-decoration: none;
  transition: all 0.2s ease;
  display: inline-block;
  border: 1px solid var(--color-primary);
}

.trade-button:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Paginación */
.markets-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.pagination-button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0.5rem;
}

/* Mensaje de vacío */
.markets-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  gap: 1rem;
}

.markets-empty i {
  font-size: 3rem;
  color: var(--text-tertiary);
}

.markets-empty p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .markets-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-bar {
    max-width: 100%;
  }
  
  .markets-table thead th,
  .markets-table tbody td {
    padding: 0.625rem 0.75rem;
  }
  
  .exchange-cell {
    min-width: 150px;
  }
  
  .pair-cell {
    min-width: 100px;
  }
}

@media (max-width: 576px) {
  .coin-markets-container {
    padding: 0.75rem;
  }
  
  .markets-table thead th,
  .markets-table tbody td {
    padding: 0.5rem 0.625rem;
    font-size: 0.75rem;
  }
  
  .exchange-logo, .exchange-logo-placeholder {
    width: 20px;
    height: 20px;
  }
  
  .trust-cell {
    display: none;
  }
}
