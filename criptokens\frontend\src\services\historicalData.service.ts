/**
 * Servicio para obtener datos históricos de criptomonedas
 */
import axios from 'axios';
// Importar la configuración directamente
const config = {
  urls: {
    backend: 'http://localhost:3002'
  }
};
import { PortfolioAsset } from './portfolio.service';

// Cliente para el backend
const backendClient = axios.create({
  baseURL: config.urls.backend,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  timeout: 30000
});

/**
 * Obtiene datos históricos de una criptomoneda
 * @param symbol Símbolo de la criptomoneda (ej. BTC, ETH)
 * @param days Número de días de datos históricos (por defecto 30)
 * @returns Datos históricos de la criptomoneda
 */
export const getHistoricalData = async (symbol: string, days: number = 30) => {
  try {
    const response = await fetch(`${config.urls.backend}/api/crypto/historical/${symbol}?days=${days}`);

    if (!response.ok) {
      throw new Error(`Error al obtener datos históricos: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error en el servicio de datos históricos:', error);
    throw error;
  }
};

/**
 * Obtiene datos históricos de una criptomoneda desde el MCP
 * @param symbol Símbolo de la criptomoneda (ej. BTC, ETH)
 * @param days Número de días de datos históricos (por defecto 30)
 * @returns Datos históricos de la criptomoneda
 */
export const getHistoricalDataFromMCP = async (symbol: string, days: number = 30) => {
  try {
    const response = await fetch(`${config.urls.backend}/api/mcp/historical/${symbol}?days=${days}`);

    if (!response.ok) {
      throw new Error(`Error al obtener datos históricos del MCP: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error en el servicio de datos históricos del MCP:', error);
    throw error;
  }
};

/**
 * Obtiene datos históricos de una criptomoneda con fallback
 * Intenta primero obtener datos del MCP, si falla, usa la API directa
 * @param symbol Símbolo de la criptomoneda (ej. BTC, ETH)
 * @param days Número de días de datos históricos (por defecto 30)
 * @returns Datos históricos de la criptomoneda
 */
export const getHistoricalDataWithFallback = async (symbol: string, days: number = 30) => {
  try {
    // Intenta obtener datos del MCP primero
    return await getHistoricalDataFromMCP(symbol, days);
  } catch (error) {
    console.log('Fallback a API directa para datos históricos');
    // Si falla, usa la API directa
    return await getHistoricalData(symbol, days);
  }
};

/**
 * Formatea los datos históricos de precios al formato requerido
 * @param prices Array de precios en formato [timestamp, price]
 * @returns Objeto con fechas como claves y precios como valores
 */
const formatHistoricalData = (prices: [number, number][]): { [date: string]: number } => {
  const formattedData: { [date: string]: number } = {};

  prices.forEach(([timestamp, price]) => {
    const date = new Date(timestamp);
    const dateStr = date.toISOString().split('T')[0]; // Formato YYYY-MM-DD
    formattedData[dateStr] = price;
  });

  return formattedData;
};

/**
 * Obtiene los datos históricos de valor para un portafolio
 * @param portfolioAssets Activos del portafolio
 * @param days Número de días para obtener datos históricos
 * @returns Array de objetos con fecha y valor del portafolio
 */
export const getHistoricalPortfolioValue = async (
  portfolioAssets: PortfolioAsset[],
  days: number = 30
): Promise<{ date: string; value: number }[]> => {
  try {
    if (!portfolioAssets || portfolioAssets.length === 0) {
      return [];
    }

    // Obtener datos históricos para cada activo
    const historicalDataPromises = portfolioAssets.map(asset =>
      getHistoricalDataWithFallback(asset.id, days)
    );

    const historicalDataResults = await Promise.all(historicalDataPromises);

    // Crear un mapa de fechas a valores del portafolio
    const dateValueMap: { [date: string]: number } = {};

    // Para cada activo, procesar sus datos históricos
    portfolioAssets.forEach((asset, index) => {
      const historicalData = historicalDataResults[index];

      if (historicalData && historicalData.prices) {
        // Procesar cada punto de datos históricos
        historicalData.prices.forEach(([timestamp, price]: [number, number]) => {
          const date = new Date(timestamp);
          const dateStr = date.toISOString().split('T')[0]; // Formato YYYY-MM-DD

          // Inicializar el valor para esta fecha si no existe
          if (!dateValueMap[dateStr]) {
            dateValueMap[dateStr] = 0;
          }

          // Añadir el valor de este activo para esta fecha
          dateValueMap[dateStr] += asset.amount * price;
        });
      }
    });

    // Convertir el mapa a un array de objetos
    const portfolioValues = Object.entries(dateValueMap).map(([date, value]) => ({
      date,
      value
    }));

    // Ordenar por fecha
    return portfolioValues.sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );
  } catch (error) {
    console.error('Error al calcular el valor histórico del portafolio:', error);
    return [];
  }
};
