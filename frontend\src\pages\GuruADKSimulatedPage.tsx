import React from 'react';
import { <PERSON> } from 'react-router-dom';
import Guru<PERSON>KSimulated from '../components/GuruADK/GuruADKSimulated';
import '../components/GuruADK/GuruADKInterface.css';

const GuruADKSimulatedPage: React.FC = () => {
  return (
    <div className="standalone-page">
      <header className="standalone-header">
        <Link to="/" className="back-link">
          ← Volver al Dashboard
        </Link>
        <h1><PERSON> ADK (Simulated)</h1>
        <div className="header-subtitle">
          Powered by OpenRouter API with Claude and Gemini models
        </div>
      </header>
      <main className="standalone-content">
        <GuruADKSimulated />
      </main>
    </div>
  );
};

export default GuruADKSimulatedPage;
