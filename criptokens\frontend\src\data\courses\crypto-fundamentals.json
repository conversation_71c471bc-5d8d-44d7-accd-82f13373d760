{"id": "crypto-fundamentals", "title": "Fundamentos de Criptomonedas", "description": "Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.", "level": "beginner", "duration": "4 horas", "modules": [{"id": "module-1", "title": "¿Qué son las Criptomonedas?", "description": "Introducción al concepto de criptomonedas y su lugar en el sistema financiero global.", "lessons": [{"id": "lesson-1-1", "title": "Definición y Características", "content": "Las criptomonedas son monedas digitales basadas en la tecnología Blockchain que no son emitidas ni controladas por bancos centrales ni entidades bancarias. Utilizan criptografía para asegurar las transacciones y proteger la propiedad. A diferencia del dinero tradicional, las criptomonedas operan en un sistema descentralizado.\n\nCaracterísticas principales:\n- Descentralización: No dependen de una autoridad central\n- Seguridad: Protegidas por criptografía avanzada\n- Transparencia: Todas las transacciones son públicas en la blockchain\n- Globalidad: Funcionan en cualquier parte del mundo\n- Irreversibilidad: Las transacciones no pueden ser canceladas una vez confirmadas", "videoUrl": "https://example.com/videos/crypto-definition", "duration": 15}, {"id": "lesson-1-2", "title": "Criptomonedas vs. Dinero Fiat", "content": "Aunque las criptomonedas y el dinero fiat (como el dólar o el euro) comparten algunas similitudes, existen diferencias fundamentales:\n\n1. Emisión y Control:\n   - Dinero Fiat: Emitido y controlado por bancos centrales y gobiernos\n   - Criptomonedas: Producidas a través de minería o mecanismos de consenso, sin control centralizado\n\n2. Base de Confianza:\n   - Dinero Fiat: La confianza se basa en la autoridad central que lo emite\n   - Criptomonedas: La confianza se basa en la tecnología blockchain y la criptografía\n\n3. Suministro:\n   - Dinero Fiat: Puede ser impreso ilimitadamente, causando inflación\n   - Bitcoin: Tiene un suministro fijo de 21 millones, haciéndolo más escaso que el oro\n\n4. Accesibilidad:\n   - Dinero Fiat: Requiere intermediarios como bancos para transacciones globales\n   - Criptomonedas: Permiten transacciones globales en cualquier momento sin necesidad de bancos", "videoUrl": "https://example.com/videos/crypto-vs-fiat", "duration": 20}, {"id": "lesson-1-3", "title": "Historia y Evolución", "content": "La historia de las criptomonedas comienza con Bitcoin, creado en 2008 por una persona o grupo bajo el seudónimo de <PERSON><PERSON>. En medio de la crisis financiera global, Nakamoto publicó el documento 'Bitcoin: un sistema de efectivo electrónico entre pares'.\n\nHitos importantes:\n\n- 2008: Publicación del whitepaper de Bitcoin\n- 2009: Primer bloque de Bitcoin (bloque génesis) minado\n- 2010: Primera transacción comercial con Bitcoin (compra de dos pizzas por 10,000 BTC)\n- 2011: Surgen las primeras altcoins como Litecoin y Namecoin\n- 2013: El precio de Bitcoin supera los $1,000 por primera vez\n- 2015: Lanzamiento de Ethereum, introduciendo los contratos inteligentes\n- 2017: Gran boom de las criptomonedas y las ICOs\n- 2020: Instituciones comienzan a adoptar Bitcoin como reserva de valor\n- 2021: El Salvador adopta Bitcoin como moneda de curso legal\n\nDesde su creación, las criptomonedas han evolucionado de ser un experimento tecnológico a convertirse en una clase de activo reconocida globalmente.", "videoUrl": "https://example.com/videos/crypto-history", "duration": 25}], "quiz": {"id": "quiz-module-1", "title": "Evaluación: Conceptos Básicos de Criptomonedas", "questions": [{"id": "q1", "question": "¿Qué tecnología fundamental utilizan las criptomonedas?", "options": ["Inteligencia Artificial", "Blockchain", "Cloud Computing", "Realidad Virtual"], "correctAnswer": 1}, {"id": "q2", "question": "¿Quién creó Bitcoin?", "options": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correctAnswer": 2}, {"id": "q3", "question": "¿Cuál de las siguientes NO es una característica de las criptomonedas?", "options": ["Descentralización", "Reversibilidad de transacciones", "Uso de criptografía", "Transparencia"], "correctAnswer": 1}, {"id": "q4", "question": "¿Cuál es el suministro máximo de Bitcoin?", "options": ["1 millón", "21 millones", "100 millones", "<PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 1}, {"id": "q5", "question": "¿Qué diferencia principal existe entre el dinero fiat y las criptomonedas?", "options": ["Las criptomonedas solo existen en forma física", "El dinero fiat no puede ser usado globalmente", "Las criptomonedas están controladas por bancos centrales", "El dinero fiat está emitido y controlado por autoridades centrales"], "correctAnswer": 3}]}}, {"id": "module-2", "title": "Blockchain: La Tecnología Detrás de las Criptomonedas", "description": "Comprende cómo funciona la tecnología blockchain y por qué es fundamental para las criptomonedas.", "lessons": [{"id": "lesson-2-1", "title": "¿Qué es Blockchain?", "content": "Blockchain es un libro mayor compartido e inmutable que facilita el proceso de registro de transacciones y seguimiento de activos en una red. Funciona como una base de datos distribuida que mantiene una lista creciente de registros llamados bloques, vinculados y asegurados mediante criptografía.\n\nCaracterísticas clave:\n\n- Inmutabilidad: Una vez que la información se registra, no puede ser alterada\n- Distribución: La información se almacena en múltiples nodos de la red\n- Consenso: Los participantes de la red deben acordar la validez de las transacciones\n- Transparencia: Todas las transacciones son visibles para todos los participantes\n\nLa blockchain elimina la necesidad de intermediarios en las transacciones, permitiendo que las partes interactúen directamente entre sí de manera segura y verificable.", "videoUrl": "https://example.com/videos/blockchain-basics", "duration": 20}, {"id": "lesson-2-2", "title": "Cómo Funciona la Blockchain", "content": "La blockchain funciona como una cadena de bloques donde cada bloque contiene un conjunto de transacciones. Cuando se realiza una nueva transacción, esta se agrupa con otras en un bloque que se añade a la cadena existente.\n\nProceso básico:\n\n1. Transacción: Un usuario inicia una transacción (por ejemplo, enviar Bitcoin)\n2. Verificación: La red de nodos verifica la transacción usando algoritmos conocidos\n3. Agrupación: La transacción verificada se combina con otras para formar un bloque\n4. Hashing: Se crea una huella digital única (hash) para el bloque\n5. Adición a la cadena: El bloque se añade a la cadena existente, creando un registro permanente\n6. Confirmación: La transacción se completa\n\nCada bloque contiene el hash del bloque anterior, creando una cadena inmutable. Si alguien intenta modificar un bloque, el hash cambiaría, rompiendo la cadena y siendo rechazado por la red.", "videoUrl": "https://example.com/videos/blockchain-process", "duration": 25}, {"id": "lesson-2-3", "title": "Mecanismos de Consenso", "content": "Los mecanismos de consenso son protocolos que aseguran que todos los nodos de la red blockchain estén sincronizados y acuerden la validez de las transacciones. Son fundamentales para mantener la seguridad y la integridad de la red sin una autoridad central.\n\nPrincipales mecanismos de consenso:\n\n1. <PERSON><PERSON><PERSON>rabajo (PoW):\n   - Utilizado por Bitcoin y otras criptomonedas\n   - Los mineros compiten para resolver complejos problemas matemáticos\n   - Alto consumo energético pero muy seguro\n\n2. Prueba de Participación (PoS):\n   - Los validadores son seleccionados según la cantidad de criptomonedas que poseen\n   - Más eficiente energéticamente que PoW\n   - Utilizado por Ethereum 2.0, Cardano, y otros\n\n3. Prueba de Participación Delegada (DPoS):\n   - Los usuarios votan por delegados que validan transacciones\n   - Más rápido y escalable\n   - Utilizado por EOS, TRON, etc.\n\n4. Prueba de Autoridad (PoA):\n   - Los validadores son nodos aprobados con identidades conocidas\n   - Ideal para blockchains privadas o de consorcio\n   - Más centralizado pero muy eficiente\n\nCada mecanismo tiene sus ventajas y desventajas en términos de seguridad, descentralización, escalabilidad y eficiencia energética.", "videoUrl": "https://example.com/videos/consensus-mechanisms", "duration": 30}, {"id": "lesson-2-4", "title": "Tipos de Blockchain", "content": "Existen diferentes tipos de blockchain según su accesibilidad, permisos y casos de uso:\n\n1. Blockchain Públicas:\n   - Abiertas a cualquier persona para participar\n   - Totalmente descentralizadas\n   - Ejemplos: Bitcoin, Ethereum\n   - Características: Alta seguridad, transparencia total, menor velocidad\n\n2. Blockchain Privadas:\n   - Acceso restringido a participantes específicos\n   - Mayor control y privacidad\n   - Ejemplos: Hyperledge<PERSON> <PERSON>ab<PERSON>, Corda\n   - Características: Mayor velocidad, menor descentralización\n\n3. Blockchain de Consorcio:\n   - Controladas por un grupo de organizaciones\n   - Balance entre públicas y privadas\n   - Ejemplos: Energy Web Chain, Quorum\n   - Características: Eficiencia, parcialmente descentralizadas\n\n4. Blockchain Híbridas:\n   - Combinan elementos de blockchains públicas y privadas\n   - Permiten transacciones privadas en una red pública\n   - Ejemplos: Dragonchain, XinFin\n   - Características: Flexibilidad, personalización\n\nLa elección del tipo de blockchain depende de factores como el caso de uso, requisitos de privacidad, escalabilidad y nivel de descentralización deseado.", "videoUrl": "https://example.com/videos/blockchain-types", "duration": 20}], "quiz": {"id": "quiz-module-2", "title": "Evaluación: Blockchain", "questions": [{"id": "q1-m2", "question": "¿Qué es un bloque en una blockchain?", "options": ["Un dispositivo físico para almacenar criptomonedas", "Un conjunto de transacciones agrupadas", "Una clave privada", "Un tipo de wallet"], "correctAnswer": 1}, {"id": "q2-m2", "question": "¿Qué mecanismo de consenso utiliza Bitcoin?", "options": ["Prueba de Participación (PoS)", "Prueba de Autoridad (PoA)", "Prueba de Trabajo (PoW)", "Prueba de Participación Delegada (DPoS)"], "correctAnswer": 2}, {"id": "q3-m2", "question": "¿Cuál de las siguientes NO es una característica de la blockchain?", "options": ["Inmutabilidad", "Centralización", "Transparencia", "Distribución"], "correctAnswer": 1}, {"id": "q4-m2", "question": "¿Qué tipo de blockchain ofrece mayor privacidad y control?", "options": ["Blockchain pública", "Blockchain privada", "Sidechain", "Layer 2"], "correctAnswer": 1}, {"id": "q5-m2", "question": "¿Qué función cumple el hash en una blockchain?", "options": ["Encripta las claves privadas", "Crea una huella digital única para cada bloque", "Acelera las transacciones", "Reduce las comisiones"], "correctAnswer": 1}]}}, {"id": "module-3", "title": "Bitcoin: La Primera Criptomoneda", "description": "Explora en detalle Bitcoin, su funcionamiento, minería y papel en el ecosistema cripto.", "lessons": [{"id": "lesson-3-1", "title": "Origen e Historia de Bitcoin", "content": "Bitcoin fue creado en 2008 por una persona o grupo bajo el seudónimo de <PERSON><PERSON>, quien publicó el whitepaper 'Bitcoin: Un Sistema de Efectivo Electrónico Peer-to-Peer' en respuesta a la crisis financiera global.\n\nCronología clave:\n\n- 31 de octubre de 2008: Publicación del whitepaper de Bitcoin\n- 3 de enero de 2009: Generación del bloque génesis con un mensaje incrustado: 'The Times 03/Jan/2009 Chancellor on brink of second bailout for banks'\n- 12 de enero de 2009: Primera transacción de Bitcoin entre Nakamoto y Hal Finney\n- 22 de mayo de 2010: Primera compra con Bitcoin (dos pizzas por 10,000 BTC)\n- 2011: Bitcoin alcanza la paridad con el dólar estadounidense\n- 2013: El precio de Bitcoin supera los $1,000 por primera vez\n- 2017: Gran boom de Bitcoin, alcanzando casi $20,000\n- 2021: Bitcoin supera los $60,000 y es adoptado como moneda de curso legal en El Salvador\n\nLa identidad de <PERSON><PERSON> sigue siendo un misterio, y se retiró del proyecto en 2010, dejando el desarrollo en manos de la comunidad.", "videoUrl": "https://example.com/videos/bitcoin-history", "duration": 20}, {"id": "lesson-3-2", "title": "Características y Funcionamiento de Bitcoin", "content": "Bitcoin es una moneda digital descentralizada que opera en una red peer-to-peer sin necesidad de intermediarios. Sus características principales incluyen:\n\n1. Su<PERSON>stro Limitado: Solo existirán 21 millones de bitcoins, lo que lo convierte en un activo escaso similar al oro.\n\n2. Descentralización: No está controlado por ningún gobierno, banco o entidad.\n\n3. Pseudoanonimidad: Las transacciones son públicas, pero las identidades están ocultas tras direcciones.\n\n4. Divisibilidad: Cada bitcoin puede dividirse hasta 8 decimales (0.00000001 BTC = 1 satoshi).\n\n5. Portabilidad: Puede enviarse instantáneamente a cualquier parte del mundo.\n\n6. Inmutabilidad: Las transacciones confirmadas no pueden revertirse.\n\nFuncionamiento básico:\n- Las transacciones son agrupadas en bloques\n- Los mineros compiten para resolver un problema matemático (prueba de trabajo)\n- El primer minero en resolver el problema añade el bloque a la blockchain\n- El minero recibe una recompensa en bitcoins nuevos y comisiones\n- La red verifica y confirma la validez del bloque\n\nEste sistema asegura que la red Bitcoin sea segura y resistente a ataques.", "videoUrl": "https://example.com/videos/bitcoin-characteristics", "duration": 25}, {"id": "lesson-3-3", "title": "Minería de Bitcoin", "content": "La minería de Bitcoin es el proceso por el cual se verifican las transacciones y se añaden nuevos bloques a la blockchain. También es el mecanismo por el cual se crean nuevos bitcoins.\n\nProceso de minería:\n\n1. Recopilación de transacciones: Los mineros agrupan transacciones pendientes en un bloque candidato.\n\n2. Prueba de trabajo: Los mineros compiten para encontrar un valor (nonce) que, cuando se añade al bloque y se pasa por una función hash SHA-256, produce un resultado que comienza con un número específico de ceros.\n\n3. Verificación: Cuando un minero encuentra la solución, la transmite a la red para que otros nodos la verifiquen.\n\n4. Recompensa: El minero exitoso recibe una recompensa en bitcoins recién creados (actualmente 6.25 BTC por bloque) más las comisiones de transacción.\n\n5. Ajuste de dificultad: La dificultad del problema matemático se ajusta cada 2016 bloques (aproximadamente 2 semanas) para mantener un tiempo promedio de 10 minutos entre bloques.\n\nEquipamiento de minería:\n- CPU (obsoleto para minería de Bitcoin)\n- GPU (tarjetas gráficas, también mayormente obsoletas)\n- FPGA (circuitos integrados programables)\n- ASIC (circuitos integrados específicos para minería, los más eficientes)\n\nPools de minería: Grupos de mineros que combinan su poder de cómputo y comparten las recompensas proporcionalmente, aumentando la probabilidad de recibir pagos regulares.", "videoUrl": "https://example.com/videos/bitcoin-mining", "duration": 30}, {"id": "lesson-3-4", "title": "Halving y Economía de Bitcoin", "content": "El halving de Bitcoin es un evento programado que reduce a la mitad la recompensa que reciben los mineros por verificar transacciones. Ocurre aproximadamente cada cuatro años (o cada 210,000 bloques).\n\nHistoria de halvings:\n- 2012: Primera reducción, de 50 a 25 BTC por bloque\n- 2016: Segunda reducción, de 25 a 12.5 BTC por bloque\n- 2020: Tercera reducción, de 12.5 a 6.25 BTC por bloque\n- 2024 (estimado): Cuarta reducción, de 6.25 a 3.125 BTC por bloque\n\nImplicaciones económicas:\n\n1. Escasez programada: El halving reduce la tasa de emisión de nuevos bitcoins, aumentando su escasez.\n\n2. Modelo Stock-to-Flow: Este modelo sugiere que el valor de Bitcoin aumenta después de cada halving debido a la reducción de la oferta nueva frente a la demanda existente.\n\n3. Sostenibilidad de la minería: A medida que disminuye la recompensa por bloque, los mineros dependerán más de las comisiones de transacción.\n\n4. Deflación: A diferencia de las monedas fiduciarias que tienden a la inflación, Bitcoin es deflacionario por diseño, lo que significa que su poder adquisitivo tiende a aumentar con el tiempo.\n\nSe estima que el último bitcoin será minado alrededor del año 2140, después de lo cual los mineros solo recibirán comisiones por transacción como incentivo para mantener la red.", "videoUrl": "https://example.com/videos/bitcoin-halving", "duration": 20}, {"id": "lesson-3-5", "title": "Bitcoin como Reserva de Valor", "content": "Bitcoin ha evolucionado desde ser principalmente un medio de pago a ser considerado una reserva de valor digital, a menudo comparado con el oro.\n\nCaracterísticas que hacen de Bitcoin una reserva de valor:\n\n1. Esca<PERSON>z: Suministro máximo limitado a 21 millones de unidades.\n\n2. Durabilidad: Al ser digital, no se degrada con el tiempo.\n\n3. Fungibilidad: Cada bitcoin es igual a otro bitcoin.\n\n4. Portabilidad: Puede transferirse instantáneamente a cualquier parte del mundo.\n\n5. Divisibilidad: Puede dividirse hasta 8 decimales (1 satoshi = 0.00000001 BTC).\n\n6. Verificabilidad: Fácil de verificar su autenticidad mediante criptografía.\n\n7. Resistencia a la censura: No puede ser confiscado o congelado por gobiernos.\n\nBitcoin vs. Oro:\n- Ambos tienen suministro limitado y son escasos\n- Bitcoin es más fácil de transportar y dividir\n- El oro tiene miles de años de historia como reserva de valor\n- Bitcoin es más volátil pero potencialmente ofrece mayor rendimiento\n\nAdopción institucional:\nEmpresas como MicroStrategy, Tesla y Square han añadido Bitcoin a sus balances como reserva de valor y cobertura contra la inflación. Fondos de inversión y gestores de patrimonio también están comenzando a incluir Bitcoin en sus carteras.", "videoUrl": "https://example.com/videos/bitcoin-store-of-value", "duration": 25}], "quiz": {"id": "quiz-module-3", "title": "Evaluación: Bitcoin", "questions": [{"id": "q1-m3", "question": "¿Quién creó Bitcoin?", "options": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correctAnswer": 1}, {"id": "q2-m3", "question": "¿Cuál es el suministro máximo de Bitcoin?", "options": ["1 millón", "21 millones", "100 millones", "<PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 1}, {"id": "q3-m3", "question": "¿Qué ocurre en un 'halving' de Bitcoin?", "options": ["El precio de Bitcoin se divide por la mitad", "La recompensa por bloque se reduce a la mitad", "El tiempo de confirmación de bloques se duplica", "La dificultad de minería se reduce a la mitad"], "correctAnswer": 1}, {"id": "q4-m3", "question": "¿Cuál fue la primera compra realizada con Bitcoin?", "options": ["Un coche", "Dos pizzas", "Una computadora", "Oro"], "correctAnswer": 1}, {"id": "q5-m3", "question": "¿Cuál es la unidad más pequeña de Bitcoin?", "options": ["Bit", "<PERSON><PERSON>", "<PERSON>", "Gwei"], "correctAnswer": 1}]}}, {"id": "module-4", "title": "Ethereum y Contratos Inteligentes", "description": "Descubre Ethereum, la segunda criptomoneda más grande, y el concepto revolucionario de los contratos inteligentes.", "lessons": [{"id": "lesson-4-1", "title": "Introducción a Ethereum", "content": "Ethereum es una plataforma blockchain de código abierto que permite la creación y ejecución de contratos inteligentes y aplicaciones descentralizadas (dApps) sin tiempo de inactividad, fraude, control o interferencia de terceros.\n\nCreada por Vitalik Buterin en 2013 y lanzada en 2015, Ethereum va más allá de ser una simple criptomoneda como Bitcoin. Es una plataforma completa que permite a los desarrolladores crear aplicaciones descentralizadas utilizando su lenguaje de programación Solidity.\n\nCaracterísticas principales de Ethereum:\n\n1. Turing completo: A diferencia de Bitcoin, Ethereum puede ejecutar cualquier programa computacional.\n\n2. Ether (ETH): La criptomoneda nativa que alimenta la red Ethereum.\n\n3. Gas: Mecanismo de tarificación utilizado para asignar recursos en la red.\n\n4. Máquina Virtual de Ethereum (EVM): Entorno de ejecución para contratos inteligentes.\n\n5. Prueba de Trabajo (PoW) → Prueba de Participación (PoS): Ethereum está en proceso de transición de PoW a PoS con Ethereum 2.0.\n\nEthereum ha sido fundamental en el desarrollo del ecosistema cripto, impulsando innovaciones como ICOs, DeFi, NFTs y DAOs.", "videoUrl": "https://example.com/videos/ethereum-intro", "duration": 25}, {"id": "lesson-4-2", "title": "Contratos Inteligentes: Fundamentos", "content": "Los contratos inteligentes son programas autoejectuables que se ejecutan exactamente como se programaron, sin posibilidad de censura, tiempo de inactividad, fraude o interferencia de terceros.\n\nDefinición y características:\n\n- Programas almacenados en una blockchain que se ejecutan cuando se cumplen condiciones predeterminadas\n- Automatizan acuerdos sin necesidad de intermediarios\n- Son inmutables una vez desplegados en la blockchain\n- Transparentes y verificables por todos los participantes de la red\n- Reducen costos de transacción y aumentan la eficiencia\n\nFuncionamiento básico:\n1. Programación: Se escribe el contrato en un lenguaje como Solidity\n2. Compilación: Se convierte a bytecode para la EVM\n3. Despliegue: Se publica en la blockchain de Ethereum\n4. Ejecución: Se activa cuando se cumplen las condiciones especificadas\n\nEjemplo simple de contrato inteligente:\n```solidity\npragma solidity ^0.8.0;\n\ncontract SimpleStorage {\n    uint storedData;\n    \n    function set(uint x) public {\n        storedData = x;\n    }\n    \n    function get() public view returns (uint) {\n        return storedData;\n    }\n}\n```\n\nEste contrato permite almacenar un número entero y recuperarlo posteriormente.", "videoUrl": "https://example.com/videos/smart-contracts-basics", "duration": 30}, {"id": "lesson-4-3", "title": "Aplicaciones Descentralizadas (dApps)", "content": "Las aplicaciones descentralizadas (dApps) son aplicaciones digitales que se ejecutan en una red blockchain o P2P de computadoras en lugar de una sola computadora, están fuera del control de una única autoridad.\n\nCaracterísticas de las dApps:\n\n1. Descentralización: Operan en una red blockchain, no en servidores centralizados.\n\n2. Código abierto: El código fuente está disponible para todos.\n\n3. Incentivos: Utilizan tokens criptográficos para incentivar a los validadores de la red.\n\n4. Consenso: Utilizan un algoritmo de consenso para validar transacciones.\n\nArquitectura típica de una dApp:\n- Frontend: Interfaz de usuario (HTML, CSS, JavaScript)\n- Backend: Contratos inteligentes en la blockchain\n- Almacenamiento descentralizado: IPFS, Swarm, etc.\n- Comunicación: Web3.js o ethers.js para interactuar con la blockchain\n\nCategorías principales de dApps:\n\n1. Finanzas (DeFi): Préstamos, intercambios, derivados, etc.\n   Ejemplos: Uniswap, Aave, Compound\n\n2. Juegos y coleccionables: Juegos basados en blockchain y NFTs\n   Ejemplos: Axie Infinity, CryptoKitties\n\n3. Identidad y reputación: Sistemas de identidad descentralizados\n   Ejemplos: uPort, Civic\n\n4. Gobernanza: Organizaciones Autónomas Descentralizadas (DAOs)\n   Ejemplos: MakerDAO, Aragon\n\nLas dApps representan un cambio de paradigma en cómo se construyen y utilizan las aplicaciones, eliminando intermediarios y dando a los usuarios mayor control sobre sus datos y activos.", "videoUrl": "https://example.com/videos/dapps-overview", "duration": 25}, {"id": "lesson-4-4", "title": "Tokens ERC-20 y Estándares de Ethereum", "content": "Los estándares de tokens en Ethereum son conjuntos de reglas que los contratos inteligentes deben seguir para ser compatibles con el ecosistema más amplio. El más conocido es el ERC-20, pero existen otros estándares importantes.\n\nERC-20 (Ethereum Request for Comment 20):\n- Estándar para tokens fungibles (intercambiables)\n- Define funciones básicas como transferencia, aprobación, saldo, etc.\n- Permite la interoperabilidad entre diferentes plataformas y exchanges\n- Utilizado por miles de proyectos para crear sus propios tokens\n\nFunciones básicas de ERC-20:\n```solidity\nfunction name() public view returns (string)\nfunction symbol() public view returns (string)\nfunction decimals() public view returns (uint8)\nfunction totalSupply() public view returns (uint256)\nfunction balanceOf(address _owner) public view returns (uint256)\nfunction transfer(address _to, uint256 _value) public returns (bool)\nfunction transferFrom(address _from, address _to, uint256 _value) public returns (bool)\nfunction approve(address _spender, uint256 _value) public returns (bool)\nfunction allowance(address _owner, address _spender) public view returns (uint256)\n```\n\nOtros estándares importantes:\n\n1. ERC-721: Estándar para tokens no fungibles (NFTs)\n   - Cada token es único y no intercambiable\n   - Utilizado para coleccionables, arte digital, propiedades virtuales\n\n2. ERC-1155: Estándar multi-token\n   - Permite manejar tanto tokens fungibles como no fungibles en un solo contrato\n   - Más eficiente en gas que usar múltiples contratos ERC-20 o ERC-721\n\n3. ERC-777: Versión mejorada de ERC-20\n   - Añade hooks para notificar a los contratos cuando reciben tokens\n   - Compatible con ERC-20\n\nEstos estándares han facilitado la creación de un ecosistema vibrante de tokens y aplicaciones en Ethereum.", "videoUrl": "https://example.com/videos/ethereum-token-standards", "duration": 25}, {"id": "lesson-4-5", "title": "Ethereum 2.0 y el Futuro", "content": "Ethereum 2.0 (ahora conocido como 'The Merge' y actualizaciones posteriores) representa una serie de actualizaciones significativas a la red Ethereum para hacerla más escalable, segura y sostenible.\n\nPrincipales cambios en Ethereum 2.0:\n\n1. Transición de Prueba de Trabajo (PoW) a Prueba de Participación (PoS):\n   - Reduce el consumo de energía en más del 99%\n   - Los validadores aseguran la red apostando ETH en lugar de resolver problemas computacionales\n   - Se requieren 32 ETH para convertirse en validador\n\n2. Fragmentación (Sharding):\n   - División de la red en múltiples fragmentos para procesar transacciones en paralelo\n   - Aumenta significativamente la capacidad de transacciones por segundo\n   - Reduce las tarifas de gas al aumentar el suministro de capacidad de procesamiento\n\n3. Capa 2 (Layer 2):\n   - Soluciones de escalabilidad que operan encima de la blockchain principal\n   - Rollups (Optimistic y ZK), canales de estado, Plasma\n   - Procesan transacciones fuera de la cadena principal, reduciendo la congestión\n\n4. EIP-1559:\n   - Mecanismo de tarificación de gas que quema parte de las tarifas\n   - Hace que ETH sea potencialmente deflacionario\n   - Mejora la experiencia del usuario con tarifas más predecibles\n\nImplicaciones para el futuro:\n- Mayor adopción debido a menores costos y mayor velocidad\n- Posible consolidación de Ethereum como la plataforma dominante para contratos inteligentes\n- Desarrollo de nuevos casos de uso anteriormente limitados por la escalabilidad\n- Posible impacto positivo en el precio de ETH debido a la reducción de la oferta\n\nEstas mejoras posicionan a Ethereum para competir efectivamente con blockchains alternativas de contratos inteligentes y potencialmente convertirse en la 'capa de liquidación' global para activos digitales.", "videoUrl": "https://example.com/videos/ethereum-2-future", "duration": 30}], "quiz": {"id": "quiz-module-4", "title": "Evaluación: Ethereum y Contratos Inteligentes", "questions": [{"id": "q1-m4", "question": "¿Quién creó Ethereum?", "options": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "correctAnswer": 1}, {"id": "q2-m4", "question": "¿Qué son los contratos inteligentes?", "options": ["Contratos legales firmados digitalmente", "Programas que se ejecutan automáticamente cuando se cumplen condiciones predeterminadas", "Acuerdos entre mineros de Ethereum", "Documentos almacenados en IPFS"], "correctAnswer": 1}, {"id": "q3-m4", "question": "¿Qué estándar de token se utiliza para NFTs en Ethereum?", "options": ["ERC-20", "ERC-721", "ERC-777", "ERC-1155"], "correctAnswer": 1}, {"id": "q4-m4", "question": "¿Qué cambio principal introduce Ethereum 2.0?", "options": ["Cambio de nombre a Etheria", "Transición de Prueba de Trabajo a Prueba de Participación", "Eliminación de contratos inteligentes", "Reducción del suministro total de ETH"], "correctAnswer": 1}, {"id": "q5-m4", "question": "¿Qué lenguaje de programación se utiliza principalmente para escribir contratos inteligentes en Ethereum?", "options": ["JavaScript", "Python", "Solidity", "C++"], "correctAnswer": 2}]}}, {"id": "module-5", "title": "Altcoins y Tokens", "description": "Conoce las alternativas a Bitcoin y los diferentes tipos de tokens en el ecosistema cripto.", "lessons": []}, {"id": "module-6", "title": "Wallets y Seguridad", "description": "Aprende a almacenar tus criptomonedas de forma segura y a proteger tus activos digitales.", "lessons": []}, {"id": "module-7", "title": "Usos Prácticos de las Criptomonedas", "description": "Explora los casos de uso reales de las criptomonedas en el mundo actual.", "lessons": []}], "resources": [{"id": "resource-1", "title": "Whitepaper de Bitcoin", "type": "document", "url": "https://bitcoin.org/bitcoin.pdf"}, {"id": "resource-2", "title": "Glosario de Términos Cripto", "type": "glossary", "url": "/academy/glossary"}, {"id": "resource-3", "title": "Herramientas de Seguridad para Criptomonedas", "type": "tools", "url": "/academy/security-tools"}], "instructor": {"name": "<PERSON>", "bio": "Experto en criptomonedas con más de 8 años de experiencia en el sector. Ha trabajado como consultor para varias empresas blockchain y es autor del libro 'Criptomonedas: El Futuro del Dinero'.", "avatar": "/images/instructors/alex-rodriguez.jpg"}, "reviews": [{"id": "review-1", "user": "<PERSON>", "rating": 5, "comment": "Excelente curso para principiantes. Explica conceptos complejos de manera sencilla y práctica."}, {"id": "review-2", "user": "<PERSON>", "rating": 4, "comment": "<PERSON><PERSON> buen contenido, aunque me hubiera gustado más ejemplos prácticos."}]}