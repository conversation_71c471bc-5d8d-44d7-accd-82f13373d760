const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const bodyParser = require('body-parser');
const guruRoutes = require('./routes/guru.routes');
const technicalRoutes = require('./routes/technical.routes');
const cryptoRoutes = require('./routes/crypto.routes');
const fundamentalRoutes = require('./routes/fundamental.routes');
const enhancedNewsRoutes = require('./routes/enhancedNews.routes');
const ultravoxRoutes = require('./routes/ultravox.routes');
const conversationRoutes = require('./routes/conversation.routes');
require('dotenv').config();
// Importar la configuración directamente
const config = {
  cors: {
    origins: [
      'http://localhost:5173', // Frontend
      'http://localhost:3002', // Backend (actualizado)
      'http://localhost:3001', // Backend (anterior)
      'http://localhost:3000'  // Desarrollo alternativo
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  }
};

const app = express();
const PORT = 3002; // Puerto fijo para evitar conflictos

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174'], // Orígenes específicos
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // Caché de preflight por 24 horas
}));
app.use(bodyParser.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Rutas
app.use('/api/guru', guruRoutes);
app.use('/api/technical', technicalRoutes);
app.use('/api/crypto', cryptoRoutes);
app.use('/api/fundamental', fundamentalRoutes);
app.use('/api/enhanced-news', enhancedNewsRoutes);
app.use('/api/ultravox', ultravoxRoutes);
app.use('/api/conversations', conversationRoutes);

// Ruta de prueba
app.get('/', (req, res) => {
  res.json({ message: 'API de Criptokens funcionando correctamente en puerto 3002' });
});

// Manejador de errores
app.use((err, req, res, next) => {
  console.error('Error no controlado:', err);
  res.status(500).json({ error: 'Error interno del servidor', details: err.message });
});

// Crear servidor HTTP
const server = http.createServer(app);

// Configurar Socket.IO
const io = socketIo(server, {
  cors: {
    origin: ['http://localhost:5173', 'http://localhost:5174'],
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Manejar conexiones de Socket.IO
io.on('connection', (socket) => {
  console.log('Cliente conectado a Socket.IO:', socket.id);

  // Unirse a una sala específica para la llamada
  socket.on('join-call', (callId) => {
    socket.join(callId);
    console.log(`Cliente ${socket.id} unido a sala de llamada ${callId}`);
  });

  socket.on('disconnect', () => {
    console.log('Cliente desconectado de Socket.IO:', socket.id);
  });
});

// Hacer io disponible globalmente
global.io = io;

// Iniciar servidor
server.listen(PORT, () => {
  console.log(`Servidor backend ejecutándose en el puerto ${PORT}`);
});

module.exports = { app, io };
