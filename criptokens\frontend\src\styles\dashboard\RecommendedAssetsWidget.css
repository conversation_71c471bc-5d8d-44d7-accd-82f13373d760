.recommended-assets-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recommended-assets-widget:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.widget-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.ai-badge {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.widget-content {
  padding: 0.75rem;
  flex: 1;
  overflow-y: auto;
  max-height: 100%;
}

.assets-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.asset-card {
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.asset-card:hover {
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-sm);
}

.asset-summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
}

.asset-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  box-shadow: var(--shadow-xs);
}

.asset-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.asset-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.asset-name-container {
  display: flex;
  flex-direction: column;
}

.asset-name {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.asset-symbol {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.asset-price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.asset-price {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.asset-change {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.asset-change.positive {
  color: var(--color-positive);
}

.asset-change.negative {
  color: var(--color-negative);
}

.asset-score {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0.5rem;
}

.score-indicator {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: conic-gradient(var(--color-primary) 0%, transparent 0%);
  position: relative;
}

.score-indicator::before {
  content: '';
  position: absolute;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: var(--color-surface);
}

.score-indicator span {
  position: relative;
  z-index: 1;
  font-size: 0.9rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.asset-expand-icon {
  color: var(--text-tertiary);
  transition: transform 0.3s ease;
}

.asset-card.expanded .asset-expand-icon {
  transform: rotate(180deg);
}

.asset-details {
  padding: 0 0.75rem 0.75rem 0.75rem;
  border-top: 1px solid var(--border-color);
  margin-top: -1px;
}

.asset-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.metric-value {
  font-size: 0.85rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.metric-value.risk-low {
  color: var(--color-positive);
}

.metric-value.risk-medium {
  color: #f39c12;
}

.metric-value.risk-high {
  color: var(--color-negative);
}

.asset-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 0.75rem;
}

.asset-tag {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background-color: var(--color-surface-dark);
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
}

.asset-reason {
  margin-bottom: 0.75rem;
}

.asset-reason h5 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.asset-reason p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.asset-actions {
  display: flex;
  justify-content: flex-end;
}

.view-asset-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-asset-button:hover {
  background-color: var(--color-primary-light);
}

.empty-recommendations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-tertiary);
}

.empty-recommendations i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-recommendations p {
  margin: 0;
  font-size: 0.9rem;
  max-width: 80%;
}

.skeleton-loading {
  width: 100%;
  height: 150px;
  background: linear-gradient(90deg,
    var(--color-surface-dark) 25%,
    var(--color-surface) 50%,
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .asset-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .widget-header {
    padding: 0.75rem;
  }

  .widget-content {
    padding: 0.5rem;
  }

  .asset-summary {
    padding: 0.5rem;
  }

  .asset-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .asset-price-container {
    align-items: flex-start;
    margin-top: 0.25rem;
  }

  .asset-metrics {
    grid-template-columns: 1fr;
  }

  .asset-details {
    padding: 0 0.5rem 0.5rem 0.5rem;
  }
}
