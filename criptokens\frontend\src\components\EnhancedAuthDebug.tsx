import React, { useState, useEffect } from 'react';
import { auth, db } from '../firebase-init';
import { signInWithEmailAndPassword, createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import '../styles/EnhancedAuthDebug.css';

const EnhancedAuthDebug: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [action, setAction] = useState<'login' | 'register'>('login');
  const [authStatus, setAuthStatus] = useState<any>(null);

  // Verificar el estado actual de autenticación al cargar
  useEffect(() => {
    const checkAuthStatus = () => {
      const currentUser = auth.currentUser;
      setAuthStatus({
        isLoggedIn: !!currentUser,
        user: currentUser ? {
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName,
        } : null
      });
    };

    checkAuthStatus();
    
    // Escuchar cambios en el estado de autenticación
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setAuthStatus({
        isLoggedIn: !!user,
        user: user ? {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
        } : null
      });
    });

    return () => unsubscribe();
  }, []);

  const testLogin = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      // Intentar iniciar sesión
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      console.log('Inicio de sesión exitoso:', user);
      
      // Intentar obtener datos del usuario de Firestore
      try {
        const userDoc = await getDoc(doc(db, 'Users', user.uid));
        if (userDoc.exists()) {
          console.log('Datos del usuario encontrados:', userDoc.data());
          setResult({
            success: true,
            user: {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
            },
            userData: userDoc.data()
          });
        } else {
          console.log('No se encontraron datos del usuario en Firestore');
          setResult({
            success: true,
            user: {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
            },
            userData: null,
            message: 'No se encontraron datos del usuario en Firestore'
          });
        }
      } catch (firestoreError: any) {
        console.error('Error al obtener datos de Firestore:', firestoreError);
        setResult({
          success: true,
          user: {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
          },
          firestoreError: firestoreError.message
        });
      }
    } catch (error: any) {
      console.error('Error en la prueba de autenticación:', error);
      setResult({
        success: false,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const testRegister = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      // Crear usuario en Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Actualizar el perfil con el nombre de usuario
      await updateProfile(user, { displayName });
      
      console.log('Registro exitoso:', user);
      
      // Crear documento de usuario en Firestore
      const userData = {
        uid: user.uid,
        email: user.email,
        displayName: displayName,
        createdAt: new Date()
      };
      
      try {
        // Intentar crear el documento de usuario en Firestore
        await setDoc(doc(db, 'Users', user.uid), userData);
        
        // Intentar crear la colección de cartera vacía para el usuario
        await setDoc(doc(db, 'Portafolio', user.uid), {
          assets: [],
          lastUpdated: new Date()
        });
        
        setResult({
          success: true,
          user: {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
          },
          userData: userData,
          message: 'Usuario registrado correctamente y datos guardados en Firestore'
        });
      } catch (firestoreError: any) {
        console.error('Error al crear documentos en Firestore:', firestoreError);
        setResult({
          success: true,
          user: {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
          },
          firestoreError: firestoreError.message
        });
      }
    } catch (error: any) {
      console.error('Error al registrar usuario:', error);
      setResult({
        success: false,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="enhanced-auth-debug">
      <h3>Depuración de Autenticación</h3>
      
      <div className="auth-status">
        <h4>Estado actual:</h4>
        <div className={`status-indicator ${authStatus?.isLoggedIn ? 'logged-in' : 'logged-out'}`}>
          {authStatus?.isLoggedIn ? 'Conectado' : 'Desconectado'}
        </div>
        {authStatus?.user && (
          <div className="user-info">
            <p><strong>Usuario:</strong> {authStatus.user.displayName || 'Sin nombre'}</p>
            <p><strong>Email:</strong> {authStatus.user.email}</p>
            <p><strong>UID:</strong> {authStatus.user.uid}</p>
          </div>
        )}
      </div>
      
      <div className="auth-tabs">
        <button 
          className={`tab-button ${action === 'login' ? 'active' : ''}`}
          onClick={() => setAction('login')}
        >
          Iniciar Sesión
        </button>
        <button 
          className={`tab-button ${action === 'register' ? 'active' : ''}`}
          onClick={() => setAction('register')}
        >
          Registrarse
        </button>
      </div>
      
      <div className="auth-form">
        <div className="form-group">
          <label>Email:</label>
          <input 
            type="email" 
            value={email} 
            onChange={(e) => setEmail(e.target.value)} 
            placeholder="<EMAIL>"
          />
        </div>
        
        <div className="form-group">
          <label>Contraseña:</label>
          <input 
            type="password" 
            value={password} 
            onChange={(e) => setPassword(e.target.value)} 
            placeholder="********"
          />
        </div>
        
        {action === 'register' && (
          <div className="form-group">
            <label>Nombre:</label>
            <input 
              type="text" 
              value={displayName} 
              onChange={(e) => setDisplayName(e.target.value)} 
              placeholder="Tu nombre"
            />
          </div>
        )}
        
        <button 
          className="action-button"
          onClick={action === 'login' ? testLogin : testRegister} 
          disabled={loading}
        >
          {loading ? 'Procesando...' : action === 'login' ? 'Probar Inicio de Sesión' : 'Probar Registro'}
        </button>
      </div>
      
      {result && (
        <div className="result-container">
          <h4>Resultado:</h4>
          <div className={`result-status ${result.success ? 'success' : 'error'}`}>
            {result.success ? '✓ Éxito' : '✗ Error'}
          </div>
          <pre className="result-data">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default EnhancedAuthDebug;
