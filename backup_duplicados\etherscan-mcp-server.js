/**
 * Servidor MCP para datos on-chain de Ethereum
 *
 * Este servidor proporciona herramientas MCP para acceder a datos de la blockchain
 * de Ethereum utilizando la API de Etherscan.
 */

const express = require('express');
const cors = require('cors');
const etherscanService = require('./criptokens/backend/src/services/etherscan.service');
require('dotenv').config();

// Crear la aplicación Express
const app = express();

// Configurar CORS
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3001'],
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Configurar middleware para parsear JSON
app.use(express.json());

// Puerto para el servidor
const PORT = process.env.ETHERSCAN_MCP_PORT || 3104;

// Endpoint para listar herramientas disponibles
app.get('/tools', (req, res) => {
  const tools = [
    {
      name: 'getAddressBalance',
      description: 'Obtiene el balance de ETH de una dirección',
      parameters: {
        address: 'Dirección Ethereum a consultar'
      }
    },
    {
      name: 'getAddressTokens',
      description: 'Obtiene los tokens ERC20 de una dirección',
      parameters: {
        address: 'Dirección Ethereum a consultar',
        page: 'Número de página (opcional, por defecto 1)',
        offset: 'Resultados por página (opcional, por defecto 10)'
      }
    },
    {
      name: 'getTokenInfo',
      description: 'Obtiene información detallada de un token ERC20',
      parameters: {
        address: 'Dirección del contrato del token'
      }
    },
    {
      name: 'getEthPrice',
      description: 'Obtiene el precio actual de ETH en USD y BTC',
      parameters: {}
    },
    {
      name: 'getGasOracle',
      description: 'Obtiene información sobre los precios actuales de gas',
      parameters: {}
    },
    {
      name: 'analyzeAddress',
      description: 'Realiza un análisis completo de una dirección Ethereum',
      parameters: {
        address: 'Dirección Ethereum a analizar'
      }
    },
    {
      name: 'analyzeSmartContract',
      description: 'Analiza un contrato inteligente',
      parameters: {
        address: 'Dirección del contrato a analizar'
      }
    },
    {
      name: 'getDefiProtocols',
      description: 'Obtiene información sobre los principales protocolos DeFi',
      parameters: {}
    }
  ];

  res.json({ tools });
});

// Endpoint para obtener el balance de una dirección
app.post('/tools/getAddressBalance', async (req, res) => {
  try {
    const { address } = req.body;

    if (!address) {
      return res.status(400).json({
        error: 'Se requiere una dirección Ethereum'
      });
    }

    const balance = await etherscanService.getAddressBalance(address);
    const ethPrice = await etherscanService.getEthPrice();
    const balanceUSD = parseFloat(balance) * ethPrice.ethusd;

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            address,
            balance,
            balanceUSD,
            ethPrice: ethPrice.ethusd
          })
        }
      ]
    });
  } catch (error) {
    console.error('Error al obtener balance:', error);
    res.status(500).json({
      error: 'Error al obtener balance',
      details: error.message
    });
  }
});

// Endpoint para obtener tokens ERC20 de una dirección
app.post('/tools/getAddressTokens', async (req, res) => {
  try {
    const { address, page = 1, offset = 10 } = req.body;

    if (!address) {
      return res.status(400).json({
        error: 'Se requiere una dirección Ethereum'
      });
    }

    const tokens = await etherscanService.getAddressERC20Transfers(
      address,
      null,
      parseInt(page),
      parseInt(offset)
    );

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            address,
            tokens
          })
        }
      ]
    });
  } catch (error) {
    console.error('Error al obtener tokens:', error);
    res.status(500).json({
      error: 'Error al obtener tokens',
      details: error.message
    });
  }
});

// Endpoint para obtener información de un token
app.post('/tools/getTokenInfo', async (req, res) => {
  try {
    const { address } = req.body;

    if (!address) {
      return res.status(400).json({
        error: 'Se requiere una dirección de token'
      });
    }

    const info = await etherscanService.getTokenInfo(address);
    const supply = await etherscanService.getTokenSupply(address);

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            address,
            info,
            supply
          })
        }
      ]
    });
  } catch (error) {
    console.error('Error al obtener información del token:', error);
    res.status(500).json({
      error: 'Error al obtener información del token',
      details: error.message
    });
  }
});

// Endpoint para obtener el precio de ETH
app.post('/tools/getEthPrice', async (req, res) => {
  try {
    const price = await etherscanService.getEthPrice();
    const stats = await etherscanService.getEthStats();

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            price,
            stats
          })
        }
      ]
    });
  } catch (error) {
    console.error('Error al obtener precio de ETH:', error);
    res.status(500).json({
      error: 'Error al obtener precio de ETH',
      details: error.message
    });
  }
});

// Endpoint para obtener información de gas
app.post('/tools/getGasOracle', async (req, res) => {
  try {
    const gasInfo = await etherscanService.getGasOracle();

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(gasInfo)
        }
      ]
    });
  } catch (error) {
    console.error('Error al obtener información de gas:', error);
    res.status(500).json({
      error: 'Error al obtener información de gas',
      details: error.message
    });
  }
});

// Endpoint para analizar una dirección
app.post('/tools/analyzeAddress', async (req, res) => {
  try {
    const { address } = req.body;

    if (!address) {
      return res.status(400).json({
        error: 'Se requiere una dirección Ethereum'
      });
    }

    // Obtener balance de la dirección
    const balance = await etherscanService.getAddressBalance(address);

    // Obtener transacciones de la dirección
    const transactions = await etherscanService.getAddressTransactions(
      address,
      0,
      99999999,
      1,
      20
    );

    // Obtener tokens ERC20 de la dirección
    const tokens = await etherscanService.getAddressERC20Transfers(
      address,
      null,
      1,
      20
    );

    // Obtener precio actual de ETH
    const ethPrice = await etherscanService.getEthPrice();

    // Calcular valor en USD
    const balanceUSD = parseFloat(balance) * ethPrice.ethusd;

    const analysis = {
      address,
      balance,
      balanceUSD,
      ethPrice: ethPrice.ethusd,
      transactions: transactions.slice(0, 5), // Limitar a 5 transacciones para no sobrecargar la respuesta
      tokens: tokens.slice(0, 5), // Limitar a 5 tokens
      transactionCount: transactions.length,
      tokenCount: tokens.length
    };

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis)
        }
      ]
    });
  } catch (error) {
    console.error('Error al analizar dirección:', error);
    res.status(500).json({
      error: 'Error al analizar dirección',
      details: error.message
    });
  }
});

// Endpoint para analizar un contrato inteligente
app.post('/tools/analyzeSmartContract', async (req, res) => {
  try {
    const { address } = req.body;

    if (!address) {
      return res.status(400).json({
        error: 'Se requiere una dirección de contrato'
      });
    }

    // Obtener código fuente del contrato
    const source = await etherscanService.getContractSourceCode(address);

    // Obtener ABI del contrato
    const abi = await etherscanService.getContractABI(address);

    // Obtener transacciones del contrato
    const transactions = await etherscanService.getAddressTransactions(
      address,
      0,
      99999999,
      1,
      10
    );

    // Verificar si es un token ERC20
    let tokenInfo = null;
    try {
      tokenInfo = await etherscanService.getTokenInfo(address);
    } catch (error) {
      console.log(`La dirección ${address} no es un token ERC20`);
    }

    const analysis = {
      address,
      contractName: source[0]?.ContractName || 'Desconocido',
      isVerified: source[0]?.ABI !== 'Contract source code not verified',
      isToken: tokenInfo !== null,
      tokenInfo,
      transactionCount: transactions.length,
      recentTransactions: transactions.slice(0, 3) // Limitar a 3 transacciones
    };

    if (analysis.isVerified) {
      analysis.compiler = source[0]?.CompilerVersion;
      analysis.license = source[0]?.LicenseType;
    }

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis)
        }
      ]
    });
  } catch (error) {
    console.error('Error al analizar contrato:', error);
    res.status(500).json({
      error: 'Error al analizar contrato',
      details: error.message
    });
  }
});

// Endpoint para obtener protocolos DeFi
app.post('/tools/getDefiProtocols', async (req, res) => {
  try {
    const protocols = await etherscanService.getTopDefiProtocols();

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(protocols)
        }
      ]
    });
  } catch (error) {
    console.error('Error al obtener protocolos DeFi:', error);
    res.status(500).json({
      error: 'Error al obtener protocolos DeFi',
      details: error.message
    });
  }
});

// Iniciar el servidor
app.listen(PORT, () => {
  console.log(`Servidor MCP de Etherscan iniciado en http://localhost:${PORT}`);
  console.log(`Herramientas disponibles en http://localhost:${PORT}/tools`);
});
