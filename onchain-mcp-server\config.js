/**
 * Configuración para el OnChain MCP Server
 */
module.exports = {
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY || '**********************************',
    apiUrl: 'https://api.etherscan.io/api'
  },
  bscscan: {
    apiKey: process.env.BSCSCAN_API_KEY || '',
    apiUrl: 'https://api.bscscan.com/api'
  },
  infura: {
    apiKey: process.env.INFURA_API_KEY || '',
    apiUrl: 'https://mainnet.infura.io/v3/'
  },
  defaultChain: 'ethereum',
  port: process.env.PORT || 3104,
  
  // Direcciones de tokens populares en Ethereum
  tokenAddresses: {
    'bitcoin': '******************************************', // WBTC
    'ethereum': '******************************************', // WETH
    'binancecoin': '******************************************', // BNB
    'cardano': '******************************************', // ADA
    'solana': '******************************************', // SOL
    'ripple': '******************************************', // XRP
    'polkadot': '******************************************', // DOT
    'dogecoin': '******************************************', // DOGE
    'usd-coin': '******************************************', // USDC
    'tether': '******************************************' // USDT
  },
  
  // Direcciones de carteras populares para análisis
  popularWallets: {
    'binance': '******************************************',
    'coinbase': '******************************************',
    'grayscale': '******************************************',
    'microstrategy': '******************************************'
  }
};
