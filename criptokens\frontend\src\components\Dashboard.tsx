import { useState } from 'react';
import MarketSummary from './MarketSummary';
import CryptoTable from './CryptoTable';
import CryptoDetail from './CryptoDetail';
import ChatInterface from './ChatInterface';
import PortfolioFirebase from './PortfolioFirebase';
import NewsSection from './NewsSection';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCryptoId, setSelectedCryptoId] = useState<string | null>(null);

  return (
    <div className="dashboard">
      <div className="dashboard-tabs">
        <button
          className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}
          onClick={() => {
            setActiveTab('dashboard');
            setSelectedCryptoId(null);
          }}
        >
          Dashboard
        </button>
        <button
          className={`tab-button ${activeTab === 'portfolio' ? 'active' : ''}`}
          onClick={() => setActiveTab('portfolio')}
        >
          Mi Cartera
        </button>
        <button
          className={`tab-button ${activeTab === 'news' ? 'active' : ''}`}
          onClick={() => setActiveTab('news')}
        >
          Noticias
        </button>
        <button
          className={`tab-button ${activeTab === 'chat' ? 'active' : ''}`}
          onClick={() => setActiveTab('chat')}
        >
          Asistente IA
        </button>
      </div>

      <div className="dashboard-content">
        {activeTab === 'dashboard' && (
          selectedCryptoId ? (
            <CryptoDetail
              cryptoId={selectedCryptoId}
              onBack={() => setSelectedCryptoId(null)}
            />
          ) : (
            <>
              <MarketSummary />
              <div className="dashboard-main-content">
                <div className="main-section">
                  <CryptoTable onSelectCrypto={(id) => setSelectedCryptoId(id)} />
                </div>
                <div className="side-section">
                  <div className="chat-widget">
                    <h3>Asistente IA</h3>
                    <p>Pregunta sobre criptomonedas, blockchain o cualquier duda que tengas.</p>
                    <button
                      className="open-chat-button"
                      onClick={() => setActiveTab('chat')}
                    >
                      Abrir Chat
                    </button>
                  </div>
                </div>
              </div>
            </>
          )
        )}

        {activeTab === 'portfolio' && (
          <PortfolioFirebase />
        )}

        {activeTab === 'news' && (
          <NewsSection />
        )}

        {activeTab === 'chat' && (
          <div className="chat-fullscreen">
            <ChatInterface />
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
