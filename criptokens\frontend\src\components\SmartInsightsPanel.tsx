import React from 'react';
import '../styles/SmartInsightsPanel.css';

interface Insight {
  type: 'success' | 'warning' | 'info' | 'opportunity' | 'caution';
  title: string;
  description: string;
  action: string;
  actionPath: string;
}

interface SmartInsightsPanelProps {
  insights: Insight[];
  onActionClick: (path: string) => void;
}

const SmartInsightsPanel: React.FC<SmartInsightsPanelProps> = ({ insights, onActionClick }) => {
  if (insights.length === 0) {
    return (
      <div className="smart-insights-panel empty">
        <div className="panel-header">
          <h3>Insights Personalizados</h3>
          <div className="ai-badge">IA</div>
        </div>
        <div className="empty-insights">
          <i className="fas fa-lightbulb"></i>
          <p>No hay insights disponibles en este momento. Añade más activos a tu portafolio o radar para recibir recomendaciones personalizadas.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="smart-insights-panel">
      <div className="panel-header">
        <h3>Insights Personalizados</h3>
        <div className="ai-badge">IA</div>
      </div>
      <div className="insights-list">
        {insights.map((insight, index) => (
          <div key={index} className={`insight-card ${insight.type}`}>
            <div className="insight-icon">
              {insight.type === 'success' && <i className="fas fa-check-circle"></i>}
              {insight.type === 'warning' && <i className="fas fa-exclamation-triangle"></i>}
              {insight.type === 'info' && <i className="fas fa-info-circle"></i>}
              {insight.type === 'opportunity' && <i className="fas fa-chart-line"></i>}
              {insight.type === 'caution' && <i className="fas fa-shield-alt"></i>}
            </div>
            <div className="insight-content">
              <h4>{insight.title}</h4>
              <p>{insight.description}</p>
              <button 
                className="insight-action-button"
                onClick={() => onActionClick(insight.actionPath)}
              >
                {insight.action}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SmartInsightsPanel;
