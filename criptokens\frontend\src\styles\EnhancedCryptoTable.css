.enhanced-crypto-table-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.enhanced-crypto-table-container:hover {
  border-color: var(--border-color-hover);
}

.enhanced-crypto-table-container .table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: var(--border-width) solid var(--border-color);
  position: relative;
}

.enhanced-crypto-table-container .table-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: var(--color-primary);
}

.enhanced-crypto-table-container .table-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.enhanced-crypto-table-container .table-actions {
  display: flex;
  align-items: center;
}

.enhanced-crypto-table-container .crypto-count {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background-color: var(--color-surface-dark);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

.enhanced-crypto-table-container .table-wrapper {
  overflow-y: auto;
  flex: 1;
  height: 100%;
  /* Estilizar la barra de desplazamiento */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.enhanced-crypto-table-container .table-wrapper::-webkit-scrollbar {
  width: 6px;
}

.enhanced-crypto-table-container .table-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.enhanced-crypto-table-container .table-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.enhanced-crypto-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  color: rgba(255, 255, 255, 0.9);
}

.enhanced-crypto-table thead th {
  position: sticky;
  top: 0;
  background-color: var(--color-surface-dark);
  padding: var(--spacing-sm) var(--spacing-sm);
  text-align: left;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.2);
}

.enhanced-crypto-table thead th:hover {
  color: var(--text-primary);
  background-color: var(--color-surface-light);
}

.enhanced-crypto-table thead th.ascending::after {
  content: ' ↑';
  color: var(--color-primary);
}

.enhanced-crypto-table thead th.descending::after {
  content: ' ↓';
  color: var(--color-primary);
}

.enhanced-crypto-table tbody tr {
  border-bottom: var(--border-width) solid var(--border-color);
  cursor: pointer;
  transition: var(--transition-fast);
  display: table-row;
  vertical-align: middle;
}

.enhanced-crypto-table tbody tr:hover {
  background-color: var(--color-surface-light);
}

.enhanced-crypto-table tbody td {
  padding: var(--spacing-sm) var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  vertical-align: middle;
  height: 3em;
}

.enhanced-crypto-table .crypto-row {
  height: auto;
  min-height: 3em;
}

.enhanced-crypto-table .rank-cell {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  width: 40px;
}

.enhanced-crypto-table .name-cell {
  min-width: clamp(120px, 15vw, 180px);
}

.enhanced-crypto-table .crypto-info {
  display: flex;
  align-items: center;
  height: 100%;
}

.enhanced-crypto-table .crypto-icon {
  width: 1em !important;
  height: 1em !important;
  max-width: 16px !important;
  max-height: 16px !important;
  margin-right: 0.5em;
  border-radius: 50%;
  vertical-align: middle;
  object-fit: contain;
}

.enhanced-crypto-table .crypto-name-container {
  display: flex;
  flex-direction: column;
}

.enhanced-crypto-table .crypto-name {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.enhanced-crypto-table .crypto-symbol {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

.enhanced-crypto-table .price-cell {
  font-weight: var(--font-weight-semibold);
  min-width: clamp(80px, 10vw, 100px);
}

.enhanced-crypto-table .change-cell {
  font-weight: var(--font-weight-semibold);
  min-width: clamp(60px, 8vw, 80px);
}

.enhanced-crypto-table .change-cell.positive {
  color: var(--color-positive);
}

.enhanced-crypto-table .change-cell.negative {
  color: var(--color-negative);
}

.enhanced-crypto-table .market-cap-cell {
  min-width: clamp(90px, 12vw, 120px);
  color: var(--text-secondary);
}

.enhanced-crypto-table .sparkline-cell {
  min-width: clamp(80px, 10vw, 100px);
  padding: 0.25rem 0.5rem;
}

.enhanced-crypto-table .sparkline {
  display: block;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.enhanced-crypto-table .actions-cell {
  width: 70px;
  text-align: center;
}

.enhanced-crypto-table .watchlist-button {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.4rem;
  width: 34px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.enhanced-crypto-table .watchlist-button:hover {
  color: #ffc107;
  transform: scale(1.08);
  background: rgba(255, 193, 7, 0.08);
  border-color: rgba(255, 193, 7, 0.2);
  box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
}

.enhanced-crypto-table .watchlist-button.active {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.3);
  box-shadow: 0 1px 4px rgba(255, 193, 7, 0.25);
}

/* Estilos para el esqueleto de carga */
.enhanced-crypto-table .skeleton-row td {
  padding: 0.75rem 0.5rem;
}

.enhanced-crypto-table .skeleton-cell {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  height: 20px;
  animation: pulse 1.5s infinite;
}

.enhanced-crypto-table .skeleton-cell.rank {
  width: 20px;
}

.enhanced-crypto-table .skeleton-cell.name {
  width: 150px;
}

.enhanced-crypto-table .skeleton-cell.price,
.enhanced-crypto-table .skeleton-cell.change,
.enhanced-crypto-table .skeleton-cell.market-cap {
  width: 80px;
}

.enhanced-crypto-table .skeleton-cell.sparkline {
  width: 100px;
}

.enhanced-crypto-table .skeleton-cell.actions {
  width: 30px;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

@media (max-width: 768px) {
  .enhanced-crypto-table-container {
    padding: 0.5rem;
  }

  .enhanced-crypto-table thead th,
  .enhanced-crypto-table tbody td {
    padding: 0.375rem 0.25rem;
    font-size: 0.75rem;
  }

  .enhanced-crypto-table .crypto-icon {
    width: 1em;
    height: 1em;
    margin-right: 0.375rem;
  }

  .enhanced-crypto-table .table-wrapper {
    max-height: none;
  }

  .enhanced-crypto-table .crypto-row {
    height: auto;
  }

  .enhanced-crypto-table .name-cell {
    min-width: 100px;
  }

  .enhanced-crypto-table .price-cell,
  .enhanced-crypto-table .change-cell,
  .enhanced-crypto-table .market-cap-cell,
  .enhanced-crypto-table .sparkline-cell {
    min-width: auto;
  }

  .enhanced-crypto-table .watchlist-button {
    width: 28px;
    height: 28px;
    font-size: 1rem;
    padding: 0.3rem;
  }

  .enhanced-crypto-table .actions-cell {
    width: 50px;
  }
}
