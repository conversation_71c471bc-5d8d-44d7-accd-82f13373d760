import { doc, getDoc, updateDoc, arrayUnion, setDoc, Timestamp } from 'firebase/firestore';
import { db } from '../firebase-init';

// Interfaz para los fondos del usuario
export interface UserFunds {
  balance: number;
  currency: string;
  lastUpdated: Date;
}

// Tipos para los activos de la cartera
export interface PortfolioAsset {
  id: string;          // ID de la criptomoneda (ej: 'bitcoin')
  symbol: string;      // Símbolo de la criptomoneda (ej: 'BTC')
  name: string;        // Nombre de la criptomoneda (ej: 'Bitcoin')
  amount: number;      // Cantidad de la criptomoneda
  purchasePrice: number; // Precio de compra en USD
  purchaseDate: Date;  // Fecha de compra
  imageUrl?: string;   // URL de la imagen (opcional)
  notes?: string;      // Notas adicionales (opcional)
}

// Interfaz para la cartera completa
export interface Portfolio {
  assets: PortfolioAsset[];
  lastUpdated: Date;
  userId: string;
  funds?: UserFunds;
}

// Obtener la cartera completa del usuario
export const getFullPortfolio = async (uid: string): Promise<Portfolio> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));
    if (portfolioDoc.exists()) {
      const data = portfolioDoc.data();

      // Convertir Timestamp a Date para lastUpdated
      const lastUpdated = data.lastUpdated instanceof Timestamp
        ? data.lastUpdated.toDate()
        : new Date(data.lastUpdated);

      // Convertir Timestamp a Date para cada activo
      const assets = (data.assets || []).map((asset: any) => ({
        ...asset,
        purchaseDate: asset.purchaseDate instanceof Timestamp
          ? asset.purchaseDate.toDate()
          : new Date(asset.purchaseDate)
      }));

      return {
        assets,
        lastUpdated,
        userId: uid
      };
    }

    // Si no existe, devolver una cartera vacía
    return {
      assets: [],
      lastUpdated: new Date(),
      userId: uid
    };
  } catch (error: any) {
    console.error('Error al obtener cartera del usuario:', error);
    throw new Error(error.message);
  }
};

// Obtener solo los activos de la cartera del usuario (para compatibilidad)
export const getUserPortfolio = async (uid: string): Promise<PortfolioAsset[]> => {
  try {
    const portfolio = await getFullPortfolio(uid);
    return portfolio.assets;
  } catch (error: any) {
    console.error('Error al obtener activos de la cartera:', error);
    throw new Error(error.message);
  }
};

// Añadir activo a la cartera
export const addAssetToPortfolio = async (
  uid: string,
  asset: PortfolioAsset
): Promise<boolean> => {
  try {
    // Primero verificamos si el activo ya existe en la cartera
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const portfolio = portfolioDoc.data();
      const assets = portfolio.assets || [];
      const existingAssetIndex = assets.findIndex((a: PortfolioAsset) => a.id === asset.id);

      // Crear una transacción para este activo
      await addTransaction(uid, {
        type: 'buy',
        assetId: asset.id,
        assetName: asset.name,
        assetSymbol: asset.symbol,
        amount: asset.amount,
        price: asset.purchasePrice,
        date: new Date(),
        notes: asset.notes || `Compra de ${asset.amount} ${asset.symbol}`
      });

      if (existingAssetIndex !== -1) {
        // Si el activo ya existe, actualizamos la cantidad y el precio promedio
        const existingAsset = assets[existingAssetIndex];
        const totalAmount = existingAsset.amount + asset.amount;
        const avgPrice = ((existingAsset.amount * existingAsset.purchasePrice) +
                       (asset.amount * asset.purchasePrice)) / totalAmount;

        assets[existingAssetIndex] = {
          ...existingAsset,
          amount: totalAmount,
          purchasePrice: avgPrice,
          // Mantener la fecha de compra original
          notes: asset.notes || existingAsset.notes
        };

        await updateDoc(doc(db, 'Portafolio', uid), {
          assets: assets,
          lastUpdated: new Date()
        });
      } else {
        // Si es un nuevo activo, lo añadimos a la cartera
        // Asegurarse de que la fecha de compra sea un objeto Date y que no haya valores undefined
        const assetToAdd = {
          id: asset.id || '',
          symbol: asset.symbol || '',
          name: asset.name || '',
          amount: asset.amount || 0,
          purchasePrice: asset.purchasePrice || 0,
          purchaseDate: asset.purchaseDate || new Date(),
          imageUrl: asset.imageUrl || null,
          notes: asset.notes || ''
        };

        // Convertir la fecha a timestamp para Firestore
        const assetToStore = {
          ...assetToAdd,
          purchaseDate: Timestamp.fromDate(new Date(assetToAdd.purchaseDate))
        };

        await updateDoc(doc(db, 'Portafolio', uid), {
          assets: arrayUnion(assetToStore),
          lastUpdated: new Date()
        });
      }
    } else {
      // Si no existe el documento de cartera, lo creamos
      // Asegurarse de que no haya valores undefined
      const assetToAdd = {
        id: asset.id || '',
        symbol: asset.symbol || '',
        name: asset.name || '',
        amount: asset.amount || 0,
        purchasePrice: asset.purchasePrice || 0,
        purchaseDate: asset.purchaseDate || new Date(),
        imageUrl: asset.imageUrl || null,
        notes: asset.notes || ''
      };

      // Convertir la fecha a timestamp para Firestore
      const assetToStore = {
        ...assetToAdd,
        purchaseDate: Timestamp.fromDate(new Date(assetToAdd.purchaseDate))
      };

      await setDoc(doc(db, 'Portafolio', uid), {
        assets: [assetToStore],
        lastUpdated: new Date(),
        userId: uid
      });

      // Crear una transacción para este activo
      await addTransaction(uid, {
        type: 'buy',
        assetId: asset.id,
        assetName: asset.name,
        assetSymbol: asset.symbol,
        amount: asset.amount,
        price: asset.purchasePrice,
        date: new Date(),
        notes: asset.notes || `Compra inicial de ${asset.amount} ${asset.symbol}`
      });
    }

    return true;
  } catch (error: any) {
    console.error('Error al añadir activo a la cartera:', error);
    throw new Error(error.message);
  }
};

// Eliminar activo de la cartera
export const removeAssetFromPortfolio = async (
  uid: string,
  assetId: string
): Promise<boolean> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const portfolio = portfolioDoc.data();
      const assets = portfolio.assets || [];
      const assetToRemove = assets.find((a: PortfolioAsset) => a.id === assetId);

      if (assetToRemove) {
        // Registrar la venta como una transacción
        await addTransaction(uid, {
          type: 'sell',
          assetId: assetToRemove.id,
          assetName: assetToRemove.name,
          assetSymbol: assetToRemove.symbol,
          amount: assetToRemove.amount,
          price: assetToRemove.purchasePrice, // Usamos el precio de compra como referencia
          date: new Date(),
          notes: `Venta de ${assetToRemove.amount} ${assetToRemove.symbol}`
        });

        await updateDoc(doc(db, 'Portafolio', uid), {
          assets: assets.filter((a: PortfolioAsset) => a.id !== assetId),
          lastUpdated: new Date()
        });
        return true;
      }
      return false; // No se encontró el activo
    }
    return false; // No existe el portafolio
  } catch (error: any) {
    console.error('Error al eliminar activo de la cartera:', error);
    throw new Error(error.message);
  }
};

// Actualizar un activo específico en la cartera
export const updateAssetInPortfolio = async (
  uid: string,
  assetId: string,
  updates: Partial<PortfolioAsset>
): Promise<boolean> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const portfolio = portfolioDoc.data();
      const assets = portfolio.assets || [];
      const assetIndex = assets.findIndex((a: PortfolioAsset) => a.id === assetId);

      if (assetIndex !== -1) {
        // Actualizar el activo con los cambios proporcionados
        assets[assetIndex] = {
          ...assets[assetIndex],
          ...updates,
          // Asegurar que la fecha siga siendo un objeto Date
          purchaseDate: updates.purchaseDate || assets[assetIndex].purchaseDate
        };

        await updateDoc(doc(db, 'Portafolio', uid), {
          assets: assets,
          lastUpdated: new Date()
        });
        return true;
      }
      return false; // No se encontró el activo
    }
    return false; // No existe el portafolio
  } catch (error: any) {
    console.error('Error al actualizar activo en la cartera:', error);
    throw new Error(error.message);
  }
};

// Actualizar la cartera completa
export const updateUserPortfolio = async (
  uid: string,
  assets: PortfolioAsset[]
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'Portafolio', uid), {
      assets: assets,
      lastUpdated: new Date()
    });
    return true;
  } catch (error: any) {
    console.error('Error al actualizar cartera del usuario:', error);
    throw new Error(error.message);
  }
};

// Obtener el historial de transacciones del usuario
export const getTransactionHistory = async (uid: string): Promise<any[]> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));
    if (portfolioDoc.exists()) {
      const data = portfolioDoc.data();
      return data.transactions || [];
    }
    return [];
  } catch (error: any) {
    console.error('Error al obtener historial de transacciones:', error);
    throw new Error(error.message);
  }
};

// Añadir una transacción al historial
export const addTransaction = async (
  uid: string,
  transaction: {
    type: 'buy' | 'sell',
    assetId: string,
    assetName: string,
    assetSymbol: string,
    amount: number,
    price: number,
    date: Date,
    notes?: string
  }
): Promise<boolean> => {
  try {
    // Asegurarse de que no haya valores undefined
    const safeTransaction = {
      type: transaction.type,
      assetId: transaction.assetId || '',
      assetName: transaction.assetName || '',
      assetSymbol: transaction.assetSymbol || '',
      amount: transaction.amount || 0,
      price: transaction.price || 0,
      date: transaction.date || new Date(),
      notes: transaction.notes || '',
      id: Date.now().toString()
    };

    // Convertir la fecha a timestamp para Firestore
    const transactionToStore = {
      ...safeTransaction,
      date: Timestamp.fromDate(new Date(safeTransaction.date))
    };

    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const data = portfolioDoc.data();
      const transactions = data.transactions || [];

      await updateDoc(doc(db, 'Portafolio', uid), {
        transactions: [...transactions, transactionToStore],
        lastUpdated: new Date()
      });

      return true;
    } else {
      // Si no existe el documento de cartera, lo creamos
      await setDoc(doc(db, 'Portafolio', uid), {
        assets: [],
        transactions: [transactionToStore],
        lastUpdated: new Date(),
        userId: uid
      });

      return true;
    }
  } catch (error: any) {
    console.error('Error al añadir transacción:', error);
    throw new Error(error.message);
  }
};

// Obtener los fondos del usuario
export const getUserFunds = async (uid: string): Promise<UserFunds> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const data = portfolioDoc.data();

      if (data.funds) {
        // Convertir Timestamp a Date para lastUpdated
        const lastUpdated = data.funds.lastUpdated instanceof Timestamp
          ? data.funds.lastUpdated.toDate()
          : new Date(data.funds.lastUpdated);

        return {
          balance: data.funds.balance || 0,
          currency: data.funds.currency || 'USD',
          lastUpdated
        };
      }
    }

    // Si no existe o no tiene fondos, devolver valores por defecto
    return {
      balance: 0,
      currency: 'USD',
      lastUpdated: new Date()
    };
  } catch (error: any) {
    console.error('Error al obtener fondos del usuario:', error);
    throw new Error(error.message);
  }
};

// Añadir fondos al usuario
export const addFundsToUser = async (
  uid: string,
  amount: number,
  currency: string = 'USD'
): Promise<boolean> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const data = portfolioDoc.data();
      const currentFunds = data.funds || { balance: 0, currency: 'USD' };

      // Actualizar los fondos
      const updatedFunds = {
        balance: (currentFunds.balance || 0) + amount,
        currency: currency || currentFunds.currency || 'USD',
        lastUpdated: new Date()
      };

      await updateDoc(doc(db, 'Portafolio', uid), {
        funds: updatedFunds,
        lastUpdated: new Date()
      });
    } else {
      // Si no existe el documento de cartera, lo creamos
      await setDoc(doc(db, 'Portafolio', uid), {
        assets: [],
        funds: {
          balance: amount,
          currency: currency,
          lastUpdated: new Date()
        },
        lastUpdated: new Date(),
        userId: uid
      });
    }

    // Registrar la transacción de fondos
    await addTransaction(uid, {
      type: 'buy',
      assetId: 'funds',
      assetName: 'Fondos',
      assetSymbol: currency,
      amount: amount,
      price: 1,
      date: new Date(),
      notes: `Añadido ${amount} ${currency} a la cuenta`
    });

    return true;
  } catch (error: any) {
    console.error('Error al añadir fondos al usuario:', error);
    throw new Error(error.message);
  }
};