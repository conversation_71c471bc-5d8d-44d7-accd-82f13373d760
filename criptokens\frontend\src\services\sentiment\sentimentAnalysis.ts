import { fetchFromMCP } from '../mcp.service';

/**
 * Interfaz para los resultados de noticias
 */
export interface NewsItem {
  title: string;
  url: string;
  description: string;
  source: string;
  date: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  sentimentScore?: number;
}

/**
 * Interfaz para los resultados de análisis de sentimiento
 */
export interface SentimentResult {
  overallSentiment: number; // -100 a 100
  newsItems: NewsItem[];
  sources: {
    positive: number;
    negative: number;
    neutral: number;
  };
  keywords: {
    word: string;
    count: number;
    sentiment: number;
  }[];
}

/**
 * Obtiene noticias recientes sobre una criptomoneda utilizando Brave Search a través de MCP
 * @param cryptoId ID de la criptomoneda (ej. bitcoin)
 * @param cryptoName Nombre de la criptomoneda (ej. Bitcoin)
 * @param limit Número máximo de noticias a obtener
 * @returns Lista de noticias
 */
export const getNewsForCrypto = async (
  cryptoId: string,
  cryptoName: string,
  limit: number = 10
): Promise<NewsItem[]> => {
  try {
    // Construir la consulta para Brave Search
    const query = `${cryptoName} cryptocurrency news latest`;
    
    // Llamar a MCP para obtener resultados de Brave Search
    const response = await fetchFromMCP('brave', '/search', {
      q: query,
      count: limit * 2 // Solicitar más resultados para filtrar
    });
    
    if (!response || !response.web || !response.web.results) {
      throw new Error('No se pudieron obtener noticias');
    }
    
    // Filtrar y formatear los resultados
    const newsItems: NewsItem[] = response.web.results
      .filter((item: any) => {
        // Filtrar resultados que no parecen noticias
        const isNews = 
          item.title.toLowerCase().includes(cryptoName.toLowerCase()) || 
          item.title.toLowerCase().includes(cryptoId.toLowerCase()) ||
          item.description.toLowerCase().includes(cryptoName.toLowerCase()) ||
          item.description.toLowerCase().includes(cryptoId.toLowerCase());
        
        return isNews;
      })
      .slice(0, limit)
      .map((item: any) => ({
        title: item.title,
        url: item.url,
        description: item.description,
        source: item.display_url || new URL(item.url).hostname,
        date: item.age || 'Reciente'
      }));
    
    return newsItems;
  } catch (error) {
    console.error('Error al obtener noticias:', error);
    return [];
  }
};

/**
 * Analiza el sentimiento de un texto
 * @param text Texto a analizar
 * @returns Puntuación de sentimiento (-100 a 100)
 */
const analyzeSentiment = (text: string): number => {
  // Lista de palabras positivas relacionadas con criptomonedas
  const positiveWords = [
    'bullish', 'rally', 'surge', 'soar', 'gain', 'rise', 'up', 'high', 'growth',
    'adoption', 'partnership', 'launch', 'success', 'breakthrough', 'innovation',
    'potential', 'opportunity', 'profit', 'positive', 'strong', 'strength',
    'support', 'confidence', 'optimistic', 'promising', 'momentum', 'upgrade',
    'institutional', 'mainstream', 'accumulation', 'development', 'progress'
  ];
  
  // Lista de palabras negativas relacionadas con criptomonedas
  const negativeWords = [
    'bearish', 'crash', 'plunge', 'drop', 'fall', 'down', 'low', 'decline',
    'sell-off', 'dump', 'loss', 'negative', 'weak', 'weakness', 'resistance',
    'concern', 'risk', 'warning', 'caution', 'volatile', 'volatility', 'fear',
    'uncertainty', 'doubt', 'fud', 'regulation', 'ban', 'restriction', 'hack',
    'scam', 'fraud', 'manipulation', 'correction', 'bubble', 'distribution'
  ];
  
  // Convertir a minúsculas para comparación
  const lowerText = text.toLowerCase();
  
  // Contar palabras positivas y negativas
  let positiveCount = 0;
  let negativeCount = 0;
  
  positiveWords.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = lowerText.match(regex);
    if (matches) {
      positiveCount += matches.length;
    }
  });
  
  negativeWords.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = lowerText.match(regex);
    if (matches) {
      negativeCount += matches.length;
    }
  });
  
  // Calcular puntuación de sentimiento
  if (positiveCount === 0 && negativeCount === 0) {
    return 0; // Neutral
  }
  
  const total = positiveCount + negativeCount;
  const score = ((positiveCount - negativeCount) / total) * 100;
  
  return Math.round(score);
};

/**
 * Analiza el sentimiento de las noticias sobre una criptomoneda
 * @param cryptoId ID de la criptomoneda
 * @param cryptoName Nombre de la criptomoneda
 * @returns Resultado del análisis de sentimiento
 */
export const analyzeCryptoSentiment = async (
  cryptoId: string,
  cryptoName: string
): Promise<SentimentResult> => {
  try {
    // Obtener noticias
    const newsItems = await getNewsForCrypto(cryptoId, cryptoName, 15);
    
    if (newsItems.length === 0) {
      return {
        overallSentiment: 0,
        newsItems: [],
        sources: { positive: 0, negative: 0, neutral: 0 },
        keywords: []
      };
    }
    
    // Analizar sentimiento de cada noticia
    let totalSentiment = 0;
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;
    
    // Mapa para contar palabras clave
    const keywordCounts: Map<string, { count: number, sentiment: number }> = new Map();
    
    // Analizar cada noticia
    const analyzedNews = newsItems.map(news => {
      // Combinar título y descripción para análisis
      const fullText = `${news.title} ${news.description}`;
      
      // Analizar sentimiento
      const sentimentScore = analyzeSentiment(fullText);
      
      // Determinar categoría de sentimiento
      let sentiment: 'positive' | 'negative' | 'neutral';
      if (sentimentScore > 20) {
        sentiment = 'positive';
        positiveCount++;
      } else if (sentimentScore < -20) {
        sentiment = 'negative';
        negativeCount++;
      } else {
        sentiment = 'neutral';
        neutralCount++;
      }
      
      // Extraer palabras clave (simplificado)
      const words = fullText.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 4 && !['about', 'these', 'their', 'there', 'which', 'would'].includes(word));
      
      // Contar palabras clave
      words.forEach(word => {
        if (keywordCounts.has(word)) {
          const current = keywordCounts.get(word)!;
          keywordCounts.set(word, {
            count: current.count + 1,
            sentiment: current.sentiment + sentimentScore
          });
        } else {
          keywordCounts.set(word, { count: 1, sentiment: sentimentScore });
        }
      });
      
      totalSentiment += sentimentScore;
      
      return {
        ...news,
        sentiment,
        sentimentScore
      };
    });
    
    // Calcular sentimiento general
    const overallSentiment = analyzedNews.length > 0 ? 
      Math.round(totalSentiment / analyzedNews.length) : 0;
    
    // Extraer palabras clave más frecuentes
    const keywords = Array.from(keywordCounts.entries())
      .filter(([_, data]) => data.count > 1)
      .map(([word, data]) => ({
        word,
        count: data.count,
        sentiment: Math.round(data.sentiment / data.count)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    return {
      overallSentiment,
      newsItems: analyzedNews,
      sources: {
        positive: positiveCount,
        negative: negativeCount,
        neutral: neutralCount
      },
      keywords
    };
  } catch (error) {
    console.error('Error al analizar sentimiento:', error);
    return {
      overallSentiment: 0,
      newsItems: [],
      sources: { positive: 0, negative: 0, neutral: 0 },
      keywords: []
    };
  }
};
