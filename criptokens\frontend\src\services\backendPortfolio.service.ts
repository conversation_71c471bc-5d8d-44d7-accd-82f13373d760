/**
 * Servicio para interactuar con la API de portafolio del backend
 *
 * Este servicio proporciona métodos para obtener y actualizar los datos del portafolio
 * desde el backend, que a su vez se conecta a Firestore.
 */

import axios from 'axios';
import { Portfolio, PortfolioAsset } from './portfolio.service';
// Importar la configuración directamente
const config = {
  urls: {
    backend: 'http://localhost:3008'
  }
};

// URL base para el backend desde la configuración centralizada
const BACKEND_URL = config.urls.backend;

// Cliente de axios para el backend
const backendClient = axios.create({
  baseURL: BACKEND_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  timeout: 30000,
  withCredentials: false
});

/**
 * Obtiene el portafolio del usuario desde el backend
 * @param userId ID del usuario
 * @returns Datos del portafolio
 */
export const getPortfolioFromBackend = async (userId: string): Promise<Portfolio> => {
  try {
    console.log(`Obteniendo portafolio desde el backend para el usuario ${userId}`);
    const response = await backendClient.get(`/api/guru/portfolio/${userId}`);

    if (response.data && response.data.portfolio) {
      console.log('Portafolio obtenido correctamente desde el backend');

      // Convertir fechas de string a Date
      const portfolio = response.data.portfolio;

      if (portfolio.lastUpdated) {
        portfolio.lastUpdated = new Date(portfolio.lastUpdated);
      }

      if (portfolio.assets && Array.isArray(portfolio.assets)) {
        portfolio.assets = portfolio.assets.map((asset: any) => ({
          ...asset,
          lastUpdated: asset.lastUpdated ? new Date(asset.lastUpdated) : new Date(),
          purchaseDate: asset.purchaseDate ? new Date(asset.purchaseDate) : new Date()
        }));
      }

      return portfolio;
    }

    throw new Error('Formato de respuesta inválido');
  } catch (error: any) {
    console.error('Error al obtener portafolio desde el backend:', error);

    // Devolver un portafolio vacío en caso de error
    return {
      assets: [],
      totalValue: 0,
      totalInvestment: 0,
      totalProfitLoss: 0,
      totalProfitLossPercentage: 0,
      assetCount: 0,
      lastUpdated: new Date(),
      userId
    };
  }
};

/**
 * Envía una pregunta al Gurú Cripto con el ID del usuario para incluir datos del portafolio
 * @param question Pregunta para el Gurú
 * @param userId ID del usuario
 * @returns Respuesta del Gurú
 */
export const askGuruWithPortfolio = async (question: string, userId: string) => {
  try {
    console.log(`Enviando pregunta al Gurú con ID de usuario ${userId}`);
    const response = await backendClient.post('/api/guru/ask', {
      question,
      userId
    });

    return response.data;
  } catch (error: any) {
    console.error('Error al enviar pregunta al Gurú:', error);
    throw new Error(error.message || 'Error al comunicarse con el Gurú Cripto');
  }
};

export default {
  getPortfolioFromBackend,
  askGuruWithPortfolio
};
