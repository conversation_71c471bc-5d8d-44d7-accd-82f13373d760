import React, { useState } from 'react';
import { useAuth } from '../context/NewAuthContext';
import '../styles/AddFundsModal.css';

interface AddFundsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddFunds: (amount: number, currency: string) => Promise<boolean>;
}

const AddFundsModal: React.FC<AddFundsModalProps> = ({ isOpen, onClose, onAddFunds }) => {
  const [amount, setAmount] = useState<string>('');
  const [currency, setCurrency] = useState<string>('USD');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const { currentUser } = useAuth();

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentUser) {
      setError('Debes iniciar sesión para agregar fondos.');
      return;
    }

    setError(null);
    setSuccess(false);

    // Validar el formulario
    if (!amount || parseFloat(amount) <= 0) {
      setError('Por favor, ingresa una cantidad válida.');
      return;
    }

    try {
      setIsLoading(true);
      const result = await onAddFunds(parseFloat(amount), currency);
      
      if (result) {
        setSuccess(true);
        setAmount('');
        // Cerrar el modal después de 2 segundos
        setTimeout(() => {
          onClose();
          setSuccess(false);
        }, 2000);
      }
    } catch (err: any) {
      console.error('Error al agregar fondos:', err);
      setError(err.message || 'No se pudieron agregar los fondos. Por favor, intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="add-funds-modal">
        <div className="modal-header">
          <h3>Agregar Fondos</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <form onSubmit={handleSubmit}>
          {error && <p className="error-message">{error}</p>}
          {success && <p className="success-message">¡Fondos agregados correctamente!</p>}
          
          <div className="form-group">
            <label htmlFor="amount">Cantidad</label>
            <div className="amount-input-container">
              <input
                id="amount"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                min="0.01"
                step="0.01"
                required
                disabled={isLoading}
              />
            </div>
          </div>
          
          <div className="form-group">
            <label htmlFor="currency">Moneda</label>
            <select
              id="currency"
              value={currency}
              onChange={(e) => setCurrency(e.target.value)}
              disabled={isLoading}
            >
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="GBP">GBP</option>
            </select>
          </div>
          
          <div className="form-actions">
            <button
              type="button"
              className="cancel-button"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="submit-button"
              disabled={isLoading}
            >
              {isLoading ? 'Procesando...' : 'Agregar Fondos'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddFundsModal;
