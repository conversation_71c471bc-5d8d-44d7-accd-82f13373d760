import React, { useState } from 'react';
import NewsCard from './NewsCard';
import { NewsItem } from '../services/braveSearch';
import '../styles/NewsList.css';

interface NewsListProps {
  news: NewsItem[];
  isLoading: boolean;
  error: string | null;
  onLoadMore?: () => void;
  hasMore?: boolean;
  showFilters?: boolean;
  onFilterChange?: (freshness: string) => void;
}

const NewsList: React.FC<NewsListProps> = ({
  news,
  isLoading,
  error,
  onLoadMore,
  hasMore = false,
  showFilters = true,
  onFilterChange
}) => {
  const [selectedFreshness, setSelectedFreshness] = useState<string>('');

  // Manejar el cambio de filtro de tiempo
  const handleFreshnessChange = (freshness: string) => {
    setSelectedFreshness(freshness);
    if (onFilterChange) {
      onFilterChange(freshness);
    }
  };

  // Renderizar los filtros de tiempo
  const renderFilters = () => {
    if (!showFilters) return null;

    return (
      <div className="news-filters">
        <div className="filter-group">
          <span className="filter-label">Filtrar por tiempo:</span>
          <div className="filter-options">
            <button
              className={selectedFreshness === '' ? 'active' : ''}
              onClick={() => handleFreshnessChange('')}
            >
              Todos
            </button>
            <button
              className={selectedFreshness === 'pd' ? 'active' : ''}
              onClick={() => handleFreshnessChange('pd')}
            >
              Hoy
            </button>
            <button
              className={selectedFreshness === 'pw' ? 'active' : ''}
              onClick={() => handleFreshnessChange('pw')}
            >
              Esta semana
            </button>
            <button
              className={selectedFreshness === 'pm' ? 'active' : ''}
              onClick={() => handleFreshnessChange('pm')}
            >
              Este mes
            </button>
            <button
              className={selectedFreshness === 'py' ? 'active' : ''}
              onClick={() => handleFreshnessChange('py')}
            >
              Este año
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Renderizar el estado de carga
  const renderLoading = () => {
    if (!isLoading) return null;

    return (
      <div className="news-loading">
        <div className="loading-spinner"></div>
        <p>Cargando noticias...</p>
      </div>
    );
  };

  // Renderizar el mensaje de error
  const renderError = () => {
    if (!error) return null;

    return (
      <div className="news-error">
        <i className="fas fa-exclamation-circle"></i>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Reintentar</button>
      </div>
    );
  };

  // Renderizar el mensaje de no hay resultados
  const renderNoResults = () => {
    if (isLoading || error || news.length > 0) return null;

    return (
      <div className="news-no-results">
        <i className="fas fa-search"></i>
        <p>No se encontraron noticias con los filtros actuales.</p>
        <p>Intenta con otros filtros o términos de búsqueda.</p>
      </div>
    );
  };

  return (
    <div className="news-list-container">
      {renderFilters()}
      
      <div className="news-list">
        {news.map((item, index) => (
          <NewsCard key={`${item.url}-${index}`} news={item} />
        ))}
      </div>
      
      {renderLoading()}
      {renderError()}
      {renderNoResults()}
      
      {hasMore && !isLoading && !error && news.length > 0 && (
        <div className="load-more-container">
          <button className="load-more-button" onClick={onLoadMore}>
            Cargar más noticias
          </button>
        </div>
      )}
    </div>
  );
};

export default NewsList;
