import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "@modelcontextprotocol/sdk/zod.js";
import fetch from "node-fetch";
import * as cheerio from "cheerio";

// Crear el servidor MCP
const server = new McpServer({
  name: "CriptoTokens MCP Server",
  version: "1.0.0",
  description: "Servidor MCP para obtener datos de criptomonedas en tiempo real"
});

// URL base de la API de CoinGecko
const COINGECKO_API_URL = "https://api.coingecko.com/api/v3";

// Función para obtener datos de la API de CoinGecko
async function fetchFromCoinGecko(endpoint, params = {}) {
  try {
    // Construir la URL con los parámetros
    const url = new URL(`${COINGECKO_API_URL}${endpoint}`);
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });

    console.log(`Fetching data from: ${url.toString()}`);

    // Realizar la petición
    const response = await fetch(url.toString());

    // Verificar si la respuesta es exitosa
    if (!response.ok) {
      throw new Error(`Error en la petición: ${response.status} ${response.statusText}`);
    }

    // Parsear la respuesta como JSON
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error al obtener datos de CoinGecko:`, error);
    throw error;
  }
}

// Función para obtener el precio de una criptomoneda desde CoinGecko
async function getCryptoPrice(cryptoId) {
  try {
    // Usar la API de CoinGecko para obtener datos detallados
    const data = await fetchFromCoinGecko(`/coins/${cryptoId}`, {
      localization: false,
      tickers: false,
      market_data: true,
      community_data: false,
      developer_data: false
    });

    return {
      id: data.id,
      name: data.name,
      symbol: data.symbol.toUpperCase(),
      price: data.market_data.current_price.usd,
      price_change_24h: data.market_data.price_change_percentage_24h,
      market_cap: data.market_data.market_cap.usd,
      total_volume: data.market_data.total_volume.usd,
      high_24h: data.market_data.high_24h.usd,
      low_24h: data.market_data.low_24h.usd,
      image: data.image.large,
      last_updated: data.last_updated
    };
  } catch (error) {
    console.error(`Error al obtener el precio de ${cryptoId}:`, error);
    throw error;
  }
}

// Función para obtener las principales criptomonedas
async function getTopCryptocurrencies(limit = 10, page = 1) {
  try {
    const data = await fetchFromCoinGecko('/coins/markets', {
      vs_currency: 'usd',
      order: 'market_cap_desc',
      per_page: limit,
      page: page,
      sparkline: true,
      price_change_percentage: '24h'
    });

    return data;
  } catch (error) {
    console.error('Error al obtener las principales criptomonedas:', error);
    throw error;
  }
}

// Función para obtener datos históricos de una criptomoneda
async function getCryptoHistoricalData(cryptoId, days = 7) {
  try {
    const backendUrl = "http://localhost:3002"; // Reemplazar con la URL del backend
    const response = await fetch(`${backendUrl}/api/crypto/historical/${cryptoId}?days=${days}`);

    if (!response.ok) {
      throw new Error(`Error al obtener datos históricos: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${cryptoId}:`, error);
    throw error;
  }
}

// Herramienta para obtener el precio de una criptomoneda
server.tool(
  "getCryptoPrice",
  {
    cryptoId: z.string().describe("ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)")
  },
  async ({ cryptoId }) => {
    try {
      const data = await getCryptoPrice(cryptoId.toLowerCase());

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2)
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error al obtener el precio de ${cryptoId}: ${error.message}`
          }
        ]
      };
    }
  }
);

// Herramienta para obtener las principales criptomonedas
server.tool(
  "getTopCryptocurrencies",
  {
    limit: z.number().min(1).max(100).default(10).describe("Número de criptomonedas a obtener"),
    page: z.number().min(1).default(1).describe("Página de resultados")
  },
  async ({ limit, page }) => {
    try {
      const data = await getTopCryptocurrencies(limit, page);

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2)
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error al obtener las principales criptomonedas: ${error.message}`
          }
        ]
      };
    }
  }
);

// Herramienta para obtener datos históricos de una criptomoneda
server.tool(
  "getCryptoHistoricalData",
  {
    cryptoId: z.string().describe("ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)"),
    days: z.number().min(1).max(365).default(7).describe("Número de días de datos históricos")
  },
  async ({ cryptoId, days }) => {
    try {
      const data = await getCryptoHistoricalData(cryptoId.toLowerCase(), days);

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2)
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error al obtener datos históricos para ${cryptoId}: ${error.message}`
          }
        ]
      };
    }
  }
);

// Herramienta para buscar criptomonedas por nombre o símbolo
server.tool(
  "searchCryptocurrencies",
  {
    query: z.string().describe("Término de búsqueda (nombre o símbolo)")
  },
  async ({ query }) => {
    try {
      const data = await fetchFromCoinGecko('/search', { query });

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2)
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error al buscar criptomonedas: ${error.message}`
          }
        ]
      };
    }
  }
);

// Iniciar el servidor MCP
console.log("Iniciando servidor MCP para CriptoTokens...");
const transport = new StdioServerTransport();
await server.connect(transport);
console.log("Servidor MCP conectado y listo para recibir solicitudes.");
