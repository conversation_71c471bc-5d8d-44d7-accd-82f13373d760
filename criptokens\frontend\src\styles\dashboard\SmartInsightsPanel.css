.smart-insights-panel {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.smart-insights-panel:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.ai-badge {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.panel-content {
  padding: 0.75rem;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.insight-card {
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
  border-left: 3px solid transparent;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.insight-card.success {
  border-left-color: #2ecc71;
}

.insight-card.warning {
  border-left-color: #f39c12;
}

.insight-card.info {
  border-left-color: #3498db;
}

.insight-card.opportunity {
  border-left-color: #9b59b6;
}

.insight-card.caution {
  border-left-color: #e74c3c;
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.03);
}

.insight-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  flex-shrink: 0;
  font-size: 1rem;
}

.insight-card.success .insight-icon {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
}

.insight-card.warning .insight-icon {
  color: #f39c12;
  background-color: rgba(243, 156, 18, 0.1);
}

.insight-card.info .insight-icon {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.insight-card.opportunity .insight-icon {
  color: #9b59b6;
  background-color: rgba(155, 89, 182, 0.1);
}

.insight-card.caution .insight-icon {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.insight-title {
  flex: 1;
}

.insight-title h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.insight-source {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  background-color: var(--color-surface-dark);
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
}

.dismiss-button {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.dismiss-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-secondary);
}

.insight-body {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.insight-card.expanded .insight-body {
  padding: 0.75rem;
  max-height: 500px;
}

.insight-body p {
  margin: 0 0 0.75rem 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.related-assets {
  margin-bottom: 0.75rem;
}

.related-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  margin-right: 0.5rem;
}

.asset-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.asset-tag {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background-color: var(--color-surface-dark);
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
}

.insight-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.insight-action-button {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.insight-action-button:hover {
  color: var(--color-primary-light);
}

.insight-action-button i {
  font-size: 0.75rem;
}

.insight-timestamp {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.empty-insights {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-tertiary);
}

.empty-insights i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-insights p {
  margin: 0;
  font-size: 0.9rem;
  max-width: 80%;
}

.skeleton-loading {
  width: 100%;
  height: 150px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .insights-list {
    max-height: 300px;
  }
}

@media (max-width: 576px) {
  .panel-header {
    padding: 0.75rem;
  }
  
  .panel-content {
    padding: 0.5rem;
  }
  
  .insight-header {
    padding: 0.5rem;
  }
  
  .insight-card.expanded .insight-body {
    padding: 0.5rem;
  }
}
