/* Estilos para el conversor de criptomonedas */

.coin-converter-container {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.coin-converter-container h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.converter-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.converter-description p {
  margin: 0;
}

.converter-card {
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.converter-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
}

.converter-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.converter-input-group label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.input-with-select {
  display: flex;
  align-items: center;
  background-color: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.2s ease;
}

.input-with-select:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-transparent);
}

.currency-symbol {
  padding: 0 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  border-right: 1px solid var(--border-color);
}

.input-with-select input {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 1rem;
  outline: none;
}

.input-with-select select {
  padding: 0.75rem;
  border: none;
  border-left: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
  font-size: 0.875rem;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.input-with-select select:hover {
  background-color: var(--color-surface-light);
}

.swap-button {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.swap-button:hover {
  background-color: var(--color-primary-light);
  transform: translate(-50%, -50%) rotate(180deg);
}

.conversion-rate {
  text-align: center;
  font-size: 0.875rem;
  color: var(--text-secondary);
  padding-top: 0.5rem;
  border-top: 1px solid var(--border-color);
}

.conversion-rate p {
  margin: 0;
}

.common-conversions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.common-conversions h4 {
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--text-primary);
}

.conversions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.conversion-item {
  padding: 1rem;
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.conversion-item:hover {
  background-color: var(--color-surface-light);
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
}

.conversion-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.conversion-value {
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .converter-form {
    gap: 2rem;
  }
  
  .swap-button {
    position: relative;
    left: auto;
    top: auto;
    transform: none;
    margin: 0 auto;
  }
  
  .conversions-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 576px) {
  .coin-converter-container {
    padding: 0.75rem;
  }
  
  .converter-card {
    padding: 1rem;
  }
  
  .input-with-select input,
  .input-with-select select {
    padding: 0.625rem;
    font-size: 0.875rem;
  }
  
  .conversions-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .conversion-item {
    padding: 0.75rem;
  }
}
