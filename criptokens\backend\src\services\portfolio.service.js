// Servicio de portafolio que utiliza Firebase Admin para acceder a Firestore
const { db } = require('../config/firebase');

// Datos simulados de portafolio como fallback
const mockPortfolios = {
  'usuario1': {
    assets: [
      {
        id: 'bitcoin',
        symbol: 'BTC',
        name: 'Bitcoin',
        amount: 0.5,
        purchasePrice: 35000,
        currentPrice: 40000,
        value: 20000,
        profitLoss: 2500,
        profitLossPercentage: 14.28
      },
      {
        id: 'ethereum',
        symbol: 'ETH',
        name: 'Ethereum',
        amount: 5,
        purchasePrice: 2000,
        currentPrice: 2200,
        value: 11000,
        profitLoss: 1000,
        profitLossPercentage: 10
      },
      {
        id: 'solana',
        symbol: 'SOL',
        name: '<PERSON><PERSON>',
        amount: 20,
        purchasePrice: 80,
        currentPrice: 100,
        value: 2000,
        profitLoss: 400,
        profitLossPercentage: 25
      }
    ],
    totalValue: 33000,
    totalInvestment: 29100,
    totalProfitLoss: 3900,
    totalProfitLossPercentage: 13.4,
    assetCount: 3,
    lastUpdated: new Date()
  }
};

// Variable para controlar si se usan datos reales o simulados
let useFirestore = true;

// Función para verificar si Firestore está disponible
function checkFirestoreAvailability() {
  try {
    if (!db) {
      console.warn('Firestore no está disponible. Usando datos simulados.');
      useFirestore = false;
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error al verificar disponibilidad de Firestore:', error);
    useFirestore = false;
    return false;
  }
}

/**
 * Obtiene el portafolio de un usuario desde Firestore
 * @param {string} userId - ID del usuario
 * @returns {Promise<Object>} - Datos del portafolio
 */
async function getUserPortfolio(userId) {
  try {
    if (!userId) {
      console.warn('getUserPortfolio: No se proporcionó ID de usuario');
      return null;
    }

    // Verificar si Firestore está disponible
    if (!checkFirestoreAvailability() || !useFirestore) {
      console.log(`Usando portafolio simulado para el usuario ${userId} (Firestore no disponible)`);
      return mockPortfolios['usuario1'] || null;
    }

    try {
      console.log(`Obteniendo portafolio real desde Firestore para el usuario ${userId}`);

      // Obtener el documento de portafolio desde Firestore
      const portfolioRef = db.collection('Portafolio').doc(userId);
      const portfolioDoc = await portfolioRef.get();

      if (!portfolioDoc.exists) {
        console.log(`No se encontró portafolio para el usuario ${userId} en Firestore`);
        return null;
      }

      // Obtener los datos del portafolio
      const portfolioData = portfolioDoc.data();

      // Verificar si hay activos en el portafolio
      if (!portfolioData.assets || portfolioData.assets.length === 0) {
        console.log(`El portafolio del usuario ${userId} no tiene activos`);
        return {
          assets: [],
          totalValue: 0,
          totalInvestment: 0,
          totalProfitLoss: 0,
          totalProfitLossPercentage: 0,
          assetCount: 0,
          lastUpdated: new Date()
        };
      }

      // Convertir fechas de Firestore a objetos Date de JavaScript
      const assets = portfolioData.assets.map(asset => ({
        ...asset,
        purchaseDate: asset.purchaseDate ? new Date(asset.purchaseDate.toDate()) : new Date()
      }));

      // Si no hay estadísticas calculadas, devolver solo los activos
      if (!portfolioData.totalValue) {
        return {
          assets,
          lastUpdated: portfolioData.lastUpdated ? new Date(portfolioData.lastUpdated.toDate()) : new Date()
        };
      }

      // Devolver el portafolio completo con estadísticas
      return {
        assets,
        totalValue: portfolioData.totalValue || 0,
        totalInvestment: portfolioData.totalInvestment || 0,
        totalProfitLoss: portfolioData.totalProfitLoss || 0,
        totalProfitLossPercentage: portfolioData.totalProfitLossPercentage || 0,
        assetCount: assets.length,
        lastUpdated: portfolioData.lastUpdated ? new Date(portfolioData.lastUpdated.toDate()) : new Date()
      };
    } catch (firestoreError) {
      console.error('Error al obtener portafolio desde Firestore:', firestoreError);
      console.log('Fallback a datos simulados debido a error de Firestore');

      // En caso de error, usar datos simulados como fallback
      return mockPortfolios['usuario1'] || null;
    }
  } catch (error) {
    console.error('Error general al obtener portafolio del usuario:', error);
    return null;
  }
}

/**
 * Calcula estadísticas del portafolio basadas en precios actuales
 * @param {Object} portfolioData - Datos del portafolio
 * @param {Object} currentPrices - Precios actuales de las criptomonedas
 * @returns {Object} - Estadísticas del portafolio
 */
function calculatePortfolioStats(portfolioData, currentPrices = {}) {
  // Si ya tenemos un portafolio completo, simplemente lo devolvemos
  if (portfolioData && portfolioData.assets && portfolioData.totalValue) {
    // Actualizar precios actuales si se proporcionan
    if (Object.keys(currentPrices).length > 0) {
      let totalValue = 0;
      let totalProfitLoss = 0;

      const updatedAssets = portfolioData.assets.map(asset => {
        // Usar el precio proporcionado o mantener el actual
        const newCurrentPrice = currentPrices[asset.id] || asset.currentPrice;
        const newValue = asset.amount * newCurrentPrice;
        const profitLoss = newValue - (asset.amount * asset.purchasePrice);
        const profitLossPercentage = asset.purchasePrice > 0
          ? (profitLoss / (asset.amount * asset.purchasePrice)) * 100
          : 0;

        totalValue += newValue;
        totalProfitLoss += profitLoss;

        return {
          ...asset,
          currentPrice: newCurrentPrice,
          value: newValue,
          profitLoss,
          profitLossPercentage
        };
      });

      const totalProfitLossPercentage = portfolioData.totalInvestment > 0
        ? (totalProfitLoss / portfolioData.totalInvestment) * 100
        : 0;

      return {
        ...portfolioData,
        assets: updatedAssets,
        totalValue,
        totalProfitLoss,
        totalProfitLossPercentage,
        lastUpdated: new Date()
      };
    }

    return portfolioData;
  }

  // Si solo tenemos activos, calculamos las estadísticas
  const assets = portfolioData?.assets || [];
  let totalValue = 0;
  let totalInvestment = 0;
  const assetCount = assets.length;

  // Calcular valores para cada activo
  const assetsWithStats = assets.map(asset => {
    const currentPrice = currentPrices[asset.id] || 0;
    const value = asset.amount * currentPrice;
    const investment = asset.amount * asset.purchasePrice;
    const profitLoss = value - investment;
    const profitLossPercentage = investment > 0
      ? (profitLoss / investment) * 100
      : 0;

    totalValue += value;
    totalInvestment += investment;

    return {
      ...asset,
      currentPrice,
      value,
      profitLoss,
      profitLossPercentage
    };
  });

  // Calcular estadísticas generales
  const totalProfitLoss = totalValue - totalInvestment;
  const totalProfitLossPercentage = totalInvestment > 0
    ? (totalProfitLoss / totalInvestment) * 100
    : 0;

  return {
    assets: assetsWithStats,
    totalValue,
    totalInvestment,
    totalProfitLoss,
    totalProfitLossPercentage,
    assetCount,
    lastUpdated: new Date()
  };
}

/**
 * Actualiza las estadísticas del portafolio en Firestore
 * @param {string} userId - ID del usuario
 * @param {Object} portfolioStats - Estadísticas del portafolio
 * @returns {Promise<boolean>} - True si la actualización fue exitosa
 */
async function updatePortfolioStats(userId, portfolioStats) {
  try {
    if (!userId || !portfolioStats) {
      console.warn('updatePortfolioStats: Faltan parámetros requeridos');
      return false;
    }

    // Verificar si Firestore está disponible
    if (!checkFirestoreAvailability() || !useFirestore) {
      console.log(`No se puede actualizar el portafolio para ${userId} (Firestore no disponible)`);
      return false;
    }

    try {
      console.log(`Actualizando estadísticas del portafolio para el usuario ${userId} en Firestore`);

      // Referencia al documento de portafolio
      const portfolioRef = db.collection('Portafolio').doc(userId);

      // Actualizar solo las estadísticas, manteniendo los activos originales
      await portfolioRef.update({
        totalValue: portfolioStats.totalValue || 0,
        totalInvestment: portfolioStats.totalInvestment || 0,
        totalProfitLoss: portfolioStats.totalProfitLoss || 0,
        totalProfitLossPercentage: portfolioStats.totalProfitLossPercentage || 0,
        assetCount: portfolioStats.assetCount || 0,
        lastUpdated: new Date()
      });

      return true;
    } catch (firestoreError) {
      console.error('Error al actualizar estadísticas en Firestore:', firestoreError);
      return false;
    }
  } catch (error) {
    console.error('Error general al actualizar estadísticas del portafolio:', error);
    return false;
  }
}

/**
 * Actualiza los precios actuales de los activos en el portafolio
 * @param {string} userId - ID del usuario
 * @param {Object} currentPrices - Precios actuales de las criptomonedas
 * @returns {Promise<boolean>} - True si la actualización fue exitosa
 */
async function updateAssetPrices(userId, currentPrices) {
  try {
    if (!userId || !currentPrices || Object.keys(currentPrices).length === 0) {
      console.warn('updateAssetPrices: Faltan parámetros requeridos');
      return false;
    }

    // Obtener el portafolio actual
    const portfolio = await getUserPortfolio(userId);
    if (!portfolio || !portfolio.assets || portfolio.assets.length === 0) {
      console.log(`No hay activos para actualizar en el portafolio de ${userId}`);
      return false;
    }

    // Calcular estadísticas con los nuevos precios
    const updatedPortfolio = calculatePortfolioStats(portfolio, currentPrices);

    // Actualizar el portafolio en Firestore
    return await updatePortfolioStats(userId, updatedPortfolio);
  } catch (error) {
    console.error('Error al actualizar precios de activos:', error);
    return false;
  }
}

module.exports = {
  getUserPortfolio,
  calculatePortfolioStats,
  updatePortfolioStats,
  updateAssetPrices
};
