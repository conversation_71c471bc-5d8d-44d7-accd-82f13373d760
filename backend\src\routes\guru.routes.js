const express = require('express');
const router = express.Router();
const { searchCryptoNews } = require('../services/brave.service');
const { generateResponse, getChatResponse } = require('../services/openrouter.service');
const { getCryptoPrice, getTopCryptocurrencies, getCryptoHistoricalData, getMarketSentimentIndex } = require('../services/mcp.service');
const { visualizeWebPage } = require('../services/playwright-mcp.service');
const technicalAnalysisService = require('../services/technical-analysis.service');
const intentDetection = require('../services/intentDetection.service');

// Función para detectar posibles IDs de criptomonedas en una pregunta
function detectCryptoId(question) {
  // Lista de criptomonedas comunes para detectar
  const commonCryptos = [
    { id: 'bitcoin', keywords: ['bitcoin', 'btc'] },
    { id: 'ethereum', keywords: ['ethereum', 'ether', 'eth'] },
    { id: 'tether', keywords: ['tether', 'usdt'] },
    { id: 'binancecoin', keywords: ['bnb', 'binance coin', 'binancecoin'] },
    { id: 'solana', keywords: ['solana', 'sol'] },
    { id: 'ripple', keywords: ['ripple', 'xrp'] },
    { id: 'cardano', keywords: ['cardano', 'ada'] },
    { id: 'dogecoin', keywords: ['dogecoin', 'doge'] },
    { id: 'polkadot', keywords: ['polkadot', 'dot'] },
    { id: 'litecoin', keywords: ['litecoin', 'ltc'] }
  ];

  // Convertir la pregunta a minúsculas para facilitar la comparación
  const lowerQuestion = question.toLowerCase();

  // Buscar coincidencias
  for (const crypto of commonCryptos) {
    for (const keyword of crypto.keywords) {
      // Buscar la palabra clave como palabra completa
      const regex = new RegExp(`\\b${keyword}\\b`, 'i');
      if (regex.test(lowerQuestion)) {
        return crypto.id;
      }
    }
  }

  return null;
}

// Endpoint para generar respuestas del Guru (ruta original)
router.post('/chat', async (req, res) => {
  try {
    const { message, history } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Se requiere un mensaje' });
    }

    const response = await generateResponse(message, history);
    res.json({ response });
  } catch (error) {
    console.error('Error en el endpoint de chat:', error);
    res.status(500).json({ error: 'Error al generar respuesta', details: error.message });
  }
});

// Endpoint para generar respuestas del Guru (ruta alternativa para compatibilidad con el frontend)
router.post('/ask', async (req, res) => {
  try {
    const { question, userId } = req.body;

    if (!question) {
      return res.status(400).json({ error: 'Se requiere una pregunta' });
    }

    console.log(`Pregunta recibida: ${question}`);
    if (userId) {
      console.log(`Usuario autenticado: ${userId}`);
    }

    // Detectar si la pregunta menciona una criptomoneda específica
    const cryptoId = detectCryptoId(question);
    console.log(`Criptomoneda detectada: ${cryptoId || 'ninguna'}`);

    // Detectar la intención de la pregunta
    const { intent, confidence, metadata } = intentDetection.detectIntent(question);
    console.log(`Intención detectada: ${intent} (confianza: ${confidence})`);
    console.log('Metadatos:', metadata);

    // Verificar si es una pregunta de análisis técnico
    const isTechnicalAnalysisQuestion = intent === 'technical_analysis';
    console.log(`¿Es pregunta de análisis técnico? ${isTechnicalAnalysisQuestion ? 'Sí' : 'No'}`);

    // Forzar análisis técnico si la pregunta contiene "Bitcoin" o "BTC"
    let forceTechnicalAnalysis = false;
    if (cryptoId === 'bitcoin' || cryptoId === 'btc') {
      console.log('Forzando análisis técnico para Bitcoin');
      forceTechnicalAnalysis = true;
    }

    // Si es una pregunta de análisis técnico y se detectó una criptomoneda, usar el endpoint específico
    if ((isTechnicalAnalysisQuestion || forceTechnicalAnalysis) && cryptoId) {
      console.log(`Detectada pregunta de análisis técnico para ${cryptoId}`);

      // Mapear el ID de la criptomoneda al símbolo
      const symbolMap = {
        'bitcoin': 'BTC',
        'ethereum': 'ETH',
        'tether': 'USDT',
        'binancecoin': 'BNB',
        'solana': 'SOL',
        'ripple': 'XRP',
        'cardano': 'ADA',
        'dogecoin': 'DOGE',
        'polkadot': 'DOT',
        'litecoin': 'LTC'
      };

      const symbol = symbolMap[cryptoId] || cryptoId.toUpperCase();

      try {
        // Realizar análisis técnico directamente a través del endpoint
        const analysisResponse = await fetch(`http://localhost:3001/api/guru/technical-analysis`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ symbol })
        });

        if (!analysisResponse.ok) {
          throw new Error(`Error al realizar análisis técnico: ${analysisResponse.statusText}`);
        }

        const analysisData = await analysisResponse.json();

        // Obtener datos de la criptomoneda para el contexto
        console.log(`Obteniendo datos específicos para ${cryptoId}...`);
        const cryptoData = await getCryptoPrice(cryptoId);

        // Enviar respuesta al cliente
        return res.json({
          reply: analysisData.naturalLanguageResponse,
          cryptoData: cryptoData || null,
          hasPortfolioData: false,
          webPageData: null,
          technicalAnalysis: analysisData.analysis
        });
      } catch (analysisError) {
        console.error('Error al realizar análisis técnico:', analysisError);
        // Continuar con el flujo normal si hay un error en el análisis técnico
      }
    }

    // Si es una pregunta de análisis fundamental y se detectó una criptomoneda, usar el endpoint específico
    if (intent === 'fundamental_analysis' && cryptoId) {
      console.log(`Detectada pregunta de análisis fundamental para ${cryptoId}`);

      // Mapear el ID de la criptomoneda al símbolo
      const symbolMap = {
        'bitcoin': 'BTC',
        'ethereum': 'ETH',
        'tether': 'USDT',
        'binancecoin': 'BNB',
        'solana': 'SOL',
        'ripple': 'XRP',
        'cardano': 'ADA',
        'dogecoin': 'DOGE',
        'polkadot': 'DOT',
        'litecoin': 'LTC'
      };

      const symbol = symbolMap[cryptoId] || cryptoId.toUpperCase();

      try {
        // Realizar análisis fundamental directamente a través del endpoint
        const analysisResponse = await fetch(`http://localhost:3001/api/fundamental`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ symbol })
        });

        if (!analysisResponse.ok) {
          throw new Error(`Error al realizar análisis fundamental: ${analysisResponse.statusText}`);
        }

        const analysisData = await analysisResponse.json();

        // Obtener datos de la criptomoneda para el contexto
        console.log(`Obteniendo datos específicos para ${cryptoId}...`);
        const cryptoData = await getCryptoPrice(cryptoId);

        // Enviar respuesta al cliente
        return res.json({
          reply: analysisData.naturalLanguageResponse,
          cryptoData: cryptoData || null,
          hasPortfolioData: false,
          webPageData: null,
          fundamentalAnalysis: analysisData.analysis
        });
      } catch (analysisError) {
        console.error('Error al realizar análisis fundamental:', analysisError);
        // Continuar con el flujo normal si hay un error en el análisis fundamental
      }
    }

    // Recopilar datos para enriquecer el contexto
    const contextData = {};

    // 1. Obtener datos de las principales criptomonedas (siempre)
    console.log('Obteniendo datos de las 5 principales criptomonedas...');
    const topCryptos = await getTopCryptocurrencies(5);
    if (topCryptos && topCryptos.length > 0) {
      contextData.topCryptos = topCryptos;
    }

    // 2. Si se detectó una criptomoneda específica, obtener sus datos
    if (cryptoId) {
      console.log(`Obteniendo datos específicos para ${cryptoId}...`);
      const cryptoData = await getCryptoPrice(cryptoId);
      if (cryptoData) {
        contextData.cryptoData = cryptoData;

        // 3. Obtener datos históricos de la criptomoneda (opcional)
        console.log(`Obteniendo datos históricos para ${cryptoId}...`);
        const historicalData = await getCryptoHistoricalData(cryptoId, 1);
        if (historicalData) {
          contextData.historicalData = historicalData;
        }
      }
    }

    // 4. Obtener índice de sentimiento del mercado
    console.log('Calculando índice de sentimiento del mercado...');
    const sentimentData = await getMarketSentimentIndex();
    if (sentimentData) {
      contextData.marketSentiment = sentimentData;
    }

    console.log('Contexto enriquecido preparado para el LLM');

    // Generar respuesta con el servicio de Gemini
    const geminiService = require('../services/gemini.service');
    let reply;
    try {
      // Historial de conversación (puede ser undefined)
      const conversationHistory = req.body.history || [];
      reply = await geminiService.generateResponse(question, contextData, conversationHistory);
    } catch (geminiError) {
      console.error('Error al generar respuesta con Gemini:', geminiError);
      reply = "¡Hola! Soy el Guru Cripto, tu asistente experto en criptomonedas. ¿En qué puedo ayudarte hoy?";
    }

    // Verificar si la respuesta contiene una solicitud para visualizar una página web
    const webPageRegex = /\[BROWSE_WEBPAGE:(.*?)\]/;
    const webPageMatch = reply.match(webPageRegex);

    let finalReply = reply;
    let webPageData = null;

    if (webPageMatch && webPageMatch[1]) {
      // Extraer la URL de la página web
      const url = webPageMatch[1].trim();
      console.log(`Solicitud de visualización de página web detectada: ${url}`);

      try {
        // Simular datos de página web para pruebas
        webPageData = {
          url: url,
          title: "Página Web Visualizada",
          screenshot: "iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAIAAADTED8xAAADMElEQVR4nOzVMQEAIAzAMMC/5yFjRxMFfXpnZg5Eve8A2GQA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwA2Q9hTQzDfzgDHAAAAABJRU5ErkJggg==",
          extractedText: "Este es un texto de ejemplo extraído de la página web. Aquí se mostraría el contenido real de la página web visualizada.",
          summary: "Esta es una página web sobre criptomonedas que muestra información actualizada sobre precios, tendencias y análisis del mercado.",
          timestamp: new Date().toLocaleString()
        };

        // Eliminar el marcador de visualización de página web del texto
        finalReply = reply.replace(webPageMatch[0], '');

        console.log(`Simulación de visualización de página web completada para: ${url}`);
      } catch (webPageError) {
        console.error('Error al visualizar la página web:', webPageError);
        // Mantener la respuesta original si hay un error
        finalReply = reply;
      }
    }

    // Enviar respuesta al cliente
    res.json({
      reply: finalReply,
      cryptoData: contextData.cryptoData || null,
      hasPortfolioData: false,
      webPageData: webPageData
    });
  } catch (error) {
    console.error('Error en el endpoint de ask:', error);
    console.error('Detalles del error:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));

    // Fallback a respuesta simulada en caso de error
    const fallbackReply = `Lo siento, ha ocurrido un error al procesar tu consulta. Por favor, inténtalo de nuevo más tarde.`;

    res.json({
      reply: fallbackReply,
      cryptoData: null,
      hasPortfolioData: false,
      webPageData: null,
      fallback: true,
      error: {
        message: error.message,
        type: error.constructor.name
      }
    });
  }
});

// Endpoint para buscar noticias de criptomonedas
router.post('/news', async (req, res) => {
  try {
    const { topic = 'criptomonedas noticias', freshness = '', count = 10 } = req.body;

    console.log(`Solicitud de noticias recibida - Tema: ${topic}, Freshness: ${freshness}, Count: ${count}`);

    const newsResults = await searchCryptoNews(topic, freshness, count);
    res.json(newsResults);
  } catch (error) {
    console.error('Error en el endpoint de noticias:', error);
    res.status(500).json({ error: 'Error al buscar noticias', details: error.message });
  }
});

// Endpoint para visualizar una página web
router.post('/visualize-webpage', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'Se requiere una URL' });
    }

    console.log(`Visualizando página web: ${url}`);

    // Navegar a la página y obtener snapshot
    const pageData = await visualizeWebPage(url);

    // Devolver los datos
    res.json(pageData);
  } catch (error) {
    console.error('Error al visualizar página web:', error);
    res.status(500).json({ error: 'Error al visualizar página web', details: error.message });
  }
});

// Endpoint para realizar análisis técnico
router.post('/technical-analysis', async (req, res) => {
  try {
    const { symbol, interval = '1d', limit = 30 } = req.body;

    if (!symbol) {
      return res.status(400).json({ error: 'Se requiere un símbolo de criptomoneda' });
    }

    console.log(`Realizando análisis técnico para ${symbol} con intervalo ${interval} y límite ${limit}...`);

    // Realizar análisis técnico
    const analysis = await technicalAnalysisService.performTechnicalAnalysis(
      symbol,
      interval,
      limit
    );

    // Generar una respuesta en lenguaje natural basada en el análisis
    let recommendation = '';
    let explanation = '';

    // Interpretar RSI
    const rsi = analysis.indicators.rsi;
    if (rsi < 30) {
      recommendation += 'El RSI indica condiciones de sobreventa, lo que podría ser una señal de compra. ';
      explanation += `El RSI actual es ${rsi.toFixed(2)}, lo que sugiere que el activo podría estar infravalorado. `;
    } else if (rsi > 70) {
      recommendation += 'El RSI indica condiciones de sobrecompra, lo que podría ser una señal de venta. ';
      explanation += `El RSI actual es ${rsi.toFixed(2)}, lo que sugiere que el activo podría estar sobrevalorado. `;
    } else {
      explanation += `El RSI actual es ${rsi.toFixed(2)}, lo que indica condiciones de mercado neutrales. `;
    }

    // Interpretar MACD
    const macd = analysis.indicators.macd.macd;
    if (macd > 0) {
      explanation += `El MACD es positivo (${macd.toFixed(2)}), lo que indica momentum alcista. `;
    } else if (macd < 0) {
      explanation += `El MACD es negativo (${macd.toFixed(2)}), lo que indica momentum bajista. `;
    }

    // Interpretar Bandas de Bollinger
    const bb = analysis.indicators.bollingerBands;
    const currentPrice = (bb.upper + bb.lower) / 2; // Estimación del precio actual
    if (currentPrice <= bb.lower) {
      recommendation += 'El precio está cerca o por debajo de la banda inferior de Bollinger, lo que podría indicar una oportunidad de compra. ';
    } else if (currentPrice >= bb.upper) {
      recommendation += 'El precio está cerca o por encima de la banda superior de Bollinger, lo que podría indicar una oportunidad de venta. ';
    } else {
      explanation += 'El precio se encuentra dentro de las Bandas de Bollinger, lo que sugiere una volatilidad normal. ';
    }

    // Interpretar patrones
    let bullishPatterns = 0;
    let bearishPatterns = 0;

    Object.entries(analysis.patterns.bullish).forEach(([pattern, values]) => {
      if (values[values.length - 1]) {
        bullishPatterns++;
        explanation += `Se ha detectado el patrón alcista "${pattern}". `;
      }
    });

    Object.entries(analysis.patterns.bearish).forEach(([pattern, values]) => {
      if (values[values.length - 1]) {
        bearishPatterns++;
        explanation += `Se ha detectado el patrón bajista "${pattern}". `;
      }
    });

    if (bullishPatterns > bearishPatterns) {
      recommendation += 'Los patrones de velas sugieren una tendencia alcista. ';
    } else if (bearishPatterns > bullishPatterns) {
      recommendation += 'Los patrones de velas sugieren una tendencia bajista. ';
    }

    // Recomendación final
    let finalRecommendation = '';
    if (analysis.signals && analysis.signals.recommendation) {
      switch (analysis.signals.recommendation) {
        case 'COMPRA':
          finalRecommendation = 'Basado en el análisis técnico, podría ser un buen momento para considerar comprar.';
          break;
        case 'VENTA':
          finalRecommendation = 'Basado en el análisis técnico, podría ser un buen momento para considerar vender.';
          break;
        default:
          finalRecommendation = 'Basado en el análisis técnico, el mercado parece estar en una fase neutral. Considera esperar a señales más claras antes de tomar una decisión.';
      }
    } else {
      // Determinar recomendación basada en los indicadores analizados
      if ((rsi < 40 && macd > 0) || (currentPrice <= bb.lower && bullishPatterns > bearishPatterns)) {
        finalRecommendation = 'Basado en el análisis técnico, podría ser un buen momento para considerar comprar.';
      } else if ((rsi > 60 && macd < 0) || (currentPrice >= bb.upper && bearishPatterns > bullishPatterns)) {
        finalRecommendation = 'Basado en el análisis técnico, podría ser un buen momento para considerar vender.';
      } else {
        finalRecommendation = 'Basado en el análisis técnico, el mercado parece estar en una fase neutral. Considera esperar a señales más claras antes de tomar una decisión.';
      }
    }

    // Disclaimer
    const disclaimer = 'Recuerda que este análisis técnico es solo informativo y no constituye asesoramiento financiero. Los mercados de criptomonedas son altamente volátiles y conllevan riesgos significativos. Siempre realiza tu propia investigación antes de tomar decisiones de inversión.';

    // Construir respuesta completa
    const response = {
      analysis,
      interpretation: {
        recommendation,
        explanation,
        finalRecommendation,
        disclaimer
      },
      naturalLanguageResponse: `# Análisis Técnico de ${symbol.toUpperCase()}\n\n${explanation}\n\n${recommendation}\n\n**Recomendación final:** ${finalRecommendation}\n\n**Disclaimer:** ${disclaimer}`
    };

    res.json(response);
  } catch (error) {
    console.error('Error al realizar análisis técnico:', error);
    res.status(500).json({ error: 'Error al realizar análisis técnico', details: error.message });
  }
});

module.exports = router;
