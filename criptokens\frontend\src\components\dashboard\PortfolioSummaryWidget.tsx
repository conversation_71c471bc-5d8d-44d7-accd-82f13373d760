import React from 'react';
import { PortfolioStats } from '../../types/dashboard';
import '../../styles/dashboard/PortfolioSummaryWidget.css';

interface PortfolioSummaryWidgetProps {
  portfolioStats: PortfolioStats;
  isLoading: boolean;
  onClick: () => void;
}

const PortfolioSummaryWidget: React.FC<PortfolioSummaryWidgetProps> = ({
  portfolioStats,
  isLoading,
  onClick
}) => {
  // Formatear números para mostrar
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  if (isLoading) {
    return (
      <div className="portfolio-summary-widget loading" data-testid="portfolio-summary-loading">
        <div className="widget-header">
          <h3>Tu Portafolio</h3>
        </div>
        <div className="widget-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  // Si no hay activos en el portafolio
  if (portfolioStats.assetCount === 0) {
    return (
      <div className="portfolio-summary-widget empty" data-testid="portfolio-summary-empty">
        <div className="widget-header">
          <h3>Tu Portafolio</h3>
        </div>
        <div className="widget-content">
          <p>No tienes activos en tu portafolio.</p>
          <button className="add-assets-button" onClick={onClick}>
            Añadir Activos
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="portfolio-summary-widget" 
      onClick={onClick}
      data-testid="portfolio-summary-widget"
    >
      <div className="widget-header">
        <h3>Tu Portafolio</h3>
        <span className="asset-count">{portfolioStats.assetCount} activos</span>
      </div>
      <div className="widget-content">
        <div className="portfolio-value">
          <span className="value-label">Valor Total:</span>
          <span className="value-amount">{formatCurrency(portfolioStats.totalValue)}</span>
        </div>
        
        <div className="portfolio-metrics">
          <div className="metric">
            <span className="metric-label">Inversión:</span>
            <span className="metric-value">{formatCurrency(portfolioStats.totalInvestment)}</span>
          </div>
          <div className="metric">
            <span className="metric-label">Ganancia/Pérdida:</span>
            <span className={`metric-value ${portfolioStats.totalProfitLoss >= 0 ? 'positive' : 'negative'}`}>
              {formatCurrency(portfolioStats.totalProfitLoss)} 
              ({portfolioStats.totalProfitLossPercentage.toFixed(2)}%)
            </span>
          </div>
        </div>
        
        <div className="portfolio-allocation">
          <h4>Distribución</h4>
          <div className="allocation-bars">
            {portfolioStats.assetAllocation.map((allocation, index) => (
              <div className="allocation-item" key={index}>
                <div className="allocation-label">
                  <span className="category">{allocation.category}</span>
                  <span className="percentage">{allocation.percentage.toFixed(1)}%</span>
                </div>
                <div className="allocation-bar">
                  <div 
                    className="allocation-fill"
                    style={{ 
                      width: `${allocation.percentage}%`,
                      backgroundColor: getCategoryColor(allocation.category, index)
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="portfolio-risk">
          <span className="risk-label">Nivel de Riesgo:</span>
          <div className="risk-meter">
            <div className="risk-bar">
              <div 
                className="risk-indicator"
                style={{ width: `${(portfolioStats.riskScore / 10) * 100}%` }}
              ></div>
            </div>
            <span className="risk-value">{portfolioStats.riskScore}/10</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Función para generar colores para categorías
const getCategoryColor = (category: string, index: number): string => {
  const colors = [
    '#4361ee', '#3a0ca3', '#7209b7', '#f72585', '#4cc9f0',
    '#4895ef', '#560bad', '#b5179e', '#f15bb5', '#00bbf9'
  ];
  
  // Asignar colores específicos a categorías comunes
  switch (category.toLowerCase()) {
    case 'bitcoin':
    case 'btc':
      return '#f7931a';
    case 'ethereum':
    case 'eth':
      return '#627eea';
    case 'defi':
      return '#7209b7';
    case 'layer1':
      return '#4361ee';
    case 'stablecoin':
      return '#00bbf9';
    default:
      // Usar el índice para seleccionar un color del array
      return colors[index % colors.length];
  }
};

export default PortfolioSummaryWidget;
