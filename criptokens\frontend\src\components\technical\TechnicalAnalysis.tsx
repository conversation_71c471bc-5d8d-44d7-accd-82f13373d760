import React from 'react';
import { <PERSON>, CardContent, Typography, Grid, Divider, Chip, CircularProgress, Box, Button } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import { useTheme } from '@mui/material/styles';
import useTechnicalAnalysis, { TechnicalAnalysisResult } from '../../hooks/useTechnicalAnalysis';

interface TechnicalAnalysisProps {
  symbol: string;
  interval?: string;
  limit?: number;
}

const TechnicalAnalysis: React.FC<TechnicalAnalysisProps> = ({ symbol, interval = '1d', limit = 30 }) => {
  const { data: analysis, loading, error, refetch: refreshAnalysis } = useTechnicalAnalysis({ symbol, interval, limit });
  const theme = useTheme();

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'COMPRA':
        return theme.palette.success.main;
      case 'VENTA':
        return theme.palette.error.main;
      default:
        return theme.palette.info.main;
    }
  };

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'COMPRA':
        return <TrendingUpIcon />;
      case 'VENTA':
        return <TrendingDownIcon />;
      default:
        return <TrendingFlatIcon />;
    }
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString('es-ES', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const getRSIInterpretation = (rsi: number) => {
    if (rsi < 30) {
      return { text: 'Sobreventa', color: theme.palette.success.main };
    } else if (rsi > 70) {
      return { text: 'Sobrecompra', color: theme.palette.error.main };
    } else {
      return { text: 'Neutral', color: theme.palette.info.main };
    }
  };

  const getMACDInterpretation = (macd: number) => {
    if (macd > 0) {
      return { text: 'Alcista', color: theme.palette.success.main };
    } else if (macd < 0) {
      return { text: 'Bajista', color: theme.palette.error.main };
    } else {
      return { text: 'Neutral', color: theme.palette.info.main };
    }
  };

  const countPatterns = (patterns: Pattern) => {
    let bullishCount = 0;
    let bearishCount = 0;

    // Contar patrones alcistas
    Object.values(patterns.bullish).forEach(values => {
      const lastValue = values[values.length - 1];
      if (lastValue) bullishCount++;
    });

    // Contar patrones bajistas
    Object.values(patterns.bearish).forEach(values => {
      const lastValue = values[values.length - 1];
      if (lastValue) bearishCount++;
    });

    return { bullishCount, bearishCount };
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if (!analysis) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <Typography>No hay datos de análisis disponibles</Typography>
      </Box>
    );
  }

  const { indicators, patterns, signals } = analysis;
  const { bullishCount, bearishCount } = countPatterns(patterns);
  const rsiInterpretation = getRSIInterpretation(indicators.rsi);
  const macdInterpretation = getMACDInterpretation(indicators.macd.macd);

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" component="h2">
            Análisis Técnico de {symbol}
          </Typography>
          <Button
            variant="outlined"
            size="small"
            onClick={refreshAnalysis}
            disabled={loading}
          >
            Actualizar
          </Button>
        </Box>

        {/* Recomendación general */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 2,
            mb: 3,
            bgcolor: 'background.paper',
            borderRadius: 1,
            boxShadow: 1
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
            {getRecommendationIcon(signals.recommendation)}
          </Box>
          <Typography variant="h6" component="span" sx={{ color: getRecommendationColor(signals.recommendation) }}>
            Recomendación: {signals.recommendation}
          </Typography>
          <Box sx={{ ml: 2, display: 'flex', gap: 1 }}>
            <Chip
              label={`${signals.buySignals} Compra`}
              size="small"
              color="success"
              variant={signals.buySignals > 0 ? "filled" : "outlined"}
            />
            <Chip
              label={`${signals.sellSignals} Venta`}
              size="small"
              color="error"
              variant={signals.sellSignals > 0 ? "filled" : "outlined"}
            />
            <Chip
              label={`${signals.neutralSignals} Neutral`}
              size="small"
              color="info"
              variant={signals.neutralSignals > 0 ? "filled" : "outlined"}
            />
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Indicadores técnicos */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Indicadores Técnicos
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body1">RSI (14)</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body1" sx={{ mr: 1 }}>
                      {formatNumber(indicators.rsi)}
                    </Typography>
                    <Chip
                      label={rsiInterpretation.text}
                      size="small"
                      sx={{ bgcolor: rsiInterpretation.color, color: 'white' }}
                    />
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body1">MACD</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body1" sx={{ mr: 1 }}>
                      {formatNumber(indicators.macd.macd)}
                    </Typography>
                    <Chip
                      label={macdInterpretation.text}
                      size="small"
                      sx={{ bgcolor: macdInterpretation.color, color: 'white' }}
                    />
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="body1" gutterBottom>Bandas de Bollinger (20, 2)</Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Typography variant="body2">Superior:</Typography>
                  <Typography variant="body2">{formatNumber(indicators.bollingerBands.upper)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Typography variant="body2">Media:</Typography>
                  <Typography variant="body2">{formatNumber(indicators.bollingerBands.middle)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Typography variant="body2">Inferior:</Typography>
                  <Typography variant="body2">{formatNumber(indicators.bollingerBands.lower)}</Typography>
                </Box>
              </Grid>
            </Grid>
          </Grid>

          {/* Patrones y señales */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Patrones y Señales
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body1">Patrones detectados</Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip
                    label={`${bullishCount} Alcistas`}
                    size="small"
                    color="success"
                    variant={bullishCount > 0 ? "filled" : "outlined"}
                  />
                  <Chip
                    label={`${bearishCount} Bajistas`}
                    size="small"
                    color="error"
                    variant={bearishCount > 0 ? "filled" : "outlined"}
                  />
                </Box>
              </Box>
            </Box>

            <Typography variant="body1" gutterBottom>Señales</Typography>

            {signals.details.buy.length > 0 && (
              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" color="success.main" gutterBottom>Señales de compra:</Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                  {signals.details.buy.map((signal, index) => (
                    <li key={index}>
                      <Typography variant="body2">{signal}</Typography>
                    </li>
                  ))}
                </ul>
              </Box>
            )}

            {signals.details.sell.length > 0 && (
              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" color="error.main" gutterBottom>Señales de venta:</Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                  {signals.details.sell.map((signal, index) => (
                    <li key={index}>
                      <Typography variant="body2">{signal}</Typography>
                    </li>
                  ))}
                </ul>
              </Box>
            )}

            {signals.details.neutral.length > 0 && (
              <Box>
                <Typography variant="body2" color="info.main" gutterBottom>Señales neutrales:</Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                  {signals.details.neutral.map((signal, index) => (
                    <li key={index}>
                      <Typography variant="body2">{signal}</Typography>
                    </li>
                  ))}
                </ul>
              </Box>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default TechnicalAnalysis;
