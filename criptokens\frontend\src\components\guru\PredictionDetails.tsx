import React, { useState } from 'react';
import { CryptoPrediction } from '../../services/ai/predictionService';
import { SentimentResult, NewsItem } from '../../services/sentiment/sentimentAnalysis';
import { OnChainMetrics, WhaleTransaction } from '../../services/onchain/etherscanService';
import '../../styles/guru/PredictionDetails.css';

interface PredictionDetailsProps {
  prediction: CryptoPrediction;
  sentimentData?: SentimentResult;
  onChainData?: OnChainMetrics;
}

const PredictionDetails: React.FC<PredictionDetailsProps> = ({
  prediction,
  sentimentData,
  onChainData
}) => {
  const [activeTab, setActiveTab] = useState<'sentiment' | 'onchain' | 'technical'>('sentiment');
  
  // Renderizar análisis de sentimiento
  const renderSentimentAnalysis = () => {
    if (!sentimentData) {
      return (
        <div className="no-data-message">
          <p>No hay datos de sentimiento disponibles</p>
        </div>
      );
    }
    
    return (
      <div className="sentiment-analysis">
        <div className="sentiment-overview">
          <div className="sentiment-gauge">
            <div className="gauge-label">Sentimiento General</div>
            <div className="gauge-container">
              <div 
                className={`gauge-fill ${sentimentData.overallSentiment > 20 ? 'positive' : sentimentData.overallSentiment < -20 ? 'negative' : 'neutral'}`}
                style={{ 
                  transform: `rotate(${(sentimentData.overallSentiment / 100 + 1) * 90}deg)`
                }}
              ></div>
              <div className="gauge-center">
                <span className="gauge-value">{sentimentData.overallSentiment}</span>
              </div>
            </div>
          </div>
          
          <div className="sentiment-sources">
            <div className="sources-label">Fuentes de Noticias</div>
            <div className="sources-chart">
              <div 
                className="sources-positive" 
                style={{ 
                  width: `${sentimentData.sources.positive / (sentimentData.sources.positive + sentimentData.sources.negative + sentimentData.sources.neutral) * 100}%` 
                }}
              >
                {sentimentData.sources.positive}
              </div>
              <div 
                className="sources-neutral" 
                style={{ 
                  width: `${sentimentData.sources.neutral / (sentimentData.sources.positive + sentimentData.sources.negative + sentimentData.sources.neutral) * 100}%` 
                }}
              >
                {sentimentData.sources.neutral}
              </div>
              <div 
                className="sources-negative" 
                style={{ 
                  width: `${sentimentData.sources.negative / (sentimentData.sources.positive + sentimentData.sources.negative + sentimentData.sources.neutral) * 100}%` 
                }}
              >
                {sentimentData.sources.negative}
              </div>
            </div>
            <div className="sources-legend">
              <div className="legend-item">
                <div className="legend-color positive"></div>
                <div className="legend-label">Positivas</div>
              </div>
              <div className="legend-item">
                <div className="legend-color neutral"></div>
                <div className="legend-label">Neutrales</div>
              </div>
              <div className="legend-item">
                <div className="legend-color negative"></div>
                <div className="legend-label">Negativas</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="sentiment-keywords">
          <h4>Palabras Clave</h4>
          <div className="keywords-cloud">
            {sentimentData.keywords.map((keyword, index) => (
              <div 
                key={index} 
                className={`keyword-item ${keyword.sentiment > 20 ? 'positive' : keyword.sentiment < -20 ? 'negative' : 'neutral'}`}
                style={{ fontSize: `${Math.max(0.8, Math.min(1.5, 0.8 + keyword.count / 10))}rem` }}
              >
                {keyword.word}
                <span className="keyword-count">({keyword.count})</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="sentiment-news">
          <h4>Noticias Recientes</h4>
          <div className="news-list">
            {sentimentData.newsItems.slice(0, 5).map((news, index) => (
              <div 
                key={index} 
                className={`news-item ${news.sentiment && news.sentiment > 20 ? 'positive' : news.sentiment && news.sentiment < -20 ? 'negative' : 'neutral'}`}
              >
                <div className="news-title">
                  <a href={news.url} target="_blank" rel="noopener noreferrer">
                    {news.title}
                  </a>
                </div>
                <div className="news-meta">
                  <span className="news-source">{news.source}</span>
                  <span className="news-date">{news.date}</span>
                  {news.sentimentScore && (
                    <span className="news-sentiment">
                      Sentimiento: {news.sentimentScore > 0 ? '+' : ''}{news.sentimentScore}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };
  
  // Renderizar datos on-chain
  const renderOnChainData = () => {
    if (!onChainData) {
      return (
        <div className="no-data-message">
          <p>No hay datos on-chain disponibles</p>
        </div>
      );
    }
    
    return (
      <div className="onchain-analysis">
        <div className="onchain-metrics">
          <div className="metric-card">
            <div className="metric-title">Transacciones Diarias</div>
            <div className="metric-value">{onChainData.dailyTransactions.toLocaleString()}</div>
          </div>
          
          <div className="metric-card">
            <div className="metric-title">Direcciones Activas</div>
            <div className="metric-value">{onChainData.activeAddresses.toLocaleString()}</div>
          </div>
          
          <div className="metric-card">
            <div className="metric-title">Comisión Promedio</div>
            <div className="metric-value">${onChainData.averageFee.toFixed(2)}</div>
          </div>
          
          {onChainData.stakingRate && (
            <div className="metric-card">
              <div className="metric-title">Tasa de Staking</div>
              <div className="metric-value">{onChainData.stakingRate}%</div>
            </div>
          )}
        </div>
        
        <div className="whale-activity">
          <h4>Actividad de Ballenas</h4>
          
          <div className="whale-flows">
            <div className="flow-item inflow">
              <div className="flow-label">Entradas</div>
              <div className="flow-value">${onChainData.whaleInflows.toLocaleString()}</div>
            </div>
            
            <div className="flow-item outflow">
              <div className="flow-label">Salidas</div>
              <div className="flow-value">${onChainData.whaleOutflows.toLocaleString()}</div>
            </div>
            
            <div className="flow-item netflow">
              <div className="flow-label">Flujo Neto</div>
              <div className={`flow-value ${onChainData.netWhaleFlow >= 0 ? 'positive' : 'negative'}`}>
                {onChainData.netWhaleFlow >= 0 ? '+' : ''}{onChainData.netWhaleFlow.toLocaleString()}
              </div>
            </div>
          </div>
          
          <div className="whale-transactions">
            <h5>Transacciones Recientes</h5>
            <div className="transactions-list">
              {onChainData.whaleTransactions.slice(0, 5).map((tx, index) => (
                <div key={index} className={`transaction-item ${tx.isInflow ? 'inflow' : 'outflow'}`}>
                  <div className="transaction-direction">
                    {tx.isInflow ? 'Entrada' : 'Salida'}
                  </div>
                  <div className="transaction-value">
                    ${tx.value.toLocaleString()}
                  </div>
                  <div className="transaction-addresses">
                    <div className="transaction-from" title={tx.from}>
                      De: {tx.from.substring(0, 6)}...{tx.from.substring(tx.from.length - 4)}
                    </div>
                    <div className="transaction-to" title={tx.to}>
                      A: {tx.to.substring(0, 6)}...{tx.to.substring(tx.to.length - 4)}
                    </div>
                  </div>
                  <div className="transaction-time">
                    {new Date(tx.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  // Renderizar análisis técnico
  const renderTechnicalAnalysis = () => {
    return (
      <div className="technical-analysis">
        <div className="technical-signals">
          <h4>Señales Técnicas</h4>
          <div className="signal-item">
            <span className="signal-label">Técnico</span>
            <div className="signal-bar-container">
              <div 
                className={`signal-bar ${prediction.signals.technical > 0 ? 'positive' : prediction.signals.technical < 0 ? 'negative' : 'neutral'}`}
                style={{ 
                  width: `${Math.abs(prediction.signals.technical)}%`,
                  marginLeft: prediction.signals.technical >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.technical)}%`
                }}
              ></div>
              <div className="signal-zero-line"></div>
            </div>
            <span className="signal-value">{prediction.signals.technical > 0 ? '+' : ''}{prediction.signals.technical}</span>
          </div>
          
          <div className="signal-item">
            <span className="signal-label">Sentimiento</span>
            <div className="signal-bar-container">
              <div 
                className={`signal-bar ${prediction.signals.sentiment > 0 ? 'positive' : prediction.signals.sentiment < 0 ? 'negative' : 'neutral'}`}
                style={{ 
                  width: `${Math.abs(prediction.signals.sentiment)}%`,
                  marginLeft: prediction.signals.sentiment >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.sentiment)}%`
                }}
              ></div>
              <div className="signal-zero-line"></div>
            </div>
            <span className="signal-value">{prediction.signals.sentiment > 0 ? '+' : ''}{prediction.signals.sentiment}</span>
          </div>
          
          <div className="signal-item">
            <span className="signal-label">On-Chain</span>
            <div className="signal-bar-container">
              <div 
                className={`signal-bar ${prediction.signals.onChain > 0 ? 'positive' : prediction.signals.onChain < 0 ? 'negative' : 'neutral'}`}
                style={{ 
                  width: `${Math.abs(prediction.signals.onChain)}%`,
                  marginLeft: prediction.signals.onChain >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.onChain)}%`
                }}
              ></div>
              <div className="signal-zero-line"></div>
            </div>
            <span className="signal-value">{prediction.signals.onChain > 0 ? '+' : ''}{prediction.signals.onChain}</span>
          </div>
          
          <div className="signal-item">
            <span className="signal-label">Fundamental</span>
            <div className="signal-bar-container">
              <div 
                className={`signal-bar ${prediction.signals.fundamental > 0 ? 'positive' : prediction.signals.fundamental < 0 ? 'negative' : 'neutral'}`}
                style={{ 
                  width: `${Math.abs(prediction.signals.fundamental)}%`,
                  marginLeft: prediction.signals.fundamental >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.fundamental)}%`
                }}
              ></div>
              <div className="signal-zero-line"></div>
            </div>
            <span className="signal-value">{prediction.signals.fundamental > 0 ? '+' : ''}{prediction.signals.fundamental}</span>
          </div>
        </div>
        
        <div className="prediction-reasoning">
          <h4>Análisis del Guru</h4>
          <ul className="reasoning-list">
            {prediction.reasoning.map((reason, index) => (
              <li key={index} className="reasoning-item">{reason}</li>
            ))}
          </ul>
        </div>
      </div>
    );
  };
  
  return (
    <div className="prediction-details">
      <div className="details-tabs">
        <button 
          className={activeTab === 'sentiment' ? 'active' : ''} 
          onClick={() => setActiveTab('sentiment')}
        >
          Análisis de Sentimiento
        </button>
        <button 
          className={activeTab === 'onchain' ? 'active' : ''} 
          onClick={() => setActiveTab('onchain')}
        >
          Datos On-Chain
        </button>
        <button 
          className={activeTab === 'technical' ? 'active' : ''} 
          onClick={() => setActiveTab('technical')}
        >
          Análisis Técnico
        </button>
      </div>
      
      <div className="details-content">
        {activeTab === 'sentiment' && renderSentimentAnalysis()}
        {activeTab === 'onchain' && renderOnChainData()}
        {activeTab === 'technical' && renderTechnicalAnalysis()}
      </div>
    </div>
  );
};

export default PredictionDetails;
