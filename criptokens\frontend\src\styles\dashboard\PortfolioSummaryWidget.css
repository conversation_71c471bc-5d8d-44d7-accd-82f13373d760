.portfolio-summary-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.portfolio-summary-widget:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary-transparent);
}

.portfolio-summary-widget.loading .widget-content,
.portfolio-summary-widget.empty .widget-content {
  padding: 1.5rem;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.widget-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.asset-count {
  font-size: 0.8rem;
  color: var(--text-secondary);
  background-color: var(--color-surface-light);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.widget-content {
  padding: 1rem;
}

.portfolio-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.value-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.value-amount {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.portfolio-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.metric-value {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.metric-value.positive {
  color: var(--color-positive);
}

.metric-value.negative {
  color: var(--color-negative);
}

.portfolio-allocation {
  margin-bottom: 1rem;
}

.portfolio-allocation h4 {
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.allocation-bars {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.allocation-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.allocation-label {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
}

.category {
  color: var(--text-secondary);
}

.percentage {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.allocation-bar {
  height: 6px;
  background-color: var(--color-surface-light);
  border-radius: 3px;
  overflow: hidden;
}

.allocation-fill {
  height: 100%;
  border-radius: 3px;
  background-color: var(--color-primary);
}

.portfolio-risk {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.risk-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.risk-meter {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.risk-bar {
  flex: 1;
  height: 6px;
  background-color: var(--color-surface-light);
  border-radius: 3px;
  overflow: hidden;
}

.risk-indicator {
  height: 100%;
  background: linear-gradient(90deg, #4cc9f0, #4361ee, #3a0ca3);
  border-radius: 3px;
}

.risk-value {
  font-size: 0.8rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.add-assets-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.add-assets-button:hover {
  background-color: var(--color-primary-light);
}

.skeleton-loading {
  width: 100%;
  height: 150px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .portfolio-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .value-amount {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .widget-header {
    padding: 0.75rem;
  }
  
  .widget-content {
    padding: 0.75rem;
  }
}
