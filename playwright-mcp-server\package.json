{"name": "playwright-mcp-server", "version": "1.0.0", "description": "<PERSON><PERSON>or <PERSON> para Playwright en Criptokens", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["playwright", "mcp", "criptok<PERSON>"], "author": "Criptokens Team", "license": "ISC", "dependencies": {"@playwright/mcp": "^0.0.14", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "playwright": "^1.52.0", "uuid": "^11.1.0", "ws": "^8.18.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}