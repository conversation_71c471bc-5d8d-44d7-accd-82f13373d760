"""
Utility functions for MCP tools and ADK agents.
"""
import os
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from google.adk.runtime import InvocationContext

# Common cryptocurrency mappings
CRYPTO_MAPPINGS = {
    "bitcoin": "bitcoin",
    "btc": "bitcoin",
    "ethereum": "ethereum",
    "eth": "ethereum",
    "binance coin": "binancecoin",
    "bnb": "binancecoin",
    "cardano": "cardano",
    "ada": "cardano",
    "solana": "solana",
    "sol": "solana",
    "xrp": "ripple",
    "dogecoin": "dogecoin",
    "doge": "dogecoin",
    "polkadot": "polkadot",
    "dot": "polkadot",
    "tether": "tether",
    "usdt": "tether",
    "usd coin": "usd-coin",
    "usdc": "usd-coin"
}

def extract_crypto_id(query: str) -> str:
    """
    Extract cryptocurrency ID from query.
    
    Args:
        query: User query
        
    Returns:
        Cryptocurrency ID
    """
    query_lower = query.lower()
    
    # Check for exact matches
    for name_lower, crypto_id in CRYPTO_MAPPINGS.items():
        if name_lower in query_lower:
            return crypto_id
    
    # Default to Bitcoin if no match found
    return "bitcoin"

def extract_timeframe(query: str) -> Tuple[int, str]:
    """
    Extract timeframe from query.
    
    Args:
        query: User query
        
    Returns:
        Tuple of (days, interval)
    """
    query_lower = query.lower()
    
    if "year" in query_lower or "365" in query_lower:
        return 365, "daily"
    elif "6 month" in query_lower or "180" in query_lower:
        return 180, "daily"
    elif "3 month" in query_lower or "90" in query_lower:
        return 90, "daily"
    elif "month" in query_lower or "30" in query_lower:
        return 30, "daily"
    elif "week" in query_lower or "7" in query_lower:
        return 7, "daily"
    elif "day" in query_lower or "24" in query_lower:
        return 1, "hourly"
    
    # Default to 7 days
    return 7, "daily"

def is_prediction_query(query: str) -> bool:
    """
    Determine if query is about price prediction.
    
    Args:
        query: User query
        
    Returns:
        True if query is about price prediction
    """
    prediction_keywords = [
        "predict", "prediction", "forecast", "future price", "will go",
        "price target", "where will", "how high", "how low", "potential",
        "outlook", "projection", "estimate", "expected price"
    ]
    
    query_lower = query.lower()
    
    for keyword in prediction_keywords:
        if keyword in query_lower:
            return True
    
    return False

def is_technical_query(query: str) -> bool:
    """
    Determine if query is about technical analysis.
    
    Args:
        query: User query
        
    Returns:
        True if query is about technical analysis
    """
    technical_keywords = [
        "technical", "chart", "pattern", "indicator", "moving average",
        "support", "resistance", "rsi", "macd", "bollinger", "trend",
        "volume", "price action", "fibonacci", "oscillator", "momentum"
    ]
    
    query_lower = query.lower()
    
    for keyword in technical_keywords:
        if keyword in query_lower:
            return True
    
    return False

def is_sentiment_query(query: str) -> bool:
    """
    Determine if query is about sentiment analysis.
    
    Args:
        query: User query
        
    Returns:
        True if query is about sentiment analysis
    """
    sentiment_keywords = [
        "sentiment", "news", "social", "twitter", "reddit", "media",
        "feeling", "perception", "opinion", "fear", "greed", "index",
        "market sentiment", "bullish", "bearish", "optimism", "pessimism"
    ]
    
    query_lower = query.lower()
    
    for keyword in sentiment_keywords:
        if keyword in query_lower:
            return True
    
    return False

def is_onchain_query(query: str) -> bool:
    """
    Determine if query is about on-chain analysis.
    
    Args:
        query: User query
        
    Returns:
        True if query is about on-chain analysis
    """
    onchain_keywords = [
        "on-chain", "onchain", "blockchain", "whale", "transaction",
        "wallet", "address", "holder", "holding", "accumulation",
        "distribution", "gas", "fee", "network", "activity", "transfer"
    ]
    
    query_lower = query.lower()
    
    for keyword in onchain_keywords:
        if keyword in query_lower:
            return True
    
    return False

def store_in_session(ctx: InvocationContext, key: str, value: Any) -> None:
    """
    Store a value in the session state.
    
    Args:
        ctx: Invocation context
        key: Key to store the value under
        value: Value to store
    """
    ctx.session.state[key] = value

def get_from_session(ctx: InvocationContext, key: str, default: Any = None) -> Any:
    """
    Get a value from the session state.
    
    Args:
        ctx: Invocation context
        key: Key to get the value from
        default: Default value to return if key is not found
        
    Returns:
        Value from the session state
    """
    return ctx.session.state.get(key, default)
