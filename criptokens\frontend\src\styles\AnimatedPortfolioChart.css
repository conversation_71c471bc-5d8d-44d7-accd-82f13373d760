.animated-portfolio-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(20, 20, 50, 0.5);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(64, 220, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
  text-align: center;
}

.chart-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.pie-chart {
  position: relative;
}

.chart-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  clip: rect(0, 50%, 100%, 0);
  transform-origin: center;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.chart-segment:hover {
  transform-origin: center;
  transform: scale(1.05) rotate(var(--rotation));
  z-index: 10;
}

.chart-segment::after {
  content: attr(data-tooltip);
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(10, 10, 30, 0.9);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 100;
  pointer-events: none;
}

.chart-segment:hover::after {
  opacity: 1;
  visibility: visible;
}

.segment-inner {
  position: absolute;
  width: 100%;
  height: 100%;
  clip: rect(0, 100%, 100%, 50%);
  transform-origin: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.chart-label {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.7);
  pointer-events: none;
  white-space: nowrap;
}

.total-value-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(10, 10, 30, 0.7);
  padding: 15px;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(64, 220, 255, 0.3);
  z-index: 5;
}

.total-value {
  font-size: 16px;
  font-weight: 700;
  color: white;
}

.total-label {
  font-size: 12px;
  color: #a0a0d0;
  margin-top: 5px;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  width: 100%;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  background: rgba(30, 30, 60, 0.5);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  transition: all 0.3s;
}

.legend-item:hover {
  background: rgba(30, 30, 60, 0.8);
  transform: translateY(-2px);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  margin-right: 8px;
}

.legend-label {
  margin-right: 8px;
  color: #e0e0ff;
}

.legend-value {
  font-weight: 600;
  color: white;
}

/* Efectos de brillo */
.animated-portfolio-chart::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(64, 220, 255, 0.1) 0%,
    rgba(64, 220, 255, 0) 70%
  );
  animation: rotate 10s linear infinite;
  pointer-events: none;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .legend-container {
    flex-direction: column;
    align-items: center;
  }
  
  .legend-item {
    width: 100%;
    justify-content: space-between;
  }
  
  .total-value-container {
    width: 80px;
    height: 80px;
  }
  
  .total-value {
    font-size: 14px;
  }
  
  .total-label {
    font-size: 10px;
  }
}
