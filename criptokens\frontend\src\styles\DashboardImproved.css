/* Estilos mejorados para el dashboard */

/* Estructura general */
.dashboard-content {
  padding: clamp(0.5rem, 2vw, var(--spacing-xl));
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  gap: clamp(0.75rem, 2vw, var(--spacing-xl));
  max-width: 100%;
  box-sizing: border-box;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  border-bottom: var(--border-width) solid var(--border-color);
  padding-bottom: var(--spacing-md);
}

.dashboard-title {
  font-size: clamp(1.5rem, 3vw, var(--font-size-2xl));
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
  position: relative;
}

.dashboard-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  border-radius: 2px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Barra de búsqueda */
.search-bar {
  display: flex;
  align-items: center;
  background-color: rgba(15, 20, 40, 0.5);
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.search-bar:focus-within {
  border-color: rgba(0, 224, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.search-bar input {
  background: transparent;
  border: none;
  outline: none;
  color: #fff;
  width: 200px;
  padding: 0;
  font-size: 14px;
}

.search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button {
  background-color: rgba(15, 20, 40, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:hover, .search-button:hover {
  color: rgba(0, 224, 255, 1);
}

/* Grid mejorado */
.dashboard-grid-improved {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-auto-rows: minmax(min-content, max-content);
  gap: 20px;
  grid-template-areas:
    "market market market market market market top-crypto top-crypto top-crypto top-crypto top-crypto top-crypto"
    "chart chart chart chart chart chart chart chart chart chart chart chart"
    "agent agent agent agent agent agent agent agent agent agent agent agent";
  height: auto;
  min-height: 100%;
  width: 100%;
  overflow: visible;
}

/* Nuevo Grid Mejorado con Filas */
.dashboard-grid-enhanced {
  display: flex;
  flex-direction: column;
  gap: clamp(0.75rem, 2vw, var(--spacing-xl));
  width: 100%;
  max-width: 100%;
  height: auto;
  min-height: 0;
}

.dashboard-row {
  display: flex;
  gap: clamp(0.5rem, 1vw, var(--spacing-lg));
  width: 100%;
}

/* Fila superior: Cartera y Mercado (máxima prominencia) */
.primary-row {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: clamp(0.5rem, 1vw, var(--spacing-lg));
  width: 100%;
  min-height: 0;
}

.primary-widget {
  min-height: auto;
  height: auto;
  max-height: 40vh;
  box-shadow: var(--shadow-lg);
  border-radius: var(--border-radius-lg);
  border: var(--border-width) solid rgba(255, 255, 255, 0.15);
  background-color: var(--color-surface-light);
  transition: var(--transition-normal);
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.primary-widget:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

/* Fila media: Tabla de criptomonedas (importancia secundaria) */
.secondary-row {
  width: 100%;
}

.secondary-widget {
  width: 100%;
  min-height: auto;
  height: auto;
  max-height: 50vh;
  box-shadow: var(--shadow-md);
  border-radius: var(--border-radius-md);
  border: var(--border-width) solid var(--border-color);
  background-color: var(--color-surface);
  transition: var(--transition-normal);
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.secondary-widget:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Fila inferior: Gráfico y Gurú (menor prominencia) */
.tertiary-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: clamp(0.5rem, 1vw, var(--spacing-lg));
  width: 100%;
  min-height: 0;
}

.tertiary-widget {
  min-height: auto;
  height: auto;
  max-height: 50vh;
  box-shadow: var(--shadow-sm);
  border-radius: var(--border-radius-md);
  border: var(--border-width) solid var(--border-color);
  background-color: var(--color-surface-dark);
  transition: var(--transition-normal);
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.tertiary-widget:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.portfolio-summary-container {
  min-height: auto;
  height: auto;
  max-height: 50vh;
  overflow: auto;
}

.guru-insight-container {
  min-height: auto;
  height: auto;
  max-height: 50vh;
  overflow: auto;
}

.enhanced-crypto-table-container-wrapper {
  width: 100%;
  min-height: auto;
  height: auto;
  max-height: 50vh;
  overflow: auto;
}

/* Tarjetas */
.market-overview-card {
  grid-area: market;
  background-color: rgba(23, 27, 54, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  min-height: 250px;
  display: flex;
  flex-direction: column;
}

.top-cryptos-card {
  grid-area: top-crypto;
  background-color: rgba(23, 27, 54, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  min-height: 250px;
  display: flex;
  flex-direction: column;
}

.crypto-chart-card {
  grid-area: chart;
  background-color: rgba(23, 27, 54, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 500px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.agent-card {
  grid-area: agent;
  background-color: rgba(23, 27, 54, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 200px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Encabezados de tarjetas */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: var(--border-width) solid var(--border-color);
  background-color: rgba(15, 20, 40, 0.3);
}

.primary-widget .card-header {
  background-color: rgba(15, 20, 40, 0.4);
  border-bottom: var(--border-width) solid rgba(255, 255, 255, 0.15);
  border-top-left-radius: var(--border-radius-lg);
  border-top-right-radius: var(--border-radius-lg);
}

.card-header h3 {
  margin: 0;
  font-size: clamp(1rem, 1.5vw, var(--font-size-lg));
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  position: relative;
}

.primary-widget .card-header h3 {
  font-size: clamp(1.125rem, 2vw, var(--font-size-xl));
  font-weight: var(--font-weight-bold);
}

.primary-widget .card-header h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  border-radius: 1px;
}

.last-updated {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background-color: rgba(0, 0, 0, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.view-all {
  background: none;
  border: none;
  color: rgba(0, 224, 255, 0.8);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.view-all:hover {
  color: rgba(0, 224, 255, 1);
  text-decoration: underline;
}

.chat-button {
  background-color: rgba(0, 224, 255, 0.2);
  border: 1px solid rgba(0, 224, 255, 0.5);
  border-radius: 6px;
  padding: 6px 12px;
  color: rgba(0, 224, 255, 1);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.chat-button:hover {
  background-color: rgba(0, 224, 255, 0.3);
}

/* Estadísticas del mercado */
.market-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  flex: 1;
}

.primary-widget .market-stats-grid {
  padding: var(--spacing-xl);
}

.market-stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-sm);
  border: var(--border-width) solid var(--border-color);
  transition: var(--transition-normal);
}

.primary-widget .market-stat-item {
  background-color: rgba(15, 20, 40, 0.4);
  border: var(--border-width) solid rgba(255, 255, 255, 0.15);
}

.market-stat-item:hover {
  background-color: rgba(15, 20, 40, 0.5);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: var(--color-primary);
}

.primary-widget .stat-icon {
  width: 48px;
  height: 48px;
  font-size: var(--font-size-xl);
  background-color: rgba(0, 0, 0, 0.4);
}

.market-cap-icon {
  background-color: rgba(64, 153, 255, 0.15);
  color: var(--color-primary);
}

.volume-icon {
  background-color: rgba(0, 230, 118, 0.15);
  color: var(--color-positive);
}

.dominance-icon {
  background-color: rgba(255, 171, 0, 0.15);
  color: var(--color-warning);
}

.change-icon {
  background-color: rgba(255, 255, 255, 0.1);
}

.primary-widget .market-cap-icon {
  background-color: rgba(64, 153, 255, 0.2);
}

.primary-widget .volume-icon {
  background-color: rgba(0, 230, 118, 0.2);
}

.primary-widget .dominance-icon {
  background-color: rgba(255, 171, 0, 0.2);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.primary-widget .stat-label {
  color: var(--text-secondary);
}

.stat-value {
  font-size: clamp(0.875rem, 1.5vw, var(--font-size-lg));
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.primary-widget .stat-value {
  font-size: clamp(1rem, 2vw, var(--font-size-xl));
  font-weight: var(--font-weight-bold);
}

/* Lista de criptomonedas */
.crypto-list-table {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 12px 20px;
  background-color: rgba(15, 20, 40, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
}

.table-body {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}

.table-row:hover {
  background-color: rgba(0, 224, 255, 0.05);
}

.table-row.selected {
  background-color: rgba(0, 224, 255, 0.1);
  border-left: 3px solid rgba(0, 224, 255, 0.8);
}

.col-crypto {
  display: flex;
  align-items: center;
  gap: 10px;
}

.crypto-icon-small {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.crypto-name-container {
  display: flex;
  flex-direction: column;
}

.crypto-name {
  font-weight: 500;
  color: #fff;
  font-size: 14px;
}

.crypto-symbol {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.col-price {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #fff;
}

.col-change {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
}

/* Gráfico de criptomoneda */
.crypto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.crypto-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.crypto-icon {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  object-fit: contain !important;
  vertical-align: middle !important;
}

.tertiary-widget .crypto-icon {
  width: 16px;
  height: 16px;
}

.crypto-title h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.crypto-title .crypto-symbol {
  color: var(--text-tertiary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-sm);
}

.crypto-price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.price-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.chart-controls {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: var(--border-width) solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.1);
}

.time-range-selector {
  display: flex;
  gap: var(--spacing-sm);
}

.time-range-selector button {
  background: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: var(--font-size-xs);
  transition: var(--transition-fast);
}

.time-range-selector button:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  border-color: var(--border-color-hover);
}

.time-range-selector button.active {
  background-color: rgba(64, 153, 255, 0.2);
  border-color: rgba(64, 153, 255, 0.5);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.chart-container {
  flex: 1;
  padding: clamp(0.5rem, 1vw, var(--spacing-lg));
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  height: auto;
  max-height: 50vh;
  background-color: rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.crypto-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: rgba(0, 0, 0, 0.05);
  border-top: var(--border-width) solid var(--border-color);
  margin-top: auto;
}

.crypto-stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-sm);
  border: var(--border-width) solid var(--border-color);
}

.no-crypto-selected {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-tertiary);
  text-align: center;
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-lg);
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-lg);
  color: var(--color-primary);
  opacity: 0.3;
}

.no-crypto-selected h3 {
  margin: 0 0 var(--spacing-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

.no-crypto-selected p {
  margin: 0;
  max-width: 300px;
  line-height: 1.5;
  font-size: var(--font-size-sm);
}

/* Panel del agente */
.agent-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.agent-message-container {
  background-color: rgba(15, 20, 40, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.agent-message {
  color: #fff;
  line-height: 1.5;
}

.quick-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.quick-actions button {
  background-color: rgba(15, 20, 40, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 15px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-actions button:hover {
  background-color: rgba(0, 224, 255, 0.1);
  border-color: rgba(0, 224, 255, 0.3);
  color: rgba(0, 224, 255, 1);
}

/* Indicador de carga */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  gap: var(--spacing-md);
  color: var(--text-secondary);
  min-height: 200px;
}

.loading-spinner {
  width: 36px;
  height: 36px;
  border: 3px solid rgba(64, 153, 255, 0.1);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Indicador de carga global */
.global-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 40px;
  text-align: center;
  color: #fff;
}

.loading-spinner-large {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(0, 224, 255, 0.1);
  border-top-color: rgba(0, 224, 255, 0.8);
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin-bottom: 20px;
}

.global-loading-container h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 500;
}

.global-loading-container p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard-grid-improved {
    grid-template-columns: repeat(6, 1fr);
    grid-template-areas:
      "market market market top-crypto top-crypto top-crypto"
      "chart chart chart chart chart chart"
      "agent agent agent agent agent agent";
  }

  /* Nuevo grid responsivo */
  .primary-row {
    grid-template-columns: 1fr 1fr;
    gap: clamp(0.5rem, 1vw, var(--spacing-lg));
  }

  .tertiary-row {
    grid-template-columns: 1fr 1fr;
    gap: clamp(0.5rem, 1vw, var(--spacing-lg));
  }

  .primary-widget,
  .secondary-widget,
  .tertiary-widget {
    width: 100%;
    height: auto;
    max-height: 60vh;
  }

  .market-stats-grid, .crypto-stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .enhanced-crypto-table-container .table-wrapper {
    max-height: 50vh;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: clamp(0.5rem, 1vw, var(--spacing-md));
    gap: clamp(0.75rem, 1.5vw, var(--spacing-lg));
  }

  .dashboard-grid-improved {
    grid-template-columns: 1fr;
    grid-template-areas:
      "market"
      "top-crypto"
      "chart"
      "agent";
    gap: clamp(0.75rem, 1.5vw, var(--spacing-lg));
  }

  .dashboard-row {
    gap: clamp(0.5rem, 1vw, var(--spacing-md));
    flex-direction: column;
  }

  .primary-row,
  .tertiary-row {
    grid-template-columns: 1fr;
    gap: clamp(0.5rem, 1vw, var(--spacing-md));
  }

  .market-stats-grid, .crypto-stats-grid {
    grid-template-columns: 1fr;
    padding: clamp(0.5rem, 1vw, var(--spacing-md));
    gap: clamp(0.25rem, 0.5vw, var(--spacing-md));
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: clamp(0.5rem, 1vw, var(--spacing-md));
    margin-bottom: clamp(0.5rem, 1vw, var(--spacing-md));
  }

  .header-actions {
    width: 100%;
  }

  .search-bar {
    flex: 1;
  }

  /* Ajustes adicionales para móviles */
  .crypto-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: clamp(0.25rem, 0.5vw, var(--spacing-sm));
  }

  .crypto-stat-item {
    padding: clamp(0.125rem, 0.25vw, var(--spacing-xs));
  }

  .stat-icon {
    width: 28px;
    height: 28px;
    font-size: clamp(0.75rem, 1vw, var(--font-size-sm));
  }

  .stat-label {
    font-size: clamp(0.625rem, 0.75vw, var(--font-size-xs));
  }

  .stat-value {
    font-size: clamp(0.75rem, 1vw, var(--font-size-sm));
  }

  .card-header {
    padding: clamp(0.25rem, 0.5vw, var(--spacing-sm)) clamp(0.5rem, 1vw, var(--spacing-md));
  }

  .card-header h3 {
    font-size: clamp(0.875rem, 1.25vw, var(--font-size-md));
  }

  .primary-widget .card-header h3 {
    font-size: clamp(1rem, 1.5vw, var(--font-size-lg));
  }

  .primary-widget,
  .secondary-widget,
  .tertiary-widget {
    max-height: none;
    height: auto;
    min-height: 0;
    overflow: auto;
  }

  .enhanced-crypto-table-container .table-wrapper {
    max-height: none;
    height: auto;
  }

  .chart-container {
    min-height: 150px;
    max-height: none;
    height: auto;
  }
}
