/**
 * Script para iniciar todos los servicios de Criptokens
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para iniciar un proceso
function startProcess(name, command, args, cwd, color = colors.reset) {
  log(`Iniciando ${name}...`, color);
  
  const process = spawn(command, args, {
    cwd,
    stdio: 'inherit',
    shell: true
  });
  
  process.on('error', (error) => {
    log(`Error al iniciar ${name}: ${error.message}`, colors.red);
  });
  
  process.on('close', (code) => {
    if (code !== 0) {
      log(`${name} se cerró con código: ${code}`, colors.yellow);
    } else {
      log(`${name} se cerró correctamente`, colors.green);
    }
  });
  
  return process;
}

// Directorios
const rootDir = __dirname;
const frontendDir = path.join(rootDir, 'frontend');
const backendDir = path.join(rootDir, 'backend');
const cryptoMcpServerDir = path.join(path.dirname(rootDir), 'crypto-mcp-server');
const playwrightMcpServerDir = path.join(path.dirname(rootDir), 'playwright-mcp-server');
const braveSearchServerPath = path.join(path.dirname(rootDir), 'brave-search-server.js');

// Cargar la configuración de MCP
let mcpConfig = {};
try {
  const configPath = path.join(rootDir, 'mcp-config.json');
  if (fs.existsSync(configPath)) {
    const configData = fs.readFileSync(configPath, 'utf8');
    mcpConfig = JSON.parse(configData);
  }
} catch (error) {
  log(`Error al cargar la configuración de MCP: ${error.message}`, colors.red);
}

// Verificar que los directorios existen
const missingDirs = [];

if (!fs.existsSync(frontendDir)) {
  missingDirs.push(`Frontend: ${frontendDir}`);
}

if (!fs.existsSync(backendDir)) {
  missingDirs.push(`Backend: ${backendDir}`);
}

if (!fs.existsSync(cryptoMcpServerDir)) {
  missingDirs.push(`Crypto MCP Server: ${cryptoMcpServerDir}`);
}

// Si hay directorios faltantes, mostrar advertencia pero continuar
if (missingDirs.length > 0) {
  log('ADVERTENCIA: Algunos directorios no se encontraron:', colors.yellow);
  missingDirs.forEach(dir => log(`- ${dir}`, colors.yellow));
  log('Se intentará iniciar los servicios disponibles.', colors.yellow);
}

log('Iniciando servidores en el orden correcto...', colors.bright);

// Paso 1: Iniciar el servidor MCP de crypto (puerto 3101)
let cryptoMcpProcess = null;
if (fs.existsSync(cryptoMcpServerDir)) {
  cryptoMcpProcess = startProcess('Crypto MCP Server (Puerto 3101)', 'node', ['http-server.js'], cryptoMcpServerDir, colors.magenta);
  log('Esperando 2 segundos para que el servidor Crypto MCP inicie...', colors.dim);
  setTimeout(() => { log('Continuando con el siguiente servidor...', colors.dim); }, 2000);
} else {
  log(`Advertencia: No se encontró el directorio crypto-mcp-server en ${cryptoMcpServerDir}`, colors.yellow);
}

// Esperar un momento para que el servidor MCP de crypto se inicie
setTimeout(() => {
  // Paso 2: Iniciar el servidor Brave Search (puerto 3102)
  let braveSearchProcess = null;
  if (fs.existsSync(braveSearchServerPath)) {
    braveSearchProcess = startProcess('Brave Search Server (Puerto 3102)', 'node', [braveSearchServerPath], path.dirname(rootDir), colors.blue);
    log('Esperando 2 segundos para que el servidor Brave Search inicie...', colors.dim);
    setTimeout(() => { log('Continuando con el siguiente servidor...', colors.dim); }, 2000);
  } else {
    log(`Advertencia: No se encontró el archivo brave-search-server.js en ${braveSearchServerPath}`, colors.yellow);
  }

  // Paso 3: Iniciar el servidor Playwright MCP (puerto 3103)
  let playwrightMcpProcess = null;
  if (fs.existsSync(playwrightMcpServerDir)) {
    playwrightMcpProcess = startProcess('Playwright MCP Server (Puerto 3103)', 'node', ['dist/server.js'], playwrightMcpServerDir, colors.cyan);
    log('Esperando 2 segundos para que el servidor Playwright MCP inicie...', colors.dim);
    setTimeout(() => { log('Continuando con el siguiente servidor...', colors.dim); }, 2000);
  } else {
    log(`Advertencia: No se encontró el directorio playwright-mcp-server en ${playwrightMcpServerDir}`, colors.yellow);
  }

  // Paso 4: Iniciar el servidor Etherscan MCP (puerto 3104)
  let etherscanMcpProcess = null;
  if (fs.existsSync(path.join(rootDir, 'etherscan-mcp-server.js'))) {
    etherscanMcpProcess = startProcess('Etherscan MCP Server (Puerto 3104)', 'node', ['etherscan-mcp-server.js'], rootDir, colors.green);
    log('Esperando 2 segundos para que el servidor Etherscan MCP inicie...', colors.dim);
    setTimeout(() => { log('Continuando con el siguiente servidor...', colors.dim); }, 2000);
  } else {
    log(`Advertencia: No se encontró el archivo etherscan-mcp-server.js en ${rootDir}`, colors.yellow);
  }

  // Esperar un momento para que los servidores MCP se inicien
  setTimeout(() => {
    // Paso 5: Iniciar el backend (puerto 3001)
    const backendProcess = startProcess('Backend Server (Puerto 3001)', 'node', ['src/server.js'], backendDir, colors.blue);
    log('Esperando 3 segundos para que el servidor Backend inicie...', colors.dim);
    setTimeout(() => { log('Continuando con el siguiente servidor...', colors.dim); }, 3000);

    // Esperar un momento para que el backend se inicie
    setTimeout(() => {
      // Paso 6: Iniciar el frontend (puerto 5173)
      const frontendProcess = startProcess('Frontend Server (Puerto 5173)', 'npm', ['run', 'dev'], frontendDir, colors.yellow);

      // Iniciar servidores MCP adicionales desde la configuración
      const mcpServers = mcpConfig.mcpServers || {};
      const mcpProcesses = {};

      // Iniciar Context7 MCP si está configurado
      if (mcpServers.context7) {
        log('Iniciando Context7 MCP Server...', colors.magenta);
        mcpProcesses.context7 = spawn(mcpServers.context7.command, mcpServers.context7.args, {
          stdio: 'inherit',
          shell: true
        });
      }

      // Manejar la terminación del proceso principal
      process.on('SIGINT', () => {
        log('\nTerminando todos los procesos...', colors.yellow);
        
        if (frontendProcess) frontendProcess.kill();
        if (backendProcess) backendProcess.kill();
        if (cryptoMcpProcess) cryptoMcpProcess.kill();
        if (braveSearchProcess) braveSearchProcess.kill();
        if (playwrightMcpProcess) playwrightMcpProcess.kill();
        if (etherscanMcpProcess) etherscanMcpProcess.kill();

        // Terminar todos los servidores MCP adicionales
        Object.entries(mcpProcesses).forEach(([name, process]) => {
          log(`Terminando servidor MCP ${name}...`, colors.yellow);
          process.kill();
        });

        setTimeout(() => {
          log('Todos los procesos han sido terminados', colors.green);
          process.exit(0);
        }, 1000);
      });

      // Mostrar mensaje de éxito
      log(`
¡Todos los servicios iniciados correctamente!

Servicios disponibles:
- Frontend: http://localhost:5173
- Backend: http://localhost:3001
- Crypto MCP Server: http://localhost:3101
- Brave Search Server: http://localhost:3102
- Playwright MCP Server: http://localhost:3103
- Etherscan MCP Server: http://localhost:3104
${mcpServers.context7 ? '- Context7 MCP Server: (puerto dinámico)' : ''}

Presiona Ctrl+C para terminar todos los servidores.
`, colors.green + colors.bright);
    }, 3000); // Esperar 3 segundos para que el backend se inicie
  }, 2000); // Esperar 2 segundos para que los servidores MCP se inicien
}, 2000); // Esperar 2 segundos para que el servidor MCP de crypto se inicie
