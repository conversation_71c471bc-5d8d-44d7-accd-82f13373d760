.portfolio-summary-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  border: var(--border-width) solid var(--border-color);
  transition: var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.portfolio-summary-widget:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.portfolio-summary-widget .widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: var(--border-width) solid var(--border-color);
}

.portfolio-summary-widget .widget-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.portfolio-summary-widget .last-updated {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.portfolio-summary-widget .widget-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 1.25rem;
  flex: 1;
}

.portfolio-summary-widget .portfolio-value {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-lg);
  background-color: rgba(64, 153, 255, 0.05);
  border-radius: var(--border-radius-sm);
  border: var(--border-width) solid rgba(64, 153, 255, 0.2);
  margin-bottom: var(--spacing-md);
  position: relative;
  overflow: hidden;
}

.portfolio-summary-widget .portfolio-value::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
}

.portfolio-summary-widget .value-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: var(--font-weight-medium);
}

.portfolio-summary-widget .value-amount {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: var(--spacing-xs);
  letter-spacing: -0.5px;
}

.portfolio-summary-widget .portfolio-change,
.portfolio-summary-widget .portfolio-assets-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-sm);
  border: var(--border-width) solid var(--border-color);
  height: 100%;
}

.portfolio-summary-widget .change-label,
.portfolio-summary-widget .count-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.portfolio-summary-widget .change-amount,
.portfolio-summary-widget .count-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-top: var(--spacing-xs);
}

.portfolio-summary-widget .change-amount.positive {
  color: var(--color-positive);
}

.portfolio-summary-widget .change-amount.negative {
  color: var(--color-negative);
}

.portfolio-summary-widget .change-percentage {
  font-size: 0.875rem;
  margin-left: 0.25rem;
}

.portfolio-summary-widget .count-value {
  color: #fff;
}

.portfolio-summary-widget .widget-footer {
  margin-top: 1.25rem;
  text-align: center;
}

.portfolio-summary-widget .view-portfolio-button {
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-fast);
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.portfolio-summary-widget .view-portfolio-button:hover {
  background: linear-gradient(90deg, var(--color-primary-light), var(--color-primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(64, 153, 255, 0.3);
}

.portfolio-summary-widget.loading .widget-content,
.portfolio-summary-widget.error .widget-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
}

.portfolio-summary-widget .loading-indicator {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9375rem;
}

.portfolio-summary-widget .error-message {
  color: #ff5252;
  font-size: 0.9375rem;
  text-align: center;
}

@media (max-width: 768px) {
  .portfolio-summary-widget .widget-content {
    grid-template-columns: 1fr;
    grid-gap: 0.75rem;
  }
}
