const express = require('express');
const router = express.Router();
const { performFundamentalAnalysis } = require('../services/fundamentalAnalysis.service');

/**
 * @route POST /api/guru/fundamental-analysis
 * @description Realiza un análisis fundamental de una criptomoneda
 * @param {string} symbol - Símbolo de la criptomoneda (ej. BTC, ETH)
 * @access Public
 */
router.post('/', async (req, res) => {
  try {
    const { symbol } = req.body;
    
    if (!symbol) {
      return res.status(400).json({ error: 'Se requiere el símbolo de la criptomoneda' });
    }
    
    console.log(`Realizando análisis fundamental para ${symbol}...`);
    
    // Realizar análisis fundamental
    const analysis = await performFundamentalAnalysis(symbol);
    
    // Generar respuesta en lenguaje natural
    const naturalLanguageResponse = generateNaturalLanguageResponse(analysis);
    
    res.json({
      analysis,
      naturalLanguageResponse
    });
  } catch (error) {
    console.error('Error al realizar análisis fundamental:', error);
    res.status(500).json({ error: 'Error al realizar análisis fundamental' });
  }
});

/**
 * Genera una respuesta en lenguaje natural basada en el análisis fundamental
 * @param {Object} analysis - Análisis fundamental
 * @returns {string} - Respuesta en lenguaje natural
 */
function generateNaturalLanguageResponse(analysis) {
  const { general, market, tokenomics, onChain, development, analysis: swotAnalysis } = analysis;
  
  // Introducción
  let response = `# Análisis Fundamental de ${general.name} (${general.symbol})\n\n`;
  
  // Descripción general
  response += `## Descripción General\n\n${general.description}\n\n`;
  
  // Datos de mercado
  response += `## Datos de Mercado\n\n`;
  response += `- **Capitalización de Mercado**: ${formatCurrency(market.marketCap)}\n`;
  response += `- **Volumen (24h)**: ${formatCurrency(market.volume24h)}\n`;
  response += `- **Ranking**: #${market.marketCapRank}\n`;
  
  if (market.circulatingSupply) {
    response += `- **Suministro Circulante**: ${formatNumber(market.circulatingSupply)} ${general.symbol}\n`;
  }
  
  if (market.totalSupply) {
    response += `- **Suministro Total**: ${formatNumber(market.totalSupply)} ${general.symbol}\n`;
  }
  
  if (market.maxSupply) {
    response += `- **Suministro Máximo**: ${formatNumber(market.maxSupply)} ${general.symbol}\n`;
  }
  
  response += `\n`;
  
  // Tokenomics
  response += `## Tokenomics\n\n`;
  
  if (tokenomics.circulatingSupplyPercent) {
    response += `- **Porcentaje en Circulación**: ${tokenomics.circulatingSupplyPercent}%\n`;
  }
  
  if (tokenomics.inflation) {
    response += `- **Tasa de Inflación**: ${tokenomics.inflation}\n`;
  }
  
  response += `- **Tipo de Token**: ${tokenomics.tokenType}\n`;
  
  if (tokenomics.tokenStandard !== 'Native') {
    response += `- **Estándar**: ${tokenomics.tokenStandard}\n`;
  }
  
  response += `\n`;
  
  // Datos on-chain si están disponibles
  if (onChain && (onChain.activeAddresses || onChain.transactions24h)) {
    response += `## Datos On-Chain\n\n`;
    
    if (onChain.activeAddresses) {
      response += `- **Direcciones Activas**: ${formatNumber(onChain.activeAddresses)}\n`;
    }
    
    if (onChain.transactions24h) {
      response += `- **Transacciones (24h)**: ${formatNumber(onChain.transactions24h)}\n`;
    }
    
    if (onChain.averageFee) {
      response += `- **Comisión Promedio**: ${onChain.averageFee}\n`;
    }
    
    if (onChain.hashRate) {
      response += `- **Hash Rate**: ${onChain.hashRate}\n`;
    }
    
    if (onChain.totalValueLocked) {
      response += `- **Valor Total Bloqueado (TVL)**: ${onChain.totalValueLocked}\n`;
    }
    
    response += `\n`;
  }
  
  // Datos de desarrollo si están disponibles
  if (development && (development.githubStars || development.githubCommits)) {
    response += `## Desarrollo\n\n`;
    
    if (development.githubStars) {
      response += `- **Estrellas en GitHub**: ${formatNumber(development.githubStars)}\n`;
    }
    
    if (development.githubForks) {
      response += `- **Forks en GitHub**: ${formatNumber(development.githubForks)}\n`;
    }
    
    if (development.githubContributors) {
      response += `- **Contribuidores**: ${formatNumber(development.githubContributors)}\n`;
    }
    
    if (development.githubCommits) {
      response += `- **Commits**: ${formatNumber(development.githubCommits)}\n`;
    }
    
    if (development.lastUpdate) {
      response += `- **Última Actualización**: ${formatDate(development.lastUpdate)}\n`;
    }
    
    response += `\n`;
  }
  
  // Análisis SWOT
  response += `## Análisis SWOT\n\n`;
  
  response += `### Fortalezas\n\n`;
  swotAnalysis.strengths.forEach(strength => {
    response += `- ${strength}\n`;
  });
  response += `\n`;
  
  response += `### Debilidades\n\n`;
  swotAnalysis.weaknesses.forEach(weakness => {
    response += `- ${weakness}\n`;
  });
  response += `\n`;
  
  response += `### Oportunidades\n\n`;
  swotAnalysis.opportunities.forEach(opportunity => {
    response += `- ${opportunity}\n`;
  });
  response += `\n`;
  
  response += `### Amenazas\n\n`;
  swotAnalysis.threats.forEach(threat => {
    response += `- ${threat}\n`;
  });
  response += `\n`;
  
  // Resumen
  response += `## Resumen\n\n${swotAnalysis.summary}\n\n`;
  
  // Disclaimer
  response += `**Disclaimer**: Este análisis es solo informativo y no constituye asesoramiento financiero. Los mercados de criptomonedas son altamente volátiles y conllevan riesgos significativos. Siempre realiza tu propia investigación antes de tomar decisiones de inversión.`;
  
  return response;
}

/**
 * Formatea un número como moneda
 * @param {number} value - Valor a formatear
 * @returns {string} - Valor formateado
 */
function formatCurrency(value) {
  if (!value) return 'N/A';
  
  // Formatear como moneda
  if (value >= 1000000000000) {
    return `$${(value / 1000000000000).toFixed(2)} T`;
  } else if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(2)} B`;
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(2)} M`;
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(2)} K`;
  } else {
    return `$${value.toFixed(2)}`;
  }
}

/**
 * Formatea un número
 * @param {number} value - Valor a formatear
 * @returns {string} - Valor formateado
 */
function formatNumber(value) {
  if (!value) return 'N/A';
  
  // Formatear número
  if (value >= 1000000000) {
    return `${(value / 1000000000).toFixed(2)} B`;
  } else if (value >= 1000000) {
    return `${(value / 1000000).toFixed(2)} M`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(2)} K`;
  } else {
    return value.toString();
  }
}

/**
 * Formatea una fecha
 * @param {string} dateString - Fecha en formato ISO
 * @returns {string} - Fecha formateada
 */
function formatDate(dateString) {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

module.exports = router;
