.analysis-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--text-primary);
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.back-link {
  color: var(--text-secondary);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--color-primary);
}

.analysis-header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--text-primary);
}

.analysis-actions {
  display: flex;
  gap: 1rem;
}

.crypto-selector {
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: var(--text-primary);
  font-size: 0.9rem;
  min-width: 200px;
}

.analysis-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.25rem;
  color: var(--text-secondary);
  font-size: 1rem;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: rgba(123, 97, 255, 0.1);
}

.tab-button.active {
  color: var(--color-primary);
  border-bottom: 3px solid var(--color-primary);
  font-weight: 500;
}

/* Technical Analysis Section */
.technical-analysis {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

.chart-container {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.chart-placeholder {
  height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--text-secondary);
}

.placeholder-chart {
  width: 100%;
  height: 200px;
  margin-top: 2rem;
  position: relative;
  border-bottom: 1px solid var(--border-color);
}

.chart-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, var(--color-primary-transparent), transparent);
  clip-path: polygon(0% 80%, 10% 60%, 20% 40%, 30% 50%, 40% 30%, 50% 40%, 60% 35%, 70% 50%, 80% 20%, 90% 30%, 100% 10%);
}

.chart-indicators {
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.indicator {
  width: 1px;
  height: 10px;
  background-color: var(--border-color);
}

.indicators-panel {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.indicators-panel h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.indicator-card {
  background-color: var(--color-surface-light);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.indicator-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.indicator-value {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.indicator-status {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

.indicator-status.positive {
  background-color: rgba(0, 200, 83, 0.1);
  color: #00c853;
}

.indicator-status.negative {
  background-color: rgba(255, 82, 82, 0.1);
  color: #ff5252;
}

.indicator-status.neutral {
  background-color: rgba(255, 171, 0, 0.1);
  color: #ffab00;
}

/* Fundamental Analysis Section */
.fundamental-analysis {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.fundamental-analysis h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1.5rem;
}

.metric-card {
  background-color: var(--color-surface-light);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.metric-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.metric-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Calculators Section */
.calculators {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.calculators h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.calculators-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.calculator-card {
  background-color: var(--color-surface-light);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.calculator-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.calculator-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.calculator-card p {
  margin: 0 0 1.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.calculator-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.calculator-button:hover {
  background-color: var(--color-primary-dark);
}

/* Comparison Tool Section */
.comparison-tool {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.comparison-tool h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.comparison-selectors {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin: 2rem 0;
}

.comparison-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 40%;
}

.comparison-selector label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.comparison-selector select {
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-size: 1rem;
}

.comparison-vs {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.compare-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: block;
  margin: 0 auto 2rem;
}

.compare-button:hover {
  background-color: var(--color-primary-dark);
}

.comparison-placeholder {
  text-align: center;
  color: var(--text-secondary);
  padding: 3rem 0;
  border: 1px dashed var(--border-color);
  border-radius: 8px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .technical-analysis {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .analysis-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .analysis-tabs {
    flex-wrap: wrap;
  }
  
  .tab-button {
    flex: 1 0 calc(50% - 0.5rem);
  }
  
  .indicators-grid,
  .calculators-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .comparison-selectors {
    flex-direction: column;
    gap: 1rem;
  }
  
  .comparison-selector {
    width: 100%;
  }
}
