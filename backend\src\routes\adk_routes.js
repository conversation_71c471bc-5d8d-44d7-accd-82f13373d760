/**
 * ADK Routes for Criptokens Backend
 * 
 * This module provides API routes to interact with the ADK agents.
 */

const express = require('express');
const router = express.Router();
const adkIntegration = require('../adk_integration');

// Start ADK API server when this module is loaded
let adkServerProcess = null;
adkIntegration.startAdkApiServer()
    .then(process => {
        adkServerProcess = process;
        console.log('ADK API server started successfully');
    })
    .catch(err => {
        console.error('Failed to start ADK API server:', err);
    });

// Middleware to handle errors
const asyncHandler = fn => (req, res, next) =>
    Promise.resolve(fn(req, res, next)).catch(next);

/**
 * @route POST /api/adk/guru
 * @desc Query the Guru Cripto agent
 * @access Public
 */
router.post('/guru', asyncHandler(async (req, res) => {
    const { question } = req.body;
    
    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }
    
    const response = await adkIntegration.queryGuruAgent(question);
    res.json(response);
}));

/**
 * @route POST /api/adk/technical
 * @desc Query the Technical Analysis agent
 * @access Public
 */
router.post('/technical', asyncHandler(async (req, res) => {
    const { question } = req.body;
    
    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }
    
    const response = await adkIntegration.queryTechnicalAgent(question);
    res.json(response);
}));

/**
 * @route POST /api/adk/sentiment
 * @desc Query the Sentiment Analysis agent
 * @access Public
 */
router.post('/sentiment', asyncHandler(async (req, res) => {
    const { question } = req.body;
    
    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }
    
    const response = await adkIntegration.querySentimentAgent(question);
    res.json(response);
}));

/**
 * @route POST /api/adk/onchain
 * @desc Query the On-Chain Analysis agent
 * @access Public
 */
router.post('/onchain', asyncHandler(async (req, res) => {
    const { question } = req.body;
    
    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }
    
    const response = await adkIntegration.queryOnchainAgent(question);
    res.json(response);
}));

/**
 * @route POST /api/adk/analysis
 * @desc Generate a comprehensive analysis for a cryptocurrency
 * @access Public
 */
router.post('/analysis', asyncHandler(async (req, res) => {
    const { cryptoName, timeframe } = req.body;
    
    if (!cryptoName) {
        return res.status(400).json({ error: 'Cryptocurrency name is required' });
    }
    
    const response = await adkIntegration.generateComprehensiveAnalysis(
        cryptoName,
        timeframe || '7d'
    );
    
    res.json(response);
}));

/**
 * @route POST /api/adk/prediction
 * @desc Generate a price prediction for a cryptocurrency
 * @access Public
 */
router.post('/prediction', asyncHandler(async (req, res) => {
    const { cryptoName, timeframe } = req.body;
    
    if (!cryptoName) {
        return res.status(400).json({ error: 'Cryptocurrency name is required' });
    }
    
    const response = await adkIntegration.generatePricePrediction(
        cryptoName,
        timeframe || '7d'
    );
    
    res.json(response);
}));

// Cleanup when the server is shutting down
process.on('SIGINT', () => {
    if (adkServerProcess && adkServerProcess.process) {
        console.log('Shutting down ADK API server...');
        process.kill(-adkServerProcess.process.pid);
    }
    process.exit(0);
});

module.exports = router;
