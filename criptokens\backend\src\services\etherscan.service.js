/**
 * Servicio para interactuar con la API de Etherscan
 *
 * Este servicio proporciona métodos para obtener datos on-chain de la red Ethereum
 * utilizando la API de Etherscan.
 */

const axios = require('axios');
require('dotenv').config();

// API Key para Etherscan
const ETHERSCAN_API_KEY = process.env.ETHERSCAN_API_KEY || '**********************************';

// URL base de la API de Etherscan
const ETHERSCAN_API_URL = 'https://api.etherscan.io/api';

// Sistema de caché simple para reducir llamadas a la API
const cache = {
  data: {},
  timestamps: {},
  // Tiempo de caché en milisegundos (5 minutos)
  cacheDuration: 5 * 60 * 1000
};

/**
 * Genera datos simulados para desarrollo cuando la API de Etherscan no está disponible
 * @param {string} module - <PERSON>ó<PERSON>lo de la API
 * @param {string} action - Acción a realizar
 * @param {Object} params - Parámetros adicionales
 * @returns {Object} - Datos simulados
 */
function generateMockData(module, action, params = {}) {
  // Datos simulados para diferentes endpoints
  const mockData = {
    // Precio de ETH
    'stats/ethprice': {
      status: '1',
      message: 'OK',
      result: {
        ethbtc: '0.0190012483232663',
        ethbtc_timestamp: '1745392324',
        ethusd: '1776.83276396216',
        ethusd_timestamp: '1745392324'
      }
    },

    // Estadísticas de ETH
    'stats/ethsupply': {
      status: '1',
      message: 'OK',
      result: '1***********000000000000000'
    },

    // Estadísticas de ETH2
    'stats/ethsupply2': {
      status: '1',
      message: 'OK',
      result: {
        EthSupply: '1***********000000000000000',
        Eth2Staking: '25000000000000000000000000',
        BurntFees: '3000000000000000000000000',
        ValidatorCount: '800000',
        TotalEth2Supply: '145000000000000000000000000',
        ActiveValidatorCount: '750000',
        PendingValidatorCount: '50000'
      }
    },

    // Precio del gas
    'gastracker/gasoracle': {
      status: '1',
      message: 'OK',
      result: {
        SafeGasPrice: '20',
        ProposeGasPrice: '25',
        FastGasPrice: '30',
        suggestBaseFee: '19.5',
        gasUsedRatio: '0.5,0.6,0.7,0.55,0.45'
      }
    },

    // Balance de dirección
    'account/balance': {
      status: '1',
      message: 'OK',
      result: '1000000000000000000' // 1 ETH
    },

    // Transacciones de dirección
    'account/txlist': {
      status: '1',
      message: 'OK',
      result: [
        {
          blockNumber: '********',
          timeStamp: '**********',
          hash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
          from: '******************************************',
          to: '******************************************',
          value: '1000000000000000000',
          gas: '21000',
          gasPrice: '***********',
          isError: '0',
          txreceipt_status: '1',
          input: '0x',
          contractAddress: '',
          cumulativeGasUsed: '21000',
          gasUsed: '21000',
          confirmations: '1000000'
        },
        {
          blockNumber: '********',
          timeStamp: '**********',
          hash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
          from: '******************************************',
          to: '******************************************',
          value: '500000000000000000',
          gas: '21000',
          gasPrice: '***********',
          isError: '0',
          txreceipt_status: '1',
          input: '0x',
          contractAddress: '',
          cumulativeGasUsed: '21000',
          gasUsed: '21000',
          confirmations: '1000000'
        }
      ]
    },

    // Transferencias de tokens ERC20
    'account/tokentx': {
      status: '1',
      message: 'OK',
      result: [
        {
          blockNumber: '********',
          timeStamp: '**********',
          hash: '0x2345678901abcdef2345678901abcdef2345678901abcdef2345678901abcdef',
          from: '******************************************',
          to: '******************************************',
          value: '1000000000000000000',
          tokenName: 'Lido DAO Token',
          tokenSymbol: 'LDO',
          tokenDecimal: '18',
          contractAddress: '******************************************',
          confirmations: '1000000'
        }
      ]
    },

    // Código fuente de contrato
    'contract/getsourcecode': {
      status: '1',
      message: 'OK',
      result: [
        {
          SourceCode: 'contract Token { ... }',
          ABI: '[{"constant":true,"inputs":[],"name":"name","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"}]',
          ContractName: 'Token',
          CompilerVersion: 'v0.8.0+commit.c7dfd78e',
          OptimizationUsed: '1',
          Runs: '200',
          ConstructorArguments: '',
          EVMVersion: 'Default',
          Library: '',
          LicenseType: 'MIT',
          Proxy: '0',
          Implementation: '',
          SwarmSource: ''
        }
      ]
    },

    // ABI de contrato
    'contract/getabi': {
      status: '1',
      message: 'OK',
      result: '[{"constant":true,"inputs":[],"name":"name","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"}]'
    },

    // Información de token
    'token/tokeninfo': {
      status: '1',
      message: 'OK',
      result: {
        contractAddress: '******************************************',
        tokenName: 'Lido DAO Token',
        symbol: 'LDO',
        divisor: '1000000000000000000',
        tokenType: 'ERC20',
        totalSupply: '1000000000000000000000000000',
        blueCheckmark: 'true',
        description: 'Lido DAO Token',
        website: 'https://lido.fi/',
        email: '<EMAIL>',
        blog: 'https://blog.lido.fi/',
        reddit: 'https://reddit.com/r/LidoFinance',
        slack: '',
        facebook: '',
        twitter: 'https://twitter.com/LidoFinance',
        bitcointalk: '',
        github: 'https://github.com/lidofinance',
        telegram: 'https://t.me/lidofinance',
        wechat: '',
        linkedin: '',
        discord: 'https://discord.gg/lido',
        whitepaper: 'https://lido.fi/static/Lido:Ethereum-Liquid-Staking.pdf',
        tokenPriceUSD: '2.50'
      }
    },

    // Suministro de token
    'stats/tokensupply': {
      status: '1',
      message: 'OK',
      result: '1000000000000000000000000000'
    }
  };

  // Construir la clave para buscar en los datos simulados
  const key = `${module}/${action}`;

  // Devolver datos simulados si existen, o un objeto genérico si no
  if (mockData[key]) {
    return mockData[key];
  } else {
    console.log(`No hay datos simulados para ${key}, devolviendo datos genéricos`);
    return {
      status: '1',
      message: 'OK',
      result: 'Datos simulados genéricos para desarrollo'
    };
  }
}

/**
 * Función genérica para hacer peticiones a la API de Etherscan
 * @param {string} module - Módulo de la API (account, contract, transaction, etc.)
 * @param {string} action - Acción a realizar
 * @param {Object} params - Parámetros adicionales
 * @returns {Promise<Object>} - Respuesta de la API
 */
async function fetchFromEtherscan(module, action, params = {}) {
  try {
    // Construir la URL de la petición
    const queryParams = new URLSearchParams({
      module,
      action,
      apikey: ETHERSCAN_API_KEY,
      ...params
    });

    const url = `${ETHERSCAN_API_URL}?${queryParams.toString()}`;

    // Verificar si tenemos datos en caché
    const cacheKey = url;
    if (cache.data[cacheKey] &&
        (Date.now() - cache.timestamps[cacheKey] < cache.cacheDuration)) {
      console.log(`Usando datos en caché para: ${url}`);
      return cache.data[cacheKey];
    }

    console.log(`Fetching data from Etherscan: ${url}`);

    // Realizar la petición
    const response = await axios.get(url, {
      timeout: 10000 // 10 segundos de timeout
    });

    // Verificar si la respuesta es correcta
    if (response.data.status === '1') {
      // Guardar en caché
      cache.data[cacheKey] = response.data;
      cache.timestamps[cacheKey] = Date.now();

      return response.data;
    } else {
      console.error(`Error en la petición a Etherscan: ${response.data.message || response.data.result}`);

      // Generar datos simulados para desarrollo
      console.log(`Generando datos simulados para: ${module}/${action}`);
      return generateMockData(module, action, params);
    }
  } catch (error) {
    console.error('Error al obtener datos de Etherscan:', error.message);

    // Generar datos simulados para desarrollo
    console.log(`Generando datos simulados para: ${module}/${action}`);
    return generateMockData(module, action, params);
  }
}

/**
 * Obtiene el balance de ETH de una dirección
 * @param {string} address - Dirección Ethereum
 * @returns {Promise<string>} - Balance en ETH
 */
async function getAddressBalance(address) {
  try {
    const data = await fetchFromEtherscan('account', 'balance', {
      address,
      tag: 'latest'
    });

    // Convertir de wei a ETH
    const balanceInWei = data.result;
    const balanceInEth = balanceInWei / 1e18;

    return balanceInEth.toString();
  } catch (error) {
    console.error(`Error al obtener balance de ${address}:`, error);
    throw error;
  }
}

/**
 * Obtiene las transacciones normales de una dirección
 * @param {string} address - Dirección Ethereum
 * @param {number} startblock - Bloque inicial
 * @param {number} endblock - Bloque final
 * @param {number} page - Número de página
 * @param {number} offset - Número de resultados por página
 * @param {string} sort - Ordenación (asc o desc)
 * @returns {Promise<Array>} - Lista de transacciones
 */
async function getAddressTransactions(address, startblock = 0, endblock = ********, page = 1, offset = 10, sort = 'desc') {
  try {
    const data = await fetchFromEtherscan('account', 'txlist', {
      address,
      startblock,
      endblock,
      page,
      offset,
      sort
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener transacciones de ${address}:`, error);
    throw error;
  }
}

/**
 * Obtiene las transacciones internas de una dirección
 * @param {string} address - Dirección Ethereum
 * @param {number} startblock - Bloque inicial
 * @param {number} endblock - Bloque final
 * @param {number} page - Número de página
 * @param {number} offset - Número de resultados por página
 * @param {string} sort - Ordenación (asc o desc)
 * @returns {Promise<Array>} - Lista de transacciones internas
 */
async function getAddressInternalTransactions(address, startblock = 0, endblock = ********, page = 1, offset = 10, sort = 'desc') {
  try {
    const data = await fetchFromEtherscan('account', 'txlistinternal', {
      address,
      startblock,
      endblock,
      page,
      offset,
      sort
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener transacciones internas de ${address}:`, error);
    throw error;
  }
}

/**
 * Obtiene las transacciones ERC20 de una dirección
 * @param {string} address - Dirección Ethereum
 * @param {string} contractaddress - Dirección del contrato (opcional)
 * @param {number} page - Número de página
 * @param {number} offset - Número de resultados por página
 * @param {string} sort - Ordenación (asc o desc)
 * @returns {Promise<Array>} - Lista de transacciones ERC20
 */
async function getAddressERC20Transfers(address, contractaddress = null, page = 1, offset = 10, sort = 'desc') {
  try {
    const params = {
      address,
      page,
      offset,
      sort
    };

    if (contractaddress) {
      params.contractaddress = contractaddress;
    }

    const data = await fetchFromEtherscan('account', 'tokentx', params);

    return data.result;
  } catch (error) {
    console.error(`Error al obtener transferencias ERC20 de ${address}:`, error);
    throw error;
  }
}

/**
 * Obtiene el código fuente de un contrato verificado
 * @param {string} address - Dirección del contrato
 * @returns {Promise<Object>} - Código fuente y metadatos del contrato
 */
async function getContractSourceCode(address) {
  try {
    const data = await fetchFromEtherscan('contract', 'getsourcecode', {
      address
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener código fuente del contrato ${address}:`, error);
    throw error;
  }
}

/**
 * Obtiene el ABI de un contrato verificado
 * @param {string} address - Dirección del contrato
 * @returns {Promise<string>} - ABI del contrato
 */
async function getContractABI(address) {
  try {
    const data = await fetchFromEtherscan('contract', 'getabi', {
      address
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener ABI del contrato ${address}:`, error);
    throw error;
  }
}

/**
 * Obtiene el precio actual de ETH en USD
 * @returns {Promise<number>} - Precio de ETH en USD
 */
async function getEthPrice() {
  try {
    const data = await fetchFromEtherscan('stats', 'ethprice');

    return {
      ethusd: parseFloat(data.result.ethusd),
      ethbtc: parseFloat(data.result.ethbtc),
      timestamp: parseInt(data.result.ethusd_timestamp)
    };
  } catch (error) {
    console.error('Error al obtener precio de ETH:', error);
    throw error;
  }
}

/**
 * Obtiene estadísticas de la red Ethereum
 * @returns {Promise<Object>} - Estadísticas de la red
 */
async function getEthStats() {
  try {
    const data = await fetchFromEtherscan('stats', 'ethsupply');
    const data2 = await fetchFromEtherscan('stats', 'ethprice');

    return {
      supply: parseFloat(data.result) / 1e18,
      price: parseFloat(data2.result.ethusd),
      marketCap: (parseFloat(data.result) / 1e18) * parseFloat(data2.result.ethusd)
    };
  } catch (error) {
    console.error('Error al obtener estadísticas de ETH:', error);
    throw error;
  }
}

/**
 * Obtiene información sobre un bloque específico
 * @param {number} blockno - Número de bloque
 * @returns {Promise<Object>} - Información del bloque
 */
async function getBlockInfo(blockno) {
  try {
    const data = await fetchFromEtherscan('proxy', 'eth_getBlockByNumber', {
      tag: `0x${blockno.toString(16)}`,
      boolean: 'true'
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener información del bloque ${blockno}:`, error);
    throw error;
  }
}

/**
 * Obtiene información sobre una transacción específica
 * @param {string} txhash - Hash de la transacción
 * @returns {Promise<Object>} - Información de la transacción
 */
async function getTransactionInfo(txhash) {
  try {
    const data = await fetchFromEtherscan('proxy', 'eth_getTransactionByHash', {
      txhash
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener información de la transacción ${txhash}:`, error);
    throw error;
  }
}

/**
 * Obtiene el recibo de una transacción
 * @param {string} txhash - Hash de la transacción
 * @returns {Promise<Object>} - Recibo de la transacción
 */
async function getTransactionReceipt(txhash) {
  try {
    const data = await fetchFromEtherscan('proxy', 'eth_getTransactionReceipt', {
      txhash
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener recibo de la transacción ${txhash}:`, error);
    throw error;
  }
}

/**
 * Obtiene el historial de gas de la red Ethereum
 * @returns {Promise<Array>} - Historial de gas
 */
async function getGasOracle() {
  try {
    const data = await fetchFromEtherscan('gastracker', 'gasoracle');

    return data.result;
  } catch (error) {
    console.error('Error al obtener oracle de gas:', error);
    throw error;
  }
}

/**
 * Obtiene información sobre un token ERC20
 * @param {string} contractaddress - Dirección del contrato
 * @returns {Promise<Object>} - Información del token
 */
async function getTokenInfo(contractaddress) {
  try {
    const data = await fetchFromEtherscan('token', 'tokeninfo', {
      contractaddress
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener información del token ${contractaddress}:`, error);
    throw error;
  }
}

/**
 * Obtiene el suministro total de un token ERC20
 * @param {string} contractaddress - Dirección del contrato
 * @returns {Promise<string>} - Suministro total del token
 */
async function getTokenSupply(contractaddress) {
  try {
    const data = await fetchFromEtherscan('stats', 'tokensupply', {
      contractaddress
    });

    return data.result;
  } catch (error) {
    console.error(`Error al obtener suministro del token ${contractaddress}:`, error);
    throw error;
  }
}

/**
 * Obtiene información sobre los validadores de Ethereum 2.0
 * @returns {Promise<Object>} - Información de validadores
 */
async function getEth2Validators() {
  try {
    const data = await fetchFromEtherscan('stats', 'ethsupply2');

    return {
      validatorCount: parseInt(data.result.ValidatorCount),
      totalEthStaked: parseFloat(data.result.TotalEth2Supply) / 1e18,
      activeValidators: parseInt(data.result.ActiveValidatorCount),
      pendingValidators: parseInt(data.result.PendingValidatorCount)
    };
  } catch (error) {
    console.error('Error al obtener información de validadores ETH2:', error);
    throw error;
  }
}

/**
 * Obtiene información sobre los protocolos DeFi más importantes
 * @returns {Promise<Array>} - Lista de protocolos DeFi con sus direcciones
 */
async function getTopDefiProtocols() {
  // Esta es una lista estática de los principales protocolos DeFi y sus contratos principales
  // En una implementación real, esto podría obtenerse de una API o base de datos
  return [
    {
      name: "Lido",
      symbol: "LDO",
      type: "Staking",
      contractAddress: "******************************************", // stETH contract
      tokenAddress: "******************************************"  // LDO token
    },
    {
      name: "Rocket Pool",
      symbol: "RPL",
      type: "Staking",
      contractAddress: "******************************************", // rETH contract
      tokenAddress: "******************************************"  // RPL token
    },
    {
      name: "Aave",
      symbol: "AAVE",
      type: "Lending",
      contractAddress: "******************************************", // Aave protocol
      tokenAddress: "******************************************"  // AAVE token
    },
    {
      name: "Uniswap",
      symbol: "UNI",
      type: "DEX",
      contractAddress: "******************************************", // Uniswap V3 Factory
      tokenAddress: "******************************************"  // UNI token
    },
    {
      name: "Compound",
      symbol: "COMP",
      type: "Lending",
      contractAddress: "******************************************", // Compound protocol
      tokenAddress: "******************************************"  // COMP token
    },
    {
      name: "MakerDAO",
      symbol: "MKR",
      type: "Stablecoin",
      contractAddress: "******************************************", // MakerDAO protocol
      tokenAddress: "******************************************"  // MKR token
    },
    {
      name: "Curve",
      symbol: "CRV",
      type: "DEX",
      contractAddress: "0xD533a949740bb3306d119CC777fa900bA034cd52", // Curve protocol
      tokenAddress: "0xD533a949740bb3306d119CC777fa900bA034cd52"  // CRV token
    }
  ];
}

/**
 * Obtiene información sobre un protocolo DeFi específico
 * @param {string} protocolName - Nombre del protocolo (Lido, Rocket Pool, etc.)
 * @returns {Promise<Object>} - Información del protocolo
 */
async function getDefiProtocolInfo(protocolName) {
  try {
    const protocols = await getTopDefiProtocols();
    const protocol = protocols.find(p => p.name.toLowerCase() === protocolName.toLowerCase());

    if (!protocol) {
      throw new Error(`Protocolo ${protocolName} no encontrado`);
    }

    // Obtener información del token
    const tokenInfo = await getTokenInfo(protocol.tokenAddress);

    // Obtener balance del contrato principal
    const contractBalance = await getAddressBalance(protocol.contractAddress);

    return {
      ...protocol,
      tokenInfo,
      contractBalance
    };
  } catch (error) {
    console.error(`Error al obtener información del protocolo ${protocolName}:`, error);
    throw error;
  }
}

/**
 * Analiza el impacto de The Merge en un protocolo de staking
 * @param {string} protocolName - Nombre del protocolo (Lido, Rocket Pool)
 * @returns {Promise<Object>} - Análisis del impacto
 */
async function analyzeStakingProtocolPostMerge(protocolName) {
  try {
    // Obtener información del protocolo
    const protocol = await getDefiProtocolInfo(protocolName);

    // Obtener información de validadores ETH2
    const validators = await getEth2Validators();

    // Obtener transacciones recientes del contrato
    const transactions = await getAddressTransactions(protocol.contractAddress, 0, ********, 1, 50);

    // Calcular actividad post-merge (The Merge ocurrió en el bloque 15537394)
    const MERGE_BLOCK = 15537394;
    const postMergeTransactions = transactions.filter(tx => parseInt(tx.blockNumber) > MERGE_BLOCK);
    const preMergeTransactions = transactions.filter(tx => parseInt(tx.blockNumber) <= MERGE_BLOCK);

    // Calcular cambio en la actividad
    const activityChange = postMergeTransactions.length / (preMergeTransactions.length || 1);

    return {
      protocol,
      validators,
      postMergeActivity: {
        transactionCount: postMergeTransactions.length,
        activityChangeRatio: activityChange,
        percentageChange: (activityChange - 1) * 100
      }
    };
  } catch (error) {
    console.error(`Error al analizar impacto post-merge en ${protocolName}:`, error);
    throw error;
  }
}

module.exports = {
  getAddressBalance,
  getAddressTransactions,
  getAddressInternalTransactions,
  getAddressERC20Transfers,
  getContractSourceCode,
  getContractABI,
  getEthPrice,
  getEthStats,
  getBlockInfo,
  getTransactionInfo,
  getTransactionReceipt,
  getGasOracle,
  getTokenInfo,
  getTokenSupply,
  getEth2Validators,
  getTopDefiProtocols,
  getDefiProtocolInfo,
  analyzeStakingProtocolPostMerge
};
