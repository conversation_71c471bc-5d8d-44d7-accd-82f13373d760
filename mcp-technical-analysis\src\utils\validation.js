/**
 * Utilidades de validación
 * 
 * Este módulo proporciona funciones para validar los datos
 * de entrada en las solicitudes a la API.
 */

/**
 * Valida un array de precios
 * @param {Array<number>} prices - Array de precios
 * @returns {boolean} - true si los datos son válidos, false en caso contrario
 */
function validatePrices(prices) {
  // Verificar que prices sea un array
  if (!Array.isArray(prices)) {
    return false;
  }
  
  // Verificar que el array no esté vacío
  if (prices.length === 0) {
    return false;
  }
  
  // Verificar que todos los elementos sean números
  return prices.every(price => typeof price === 'number' && !isNaN(price));
}

/**
 * Valida un array de velas (OHLC)
 * @param {Array<Object>} candles - Array de velas
 * @returns {boolean} - true si los datos son válidos, false en caso contrario
 */
function validateCandles(candles) {
  // Verificar que candles sea un array
  if (!Array.isArray(candles)) {
    return false;
  }
  
  // Verificar que el array no esté vacío
  if (candles.length === 0) {
    return false;
  }
  
  // Verificar que todas las velas tengan las propiedades requeridas
  return candles.every(candle => {
    return (
      typeof candle === 'object' &&
      candle !== null &&
      typeof candle.open === 'number' && !isNaN(candle.open) &&
      typeof candle.high === 'number' && !isNaN(candle.high) &&
      typeof candle.low === 'number' && !isNaN(candle.low) &&
      typeof candle.close === 'number' && !isNaN(candle.close) &&
      candle.high >= candle.low &&
      candle.high >= candle.open &&
      candle.high >= candle.close &&
      candle.low <= candle.open &&
      candle.low <= candle.close
    );
  });
}

/**
 * Valida los parámetros para el cálculo de indicadores
 * @param {Object} params - Parámetros
 * @returns {Object} - Objeto con la validación y posibles errores
 */
function validateIndicatorParams(params) {
  const result = {
    valid: true,
    errors: []
  };
  
  // Validar precios
  if (!params.prices || !validatePrices(params.prices)) {
    result.valid = false;
    result.errors.push('Se requiere un array válido de precios');
  }
  
  // Validar período
  if (params.period !== undefined) {
    if (typeof params.period !== 'number' || params.period <= 0 || !Number.isInteger(params.period)) {
      result.valid = false;
      result.errors.push('El período debe ser un número entero positivo');
    }
  }
  
  return result;
}

module.exports = {
  validatePrices,
  validateCandles,
  validateIndicatorParams
};
