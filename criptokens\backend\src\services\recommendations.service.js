/**
 * Servicio para generar recomendaciones personalizadas basadas en el portafolio del usuario
 * 
 * Este servicio proporciona funciones para analizar el portafolio del usuario
 * y generar recomendaciones personalizadas.
 */

const { getUserPortfolio, calculatePortfolioStats } = require('./portfolio.service');
const sentimentService = require('./sentiment.service');
const guruEtherscanService = require('./guru-etherscan.service');
const { searchNews } = require('./brave.service');

/**
 * Genera recomendaciones de diversificación basadas en el portafolio del usuario
 * @param {string} userId - ID del usuario
 * @returns {Promise<Object>} - Recomendaciones de diversificación
 */
async function generateDiversificationRecommendations(userId) {
  try {
    console.log(`Generando recomendaciones de diversificación para el usuario ${userId}...`);
    
    // Obtener el portafolio del usuario
    const portfolio = await getUserPortfolio(userId);
    
    if (!portfolio || !portfolio.assets || portfolio.assets.length === 0) {
      return {
        userId,
        recommendations: [],
        message: 'No se encontró un portafolio para este usuario o está vacío.'
      };
    }
    
    // Calcular estadísticas del portafolio
    const stats = await calculatePortfolioStats(portfolio);
    
    // Analizar la diversificación actual
    const assetTypes = {
      largeCap: 0,      // BTC, ETH
      midCap: 0,        // Top 10-50
      smallCap: 0,      // Resto
      stablecoins: 0,   // USDT, USDC, DAI, etc.
      defi: 0,          // Tokens DeFi
      nft: 0,           // Tokens NFT
      gaming: 0,        // Tokens Gaming
      layer1: 0,        // Blockchains alternativas
      layer2: 0         // Soluciones de escalabilidad
    };
    
    // Categorizar los activos
    portfolio.assets.forEach(asset => {
      const symbol = asset.symbol.toUpperCase();
      const value = asset.value || 0;
      
      if (symbol === 'BTC' || symbol === 'ETH') {
        assetTypes.largeCap += value;
      } else if (['USDT', 'USDC', 'DAI', 'BUSD', 'UST', 'TUSD'].includes(symbol)) {
        assetTypes.stablecoins += value;
      } else if (['AAVE', 'UNI', 'COMP', 'MKR', 'SNX', 'YFI', 'SUSHI', 'CRV', '1INCH'].includes(symbol)) {
        assetTypes.defi += value;
      } else if (['AXS', 'MANA', 'SAND', 'ENJ', 'GALA', 'ILV'].includes(symbol)) {
        assetTypes.gaming += value;
      } else if (['SOL', 'ADA', 'AVAX', 'DOT', 'ATOM', 'NEAR', 'FTM', 'ALGO'].includes(symbol)) {
        assetTypes.layer1 += value;
      } else if (['MATIC', 'LRC', 'OMG', 'ZKS', 'IMX', 'BOBA', 'OP'].includes(symbol)) {
        assetTypes.layer2 += value;
      } else if (['APE', 'BAYC', 'FLOW', 'RARE', 'WAXP'].includes(symbol)) {
        assetTypes.nft += value;
      } else {
        // Clasificar por capitalización de mercado (simplificado)
        assetTypes.midCap += value;
      }
    });
    
    // Calcular porcentajes
    const totalValue = stats.totalValue || 1;
    Object.keys(assetTypes).forEach(key => {
      assetTypes[key] = (assetTypes[key] / totalValue) * 100;
    });
    
    // Definir distribución ideal (simplificada)
    const idealDistribution = {
      largeCap: 40,      // BTC, ETH
      midCap: 20,        // Top 10-50
      smallCap: 10,      // Resto
      stablecoins: 10,   // USDT, USDC, DAI, etc.
      defi: 5,           // Tokens DeFi
      nft: 2,            // Tokens NFT
      gaming: 3,         // Tokens Gaming
      layer1: 5,         // Blockchains alternativas
      layer2: 5          // Soluciones de escalabilidad
    };
    
    // Generar recomendaciones
    const recommendations = [];
    
    // Verificar si hay demasiada concentración en un solo activo
    const largestAsset = portfolio.assets.reduce((prev, current) => 
      (prev.value > current.value) ? prev : current
    );
    
    if (largestAsset.value / totalValue > 0.5) {
      recommendations.push({
        type: 'diversification',
        priority: 'alta',
        description: `Tu portafolio está muy concentrado en ${largestAsset.name} (${largestAsset.symbol}). Considera diversificar para reducir el riesgo.`,
        action: `Reducir la posición en ${largestAsset.symbol} y distribuir en otras categorías de activos.`
      });
    }
    
    // Verificar categorías subrepresentadas
    Object.keys(idealDistribution).forEach(category => {
      const current = assetTypes[category] || 0;
      const ideal = idealDistribution[category];
      
      if (current < ideal * 0.5) {
        // Menos del 50% de lo ideal
        const categoryNames = {
          largeCap: 'criptomonedas de gran capitalización (BTC, ETH)',
          midCap: 'criptomonedas de mediana capitalización',
          smallCap: 'criptomonedas de pequeña capitalización',
          stablecoins: 'stablecoins',
          defi: 'tokens DeFi',
          nft: 'tokens relacionados con NFTs',
          gaming: 'tokens de gaming',
          layer1: 'blockchains alternativas',
          layer2: 'soluciones de escalabilidad (Layer 2)'
        };
        
        recommendations.push({
          type: 'allocation',
          priority: current === 0 ? 'alta' : 'media',
          description: `Tu portafolio tiene poca exposición a ${categoryNames[category]} (${current.toFixed(2)}% vs. ${ideal}% ideal).`,
          action: `Considera añadir exposición a ${categoryNames[category]} para mejorar la diversificación.`
        });
      }
    });
    
    // Verificar si hay demasiados activos de alto riesgo
    const highRiskPercentage = assetTypes.smallCap + assetTypes.nft + assetTypes.gaming;
    if (highRiskPercentage > 25) {
      recommendations.push({
        type: 'risk',
        priority: 'media',
        description: `Tu portafolio tiene una alta exposición a activos de mayor riesgo (${highRiskPercentage.toFixed(2)}%).`,
        action: 'Considera reducir la exposición a activos de pequeña capitalización y tokens especulativos.'
      });
    }
    
    // Verificar si hay suficiente liquidez
    if (assetTypes.stablecoins < 5) {
      recommendations.push({
        type: 'liquidity',
        priority: 'media',
        description: `Tu portafolio tiene poca liquidez (${assetTypes.stablecoins.toFixed(2)}% en stablecoins).`,
        action: 'Considera mantener al menos un 5-10% en stablecoins para aprovechar oportunidades de compra.'
      });
    }
    
    return {
      userId,
      portfolioStats: stats,
      currentAllocation: assetTypes,
      idealDistribution,
      recommendations
    };
  } catch (error) {
    console.error(`Error al generar recomendaciones de diversificación para el usuario ${userId}:`, error);
    throw error;
  }
}

/**
 * Genera recomendaciones basadas en el sentimiento del mercado y el portafolio del usuario
 * @param {string} userId - ID del usuario
 * @returns {Promise<Object>} - Recomendaciones basadas en sentimiento
 */
async function generateSentimentBasedRecommendations(userId) {
  try {
    console.log(`Generando recomendaciones basadas en sentimiento para el usuario ${userId}...`);
    
    // Obtener el portafolio del usuario
    const portfolio = await getUserPortfolio(userId);
    
    if (!portfolio || !portfolio.assets || portfolio.assets.length === 0) {
      return {
        userId,
        recommendations: [],
        message: 'No se encontró un portafolio para este usuario o está vacío.'
      };
    }
    
    // Analizar el sentimiento del mercado
    const marketSentiment = await sentimentService.analyzeMarketSentiment();
    
    // Analizar el sentimiento de los activos del portafolio
    const assetSentiments = await Promise.all(
      portfolio.assets.map(async asset => {
        try {
          const sentiment = await sentimentService.analyzeNewsSentiment(asset.name, 5);
          return {
            ...asset,
            sentiment
          };
        } catch (error) {
          console.error(`Error al analizar sentimiento para ${asset.name}:`, error);
          return {
            ...asset,
            sentiment: {
              sentiment: 'neutral',
              score: 0,
              confidence: 0,
              error: error.message
            }
          };
        }
      })
    );
    
    // Generar recomendaciones
    const recommendations = [];
    
    // Recomendaciones basadas en sentimiento del mercado
    if (marketSentiment.marketSentiment === 'negative' && marketSentiment.confidence > 0.3) {
      recommendations.push({
        type: 'market',
        priority: 'alta',
        description: `El sentimiento general del mercado es negativo (${marketSentiment.score.toFixed(2)}) con confianza ${marketSentiment.confidence.toFixed(2)}.`,
        action: 'Considera aumentar tu posición en stablecoins y reducir exposición a activos de alto riesgo temporalmente.'
      });
    } else if (marketSentiment.marketSentiment === 'positive' && marketSentiment.confidence > 0.3) {
      recommendations.push({
        type: 'market',
        priority: 'media',
        description: `El sentimiento general del mercado es positivo (${marketSentiment.score.toFixed(2)}) con confianza ${marketSentiment.confidence.toFixed(2)}.`,
        action: 'Podrías considerar aprovechar el momento positivo, pero mantén una estrategia disciplinada.'
      });
    }
    
    // Recomendaciones basadas en sentimiento de activos individuales
    assetSentiments.forEach(asset => {
      if (asset.sentiment.sentiment === 'negative' && asset.sentiment.confidence > 0.4) {
        recommendations.push({
          type: 'asset',
          asset: asset.symbol,
          priority: 'media',
          description: `El sentimiento para ${asset.name} (${asset.symbol}) es muy negativo (${asset.sentiment.score.toFixed(2)}) con alta confianza.`,
          action: `Considera revisar tu posición en ${asset.symbol} o investigar más a fondo las razones del sentimiento negativo.`
        });
      } else if (asset.sentiment.sentiment === 'positive' && asset.sentiment.confidence > 0.4 && asset.percentage < 5) {
        recommendations.push({
          type: 'asset',
          asset: asset.symbol,
          priority: 'baja',
          description: `El sentimiento para ${asset.name} (${asset.symbol}) es muy positivo (${asset.sentiment.score.toFixed(2)}) y tu exposición es relativamente baja.`,
          action: `Podrías considerar aumentar tu posición en ${asset.symbol} si se alinea con tu estrategia general.`
        });
      }
    });
    
    // Buscar activos con sentimiento positivo que no estén en el portafolio
    const topCryptos = ['Bitcoin', 'Ethereum', 'Solana', 'Cardano', 'Binance Coin', 'Polkadot', 'Avalanche'];
    const notInPortfolio = topCryptos.filter(crypto => 
      !portfolio.assets.some(asset => 
        asset.name.toLowerCase() === crypto.toLowerCase() || 
        asset.symbol.toLowerCase() === crypto.toLowerCase()
      )
    );
    
    if (notInPortfolio.length > 0) {
      // Analizar sentimiento de criptos que no están en el portafolio
      const externalSentiments = await Promise.all(
        notInPortfolio.slice(0, 3).map(async crypto => {
          try {
            const sentiment = await sentimentService.analyzeNewsSentiment(crypto, 5);
            return {
              name: crypto,
              sentiment
            };
          } catch (error) {
            console.error(`Error al analizar sentimiento para ${crypto}:`, error);
            return {
              name: crypto,
              sentiment: {
                sentiment: 'neutral',
                score: 0,
                confidence: 0,
                error: error.message
              }
            };
          }
        })
      );
      
      // Recomendar activos con sentimiento positivo
      externalSentiments.forEach(crypto => {
        if (crypto.sentiment.sentiment === 'positive' && crypto.sentiment.confidence > 0.3) {
          recommendations.push({
            type: 'opportunity',
            asset: crypto.name,
            priority: 'baja',
            description: `${crypto.name} muestra un sentimiento positivo (${crypto.sentiment.score.toFixed(2)}) y no está en tu portafolio.`,
            action: `Considera investigar ${crypto.name} como posible adición a tu portafolio si se alinea con tu estrategia.`
          });
        }
      });
    }
    
    return {
      userId,
      marketSentiment: {
        sentiment: marketSentiment.marketSentiment,
        score: marketSentiment.score,
        confidence: marketSentiment.confidence
      },
      assetSentiments: assetSentiments.map(asset => ({
        symbol: asset.symbol,
        name: asset.name,
        sentiment: asset.sentiment.sentiment,
        score: asset.sentiment.score,
        confidence: asset.sentiment.confidence
      })),
      recommendations
    };
  } catch (error) {
    console.error(`Error al generar recomendaciones basadas en sentimiento para el usuario ${userId}:`, error);
    throw error;
  }
}

/**
 * Genera recomendaciones de rebalanceo para el portafolio del usuario
 * @param {string} userId - ID del usuario
 * @returns {Promise<Object>} - Recomendaciones de rebalanceo
 */
async function generateRebalancingRecommendations(userId) {
  try {
    console.log(`Generando recomendaciones de rebalanceo para el usuario ${userId}...`);
    
    // Obtener el portafolio del usuario
    const portfolio = await getUserPortfolio(userId);
    
    if (!portfolio || !portfolio.assets || portfolio.assets.length === 0) {
      return {
        userId,
        recommendations: [],
        message: 'No se encontró un portafolio para este usuario o está vacío.'
      };
    }
    
    // Calcular estadísticas del portafolio
    const stats = await calculatePortfolioStats(portfolio);
    
    // Definir umbrales de rebalanceo (simplificados)
    const rebalancingThresholds = {
      major: 5,    // Rebalancear si la desviación es mayor al 5%
      minor: 2     // Considerar rebalanceo si la desviación es mayor al 2%
    };
    
    // Obtener la asignación objetivo del portafolio
    // En un caso real, esto podría estar almacenado en la base de datos
    // Aquí usamos una asignación simplificada basada en la capitalización de mercado
    const targetAllocation = {};
    
    // Asignar objetivos basados en la capitalización de mercado actual
    portfolio.assets.forEach(asset => {
      const symbol = asset.symbol.toUpperCase();
      
      if (symbol === 'BTC') {
        targetAllocation[symbol] = 30;
      } else if (symbol === 'ETH') {
        targetAllocation[symbol] = 20;
      } else if (['USDT', 'USDC', 'DAI', 'BUSD'].includes(symbol)) {
        targetAllocation[symbol] = 10;
      } else {
        // Para otros activos, asignar un porcentaje equitativo del restante
        const remainingAssets = portfolio.assets.length - Object.keys(targetAllocation).length;
        const remainingPercentage = 40; // 100 - 30 - 20 - 10
        targetAllocation[symbol] = remainingAssets > 0 ? remainingPercentage / remainingAssets : 5;
      }
    });
    
    // Calcular desviaciones
    const deviations = portfolio.assets.map(asset => {
      const currentPercentage = (asset.value / stats.totalValue) * 100;
      const targetPercentage = targetAllocation[asset.symbol.toUpperCase()] || 0;
      const deviation = currentPercentage - targetPercentage;
      
      return {
        symbol: asset.symbol,
        name: asset.name,
        currentPercentage,
        targetPercentage,
        deviation,
        value: asset.value,
        needsRebalancing: Math.abs(deviation) > rebalancingThresholds.major,
        considerRebalancing: Math.abs(deviation) > rebalancingThresholds.minor
      };
    });
    
    // Generar recomendaciones
    const recommendations = [];
    
    // Recomendaciones para activos que necesitan rebalanceo
    const needsRebalancing = deviations.filter(d => d.needsRebalancing);
    if (needsRebalancing.length > 0) {
      needsRebalancing.forEach(asset => {
        const action = asset.deviation > 0 ? 'reducir' : 'aumentar';
        const amount = Math.abs(asset.deviation).toFixed(2);
        
        recommendations.push({
          type: 'rebalancing',
          priority: 'alta',
          asset: asset.symbol,
          description: `${asset.name} (${asset.symbol}) está ${action === 'reducir' ? 'sobre' : 'sub'}ponderado por ${amount}% respecto a tu asignación objetivo.`,
          action: `Considera ${action} tu posición en ${asset.symbol} en aproximadamente ${amount}%.`,
          currentPercentage: asset.currentPercentage.toFixed(2),
          targetPercentage: asset.targetPercentage.toFixed(2),
          deviation: asset.deviation.toFixed(2)
        });
      });
    }
    
    // Recomendaciones para activos que podrían considerar rebalanceo
    const considerRebalancing = deviations.filter(d => !d.needsRebalancing && d.considerRebalancing);
    if (considerRebalancing.length > 0) {
      considerRebalancing.forEach(asset => {
        const action = asset.deviation > 0 ? 'reducir' : 'aumentar';
        const amount = Math.abs(asset.deviation).toFixed(2);
        
        recommendations.push({
          type: 'rebalancing',
          priority: 'media',
          asset: asset.symbol,
          description: `${asset.name} (${asset.symbol}) está ligeramente ${action === 'reducir' ? 'sobre' : 'sub'}ponderado por ${amount}% respecto a tu asignación objetivo.`,
          action: `Podrías considerar ${action} tu posición en ${asset.symbol} en aproximadamente ${amount}% en tu próximo rebalanceo.`,
          currentPercentage: asset.currentPercentage.toFixed(2),
          targetPercentage: asset.targetPercentage.toFixed(2),
          deviation: asset.deviation.toFixed(2)
        });
      });
    }
    
    // Si no hay recomendaciones, añadir una recomendación general
    if (recommendations.length === 0) {
      recommendations.push({
        type: 'rebalancing',
        priority: 'baja',
        description: 'Tu portafolio está bien balanceado respecto a tus objetivos de asignación.',
        action: 'Continúa monitoreando tu portafolio regularmente.'
      });
    }
    
    return {
      userId,
      portfolioStats: stats,
      targetAllocation,
      deviations,
      recommendations,
      rebalancingThresholds
    };
  } catch (error) {
    console.error(`Error al generar recomendaciones de rebalanceo para el usuario ${userId}:`, error);
    throw error;
  }
}

/**
 * Genera todas las recomendaciones disponibles para el usuario
 * @param {string} userId - ID del usuario
 * @returns {Promise<Object>} - Todas las recomendaciones
 */
async function generateAllRecommendations(userId) {
  try {
    console.log(`Generando todas las recomendaciones para el usuario ${userId}...`);
    
    // Obtener todas las recomendaciones
    const [diversification, sentiment, rebalancing] = await Promise.all([
      generateDiversificationRecommendations(userId),
      generateSentimentBasedRecommendations(userId),
      generateRebalancingRecommendations(userId)
    ]);
    
    // Combinar todas las recomendaciones
    const allRecommendations = [
      ...(diversification.recommendations || []),
      ...(sentiment.recommendations || []),
      ...(rebalancing.recommendations || [])
    ];
    
    // Ordenar por prioridad
    const priorityOrder = { 'alta': 0, 'media': 1, 'baja': 2 };
    allRecommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
    
    return {
      userId,
      timestamp: new Date().toISOString(),
      recommendations: allRecommendations,
      details: {
        diversification,
        sentiment,
        rebalancing
      }
    };
  } catch (error) {
    console.error(`Error al generar todas las recomendaciones para el usuario ${userId}:`, error);
    throw error;
  }
}

module.exports = {
  generateDiversificationRecommendations,
  generateSentimentBasedRecommendations,
  generateRebalancingRecommendations,
  generateAllRecommendations
};
