# Script para iniciar Context7 MCP Server en PowerShell

Write-Host "Iniciando Context7 MCP Server..." -ForegroundColor Cyan
Write-Host ""
Write-Host "Este servidor proporciona documentación actualizada para bibliotecas y frameworks," -ForegroundColor White
Write-Host "mejorando las capacidades del Gurú Cripto." -ForegroundColor White
Write-Host ""
Write-Host "Para usar Context7 en tus consultas al Gurú, simplemente añade 'use context7' al final de tu prompt." -ForegroundColor Yellow
Write-Host "Ejemplo: 'Crea un componente React que muestre un gráfico de precios de Bitcoin usando Chart.js. use context7'" -ForegroundColor Yellow
Write-Host ""
Write-Host "Presiona Ctrl+C para detener el servidor." -ForegroundColor Gray
Write-Host ""

# Iniciar Context7 MCP
npx -y @upstash/context7-mcp@latest

Write-Host ""
Write-Host "Context7 MCP Server se ha detenido." -ForegroundColor Red
Read-Host -Prompt "Presiona Enter para salir"
