import os
import logging
import click
from dotenv import load_dotenv
from ...common.server import A2AServer
from ...common.types import AgentCard, AgentCapabilities, AgentSkill, AgentProvider
from .task_manager import TechnicalAgentTaskManager
from .agent import TechnicalAnalysisAgent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@click.command()
@click.option("--host", default="localhost", help="Host to bind the server to")
@click.option("--port", default=3201, help="Port to bind the server to")
def main(host, port):
    """Run the technical analysis agent server."""
    try:
        # Create agent capabilities
        capabilities = AgentCapabilities(streaming=True)
        
        # Create agent skills
        skill = AgentSkill(
            id="technical_analysis",
            name="Technical Analysis",
            description="Analyzes cryptocurrency price data using technical indicators",
            tags=["crypto", "technical", "analysis", "price"],
            examples=[
                "Analyze Bitcoin technical indicators",
                "What's the technical outlook for Ethereum?",
                "Show me the technical analysis for Solana for the last 30 days"
            ],
            inputModes=["text"],
            outputModes=["text", "data"]
        )
        
        # Create agent provider
        provider = AgentProvider(
            organization="Criptokens",
            url="https://criptokens.com"
        )
        
        # Create agent card
        agent_card = AgentCard(
            name="Technical Analysis Agent",
            description="This agent analyzes cryptocurrency price data using technical indicators",
            url=f"http://{host}:{port}/",
            provider=provider,
            version="1.0.0",
            capabilities=capabilities,
            defaultInputModes=["text"],
            defaultOutputModes=["text", "data"],
            skills=[skill]
        )
        
        # Create agent and task manager
        agent = TechnicalAnalysisAgent()
        task_manager = TechnicalAgentTaskManager(agent=agent)
        
        # Create and start server
        server = A2AServer(
            agent_card=agent_card,
            task_manager=task_manager,
            host=host,
            port=port
        )
        
        logger.info(f"Starting Technical Analysis Agent server on http://{host}:{port}/")
        server.start()
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)

if __name__ == "__main__":
    main()
