import React, { useState, useEffect } from 'react';
import { useTopCryptocurrencies } from '../../hooks/useMcpClient';
import PredictionIndicator from './PredictionIndicator';
import '../../styles/guru/CryptoPredictor.css';

// Importar el servicio de OpenRouter para mostrar información sobre el modelo
import { callOpenRouter } from '../../services/ai/openRouterService';

const CryptoPredictor: React.FC = () => {
  const [selectedCrypto, setSelectedCrypto] = useState<string>('bitcoin');
  const [timeframe, setTimeframe] = useState<'1d' | '7d' | '30d'>('7d');
  const [modelInfo, setModelInfo] = useState<string | null>(null);
  const [isCheckingModel, setIsCheckingModel] = useState<boolean>(false);
  const { data: cryptoList, loading } = useTopCryptocurrencies();

  // Verificar la disponibilidad del modelo de IA
  useEffect(() => {
    const checkModelAvailability = async () => {
      try {
        setIsCheckingModel(true);
        // Realizar una llamada simple para verificar la conexión
        const testPrompt = "Responde con 'OK' si estás funcionando correctamente.";
        const response = await callOpenRouter(testPrompt, {
          model: "anthropic/claude-3-haiku-20240307",
          max_tokens: 10,
          temperature: 0.1
        });

        setModelInfo(`Modelo IA disponible: ${response.model || 'Claude'}`);
      } catch (error) {
        console.error('Error al verificar modelo:', error);
        setModelInfo('Modelo IA no disponible - usando predicciones simuladas');
      } finally {
        setIsCheckingModel(false);
      }
    };

    checkModelAvailability();
  }, []);

  // Seleccionar Bitcoin por defecto
  useEffect(() => {
    if (cryptoList && cryptoList.length > 0 && !selectedCrypto) {
      setSelectedCrypto(cryptoList[0].id);
    }
  }, [cryptoList, selectedCrypto]);

  return (
    <div className="crypto-predictor">
      <div className="predictor-header">
        <h2>Predictor de Mercado IA</h2>
        <p className="predictor-description">
          Análisis avanzado impulsado por inteligencia artificial para predecir movimientos de precios
        </p>
        {modelInfo && (
          <div className={`model-status ${modelInfo.includes('no disponible') ? 'unavailable' : 'available'}`}>
            {isCheckingModel ? 'Verificando conexión con IA...' : modelInfo}
          </div>
        )}
      </div>

      <div className="predictor-controls">
        <div className="crypto-selector">
          <label htmlFor="crypto-select">Seleccionar Criptomoneda:</label>
          <select
            id="crypto-select"
            value={selectedCrypto}
            onChange={(e) => setSelectedCrypto(e.target.value)}
            disabled={loading}
          >
            {loading ? (
              <option value="">Cargando...</option>
            ) : (
              cryptoList?.map(crypto => (
                <option key={crypto.id} value={crypto.id}>
                  {crypto.name} ({crypto.symbol.toUpperCase()})
                </option>
              ))
            )}
          </select>
        </div>

        <div className="timeframe-selector">
          <label>Horizonte de Tiempo:</label>
          <div className="timeframe-buttons">
            <button
              className={timeframe === '1d' ? 'active' : ''}
              onClick={() => setTimeframe('1d')}
            >
              24 horas
            </button>
            <button
              className={timeframe === '7d' ? 'active' : ''}
              onClick={() => setTimeframe('7d')}
            >
              7 días
            </button>
            <button
              className={timeframe === '30d' ? 'active' : ''}
              onClick={() => setTimeframe('30d')}
            >
              30 días
            </button>
          </div>
        </div>
      </div>

      <div className="prediction-container">
        {selectedCrypto ? (
          <PredictionIndicator
            cryptoId={selectedCrypto}
            timeframe={timeframe}
          />
        ) : (
          <div className="select-crypto-message">
            <p>Selecciona una criptomoneda para ver la predicción</p>
          </div>
        )}
      </div>

      <div className="predictor-features">
        <div className="feature-card">
          <div className="feature-icon">📊</div>
          <h3>Análisis Técnico</h3>
          <p>Combinación de múltiples indicadores técnicos y patrones de gráficos para identificar tendencias</p>
        </div>

        <div className="feature-card">
          <div className="feature-icon">🔍</div>
          <h3>Datos On-Chain</h3>
          <p>Análisis de actividad en la blockchain, movimientos de ballenas y métricas de red</p>
        </div>

        <div className="feature-card">
          <div className="feature-icon">📰</div>
          <h3>Sentimiento de Mercado</h3>
          <p>Evaluación del sentimiento en redes sociales, noticias y comunidades de trading</p>
        </div>

        <div className="feature-card">
          <div className="feature-icon">🧠</div>
          <h3>IA Avanzada</h3>
          <p>Modelos de inteligencia artificial entrenados con datos históricos y patrones de mercado</p>
        </div>
      </div>

      <div className="predictor-disclaimer">
        <h3>Información Importante</h3>
        <p>
          El Predictor de Mercado IA es una herramienta experimental que utiliza inteligencia artificial para analizar datos de mercado y generar predicciones.
          Estas predicciones no constituyen asesoramiento financiero y no deben ser la única base para tus decisiones de inversión.
        </p>
        <p>
          El mercado de criptomonedas es altamente volátil e impredecible. Incluso las mejores herramientas de análisis tienen limitaciones.
          Siempre realiza tu propia investigación y considera consultar con un asesor financiero profesional antes de invertir.
        </p>
      </div>
    </div>
  );
};

export default CryptoPredictor;
