import React, { useState, useEffect } from 'react';
import { PortfolioAsset } from '../../services/portfolio.service';
import '../../styles/portfolio/EnhancedDistributionChart.css';

interface EnhancedDistributionChartProps {
  portfolio: PortfolioAsset[];
  availableCryptos: any[];
}

interface AssetWithDetails extends PortfolioAsset {
  value: number;
  percentage: number;
  color: string;
  currentPrice: number;
}

// Componente para mostrar una distribución mejorada del portafolio
const EnhancedDistributionChart: React.FC<EnhancedDistributionChartProps> = ({ 
  portfolio, 
  availableCryptos 
}) => {
  const [assets, setAssets] = useState<AssetWithDetails[]>([]);
  const [viewMode, setViewMode] = useState<'pie' | 'bar'>('pie');
  const [selectedAsset, setSelectedAsset] = useState<AssetWithDetails | null>(null);
  const [sortBy, setSortBy] = useState<'value' | 'name' | 'change'>('value');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Colores predefinidos para criptomonedas comunes
  const cryptoColors: Record<string, string> = {
    bitcoin: '#F7931A',
    ethereum: '#627EEA',
    solana: '#00FFA3',
    cardano: '#0033AD',
    polkadot: '#E6007A',
    dogecoin: '#C2A633',
    ripple: '#23292F',
    litecoin: '#345D9D',
    binancecoin: '#F3BA2F',
    chainlink: '#2A5ADA',
    avalanche: '#E84142',
    polygon: '#8247E5',
    cosmos: '#2E3148',
    stellar: '#7D00FF',
    tron: '#FF0013',
    uniswap: '#FF007A',
    aave: '#B6509E',
    maker: '#6FCEBB',
    compound: '#00D395',
    tezos: '#2C7DF7'
  };

  // Categorías de criptomonedas
  const categories = [
    { id: 'all', name: 'Todas' },
    { id: 'layer1', name: 'Layer 1' },
    { id: 'defi', name: 'DeFi' },
    { id: 'exchange', name: 'Exchange' },
    { id: 'stablecoin', name: 'Stablecoins' },
    { id: 'other', name: 'Otras' }
  ];

  // Asignar categorías a las criptomonedas
  const getCryptoCategory = (id: string): string => {
    const layer1 = ['bitcoin', 'ethereum', 'solana', 'cardano', 'avalanche', 'polkadot', 'cosmos', 'tezos'];
    const defi = ['uniswap', 'aave', 'maker', 'compound', 'chainlink', 'curve-dao-token'];
    const exchange = ['binancecoin', 'ftx-token', 'kucoin-shares', 'huobi-token'];
    const stablecoin = ['tether', 'usd-coin', 'binance-usd', 'dai', 'true-usd'];
    
    if (layer1.includes(id)) return 'layer1';
    if (defi.includes(id)) return 'defi';
    if (exchange.includes(id)) return 'exchange';
    if (stablecoin.includes(id)) return 'stablecoin';
    return 'other';
  };

  // Procesar los activos del portafolio
  useEffect(() => {
    if (portfolio.length === 0 || availableCryptos.length === 0) {
      setAssets([]);
      return;
    }

    // Calcular el valor total del portafolio
    let totalValue = 0;
    const processedAssets: AssetWithDetails[] = portfolio.map(asset => {
      const cryptoInfo = availableCryptos.find(c => c.id === asset.id);
      const currentPrice = cryptoInfo ? cryptoInfo.current_price : 0;
      const value = asset.amount * currentPrice;
      totalValue += value;

      // Asignar un color al activo
      let color = cryptoColors[asset.id] || '';
      if (!color) {
        // Generar un color aleatorio si no hay uno predefinido
        const hue = Math.floor(Math.random() * 360);
        color = `hsl(${hue}, 70%, 50%)`;
      }

      return {
        ...asset,
        value,
        percentage: 0, // Se calculará después
        color,
        currentPrice
      };
    });

    // Calcular el porcentaje de cada activo
    const assetsWithPercentage = processedAssets.map(asset => ({
      ...asset,
      percentage: totalValue > 0 ? (asset.value / totalValue) * 100 : 0
    }));

    // Ordenar los activos según el criterio seleccionado
    const sortedAssets = sortAssets(assetsWithPercentage, sortBy);
    setAssets(sortedAssets);
  }, [portfolio, availableCryptos, sortBy]);

  // Función para ordenar los activos
  const sortAssets = (assets: AssetWithDetails[], criterion: string) => {
    switch (criterion) {
      case 'value':
        return [...assets].sort((a, b) => b.value - a.value);
      case 'name':
        return [...assets].sort((a, b) => a.name.localeCompare(b.name));
      case 'change':
        return [...assets].sort((a, b) => {
          const cryptoA = availableCryptos.find(c => c.id === a.id);
          const cryptoB = availableCryptos.find(c => c.id === b.id);
          const changeA = cryptoA ? cryptoA.price_change_percentage_24h : 0;
          const changeB = cryptoB ? cryptoB.price_change_percentage_24h : 0;
          return changeB - changeA;
        });
      default:
        return assets;
    }
  };

  // Filtrar activos por categoría
  const filteredAssets = categoryFilter === 'all'
    ? assets
    : assets.filter(asset => getCryptoCategory(asset.id) === categoryFilter);

  // Manejar clic en un activo
  const handleAssetClick = (asset: AssetWithDetails) => {
    setSelectedAsset(asset === selectedAsset ? null : asset);
  };

  if (portfolio.length === 0) {
    return (
      <div className="enhanced-distribution-chart empty-chart">
        <h3>Distribución de Activos</h3>
        <div className="empty-chart-message">
          <p>Añade activos a tu portafolio para ver la distribución</p>
        </div>
      </div>
    );
  }

  return (
    <div className="enhanced-distribution-chart">
      <div className="chart-header">
        <h3>Distribución de Activos</h3>
        <div className="view-mode-toggle">
          <button 
            className={viewMode === 'pie' ? 'active' : ''} 
            onClick={() => setViewMode('pie')}
          >
            Gráfico Circular
          </button>
          <button 
            className={viewMode === 'bar' ? 'active' : ''} 
            onClick={() => setViewMode('bar')}
          >
            Barras
          </button>
        </div>
      </div>
      
      <div className="chart-filters">
        <div className="sort-options">
          <label>Ordenar por:</label>
          <select value={sortBy} onChange={(e) => setSortBy(e.target.value as any)}>
            <option value="value">Valor</option>
            <option value="name">Nombre</option>
            <option value="change">Cambio 24h</option>
          </select>
        </div>
        
        <div className="category-filter">
          <label>Categoría:</label>
          <div className="category-buttons">
            {categories.map(category => (
              <button
                key={category.id}
                className={categoryFilter === category.id ? 'active' : ''}
                onClick={() => setCategoryFilter(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      <div className="chart-container">
        {viewMode === 'pie' ? (
          <div className="pie-chart-container">
            {filteredAssets.length > 0 ? (
              <>
                <div className="pie-chart">
                  {filteredAssets.map((asset, index) => (
                    <div
                      key={asset.id}
                      className={`pie-segment ${selectedAsset?.id === asset.id ? 'selected' : ''}`}
                      style={{
                        '--percentage': `${asset.percentage}%`,
                        '--color': asset.color,
                        '--rotation': `${filteredAssets.slice(0, index).reduce((acc, a) => acc + a.percentage, 0) * 3.6}deg`
                      } as React.CSSProperties}
                      onClick={() => handleAssetClick(asset)}
                      title={`${asset.name} (${asset.symbol.toUpperCase()}): ${asset.percentage.toFixed(2)}%`}
                    ></div>
                  ))}
                  <div className="pie-center">
                    {selectedAsset ? (
                      <div className="selected-asset-info">
                        <div className="asset-symbol">{selectedAsset.symbol.toUpperCase()}</div>
                        <div className="asset-percentage">{selectedAsset.percentage.toFixed(2)}%</div>
                      </div>
                    ) : (
                      <div className="total-assets">{filteredAssets.length} Activos</div>
                    )}
                  </div>
                </div>
                
                <div className="pie-legend">
                  {filteredAssets.map(asset => (
                    <div 
                      key={asset.id} 
                      className={`legend-item ${selectedAsset?.id === asset.id ? 'selected' : ''}`}
                      onClick={() => handleAssetClick(asset)}
                    >
                      <div className="legend-color" style={{ backgroundColor: asset.color }}></div>
                      <div className="legend-name">{asset.symbol.toUpperCase()}</div>
                      <div className="legend-percentage">{asset.percentage.toFixed(2)}%</div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="no-assets-message">
                <p>No hay activos en la categoría seleccionada</p>
              </div>
            )}
          </div>
        ) : (
          <div className="bar-chart-container">
            {filteredAssets.length > 0 ? (
              <div className="bar-chart">
                {filteredAssets.map(asset => (
                  <div 
                    key={asset.id} 
                    className={`bar-item ${selectedAsset?.id === asset.id ? 'selected' : ''}`}
                    onClick={() => handleAssetClick(asset)}
                  >
                    <div className="bar-label">
                      <div className="bar-color" style={{ backgroundColor: asset.color }}></div>
                      <div className="bar-name">{asset.symbol.toUpperCase()}</div>
                    </div>
                    <div className="bar-container">
                      <div 
                        className="bar-fill" 
                        style={{ 
                          width: `${asset.percentage}%`, 
                          backgroundColor: asset.color 
                        }}
                      ></div>
                    </div>
                    <div className="bar-value">${asset.value.toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-assets-message">
                <p>No hay activos en la categoría seleccionada</p>
              </div>
            )}
          </div>
        )}
      </div>
      
      {selectedAsset && (
        <div className="asset-details">
          <div className="asset-details-header">
            <h4>{selectedAsset.name} ({selectedAsset.symbol.toUpperCase()})</h4>
            <button className="close-details" onClick={() => setSelectedAsset(null)}>×</button>
          </div>
          <div className="asset-details-content">
            <div className="detail-item">
              <span className="detail-label">Cantidad:</span>
              <span className="detail-value">{selectedAsset.amount.toLocaleString(undefined, { maximumFractionDigits: 8 })}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Precio actual:</span>
              <span className="detail-value">${selectedAsset.currentPrice.toLocaleString(undefined, { maximumFractionDigits: 2 })}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Valor:</span>
              <span className="detail-value">${selectedAsset.value.toLocaleString(undefined, { maximumFractionDigits: 2 })}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">% del Portafolio:</span>
              <span className="detail-value">{selectedAsset.percentage.toFixed(2)}%</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Precio de compra:</span>
              <span className="detail-value">${selectedAsset.purchasePrice.toLocaleString(undefined, { maximumFractionDigits: 2 })}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Categoría:</span>
              <span className="detail-value">{categories.find(c => c.id === getCryptoCategory(selectedAsset.id))?.name || 'Otra'}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedDistributionChart;
