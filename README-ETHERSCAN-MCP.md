# Servidor MCP de Etherscan para Criptokens

Este servidor MCP proporciona herramientas para acceder a datos on-chain de la blockchain de Ethereum utilizando la API de Etherscan.

## Características

- Obtención de balances de direcciones Ethereum
- Análisis de tokens ERC20
- Información de contratos inteligentes
- Precios de gas en tiempo real
- Estadísticas de la red Ethereum
- Análisis de protocolos DeFi

## Requisitos

- Node.js (v14 o superior)
- API Key de Etherscan (incluida por defecto: `**********************************`)

## Instalación

El servidor MCP de Etherscan ya está configurado en el proyecto Criptokens. No se requiere instalación adicional.

## Uso

### Iniciar el servidor individualmente

```bash
node etherscan-mcp-server.js
```

### Iniciar con otros servidores MCP

```bash
node start-mcp-servers.js
```

### Iniciar con todos los servicios de Criptokens

```bash
node start-all-services.js
```

## Herramientas disponibles

El servidor MCP de Etherscan proporciona las siguientes herramientas:

1. **getAddressBalance**
   - Obtiene el balance de ETH de una dirección
   - Parámetros: `address` (dirección Ethereum)

2. **getAddressTokens**
   - Obtiene los tokens ERC20 de una dirección
   - Parámetros: `address` (dirección Ethereum), `page` (opcional), `offset` (opcional)

3. **getTokenInfo**
   - Obtiene información detallada de un token ERC20
   - Parámetros: `address` (dirección del contrato del token)

4. **getEthPrice**
   - Obtiene el precio actual de ETH en USD y BTC
   - No requiere parámetros

5. **getGasOracle**
   - Obtiene información sobre los precios actuales de gas
   - No requiere parámetros

6. **analyzeAddress**
   - Realiza un análisis completo de una dirección Ethereum
   - Parámetros: `address` (dirección Ethereum)

7. **analyzeSmartContract**
   - Analiza un contrato inteligente
   - Parámetros: `address` (dirección del contrato)

8. **getDefiProtocols**
   - Obtiene información sobre los principales protocolos DeFi
   - No requiere parámetros

## Integración con el Gurú Cripto

El Gurú Cripto puede utilizar estas herramientas para proporcionar análisis on-chain detallados. Las herramientas están disponibles a través de las siguientes funciones:

1. **getEthereumAddressInfo**
   - Analiza una dirección de Ethereum
   - Ejemplo: "Analiza la dirección 0x..."

2. **getSmartContractInfo**
   - Analiza un contrato inteligente
   - Ejemplo: "Analiza el contrato 0x..."

3. **getEthereumGasInfo**
   - Obtiene información de gas
   - Ejemplo: "¿Cuál es el precio actual del gas en Ethereum?"

4. **getEthereumPrice**
   - Obtiene el precio de ETH
   - Ejemplo: "¿Cuál es el precio actual de Ethereum?"

5. **getDefiProtocolsInfo**
   - Obtiene información sobre protocolos DeFi
   - Ejemplo: "¿Cuáles son los principales protocolos DeFi en Ethereum?"

## Configuración

El servidor MCP de Etherscan se configura en el archivo `mcp-config.json`:

```json
{
  "mcpServers": {
    "etherscan": {
      "command": "node",
      "args": ["etherscan-mcp-server.js"],
      "env": {
        "PORT": "3104",
        "ETHERSCAN_API_KEY": "**********************************"
      }
    }
  }
}
```

## Solución de problemas

Si encuentras problemas con el servidor MCP de Etherscan:

1. Verifica que el servidor esté en ejecución en http://localhost:3104
2. Comprueba que la API Key de Etherscan sea válida
3. Verifica los logs del servidor para identificar errores específicos

## Limitaciones

- La API gratuita de Etherscan tiene límites de uso (5 llamadas por segundo)
- Algunas funcionalidades avanzadas pueden requerir una API Key premium
- Los datos on-chain pueden tener un ligero retraso respecto al estado actual de la blockchain
