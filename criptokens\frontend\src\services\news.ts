import axios from 'axios';

// Interfaz para los artículos de noticias
export interface NewsArticle {
  id: string;
  title: string;
  description: string;
  url: string;
  imageUrl: string;
  source: string;
  publishedAt: Date;
  categories: string[];
}

// Interfaz para los resultados de noticias de Brave Search
export interface BraveNewsResult {
  title: string;
  url: string;
  description: string;
  publishedDate: string;
  source: string;
}

// Función para obtener noticias sobre criptomonedas usando Brave Search
export const getCryptoNews = async (limit = 10, topic = 'criptomonedas'): Promise<NewsArticle[]> => {
  try {
    console.log(`Buscando noticias sobre ${topic} usando Brave Search...`);

    // Intentar obtener noticias desde el backend que usa Brave Search
    const response = await axios.post('http://localhost:3002/api/guru/news', {
      topic
    }, {
      withCredentials: true
    });

    if (response.data && response.data.results) {
      // Convertir los resultados de Brave Search al formato de NewsArticle
      const newsArticles: NewsArticle[] = response.data.results.map((result: BraveNewsResult, index: number) => ({
        id: `brave-${index}`,
        title: result.title,
        description: result.description || 'No hay descripción disponible',
        url: result.url,
        imageUrl: `https://source.unsplash.com/random/300x200?crypto,${index}`, // Imagen aleatoria como placeholder
        source: result.source || 'Brave Search',
        publishedAt: result.publishedDate ? new Date(result.publishedDate) : new Date(),
        categories: [topic, 'Noticias']
      }));

      return newsArticles.slice(0, limit);
    }

    // Si no hay resultados o hay un error, usar datos simulados como fallback
    console.log('No se encontraron resultados de Brave Search, usando datos simulados...');
    return mockNewsData.slice(0, limit);
  } catch (error) {
    console.error('Error al obtener noticias de criptomonedas:', error);
    console.log('Usando datos simulados como fallback debido a un error...');
    return mockNewsData.slice(0, limit);
  }
};

// Datos simulados para desarrollo
const mockNewsData: NewsArticle[] = [
  {
    id: '1',
    title: 'Bitcoin supera los $85,000 por primera vez en su historia',
    description: 'La principal criptomoneda del mercado ha alcanzado un nuevo máximo histórico, superando los $85,000 por unidad, impulsada por la creciente adopción institucional y la escasez tras el último halving.',
    url: 'https://example.com/bitcoin-new-ath',
    imageUrl: 'https://images.unsplash.com/photo-1518546305927-5a555bb7020d',
    source: 'Crypto News',
    publishedAt: new Date('2023-04-18T14:30:00Z'),
    categories: ['Bitcoin', 'Markets', 'Price Analysis']
  },
  {
    id: '2',
    title: 'Ethereum completa actualización importante mejorando la escalabilidad',
    description: 'La red Ethereum ha completado con éxito una actualización que promete reducir significativamente las tarifas de gas y aumentar la capacidad de transacciones por segundo.',
    url: 'https://example.com/ethereum-upgrade',
    imageUrl: 'https://images.unsplash.com/photo-1622630998477-20aa696ecb05',
    source: 'Blockchain Insider',
    publishedAt: new Date('2023-04-17T09:15:00Z'),
    categories: ['Ethereum', 'Technology', 'Development']
  },
  {
    id: '3',
    title: 'Reguladores de EE.UU. aprueban nuevos ETF de Bitcoin',
    description: 'La Comisión de Valores y Bolsa (SEC) ha aprobado nuevos fondos cotizados en bolsa (ETF) de Bitcoin, ampliando las opciones de inversión para los inversores institucionales y minoristas.',
    url: 'https://example.com/bitcoin-etf-approval',
    imageUrl: 'https://images.unsplash.com/photo-1621761191319-c6fb62004040',
    source: 'Financial Times',
    publishedAt: new Date('2023-04-16T18:45:00Z'),
    categories: ['Regulation', 'Bitcoin', 'Investment']
  },
  {
    id: '4',
    title: 'Cardano lanza nueva plataforma de contratos inteligentes',
    description: 'La fundación Cardano ha anunciado el lanzamiento de una nueva plataforma de contratos inteligentes que promete mayor seguridad y eficiencia energética que sus competidores.',
    url: 'https://example.com/cardano-smart-contracts',
    imageUrl: 'https://images.unsplash.com/photo-1642104704074-907c0698cbd9',
    source: 'Crypto Daily',
    publishedAt: new Date('2023-04-15T11:20:00Z'),
    categories: ['Cardano', 'Smart Contracts', 'Development']
  },
  {
    id: '5',
    title: 'El Salvador construye la primera ciudad Bitcoin del mundo',
    description: 'El gobierno de El Salvador ha iniciado la construcción de "Bitcoin City", una ciudad que utilizará energía geotérmica para la minería de Bitcoin y no tendrá impuestos excepto el IVA.',
    url: 'https://example.com/el-salvador-bitcoin-city',
    imageUrl: 'https://images.unsplash.com/photo-1640340434855-6084b1f4901c',
    source: 'Latin America Herald',
    publishedAt: new Date('2023-04-14T16:10:00Z'),
    categories: ['Bitcoin', 'Adoption', 'El Salvador']
  },
  {
    id: '6',
    title: 'Solana sufre interrupción de red por sexta vez este año',
    description: 'La blockchain Solana experimentó otra interrupción de red, la sexta en lo que va del año, generando preocupaciones sobre la estabilidad y confiabilidad de la plataforma.',
    url: 'https://example.com/solana-outage',
    imageUrl: 'https://images.unsplash.com/photo-1639762681057-408e52192e55',
    source: 'DeFi Pulse',
    publishedAt: new Date('2023-04-13T08:30:00Z'),
    categories: ['Solana', 'Network Issues', 'Technology']
  },
  {
    id: '7',
    title: 'NFTs de arte digital superan los $10 mil millones en ventas totales',
    description: 'El mercado de tokens no fungibles (NFTs) de arte digital ha superado los $10 mil millones en ventas totales, marcando un hito importante para esta nueva clase de activos digitales.',
    url: 'https://example.com/nft-sales-milestone',
    imageUrl: 'https://images.unsplash.com/photo-1620321023374-d1a68fbc720d',
    source: 'Art Tech Review',
    publishedAt: new Date('2023-04-12T13:45:00Z'),
    categories: ['NFTs', 'Digital Art', 'Markets']
  },
  {
    id: '8',
    title: 'China intensifica represión contra minería ilegal de criptomonedas',
    description: 'Las autoridades chinas han intensificado sus esfuerzos para erradicar la minería ilegal de criptomonedas, confiscando miles de equipos de minería en varias provincias.',
    url: 'https://example.com/china-crypto-crackdown',
    imageUrl: 'https://images.unsplash.com/photo-1605792657660-596af9009e82',
    source: 'Asia Blockchain Review',
    publishedAt: new Date('2023-04-11T10:20:00Z'),
    categories: ['Mining', 'Regulation', 'China']
  },
  {
    id: '9',
    title: 'Ripple gana batalla legal contra la SEC en caso de valores',
    description: 'Ripple Labs ha ganado una importante batalla legal contra la Comisión de Valores y Bolsa de EE.UU. (SEC), que podría tener implicaciones significativas para la regulación de las criptomonedas.',
    url: 'https://example.com/ripple-sec-case',
    imageUrl: 'https://images.unsplash.com/photo-1629339942248-45d4b10c8c2f',
    source: 'Legal Crypto',
    publishedAt: new Date('2023-04-10T15:30:00Z'),
    categories: ['Ripple', 'Regulation', 'Legal']
  },
  {
    id: '10',
    title: 'DeFi alcanza nuevo récord con $150 mil millones en valor total bloqueado',
    description: 'El sector de finanzas descentralizadas (DeFi) ha alcanzado un nuevo récord con $150 mil millones en valor total bloqueado en protocolos, reflejando el creciente interés en alternativas financieras descentralizadas.',
    url: 'https://example.com/defi-tvl-record',
    imageUrl: 'https://images.unsplash.com/photo-1639152201720-5e536d254d81',
    source: 'DeFi Insight',
    publishedAt: new Date('2023-04-09T12:15:00Z'),
    categories: ['DeFi', 'Markets', 'Growth']
  }
];
