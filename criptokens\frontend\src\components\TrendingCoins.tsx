import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/TrendingCoins.css';

interface TrendingCoinsProps {
  cryptos: any[];
  isLoading: boolean;
}

const TrendingCoins: React.FC<TrendingCoinsProps> = ({ cryptos, isLoading }) => {
  const navigate = useNavigate();

  // Función para formatear números con separadores de miles
  const formatNumber = (num: number, maximumFractionDigits: number = 2): string => {
    return new Intl.NumberFormat('es-ES', {
      minimumFractionDigits: 2,
      maximumFractionDigits
    }).format(num);
  };

  // Renderizar un esqueleto de carga
  const renderSkeleton = () => {
    return Array(5).fill(0).map((_, index) => (
      <div key={`skeleton-${index}`} className="trending-coin-card skeleton">
        <div className="coin-rank skeleton-rank"></div>
        <div className="coin-icon-container">
          <div className="skeleton-icon"></div>
        </div>
        <div className="coin-details">
          <div className="skeleton-name"></div>
          <div className="skeleton-symbol"></div>
        </div>
        <div className="coin-price">
          <div className="skeleton-price"></div>
          <div className="skeleton-change"></div>
        </div>
      </div>
    ));
  };

  return (
    <div className="trending-coins">
      <div className="trending-coins-container">
        {isLoading ? (
          renderSkeleton()
        ) : (
          cryptos.map((crypto, index) => (
            <div 
              key={crypto.id} 
              className="trending-coin-card"
              onClick={() => navigate(`/coins/${crypto.id}`)}
            >
              <div className="coin-rank">{index + 1}</div>
              <div className="coin-icon-container">
                <img 
                  src={crypto.image} 
                  alt={crypto.name} 
                  className="coin-icon" 
                />
              </div>
              <div className="coin-details">
                <div className="coin-name">{crypto.name}</div>
                <div className="coin-symbol">{crypto.symbol.toUpperCase()}</div>
              </div>
              <div className="coin-price">
                <div className="price-value">${formatNumber(crypto.current_price)}</div>
                <div className={`price-change ${crypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                  <i className={`fas ${crypto.price_change_percentage_24h >= 0 ? 'fa-caret-up' : 'fa-caret-down'}`}></i>
                  {crypto.price_change_percentage_24h?.toFixed(2)}%
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TrendingCoins;
