import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { FundamentalAnalysisCard } from '../components/fundamental';
import { 
  Container, 
  Typography, 
  Box, 
  TextField, 
  Button, 
  CircularProgress,
  Autocomplete,
  Paper,
  Grid,
  Divider,
  Chip
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import InfoIcon from '@mui/icons-material/Info';
import '../styles/FundamentalAnalysisPage.css';

interface CryptoOption {
  id: string;
  name: string;
  symbol: string;
}

const FundamentalAnalysisPage: React.FC = () => {
  const { symbol } = useParams<{ symbol?: string }>();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [analysis, setAnalysis] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [cryptoOptions, setCryptoOptions] = useState<CryptoOption[]>([]);
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoOption | null>(null);

  // Cargar opciones de criptomonedas
  useEffect(() => {
    const fetchCryptoOptions = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/crypto/top');
        if (!response.ok) {
          throw new Error('Error al obtener lista de criptomonedas');
        }
        const data = await response.json();
        
        // Transformar los datos al formato requerido por Autocomplete
        const options = data.map((crypto: any) => ({
          id: crypto.id,
          name: crypto.name,
          symbol: crypto.symbol
        }));
        
        setCryptoOptions(options);
      } catch (error) {
        console.error('Error al cargar opciones de criptomonedas:', error);
        setError('No se pudieron cargar las opciones de criptomonedas');
      }
    };

    fetchCryptoOptions();
  }, []);

  // Cargar análisis si hay un símbolo en la URL
  useEffect(() => {
    if (symbol) {
      performAnalysis(symbol);
    }
  }, [symbol]);

  const performAnalysis = async (cryptoSymbol: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://localhost:3001/api/fundamental', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ symbol: cryptoSymbol })
      });
      
      if (!response.ok) {
        throw new Error('Error al realizar análisis fundamental');
      }
      
      const data = await response.json();
      setAnalysis(data.analysis);
    } catch (error) {
      console.error('Error:', error);
      setError('No se pudo realizar el análisis fundamental. Por favor, inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (selectedCrypto) {
      navigate(`/fundamental/${selectedCrypto.symbol}`);
      performAnalysis(selectedCrypto.symbol);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && selectedCrypto) {
      handleSearch();
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" className="fundamental-analysis-page">
        <Box className="page-header">
          <Typography variant="h4" component="h1">
            Análisis Fundamental
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Analiza los fundamentos de cualquier criptomoneda para tomar decisiones informadas
          </Typography>
        </Box>

        <Paper elevation={3} className="search-container">
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Autocomplete
                options={cryptoOptions}
                getOptionLabel={(option) => `${option.name} (${option.symbol})`}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Buscar criptomoneda"
                    variant="outlined"
                    fullWidth
                    onKeyPress={handleKeyPress}
                  />
                )}
                value={selectedCrypto}
                onChange={(event, newValue) => {
                  setSelectedCrypto(newValue);
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                variant="contained"
                color="primary"
                fullWidth
                onClick={handleSearch}
                disabled={!selectedCrypto || loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SearchIcon />}
              >
                {loading ? 'Analizando...' : 'Analizar'}
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {error && (
          <Paper elevation={3} className="error-container">
            <Typography color="error">{error}</Typography>
          </Paper>
        )}

        <Box className="popular-searches">
          <Typography variant="subtitle1" gutterBottom>
            Análisis populares:
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            <Chip 
              label="Bitcoin (BTC)" 
              clickable 
              onClick={() => {
                navigate('/fundamental/BTC');
                performAnalysis('BTC');
              }} 
            />
            <Chip 
              label="Ethereum (ETH)" 
              clickable 
              onClick={() => {
                navigate('/fundamental/ETH');
                performAnalysis('ETH');
              }} 
            />
            <Chip 
              label="Binance Coin (BNB)" 
              clickable 
              onClick={() => {
                navigate('/fundamental/BNB');
                performAnalysis('BNB');
              }} 
            />
            <Chip 
              label="Solana (SOL)" 
              clickable 
              onClick={() => {
                navigate('/fundamental/SOL');
                performAnalysis('SOL');
              }} 
            />
            <Chip 
              label="Cardano (ADA)" 
              clickable 
              onClick={() => {
                navigate('/fundamental/ADA');
                performAnalysis('ADA');
              }} 
            />
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
            <CircularProgress />
          </Box>
        ) : analysis ? (
          <FundamentalAnalysisCard analysis={analysis} />
        ) : (
          <Paper elevation={3} className="info-container">
            <Box display="flex" alignItems="flex-start" gap={2}>
              <InfoIcon color="primary" fontSize="large" />
              <Box>
                <Typography variant="h6">¿Qué es el análisis fundamental?</Typography>
                <Typography variant="body1">
                  El análisis fundamental evalúa el valor intrínseco de una criptomoneda examinando factores como:
                </Typography>
                <ul>
                  <li>Capitalización de mercado y volumen de negociación</li>
                  <li>Tokenomics (suministro, inflación, distribución)</li>
                  <li>Equipo de desarrollo y actividad en GitHub</li>
                  <li>Casos de uso y adopción real</li>
                  <li>Competencia y ventajas competitivas</li>
                  <li>Métricas on-chain (transacciones, direcciones activas)</li>
                </ul>
                <Typography variant="body1">
                  Utiliza el buscador para analizar cualquier criptomoneda o selecciona una de las opciones populares.
                </Typography>
              </Box>
            </Box>
          </Paper>
        )}
      </Container>
    </Layout>
  );
};

export default FundamentalAnalysisPage;
