.market-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.market-summary h2 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.market-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.metric {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.metric-label {
  font-size: 0.875rem;
  color: #666;
  margin-right: 0.5rem;
}

.metric-value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-right: 0.5rem;
}

.metric-change {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.metric-change.positive {
  color: #00C853;
  background-color: rgba(0, 200, 83, 0.1);
}

.metric-change.negative {
  color: #FF3D00;
  background-color: rgba(255, 61, 0, 0.1);
}

.fear-index-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 0.5rem;
}

.fear-index-indicator.extreme-fear {
  background-color: #FF3D00;
}

.fear-index-indicator.fear {
  background-color: #FF9100;
}

.fear-index-indicator.neutral {
  background-color: #FFEA00;
}

.fear-index-indicator.greed {
  background-color: #76FF03;
}

.fear-index-indicator.extreme-greed {
  background-color: #00C853;
}

.market-summary.loading,
.market-summary.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 1.5rem;
}

.market-summary.error {
  background-color: #fff8f8;
}

.error-message {
  color: #d32f2f;
  margin-top: 0.5rem;
  font-size: 0.9375rem;
}

@media (max-width: 768px) {
  .market-metrics {
    flex-direction: column;
    gap: 1rem;
  }

  .metric {
    width: 100%;
    justify-content: space-between;
  }
}
