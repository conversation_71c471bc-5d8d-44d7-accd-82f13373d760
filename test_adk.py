"""
Test script for ADK
"""
import os
import asyncio
from google.adk.agents.llm_agent import LlmAgent
from google.adk.events import Event

# Set the Google API key
os.environ["GOOGLE_API_KEY"] = "AIzaSyCashyNX3fNv-bj5xOCtTDMfL_-vGm9Yag"

# Create a simple agent
agent = LlmAgent(
    name="test_agent",
    model="gemini-1.5-pro",
    description="A test agent",
    instruction="You are a helpful assistant."
)

# Create a simple invocation context
class SimpleInvocationContext:
    def __init__(self, query):
        self.query = query
        self.session_id = "test_session"
        self.app_name = "test_app"
        self.user_id = "test_user"

# Run the agent
async def main():
    # Create a simple context
    context = SimpleInvocationContext("Hello, how are you?")

    # Run the agent
    async for event in agent.run_async(context):
        if isinstance(event, Event):
            print(f"Event type: {type(event).__name__}")
            print(f"Event content: {event.model_dump()}")

# Run the main function
if __name__ == "__main__":
    asyncio.run(main())
