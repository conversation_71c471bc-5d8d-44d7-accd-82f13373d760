.enhanced-auth-debug {
  background: rgba(15, 15, 35, 0.7);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(64, 220, 255, 0.2);
  color: white;
  max-width: 600px;
  margin: 20px auto;
}

.enhanced-auth-debug h3 {
  margin-top: 0;
  color: #00f2ff;
  font-size: 20px;
  border-bottom: 1px solid rgba(64, 220, 255, 0.3);
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.enhanced-auth-debug h4 {
  margin: 15px 0 10px;
  color: white;
  font-size: 16px;
}

.auth-status {
  background: rgba(10, 10, 26, 0.5);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(64, 220, 255, 0.1);
}

.status-indicator {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
  margin-bottom: 10px;
}

.status-indicator.logged-in {
  background: rgba(0, 200, 83, 0.2);
  color: #00c853;
  border: 1px solid rgba(0, 200, 83, 0.3);
}

.status-indicator.logged-out {
  background: rgba(255, 82, 82, 0.2);
  color: #ff5252;
  border: 1px solid rgba(255, 82, 82, 0.3);
}

.user-info {
  background: rgba(64, 220, 255, 0.1);
  border-radius: 6px;
  padding: 10px;
}

.user-info p {
  margin: 5px 0;
  font-size: 14px;
}

.auth-tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(64, 220, 255, 0.2);
}

.tab-button {
  flex: 1;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 10px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.tab-button:hover {
  color: white;
  background: rgba(64, 220, 255, 0.1);
}

.tab-button.active {
  color: #00f2ff;
  border-bottom: 2px solid #00f2ff;
}

.auth-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: rgba(255, 255, 255, 0.8);
}

.form-group input {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid rgba(64, 220, 255, 0.3);
  background: rgba(10, 10, 26, 0.5);
  color: white;
  font-size: 14px;
}

.form-group input:focus {
  outline: none;
  border-color: rgba(64, 220, 255, 0.6);
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.2);
}

.action-button {
  width: 100%;
  padding: 12px;
  border-radius: 6px;
  border: none;
  background: linear-gradient(90deg, #4657ce, #00f2ff);
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 242, 255, 0.3);
}

.action-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.result-container {
  background: rgba(10, 10, 26, 0.5);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(64, 220, 255, 0.1);
}

.result-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
  margin-bottom: 10px;
}

.result-status.success {
  background: rgba(0, 200, 83, 0.2);
  color: #00c853;
  border: 1px solid rgba(0, 200, 83, 0.3);
}

.result-status.error {
  background: rgba(255, 82, 82, 0.2);
  color: #ff5252;
  border: 1px solid rgba(255, 82, 82, 0.3);
}

.result-data {
  background: rgba(15, 15, 35, 0.5);
  padding: 10px;
  border-radius: 6px;
  overflow: auto;
  max-height: 200px;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  color: rgba(255, 255, 255, 0.9);
}
