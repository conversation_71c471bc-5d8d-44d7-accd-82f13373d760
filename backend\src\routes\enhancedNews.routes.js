const express = require('express');
const router = express.Router();
const { searchCryptoNewsWithSentiment } = require('../services/enhancedNews.service');

/**
 * @route POST /api/enhanced-news/search
 * @description Busca noticias sobre criptomonedas con análisis de sentimiento
 * @param {string} query - Consulta de búsqueda
 * @param {number} count - Número de resultados a devolver (opcional, por defecto 10)
 * @param {string} freshness - Frescura de los resultados (opcional, por defecto 'pw')
 * @access Public
 */
router.post('/search', async (req, res) => {
  try {
    const { query, count = 10, freshness = 'pw' } = req.body;
    
    if (!query) {
      return res.status(400).json({ error: 'Se requiere una consulta de búsqueda' });
    }
    
    console.log(`Buscando noticias para "${query}" (${count} resultados, frescura: ${freshness})...`);
    
    const results = await searchCryptoNewsWithSentiment(query, count, freshness);
    
    res.json(results);
  } catch (error) {
    console.error('Error al buscar noticias:', error);
    res.status(500).json({ error: 'Error al buscar noticias' });
  }
});

/**
 * @route GET /api/enhanced-news/trending
 * @description Obtiene noticias tendencia sobre criptomonedas
 * @access Public
 */
router.get('/trending', async (req, res) => {
  try {
    console.log('Obteniendo noticias tendencia sobre criptomonedas...');
    
    // Buscar noticias sobre el mercado de criptomonedas en general
    const results = await searchCryptoNewsWithSentiment('cryptocurrency market trends', 10, 'pd');
    
    res.json(results);
  } catch (error) {
    console.error('Error al obtener noticias tendencia:', error);
    res.status(500).json({ error: 'Error al obtener noticias tendencia' });
  }
});

/**
 * @route GET /api/enhanced-news/crypto/:symbol
 * @description Obtiene noticias sobre una criptomoneda específica
 * @param {string} symbol - Símbolo de la criptomoneda
 * @access Public
 */
router.get('/crypto/:symbol', async (req, res) => {
  try {
    const { symbol } = req.params;
    const count = req.query.count ? parseInt(req.query.count) : 10;
    const freshness = req.query.freshness || 'pw';
    
    if (!symbol) {
      return res.status(400).json({ error: 'Se requiere el símbolo de la criptomoneda' });
    }
    
    console.log(`Obteniendo noticias para ${symbol}...`);
    
    // Mapear símbolos comunes a nombres completos para mejorar la búsqueda
    const symbolMap = {
      'BTC': 'Bitcoin',
      'ETH': 'Ethereum',
      'BNB': 'Binance Coin',
      'SOL': 'Solana',
      'ADA': 'Cardano',
      'XRP': 'Ripple',
      'DOT': 'Polkadot',
      'DOGE': 'Dogecoin',
      'AVAX': 'Avalanche',
      'MATIC': 'Polygon'
    };
    
    // Usar el nombre completo si está disponible, de lo contrario usar el símbolo
    const searchQuery = symbolMap[symbol.toUpperCase()] || symbol;
    
    const results = await searchCryptoNewsWithSentiment(searchQuery, count, freshness);
    
    res.json(results);
  } catch (error) {
    console.error(`Error al obtener noticias para ${req.params.symbol}:`, error);
    res.status(500).json({ error: 'Error al obtener noticias' });
  }
});

module.exports = router;
