import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/NewAuthContext';
import { usePortfolio } from '../hooks/usePortfolio';
import { PortfolioAsset } from '../services/portfolio.service';
import AnimatedPortfolioChart from './AnimatedPortfolioChart';
import TransactionHistory from './TransactionHistory';
import anime from '../utils/animeUtils';
import '../styles/EnhancedPortfolio.css';

const EnhancedPortfolio = () => {
  const { currentUser } = useAuth();
  const {
    portfolio,
    fullPortfolio,
    transactions,
    portfolioStats,
    isLoading,
    error,
    availableCryptos,
    currentPrices,
    addAssetToPortfolio,
    removeAssetFromPortfolio,
    updateAsset,
    refreshPrices,
    calculateAssetStats
  } = usePortfolio();

  const [showTransactions, setShowTransactions] = useState(false);

  const [showAddAssetForm, setShowAddAssetForm] = useState(false);
  const [selectedCryptoId, setSelectedCryptoId] = useState('');
  const [amount, setAmount] = useState('');
  const [purchasePrice, setPurchasePrice] = useState('');
  const [formError, setFormError] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Formatear números para mostrar
  const formatNumber = (num: number, decimals = 2) => {
    return num.toLocaleString('es-ES', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    });
  };

  // Formatear fecha para mostrar
  const formatDate = (date: Date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Manejar actualización de precios
  const handleRefreshPrices = async () => {
    setIsRefreshing(true);

    // Animar el botón de actualizar
    anime({
      targets: '.refresh-button',
      rotate: 360,
      duration: 1000,
      easing: 'easeInOutQuad'
    });

    await refreshPrices();
    setIsRefreshing(false);

    // Animar los valores actualizados
    anime({
      targets: '.asset-value, .profit-loss-value',
      backgroundColor: ['rgba(64, 220, 255, 0.2)', 'rgba(64, 220, 255, 0)'],
      duration: 1500,
      easing: 'easeOutQuad'
    });
  };

  // Manejar eliminación de activo
  const handleRemoveAsset = async (assetId: string) => {
    if (window.confirm('¿Estás seguro de que deseas eliminar este activo de tu cartera?')) {
      // Animar la fila que se va a eliminar
      anime({
        targets: `#asset-${assetId}`,
        translateX: 20,
        opacity: 0,
        duration: 500,
        easing: 'easeOutQuad',
        complete: async () => {
          await removeAssetFromPortfolio(assetId);
        }
      });
    }
  };

  // Manejar envío del formulario
  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');

    // Validar formulario
    if (!selectedCryptoId) {
      setFormError('Por favor, selecciona una criptomoneda.');
      return;
    }

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      setFormError('Por favor, ingresa una cantidad válida.');
      return;
    }

    if (!purchasePrice || isNaN(parseFloat(purchasePrice)) || parseFloat(purchasePrice) < 0) {
      setFormError('Por favor, ingresa un precio de compra válido.');
      return;
    }

    try {
      // Obtener detalles de la criptomoneda seleccionada
      const selectedCrypto = availableCryptos.find(crypto => crypto.id === selectedCryptoId);

      // Crear el nuevo activo
      const newAsset: PortfolioAsset = {
        id: selectedCryptoId,
        symbol: selectedCrypto.symbol.toUpperCase(),
        name: selectedCrypto.name,
        amount: parseFloat(amount),
        purchasePrice: parseFloat(purchasePrice),
        purchaseDate: new Date()
      };

      // Añadir el activo a la cartera
      const success = await addAssetToPortfolio(newAsset);

      if (success) {
        // Resetear el formulario
        setSelectedCryptoId('');
        setAmount('');
        setPurchasePrice('');
        setShowAddAssetForm(false);

        // Animar la aparición del nuevo activo
        setTimeout(() => {
          anime({
            targets: '.assets-table tr:last-child',
            translateY: [20, 0],
            opacity: [0, 1],
            duration: 800,
            easing: 'spring(1, 80, 10, 0)'
          });
        }, 100);
      }
    } catch (err) {
      console.error('Error al añadir activo:', err);
      setFormError('No se pudo añadir el activo. Por favor, intenta de nuevo.');
    }
  };

  // Preparar datos para la gráfica
  const chartData = portfolio.map(asset => {
    const stats = calculateAssetStats(asset);
    return {
      id: asset.id,
      name: asset.name,
      symbol: asset.symbol,
      amount: asset.amount,
      value: stats.value
    };
  });

  return (
    <div className="enhanced-portfolio">
      <div className="portfolio-header">
        <h2>Mi Cartera</h2>
        <div className="portfolio-actions">
          <button
            className="refresh-button"
            onClick={handleRefreshPrices}
            disabled={isLoading || isRefreshing || portfolio.length === 0}
          >
            <span className="refresh-icon">↻</span>
            <span className="button-text">Actualizar Precios</span>
          </button>
          <button
            className="add-asset-button"
            onClick={() => setShowAddAssetForm(!showAddAssetForm)}
          >
            <span className="add-icon">{showAddAssetForm ? '✕' : '+'}</span>
            <span className="button-text">{showAddAssetForm ? 'Cancelar' : 'Añadir Activo'}</span>
          </button>
          <button
            className="transactions-button"
            onClick={() => setShowTransactions(!showTransactions)}
          >
            <span className="transactions-icon">📋</span>
            <span className="button-text">{showTransactions ? 'Ocultar Historial' : 'Ver Historial'}</span>
          </button>
        </div>
      </div>

      {showAddAssetForm && (
        <div className="add-asset-form-container">
          <form onSubmit={handleSubmitForm} className="add-asset-form">
            <h3>Añadir Nuevo Activo</h3>

            <div className="form-group">
              <label htmlFor="crypto-select">Criptomoneda</label>
              <select
                id="crypto-select"
                value={selectedCryptoId}
                onChange={(e) => setSelectedCryptoId(e.target.value)}
                required
              >
                <option value="">Selecciona una criptomoneda</option>
                {availableCryptos.map((crypto) => (
                  <option key={crypto.id} value={crypto.id}>
                    {crypto.name} ({crypto.symbol.toUpperCase()})
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="amount-input">Cantidad</label>
              <input
                id="amount-input"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Ej: 0.5"
                step="any"
                min="0"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="price-input">Precio de Compra (USD)</label>
              <input
                id="price-input"
                type="number"
                value={purchasePrice}
                onChange={(e) => setPurchasePrice(e.target.value)}
                placeholder="Ej: 30000"
                step="any"
                min="0"
                required
              />
            </div>

            {formError && <div className="form-error">{formError}</div>}

            <div className="form-actions">
              <button type="button" onClick={() => setShowAddAssetForm(false)} className="cancel-button">
                Cancelar
              </button>
              <button type="submit" className="submit-button">
                Añadir a Cartera
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="portfolio-overview">
        <div className="portfolio-summary">
          <div className="summary-card total-value">
            <div className="summary-icon">💰</div>
            <div className="summary-details">
              <span className="summary-label">Valor Total:</span>
              <span className="summary-value">${formatNumber(portfolioStats.totalValue)}</span>
            </div>
          </div>

          <div className="summary-card profit-loss">
            <div className="summary-icon">📈</div>
            <div className="summary-details">
              <span className="summary-label">Ganancia/Pérdida:</span>
              <span className={`summary-value ${portfolioStats.totalProfitLoss >= 0 ? 'positive' : 'negative'}`}>
                ${formatNumber(portfolioStats.totalProfitLoss)}
                ({formatNumber(portfolioStats.totalProfitLossPercentage)}%)
              </span>
            </div>
          </div>

          <div className="summary-card last-update">
            <div className="summary-icon">🕒</div>
            <div className="summary-details">
              <span className="summary-label">Última Actualización:</span>
              <span className="summary-value date">
                {formatDate(portfolioStats.lastUpdated)}
              </span>
            </div>
          </div>
        </div>

        {portfolio.length > 0 && (
          <div className="portfolio-chart-container">
            <AnimatedPortfolioChart
              assets={chartData}
              totalValue={portfolioStats.totalValue}
              width={350}
              height={350}
              title="Distribución del Portafolio"
              totalLabel="Valor Total"
            />
          </div>
        )}
      </div>

      {portfolio.length > 0 ? (
        <div className="portfolio-assets">
          <table className="assets-table">
            <thead>
              <tr>
                <th>Activo</th>
                <th>Cantidad</th>
                <th>Precio de Compra</th>
                <th>Precio Actual</th>
                <th>Valor</th>
                <th>Ganancia/Pérdida</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              {portfolio.map((asset) => {
                const stats = calculateAssetStats(asset);
                return (
                  <tr key={asset.id} id={`asset-${asset.id}`}>
                    <td className="asset-info">
                      {availableCryptos.find(c => c.id === asset.id)?.image && (
                        <img
                          src={availableCryptos.find(c => c.id === asset.id)?.image}
                          alt={asset.name}
                          className="asset-icon"
                        />
                      )}
                      <div>
                        <span className="asset-name">{asset.name}</span>
                        <span className="asset-symbol">{asset.symbol}</span>
                      </div>
                    </td>
                    <td>{formatNumber(asset.amount, 8)}</td>
                    <td>${formatNumber(asset.purchasePrice)}</td>
                    <td className="current-price">${formatNumber(stats.currentPrice)}</td>
                    <td className="asset-value">${formatNumber(stats.value)}</td>
                    <td className={`profit-loss-value ${stats.profitLoss >= 0 ? 'positive' : 'negative'}`}>
                      ${formatNumber(stats.profitLoss)}
                      ({formatNumber(stats.profitLossPercentage)}%)
                    </td>
                    <td>
                      <button
                        className="remove-asset-button"
                        onClick={() => handleRemoveAsset(asset.id)}
                      >
                        <span className="remove-icon">✕</span>
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="empty-portfolio">
          <div className="empty-icon">💼</div>
          <h3>Tu cartera está vacía</h3>
          <p>Haz clic en "Añadir Activo" para comenzar a construir tu cartera de criptomonedas.</p>
          <button
            className="add-first-asset-button"
            onClick={() => setShowAddAssetForm(true)}
          >
            Añadir Primer Activo
          </button>
        </div>
      )}

      {/* Historial de Transacciones */}
      {showTransactions && (
        <div className="transaction-history-section">
          <TransactionHistory />
        </div>
      )}
    </div>
  );
};

export default EnhancedPortfolio;
