.simple-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-color: var(--color-background, #0f1123);
  overflow: hidden;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(123, 77, 255, 0.08) 0%, transparent 60%),
              radial-gradient(circle at bottom left, rgba(0, 224, 255, 0.08) 0%, transparent 60%),
              linear-gradient(to bottom, rgba(15, 17, 35, 0), rgba(15, 17, 35, 0.1) 100%);
}
