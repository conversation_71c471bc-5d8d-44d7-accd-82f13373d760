import React, { useState } from 'react';
import '../styles/CoinMarkets.css';

interface CoinMarketsProps {
  markets: any[];
  coinSymbol: string;
  formatNumber: (num: number, maximumFractionDigits?: number) => string;
}

const CoinMarkets: React.FC<CoinMarketsProps> = ({ 
  markets, 
  coinSymbol, 
  formatNumber 
}) => {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'ascending' | 'descending';
  }>({
    key: 'volume',
    direction: 'descending'
  });
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const itemsPerPage = 10;

  // Función para manejar el cambio de ordenación
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Función para obtener la clase de la columna de ordenación
  const getSortDirectionClass = (key: string) => {
    if (sortConfig.key !== key) return '';
    return sortConfig.direction === 'ascending' ? 'ascending' : 'descending';
  };

  // Filtrar mercados por búsqueda
  const filteredMarkets = markets.filter(market => 
    market.exchange.toLowerCase().includes(searchQuery.toLowerCase()) ||
    market.pair.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Ordenar mercados
  const sortedMarkets = React.useMemo(() => {
    const sortableItems = [...filteredMarkets];
    sortableItems.sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? 1 : -1;
      }
      return 0;
    });
    return sortableItems;
  }, [filteredMarkets, sortConfig]);

  // Calcular paginación
  const totalPages = Math.ceil(sortedMarkets.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedMarkets.slice(indexOfFirstItem, indexOfLastItem);

  // Función para cambiar de página
  const paginate = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Renderizar un esqueleto de carga si no hay datos
  if (!markets || markets.length === 0) {
    return (
      <div className="coin-markets-container">
        <div className="markets-header">
          <h3>Mercados para {coinSymbol}</h3>
          <div className="search-bar">
            <input
              type="text"
              placeholder="Buscar mercado o par..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <i className="fas fa-search"></i>
          </div>
        </div>
        <div className="markets-empty">
          <i className="fas fa-exchange-alt"></i>
          <p>No hay datos de mercados disponibles para {coinSymbol}.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="coin-markets-container">
      <div className="markets-header">
        <h3>Mercados para {coinSymbol}</h3>
        <div className="search-bar">
          <input
            type="text"
            placeholder="Buscar mercado o par..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <i className="fas fa-search"></i>
        </div>
      </div>
      
      <div className="markets-table-container">
        <table className="markets-table">
          <thead>
            <tr>
              <th 
                onClick={() => requestSort('exchange')} 
                className={getSortDirectionClass('exchange')}
              >
                Exchange
              </th>
              <th 
                onClick={() => requestSort('pair')} 
                className={getSortDirectionClass('pair')}
              >
                Par
              </th>
              <th 
                onClick={() => requestSort('price')} 
                className={getSortDirectionClass('price')}
              >
                Precio
              </th>
              <th 
                onClick={() => requestSort('volume')} 
                className={getSortDirectionClass('volume')}
              >
                Volumen 24h
              </th>
              <th 
                onClick={() => requestSort('trust_score')} 
                className={getSortDirectionClass('trust_score')}
              >
                Confianza
              </th>
              <th>Acciones</th>
            </tr>
          </thead>
          <tbody>
            {currentItems.map((market, index) => (
              <tr key={`${market.exchange}-${market.pair}-${index}`}>
                <td className="exchange-cell">
                  <div className="exchange-info">
                    {market.exchange_logo ? (
                      <img 
                        src={market.exchange_logo} 
                        alt={market.exchange} 
                        className="exchange-logo" 
                      />
                    ) : (
                      <div className="exchange-logo-placeholder">
                        {market.exchange.charAt(0).toUpperCase()}
                      </div>
                    )}
                    <span className="exchange-name">{market.exchange}</span>
                  </div>
                </td>
                <td className="pair-cell">{market.pair}</td>
                <td className="price-cell">${formatNumber(market.price)}</td>
                <td className="volume-cell">${formatNumber(market.volume)}</td>
                <td className="trust-cell">
                  <div className={`trust-score trust-score-${market.trust_score || 'unknown'}`}>
                    {market.trust_score ? (
                      <>
                        {Array.from({ length: market.trust_score }).map((_, i) => (
                          <i key={i} className="fas fa-circle"></i>
                        ))}
                        {Array.from({ length: 5 - market.trust_score }).map((_, i) => (
                          <i key={i} className="far fa-circle"></i>
                        ))}
                      </>
                    ) : (
                      'N/A'
                    )}
                  </div>
                </td>
                <td className="actions-cell">
                  <a 
                    href={market.trade_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="trade-button"
                  >
                    Operar
                  </a>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Paginación */}
      {totalPages > 1 && (
        <div className="markets-pagination">
          <button 
            className="pagination-button"
            onClick={() => paginate(1)}
            disabled={currentPage === 1}
          >
            <i className="fas fa-angle-double-left"></i>
          </button>
          <button 
            className="pagination-button"
            onClick={() => paginate(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <i className="fas fa-angle-left"></i>
          </button>
          
          <div className="pagination-info">
            Página {currentPage} de {totalPages}
          </div>
          
          <button 
            className="pagination-button"
            onClick={() => paginate(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <i className="fas fa-angle-right"></i>
          </button>
          <button 
            className="pagination-button"
            onClick={() => paginate(totalPages)}
            disabled={currentPage === totalPages}
          >
            <i className="fas fa-angle-double-right"></i>
          </button>
        </div>
      )}
    </div>
  );
};

export default CoinMarkets;
