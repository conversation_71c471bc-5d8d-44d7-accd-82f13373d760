import React, { useState, useEffect } from 'react';
import CriptoAgentAvatar from './CriptoAgentAvatar';
import '../styles/GuruInsightWidget.css';

interface GuruInsightWidgetProps {
  onOpenChat: () => void;
}

const GuruInsightWidget: React.FC<GuruInsightWidgetProps> = ({ onOpenChat }) => {
  const [latestInsight, setLatestInsight] = useState<string>('');
  const [mood, setMood] = useState<'neutral' | 'happy' | 'thinking' | 'concerned'>('neutral');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Efecto para cargar el último mensaje del Gurú
  useEffect(() => {
    const fetchLatestInsight = async () => {
      setIsLoading(true);
      try {
        // Intentar obtener el último mensaje del almacenamiento local
        const storedMessages = localStorage.getItem('guru_chat_messages');

        if (storedMessages) {
          const messages = JSON.parse(storedMessages);
          // Buscar el último mensaje del Gurú
          const aiMessages = messages.filter((msg: any) => msg.sender === 'ai');

          if (aiMessages.length > 0) {
            const lastMessage = aiMessages[aiMessages.length - 1];
            setLatestInsight(lastMessage.text);

            // Determinar el estado de ánimo basado en el contenido del mensaje
            if (lastMessage.text.toLowerCase().includes('aument') ||
                lastMessage.text.toLowerCase().includes('positiv') ||
                lastMessage.text.toLowerCase().includes('crec')) {
              setMood('happy');
            } else if (lastMessage.text.toLowerCase().includes('disminuy') ||
                      lastMessage.text.toLowerCase().includes('caid') ||
                      lastMessage.text.toLowerCase().includes('negativ')) {
              setMood('concerned');
            } else {
              setMood('neutral');
            }
          } else {
            // Si no hay mensajes del Gurú, mostrar un mensaje predeterminado
            setLatestInsight('Hola, soy el Gurú Cripto. Pregúntame sobre criptomonedas, tendencias del mercado o cualquier duda que tengas.');
          }
        } else {
          // Si no hay mensajes almacenados, mostrar un mensaje predeterminado
          setLatestInsight('Hola, soy el Gurú Cripto. Pregúntame sobre criptomonedas, tendencias del mercado o cualquier duda que tengas.');
        }
      } catch (error) {
        console.error('Error al cargar el último mensaje del Gurú:', error);
        setLatestInsight('Hola, soy el Gurú Cripto. Pregúntame sobre criptomonedas, tendencias del mercado o cualquier duda que tengas.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLatestInsight();
  }, []);

  // Truncar el mensaje si es muy largo
  const truncateMessage = (message: string, maxLength: number = 150): string => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  return (
    <div className="guru-insight-widget">
      <div className="guru-avatar-container">
        <CriptoAgentAvatar
          mood={mood}
          size="medium"
          pulseEffect={true}
        />
      </div>
      <div className="guru-message-container">
        {isLoading ? (
          <div className="loading-message">Cargando...</div>
        ) : (
          <p className="guru-message">{truncateMessage(latestInsight, 100)}</p>
        )}
        <button className="open-chat-button" onClick={onOpenChat}>
          Consultar al Gurú
        </button>
      </div>
    </div>
  );
};

export default GuruInsightWidget;
