/**
 * Guru Cripto Orchestrator Routes
 * 
 * This module provides routes to interact with the Guru Cripto Orchestrator.
 */

const express = require('express');
const router = express.Router();
const { startGuruOrchestrator, stopGuruOrchestrator, queryGuruOrchestrator } = require('../guru_orchestrator');
const logger = require('../utils/logger');

/**
 * @route POST /api/guru-orchestrator/query
 * @desc Send a query to the Guru Cripto Orchestrator
 * @access Public
 */
router.post('/query', async (req, res) => {
  try {
    const { query, sessionId } = req.body;
    
    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }
    
    // Generate a session ID if not provided
    const finalSessionId = sessionId || `session-${Date.now()}`;
    
    // Send the query to the Guru Cripto Orchestrator
    const response = await queryGuruOrchestrator(query, finalSessionId);
    
    res.json(response);
  } catch (error) {
    logger.error(`Error in /api/guru-orchestrator/query: ${error.message}`);
    res.status(500).json({ error: 'Error querying Guru Cripto Orchestrator' });
  }
});

/**
 * @route POST /api/guru-orchestrator/start
 * @desc Start the Guru Cripto Orchestrator
 * @access Public
 */
router.post('/start', async (req, res) => {
  try {
    await startGuruOrchestrator();
    res.json({ message: 'Guru Cripto Orchestrator started successfully' });
  } catch (error) {
    logger.error(`Error in /api/guru-orchestrator/start: ${error.message}`);
    res.status(500).json({ error: 'Error starting Guru Cripto Orchestrator' });
  }
});

/**
 * @route POST /api/guru-orchestrator/stop
 * @desc Stop the Guru Cripto Orchestrator
 * @access Public
 */
router.post('/stop', async (req, res) => {
  try {
    await stopGuruOrchestrator();
    res.json({ message: 'Guru Cripto Orchestrator stopped successfully' });
  } catch (error) {
    logger.error(`Error in /api/guru-orchestrator/stop: ${error.message}`);
    res.status(500).json({ error: 'Error stopping Guru Cripto Orchestrator' });
  }
});

/**
 * @route GET /api/guru-orchestrator/status
 * @desc Get the status of the Guru Cripto Orchestrator
 * @access Public
 */
router.get('/status', async (req, res) => {
  try {
    // Check if the orchestrator is running
    const isRunning = global.guruOrchestratorProcess !== null && global.guruOrchestratorProcess !== undefined;
    
    res.json({
      status: isRunning ? 'running' : 'stopped'
    });
  } catch (error) {
    logger.error(`Error in /api/guru-orchestrator/status: ${error.message}`);
    res.status(500).json({ error: 'Error getting Guru Cripto Orchestrator status' });
  }
});

module.exports = router;
