[project]
name = "a2a-samples"
version = "0.1.0"
description = "Agent2Agent samples"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "httpx>=0.28.1",
    "httpx-sse>=0.4.0",
    "jwcrypto>=1.5.6",
    "pydantic>=2.10.6",
    "pyjwt>=2.10.1",
    "sse-starlette>=2.2.1",
    "starlette>=0.46.1",
    "typing-extensions>=4.12.2",
    "uvicorn>=0.34.0",
]

[tool.hatch.build.targets.wheel]
packages = ["common", "hosts"]

[tool.uv.workspace]
members = [
    "agents/crewai",
    "agents/google_adk",
    "agents/langgraph",
    "agents/marvin",
    "hosts/cli",
    "hosts/multiagent",
    "agents/llama_index_file_chat",
    "agents/semantickernel",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = ["pytest>=8.3.5", "pytest-mock>=3.14.0", "ruff>=0.11.2"]
