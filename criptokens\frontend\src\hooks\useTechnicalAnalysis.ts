import { useState, useEffect } from 'react';

interface TechnicalAnalysisParams {
  symbol: string;
  interval?: string;
  limit?: number;
}

interface Indicator {
  rsi: number;
  macd: {
    macd: number;
    signal?: number;
    histogram?: number;
  };
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
  };
  sma50?: number;
  sma200?: number;
}

interface Pattern {
  bullish: Record<string, boolean[]>;
  bearish: Record<string, boolean[]>;
}

interface Signal {
  recommendation: 'COMPRA' | 'VENTA' | 'NEUTRAL';
  buySignals: number;
  sellSignals: number;
  neutralSignals: number;
  details: {
    buy: string[];
    sell: string[];
    neutral: string[];
  };
}

export interface TechnicalAnalysisResult {
  indicators: Indicator;
  patterns: Pattern;
  signals: Signal;
}

export const useTechnicalAnalysis = ({ symbol, interval = '1d', limit = 30 }: TechnicalAnalysisParams) => {
  const [data, setData] = useState<TechnicalAnalysisResult | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalysis = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/technical/analysis/${symbol}?interval=${interval}&limit=${limit}`);
        
        if (!response.ok) {
          throw new Error(`Error al obtener análisis técnico: ${response.statusText}`);
        }
        
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error desconocido');
        console.error('Error al obtener análisis técnico:', err);
      } finally {
        setLoading(false);
      }
    };
    
    if (symbol) {
      fetchAnalysis();
    }
  }, [symbol, interval, limit]);

  const refetch = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/technical/analysis/${symbol}?interval=${interval}&limit=${limit}&t=${Date.now()}`);
      
      if (!response.ok) {
        throw new Error(`Error al obtener análisis técnico: ${response.statusText}`);
      }
      
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
      console.error('Error al obtener análisis técnico:', err);
    } finally {
      setLoading(false);
    }
  };

  return { data, loading, error, refetch };
};

export default useTechnicalAnalysis;
