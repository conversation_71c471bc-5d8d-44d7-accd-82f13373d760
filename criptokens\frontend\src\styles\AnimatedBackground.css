.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  background-color: var(--color-background, #0f1123);
  overflow: hidden;
}

/* Contenedor de partículas */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

/* Estilos para las partículas */
.particle {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(0, 242, 255, 0.7);
  pointer-events: none;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  width: 4px !important;
  height: 4px !important;
  box-shadow: 0 0 8px rgba(0, 242, 255, 0.6);
  z-index: 2;
}

/* Colores de partículas según el estado */
.particle.positive {
  background-color: rgba(0, 255, 157, 0.7);
  box-shadow: 0 0 8px rgba(0, 255, 157, 0.6);
}

.particle.negative {
  background-color: rgba(255, 58, 110, 0.7);
  box-shadow: 0 0 8px rgba(255, 58, 110, 0.6);
}

.particle.concerned {
  background-color: rgba(255, 204, 0, 0.7);
  box-shadow: 0 0 8px rgba(255, 204, 0, 0.6);
}

.particle.thinking {
  background-color: rgba(123, 77, 255, 0.7);
  box-shadow: 0 0 8px rgba(123, 77, 255, 0.6);
}

.particle.neutral, .particle.idle {
  background-color: rgba(0, 242, 255, 0.7);
  box-shadow: 0 0 8px rgba(0, 242, 255, 0.6);
}

/* Orbitas de fondo */
.background-orbit {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(0, 242, 255, 0.15);
  pointer-events: none;
}

.background-orbit-1 {
  width: 300px;
  height: 300px;
  border-color: rgba(123, 77, 255, 0.15);
}

.background-orbit-2 {
  width: 500px;
  height: 500px;
  border-color: rgba(0, 242, 255, 0.15);
}

.background-orbit-3 {
  width: 700px;
  height: 700px;
  border-color: rgba(123, 77, 255, 0.1);
}

/* Animación de respiración para el gradiente */
@keyframes gradient-breathe {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

/* Animación de pulso para la red */
@keyframes grid-pulse {
  0% {
    opacity: 0.03;
  }
  50% {
    opacity: 0.06;
  }
  100% {
    opacity: 0.03;
  }
}

/* Capa de gradiente con efecto de respiración */
.gradient-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 75% 25%, rgba(123, 77, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 25% 75%, rgba(0, 242, 255, 0.1) 0%, transparent 50%),
              linear-gradient(to bottom right, rgba(15, 17, 35, 0), rgba(70, 87, 206, 0.05) 50%, rgba(15, 17, 35, 0) 100%);
  animation: gradient-breathe 20s ease-in-out infinite;
}

/* Red tenue */
.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.04;
  animation: grid-pulse 15s ease-in-out infinite;
}

/* Líneas horizontales */
.grid-lines.horizontal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: repeating-linear-gradient(to bottom,
    rgba(0, 242, 255, 0.1) 0px,
    rgba(0, 242, 255, 0.1) 1px,
    transparent 1px,
    transparent 80px
  );
}

/* Líneas verticales */
.grid-lines.vertical {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: repeating-linear-gradient(to right,
    rgba(0, 242, 255, 0.1) 0px,
    rgba(0, 242, 255, 0.1) 1px,
    transparent 1px,
    transparent 80px
  );
}

/* Línea diagonal 1 */
.grid-lines.diagonal-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, transparent, transparent 49.9%, rgba(123, 77, 255, 0.05) 50%, transparent 50.1%, transparent);
}

/* Línea diagonal 2 */
.grid-lines.diagonal-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom left, transparent, transparent 49.9%, rgba(0, 242, 255, 0.05) 50%, transparent 50.1%, transparent);
}
