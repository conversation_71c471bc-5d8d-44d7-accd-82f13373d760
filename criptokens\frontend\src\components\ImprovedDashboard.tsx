import React, { useState, useEffect } from 'react';
import MarketSummary from './MarketSummary';
import CryptoDetail from './CryptoDetail';
import NewsSection from './NewsSection';
import EnhancedPortfolio from './EnhancedPortfolio';
import EnhancedChatInterface from './EnhancedChatInterface';
import CryptoTable from './CryptoTable';
import CriptoAgentAvatar from './CriptoAgentAvatar';
import McpInterface from './McpInterface';
import { getTopCryptocurrencies } from '../services/api';
import '../styles/EnhancedDashboard.css';

const ImprovedDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showMcpInterface, setShowMcpInterface] = useState(false);
  const [selectedCryptoId, setSelectedCryptoId] = useState<string | null>(null);
  const [cryptos, setCryptos] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [agentMood, setAgentMood] = useState<'neutral' | 'happy' | 'thinking' | 'excited' | 'concerned'>('neutral');

  // Cargar datos de criptomonedas
  useEffect(() => {
    const fetchCryptos = async () => {
      try {
        setIsLoading(true);
        const data = await getTopCryptocurrencies(20);
        setCryptos(data);

        // Determinar el estado de ánimo del agente basado en el mercado
        const marketSentiment = data.reduce((acc, crypto) => acc + crypto.price_change_percentage_24h, 0) / data.length;

        if (marketSentiment > 3) {
          setAgentMood('excited');
        } else if (marketSentiment > 1) {
          setAgentMood('happy');
        } else if (marketSentiment < -3) {
          setAgentMood('concerned');
        } else if (marketSentiment < -1) {
          setAgentMood('thinking');
        } else {
          setAgentMood('neutral');
        }
      } catch (error) {
        console.error('Error al cargar criptomonedas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCryptos();

    // Actualizar datos cada 60 segundos
    const interval = setInterval(fetchCryptos, 60000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="dashboard-container">
      <div className="dashboard-sidebar">
        <div className="logo">
          <div className="logo-icon">
            <div className="logo-core"></div>
            <div className="logo-ring ring1"></div>
            <div className="logo-ring ring2"></div>
          </div>
          <span className="logo-text">Criptokens</span>
        </div>

        <nav className="sidebar-nav">
          <button
            className={`nav-item ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            <span className="nav-icon">📊</span>
            <span className="nav-text">Dashboard</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'portfolio' ? 'active' : ''}`}
            onClick={() => setActiveTab('portfolio')}
          >
            <span className="nav-icon">💼</span>
            <span className="nav-text">Mi Cartera</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'news' ? 'active' : ''}`}
            onClick={() => setActiveTab('news')}
          >
            <span className="nav-icon">📰</span>
            <span className="nav-text">Noticias</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'chat' ? 'active' : ''}`}
            onClick={() => setActiveTab('chat')}
          >
            <span className="nav-icon">🤖</span>
            <span className="nav-text">Asistente IA</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'mcp' ? 'active' : ''}`}
            onClick={() => setActiveTab('mcp')}
          >
            <span className="nav-icon">🔌</span>
            <span className="nav-text">MCP</span>
          </button>
        </nav>
      </div>

      <div className="dashboard-main">
        <div className="dashboard-content">
          {activeTab === 'dashboard' && (
            selectedCryptoId ? (
              <CryptoDetail
                cryptoId={selectedCryptoId}
                onBack={() => setSelectedCryptoId(null)}
              />
            ) : (
              <div className="dashboard-overview">
                <MarketSummary />
                <div className="dashboard-main-content">
                  <div className="main-section">
                    {isLoading ? (
                      <div className="loading-container">
                        <div className="loading-spinner"></div>
                        <p>Cargando criptomonedas...</p>
                      </div>
                    ) : (
                      <CryptoTable onSelectCrypto={(id) => setSelectedCryptoId(id)} />
                    )}
                  </div>
                  <div className="side-section">
                    <div className="chat-widget">
                      <div className="widget-header">
                        <CriptoAgentAvatar
                          mood={agentMood}
                          size="small"
                          pulseEffect={true}
                        />
                        <h3>Asistente Cripto</h3>
                      </div>
                      <p>Pregunta sobre criptomonedas, blockchain o cualquier duda que tengas.</p>
                      <button
                        className="open-chat-button"
                        onClick={() => setActiveTab('chat')}
                      >
                        Abrir Chat
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}

          {activeTab === 'portfolio' && (
            <EnhancedPortfolio />
          )}

          {activeTab === 'news' && (
            <NewsSection />
          )}

          {activeTab === 'chat' && (
            <div className="chat-fullscreen">
              <EnhancedChatInterface />
            </div>
          )}

          {activeTab === 'mcp' && (
            <div className="mcp-fullscreen">
              <McpInterface />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImprovedDashboard;
