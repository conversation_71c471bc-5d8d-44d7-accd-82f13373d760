import React from 'react';
import { NewsItem } from '../services/braveSearch';
import '../styles/NewsCard.css';
import { getNewsImage } from '../utils/imageUtils';

interface NewsCardProps {
  news: NewsItem;
  onClick?: () => void;
}

const NewsCard: React.FC<NewsCardProps> = ({ news, onClick }) => {
  // Formatear la fecha de publicación
  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'Fecha desconocida';

    try {
      const date = new Date(dateString);

      // Si la fecha es inválida, devolver un mensaje genérico
      if (isNaN(date.getTime())) {
        return 'Fecha desconocida';
      }

      // Calcular la diferencia de tiempo
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      // Formatear la fecha según la diferencia de tiempo
      if (diffMins < 60) {
        return `Hace ${diffMins} minutos`;
      } else if (diffHours < 24) {
        return `Hace ${diffHours} horas`;
      } else if (diffDays < 7) {
        return `Hace ${diffDays} días`;
      } else {
        return date.toLocaleDateString('es-ES', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      console.error('Error al formatear la fecha:', error);
      return 'Fecha desconocida';
    }
  };

  // Abrir la URL de la noticia en una nueva pestaña
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      window.open(news.url, '_blank', 'noopener,noreferrer');
    }
  };

  // Obtener la clase CSS según el sentimiento
  const getSentimentClass = (): string => {
    switch (news.sentiment) {
      case 'positive':
        return 'sentiment-positive';
      case 'negative':
        return 'sentiment-negative';
      default:
        return 'sentiment-neutral';
    }
  };

  // Usar la utilidad para obtener la imagen

  return (
    <div className={`news-card ${getSentimentClass()}`} onClick={handleClick}>
      <div className="news-image-container">
        <img
          src={getNewsImage(news.imageUrl)}
          alt={news.title}
          className="news-image"
          onError={(e) => {
            // Si la imagen falla, usar la imagen por defecto
            (e.target as HTMLImageElement).src = getNewsImage(null);
          }}
        />
      </div>
      <div className="news-content">
        <h3 className="news-title">{news.title}</h3>
        <p className="news-description">{news.description}</p>
        <div className="news-footer">
          <span className="news-source">{news.source}</span>
          <span className="news-date">{formatDate(news.publishedTime)}</span>
        </div>
      </div>
    </div>
  );
};

export default NewsCard;
