const express = require('express');
const router = express.Router();

/**
 * @route GET /api/conversations
 * @desc Obtiene todas las conversaciones de un usuario
 * @access Public
 */
router.get('/', async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'El ID de usuario es requerido'
      });
    }

    // Simulamos que hay conversaciones
    return res.status(200).json({
      success: true,
      conversations: [
        {
          id: '1',
          title: 'Conversación 1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId
        },
        {
          id: '2',
          title: 'Conversación 2',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          userId
        }
      ]
    });
  } catch (error) {
    console.error('Error obteniendo conversaciones:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener las conversaciones',
      error: error.message
    });
  }
});

/**
 * @route POST /api/conversations
 * @desc Crea una nueva conversación
 * @access Public
 */
router.post('/', async (req, res) => {
  try {
    const { userId, title } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'El ID de usuario es requerido'
      });
    }

    // Simulamos que se ha creado una conversación
    return res.status(201).json({
      success: true,
      conversation: {
        id: Math.random().toString(36).substring(7),
        title: title || 'Nueva conversación',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId
      }
    });
  } catch (error) {
    console.error('Error creando conversación:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al crear la conversación',
      error: error.message
    });
  }
});

/**
 * @route GET /api/conversations/:id
 * @desc Obtiene una conversación por su ID
 * @access Public
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'El ID de usuario es requerido'
      });
    }

    // Simulamos que se ha encontrado la conversación
    return res.status(200).json({
      success: true,
      conversation: {
        id,
        title: 'Conversación ' + id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId,
        messages: [
          {
            id: '1',
            content: '¡Hola! ¿En qué puedo ayudarte hoy?',
            role: 'assistant',
            createdAt: new Date().toISOString()
          }
        ]
      }
    });
  } catch (error) {
    console.error('Error obteniendo conversación:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener la conversación',
      error: error.message
    });
  }
});

/**
 * @route POST /api/conversations/:id/messages
 * @desc Añade un mensaje a una conversación
 * @access Public
 */
router.post('/:id/messages', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'El ID de usuario es requerido'
      });
    }

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'El contenido del mensaje es requerido'
      });
    }

    // Simulamos que se ha añadido el mensaje
    return res.status(201).json({
      success: true,
      message: {
        id: Math.random().toString(36).substring(7),
        content,
        role: 'user',
        createdAt: new Date().toISOString()
      },
      response: {
        id: Math.random().toString(36).substring(7),
        content: 'Esta es una respuesta simulada del asistente.',
        role: 'assistant',
        createdAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error añadiendo mensaje:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al añadir el mensaje',
      error: error.message
    });
  }
});

module.exports = router;
