"""
Technical Analysis Agent using Google ADK
"""
import os
import json
import aiohttp
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from google.adk.runtime import InvocationContext

# API Keys
COINMARKETCAP_API_KEY = os.getenv("COINMARKETCAP_API_KEY", "37f9968e-6ab7-431f-80d7-0ac6686319f3")
MCP_SERVER_URL = os.getenv("MCP_SERVER_URL", "http://localhost:3101")

# Function tools for the agent
async def get_historical_data(crypto_id: str, timeframe: str = "7d") -> Dict[str, Any]:
    """
    Fetches historical price data for a cryptocurrency.

    Args:
        crypto_id: The ID of the cryptocurrency (e.g., "1" for Bitcoin, "1027" for Ethereum)
        timeframe: The timeframe for historical data (1d, 7d, 30d, 90d, 365d)

    Returns:
        Historical price data
    """
    # Convert timeframe to days
    days_mapping = {
        "1d": 1,
        "7d": 7,
        "30d": 30,
        "90d": 90,
        "365d": 365
    }
    days = days_mapping.get(timeframe, 30)

    try:
        # Try to get data from MCP service first
        async with aiohttp.ClientSession() as session:
            payload = {
                "cryptoId": crypto_id,
                "days": days
            }
            async with session.post(f"{MCP_SERVER_URL}/historical", json=payload) as response:
                if response.status == 200:
                    return await response.json()

        # Fallback to direct API call if MCP fails
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        url = f"https://pro-api.coinmarketcap.com/v2/cryptocurrency/ohlcv/historical"
        parameters = {
            'id': crypto_id,
            'time_start': start_date.strftime('%Y-%m-%dT%H:%M:%S'),
            'time_end': end_date.strftime('%Y-%m-%dT%H:%M:%S'),
            'interval': 'daily'
        }

        headers = {
            'X-CMC_PRO_API_KEY': COINMARKETCAP_API_KEY,
            'Accept': 'application/json'
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=parameters, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return _generate_simulated_data(crypto_id, days)
    except Exception as e:
        print(f"Error fetching historical data: {e}")
        return _generate_simulated_data(crypto_id, days)

def _generate_simulated_data(crypto_id: str, days: int) -> Dict[str, Any]:
    """Generate simulated historical data."""
    import random

    data = {
        "status": {
            "timestamp": datetime.now().isoformat(),
            "error_code": 0,
            "error_message": None,
            "elapsed": 10,
            "credit_count": 1
        },
        "data": {
            "id": crypto_id,
            "name": f"Crypto {crypto_id}",
            "symbol": "CRYPTO",
            "quotes": []
        }
    }

    base_price = 1000 + random.random() * 9000
    volatility = 0.02

    end_date = datetime.now()
    for i in range(days):
        date = end_date - timedelta(days=i)
        change = (random.random() - 0.5) * 2 * volatility
        base_price = base_price * (1 + change)

        open_price = base_price * (1 + (random.random() - 0.5) * 0.01)
        close_price = base_price * (1 + (random.random() - 0.5) * 0.01)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.01)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.01)
        volume = base_price * 1000 * (0.5 + random.random())

        data["data"]["quotes"].append({
            "timestamp": date.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            "quote": {
                "USD": {
                    "open": open_price,
                    "high": high_price,
                    "low": low_price,
                    "close": close_price,
                    "volume": volume,
                    "market_cap": close_price * 1000000,
                    "timestamp": date.strftime('%Y-%m-%dT%H:%M:%S.000Z')
                }
            }
        })

    return data

async def calculate_indicators(historical_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate technical indicators from historical data.

    Args:
        historical_data: Historical price data from get_historical_data

    Returns:
        Technical indicators
    """
    try:
        quotes = historical_data.get("data", {}).get("quotes", [])
        if not quotes:
            return {"error": "No historical data available"}

        # Sort quotes by timestamp (newest first)
        quotes.sort(key=lambda x: x.get("timestamp"), reverse=True)

        # Extract closing prices
        closing_prices = [quote.get("quote", {}).get("USD", {}).get("close", 0) for quote in quotes]

        # Calculate simple moving averages
        sma_7 = sum(closing_prices[:7]) / 7 if len(closing_prices) >= 7 else None
        sma_14 = sum(closing_prices[:14]) / 14 if len(closing_prices) >= 14 else None
        sma_30 = sum(closing_prices[:30]) / 30 if len(closing_prices) >= 30 else None

        # Calculate price change percentages
        price_change_24h = ((closing_prices[0] / closing_prices[1]) - 1) * 100 if len(closing_prices) >= 2 else None
        price_change_7d = ((closing_prices[0] / closing_prices[6]) - 1) * 100 if len(closing_prices) >= 7 else None
        price_change_30d = ((closing_prices[0] / closing_prices[29]) - 1) * 100 if len(closing_prices) >= 30 else None

        # Calculate volatility (standard deviation of daily returns)
        daily_returns = []
        for i in range(1, len(closing_prices)):
            daily_return = (closing_prices[i-1] / closing_prices[i]) - 1
            daily_returns.append(daily_return)

        volatility = np.std(daily_returns) * 100 if daily_returns else None

        # Calculate RSI (Relative Strength Index)
        rsi = _calculate_rsi(closing_prices, 14) if len(closing_prices) >= 14 else None

        # Determine trend
        trend = "neutral"
        if sma_7 and sma_30:
            if sma_7 > sma_30:
                trend = "bullish"
            elif sma_7 < sma_30:
                trend = "bearish"

        # Determine support and resistance levels
        support, resistance = _calculate_support_resistance(quotes)

        return {
            "current_price": closing_prices[0],
            "sma_7": sma_7,
            "sma_14": sma_14,
            "sma_30": sma_30,
            "price_change_24h": price_change_24h,
            "price_change_7d": price_change_7d,
            "price_change_30d": price_change_30d,
            "volatility": volatility,
            "rsi": rsi,
            "trend": trend,
            "support": support,
            "resistance": resistance
        }
    except Exception as e:
        print(f"Error analyzing technical indicators: {e}")
        return {"error": f"Error analyzing technical indicators: {str(e)}"}

def _calculate_rsi(prices: list, period: int = 14) -> float:
    """Calculate the Relative Strength Index."""
    if len(prices) <= period:
        return 50  # Default neutral value

    # Calculate price changes
    deltas = [prices[i-1] - prices[i] for i in range(1, len(prices))]

    # Get gains and losses
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]

    # Calculate average gains and losses
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period

    if avg_loss == 0:
        return 100  # No losses, RSI is 100

    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    return rsi

def _calculate_support_resistance(quotes: list) -> tuple:
    """Calculate support and resistance levels."""
    # Extract high and low prices
    highs = [quote.get("quote", {}).get("USD", {}).get("high", 0) for quote in quotes]
    lows = [quote.get("quote", {}).get("USD", {}).get("low", 0) for quote in quotes]

    # Simple approach: support is recent low, resistance is recent high
    support = min(lows[:7]) if len(lows) >= 7 else min(lows)
    resistance = max(highs[:7]) if len(highs) >= 7 else max(highs)

    return support, resistance

def extract_crypto_id(query: str) -> str:
    """Extract cryptocurrency ID from query."""
    # Common cryptocurrencies and their IDs
    crypto_mapping = {
        "bitcoin": "1",
        "btc": "1",
        "ethereum": "1027",
        "eth": "1027",
        "binance coin": "1839",
        "bnb": "1839",
        "cardano": "2010",
        "ada": "2010",
        "solana": "5426",
        "sol": "5426",
        "xrp": "52",
        "dogecoin": "74",
        "doge": "74",
        "polkadot": "6636",
        "dot": "6636",
        "tether": "825",
        "usdt": "825",
        "usd coin": "3408",
        "usdc": "3408"
    }

    query_lower = query.lower()

    # Check for exact matches
    for crypto_name, crypto_id in crypto_mapping.items():
        if crypto_name in query_lower:
            return crypto_id

    # Default to Bitcoin if no match found
    return "1"

def extract_timeframe(query: str) -> str:
    """Extract timeframe from query."""
    query_lower = query.lower()

    if "year" in query_lower or "365" in query_lower:
        return "365d"
    elif "6 month" in query_lower or "180" in query_lower:
        return "90d"  # Using 90d as closest match
    elif "3 month" in query_lower or "90" in query_lower:
        return "90d"
    elif "month" in query_lower or "30" in query_lower:
        return "30d"
    elif "week" in query_lower or "7" in query_lower:
        return "7d"
    elif "day" in query_lower or "24" in query_lower:
        return "1d"

    # Default to 7 days
    return "7d"

async def analyze_crypto(query: str, ctx: InvocationContext) -> str:
    """
    Analyze cryptocurrency based on user query.

    Args:
        query: User query about cryptocurrency
        ctx: Invocation context

    Returns:
        Analysis result
    """
    # Extract crypto ID and timeframe from query
    crypto_id = extract_crypto_id(query)
    timeframe = extract_timeframe(query)

    # Get historical data
    historical_data = await get_historical_data(crypto_id, timeframe)

    # Calculate technical indicators
    indicators = await calculate_indicators(historical_data)

    # Store results in session state
    ctx.session.state["crypto_id"] = crypto_id
    ctx.session.state["timeframe"] = timeframe
    ctx.session.state["historical_data"] = historical_data
    ctx.session.state["technical_indicators"] = indicators

    # Return structured data for the LLM to format
    return json.dumps(indicators)

# Create the technical analysis agent
technical_agent = LlmAgent(
    name="technical_analysis_agent",
    model="gemini-1.5-pro",
    description="Analyzes cryptocurrency price data using technical indicators",
    instruction="""
    You are a cryptocurrency technical analysis expert. Your task is to:

    1. Analyze the technical indicators provided to you
    2. Identify the current trend (bullish, bearish, neutral)
    3. Explain what the indicators suggest about future price movements
    4. Highlight key support and resistance levels
    5. Provide a clear, concise technical analysis summary

    When responding:
    - Be specific about what the indicators mean
    - Explain the significance of moving averages, RSI, and price changes
    - Mention support and resistance levels
    - Provide a conclusion about the overall technical outlook

    The technical indicators will be provided to you as JSON data.
    """,
    tools=[FunctionTool(func=analyze_crypto)],
    output_key="technical_analysis"
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime
    import asyncio

    async def main():
        runtime = Runtime()
        session = runtime.new_session()

        # Test the agent with a query
        response = await technical_agent.run_async(
            session=session,
            query="Analyze Bitcoin technical indicators for the last week"
        )

        print(response)

    asyncio.run(main())
