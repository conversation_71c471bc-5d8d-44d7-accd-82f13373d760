<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cripto Agent - Interactive Demo</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', 'Roboto', sans-serif;
      background-color: #0a0a1a;
      color: #e0e0ff;
      overflow-x: hidden;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* Header */
    header {
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(64, 220, 255, 0.2);
    }
    
    .logo {
      display: flex;
      align-items: center;
    }
    
    .logo-icon {
      position: relative;
      width: 40px;
      height: 40px;
      margin-right: 10px;
    }
    
    .logo-core {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      background: radial-gradient(circle, #00f2ff 0%, #4657ce 100%);
      border-radius: 50%;
      box-shadow: 0 0 10px rgba(0, 242, 255, 0.8);
    }
    
    .logo-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      border: 1px solid rgba(64, 220, 255, 0.5);
    }
    
    .ring1 {
      width: 24px;
      height: 24px;
    }
    
    .ring2 {
      width: 32px;
      height: 32px;
    }
    
    .logo-text {
      font-size: 20px;
      font-weight: 700;
      background: linear-gradient(90deg, #00f2ff, #4657ce);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    /* Main Content */
    main {
      flex: 1;
      display: flex;
      position: relative;
      overflow: hidden;
    }
    
    /* Particles Container */
    .particles-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;
    }
    
    .particle {
      position: absolute;
      background: rgba(64, 220, 255, 0.5);
      border-radius: 50%;
      box-shadow: 0 0 5px rgba(64, 220, 255, 0.3);
    }
    
    /* Agent Section */
    .agent-section {
      width: 40%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      position: relative;
      z-index: 2;
    }
    
    .agent-avatar {
      position: relative;
      width: 180px;
      height: 180px;
      margin-bottom: 30px;
    }
    
    .agent-core {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      background: radial-gradient(circle, #00f2ff 0%, #4657ce 100%);
      border-radius: 50%;
      box-shadow: 0 0 30px rgba(0, 242, 255, 0.8);
    }
    
    .agent-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      border: 2px solid rgba(64, 220, 255, 0.5);
    }
    
    .ring-1 {
      width: 90px;
      height: 90px;
    }
    
    .ring-2 {
      width: 120px;
      height: 120px;
    }
    
    .ring-3 {
      width: 150px;
      height: 150px;
    }
    
    .agent-title {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 15px;
      background: linear-gradient(90deg, #ffffff, #a0a0ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .agent-subtitle {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #00f2ff;
    }
    
    .agent-description {
      text-align: center;
      max-width: 400px;
      color: #a0a0d0;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    
    .agent-stats {
      display: flex;
      gap: 20px;
      margin-top: 30px;
    }
    
    .stat-item {
      text-align: center;
      background: rgba(20, 20, 50, 0.5);
      padding: 15px;
      border-radius: 12px;
      border: 1px solid rgba(64, 220, 255, 0.2);
      min-width: 100px;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: 700;
      color: #00f2ff;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #a0a0d0;
    }
    
    /* Chat Section */
    .chat-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: rgba(15, 15, 45, 0.7);
      border-left: 1px solid rgba(64, 220, 255, 0.2);
      backdrop-filter: blur(10px);
      z-index: 2;
    }
    
    .chat-header {
      padding: 20px;
      border-bottom: 1px solid rgba(64, 220, 255, 0.2);
    }
    
    .chat-title {
      font-size: 20px;
      font-weight: 600;
      color: white;
    }
    
    .chat-subtitle {
      font-size: 14px;
      color: #a0a0d0;
      margin-top: 5px;
    }
    
    .chat-messages {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    
    .message {
      max-width: 80%;
      padding: 12px 16px;
      border-radius: 18px;
      position: relative;
      animation: fadeIn 0.3s ease-out;
    }
    
    .user-message {
      align-self: flex-end;
      background: linear-gradient(135deg, #4657ce 0%, #764ba2 100%);
      color: white;
      border-bottom-right-radius: 4px;
    }
    
    .agent-message {
      align-self: flex-start;
      background: rgba(30, 30, 60, 0.8);
      color: #e0e0ff;
      border-bottom-left-radius: 4px;
      border-left: 3px solid #00f2ff;
    }
    
    .message-time {
      font-size: 11px;
      opacity: 0.7;
      margin-top: 5px;
      text-align: right;
    }
    
    .chat-input {
      display: flex;
      padding: 15px 20px;
      background: rgba(20, 20, 50, 0.9);
      border-top: 1px solid rgba(64, 220, 255, 0.2);
    }
    
    .message-input {
      flex: 1;
      background: rgba(30, 30, 60, 0.6);
      border: 1px solid rgba(64, 220, 255, 0.3);
      border-radius: 24px;
      padding: 12px 20px;
      color: white;
      font-size: 16px;
      outline: none;
    }
    
    .message-input::placeholder {
      color: rgba(200, 200, 255, 0.5);
    }
    
    .send-button {
      width: 46px;
      height: 46px;
      margin-left: 10px;
      background: linear-gradient(135deg, #4657ce 0%, #00f2ff 100%);
      border: none;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: 0 0 10px rgba(64, 220, 255, 0.5);
    }
    
    .send-icon {
      color: white;
      font-size: 16px;
    }
    
    /* Code Block */
    .code-block {
      background: rgba(10, 10, 30, 0.8);
      padding: 15px;
      border-radius: 8px;
      font-family: monospace;
      color: #e0e0ff;
      border-left: 3px solid #00f2ff;
      overflow-x: auto;
      margin: 5px 0;
    }
    
    .code-comment {
      color: #a0a0d0;
    }
    
    .code-keyword {
      color: #ff79c6;
    }
    
    .code-string {
      color: #f1fa8c;
    }
    
    .code-function {
      color: #50fa7b;
    }
    
    /* Typing Indicator */
    .typing-indicator {
      display: inline-flex;
      align-items: center;
      padding: 6px 12px;
      background: rgba(30, 30, 60, 0.5);
      border-radius: 12px;
      margin-top: 5px;
    }
    
    .typing-dot {
      width: 8px;
      height: 8px;
      background: #00f2ff;
      border-radius: 50%;
      margin: 0 2px;
      opacity: 0.6;
    }
    
    /* Animations */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    @keyframes pulse {
      0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
      }
      50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.4;
      }
      100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
      }
    }
    
    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
      100% {
        transform: translateY(0px);
      }
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
      main {
        flex-direction: column;
      }
      
      .agent-section {
        width: 100%;
        padding: 30px 20px;
      }
      
      .chat-section {
        border-left: none;
        border-top: 1px solid rgba(64, 220, 255, 0.2);
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="logo">
      <div class="logo-icon">
        <div class="logo-core"></div>
        <div class="logo-ring ring1"></div>
        <div class="logo-ring ring2"></div>
      </div>
      <span class="logo-text">Criptokens</span>
    </div>
  </header>
  
  <main>
    <div id="particles-container" class="particles-container"></div>
    
    <section class="agent-section">
      <div class="agent-avatar">
        <div class="agent-core"></div>
        <div class="agent-ring ring-1"></div>
        <div class="agent-ring ring-2"></div>
        <div class="agent-ring ring-3"></div>
      </div>
      
      <h1 class="agent-title">Cripto Agent</h1>
      <h2 class="agent-subtitle">Your AI Cryptocurrency Assistant</h2>
      
      <p class="agent-description">
        Powered by advanced artificial intelligence, Cripto understands natural language and provides real-time insights, portfolio management, and expert advice on cryptocurrencies.
      </p>
      
      <div class="agent-stats">
        <div class="stat-item">
          <div class="stat-value">500+</div>
          <div class="stat-label">Cryptocurrencies</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">24/7</div>
          <div class="stat-label">Availability</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">99.9%</div>
          <div class="stat-label">Accuracy</div>
        </div>
      </div>
    </section>
    
    <section class="chat-section">
      <div class="chat-header">
        <div class="chat-title">Conversation with Cripto</div>
        <div class="chat-subtitle">Ask anything about cryptocurrencies, trading, or your portfolio</div>
      </div>
      
      <div id="chat-messages" class="chat-messages">
        <div class="message agent-message">
          Hello! I'm Cripto, your AI cryptocurrency assistant. How can I help you today?
          <div class="message-time">10:30 AM</div>
        </div>
      </div>
      
      <div class="chat-input">
        <input type="text" id="message-input" class="message-input" placeholder="Ask Cripto anything about cryptocurrencies...">
        <button id="send-button" class="send-button">
          <span class="send-icon">➤</span>
        </button>
      </div>
    </section>
  </main>
  
  <script>
    // Initialize animations when the page loads
    document.addEventListener('DOMContentLoaded', () => {
      // Animate logo rings
      anime({
        targets: '.logo-ring',
        scale: [1, 1.2, 1],
        opacity: [0.8, 0.4, 0.8],
        easing: 'easeInOutSine',
        duration: 2000,
        loop: true
      });
      
      // Animate agent rings
      anime({
        targets: '.agent-ring',
        scale: [1, 1.1, 1],
        opacity: [0.8, 0.4, 0.8],
        easing: 'easeInOutSine',
        duration: 3000,
        delay: function(el, i) {
          return i * 500;
        },
        loop: true
      });
      
      // Animate agent core
      anime({
        targets: '.agent-core',
        boxShadow: [
          '0 0 20px rgba(0, 242, 255, 0.6)',
          '0 0 40px rgba(0, 242, 255, 0.8)',
          '0 0 20px rgba(0, 242, 255, 0.6)'
        ],
        scale: [1, 1.05, 1],
        easing: 'easeInOutSine',
        duration: 3000,
        loop: true
      });
      
      // Create and animate particles
      createParticles();
      
      // Setup chat functionality
      setupChat();
    });
    
    // Create particles
    function createParticles() {
      const container = document.getElementById('particles-container');
      const particleCount = 50;
      
      // Create particles
      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.classList.add('particle');
        
        // Random size
        const size = Math.random() * 5 + 2;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        
        // Random position
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        
        // Random opacity
        particle.style.opacity = Math.random() * 0.5 + 0.1;
        
        container.appendChild(particle);
      }
      
      // Animate particles
      anime({
        targets: '.particle',
        translateX: function() { return anime.random(-50, 50); },
        translateY: function() { return anime.random(-50, 50); },
        opacity: function() { return Math.random() * 0.5 + 0.1; },
        scale: function() { return anime.random(0.5, 2); },
        easing: 'easeInOutQuad',
        duration: function() { return anime.random(2000, 5000); },
        complete: function(anim) {
          anim.restart();
        },
        loop: true
      });
    }
    
    // Setup chat functionality
    function setupChat() {
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');
      const chatMessages = document.getElementById('chat-messages');
      
      // Sample responses with code examples
      const responses = [
        {
          text: "Bitcoin is currently trading at $30,000 with a 2.4% increase in the last 24 hours.",
          type: "text"
        },
        {
          text: "Your portfolio has grown by 3.2% this week, with Solana being your best performer.",
          type: "text"
        },
        {
          text: "Ethereum's upcoming upgrade is expected to reduce gas fees by up to 90%.",
          type: "text"
        },
        {
          text: "Based on current market trends, it might be a good time to diversify your portfolio with some DeFi tokens.",
          type: "text"
        },
        {
          text: "Here's a simple JavaScript function to calculate your potential returns:<div class='code-block'><span class='code-keyword'>function</span> <span class='code-function'>calculateReturns</span>(initialInvestment, expectedGrowth, years) {<br>  <span class='code-comment'>// Compound annual growth rate</span><br>  <span class='code-keyword'>return</span> initialInvestment * Math.pow(1 + expectedGrowth, years);<br>}</div>",
          type: "code"
        },
        {
          text: "The crypto market cap has increased by 5% in the last week, showing positive momentum.",
          type: "text"
        }
      ];
      
      // Send message function
      function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;
        
        // Get current time
        const now = new Date();
        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        // Add user message
        const userMessageElement = document.createElement('div');
        userMessageElement.classList.add('message', 'user-message');
        userMessageElement.innerHTML = `${message}<div class="message-time">${timeString}</div>`;
        chatMessages.appendChild(userMessageElement);
        
        // Clear input
        messageInput.value = '';
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Add typing indicator
        const typingIndicator = document.createElement('div');
        typingIndicator.classList.add('typing-indicator');
        for (let i = 0; i < 3; i++) {
          const dot = document.createElement('div');
          dot.classList.add('typing-dot');
          typingIndicator.appendChild(dot);
        }
        chatMessages.appendChild(typingIndicator);
        
        // Animate typing dots
        anime({
          targets: '.typing-dot',
          translateY: [0, -5, 0],
          opacity: [0.6, 1, 0.6],
          easing: 'easeInOutSine',
          duration: 600,
          delay: anime.stagger(150),
          loop: true
        });
        
        // Animate agent thinking
        anime({
          targets: '.agent-core',
          scale: [1, 1.2, 1],
          boxShadow: [
            '0 0 20px rgba(0, 242, 255, 0.6)',
            '0 0 50px rgba(0, 242, 255, 0.9)',
            '0 0 20px rgba(0, 242, 255, 0.6)'
          ],
          duration: 1500,
          easing: 'easeInOutQuad'
        });
        
        // Simulate agent response after delay
        setTimeout(() => {
          // Remove typing indicator
          chatMessages.removeChild(typingIndicator);
          
          // Get random response
          const response = responses[Math.floor(Math.random() * responses.length)];
          
          // Add agent message
          const agentMessageElement = document.createElement('div');
          agentMessageElement.classList.add('message', 'agent-message');
          agentMessageElement.innerHTML = `${response.text}<div class="message-time">${timeString}</div>`;
          chatMessages.appendChild(agentMessageElement);
          
          // Scroll to bottom
          chatMessages.scrollTop = chatMessages.scrollHeight;
          
          // Animate new message
          anime({
            targets: agentMessageElement,
            translateY: [20, 0],
            opacity: [0, 1],
            duration: 800,
            easing: 'spring(1, 80, 10, 0)'
          });
        }, 2000);
      }
      
      // Event listeners
      sendButton.addEventListener('click', sendMessage);
      messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendMessage();
      });
      
      // Animate the dots for the typing indicator
      anime({
        targets: '.typing-dot',
        scale: [1, 1.4, 1],
        opacity: [0.6, 1, 0.6],
        easing: 'easeInOutSine',
        duration: 600,
        delay: anime.stagger(150),
        loop: true
      });
    }
  </script>
</body>
</html>
