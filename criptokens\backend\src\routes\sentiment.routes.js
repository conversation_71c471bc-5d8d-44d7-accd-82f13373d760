/**
 * Rutas para el análisis de sentimiento
 */

const express = require('express');
const router = express.Router();
const sentimentService = require('../services/sentiment.service');

/**
 * @route GET /api/sentiment/market
 * @desc Obtiene el sentimiento general del mercado de criptomonedas
 * @access Public
 */
router.get('/market', async (req, res) => {
  try {
    const sentiment = await sentimentService.analyzeMarketSentiment();
    res.json(sentiment);
  } catch (error) {
    console.error('Error al obtener sentimiento del mercado:', error);
    res.status(500).json({ error: 'Error al obtener sentimiento del mercado', details: error.message });
  }
});

/**
 * @route POST /api/sentiment/crypto
 * @desc Analiza el sentimiento de una criptomoneda específica
 * @access Public
 */
router.post('/crypto', async (req, res) => {
  try {
    const { crypto, count = 10 } = req.body;
    
    if (!crypto) {
      return res.status(400).json({ error: 'Se requiere el nombre de la criptomoneda' });
    }
    
    const sentiment = await sentimentService.analyzeNewsSentiment(crypto, count);
    res.json(sentiment);
  } catch (error) {
    console.error('Error al analizar sentimiento de criptomoneda:', error);
    res.status(500).json({ error: 'Error al analizar sentimiento de criptomoneda', details: error.message });
  }
});

/**
 * @route POST /api/sentiment/webpage
 * @desc Analiza el sentimiento de una página web específica
 * @access Public
 */
router.post('/webpage', async (req, res) => {
  try {
    const { url } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'Se requiere la URL de la página web' });
    }
    
    const sentiment = await sentimentService.analyzeWebPageSentiment(url);
    res.json(sentiment);
  } catch (error) {
    console.error('Error al analizar sentimiento de página web:', error);
    res.status(500).json({ error: 'Error al analizar sentimiento de página web', details: error.message });
  }
});

/**
 * @route POST /api/sentiment/topic
 * @desc Analiza el sentimiento de un tema específico
 * @access Public
 */
router.post('/topic', async (req, res) => {
  try {
    const { topic, count = 10 } = req.body;
    
    if (!topic) {
      return res.status(400).json({ error: 'Se requiere el tema a analizar' });
    }
    
    const sentiment = await sentimentService.analyzeTopicSentiment(topic, count);
    res.json(sentiment);
  } catch (error) {
    console.error('Error al analizar sentimiento de tema:', error);
    res.status(500).json({ error: 'Error al analizar sentimiento de tema', details: error.message });
  }
});

/**
 * @route POST /api/sentiment/text
 * @desc Analiza el sentimiento de un texto
 * @access Public
 */
router.post('/text', async (req, res) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({ error: 'Se requiere el texto a analizar' });
    }
    
    const sentiment = sentimentService.analyzeSentiment(text);
    res.json(sentiment);
  } catch (error) {
    console.error('Error al analizar sentimiento de texto:', error);
    res.status(500).json({ error: 'Error al analizar sentimiento de texto', details: error.message });
  }
});

module.exports = router;
