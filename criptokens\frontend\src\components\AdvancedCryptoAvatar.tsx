import React, { useEffect, useRef } from 'react';

interface SimpleAvatarProps {
  mood: 'neutral' | 'happy' | 'sad' | 'thinking' | 'excited' | 'worried';
  size?: 'small' | 'medium' | 'large';
  message?: string;
  cryptoName?: string;
  cryptoChange?: number;
}

const SimpleAvatar: React.FC<SimpleAvatarProps> = ({
  mood = 'neutral',
  size = 'medium',
  message,
  cryptoName,
  cryptoChange
}) => {
  const avatarRef = useRef<HTMLDivElement>(null);
  const ringRef = useRef<HTMLDivElement>(null);

  // Determinar dimensiones según el tamaño
  const getDimensions = () => {
    switch (size) {
      case 'small':
        return { width: '80px', height: '80px', fontSize: '12px' };
      case 'large':
        return { width: '180px', height: '180px', fontSize: '18px' };
      case 'medium':
      default:
        return { width: '120px', height: '120px', fontSize: '14px' };
    }
  };

  const dimensions = getDimensions();

  // Efecto para animar el avatar
  useEffect(() => {
    if (ringRef.current) {
      // Animación de rotación para el anillo
      ringRef.current.style.animation = 'rotate 10s linear infinite';
    }

    // Agregar animación de pulso al avatar
    if (avatarRef.current) {
      avatarRef.current.style.animation = 'pulse 2s infinite';
    }
  }, []);

  // Función para obtener la expresión facial según el estado de ánimo
  const getFaceExpression = () => {
    switch (mood) {
      case 'happy':
        return (
          <>
            <div className="avatar-eyes" style={{ display: 'flex', justifyContent: 'space-around', width: '60%', margin: '0 auto' }}>
              <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#0f1123' }}></div>
              <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#0f1123' }}></div>
            </div>
            <div className="avatar-mouth" style={{ width: '40%', height: '4px', margin: '8px auto 0', background: '#0f1123', borderRadius: '2px' }}></div>
          </>
        );
      case 'sad':
        return (
          <>
            <div className="avatar-eyes" style={{ display: 'flex', justifyContent: 'space-around', width: '60%', margin: '0 auto' }}>
              <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#0f1123' }}></div>
              <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#0f1123' }}></div>
            </div>
            <div className="avatar-mouth" style={{
              width: '40%',
              height: '20px',
              margin: '5px auto 0',
              borderBottom: '4px solid #0f1123',
              borderRadius: '0 0 10px 10px'
            }}></div>
          </>
        );
      case 'neutral':
      default:
        return (
          <>
            <div className="avatar-eyes" style={{ display: 'flex', justifyContent: 'space-around', width: '60%', margin: '0 auto' }}>
              <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#0f1123' }}></div>
              <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#0f1123' }}></div>
            </div>
            <div className="avatar-mouth" style={{ width: '40%', height: '4px', margin: '8px auto 0', background: '#0f1123', borderRadius: '2px' }}></div>
          </>
        );
    }
  };

  // Función para obtener el color del avatar según el estado de ánimo
  const getAvatarColor = () => {
    switch (mood) {
      case 'happy':
        return 'linear-gradient(135deg, #00e0ff 0%, #00a3ff 100%)';
      case 'sad':
        return 'linear-gradient(135deg, #00a3ff 0%, #0073ff 100%)';
      case 'neutral':
      default:
        return 'linear-gradient(135deg, #00e0ff 0%, #00a3ff 100%)';
    }
  };

  return (
    <div className="simple-avatar" style={{ position: 'relative', width: dimensions.width, height: dimensions.height }}>
      {/* Anillo animado */}
      <div
        ref={ringRef}
        className="avatar-ring"
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '100%',
          height: '100%',
          borderRadius: '50%',
          border: '2px solid #00e0ff',
          opacity: 0.6,
          zIndex: 1
        }}
      ></div>

      {/* Cara del avatar */}
      <div
        ref={avatarRef}
        className="avatar-face"
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '60%',
          height: '60%',
          borderRadius: '50%',
          background: getAvatarColor(),
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          boxShadow: '0 0 20px rgba(0, 224, 255, 0.5)',
          zIndex: 3
        }}
      >
        {getFaceExpression()}
      </div>

      {/* Mensaje */}
      {message && (
        <div
          className="avatar-message"
          style={{
            position: 'absolute',
            bottom: '-40px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(15, 17, 35, 0.8)',
            padding: '5px 10px',
            borderRadius: '10px',
            fontSize: dimensions.fontSize,
            color: '#fff',
            whiteSpace: 'nowrap',
            zIndex: 4,
            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)'
          }}
        >
          {message}
          {cryptoName && cryptoChange && (
            <span style={{
              color: cryptoChange >= 0 ? '#00ffb3' : '#ff3a6e',
              fontWeight: 'bold',
              marginLeft: '5px'
            }}>
              {cryptoChange >= 0 ? '▲' : '▼'} {Math.abs(cryptoChange).toFixed(2)}%
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default SimpleAvatar;
