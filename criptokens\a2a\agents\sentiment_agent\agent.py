import json
from typing import Dict, Any, AsyncIterable, List, Optional
import logging
import aiohttp
import os
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)

class SentimentAnalysisAgent:
    """Agent for sentiment analysis of cryptocurrency news and social media."""
    
    SUPPORTED_CONTENT_TYPES = ["text", "data"]
    
    def __init__(self):
        self.brave_api_key = os.getenv("BRAVE_API_KEY", "BSAccS820UUfffNOAD7yLACz9htlbe9")
        self.mcp_brave_url = os.getenv("BRAVE_SEARCH_MCP_URL", "http://localhost:3102")
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY", "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")
    
    async def fetch_news(self, crypto_name: str, days: int = 7) -> List[Dict[str, Any]]:
        """Fetch news articles about a cryptocurrency.
        
        Args:
            crypto_name: The name of the cryptocurrency
            days: Number of days of news to fetch
            
        Returns:
            List of news articles
        """
        try:
            # Try to get news from MCP Brave service
            async with aiohttp.ClientSession() as session:
                payload = {
                    "query": f"{crypto_name} cryptocurrency news",
                    "count": 10,
                    "freshness": "week" if days <= 7 else "month"
                }
                headers = {
                    "X-Brave-API-Key": self.brave_api_key
                }
                
                async with session.post(
                    f"{self.mcp_brave_url}/search",
                    json=payload,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "web" in result and "results" in result["web"]:
                            return result["web"]["results"]
            
            # Fallback to simulated news if MCP fails
            logger.warning("Failed to fetch news from MCP Brave, using simulated data")
            return self._generate_simulated_news(crypto_name, days)
        except Exception as e:
            logger.error(f"Error fetching news: {e}")
            return self._generate_simulated_news(crypto_name, days)
    
    def _generate_simulated_news(self, crypto_name: str, days: int) -> List[Dict[str, Any]]:
        """Generate simulated news articles.
        
        Args:
            crypto_name: The name of the cryptocurrency
            days: Number of days of news to generate
            
        Returns:
            List of simulated news articles
        """
        import random
        
        # Possible sentiment categories
        sentiments = ["positive", "neutral", "negative"]
        
        # Possible headlines by sentiment
        headlines = {
            "positive": [
                f"{crypto_name} Surges to New Heights as Adoption Increases",
                f"Analysts Predict Bright Future for {crypto_name}",
                f"{crypto_name} Gains Momentum as Institutional Interest Grows",
                f"Major Company Announces {crypto_name} Integration",
                f"{crypto_name} Breaks Resistance Level, Bulls Take Control"
            ],
            "neutral": [
                f"{crypto_name} Stabilizes After Recent Volatility",
                f"Experts Weigh In on {crypto_name}'s Future Prospects",
                f"Understanding {crypto_name}'s Role in the Crypto Ecosystem",
                f"Regulatory Clarity Could Impact {crypto_name}'s Development",
                f"{crypto_name} Trading Volume Remains Consistent"
            ],
            "negative": [
                f"{crypto_name} Faces Selling Pressure Amid Market Uncertainty",
                f"Regulatory Concerns Impact {crypto_name} Price",
                f"Analysts Warn of Potential {crypto_name} Correction",
                f"{crypto_name} Drops Below Key Support Level",
                f"Investors Cautious About {crypto_name}'s Short-term Outlook"
            ]
        }
        
        # Generate random news articles
        news = []
        end_date = datetime.now()
        
        for i in range(10):  # Generate 10 articles
            # Random date within the specified days
            days_ago = random.randint(0, min(days, 30))
            date = end_date - timedelta(days=days_ago)
            
            # Random sentiment
            sentiment = random.choice(sentiments)
            
            # Random headline based on sentiment
            headline = random.choice(headlines[sentiment])
            
            # Generate article
            news.append({
                "title": headline,
                "url": f"https://example.com/news/{crypto_name.lower()}-{i}",
                "description": f"This is a simulated news article about {crypto_name}. The sentiment is {sentiment}.",
                "published_date": date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "source": random.choice(["CryptoNews", "BlockchainTimes", "CoinDesk", "CoinTelegraph", "Decrypt"]),
                "_simulated": True,
                "_sentiment": sentiment
            })
        
        return news
    
    async def analyze_sentiment(self, news_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze sentiment of news articles.
        
        Args:
            news_articles: List of news articles
            
        Returns:
            Sentiment analysis results
        """
        try:
            # If we have simulated news with pre-assigned sentiment
            if all("_sentiment" in article for article in news_articles):
                return self._analyze_simulated_sentiment(news_articles)
            
            # Prepare articles for analysis
            articles_text = []
            for article in news_articles[:5]:  # Limit to 5 articles to avoid token limits
                title = article.get("title", "")
                description = article.get("description", "")
                articles_text.append(f"Title: {title}\nDescription: {description}")
            
            combined_text = "\n\n".join(articles_text)
            
            # Use OpenRouter API to analyze sentiment
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_api_key}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "model": "anthropic/claude-3-haiku-20240307",
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a sentiment analysis expert. Analyze the sentiment of cryptocurrency news articles and provide a detailed breakdown."
                        },
                        {
                            "role": "user",
                            "content": f"Analyze the sentiment of these cryptocurrency news articles. Provide a sentiment score from -100 (extremely negative) to +100 (extremely positive), with 0 being neutral. Also categorize the overall sentiment as positive, neutral, or negative, and provide a brief explanation of your analysis.\n\n{combined_text}"
                        }
                    ],
                    "response_format": {
                        "type": "json_object"
                    }
                }
                
                async with session.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    json=payload,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")
                        try:
                            sentiment_analysis = json.loads(content)
                            return sentiment_analysis
                        except json.JSONDecodeError:
                            logger.error(f"Failed to parse sentiment analysis result: {content}")
            
            # Fallback to simulated sentiment analysis
            logger.warning("Failed to analyze sentiment with OpenRouter, using simulated analysis")
            return self._analyze_simulated_sentiment(news_articles)
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return self._analyze_simulated_sentiment(news_articles)
    
    def _analyze_simulated_sentiment(self, news_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate simulated sentiment analysis.
        
        Args:
            news_articles: List of news articles
            
        Returns:
            Simulated sentiment analysis
        """
        # Count sentiments
        sentiment_counts = {"positive": 0, "neutral": 0, "negative": 0}
        
        for article in news_articles:
            sentiment = article.get("_sentiment", "neutral")
            sentiment_counts[sentiment] += 1
        
        # Calculate overall sentiment
        total_articles = len(news_articles)
        positive_ratio = sentiment_counts["positive"] / total_articles
        negative_ratio = sentiment_counts["negative"] / total_articles
        neutral_ratio = sentiment_counts["neutral"] / total_articles
        
        # Calculate sentiment score (-100 to +100)
        sentiment_score = (positive_ratio * 100) - (negative_ratio * 100)
        
        # Determine overall sentiment category
        if sentiment_score > 20:
            overall_sentiment = "positive"
        elif sentiment_score < -20:
            overall_sentiment = "negative"
        else:
            overall_sentiment = "neutral"
        
        # Generate explanation
        explanation = f"Analysis based on {total_articles} news articles. "
        explanation += f"Found {sentiment_counts['positive']} positive, {sentiment_counts['neutral']} neutral, and {sentiment_counts['negative']} negative articles. "
        
        if overall_sentiment == "positive":
            explanation += "The overall sentiment is positive, indicating optimistic market outlook."
        elif overall_sentiment == "negative":
            explanation += "The overall sentiment is negative, suggesting caution in the market."
        else:
            explanation += "The overall sentiment is neutral, showing balanced market perspectives."
        
        return {
            "sentiment_score": round(sentiment_score, 2),
            "overall_sentiment": overall_sentiment,
            "explanation": explanation,
            "sentiment_distribution": {
                "positive": sentiment_counts["positive"],
                "neutral": sentiment_counts["neutral"],
                "negative": sentiment_counts["negative"]
            }
        }
    
    async def fetch_social_media_sentiment(self, crypto_name: str) -> Dict[str, Any]:
        """Fetch sentiment from social media.
        
        Args:
            crypto_name: The name of the cryptocurrency
            
        Returns:
            Social media sentiment analysis
        """
        # This would normally call an API, but we'll simulate for now
        return self._generate_simulated_social_sentiment(crypto_name)
    
    def _generate_simulated_social_sentiment(self, crypto_name: str) -> Dict[str, Any]:
        """Generate simulated social media sentiment.
        
        Args:
            crypto_name: The name of the cryptocurrency
            
        Returns:
            Simulated social media sentiment
        """
        import random
        
        # Generate random sentiment metrics
        twitter_sentiment = random.uniform(-100, 100)
        reddit_sentiment = random.uniform(-100, 100)
        
        # Calculate average sentiment
        average_sentiment = (twitter_sentiment + reddit_sentiment) / 2
        
        # Determine sentiment category
        if average_sentiment > 20:
            overall_sentiment = "positive"
        elif average_sentiment < -20:
            overall_sentiment = "negative"
        else:
            overall_sentiment = "neutral"
        
        # Generate random metrics
        twitter_mentions = random.randint(1000, 10000)
        reddit_mentions = random.randint(500, 5000)
        
        # Generate random trending hashtags
        hashtags = [
            f"#{crypto_name}",
            f"#{crypto_name}ToTheMoon",
            f"#Buy{crypto_name}",
            f"#{crypto_name}News",
            f"#Crypto"
        ]
        random.shuffle(hashtags)
        trending_hashtags = hashtags[:3]
        
        return {
            "twitter": {
                "sentiment_score": round(twitter_sentiment, 2),
                "mentions": twitter_mentions,
                "trending_hashtags": trending_hashtags
            },
            "reddit": {
                "sentiment_score": round(reddit_sentiment, 2),
                "mentions": reddit_mentions,
                "active_subreddits": [f"r/{crypto_name}", "r/CryptoCurrency", "r/CryptoMarkets"]
            },
            "overall": {
                "sentiment_score": round(average_sentiment, 2),
                "sentiment_category": overall_sentiment,
                "total_mentions": twitter_mentions + reddit_mentions
            }
        }
    
    async def calculate_fear_greed_index(self, news_sentiment: Dict[str, Any], social_sentiment: Dict[str, Any]) -> int:
        """Calculate Fear & Greed Index.
        
        Args:
            news_sentiment: News sentiment analysis
            social_sentiment: Social media sentiment analysis
            
        Returns:
            Fear & Greed Index (0-100)
        """
        # Extract sentiment scores
        news_score = news_sentiment.get("sentiment_score", 0)
        social_score = social_sentiment.get("overall", {}).get("sentiment_score", 0)
        
        # Convert from -100/+100 scale to 0-100 scale
        news_score_normalized = (news_score + 100) / 2
        social_score_normalized = (social_score + 100) / 2
        
        # Calculate weighted average (60% news, 40% social)
        fear_greed_index = (news_score_normalized * 0.6) + (social_score_normalized * 0.4)
        
        # Ensure it's within 0-100 range
        fear_greed_index = max(0, min(100, fear_greed_index))
        
        return round(fear_greed_index)
    
    def get_fear_greed_classification(self, index: int) -> str:
        """Get classification for Fear & Greed Index.
        
        Args:
            index: Fear & Greed Index (0-100)
            
        Returns:
            Classification
        """
        if index >= 0 and index < 25:
            return "Extreme Fear"
        elif index >= 25 and index < 40:
            return "Fear"
        elif index >= 40 and index < 60:
            return "Neutral"
        elif index >= 60 and index < 75:
            return "Greed"
        else:
            return "Extreme Greed"
    
    async def process_query(self, query: str, session_id: str) -> Dict[str, Any]:
        """Process a query and return sentiment analysis.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            Sentiment analysis results
        """
        # Extract crypto name from query
        crypto_name = self._extract_crypto_name(query)
        if not crypto_name:
            return {
                "error": "Could not determine which cryptocurrency to analyze. Please specify a cryptocurrency."
            }
        
        # Extract timeframe from query
        days = self._extract_timeframe(query)
        
        # Fetch news articles
        news_articles = await self.fetch_news(crypto_name, days)
        
        # Analyze news sentiment
        news_sentiment = await self.analyze_sentiment(news_articles)
        
        # Fetch social media sentiment
        social_sentiment = await self.fetch_social_media_sentiment(crypto_name)
        
        # Calculate Fear & Greed Index
        fear_greed_index = await self.calculate_fear_greed_index(news_sentiment, social_sentiment)
        fear_greed_classification = self.get_fear_greed_classification(fear_greed_index)
        
        # Generate summary
        summary = self._generate_sentiment_summary(
            crypto_name, news_sentiment, social_sentiment, fear_greed_index, fear_greed_classification
        )
        
        return {
            "crypto_name": crypto_name,
            "timeframe_days": days,
            "news_sentiment": news_sentiment,
            "social_sentiment": social_sentiment,
            "fear_greed_index": fear_greed_index,
            "fear_greed_classification": fear_greed_classification,
            "summary": summary,
            "news_articles": news_articles[:5]  # Include top 5 articles
        }
    
    def _extract_crypto_name(self, query: str) -> Optional[str]:
        """Extract cryptocurrency name from query.
        
        Args:
            query: The user's query
            
        Returns:
            Cryptocurrency name or None if not found
        """
        # Common cryptocurrencies
        crypto_names = {
            "bitcoin": "Bitcoin",
            "btc": "Bitcoin",
            "ethereum": "Ethereum",
            "eth": "Ethereum",
            "binance coin": "Binance Coin",
            "bnb": "Binance Coin",
            "cardano": "Cardano",
            "ada": "Cardano",
            "solana": "Solana",
            "sol": "Solana",
            "xrp": "XRP",
            "dogecoin": "Dogecoin",
            "doge": "Dogecoin",
            "polkadot": "Polkadot",
            "dot": "Polkadot",
            "tether": "Tether",
            "usdt": "Tether",
            "usd coin": "USD Coin",
            "usdc": "USD Coin"
        }
        
        query_lower = query.lower()
        
        # Check for exact matches
        for crypto_name_lower, crypto_name in crypto_names.items():
            if crypto_name_lower in query_lower:
                return crypto_name
        
        # Default to Bitcoin if no match found
        return "Bitcoin"
    
    def _extract_timeframe(self, query: str) -> int:
        """Extract timeframe from query.
        
        Args:
            query: The user's query
            
        Returns:
            Number of days for analysis
        """
        query_lower = query.lower()
        
        if "year" in query_lower or "365" in query_lower:
            return 365
        elif "6 month" in query_lower or "180" in query_lower:
            return 180
        elif "3 month" in query_lower or "90" in query_lower:
            return 90
        elif "month" in query_lower or "30" in query_lower:
            return 30
        elif "week" in query_lower or "7" in query_lower:
            return 7
        elif "day" in query_lower or "24" in query_lower:
            return 1
        
        # Default to 7 days
        return 7
    
    def _generate_sentiment_summary(
        self, 
        crypto_name: str, 
        news_sentiment: Dict[str, Any], 
        social_sentiment: Dict[str, Any],
        fear_greed_index: int,
        fear_greed_classification: str
    ) -> str:
        """Generate a summary of sentiment analysis.
        
        Args:
            crypto_name: Cryptocurrency name
            news_sentiment: News sentiment analysis
            social_sentiment: Social media sentiment analysis
            fear_greed_index: Fear & Greed Index
            fear_greed_classification: Fear & Greed classification
            
        Returns:
            Summary text
        """
        if "error" in news_sentiment:
            return f"Error in sentiment analysis: {news_sentiment['error']}"
        
        # Extract sentiment scores
        news_score = news_sentiment.get("sentiment_score", 0)
        news_category = news_sentiment.get("overall_sentiment", "neutral")
        
        social_score = social_sentiment.get("overall", {}).get("sentiment_score", 0)
        social_category = social_sentiment.get("overall", {}).get("sentiment_category", "neutral")
        
        twitter_mentions = social_sentiment.get("twitter", {}).get("mentions", 0)
        reddit_mentions = social_sentiment.get("reddit", {}).get("mentions", 0)
        
        # Create summary
        summary = f"Sentiment Analysis for {crypto_name}:\n\n"
        
        # Add news sentiment
        summary += "News Sentiment:\n"
        summary += f"• Score: {news_score} (-100 to +100)\n"
        summary += f"• Category: {news_category.capitalize()}\n"
        if "explanation" in news_sentiment:
            summary += f"• {news_sentiment['explanation']}\n"
        
        # Add social media sentiment
        summary += "\nSocial Media Sentiment:\n"
        summary += f"• Overall Score: {social_score} (-100 to +100)\n"
        summary += f"• Category: {social_category.capitalize()}\n"
        summary += f"• Twitter Mentions: {twitter_mentions:,}\n"
        summary += f"• Reddit Mentions: {reddit_mentions:,}\n"
        
        # Add Fear & Greed Index
        summary += f"\nFear & Greed Index: {fear_greed_index} - {fear_greed_classification}\n"
        
        # Add emoji based on Fear & Greed classification
        emoji = "😐"
        if fear_greed_classification == "Extreme Fear":
            emoji = "😨"
        elif fear_greed_classification == "Fear":
            emoji = "😟"
        elif fear_greed_classification == "Greed":
            emoji = "😃"
        elif fear_greed_classification == "Extreme Greed":
            emoji = "🤑"
        
        summary += f"\nMarket Sentiment: {emoji} {fear_greed_classification}\n"
        
        return summary
    
    async def invoke(self, query: str, session_id: str) -> str:
        """Process a query and return a text response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            Text response
        """
        result = await self.process_query(query, session_id)
        
        if "error" in result:
            return f"Error: {result['error']}"
        
        return result["summary"]
    
    async def stream(self, query: str, session_id: str) -> AsyncIterable[Dict[str, Any]]:
        """Process a query and stream the response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Yields:
            Response updates
        """
        # Extract crypto name from query
        crypto_name = self._extract_crypto_name(query)
        if not crypto_name:
            yield {
                "is_task_complete": True,
                "content": "Could not determine which cryptocurrency to analyze. Please specify a cryptocurrency."
            }
            return
        
        # Extract timeframe from query
        days = self._extract_timeframe(query)
        
        # Update on fetching news
        yield {
            "is_task_complete": False,
            "updates": f"Fetching news articles about {crypto_name}..."
        }
        
        # Fetch news articles
        news_articles = await self.fetch_news(crypto_name, days)
        
        # Update on analyzing sentiment
        yield {
            "is_task_complete": False,
            "updates": "Analyzing news sentiment..."
        }
        
        # Analyze news sentiment
        news_sentiment = await self.analyze_sentiment(news_articles)
        
        # Update on fetching social media sentiment
        yield {
            "is_task_complete": False,
            "updates": "Fetching social media sentiment..."
        }
        
        # Fetch social media sentiment
        social_sentiment = await self.fetch_social_media_sentiment(crypto_name)
        
        # Update on calculating Fear & Greed Index
        yield {
            "is_task_complete": False,
            "updates": "Calculating Fear & Greed Index..."
        }
        
        # Calculate Fear & Greed Index
        fear_greed_index = await self.calculate_fear_greed_index(news_sentiment, social_sentiment)
        fear_greed_classification = self.get_fear_greed_classification(fear_greed_index)
        
        # Generate summary
        summary = self._generate_sentiment_summary(
            crypto_name, news_sentiment, social_sentiment, fear_greed_index, fear_greed_classification
        )
        
        # Final response with full analysis
        yield {
            "is_task_complete": True,
            "content": {
                "crypto_name": crypto_name,
                "timeframe_days": days,
                "news_sentiment": news_sentiment,
                "social_sentiment": social_sentiment,
                "fear_greed_index": fear_greed_index,
                "fear_greed_classification": fear_greed_classification,
                "summary": summary,
                "news_articles": news_articles[:5]  # Include top 5 articles
            }
        }
