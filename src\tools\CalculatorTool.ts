import { MCPTool } from "mcp-framework";
import { z } from "zod";

interface CalculatorInput {
  operation: string;
  a: number;
  b: number;
}

class CalculatorTool extends MCPTool<CalculatorInput> {
  name = "calculator";
  description = "A simple calculator tool that performs basic arithmetic operations";

  schema = {
    operation: {
      type: z.enum(["add", "subtract", "multiply", "divide"]),
      description: "The arithmetic operation to perform",
    },
    a: {
      type: z.number(),
      description: "First operand",
    },
    b: {
      type: z.number(),
      description: "Second operand",
    },
  };

  async execute(input: CalculatorInput) {
    const { operation, a, b } = input;
    
    switch (operation) {
      case "add":
        return `${a} + ${b} = ${a + b}`;
      case "subtract":
        return `${a} - ${b} = ${a - b}`;
      case "multiply":
        return `${a} * ${b} = ${a * b}`;
      case "divide":
        if (b === 0) {
          throw new Error("Cannot divide by zero");
        }
        return `${a} / ${b} = ${a / b}`;
      default:
        throw new Error(`Unknown operation: ${operation}`);
    }
  }
}

export default CalculatorTool;
