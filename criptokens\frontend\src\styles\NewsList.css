.news-list-container {
  width: 100%;
}

.news-filters {
  margin-bottom: 24px;
  background-color: var(--card-bg-color);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-group {
  margin-bottom: 12px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary-color);
  margin-right: 12px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.filter-options button {
  background-color: var(--background-secondary-color);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.85rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-options button:hover {
  background-color: var(--primary-color-light);
}

.filter-options button.active {
  background-color: var(--primary-color);
  color: white;
}

.news-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.news-loading,
.news-error,
.news-no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  text-align: center;
  color: var(--text-secondary-color);
}

.news-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.news-error i,
.news-no-results i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-tertiary-color);
}

.news-error button {
  margin-top: 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.news-error button:hover {
  background-color: var(--primary-color-dark);
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.load-more-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.load-more-button:hover {
  background-color: var(--primary-color-dark);
}

/* Responsive */
@media (max-width: 768px) {
  .news-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .filter-options {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 8px;
    -webkit-overflow-scrolling: touch;
  }
  
  .filter-options button {
    flex-shrink: 0;
  }
}
