import React from 'react';
import { Route } from 'react-router-dom';
import GuruADKPage from '../pages/GuruADKPage';
import GuruADKSimulatedPage from '../pages/GuruADKSimulatedPage';

/**
 * ADK Routes for Criptokens Frontend
 *
 * This component defines the routes for the ADK features.
 */
const ADKRoutes = () => {
  return (
    <>
      <Route path="/guru-adk" element={<GuruADKPage />} />
      <Route path="/guru-adk-simulated" element={<GuruADKSimulatedPage />} />
    </>
  );
};

export default ADKRoutes;
