import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Header from './Header';
import { useAuth } from '../context/NewAuthContext';
import AnimatedBackground from './AnimatedBackground';
import Guru<PERSON><PERSON>bar from './GuruSidebar';
import '../styles/Layout.css';

// Definir un contexto global para el estado del avatar
export const AvatarStatusContext = React.createContext<{
  avatarStatus: 'idle' | 'thinking' | 'positive' | 'negative' | 'neutral' | 'concerned';
  setAvatarStatus: React.Dispatch<React.SetStateAction<'idle' | 'thinking' | 'positive' | 'negative' | 'neutral' | 'concerned'>>;
}>({
  avatarStatus: 'neutral',
  setAvatarStatus: () => {}
});

const Layout: React.FC = () => {
  const { currentUser, loading } = useAuth();
  const [avatarStatus, setAvatarStatus] = useState<'idle' | 'thinking' | 'positive' | 'negative' | 'neutral' | 'concerned'>('neutral');
  const location = useLocation();

  // Efecto para resetear el estado cuando cambia la ruta
  useEffect(() => {
    setAvatarStatus('neutral');
  }, [location.pathname]);

  if (loading) {
    return <div className="app-loading">Cargando...</div>;
  }

  return (
    <AvatarStatusContext.Provider value={{ avatarStatus, setAvatarStatus }}>
      <div className="app-container">
        <AnimatedBackground status={avatarStatus} />
        <Header />
        <main className="main-content">
          <Outlet />
        </main>
        {/* Panel lateral del Guru Cripto */}
        <GuruSidebar />
      </div>
    </AvatarStatusContext.Provider>
  );
};

export default Layout;
