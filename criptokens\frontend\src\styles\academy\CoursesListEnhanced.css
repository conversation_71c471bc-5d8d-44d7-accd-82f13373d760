.courses-list {
  max-width: 1200px;
  margin: 0 auto;
}

.courses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.courses-header h2 {
  font-size: 1.8rem;
  margin: 0;
  color: var(--text-primary);
}

.courses-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-bar {
  display: flex;
  position: relative;
}

.search-bar input {
  width: 250px;
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  padding-right: 40px;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.search-button {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  background: none;
  border: none;
  color: var(--text-secondary);
  width: 40px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.search-button:hover {
  color: var(--color-primary);
}

.level-filter select {
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-size: 0.9rem;
  min-width: 180px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.course-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.course-image {
  position: relative;
  height: 160px;
  background-color: var(--color-primary-transparent);
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
}

.course-level {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.course-level.beginner {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.course-level.intermediate {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.course-level.advanced {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.course-content {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.course-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.course-description {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.course-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-instructor {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.course-instructor img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.course-instructor span {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.view-course-button {
  background: none;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;
  display: block;
}

.view-course-button:hover {
  background-color: var(--color-primary);
  color: white;
}

.no-courses {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.no-courses p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: var(--color-primary-transparent);
}

/* Loading State */
.courses-list.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(123, 97, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .courses-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .courses-filters {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-bar {
    width: 100%;
  }
  
  .search-bar input {
    width: 100%;
  }
  
  .level-filter select {
    width: 100%;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
  }
}
