import React from 'react';
import { Box, Card, CardContent, Typography, Divider } from '@mui/material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Registrar los componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface TechnicalAnalysisChartProps {
  analysis: any;
  historicalData?: any[];
}

const TechnicalAnalysisChart: React.FC<TechnicalAnalysisChartProps> = ({ analysis, historicalData }) => {
  if (!analysis || !historicalData || historicalData.length === 0) {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            No hay datos históricos disponibles para mostrar el gráfico.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  // Preparar datos para el gráfico
  const labels = historicalData.map(item => new Date(item.timestamp).toLocaleDateString());
  const prices = historicalData.map(item => item.price);

  // Calcular las bandas de Bollinger para todo el período
  const { upper, middle, lower } = analysis.indicators.bollingerBands;
  
  // Crear datos para las bandas de Bollinger (últimos puntos)
  const lastIndex = labels.length - 1;
  const bollingerLabels = labels.slice(Math.max(0, lastIndex - 20), lastIndex + 1);
  
  // Rellenar con valores nulos excepto el último punto
  const upperBandData = Array(bollingerLabels.length - 1).fill(null);
  upperBandData.push(upper);
  
  const middleBandData = Array(bollingerLabels.length - 1).fill(null);
  middleBandData.push(middle);
  
  const lowerBandData = Array(bollingerLabels.length - 1).fill(null);
  lowerBandData.push(lower);

  const data = {
    labels,
    datasets: [
      {
        label: 'Precio',
        data: prices,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        tension: 0.1
      },
      {
        label: 'Banda Superior',
        data: upperBandData,
        borderColor: 'rgba(255, 99, 132, 0.8)',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      },
      {
        label: 'Media Móvil',
        data: middleBandData,
        borderColor: 'rgba(54, 162, 235, 0.8)',
        backgroundColor: 'rgba(54, 162, 235, 0.1)',
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      },
      {
        label: 'Banda Inferior',
        data: lowerBandData,
        borderColor: 'rgba(75, 192, 192, 0.8)',
        backgroundColor: 'rgba(75, 192, 192, 0.1)',
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      }
    ]
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    }
  };

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Typography variant="h6" component="h3" gutterBottom>
          Análisis Técnico - Gráfico de Precios
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box sx={{ height: 300 }}>
          <Line data={data} options={options} />
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          El gráfico muestra el precio histórico junto con las bandas de Bollinger actuales.
        </Typography>
      </CardContent>
    </Card>
  );
};

export default TechnicalAnalysisChart;
