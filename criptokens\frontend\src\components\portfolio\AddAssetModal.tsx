import React, { useState, useEffect } from 'react';
import { UserFunds } from '../../services/portfolio.service';
import { formatNumber } from '../../utils/formatters';
import '../../styles/portfolio/AddAssetModal.css';

interface AddAssetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddAsset: (asset: any) => void;
  availableCryptos: any[];
  userFunds: UserFunds;
}

const AddAssetModal: React.FC<AddAssetModalProps> = ({
  isOpen,
  onClose,
  onAddAsset,
  availableCryptos,
  userFunds
}) => {
  const [selectedCrypto, setSelectedCrypto] = useState<any>(null);
  const [amount, setAmount] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredCryptos, setFilteredCryptos] = useState<any[]>([]);
  const [notes, setNotes] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [totalValue, setTotalValue] = useState<number>(0);

  // Filtrar criptomonedas basadas en el término de búsqueda
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredCryptos(availableCryptos.slice(0, 10));
    } else {
      const filtered = availableCryptos.filter(
        (crypto) =>
          crypto.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          crypto.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCryptos(filtered.slice(0, 10));
    }
  }, [searchTerm, availableCryptos]);

  // Calcular el valor total
  useEffect(() => {
    if (selectedCrypto && amount) {
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        setTotalValue(numAmount * selectedCrypto.current_price);
      } else {
        setTotalValue(0);
      }
    } else {
      setTotalValue(0);
    }
  }, [selectedCrypto, amount]);

  // Manejar la selección de una criptomoneda
  const handleSelectCrypto = (crypto: any) => {
    setSelectedCrypto(crypto);
    setSearchTerm('');
  };

  // Manejar el cambio en la cantidad
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Permitir solo números y un punto decimal
    if (/^[0-9]*\.?[0-9]*$/.test(value)) {
      setAmount(value);
      setError(null);
    }
  };

  // Manejar el envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCrypto) {
      setError('Por favor, selecciona una criptomoneda');
      return;
    }

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      setError('Por favor, ingresa una cantidad válida');
      return;
    }

    const totalCost = numAmount * selectedCrypto.current_price;
    if (totalCost > userFunds.balance) {
      setError('No tienes suficientes fondos para esta compra');
      return;
    }

    // Crear el nuevo activo
    const newAsset = {
      id: selectedCrypto.id,
      symbol: selectedCrypto.symbol,
      name: selectedCrypto.name,
      amount: numAmount,
      purchasePrice: selectedCrypto.current_price,
      purchaseDate: new Date(),
      notes: notes.trim() || undefined
    };

    // Añadir el activo al portafolio
    onAddAsset(newAsset);

    // Limpiar el formulario
    setSelectedCrypto(null);
    setAmount('');
    setNotes('');
    setError(null);
  };

  // Si el modal no está abierto, no renderizar nada
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="add-asset-modal">
        <div className="modal-header">
          <h2>Añadir Activo</h2>
          <button className="close-button" onClick={onClose}>
            &times;
          </button>
        </div>

        <div className="modal-content">
          <div className="funds-info">
            <span className="funds-label">Fondos disponibles:</span>
            <span className="funds-value">
              {userFunds.currency} {formatNumber(userFunds.balance)}
            </span>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label>Seleccionar Criptomoneda</label>
              <div className="crypto-selector">
                {selectedCrypto ? (
                  <div className="selected-crypto">
                    <img
                      src={selectedCrypto.image}
                      alt={selectedCrypto.name}
                      className="crypto-icon"
                    />
                    <div className="crypto-info">
                      <span className="crypto-name">{selectedCrypto.name}</span>
                      <span className="crypto-symbol">{selectedCrypto.symbol.toUpperCase()}</span>
                    </div>
                    <span className="crypto-price">
                      ${formatNumber(selectedCrypto.current_price)}
                    </span>
                    <button
                      type="button"
                      className="change-crypto-button"
                      onClick={() => setSelectedCrypto(null)}
                    >
                      Cambiar
                    </button>
                  </div>
                ) : (
                  <div className="crypto-search">
                    <input
                      type="text"
                      placeholder="Buscar criptomoneda..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="search-input"
                    />
                    {searchTerm && (
                      <div className="crypto-dropdown">
                        {filteredCryptos.length > 0 ? (
                          filteredCryptos.map((crypto) => (
                            <div
                              key={crypto.id}
                              className="crypto-option"
                              onClick={() => handleSelectCrypto(crypto)}
                            >
                              <img
                                src={crypto.image}
                                alt={crypto.name}
                                className="crypto-icon"
                              />
                              <div className="crypto-info">
                                <span className="crypto-name">{crypto.name}</span>
                                <span className="crypto-symbol">
                                  {crypto.symbol.toUpperCase()}
                                </span>
                              </div>
                              <span className="crypto-price">
                                ${formatNumber(crypto.current_price)}
                              </span>
                            </div>
                          ))
                        ) : (
                          <div className="no-results">No se encontraron resultados</div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="amount">Cantidad</label>
              <input
                type="text"
                id="amount"
                value={amount}
                onChange={handleAmountChange}
                placeholder="Ingresa la cantidad"
                disabled={!selectedCrypto}
                className="amount-input"
              />
            </div>

            {selectedCrypto && amount && !isNaN(parseFloat(amount)) && (
              <div className="total-value">
                <span className="total-label">Valor Total:</span>
                <span className="total-amount">${formatNumber(totalValue)}</span>
              </div>
            )}

            <div className="form-group">
              <label htmlFor="notes">Notas (opcional)</label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Añade notas sobre esta inversión"
                className="notes-input"
              />
            </div>

            {error && <div className="error-message">{error}</div>}

            <div className="form-actions">
              <button type="button" className="cancel-button" onClick={onClose}>
                Cancelar
              </button>
              <button
                type="submit"
                className="add-button"
                disabled={!selectedCrypto || !amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0}
              >
                Añadir Activo
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddAssetModal;
