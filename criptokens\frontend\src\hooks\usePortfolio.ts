import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/NewAuthContext';
import { doc, setDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { db } from '../firebase-init';
import {
  getFullPortfolio,
  getUserPortfolio,
  addAssetToPortfolio as addAsset,
  removeAssetFromPortfolio as removeAsset,
  updateAssetInPortfolio,
  addTransaction,
  getTransactionHistory,
  getUserFunds,
  addFundsToUser,
  PortfolioAsset,
  Portfolio,
  UserFunds
} from '../services/portfolio.service';
import { getPortfolioFromBackend } from '../services/backendPortfolio.service';
import { getTopCryptocurrencies } from '../services/api';
import { getHistoricalPortfolioValue } from '../services/historicalData.service';

export const usePortfolio = () => {
  const { currentUser } = useAuth();
  const [portfolio, setPortfolio] = useState<PortfolioAsset[]>([]);
  const [fullPortfolio, setFullPortfolio] = useState<Portfolio | null>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [portfolioStats, setPortfolioStats] = useState({
    totalValue: 0,
    totalInvestment: 0,
    totalProfitLoss: 0,
    totalProfitLossPercentage: 0,
    assetCount: 0,
    lastUpdated: new Date()
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [availableCryptos, setAvailableCryptos] = useState<any[]>([]);
  const [currentPrices, setCurrentPrices] = useState<{[key: string]: number}>({});
  const [userFunds, setUserFunds] = useState<UserFunds>({ balance: 0, currency: 'USD', lastUpdated: new Date() });
  const [historicalData, setHistoricalData] = useState<{ date: string; value: number }[]>([]);
  const [isLoadingHistorical, setIsLoadingHistorical] = useState(false);

  // Función para cargar los fondos del usuario
  const loadUserFunds = useCallback(async () => {
    if (!currentUser) return;

    try {
      console.log('Cargando fondos del usuario...');
      const funds = await getUserFunds(currentUser.uid);
      console.log('Fondos obtenidos:', funds);
      setUserFunds(funds);
      return funds;
    } catch (err) {
      console.error('Error al cargar fondos del usuario:', err);
      return null;
    }
  }, [currentUser]);

  // Cargar la cartera al montar el componente o cuando cambia el usuario
  useEffect(() => {
    if (!currentUser) {
      setPortfolio([]);
      setFullPortfolio(null);
      setTransactions([]);
      setHistoricalData([]);
      setIsLoading(false);
      return;
    }

    const loadPortfolio = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log('Cargando portafolio para el usuario:', currentUser.uid);

        // Obtener directamente de Firebase con máxima prioridad
        console.log('Obteniendo portafolio directamente desde Firebase...');
        const userFullPortfolio = await getFullPortfolio(currentUser.uid);
        console.log('Portafolio obtenido de Firebase:', userFullPortfolio);

        // Verificar si el portafolio existe y tiene la estructura correcta
        if (!userFullPortfolio) {
          console.warn('No se encontró un portafolio para este usuario, creando uno nuevo...');

          // Crear un portafolio vacío si no existe
          await setDoc(doc(db, 'Portafolio', currentUser.uid), {
            assets: [],
            transactions: [],
            funds: {
              balance: 0,
              currency: 'USD',
              lastUpdated: new Date()
            },
            lastUpdated: new Date(),
            userId: currentUser.uid
          });

          // Recargar el portafolio recién creado
          const newPortfolio = await getFullPortfolio(currentUser.uid);
          setFullPortfolio(newPortfolio);
          setPortfolio(newPortfolio.assets || []);
        } else {
          // Usar el portafolio existente
          setFullPortfolio(userFullPortfolio);
          setPortfolio(userFullPortfolio.assets || []);
        }

        // Obtener el historial de transacciones
        const userTransactions = await getTransactionHistory(currentUser.uid);
        console.log('Transacciones obtenidas:', userTransactions);
        setTransactions(userTransactions || []);

        // Cargar las criptomonedas disponibles para añadir
        const cryptos = await getTopCryptocurrencies(50);
        setAvailableCryptos(cryptos);

        // Obtener precios actuales para calcular valores
        const prices: {[key: string]: number} = {};
        cryptos.forEach((crypto: any) => {
          prices[crypto.id] = crypto.current_price;
        });
        setCurrentPrices(prices);

        // Obtener fondos del usuario
        const userFunds = await loadUserFunds();
        console.log('Fondos del usuario obtenidos:', userFunds);

        // Calcular estadísticas de la cartera
        calculatePortfolioStats(userFullPortfolio.assets || [], prices);

        // Como respaldo, intentar obtener desde el backend
        try {
          console.log('Intentando obtener portafolio desde el backend como respaldo...');
          const backendPortfolio = await getPortfolioFromBackend(currentUser.uid);

          if (backendPortfolio && backendPortfolio.assets && backendPortfolio.assets.length > 0) {
            console.log('Portafolio obtenido desde el backend como respaldo:', backendPortfolio);

            // Comparar si el backend tiene más activos que Firebase
            if ((backendPortfolio.assets.length > userFullPortfolio.assets.length) &&
                userFullPortfolio.assets.length === 0) {
              console.log('El backend tiene más activos, usando esos datos...');
              setFullPortfolio(backendPortfolio);
              setPortfolio(backendPortfolio.assets);

              // Sincronizar con Firebase
              const assetsToStore = backendPortfolio.assets.map(asset => ({
                ...asset,
                purchaseDate: asset.purchaseDate instanceof Date ?
                  Timestamp.fromDate(asset.purchaseDate) :
                  Timestamp.fromDate(new Date())
              }));

              await updateDoc(doc(db, 'Portafolio', currentUser.uid), {
                assets: assetsToStore,
                lastUpdated: new Date()
              });

              // Recalcular estadísticas
              calculatePortfolioStats(backendPortfolio.assets, prices);
            }
          }
        } catch (backendError) {
          console.warn('Error al obtener portafolio desde el backend:', backendError);
        }
      } catch (err) {
        console.error('Error al cargar la cartera:', err);
        setError('No se pudo cargar la cartera. Por favor, intenta de nuevo más tarde.');
      } finally {
        setIsLoading(false);
      }
    };

    loadPortfolio();
  }, [currentUser, loadUserFunds]);

  // Calcular estadísticas de la cartera
  const calculatePortfolioStats = (assets: PortfolioAsset[], prices: {[key: string]: number}) => {
    let totalValue = 0;
    let totalInvestment = 0;
    const assetCount = assets.length;

    assets.forEach(asset => {
      const currentPrice = prices[asset.id] || 0;
      const value = asset.amount * currentPrice;
      const investment = asset.amount * asset.purchasePrice;

      totalValue += value;
      totalInvestment += investment;
    });

    const totalProfitLoss = totalValue - totalInvestment;
    const totalProfitLossPercentage = totalInvestment > 0
      ? (totalProfitLoss / totalInvestment) * 100
      : 0;

    setPortfolioStats({
      totalValue,
      totalInvestment,
      totalProfitLoss,
      totalProfitLossPercentage,
      assetCount,
      lastUpdated: new Date()
    });
  };

  // Cargar datos históricos del portafolio
  const loadHistoricalData = useCallback(async (days: number = 30) => {
    if (!portfolio || portfolio.length === 0) {
      setHistoricalData([]);
      return;
    }

    try {
      setIsLoadingHistorical(true);
      setError(null);

      // Obtener datos históricos
      const data = await getHistoricalPortfolioValue(portfolio, days);

      if (data && data.length > 0) {
        setHistoricalData(data);
      } else {
        console.warn('No se pudieron obtener datos históricos para el portafolio');
        setHistoricalData([]);
      }
    } catch (err) {
      console.error('Error al cargar datos históricos:', err);
      setError('No se pudieron cargar los datos históricos del portafolio');
      setHistoricalData([]);
    } finally {
      setIsLoadingHistorical(false);
    }
  }, [portfolio]);

  // Cargar datos históricos cuando cambia el portafolio
  useEffect(() => {
    if (portfolio && portfolio.length > 0 && !isLoading) {
      loadHistoricalData(30);
    }
  }, [portfolio, isLoading, loadHistoricalData]);

  // Función para añadir un activo a la cartera
  const addAssetToPortfolio = async (asset: PortfolioAsset) => {
    if (!currentUser) {
      throw new Error('Debes iniciar sesión para añadir activos a tu cartera.');
    }

    setIsLoading(true);
    try {
      console.log('Añadiendo activo a la cartera:', asset);

      // Actualizar la cartera local INMEDIATAMENTE para feedback visual
      const updatedPortfolio = [...portfolio];
      const existingAssetIndex = updatedPortfolio.findIndex(a => a.id === asset.id);

      if (existingAssetIndex !== -1) {
        // Si el activo ya existe, actualizar la cantidad y el precio promedio
        const existingAsset = updatedPortfolio[existingAssetIndex];
        const totalAmount = existingAsset.amount + asset.amount;
        const avgPrice = ((existingAsset.amount * existingAsset.purchasePrice) +
                        (asset.amount * asset.purchasePrice)) / totalAmount;

        updatedPortfolio[existingAssetIndex] = {
          ...existingAsset,
          amount: totalAmount,
          purchasePrice: avgPrice,
          notes: asset.notes || existingAsset.notes
        };
      } else {
        // Si es un nuevo activo, añadirlo a la cartera
        updatedPortfolio.push(asset);
      }

      // Actualizar el estado local primero para feedback inmediato
      setPortfolio(updatedPortfolio);

      // Actualizar estadísticas de la cartera inmediatamente
      calculatePortfolioStats(updatedPortfolio, currentPrices);

      // Añadir el activo a Firebase (esto también registrará la transacción)
      const success = await addAsset(currentUser.uid, asset);

      if (success) {
        console.log('Activo añadido correctamente a Firebase');

        // Actualizar el historial de transacciones
        const updatedTransactions = await getTransactionHistory(currentUser.uid);
        setTransactions(updatedTransactions);

        // Recargar la cartera completa desde Firebase para asegurar sincronización
        const refreshedPortfolio = await getUserPortfolio(currentUser.uid);
        setPortfolio(refreshedPortfolio);

        // Recalcular estadísticas con los datos actualizados
        calculatePortfolioStats(refreshedPortfolio, currentPrices);

        return true;
      } else {
        // Si falla, revertir los cambios locales
        console.error('Error al guardar en Firebase, revirtiendo cambios locales');
        const refreshedPortfolio = await getUserPortfolio(currentUser.uid);
        setPortfolio(refreshedPortfolio);
        calculatePortfolioStats(refreshedPortfolio, currentPrices);
        return false;
      }
    } catch (err) {
      console.error('Error al añadir activo:', err);
      setError('No se pudo añadir el activo. Por favor, intenta de nuevo.');

      // Revertir cambios locales en caso de error
      const refreshedPortfolio = await getUserPortfolio(currentUser.uid);
      setPortfolio(refreshedPortfolio);
      calculatePortfolioStats(refreshedPortfolio, currentPrices);

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Función para eliminar un activo de la cartera
  const removeAssetFromPortfolio = async (assetId: string) => {
    if (!currentUser) {
      throw new Error('Debes iniciar sesión para eliminar activos de tu cartera.');
    }

    setIsLoading(true);
    try {
      // Buscar el activo que se va a eliminar para registrar la transacción
      const assetToRemove = portfolio.find(asset => asset.id === assetId);

      if (assetToRemove) {
        console.log('Eliminando activo de la cartera:', assetToRemove);

        // Actualizar la cartera local INMEDIATAMENTE para feedback visual
        const updatedPortfolio = portfolio.filter(asset => asset.id !== assetId);
        setPortfolio(updatedPortfolio);

        // Actualizar estadísticas de la cartera inmediatamente
        calculatePortfolioStats(updatedPortfolio, currentPrices);

        // Eliminar el activo de Firebase (esto también registrará la transacción)
        const success = await removeAsset(currentUser.uid, assetId);

        if (success) {
          console.log('Activo eliminado correctamente de Firebase');

          // Actualizar el historial de transacciones
          const updatedTransactions = await getTransactionHistory(currentUser.uid);
          setTransactions(updatedTransactions);

          // Recargar la cartera completa desde Firebase para asegurar sincronización
          const refreshedPortfolio = await getUserPortfolio(currentUser.uid);
          setPortfolio(refreshedPortfolio);

          // Recalcular estadísticas con los datos actualizados
          calculatePortfolioStats(refreshedPortfolio, currentPrices);

          return true;
        } else {
          // Si falla, revertir los cambios locales
          console.error('Error al eliminar en Firebase, revirtiendo cambios locales');
          const refreshedPortfolio = await getUserPortfolio(currentUser.uid);
          setPortfolio(refreshedPortfolio);
          calculatePortfolioStats(refreshedPortfolio, currentPrices);
          return false;
        }
      }
      return false;
    } catch (err) {
      console.error('Error al eliminar activo:', err);
      setError('No se pudo eliminar el activo. Por favor, intenta de nuevo.');

      // Revertir cambios locales en caso de error
      const refreshedPortfolio = await getUserPortfolio(currentUser.uid);
      setPortfolio(refreshedPortfolio);
      calculatePortfolioStats(refreshedPortfolio, currentPrices);

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Función para actualizar un activo específico
  const updateAsset = async (assetId: string, updates: Partial<PortfolioAsset>) => {
    if (!currentUser) {
      throw new Error('Debes iniciar sesión para actualizar activos de tu cartera.');
    }

    setIsLoading(true);
    try {
      // Actualizar el activo en Firebase
      const success = await updateAssetInPortfolio(currentUser.uid, assetId, updates);

      if (success) {
        // Actualizar la cartera local
        const updatedPortfolio = [...portfolio];
        const assetIndex = updatedPortfolio.findIndex(asset => asset.id === assetId);

        if (assetIndex !== -1) {
          updatedPortfolio[assetIndex] = {
            ...updatedPortfolio[assetIndex],
            ...updates
          };

          setPortfolio(updatedPortfolio);

          // Actualizar estadísticas de la cartera
          calculatePortfolioStats(updatedPortfolio, currentPrices);

          return true;
        }
      }
      return false;
    } catch (err) {
      console.error('Error al actualizar activo:', err);
      setError('No se pudo actualizar el activo. Por favor, intenta de nuevo.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Función para actualizar los precios de la cartera
  const refreshPrices = async () => {
    if (!currentUser) return false;

    setIsLoading(true);
    setError(null);

    try {
      // Obtener precios actualizados
      const cryptos = await getTopCryptocurrencies(50);

      // Actualizar precios actuales
      const prices: {[key: string]: number} = {};
      cryptos.forEach((crypto: any) => {
        prices[crypto.id] = crypto.current_price;
      });
      setCurrentPrices(prices);

      // Actualizar lista de criptomonedas disponibles
      setAvailableCryptos(cryptos);

      // Actualizar estadísticas de la cartera
      calculatePortfolioStats(portfolio, prices);

      return true;
    } catch (err) {
      console.error('Error al actualizar precios:', err);
      setError('No se pudieron actualizar los precios. Por favor, intenta de nuevo.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Función para agregar fondos al usuario
  const addFunds = async (amount: number, currency: string = 'USD') => {
    if (!currentUser) {
      throw new Error('Debes iniciar sesión para añadir fondos a tu cuenta.');
    }

    setIsLoading(true);
    try {
      // Actualizar fondos locales INMEDIATAMENTE antes de cualquier operación
      // Esto garantiza que el usuario vea el cambio de inmediato
      const currentBalance = userFunds.balance || 0;
      const newBalance = currentBalance + amount;

      console.log(`Actualizando fondos localmente: ${currentBalance} + ${amount} = ${newBalance} ${currency}`);

      // Actualizar el estado local primero
      setUserFunds({
        balance: newBalance,
        currency: currency || userFunds.currency,
        lastUpdated: new Date()
      });

      // Luego intentar guardar en Firebase
      const success = await addFundsToUser(currentUser.uid, amount, currency);

      if (success) {
        console.log('Fondos guardados correctamente en Firebase');

        // Actualizar el historial de transacciones
        const updatedTransactions = await getTransactionHistory(currentUser.uid);
        setTransactions(updatedTransactions);

        return true;
      } else {
        // Si falla, revertir el cambio local
        console.error('Error al guardar fondos en Firebase, revirtiendo cambio local');
        setUserFunds({
          balance: currentBalance,
          currency: userFunds.currency,
          lastUpdated: userFunds.lastUpdated
        });
        return false;
      }
    } catch (err) {
      console.error('Error al añadir fondos:', err);
      setError('No se pudieron añadir los fondos. Por favor, intenta de nuevo.');

      // Revertir cualquier cambio local en caso de error
      const updatedFunds = await getUserFunds(currentUser.uid);
      setUserFunds(updatedFunds);

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Función para obtener el historial de transacciones
  const getTransactionHistoryData = useCallback(async () => {
    if (!currentUser) return [];

    try {
      const transactions = await getTransactionHistory(currentUser.uid);
      setTransactions(transactions);
      return transactions;
    } catch (err) {
      console.error('Error al obtener historial de transacciones:', err);
      return [];
    }
  }, [currentUser]);

  // Calcular valor actual, ganancia/pérdida para un activo
  const calculateAssetStats = (asset: PortfolioAsset) => {
    const currentPrice = currentPrices[asset.id] || 0;
    const value = asset.amount * currentPrice;
    const profitLoss = value - (asset.amount * asset.purchasePrice);
    const profitLossPercentage = asset.purchasePrice > 0
      ? ((currentPrice - asset.purchasePrice) / asset.purchasePrice) * 100
      : 0;

    return {
      currentPrice,
      value,
      profitLoss,
      profitLossPercentage
    };
  };

  // Función para reiniciar completamente el portafolio
  const resetPortfolio = async () => {
    if (!currentUser) {
      throw new Error('Debes iniciar sesión para reiniciar tu portafolio.');
    }

    setIsLoading(true);
    try {
      console.log('Reiniciando portafolio para el usuario:', currentUser.uid);

      // Crear un portafolio vacío
      await setDoc(doc(db, 'Portafolio', currentUser.uid), {
        assets: [],
        transactions: [],
        funds: {
          balance: 0,
          currency: 'USD',
          lastUpdated: new Date()
        },
        lastUpdated: new Date(),
        userId: currentUser.uid
      });

      // Actualizar el estado local
      setPortfolio([]);
      setTransactions([]);
      setUserFunds({ balance: 0, currency: 'USD', lastUpdated: new Date() });
      setHistoricalData([]);

      // Actualizar estadísticas
      setPortfolioStats({
        totalValue: 0,
        totalInvestment: 0,
        totalProfitLoss: 0,
        totalProfitLossPercentage: 0,
        assetCount: 0,
        lastUpdated: new Date()
      });

      console.log('Portafolio reiniciado correctamente');
      return true;
    } catch (err) {
      console.error('Error al reiniciar portafolio:', err);
      setError('No se pudo reiniciar el portafolio. Por favor, intenta de nuevo.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    portfolio,
    fullPortfolio,
    transactions,
    portfolioStats,
    isLoading,
    error,
    availableCryptos,
    currentPrices,
    userFunds,
    historicalData,
    isLoadingHistorical,
    addAssetToPortfolio,
    removeAssetFromPortfolio,
    updateAsset,
    refreshPrices,
    calculateAssetStats,
    getTransactionHistoryData,
    loadHistoricalData,
    loadUserFunds,
    addFunds,
    resetPortfolio
  };
};
