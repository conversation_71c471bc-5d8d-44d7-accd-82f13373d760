import React, { useState, useEffect } from 'react';
import { useTopCryptocurrencies } from '../../hooks/useMcpClient';
import PredictionDetails from './PredictionDetails';
import { analyzeCryptoSentiment } from '../../services/sentiment/sentimentAnalysis';
import { getOnChainMetrics } from '../../services/onchain/etherscanService';
import '../../styles/guru/PredictionIndicator.css';

interface PredictionIndicatorProps {
  cryptoId: string;
  timeframe?: '1d' | '7d' | '30d';
}

interface Prediction {
  direction: 'up' | 'down' | 'sideways';
  confidence: number;
  potentialGain?: number;
  potentialLoss?: number;
  signals: {
    technical: number; // -100 to 100
    sentiment: number; // -100 to 100
    onChain: number; // -100 to 100
    fundamental: number; // -100 to 100
  };
  reasoning: string[];
}

const PredictionIndicator: React.FC<PredictionIndicatorProps> = ({
  cryptoId,
  timeframe = '7d'
}) => {
  const [prediction, setPrediction] = useState<Prediction | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [sentimentData, setSentimentData] = useState<any>(null);
  const [onChainData, setOnChainData] = useState<any>(null);
  const [loadingDetails, setLoadingDetails] = useState<boolean>(false);
  const { data: cryptoData } = useTopCryptocurrencies();

  // Generar predicciones utilizando el servicio de predicción
  useEffect(() => {
    const generatePrediction = async () => {
      try {
        setLoading(true);
        setError(null);

        // Encontrar datos de la criptomoneda
        const crypto = cryptoData?.find(c => c.id === cryptoId);
        if (!crypto) {
          throw new Error('Criptomoneda no encontrada');
        }

        // Convertir el timeframe al formato esperado por el servicio
        const daysMap: Record<string, string> = {
          '1d': '1d',
          '7d': '7d',
          '30d': '30d'
        };

        // Importar el servicio de predicción dinámicamente para evitar problemas de carga
        const predictionService = await import('../../services/ai/predictionService');

        console.log(`Generando predicción para ${crypto.name} (${timeframe})`);

        // Llamar al servicio de predicción
        const result = await predictionService.generatePrediction(
          cryptoId,
          daysMap[timeframe]
        );

        // Crear la predicción a partir del resultado
        const newPrediction: Prediction = {
          direction: result.direction,
          confidence: result.confidence,
          potentialGain: result.potentialGain,
          potentialLoss: result.potentialLoss,
          signals: {
            technical: result.signals.technical,
            sentiment: result.signals.sentiment,
            onChain: result.signals.onChain,
            fundamental: result.signals.fundamental
          },
          reasoning: result.reasoning
        };

        console.log('Predicción generada:', newPrediction);
        setPrediction(newPrediction);
      } catch (err: any) {
        console.error('Error al generar predicción:', err);
        setError(err.message || 'Error al generar la predicción');
      } finally {
        setLoading(false);
      }
    };

    if (cryptoId && cryptoData) {
      generatePrediction();
    }
  }, [cryptoId, timeframe, cryptoData]);

  if (loading) {
    return (
      <div className="prediction-indicator loading">
        <div className="prediction-loading">
          <div className="prediction-spinner"></div>
          <p>Analizando datos y generando predicción...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="prediction-indicator error">
        <div className="prediction-error">
          <p>Error: {error}</p>
          <button onClick={() => window.location.reload()}>Reintentar</button>
        </div>
      </div>
    );
  }

  if (!prediction) {
    return (
      <div className="prediction-indicator empty">
        <p>No hay suficientes datos para generar una predicción</p>
      </div>
    );
  }

  // Cargar datos adicionales para el análisis detallado
  const loadDetailedData = async () => {
    if (!cryptoData) return;

    try {
      setLoadingDetails(true);

      // Encontrar datos de la criptomoneda
      const crypto = cryptoData.find(c => c.id === cryptoId);
      if (!crypto) {
        throw new Error('Criptomoneda no encontrada');
      }

      // Cargar datos de sentimiento
      console.log('Cargando datos de sentimiento...');
      const sentiment = await analyzeCryptoSentiment(cryptoId, crypto.name);
      setSentimentData(sentiment);

      // Cargar datos on-chain
      console.log('Cargando datos on-chain...');
      const onChain = await getOnChainMetrics(cryptoId);
      setOnChainData(onChain);

      setShowDetails(true);
    } catch (err) {
      console.error('Error al cargar datos detallados:', err);
    } finally {
      setLoadingDetails(false);
    }
  };

  return (
    <div className={`prediction-indicator ${prediction.direction}`}>
      <div className="prediction-header">
        <h3>Predicción del Guru Cripto</h3>
        <div className="prediction-timeframe">
          <span>Horizonte: {timeframe === '1d' ? '24 horas' : timeframe === '7d' ? '7 días' : '30 días'}</span>
        </div>
      </div>

      <div className="prediction-main">
        <div className="prediction-gauge">
          <div className="gauge-container">
            <div
              className="gauge-fill"
              style={{
                transform: `rotate(${(prediction.confidence / 100) * 180 - 90}deg)`,
                backgroundColor: prediction.direction === 'up'
                  ? 'var(--success)'
                  : prediction.direction === 'down'
                    ? 'var(--error)'
                    : 'var(--warning)'
              }}
            ></div>
            <div className="gauge-center">
              <span className="gauge-value">{Math.round(prediction.confidence)}%</span>
              <span className="gauge-label">Confianza</span>
            </div>
          </div>

          <div className="prediction-direction">
            <div className={`direction-indicator ${prediction.direction}`}>
              {prediction.direction === 'up' && (
                <>
                  <span className="direction-arrow">↑</span>
                  <span className="direction-text">Alcista</span>
                  {prediction.potentialGain && (
                    <span className="potential-value">+{prediction.potentialGain}%</span>
                  )}
                </>
              )}

              {prediction.direction === 'down' && (
                <>
                  <span className="direction-arrow">↓</span>
                  <span className="direction-text">Bajista</span>
                  {prediction.potentialLoss && (
                    <span className="potential-value">-{prediction.potentialLoss}%</span>
                  )}
                </>
              )}

              {prediction.direction === 'sideways' && (
                <>
                  <span className="direction-arrow">↔</span>
                  <span className="direction-text">Lateral</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="prediction-signals">
          <h4>Señales de Mercado</h4>
          <div className="signals-container">
            <div className="signal-item">
              <span className="signal-label">Técnico</span>
              <div className="signal-bar-container">
                <div
                  className={`signal-bar ${prediction.signals.technical > 0 ? 'positive' : prediction.signals.technical < 0 ? 'negative' : 'neutral'}`}
                  style={{
                    width: `${Math.abs(prediction.signals.technical)}%`,
                    marginLeft: prediction.signals.technical >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.technical)}%`
                  }}
                ></div>
                <div className="signal-zero-line"></div>
              </div>
              <span className="signal-value">{prediction.signals.technical > 0 ? '+' : ''}{prediction.signals.technical}</span>
            </div>

            <div className="signal-item">
              <span className="signal-label">Sentimiento</span>
              <div className="signal-bar-container">
                <div
                  className={`signal-bar ${prediction.signals.sentiment > 0 ? 'positive' : prediction.signals.sentiment < 0 ? 'negative' : 'neutral'}`}
                  style={{
                    width: `${Math.abs(prediction.signals.sentiment)}%`,
                    marginLeft: prediction.signals.sentiment >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.sentiment)}%`
                  }}
                ></div>
                <div className="signal-zero-line"></div>
              </div>
              <span className="signal-value">{prediction.signals.sentiment > 0 ? '+' : ''}{prediction.signals.sentiment}</span>
            </div>

            <div className="signal-item">
              <span className="signal-label">On-Chain</span>
              <div className="signal-bar-container">
                <div
                  className={`signal-bar ${prediction.signals.onChain > 0 ? 'positive' : prediction.signals.onChain < 0 ? 'negative' : 'neutral'}`}
                  style={{
                    width: `${Math.abs(prediction.signals.onChain)}%`,
                    marginLeft: prediction.signals.onChain >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.onChain)}%`
                  }}
                ></div>
                <div className="signal-zero-line"></div>
              </div>
              <span className="signal-value">{prediction.signals.onChain > 0 ? '+' : ''}{prediction.signals.onChain}</span>
            </div>

            <div className="signal-item">
              <span className="signal-label">Fundamental</span>
              <div className="signal-bar-container">
                <div
                  className={`signal-bar ${prediction.signals.fundamental > 0 ? 'positive' : prediction.signals.fundamental < 0 ? 'negative' : 'neutral'}`}
                  style={{
                    width: `${Math.abs(prediction.signals.fundamental)}%`,
                    marginLeft: prediction.signals.fundamental >= 0 ? '50%' : `${50 - Math.abs(prediction.signals.fundamental)}%`
                  }}
                ></div>
                <div className="signal-zero-line"></div>
              </div>
              <span className="signal-value">{prediction.signals.fundamental > 0 ? '+' : ''}{prediction.signals.fundamental}</span>
            </div>
          </div>
        </div>
      </div>

      {!showDetails ? (
        <div className="prediction-reasoning">
          <h4>Análisis del Guru</h4>
          <ul className="reasoning-list">
            {prediction.reasoning.map((reason, index) => (
              <li key={index} className="reasoning-item">{reason}</li>
            ))}
          </ul>

          <button
            className="view-details-button"
            onClick={loadDetailedData}
            disabled={loadingDetails}
          >
            {loadingDetails ? 'Cargando datos...' : 'Ver análisis detallado'}
          </button>
        </div>
      ) : (
        <PredictionDetails
          prediction={prediction}
          sentimentData={sentimentData}
          onChainData={onChainData}
        />
      )}

      <div className="prediction-disclaimer">
        <p>Este análisis es generado por IA y no constituye asesoramiento financiero. Siempre realiza tu propia investigación antes de invertir.</p>
      </div>
    </div>
  );
};

export default PredictionIndicator;
