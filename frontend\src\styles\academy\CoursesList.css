.courses-list {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  color: var(--text-primary);
}

.courses-header {
  margin-bottom: 2rem;
}

.courses-header h2 {
  font-size: 1.8rem;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.courses-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.courses-filters {
  margin-bottom: 2rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.search-bar {
  position: relative;
  margin-bottom: 1.5rem;
}

.search-bar i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-bar input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background-color: var(--color-background);
  color: var(--text-primary);
}

.search-bar input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.filter-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.filter-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background-color: var(--color-background);
  color: var(--text-primary);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
}

.filter-group select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.results-count {
  margin: 0 0 1.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.course-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.course-image {
  height: 180px;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
  position: relative;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.course-level {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.course-level.beginner {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.course-level.intermediate {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.course-level.advanced {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.course-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.course-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.course-description {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  flex-grow: 1;
}

.course-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.course-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-rating {
  margin-bottom: 1rem;
}

.star-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #f39c12;
}

.rating-value {
  margin-left: 0.5rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.course-instructor {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.course-instructor img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.view-course-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  align-self: flex-start;
}

.view-course-button:hover {
  background-color: var(--color-primary-dark);
}

.no-courses-found {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  background-color: var(--color-surface-hover);
  border-radius: 12px;
}

.no-courses-found i {
  font-size: 3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.no-courses-found h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.no-courses-found p {
  margin: 0;
  color: var(--text-secondary);
}

/* Loading State */
.courses-list.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-options {
    flex-direction: column;
  }
  
  .filter-group {
    width: 100%;
  }
}
