/* Estilos para el dashboard inteligente con IA contextual */

.smart-dashboard {
  width: 100%;
  max-width: 100%;
  padding: clamp(0.5rem, 2vw, 2rem);
  background-color: var(--color-background);
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 2rem);
  box-sizing: border-box;
}

/* Header con estadísticas globales */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(0.5rem, 1vw, 1rem);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  flex-wrap: wrap;
  gap: 1rem;
}

.global-stats {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(0.5rem, 1vw, 1rem);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: clamp(0.75rem, 1vw, 0.875rem);
}

.stat-label {
  color: var(--text-secondary);
  white-space: nowrap;
}

.stat-value {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  white-space: nowrap;
}

.change {
  margin-left: 0.25rem;
  font-size: 0.75em;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
}

.change.positive {
  color: var(--color-positive);
  background-color: rgba(0, 255, 157, 0.1);
}

.change.negative {
  color: var(--color-negative);
  background-color: rgba(255, 58, 110, 0.1);
}

.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  min-width: 250px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-surface);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 300px;
}

.search-bar:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.search-bar input {
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  width: 100%;
  font-size: 0.875rem;
}

.search-bar input::placeholder {
  color: var(--text-tertiary);
}

.search-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  color: var(--color-primary);
}

/* Contenido principal */
.dashboard-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: clamp(1rem, 2vw, 2rem);
}

/* Panel lateral */
.dashboard-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.welcome-message {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  padding: 1rem;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.welcome-message h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.market-status {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.sentiment {
  font-weight: var(--font-weight-semibold);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  margin-left: 0.25rem;
}

.sentiment.fear {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.sentiment.neutral {
  color: #f1c40f;
  background-color: rgba(241, 196, 15, 0.1);
}

.sentiment.greed {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
}

.guru-avatar-container {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  padding: 1rem;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: auto;
}

.guru-message {
  text-align: center;
}

.guru-message p {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.consult-guru-button {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.consult-guru-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(110, 66, 202, 0.4);
}

/* Contenido principal del dashboard */
.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 2rem);
}

/* Secciones del dashboard */
.dashboard-section {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.dashboard-section:hover {
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-md);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: clamp(0.75rem, 1.5vw, 1.5rem);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: var(--color-primary);
}

.section-header h2 {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.recommendation-badge {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-left: 0.5rem;
}

.view-all-button {
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.view-all-button:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

/* Sección de monedas recomendadas */
.recommended-section {
  border: 1px solid rgba(110, 66, 202, 0.3);
  background-color: rgba(110, 66, 202, 0.05);
}

.recommended-section .section-header::after {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
}

/* Filtros */
.section-header.with-filters {
  flex-wrap: wrap;
  gap: 0.75rem;
}

.desktop-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.time-filter, .category-filter {
  display: flex;
  gap: 0.25rem;
}

.time-filter button, .category-filter button {
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-filter button:hover, .category-filter button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.time-filter button.active, .category-filter button.active {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.mobile-filter-button {
  display: none;
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: center;
  gap: 0.5rem;
}

.mobile-filter-button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.mobile-filters {
  display: none;
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  flex-direction: column;
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group h3 {
  font-size: 0.875rem;
  margin: 0;
  color: var(--text-secondary);
}

.close-filters-button {
  background-color: var(--color-primary);
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  color: white;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
}

.close-filters-button:hover {
  background-color: var(--color-primary-light);
}

/* Paginación */
.table-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.pagination-button {
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.pagination-button.active {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.pagination-ellipsis {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Estilos responsivos */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 250px 1fr;
  }
  
  .global-stats {
    flex-wrap: wrap;
    justify-content: space-between;
  }
  
  .stat-item {
    flex-basis: calc(33.33% - 1rem);
  }
}

@media (max-width: 992px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .dashboard-sidebar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .guru-avatar-container {
    margin-top: 0;
  }
  
  .desktop-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    width: 100%;
  }
  
  .time-filter, .category-filter {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.25rem;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .global-stats {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    justify-content: flex-start;
    gap: 1rem;
  }
  
  .stat-item {
    flex-basis: auto;
  }
  
  .search-container {
    width: 100%;
    justify-content: flex-start;
  }
  
  .search-bar {
    max-width: 100%;
  }
  
  .dashboard-sidebar {
    grid-template-columns: 1fr;
  }
  
  .desktop-filters {
    display: none;
  }
  
  .mobile-filter-button {
    display: flex;
  }
  
  .mobile-filters {
    display: flex;
  }
  
  .pagination-button:not(.active):not(:first-child):not(:last-child) {
    display: none;
  }
}

@media (max-width: 576px) {
  .smart-dashboard {
    padding: 0.5rem;
  }
  
  .dashboard-section {
    padding: 0.75rem;
  }
  
  .section-header h2 {
    font-size: 1rem;
  }
  
  .global-stats {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stat-item {
    width: 100%;
    justify-content: space-between;
  }
}
