rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Funciones de utilidad para las reglas de seguridad
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Regla por defecto: denegar todo el acceso
    match /{document=**} {
      allow read, write: if false;
    }

    // Reglas para la colección users (nueva estructura)
    match /users/{userId} {
      // Permitir lectura si el usuario está autenticado y es el propietario
      allow read: if isOwner(userId);

      // Permitir creación si el usuario está autenticado y el ID del documento coincide con su UID
      allow create: if isOwner(userId);

      // Permitir actualización si el usuario está autenticado y es el propietario
      allow update: if isOwner(userId);

      // No permitir eliminación de usuarios
      allow delete: if false;

      // Permitir acceso a todas las subcolecciones del usuario
      match /{collection}/{docId} {
        allow read, write: if isOwner(userId);
      }
    }

    // Reglas específicas para la colección radar
    match /users/{userId}/radar/{document=**} {
      allow read, write: if isOwner(userId);
    }

    // Reglas específicas para la colección portfolio
    match /users/{userId}/portfolio/{document=**} {
      allow read, write: if isOwner(userId);
    }

    // Reglas específicas para la colección conversations
    match /users/{userId}/conversations/{document=**} {
      allow read, write: if isOwner(userId);
    }

    // Reglas para la colección Users (estructura antigua)
    match /Users/<USER>
      // Permitir lectura si el usuario está autenticado y es el propietario
      allow read: if isOwner(userId);

      // Permitir creación si el usuario está autenticado y el ID del documento coincide con su UID
      allow create: if isOwner(userId) &&
                     request.resource.data.uid == userId &&
                     request.resource.data.email == request.auth.token.email;

      // Permitir actualización si el usuario está autenticado y es el propietario
      // y no está intentando cambiar su UID o email
      allow update: if isOwner(userId) &&
                     request.resource.data.uid == resource.data.uid &&
                     request.resource.data.email == resource.data.email;

      // No permitir eliminación de usuarios
      allow delete: if false;
    }

    // Reglas para la colección Portafolio (estructura antigua)
    match /Portafolio/{userId} {
      // Permitir lectura si el usuario está autenticado y es el propietario
      allow read: if isOwner(userId);

      // Permitir creación si el usuario está autenticado y el ID del documento coincide con su UID
      allow create: if isOwner(userId);

      // Permitir actualización si el usuario está autenticado y es el propietario
      allow update: if isOwner(userId);

      // Permitir eliminación si el usuario está autenticado y es el propietario
      allow delete: if isOwner(userId);
    }

    // Reglas para la colección Watchlist (estructura antigua)
    match /Watchlist/{userId} {
      // Permitir lectura si el usuario está autenticado y es el propietario
      allow read: if isOwner(userId);

      // Permitir creación si el usuario está autenticado y el ID del documento coincide con su UID
      allow create: if isOwner(userId);

      // Permitir actualización si el usuario está autenticado y es el propietario
      allow update: if isOwner(userId);

      // Permitir eliminación si el usuario está autenticado y es el propietario
      allow delete: if isOwner(userId);
    }

    // Reglas para la colección crypto_data (datos públicos)
    match /crypto_data/{document} {
      // Permitir lectura a todos los usuarios autenticados
      allow read: if isAuthenticated();
      // Solo permitir escritura a través de funciones de Cloud Functions o admin SDK
      allow write: if false;
    }

    // Reglas para la colección price_alerts
    match /price_alerts/{alertId} {
      // Permitir lectura si el usuario está autenticado y es el propietario de la alerta
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;

      // Permitir creación si el usuario está autenticado y está creando una alerta para sí mismo
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;

      // Permitir actualización si el usuario está autenticado y es el propietario de la alerta
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;

      // Permitir eliminación si el usuario está autenticado y es el propietario de la alerta
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // Reglas para la colección alert_notifications
    match /alert_notifications/{notificationId} {
      // Permitir lectura si el usuario está autenticado y es el destinatario de la notificación
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;

      // Permitir actualización si el usuario está autenticado y es el destinatario de la notificación
      // y solo está actualizando el campo 'read'
      allow update: if isAuthenticated() &&
                     resource.data.userId == request.auth.uid &&
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read', 'readAt']);

      // No permitir creación o eliminación directa (solo a través de Cloud Functions)
      allow create, delete: if false;
    }
  }
}