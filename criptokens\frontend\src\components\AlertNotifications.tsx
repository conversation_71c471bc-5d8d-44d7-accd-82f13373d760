import React, { useState } from 'react';
import { useAlertNotifications } from '../hooks/useCryptoData';
import '../styles/AlertNotifications.css';

interface AlertNotificationsProps {
  onClose?: () => void;
}

const AlertNotifications: React.FC<AlertNotificationsProps> = ({ onClose }) => {
  const { notifications, loading, error, unreadCount, markAsRead } = useAlertNotifications();
  const [showAll, setShowAll] = useState(false);

  // Función para formatear la fecha
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Función para manejar el clic en una notificación
  const handleNotificationClick = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Error al marcar notificación como leída:', error);
    }
  };

  // Filtrar notificaciones según la opción seleccionada
  const filteredNotifications = showAll 
    ? notifications 
    : notifications.filter(notification => !notification.read);

  if (loading) {
    return <div className="notifications-loading">Cargando notificaciones...</div>;
  }

  if (error) {
    return <div className="notifications-error">Error: {error}</div>;
  }

  return (
    <div className="notifications-container">
      <div className="notifications-header">
        <h2>Notificaciones</h2>
        {unreadCount > 0 && (
          <span className="unread-badge">{unreadCount}</span>
        )}
        {onClose && (
          <button className="close-button" onClick={onClose}>×</button>
        )}
      </div>

      <div className="notifications-filter">
        <button 
          className={!showAll ? 'active' : ''}
          onClick={() => setShowAll(false)}
        >
          No leídas
        </button>
        <button 
          className={showAll ? 'active' : ''}
          onClick={() => setShowAll(true)}
        >
          Todas
        </button>
      </div>

      <div className="notifications-content">
        {filteredNotifications.length === 0 ? (
          <div className="no-notifications">
            <p>{showAll ? 'No tienes notificaciones.' : 'No tienes notificaciones sin leer.'}</p>
          </div>
        ) : (
          <div className="notifications-list">
            {filteredNotifications.map(notification => (
              <div 
                key={notification.id} 
                className={`notification-item ${notification.read ? 'read' : 'unread'}`}
                onClick={() => !notification.read && handleNotificationClick(notification.id)}
              >
                <div className="notification-icon">
                  {notification.type === 'price_alert' ? (
                    <i className="price-alert-icon">💰</i>
                  ) : (
                    <i className="system-icon">ℹ️</i>
                  )}
                </div>
                
                <div className="notification-content">
                  <div className="notification-title">
                    {notification.title}
                    {!notification.read && <span className="unread-dot"></span>}
                  </div>
                  
                  <div className="notification-message">
                    {notification.message}
                  </div>
                  
                  <div className="notification-time">
                    {formatDate(notification.createdAt)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertNotifications;
