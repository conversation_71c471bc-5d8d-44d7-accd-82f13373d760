import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import GuruChatInterface from '../components/GuruChatInterface';
import ConversationSidebar from '../components/ConversationSidebar';
import GuruContextSidebar from '../components/GuruContextSidebar';
import GuruVoiceInterface from '../components/GuruVoiceInterface';
import { AuthProvider, useAuth } from '../context/NewAuthContext';
import '../styles/GuruChatInterface.css';
import '../styles/ConversationSidebar.css';
import '../styles/GuruContextSidebar.css';
import '../styles/GuruVoiceInterface.css';
import '../styles/guru/GuruHeader.css';

const CryptoGuruPageContent: React.FC = () => {
  const [showInfo, setShowInfo] = useState(false);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [showSidebar, setShowSidebar] = useState(true);
  const [currentTopic, setCurrentTopic] = useState<string | null>('general_market');
  const [showVoiceInterface, setShowVoiceInterface] = useState(false);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);
  const { currentUser } = useAuth();

  // Función para crear una nueva conversación
  const handleNewConversation = async () => {
    if (!currentUser) return;

    try {
      const response = await fetch('http://localhost:3001/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: currentUser.uid,
          title: 'Nueva conversación'
        })
      });

      if (!response.ok) {
        throw new Error(`Error al crear conversación: ${response.statusText}`);
      }

      const data = await response.json();

      // Verificar la estructura de la respuesta
      if (data && data.success && data.conversation && data.conversation.id) {
        setActiveConversationId(data.conversation.id);
      } else if (data && data.id) {
        setActiveConversationId(data.id);
      } else {
        console.error('Formato de respuesta inesperado:', data);
        alert('Error: Formato de respuesta inesperado al crear conversación');
      }
    } catch (error: any) {
      console.error('Error al crear nueva conversación:', error);
      alert(`Error al crear nueva conversación: ${error.message}`);
    }
  };

  // Función para seleccionar una conversación existente
  const handleSelectConversation = (conversationId: string) => {
    setActiveConversationId(conversationId);
  };

  // Crear una nueva conversación al cargar si no hay ninguna activa
  React.useEffect(() => {
    if (currentUser && !activeConversationId) {
      handleNewConversation();
    }
  }, [currentUser]);

  return (
    <div className="crypto-guru-page">
        <header className="guru-header">
          <Link to="/" className="back-link">
            ← Volver al Dashboard
          </Link>
          <h1>Gurú Cripto</h1>
          <div className="guru-header-actions">
            <Link to="/portfolio" className="portfolio-header-link">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 6H16V4C16 2.89 15.11 2 14 2H10C8.89 2 8 2.89 8 4V6H4C2.89 6 2 6.89 2 8V19C2 20.11 2.89 21 4 21H20C21.11 21 22 20.11 22 19V8C22 6.89 21.11 6 20 6ZM10 4H14V6H10V4ZM20 19H4V8H20V19Z" fill="currentColor"/>
                <path d="M7 14C7 12.34 8.34 11 10 11C11.66 11 13 12.34 13 14C13 15.66 11.66 17 10 17C8.34 17 7 15.66 7 14Z" fill="currentColor"/>
                <path d="M15 11H19V13H15V11Z" fill="currentColor"/>
                <path d="M15 15H19V17H15V15Z" fill="currentColor"/>
              </svg>
              <span>Mi Portafolio</span>
            </Link>
            <Link to="/predictor" className="predictor-header-link">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z" fill="currentColor"/>
                <path d="M10.5 7C8.57 7 7 5.43 7 3.5S8.57 0 10.5 0s3.5 1.57 3.5 3.5S12.43 7 10.5 7zm0-5C9.67 2 9 2.67 9 3.5S9.67 5 10.5 5s1.5-.67 1.5-1.5S11.33 2 10.5 2z" fill="currentColor"/>
              </svg>
              <span>Predictor IA</span>
            </Link>
            <button
              className="info-button"
              onClick={() => setShowInfo(!showInfo)}
              title="Información sobre el Gurú Cripto"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
                <path d="M11 17H13V11H11V17ZM12 9C12.55 9 13 8.55 13 8C13 7.45 12.55 7 12 7C11.45 7 11 7.45 11 8C11 8.55 11.45 9 12 9Z" fill="currentColor"/>
              </svg>
            </button>
            <button
              className={`voice-toggle-button ${showVoiceInterface ? 'active' : ''}`}
              onClick={() => setShowVoiceInterface(!showVoiceInterface)}
              title={showVoiceInterface ? "Ocultar interfaz de voz" : "Mostrar interfaz de voz"}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" fill="currentColor"/>
                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" fill="currentColor"/>
              </svg>
            </button>
            <div className="guru-status online">
              <span className="status-dot"></span>
              <span className="status-text">Conectado</span>
            </div>
          </div>
        </header>

        {showInfo && (
          <div className="guru-info-panel">
            <div className="guru-info-content">
              <h2>Gurú Cripto con Brave Search</h2>
              <p>El Gurú Cripto es un asistente de IA especializado en criptomonedas que puede responder a tus preguntas sobre precios, tendencias y noticias del mercado.</p>

              <h3>Funcionalidades:</h3>
              <ul>
                <li><strong>Precios en tiempo real:</strong> Pregunta por el precio actual de cualquier criptomoneda.</li>
                <li><strong>Análisis de portafolio:</strong> Conectado a tu portafolio personal para análisis personalizados.</li>
                <li><strong>Noticias:</strong> Obtén las últimas noticias sobre criptomonedas (integrado con Brave Search).</li>
                <li><strong>Asistente visual:</strong> El avatar reacciona según el rendimiento de las criptomonedas.</li>
              </ul>

              <h3>Ejemplos de preguntas:</h3>
              <ul>
                <li>"¿Cuál es el precio de Bitcoin?"</li>
                <li>"¿Cuánto vale Ethereum?"</li>
                <li>"Analiza mi portafolio"</li>
                <li>"¿Cómo está rindiendo mi inversión?"</li>
                <li>"Noticias sobre Solana"</li>
                <li>"Qué hay de nuevo con Cardano"</li>
              </ul>

              <div className="brave-search-info">
                <h3>Integraciones avanzadas</h3>
                <p><strong>Brave Search:</strong> El Gurú Cripto utiliza la API de Brave Search para obtener noticias actualizadas en tiempo real sobre criptomonedas.</p>
                <p><strong>Firestore:</strong> Conectado a tu portafolio personal almacenado en Firestore para proporcionar análisis personalizados sobre tus inversiones.</p>
                <p>Estas integraciones permiten respuestas más precisas y personalizadas sobre el mercado de criptomonedas y tu situación financiera.</p>
              </div>

              <button className="close-info-button" onClick={() => setShowInfo(false)}>Cerrar</button>
            </div>
          </div>
        )}

        <main className="guru-content">
          <div className={`guru-layout ${showSidebar ? 'with-sidebar' : ''}`}>
            {showSidebar && (
              <ConversationSidebar
                onSelectConversation={handleSelectConversation}
                onNewConversation={handleNewConversation}
                activeConversationId={activeConversationId}
              />
            )}
            <div className="guru-chat-wrapper">
              <div className="sidebar-toggle" onClick={() => setShowSidebar(!showSidebar)}>
                {showSidebar ? (
                  <i className="fas fa-chevron-left" title="Ocultar historial"></i>
                ) : (
                  <i className="fas fa-chevron-right" title="Mostrar historial"></i>
                )}
              </div>

              {showVoiceInterface && (
                <GuruVoiceInterface
                  onSendMessage={(message) => {
                    // Aquí se enviaría el mensaje al chat
                    setIsProcessingVoice(true);
                  }}
                  onReceiveResponse={(response) => {
                    // Aquí se procesaría la respuesta
                    setIsProcessingVoice(false);
                  }}
                  isProcessing={isProcessingVoice}
                />
              )}

              <GuruChatInterface
                conversationId={activeConversationId}
                onTopicChange={setCurrentTopic}
              />
            </div>
            <GuruContextSidebar currentTopic={currentTopic} />
          </div>
        </main>
      </div>
  );
};

// Componente contenedor que proporciona el contexto de autenticación
const CryptoGuruPage: React.FC = () => {
  return (
    <AuthProvider>
      <CryptoGuruPageContent />
    </AuthProvider>
  );
};

export default CryptoGuruPage;
