.course-viewer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  color: var(--text-primary);
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 100px);
}

.course-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.course-header h1 {
  margin: 0.5rem 0;
  font-size: 2rem;
  color: var(--text-primary);
}

.back-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0;
  font-size: 1rem;
  transition: color 0.2s ease;
}

.back-button:hover {
  color: var(--color-primary);
}

.course-meta-info {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.course-level {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.course-level.beginner {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.course-level.intermediate {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.course-level.advanced {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.course-duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.course-container {
  display: flex;
  flex: 1;
  position: relative;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--color-surface);
}

.toggle-sidebar {
  position: absolute;
  top: 1rem;
  left: 300px;
  z-index: 10;
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-hidden .toggle-sidebar {
  left: 0;
}

.course-sidebar {
  width: 300px;
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  transition: all 0.3s ease;
}

.sidebar-hidden .course-sidebar {
  margin-left: -300px;
}

.course-progress-bar {
  height: 8px;
  background-color: var(--color-surface-hover);
  position: relative;
  margin-bottom: 1rem;
}

.course-progress-bar .progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 0;
}

.course-progress-bar .progress-text {
  position: absolute;
  top: 12px;
  left: 10px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.modules-list {
  padding: 1rem 0;
}

.module-item {
  margin-bottom: 0.5rem;
}

.module-header {
  padding: 1rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.module-header:hover {
  background-color: var(--color-surface-hover);
}

.module-header.active {
  background-color: var(--color-surface-hover);
  border-left: 3px solid var(--color-primary);
}

.module-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.module-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quiz-passed {
  color: #2ecc71;
}

.lessons-list {
  padding: 0 0 0.5rem 0;
}

.lesson-item, .quiz-item {
  padding: 0.75rem 1rem 0.75rem 2rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
  position: relative;
}

.lesson-item:hover, .quiz-item:hover {
  background-color: var(--color-surface-hover);
}

.lesson-item.active, .quiz-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  font-weight: 600;
}

.lesson-item.completed::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #2ecc71;
}

.lesson-title, .quiz-title {
  flex: 1;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.lesson-duration {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.lesson-completed-icon {
  color: #2ecc71;
  margin-left: 0.5rem;
}

.quiz-item {
  background-color: rgba(var(--color-primary-rgb), 0.05);
  margin-top: 0.5rem;
}

.quiz-item.passed {
  border-left: 3px solid #2ecc71;
}

.quiz-score-badge {
  background-color: #2ecc71;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.course-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.sidebar-hidden .course-content {
  margin-left: 30px;
}

.content-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  color: var(--text-secondary);
  position: relative;
  transition: color 0.2s ease;
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--color-primary);
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--color-primary);
  border-radius: 3px 3px 0 0;
}

.lesson-content {
  max-width: 800px;
  margin: 0 auto;
}

.lesson-content h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.lesson-video {
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 8px;
}

.lesson-video iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.lesson-text {
  line-height: 1.6;
  font-size: 1.05rem;
}

.lesson-text h1 {
  font-size: 1.8rem;
  margin-top: 0;
}

.lesson-text h2 {
  font-size: 1.5rem;
  margin-top: 2rem;
}

.lesson-text h3 {
  font-size: 1.3rem;
}

.lesson-text p {
  margin-bottom: 1.5rem;
}

.lesson-text ul, .lesson-text ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.lesson-text li {
  margin-bottom: 0.5rem;
}

.lesson-text code {
  background-color: var(--color-surface-hover);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: monospace;
}

.lesson-text pre {
  background-color: var(--color-surface-hover);
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.lesson-text blockquote {
  border-left: 4px solid var(--color-primary);
  padding-left: 1rem;
  margin-left: 0;
  color: var(--text-secondary);
}

.lesson-text table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
}

.lesson-text th, .lesson-text td {
  border: 1px solid var(--border-color);
  padding: 0.75rem;
  text-align: left;
}

.lesson-text th {
  background-color: var(--color-surface-hover);
}

.lesson-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.prev-lesson, .mark-completed {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary, .btn-secondary {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-primary:disabled {
  background-color: var(--color-surface-hover);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-surface);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-primary);
}

.btn-secondary:disabled {
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* Quiz Styles */
.quiz-container {
  max-width: 800px;
  margin: 0 auto;
}

.quiz-container h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.quiz-description {
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
}

.quiz-instructions {
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-radius: 8px;
  font-weight: 600;
}

.quiz-questions {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.quiz-question {
  padding: 1.5rem;
  background-color: var(--color-surface-hover);
  border-radius: 8px;
}

.quiz-question h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quiz-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quiz-option:hover {
  border-color: var(--color-primary);
}

.quiz-option input[type="radio"] {
  margin: 0;
}

.submit-quiz {
  margin-top: 2rem;
  align-self: center;
}

.quiz-results {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.quiz-score {
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.quiz-score.passed {
  background-color: rgba(46, 204, 113, 0.2);
}

.quiz-score.failed {
  background-color: rgba(231, 76, 60, 0.2);
}

.quiz-score h3 {
  margin-top: 0;
  font-size: 1.5rem;
}

.quiz-answers-review {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.quiz-question-review {
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.quiz-question-review.correct {
  background-color: rgba(46, 204, 113, 0.1);
  border-left-color: #2ecc71;
}

.quiz-question-review.incorrect {
  background-color: rgba(231, 76, 60, 0.1);
  border-left-color: #e74c3c;
}

.quiz-question-review h4 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.quiz-question-review p {
  margin-bottom: 0.5rem;
}

.quiz-question-review .explanation {
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--color-surface);
  border-radius: 8px;
  font-style: italic;
}

.quiz-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

/* Resources Styles */
.resources-container {
  max-width: 800px;
  margin: 0 auto;
}

.resources-container h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.resource-card {
  display: flex;
  padding: 1.5rem;
  background-color: var(--color-surface-hover);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.resource-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-sm);
}

.resource-icon {
  font-size: 2rem;
  margin-right: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  color: white;
}

.resource-icon.document {
  background-color: #e74c3c;
}

.resource-icon.article {
  background-color: #3498db;
}

.resource-icon.video {
  background-color: #9b59b6;
}

.resource-icon.tool {
  background-color: #f39c12;
}

.resource-icon.link {
  background-color: #1abc9c;
}

.resource-content {
  flex: 1;
}

.resource-content h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.resource-content p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.resource-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.resource-link:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Discussion Styles */
.discussion-container {
  max-width: 800px;
  margin: 0 auto;
}

.discussion-container h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.discussion-placeholder {
  padding: 3rem;
  text-align: center;
  background-color: var(--color-surface-hover);
  border-radius: 8px;
  color: var(--text-secondary);
}

/* Loading and Error States */
.course-viewer.loading, .course-viewer.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .course-container {
    flex-direction: column;
  }
  
  .course-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    max-height: 300px;
  }
  
  .sidebar-hidden .course-sidebar {
    margin-left: 0;
    max-height: 0;
    overflow: hidden;
    border-bottom: none;
  }
  
  .toggle-sidebar {
    top: 0.5rem;
    left: auto;
    right: 0.5rem;
  }
  
  .sidebar-hidden .toggle-sidebar {
    left: auto;
  }
  
  .sidebar-hidden .course-content {
    margin-left: 0;
  }
  
  .lesson-navigation {
    flex-direction: column;
    gap: 1rem;
  }
  
  .prev-lesson, .mark-completed {
    width: 100%;
    justify-content: center;
  }
}
