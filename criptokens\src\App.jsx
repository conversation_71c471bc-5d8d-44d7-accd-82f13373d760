import React, { useEffect, useState } from 'react';
import anime from 'animejs';
import './App.css';
import CriptoAgentDashboard from './components/CriptoAgentDashboard';

function App() {
  // Initialize page load animation
  useEffect(() => {
    // Animate the loading screen
    anime.timeline({
      easing: 'easeOutExpo',
    })
    .add({
      targets: '.loading-screen',
      opacity: [1, 0],
      duration: 800,
      delay: 1000,
      complete: function() {
        document.querySelector('.loading-screen').style.display = 'none';
      }
    })
    .add({
      targets: '.app-container',
      opacity: [0, 1],
      duration: 800,
    }, '-=400');

    // Animate the logo
    anime({
      targets: '.loading-logo',
      scale: [0.5, 1],
      opacity: [0, 1],
      duration: 1000,
      easing: 'spring(1, 80, 10, 0)'
    });

    // Animate the loading text
    anime({
      targets: '.loading-text span',
      opacity: [0, 1],
      translateY: [20, 0],
      delay: anime.stagger(100),
      easing: 'easeOutQuad',
      duration: 800
    });
  }, []);

  const [loading, setLoading] = useState(true);

  // Set loading to false after animations complete
  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  }, []);

  return (
    <>
      {loading ? (
        <div className="loading-screen">
          <div className="loading-logo">
            <div className="logo-circle"></div>
            <div className="logo-ring ring1"></div>
            <div className="logo-ring ring2"></div>
          </div>
          <div className="loading-text">
            {Array.from('CRIPTOKENS').map((letter, index) => (
              <span key={index}>{letter}</span>
            ))}
          </div>
        </div>
      ) : (
        <div className="app-container">
          <CriptoAgentDashboard />
        </div>
      )}
    </>
  );
}

export default App;
