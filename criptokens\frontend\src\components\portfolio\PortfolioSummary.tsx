import React from 'react';
import { formatNumber, formatPercentage, formatDate } from '../../utils/formatters';
import '../../styles/portfolio/PortfolioSummary.css';

interface PortfolioStats {
  totalValue: number;
  totalInvestment: number;
  totalProfitLoss: number;
  totalProfitLossPercentage: number;
  assetCount: number;
  lastUpdated: Date;
}

interface PortfolioSummaryProps {
  stats: PortfolioStats;
}

const PortfolioSummary: React.FC<PortfolioSummaryProps> = ({ stats }) => {
  const {
    totalValue,
    totalInvestment,
    totalProfitLoss,
    totalProfitLossPercentage,
    lastUpdated
  } = stats;

  // Determinar la clase CSS basada en si hay ganancia o pérdida
  const profitLossClass = totalProfitLoss >= 0 ? 'positive' : 'negative';

  return (
    <div className="portfolio-summary">
      <div className="summary-card total-value">
        <h3>Valor Total</h3>
        <div className="summary-value">{formatNumber(totalValue)} USD</div>
        <div className="summary-subtitle">Última actualización: {formatDate(lastUpdated)}</div>
      </div>

      <div className="summary-card profit-loss">
        <h3>Ganancia/Pérdida</h3>
        <div className={`summary-value ${profitLossClass}`}>
          {formatNumber(totalProfitLoss)} USD
          <span className="percentage">({formatPercentage(totalProfitLossPercentage)})</span>
        </div>
        <div className="summary-subtitle">Desde la inversión inicial</div>
      </div>

      <div className="summary-card investment">
        <h3>Inversión Total</h3>
        <div className="summary-value">{formatNumber(totalInvestment)} USD</div>
        <div className="summary-subtitle">Capital invertido</div>
      </div>
    </div>
  );
};

export default PortfolioSummary;
