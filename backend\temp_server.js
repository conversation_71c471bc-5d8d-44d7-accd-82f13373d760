const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { spawn } = require('child_process');
const path = require('path');
const guruRoutes = require('./routes/guru.routes');
const technicalRoutes = require('./routes/technical.routes');
const cryptoRoutes = require('./routes/crypto.routes');
const fundamentalRoutes = require('./routes/fundamental.routes');
const enhancedNewsRoutes = require('./routes/enhancedNews.routes');
const ultravoxRoutes = require('./routes/ultravox.routes');
const conversationRoutes = require('./routes/conversation.routes');
const academyRoutes = require('./routes/academy.routes');
const marketRoutes = require('./routes/market.routes');
const a2aRoutes = require('./routes/a2a.routes');
const adkRoutes = require('./routes/adk_routes');
const adkAgentsRoutes = require('./routes/adk-agents.routes');
const { checkFetchMcpAvailability } = require('./services/fetch-mcp.service');
require('dotenv').config();
// Importar la configuración directamente
const config = {
  cors: {
    origins: [
      'http://localhost:5173', // Frontend
      'http://localhost:5174', // Frontend (puerto alternativo)
      'http://localhost:3008', // Backend (nuevo)
      'http://localhost:3007', // Backend (anterior)
      'http://localhost:3006', // Backend (anterior)
      'http://localhost:3005', // Backend (anterior)
      'http://localhost:3004', // Backend (anterior)
      'http://localhost:3003', // Backend (anterior)
      'http://localhost:3002', // Backend (actualizado)
      'http://localhost:3001', // Backend (anterior)
      'http://localhost:3000'  // Desarrollo alternativo
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  }
};

const app = express();
const PORT = 3008; // Usar puerto 3008 para evitar conflictos

// Middleware
app.use(cors({
  origin: config.cors.origins,
  methods: config.cors.methods,
  allowedHeaders: config.cors.allowedHeaders,
  credentials: config.cors.credentials
}));
app.use(bodyParser.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Rutas
app.use('/api/guru', guruRoutes);
app.use('/api/technical', technicalRoutes);
app.use('/api/crypto', cryptoRoutes);
app.use('/api/fundamental', fundamentalRoutes);
app.use('/api/enhanced-news', enhancedNewsRoutes);
app.use('/api/ultravox', ultravoxRoutes);
app.use('/api/conversations', conversationRoutes);
app.use('/api/academy', academyRoutes);
app.use('/api/market', marketRoutes);
app.use('/api/a2a', a2aRoutes);
app.use('/api/adk', adkRoutes);
app.use('/api/adk-agents', adkAgentsRoutes);

// Ruta de prueba
app.get('/', (req, res) => {
  res.json({ message: 'API de Criptokens funcionando correctamente' });
});

// Manejador de errores
app.use((err, req, res, next) => {
  console.error('Error no controlado:', err);
  res.status(500).json({ error: 'Error interno del servidor', details: err.message });
});

// Iniciar el servidor fetch-mcp
let fetchMcpProcess = null;
let a2aProcesses = {};

const startFetchMcp = () => {
  const fetchMcpPath = path.join(__dirname, '../../fetch-mcp/dist/index.js');
  console.log(`Iniciando servidor fetch-mcp desde: ${fetchMcpPath}`);

  fetchMcpProcess = spawn('node', [fetchMcpPath], {
    stdio: 'pipe',
    env: { ...process.env, PORT: '3104' }
  });

  fetchMcpProcess.stdout.on('data', (data) => {
    console.log(`[fetch-mcp] ${data.toString().trim()}`);
  });

  fetchMcpProcess.stderr.on('data', (data) => {
    console.error(`[fetch-mcp] Error: ${data.toString().trim()}`);
  });

  fetchMcpProcess.on('close', (code) => {
    console.log(`[fetch-mcp] Proceso terminado con código ${code}`);
    // Reiniciar si se cierra inesperadamente
    if (code !== 0) {
      console.log('[fetch-mcp] Reiniciando servidor fetch-mcp...');
      setTimeout(startFetchMcp, 5000);
    }
  });

  // Verificar disponibilidad después de un breve retraso
  setTimeout(async () => {
    const isAvailable = await checkFetchMcpAvailability();
    console.log(`[fetch-mcp] Disponibilidad: ${isAvailable ? 'OK' : 'No disponible'}`);
  }, 3000);
};

// Iniciar los agentes A2A
const startA2AAgents = () => {
  const a2aStarterPath = path.join(__dirname, '../../criptokens/a2a/start_agents.py');
  console.log(`Iniciando agentes A2A desde: ${a2aStarterPath}`);

  const a2aProcess = spawn('python', [a2aStarterPath], {
    stdio: 'pipe',
    env: { ...process.env }
  });

  a2aProcess.stdout.on('data', (data) => {
    console.log(`[a2a] ${data.toString().trim()}`);
  });

  a2aProcess.stderr.on('data', (data) => {
    console.error(`[a2a] Error: ${data.toString().trim()}`);
  });

  a2aProcess.on('close', (code) => {
    console.log(`[a2a] Proceso terminado con código ${code}`);
    // Reiniciar si se cierra inesperadamente
    if (code !== 0) {
      console.log('[a2a] Reiniciando agentes A2A...');
      setTimeout(startA2AAgents, 5000);
    }
  });

  // Almacenar proceso
  a2aProcesses.starter = a2aProcess;
};

// Iniciar servidor
const server = app.listen(PORT, () => {
  console.log(`Servidor backend ejecutándose en el puerto ${PORT}`);

  // Iniciar el servidor fetch-mcp
  startFetchMcp();

  // Iniciar los agentes A2A
  startA2AAgents();
});

// Manejar cierre del servidor
process.on('SIGINT', () => {
  console.log('Cerrando servidores...');

  // Cerrar fetch-mcp si está en ejecución
  if (fetchMcpProcess) {
    fetchMcpProcess.kill();
  }

  // Cerrar agentes A2A si están en ejecución
  Object.values(a2aProcesses).forEach(process => {
    if (process) {
      process.kill();
    }
  });

  // Cerrar servidor Express
  server.close(() => {
    console.log('Servidor Express cerrado');
    process.exit(0);
  });
});

module.exports = app;
