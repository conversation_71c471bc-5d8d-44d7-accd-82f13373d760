"""
Sentiment Analysis Agent using Google ADK
"""
import os
import json
import aiohttp
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from google.adk.runtime import InvocationContext

# API Keys
BRAVE_API_KEY = os.getenv("BRAVE_API_KEY", "BSAccS820UUfffNOAD7yLACz9htlbe9")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")
MCP_BRAVE_URL = os.getenv("BRAVE_SEARCH_MCP_URL", "http://localhost:3102")

# Function tools for the agent
async def fetch_news(crypto_name: str, days: int = 7) -> List[Dict[str, Any]]:
    """
    Fetch news articles about a cryptocurrency.

    Args:
        crypto_name: The name of the cryptocurrency
        days: Number of days of news to fetch

    Returns:
        List of news articles
    """
    try:
        # Try to get news from MCP Brave service
        async with aiohttp.ClientSession() as session:
            payload = {
                "query": f"{crypto_name} cryptocurrency news",
                "count": 10,
                "freshness": "week" if days <= 7 else "month"
            }
            headers = {
                "X-Brave-API-Key": BRAVE_API_KEY
            }

            async with session.post(
                f"{MCP_BRAVE_URL}/search",
                json=payload,
                headers=headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "web" in result and "results" in result["web"]:
                        return result["web"]["results"]

        # Fallback to simulated news if MCP fails
        print("Failed to fetch news from MCP Brave, using simulated data")
        return _generate_simulated_news(crypto_name, days)
    except Exception as e:
        print(f"Error fetching news: {e}")
        return _generate_simulated_news(crypto_name, days)

def _generate_simulated_news(crypto_name: str, days: int) -> List[Dict[str, Any]]:
    """Generate simulated news articles."""
    import random

    # Possible sentiment categories
    sentiments = ["positive", "neutral", "negative"]

    # Possible headlines by sentiment
    headlines = {
        "positive": [
            f"{crypto_name} Surges to New Heights as Adoption Increases",
            f"Analysts Predict Bright Future for {crypto_name}",
            f"{crypto_name} Gains Momentum as Institutional Interest Grows",
            f"Major Company Announces {crypto_name} Integration",
            f"{crypto_name} Breaks Resistance Level, Bulls Take Control"
        ],
        "neutral": [
            f"{crypto_name} Stabilizes After Recent Volatility",
            f"Experts Weigh In on {crypto_name}'s Future Prospects",
            f"Understanding {crypto_name}'s Role in the Crypto Ecosystem",
            f"Regulatory Clarity Could Impact {crypto_name}'s Development",
            f"{crypto_name} Trading Volume Remains Consistent"
        ],
        "negative": [
            f"{crypto_name} Faces Selling Pressure Amid Market Uncertainty",
            f"Regulatory Concerns Impact {crypto_name} Price",
            f"Analysts Warn of Potential {crypto_name} Correction",
            f"{crypto_name} Drops Below Key Support Level",
            f"Investors Cautious About {crypto_name}'s Short-term Outlook"
        ]
    }

    # Generate random news articles
    news = []
    end_date = datetime.now()

    for i in range(10):  # Generate 10 articles
        # Random date within the specified days
        days_ago = random.randint(0, min(days, 30))
        date = end_date - timedelta(days=days_ago)

        # Random sentiment
        sentiment = random.choice(sentiments)

        # Random headline based on sentiment
        headline = random.choice(headlines[sentiment])

        # Generate article
        news.append({
            "title": headline,
            "url": f"https://example.com/news/{crypto_name.lower()}-{i}",
            "description": f"This is a simulated news article about {crypto_name}. The sentiment is {sentiment}.",
            "published_date": date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "source": random.choice(["CryptoNews", "BlockchainTimes", "CoinDesk", "CoinTelegraph", "Decrypt"]),
            "_simulated": True,
            "_sentiment": sentiment
        })

    return news

async def analyze_sentiment(news_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze sentiment of news articles.

    Args:
        news_articles: List of news articles

    Returns:
        Sentiment analysis results
    """
    try:
        # If we have simulated news with pre-assigned sentiment
        if all("_sentiment" in article for article in news_articles):
            return _analyze_simulated_sentiment(news_articles)

        # Prepare articles for analysis
        articles_text = []
        for article in news_articles[:5]:  # Limit to 5 articles to avoid token limits
            title = article.get("title", "")
            description = article.get("description", "")
            articles_text.append(f"Title: {title}\nDescription: {description}")

        combined_text = "\n\n".join(articles_text)

        # Use OpenRouter API to analyze sentiment
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": "anthropic/claude-3-haiku-20240307",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a sentiment analysis expert. Analyze the sentiment of cryptocurrency news articles and provide a detailed breakdown."
                    },
                    {
                        "role": "user",
                        "content": f"Analyze the sentiment of these cryptocurrency news articles. Provide a sentiment score from -100 (extremely negative) to +100 (extremely positive), with 0 being neutral. Also categorize the overall sentiment as positive, neutral, or negative, and provide a brief explanation of your analysis.\n\n{combined_text}"
                    }
                ],
                "response_format": {
                    "type": "json_object"
                }
            }

            async with session.post(
                "https://openrouter.ai/api/v1/chat/completions",
                json=payload,
                headers=headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")
                    try:
                        sentiment_analysis = json.loads(content)
                        return sentiment_analysis
                    except json.JSONDecodeError:
                        print(f"Failed to parse sentiment analysis result: {content}")

        # Fallback to simulated sentiment analysis
        print("Failed to analyze sentiment with OpenRouter, using simulated analysis")
        return _analyze_simulated_sentiment(news_articles)
    except Exception as e:
        print(f"Error analyzing sentiment: {e}")
        return _analyze_simulated_sentiment(news_articles)

def _analyze_simulated_sentiment(news_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate simulated sentiment analysis."""
    # Count sentiments
    sentiment_counts = {"positive": 0, "neutral": 0, "negative": 0}

    for article in news_articles:
        sentiment = article.get("_sentiment", "neutral")
        sentiment_counts[sentiment] += 1

    # Calculate overall sentiment
    total_articles = len(news_articles)
    positive_ratio = sentiment_counts["positive"] / total_articles
    negative_ratio = sentiment_counts["negative"] / total_articles
    neutral_ratio = sentiment_counts["neutral"] / total_articles

    # Calculate sentiment score (-100 to +100)
    sentiment_score = (positive_ratio * 100) - (negative_ratio * 100)

    # Determine overall sentiment category
    if sentiment_score > 20:
        overall_sentiment = "positive"
    elif sentiment_score < -20:
        overall_sentiment = "negative"
    else:
        overall_sentiment = "neutral"

    # Generate explanation
    explanation = f"Analysis based on {total_articles} news articles. "
    explanation += f"Found {sentiment_counts['positive']} positive, {sentiment_counts['neutral']} neutral, and {sentiment_counts['negative']} negative articles. "

    if overall_sentiment == "positive":
        explanation += "The overall sentiment is positive, indicating optimistic market outlook."
    elif overall_sentiment == "negative":
        explanation += "The overall sentiment is negative, suggesting caution in the market."
    else:
        explanation += "The overall sentiment is neutral, showing balanced market perspectives."

    return {
        "sentiment_score": round(sentiment_score, 2),
        "overall_sentiment": overall_sentiment,
        "explanation": explanation,
        "sentiment_distribution": {
            "positive": sentiment_counts["positive"],
            "neutral": sentiment_counts["neutral"],
            "negative": sentiment_counts["negative"]
        }
    }

async def fetch_social_media_sentiment(crypto_name: str) -> Dict[str, Any]:
    """
    Fetch sentiment from social media.

    Args:
        crypto_name: The name of the cryptocurrency

    Returns:
        Social media sentiment analysis
    """
    # This would normally call an API, but we'll simulate for now
    return _generate_simulated_social_sentiment(crypto_name)

def _generate_simulated_social_sentiment(crypto_name: str) -> Dict[str, Any]:
    """Generate simulated social media sentiment."""
    import random

    # Generate random sentiment metrics
    twitter_sentiment = random.uniform(-100, 100)
    reddit_sentiment = random.uniform(-100, 100)

    # Calculate average sentiment
    average_sentiment = (twitter_sentiment + reddit_sentiment) / 2

    # Determine sentiment category
    if average_sentiment > 20:
        overall_sentiment = "positive"
    elif average_sentiment < -20:
        overall_sentiment = "negative"
    else:
        overall_sentiment = "neutral"

    # Generate random metrics
    twitter_mentions = random.randint(1000, 10000)
    reddit_mentions = random.randint(500, 5000)

    # Generate random trending hashtags
    hashtags = [
        f"#{crypto_name}",
        f"#{crypto_name}ToTheMoon",
        f"#Buy{crypto_name}",
        f"#{crypto_name}News",
        f"#Crypto"
    ]
    random.shuffle(hashtags)
    trending_hashtags = hashtags[:3]

    return {
        "twitter": {
            "sentiment_score": round(twitter_sentiment, 2),
            "mentions": twitter_mentions,
            "trending_hashtags": trending_hashtags
        },
        "reddit": {
            "sentiment_score": round(reddit_sentiment, 2),
            "mentions": reddit_mentions,
            "active_subreddits": [f"r/{crypto_name}", "r/CryptoCurrency", "r/CryptoMarkets"]
        },
        "overall": {
            "sentiment_score": round(average_sentiment, 2),
            "sentiment_category": overall_sentiment,
            "total_mentions": twitter_mentions + reddit_mentions
        }
    }

async def calculate_fear_greed_index(news_sentiment: Dict[str, Any], social_sentiment: Dict[str, Any]) -> int:
    """
    Calculate Fear & Greed Index.

    Args:
        news_sentiment: News sentiment analysis
        social_sentiment: Social media sentiment analysis

    Returns:
        Fear & Greed Index (0-100)
    """
    # Extract sentiment scores
    news_score = news_sentiment.get("sentiment_score", 0)
    social_score = social_sentiment.get("overall", {}).get("sentiment_score", 0)

    # Convert from -100/+100 scale to 0-100 scale
    news_score_normalized = (news_score + 100) / 2
    social_score_normalized = (social_score + 100) / 2

    # Calculate weighted average (60% news, 40% social)
    fear_greed_index = (news_score_normalized * 0.6) + (social_score_normalized * 0.4)

    # Ensure it's within 0-100 range
    fear_greed_index = max(0, min(100, fear_greed_index))

    return round(fear_greed_index)

def get_fear_greed_classification(index: int) -> str:
    """Get classification for Fear & Greed Index."""
    if index >= 0 and index < 25:
        return "Extreme Fear"
    elif index >= 25 and index < 40:
        return "Fear"
    elif index >= 40 and index < 60:
        return "Neutral"
    elif index >= 60 and index < 75:
        return "Greed"
    else:
        return "Extreme Greed"

def extract_crypto_name(query: str) -> str:
    """Extract cryptocurrency name from query."""
    # Common cryptocurrencies
    crypto_names = {
        "bitcoin": "Bitcoin",
        "btc": "Bitcoin",
        "ethereum": "Ethereum",
        "eth": "Ethereum",
        "binance coin": "Binance Coin",
        "bnb": "Binance Coin",
        "cardano": "Cardano",
        "ada": "Cardano",
        "solana": "Solana",
        "sol": "Solana",
        "xrp": "XRP",
        "dogecoin": "Dogecoin",
        "doge": "Dogecoin",
        "polkadot": "Polkadot",
        "dot": "Polkadot",
        "tether": "Tether",
        "usdt": "Tether",
        "usd coin": "USD Coin",
        "usdc": "USD Coin"
    }

    query_lower = query.lower()

    # Check for exact matches
    for crypto_name_lower, crypto_name in crypto_names.items():
        if crypto_name_lower in query_lower:
            return crypto_name

    # Default to Bitcoin if no match found
    return "Bitcoin"

def extract_timeframe(query: str) -> int:
    """Extract timeframe from query."""
    query_lower = query.lower()

    if "year" in query_lower or "365" in query_lower:
        return 365
    elif "6 month" in query_lower or "180" in query_lower:
        return 180
    elif "3 month" in query_lower or "90" in query_lower:
        return 90
    elif "month" in query_lower or "30" in query_lower:
        return 30
    elif "week" in query_lower or "7" in query_lower:
        return 7
    elif "day" in query_lower or "24" in query_lower:
        return 1

    # Default to 7 days
    return 7

async def analyze_sentiment_for_crypto(query: str, ctx: InvocationContext) -> str:
    """
    Analyze sentiment for a cryptocurrency based on user query.

    Args:
        query: User query about cryptocurrency sentiment
        ctx: Invocation context

    Returns:
        Sentiment analysis result
    """
    # Extract crypto name and timeframe from query
    crypto_name = extract_crypto_name(query)
    days = extract_timeframe(query)

    # Fetch news articles
    news_articles = await fetch_news(crypto_name, days)

    # Analyze news sentiment
    news_sentiment = await analyze_sentiment(news_articles)

    # Fetch social media sentiment
    social_sentiment = await fetch_social_media_sentiment(crypto_name)

    # Calculate Fear & Greed Index
    fear_greed_index = await calculate_fear_greed_index(news_sentiment, social_sentiment)
    fear_greed_classification = get_fear_greed_classification(fear_greed_index)

    # Prepare result
    result = {
        "crypto_name": crypto_name,
        "timeframe_days": days,
        "news_sentiment": news_sentiment,
        "social_sentiment": social_sentiment,
        "fear_greed_index": fear_greed_index,
        "fear_greed_classification": fear_greed_classification,
        "news_articles": news_articles[:5]  # Include top 5 articles
    }

    # Store results in session state
    ctx.session.state["crypto_name"] = crypto_name
    ctx.session.state["timeframe_days"] = days
    ctx.session.state["sentiment_analysis"] = result

    # Return structured data for the LLM to format
    return json.dumps(result)

# Create the sentiment analysis agent
sentiment_agent = LlmAgent(
    name="sentiment_analysis_agent",
    model="gemini-1.5-pro",
    description="Analyzes sentiment of cryptocurrency news and social media",
    instruction="""
    You are a cryptocurrency sentiment analysis expert. Your task is to:

    1. Analyze the sentiment data provided to you
    2. Explain what the news sentiment indicates about market perception
    3. Interpret the social media sentiment and its implications
    4. Explain the Fear & Greed Index and its significance
    5. Provide a clear, concise sentiment analysis summary

    When responding:
    - Be specific about what the sentiment metrics mean
    - Explain the significance of the Fear & Greed Index
    - Highlight any discrepancies between news and social media sentiment
    - Provide a conclusion about the overall market sentiment

    The sentiment data will be provided to you as JSON data.
    """,
    tools=[FunctionTool(func=analyze_sentiment_for_crypto)],
    output_key="sentiment_analysis"
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime
    import asyncio

    async def main():
        runtime = Runtime()
        session = runtime.new_session()

        # Test the agent with a query
        response = await sentiment_agent.run_async(
            session=session,
            query="What's the sentiment for Bitcoin right now?"
        )

        print(response)

    asyncio.run(main())
