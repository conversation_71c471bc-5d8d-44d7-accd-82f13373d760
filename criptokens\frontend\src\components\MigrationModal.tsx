import React, { useState } from 'react';
import { useAuth } from '../context/NewAuthContext';
import { migrateWatchlistToRadar } from '../services/radarCripto.service.compat';
import '../styles/MigrationModal.css';

interface MigrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onMigrationComplete: () => void;
}

const MigrationModal: React.FC<MigrationModalProps> = ({ isOpen, onClose, onMigrationComplete }) => {
  const { currentUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  if (!isOpen) return null;

  const handleMigration = async () => {
    if (!currentUser) {
      setError('Debes iniciar sesión para migrar tus datos');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await migrateWatchlistToRadar(currentUser.uid);
      if (result) {
        setSuccess(true);
        setTimeout(() => {
          onMigrationComplete();
          onClose();
        }, 2000);
      } else {
        setError('No se encontraron datos para migrar o ya han sido migrados');
      }
    } catch (err: any) {
      console.error('Error durante la migración:', err);
      setError(`Error durante la migración: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="migration-modal-backdrop" onClick={onClose}>
      <div className="migration-modal-content" onClick={e => e.stopPropagation()}>
        <div className="migration-modal-header">
          <h3>¡Hemos mejorado tu experiencia!</h3>
          <button className="migration-modal-close" onClick={onClose}>&times;</button>
        </div>
        <div className="migration-modal-body">
          <p>Hemos renombrado "Watchlist" a "Mi Radar Cripto" y añadido nuevas funcionalidades.</p>
          <p>Para continuar usando tus datos, necesitamos migrarlos al nuevo formato.</p>
          
          {isLoading && (
            <div className="migration-loading">
              <div className="migration-spinner"></div>
              <p>Migrando tus datos...</p>
            </div>
          )}
          
          {error && (
            <div className="migration-error">
              <p>{error}</p>
            </div>
          )}
          
          {success && (
            <div className="migration-success">
              <p>¡Migración completada con éxito!</p>
            </div>
          )}
        </div>
        <div className="migration-modal-footer">
          <button 
            className="migration-btn-secondary" 
            onClick={onClose}
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button 
            className="migration-btn-primary" 
            onClick={handleMigration}
            disabled={isLoading || success}
          >
            Migrar mis datos
          </button>
        </div>
      </div>
    </div>
  );
};

export default MigrationModal;
