import React, { useState, useEffect } from 'react';
import '../styles/EnhancedAlertModal.css';

interface EnhancedAlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  cryptoId: string;
  cryptoName: string;
  cryptoSymbol: string;
  currentPrice: number;
  initialAlertPrice: number | null;
  initialAlertType?: 'above' | 'below';
  initialAlertPercentage?: number | null;
  initialNotificationType?: ('app' | 'email')[];
  onSave: (
    alertType: 'above' | 'below' | 'percent-up' | 'percent-down', 
    price: number | null,
    percentage: number | null,
    notificationTypes: ('app' | 'email')[]
  ) => void;
}

const EnhancedAlertModal: React.FC<EnhancedAlertModalProps> = ({
  isOpen,
  onClose,
  cryptoId,
  cryptoName,
  cryptoSymbol,
  currentPrice,
  initialAlertPrice,
  initialAlertType = 'above',
  initialAlertPercentage = null,
  initialNotificationType = ['app'],
  onSave
}) => {
  const [alertMode, setAlertMode] = useState<'price' | 'percentage'>('price');
  const [alertType, setAlertType] = useState<'above' | 'below'>(initialAlertType);
  const [alertPrice, setAlertPrice] = useState<string>(initialAlertPrice ? initialAlertPrice.toString() : '');
  const [alertPercentage, setAlertPercentage] = useState<string>(initialAlertPercentage ? initialAlertPercentage.toString() : '');
  const [notificationTypes, setNotificationTypes] = useState<('app' | 'email')[]>(initialNotificationType);

  useEffect(() => {
    if (isOpen) {
      setAlertMode('price');
      setAlertType(initialAlertType);
      setAlertPrice(initialAlertPrice ? initialAlertPrice.toString() : '');
      setAlertPercentage(initialAlertPercentage ? initialAlertPercentage.toString() : '');
      setNotificationTypes(initialNotificationType);
    }
  }, [isOpen, initialAlertPrice, initialAlertType, initialAlertPercentage, initialNotificationType]);

  const handleSave = () => {
    let finalAlertType: 'above' | 'below' | 'percent-up' | 'percent-down';
    let finalPrice: number | null = null;
    let finalPercentage: number | null = null;

    if (alertMode === 'price') {
      finalAlertType = alertType;
      finalPrice = alertPrice ? parseFloat(alertPrice) : null;
    } else {
      finalAlertType = alertType === 'above' ? 'percent-up' : 'percent-down';
      finalPercentage = alertPercentage ? parseFloat(alertPercentage) : null;
    }

    onSave(finalAlertType, finalPrice, finalPercentage, notificationTypes);
    onClose();
  };

  const toggleNotificationType = (type: 'app' | 'email') => {
    if (notificationTypes.includes(type)) {
      setNotificationTypes(notificationTypes.filter(t => t !== type));
    } else {
      setNotificationTypes([...notificationTypes, type]);
    }
  };

  const calculateTargetPrice = (): string => {
    if (alertMode === 'percentage' && alertPercentage) {
      const percentage = parseFloat(alertPercentage);
      if (!isNaN(percentage)) {
        const multiplier = alertType === 'above' ? 1 + (percentage / 100) : 1 - (percentage / 100);
        return (currentPrice * multiplier).toFixed(2);
      }
    }
    return 'N/A';
  };

  if (!isOpen) return null;

  return (
    <div className="enhanced-alert-modal-backdrop" onClick={onClose}>
      <div className="enhanced-alert-modal-content" onClick={e => e.stopPropagation()}>
        <div className="enhanced-alert-modal-header">
          <h3>
            Configurar Alerta para {cryptoName} ({cryptoSymbol.toUpperCase()})
            <span className="current-price">Precio actual: ${currentPrice.toFixed(2)}</span>
          </h3>
          <button className="enhanced-alert-modal-close" onClick={onClose}>&times;</button>
        </div>

        <div className="enhanced-alert-modal-body">
          <div className="alert-mode-selector">
            <button 
              className={`mode-button ${alertMode === 'price' ? 'active' : ''}`}
              onClick={() => setAlertMode('price')}
            >
              Alerta por Precio
            </button>
            <button 
              className={`mode-button ${alertMode === 'percentage' ? 'active' : ''}`}
              onClick={() => setAlertMode('percentage')}
            >
              Alerta por Porcentaje
            </button>
          </div>

          {alertMode === 'price' ? (
            <div className="price-alert-section">
              <div className="form-group">
                <label>Tipo de Alerta:</label>
                <div className="radio-group">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="alertType"
                      checked={alertType === 'above'}
                      onChange={() => setAlertType('above')}
                    />
                    <span>Notificar si sube por encima de</span>
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="alertType"
                      checked={alertType === 'below'}
                      onChange={() => setAlertType('below')}
                    />
                    <span>Notificar si baja por debajo de</span>
                  </label>
                </div>
              </div>
              <div className="form-group">
                <label htmlFor="alertPrice">Precio (USD):</label>
                <input
                  type="number"
                  id="alertPrice"
                  className="form-control"
                  value={alertPrice}
                  onChange={(e) => setAlertPrice(e.target.value)}
                  placeholder={`Ej: ${currentPrice.toFixed(2)}`}
                  step="0.01"
                />
              </div>
            </div>
          ) : (
            <div className="percentage-alert-section">
              <div className="form-group">
                <label>Tipo de Alerta:</label>
                <div className="radio-group">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="percentAlertType"
                      checked={alertType === 'above'}
                      onChange={() => setAlertType('above')}
                    />
                    <span>Notificar si sube un porcentaje</span>
                  </label>
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="percentAlertType"
                      checked={alertType === 'below'}
                      onChange={() => setAlertType('below')}
                    />
                    <span>Notificar si baja un porcentaje</span>
                  </label>
                </div>
              </div>
              <div className="form-group">
                <label htmlFor="alertPercentage">Porcentaje (%):</label>
                <input
                  type="number"
                  id="alertPercentage"
                  className="form-control"
                  value={alertPercentage}
                  onChange={(e) => setAlertPercentage(e.target.value)}
                  placeholder="Ej: 5"
                  step="0.1"
                  min="0.1"
                />
              </div>
              <div className="target-price-display">
                <span>Precio objetivo: ${calculateTargetPrice()}</span>
              </div>
            </div>
          )}

          <div className="notification-options">
            <h4>Opciones de Notificación</h4>
            <div className="checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={notificationTypes.includes('app')}
                  onChange={() => toggleNotificationType('app')}
                />
                <span>Notificación en la aplicación</span>
              </label>
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={notificationTypes.includes('email')}
                  onChange={() => toggleNotificationType('email')}
                />
                <span>Notificación por correo electrónico</span>
              </label>
            </div>
          </div>

          <div className="alert-info">
            <p>
              <i className="fas fa-info-circle"></i>
              Las alertas se activarán una sola vez cuando se alcance el precio o porcentaje especificado.
              Para recibir alertas adicionales, deberás configurar una nueva alerta después de que se active.
            </p>
          </div>
        </div>

        <div className="enhanced-alert-modal-footer">
          <button className="btn-secondary" onClick={onClose}>Cancelar</button>
          <button 
            className="btn-primary" 
            onClick={handleSave}
            disabled={(alertMode === 'price' && !alertPrice) || (alertMode === 'percentage' && !alertPercentage)}
          >
            Guardar
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAlertModal;
