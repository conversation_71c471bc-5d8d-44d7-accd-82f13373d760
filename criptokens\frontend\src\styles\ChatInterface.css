.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  position: relative;
}

.user-message {
  align-self: flex-end;
  background-color: #0084ff;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.ai-message {
  align-self: flex-start;
  background-color: #e5e5ea;
  color: #333;
  border-bottom-left-radius: 0.25rem;
}

.message.ai-message.streaming::after {
  content: '';
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 8px;
  height: 8px;
  background-color: #0084ff;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
  text-align: center;
}

.chat-options {
  display: flex;
  justify-content: flex-end;
  padding: 0.5rem 1rem;
  background-color: #f8f8f8;
  border-top: 1px solid #e5e5ea;
}

.streaming-toggle {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #666;
  cursor: pointer;
}

.streaming-toggle input {
  margin-right: 0.5rem;
}

.chat-input {
  display: flex;
  padding: 1rem;
  background-color: white;
  border-top: 1px solid #e5e5ea;
}

.chat-input textarea {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 1.5rem;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  min-height: 20px;
  max-height: 120px;
  outline: none;
}

.chat-input button {
  margin-left: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 1.5rem;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
  min-width: 100px;
}

.chat-input button.loading {
  background-color: #66b2ff;
  position: relative;
}

.chat-input button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Estilos para el contenido de los mensajes */
.message-content p {
  margin: 0 0 0.5rem 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}

/* Estilos para los datos de criptomonedas */
.crypto-data-container {
  margin-top: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 0.75rem;
}

/* Estilos para la tarjeta de precio */
.crypto-price-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.crypto-price-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.crypto-price-header img {
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  border-radius: 50%;
}

.crypto-price-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.crypto-price-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.price-item {
  display: flex;
  justify-content: space-between;
}

.price-label {
  color: #666;
  font-size: 0.875rem;
}

.price-value {
  font-weight: 600;
  color: #333;
  font-size: 0.875rem;
}

/* Estilos para la tarjeta de resumen del mercado */
.market-summary-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.market-summary-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: #333;
}

.market-summary-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.market-item {
  display: flex;
  justify-content: space-between;
}

.market-label {
  color: #666;
  font-size: 0.875rem;
}

.market-value {
  font-weight: 600;
  color: #333;
  font-size: 0.875rem;
}

/* Estilos para la lista de criptomonedas */
.crypto-list-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.crypto-list-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: #333;
}

.crypto-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.crypto-list-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.crypto-rank {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  margin-right: 0.5rem;
}

.crypto-list-item .crypto-icon {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  margin-right: 0.5rem;
  border-radius: 50%;
  vertical-align: middle !important;
  object-fit: contain !important;
}

.crypto-list-item .crypto-name {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.crypto-list-item .crypto-name span {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
}

.crypto-list-item .crypto-symbol {
  font-size: 0.75rem;
  color: #666;
  font-weight: normal;
}

.crypto-list-item .crypto-price {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  margin-right: 0.75rem;
}

.crypto-list-item .crypto-change {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  min-width: 60px;
  text-align: center;
}

.positive {
  color: #00C853;
}

.negative {
  color: #FF3D00;
}
