"""
Criptokens ADK Agents Package
"""

# Import new agents
from .guru_cripto_agent import guru_cripto_agent, run_guru_cripto
from .technical_analysis_agent import technical_analysis_agent, run_technical_analysis
from .sentiment_analysis_agent import sentiment_analysis_agent, run_sentiment_analysis
from .onchain_analysis_agent import onchain_analysis_agent, run_onchain_analysis

# Legacy imports (commented out to avoid errors if modules don't exist)
# from .technical_agent.agent import technical_agent
# from .sentiment_agent.agent import sentiment_agent
# from .onchain_agent.agent import onchain_agent
# from .guru_agent.agent import guru_agent

__all__ = [
    # New agents
    'guru_cripto_agent',
    'run_guru_cripto',
    'technical_analysis_agent',
    'run_technical_analysis',
    'sentiment_analysis_agent',
    'run_sentiment_analysis',
    'onchain_analysis_agent',
    'run_onchain_analysis',

    # Legacy agents (commented out)
    # 'technical_agent',
    # 'sentiment_agent',
    # 'onchain_agent',
    # 'guru_agent'
]
