/* Estilos para el componente de transacciones del portafolio */

.portfolio-transactions {
  width: 100%;
}

.transactions-filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.type-filter {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  color: var(--text-bright);
  font-size: 0.9375rem;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-input {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  color: var(--text-bright);
  font-size: 0.9375rem;
}

.export-button {
  margin-left: auto;
  background: var(--gradient-secondary);
  color: var(--text-bright);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.export-button:hover {
  box-shadow: 0 0 10px var(--secondary-glow);
}

.transactions-table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: var(--border-light);
}

.transactions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9375rem;
  background: var(--gradient-card);
  overflow: hidden;
}

.transactions-table th {
  text-align: left;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-dim);
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

.transactions-table td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-medium);
  vertical-align: middle;
}

.transactions-table tr:last-child td {
  border-bottom: none;
}

.transactions-table tr:hover td {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Estilos para los tipos de transacciones */
.transaction-type {
  text-align: center;
}

.type-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.8125rem;
  font-weight: 600;
}

.type-badge.buy {
  background-color: rgba(0, 255, 0, 0.1);
  color: var(--success);
  border: 1px solid rgba(0, 255, 0, 0.2);
}

.type-badge.sell {
  background-color: rgba(255, 0, 0, 0.1);
  color: var(--error);
  border: 1px solid rgba(255, 0, 0, 0.2);
}

.type-badge.deposit {
  background-color: rgba(0, 100, 255, 0.1);
  color: #3498db;
  border: 1px solid rgba(0, 100, 255, 0.2);
}

.type-badge.withdrawal {
  background-color: rgba(255, 165, 0, 0.1);
  color: #f39c12;
  border: 1px solid rgba(255, 165, 0, 0.2);
}

.transaction-notes {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Mensaje de transacciones vacías */
.empty-transactions-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.empty-transactions-message p {
  margin: 0.5rem 0;
  color: var(--text-medium);
}

.no-results-message {
  padding: 2rem;
  text-align: center;
  color: var(--text-dim);
}

/* Responsive */
@media (max-width: 768px) {
  .transactions-filters {
    gap: 1.5rem;
  }
  
  .filter-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .date-range {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .date-input {
    flex: 1;
    min-width: 120px;
  }
  
  .export-button {
    margin-left: 0;
    width: 100%;
  }
  
  .transactions-table th,
  .transactions-table td {
    padding: 0.75rem 1rem;
  }
}
