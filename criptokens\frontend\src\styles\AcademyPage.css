.academy-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--text-primary);
}

.academy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.back-link {
  color: var(--text-secondary);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--color-primary);
}

.academy-header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--text-primary);
}

.academy-actions {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 1rem;
  transition: color 0.2s ease;
  position: relative;
  padding: 0.5rem 0;
}

.nav-link:hover {
  color: var(--color-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.academy-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.25rem;
  color: var(--text-secondary);
  font-size: 1rem;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: rgba(123, 97, 255, 0.1);
}

.tab-button.active {
  color: var(--color-primary);
  border-bottom: 3px solid var(--color-primary);
  font-weight: 500;
}

/* Courses Section */
.courses-section {
  padding: 1rem 0;
}

.featured-course {
  display: flex;
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 2.5rem;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
}

.featured-course-image {
  width: 40%;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
  position: relative;
  min-height: 250px;
}

.course-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.course-badge.beginner {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.course-badge.intermediate {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.course-badge.advanced {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.featured-course-content {
  width: 60%;
  padding: 2rem;
}

.featured-course-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.featured-course-content p {
  margin: 0 0 1.5rem 0;
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
}

.course-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.course-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.start-course-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.start-course-button:hover {
  background-color: var(--color-primary-dark);
}

.courses-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.course-card {
  display: flex;
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.course-image {
  width: 35%;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
  position: relative;
  min-height: 180px;
}

.course-content {
  width: 65%;
  padding: 1.25rem;
}

.course-content h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.course-content p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

.view-course-button {
  background: none;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-course-button:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Glossary Section */
.glossary-section {
  padding: 1rem 0;
}

.glossary-search {
  display: flex;
  margin-bottom: 1.5rem;
}

.glossary-search input {
  flex: 1;
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 8px 0 0 8px;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-size: 1rem;
}

.glossary-search .search-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 0 8px 8px 0;
  padding: 0 1.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.glossary-search .search-button:hover {
  background-color: var(--color-primary-dark);
}

.glossary-alphabet {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.alphabet-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.alphabet-button:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.glossary-terms {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.glossary-term {
  background-color: var(--color-surface);
  border-radius: 8px;
  padding: 1.25rem;
  border: 1px solid var(--border-color);
}

.glossary-term h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.glossary-term p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Resources Section */
.resources-section {
  padding: 1rem 0;
}

.resources-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.resources-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.category-button {
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 0.5rem 1.25rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-button:hover {
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.category-button.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.resource-card {
  display: flex;
  background-color: var(--color-surface);
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  gap: 1rem;
}

.resource-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.resource-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.resource-icon.book {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.resource-icon.podcast {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.resource-icon.video {
  background-color: rgba(230, 126, 34, 0.1);
  color: #e67e22;
}

.resource-icon.blog {
  background-color: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.resource-icon.tool {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.resource-content {
  flex: 1;
}

.resource-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.resource-content p {
  margin: 0 0 0.75rem 0;
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.resource-type {
  display: inline-block;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: var(--color-surface-light);
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
}

.resource-link {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 0.85rem;
  transition: color 0.2s ease;
}

.resource-link:hover {
  text-decoration: underline;
}

/* FAQ Section */
.faq-section {
  padding: 1rem 0;
}

.faq-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.faq-search {
  display: flex;
  margin-bottom: 1.5rem;
}

.faq-search input {
  flex: 1;
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 8px 0 0 8px;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-size: 1rem;
}

.faq-search .search-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 0 8px 8px 0;
  padding: 0 1.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.faq-search .search-button:hover {
  background-color: var(--color-primary-dark);
}

.faq-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq-item {
  background-color: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  cursor: pointer;
}

.faq-question h4 {
  margin: 0;
  font-size: 1.05rem;
  color: var(--text-primary);
}

.toggle-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.faq-answer {
  padding: 0 1.25rem 1.25rem;
  border-top: 1px solid var(--border-color);
}

.faq-answer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Estilos para el visor de cursos */
.course-viewer-container {
  min-height: calc(100vh - 150px);
}

/* Nuevo estilo para el contenido de la academia */
.academy-content {
  padding: 1rem 0;
  min-height: calc(100vh - 150px);
}

.back-to-courses-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
  font-size: 0.95rem;
}

.back-to-courses-button:hover {
  color: var(--color-primary);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .courses-grid,
  .resources-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .academy-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .academy-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1 0 calc(50% - 0.5rem);
  }

  .featured-course {
    flex-direction: column;
  }

  .featured-course-image,
  .featured-course-content {
    width: 100%;
  }

  .courses-grid,
  .resources-grid {
    grid-template-columns: 1fr;
  }

  .course-card {
    flex-direction: column;
  }

  .course-image,
  .course-content {
    width: 100%;
  }
}
