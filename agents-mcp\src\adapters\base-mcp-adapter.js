/**
 * Adaptador base para servidores MCP
 * 
 * Esta clase proporciona la funcionalidad común para todos los adaptadores MCP.
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { McpServerError } = require('../utils/error-handler');
const { withRetry, sanitizeForLogging } = require('../utils/helpers');
const config = require('../config/config');

class BaseMcpAdapter {
  /**
   * @param {string} serverName - Nombre del servidor MCP
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(serverName, options = {}) {
    this.serverName = serverName;
    this.serverConfig = config.mcpServers[serverName] || {};
    this.baseUrl = options.baseUrl || this.serverConfig.url;
    this.timeout = options.timeout || this.serverConfig.timeout || 10000;
    this.capabilities = this.serverConfig.capabilities || [];
    this.sessionId = null;
    
    // Verificar que el servidor esté configurado
    if (!this.baseUrl) {
      throw new McpServerError(`No se ha configurado la URL para el servidor MCP ${serverName}`);
    }
    
    logger.debug('BaseMcpAdapter', `Inicializado adaptador para servidor MCP ${serverName}`, {
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      capabilities: this.capabilities
    });
  }
  
  /**
   * Crea una sesión en el servidor MCP
   * @returns {Promise<string>} ID de la sesión
   */
  async createSession() {
    try {
      logger.debug('BaseMcpAdapter', `Creando sesión en servidor MCP ${this.serverName}`);
      
      // Si el servidor es interno, generar un ID de sesión local
      if (this.baseUrl === 'internal') {
        this.sessionId = `internal-${uuidv4()}`;
        logger.debug('BaseMcpAdapter', `Creada sesión interna con ID ${this.sessionId}`);
        return this.sessionId;
      }
      
      // Crear sesión en el servidor MCP
      const response = await withRetry(() => axios.post(`${this.baseUrl}/mcp`, {
        jsonrpc: '2.0',
        method: 'session.create',
        params: {},
        id: 1
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Mcp-Session-Id': 'temp-session-id'
        },
        timeout: this.timeout
      }), {
        maxRetries: config.system.maxRetries,
        shouldRetry: (error) => {
          // Reintentar en caso de errores de red o timeout
          return error.code === 'ECONNREFUSED' || 
                 error.code === 'ETIMEDOUT' || 
                 error.code === 'ECONNRESET';
        }
      });
      
      // Verificar la respuesta
      if (!response.data || response.data.error) {
        throw new McpServerError(
          `Error al crear sesión en servidor MCP ${this.serverName}`,
          { error: response.data?.error || 'Respuesta vacía' }
        );
      }
      
      // Guardar el ID de sesión
      this.sessionId = response.data.result.sessionId;
      logger.debug('BaseMcpAdapter', `Creada sesión en servidor MCP ${this.serverName} con ID ${this.sessionId}`);
      
      return this.sessionId;
    } catch (error) {
      throw new McpServerError(
        `Error al crear sesión en servidor MCP ${this.serverName}`,
        { originalError: error.message },
        error
      );
    }
  }
  
  /**
   * Ejecuta una herramienta en el servidor MCP
   * @param {string} toolName - Nombre de la herramienta
   * @param {Object} input - Parámetros de entrada
   * @returns {Promise<any>} Resultado de la ejecución
   */
  async executeTool(toolName, input = {}) {
    try {
      logger.debug('BaseMcpAdapter', `Ejecutando herramienta ${toolName} en servidor MCP ${this.serverName}`, 
        sanitizeForLogging(input));
      
      // Verificar que la herramienta esté en las capacidades del servidor
      if (this.capabilities.length > 0 && !this.capabilities.includes(toolName)) {
        throw new McpServerError(
          `La herramienta ${toolName} no está disponible en el servidor MCP ${this.serverName}`,
          { availableTools: this.capabilities }
        );
      }
      
      // Si no hay sesión, crearla
      if (!this.sessionId) {
        await this.createSession();
      }
      
      // Si el servidor es interno, manejar internamente
      if (this.baseUrl === 'internal') {
        return await this._executeInternalTool(toolName, input);
      }
      
      // Ejecutar la herramienta en el servidor MCP
      const response = await withRetry(() => axios.post(`${this.baseUrl}/mcp`, {
        jsonrpc: '2.0',
        method: 'execute',
        params: {
          tool: toolName,
          input: input
        },
        id: 2
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Mcp-Session-Id': this.sessionId
        },
        timeout: this.timeout
      }), {
        maxRetries: config.system.maxRetries
      });
      
      // Verificar la respuesta
      if (!response.data || response.data.error) {
        throw new McpServerError(
          `Error al ejecutar herramienta ${toolName} en servidor MCP ${this.serverName}`,
          { error: response.data?.error || 'Respuesta vacía' }
        );
      }
      
      logger.debug('BaseMcpAdapter', `Herramienta ${toolName} ejecutada correctamente en servidor MCP ${this.serverName}`);
      
      return response.data.result;
    } catch (error) {
      throw new McpServerError(
        `Error al ejecutar herramienta ${toolName} en servidor MCP ${this.serverName}`,
        { originalError: error.message, input: sanitizeForLogging(input) },
        error
      );
    }
  }
  
  /**
   * Ejecuta una herramienta interna (para servidores MCP simulados)
   * @param {string} toolName - Nombre de la herramienta
   * @param {Object} input - Parámetros de entrada
   * @returns {Promise<any>} Resultado de la ejecución
   * @private
   */
  async _executeInternalTool(toolName, input) {
    // Esta función debe ser implementada por las clases derivadas
    throw new McpServerError(
      `La herramienta ${toolName} no está implementada para el servidor MCP interno ${this.serverName}`
    );
  }
  
  /**
   * Cierra la sesión en el servidor MCP
   * @returns {Promise<void>}
   */
  async closeSession() {
    if (!this.sessionId) {
      return;
    }
    
    try {
      logger.debug('BaseMcpAdapter', `Cerrando sesión en servidor MCP ${this.serverName}`);
      
      // Si el servidor es interno, simplemente limpiar el ID de sesión
      if (this.baseUrl === 'internal') {
        this.sessionId = null;
        return;
      }
      
      // Cerrar sesión en el servidor MCP
      await axios.post(`${this.baseUrl}/mcp`, {
        jsonrpc: '2.0',
        method: 'session.close',
        params: {},
        id: 3
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Mcp-Session-Id': this.sessionId
        },
        timeout: this.timeout
      });
      
      logger.debug('BaseMcpAdapter', `Sesión cerrada en servidor MCP ${this.serverName}`);
    } catch (error) {
      logger.warn('BaseMcpAdapter', `Error al cerrar sesión en servidor MCP ${this.serverName}`, {
        error: error.message
      });
    } finally {
      this.sessionId = null;
    }
  }
}

module.exports = BaseMcpAdapter;
