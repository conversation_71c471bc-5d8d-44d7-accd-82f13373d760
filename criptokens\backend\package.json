{"name": "criptokens-backend", "version": "0.1.0", "description": "Backend para la aplicación Criptokens con agente de IA conversacional", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "setup-firebase": "node scripts/generate-dev-credentials.js"}, "keywords": ["ai", "chatbot", "streaming", "express", "api"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.11.1", "openai": "^4.95.1", "string-similarity": "^4.0.4"}, "devDependencies": {"nodemon": "^3.0.1"}}