:root {
  /* Paleta de colores futurista */
  --primary: #00f2ff;
  --primary-glow: rgba(0, 242, 255, 0.5);
  --primary-dark: #00c2cc;
  --secondary: #7b4dff;
  --secondary-glow: rgba(123, 77, 255, 0.5);
  --accent: #ff2a6d;
  --accent-glow: rgba(255, 42, 109, 0.5);
  --success: #05ffa1;
  --warning: #ffb800;
  --error: #ff2a6d;
  
  /* Fondos */
  --bg-darkest: #0a0a1a;
  --bg-dark: #12122a;
  --bg-medium: #1a1a40;
  --bg-light: #252550;
  
  /* Texto */
  --text-bright: #ffffff;
  --text-medium: #d0d0ff;
  --text-dim: #8080b0;
  
  /* Gradientes */
  --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
  --gradient-accent: linear-gradient(135deg, var(--accent), var(--secondary));
  --gradient-card: linear-gradient(135deg, rgba(26, 26, 64, 0.7), rgba(18, 18, 42, 0.7));
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  
  /* Efectos */
  --glow-sm: 0 0 10px;
  --glow-md: 0 0 20px;
  --glow-lg: 0 0 30px;
  
  /* Bordes */
  --border-light: 1px solid rgba(255, 255, 255, 0.1);
  --border-medium: 1px solid rgba(255, 255, 255, 0.15);
  --border-glow: 1px solid rgba(0, 242, 255, 0.3);
  
  /* Sombras */
  --shadow-sm: 0 4px 12px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 8px 24px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 12px 36px rgba(0, 0, 0, 0.4);
  
  /* Bordes redondeados */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;
  
  /* Espaciado */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  
  /* Transiciones */
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index */
  --z-base: 1;
  --z-above: 10;
  --z-modal: 100;
  --z-overlay: 1000;
}

/* Estilos base */
body {
  background-color: var(--bg-darkest);
  color: var(--text-medium);
  font-family: 'Inter', 'Roboto', system-ui, sans-serif;
  margin: 0;
  padding: 0;
  line-height: 1.5;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-dark);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Tipografía */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-bright);
  font-weight: 600;
  line-height: 1.2;
  margin-top: 0;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: var(--space-lg);
}

h2 {
  font-size: 2rem;
  margin-bottom: var(--space-md);
}

h3 {
  font-size: 1.5rem;
  margin-bottom: var(--space-md);
}

p {
  margin-top: 0;
  margin-bottom: var(--space-md);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--text-bright);
}

/* Botones */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-lg);
  background: var(--gradient-primary);
  color: var(--text-bright);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 15px var(--primary-glow);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: var(--transition-normal);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px var(--primary-glow);
}

.btn:hover::before {
  left: 100%;
}

.btn-secondary {
  background: var(--gradient-glass);
  border: var(--border-light);
  box-shadow: none;
}

.btn-secondary:hover {
  border: var(--border-glow);
  box-shadow: 0 0 15px var(--primary-glow);
}

.btn-accent {
  background: var(--gradient-accent);
  box-shadow: 0 0 15px var(--accent-glow);
}

.btn-accent:hover {
  box-shadow: 0 0 20px var(--accent-glow);
}

/* Tarjetas */
.card {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: var(--border-light);
  padding: var(--space-lg);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
  opacity: 0.5;
}

.card:hover {
  transform: translateY(-5px);
  border: var(--border-glow);
  box-shadow: 0 10px 30px rgba(0, 242, 255, 0.15);
}

/* Efectos de texto */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.text-glow {
  text-shadow: 0 0 10px var(--primary-glow);
}

.text-accent {
  color: var(--accent);
}

/* Inputs */
.input {
  background: rgba(10, 10, 26, 0.6);
  border: var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-bright);
  padding: var(--space-sm) var(--space-md);
  width: 100%;
  transition: all var(--transition-normal);
}

.input:focus {
  outline: none;
  border: var(--border-glow);
  box-shadow: 0 0 15px var(--primary-glow);
}

/* Animaciones */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 10s linear infinite;
}

/* Efectos de fondo */
.bg-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(123, 77, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(123, 77, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: -1;
  pointer-events: none;
}

.bg-glow {
  position: fixed;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60vw;
  height: 60vh;
  background: radial-gradient(
    circle,
    rgba(0, 242, 255, 0.1) 0%,
    rgba(123, 77, 255, 0.1) 50%,
    transparent 70%
  );
  filter: blur(60px);
  z-index: -1;
  pointer-events: none;
}

/* Utilidades */
.glass {
  background: var(--gradient-glass);
  backdrop-filter: blur(10px);
  border: var(--border-light);
  border-radius: var(--radius-md);
}

.glow {
  box-shadow: 0 0 20px var(--primary-glow);
}

.border-glow {
  border: var(--border-glow);
}

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }

.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

/* Responsive */
@media (max-width: 768px) {
  :root {
    --space-lg: 16px;
    --space-xl: 24px;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.25rem; }
  
  .card { padding: var(--space-md); }
}
