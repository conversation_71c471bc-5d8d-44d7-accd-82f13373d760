/* ===== FUTURISTIC DASHBOARD STYLES ===== */

.futuristic-dashboard {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
  background-color: var(--bg-darkest);
  color: var(--text-medium);
  position: relative;
  overflow: hidden;
}

/* Background effects */
.bg-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(123, 77, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(123, 77, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 0;
  opacity: 0.2;
  pointer-events: none;
}

/* Sidebar */
.dashboard-sidebar {
  background: rgba(18, 18, 42, 0.7);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(123, 77, 255, 0.2);
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  z-index: 10;
  box-shadow: 5px 0 20px rgba(0, 0, 0, 0.2);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 40px;
  padding: 0 12px;
}

.logo-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-core {
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 50%;
  box-shadow: 0 0 15px var(--primary-glow);
}

.logo-ring {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: var(--primary);
  border-left-color: var(--secondary);
  animation: rotate 10s linear infinite;
}

.logo-text {
  font-size: 24px;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 40px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: var(--radius-md);
  background: transparent;
  border: none;
  color: var(--text-dim);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: left;
}

.nav-item:hover {
  background: rgba(123, 77, 255, 0.1);
  color: var(--text-bright);
}

.nav-item.active {
  background: var(--gradient-glass);
  color: var(--text-bright);
  border: 1px solid rgba(123, 77, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-icon {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

/* Agent container */
.agent-container {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: rgba(10, 10, 26, 0.5);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(0, 242, 255, 0.2);
}

.agent-message {
  margin-top: 16px;
  padding: 12px;
  background: rgba(18, 18, 42, 0.8);
  border-radius: var(--radius-md);
  border: 1px solid rgba(0, 242, 255, 0.2);
  width: 100%;
  animation: fadeIn 0.3s ease-out;
}

.agent-message p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-medium);
}

/* Main content */
.dashboard-main {
  padding: 24px;
  overflow-y: auto;
  z-index: 1;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.dashboard-header h2 {
  font-size: 28px;
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-bar {
  display: flex;
  align-items: center;
  background: rgba(18, 18, 42, 0.7);
  border-radius: var(--radius-full);
  border: 1px solid rgba(123, 77, 255, 0.3);
  padding: 8px 16px;
  transition: all var(--transition-normal);
}

.search-bar:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 15px var(--primary-glow);
}

.search-bar input {
  background: transparent;
  border: none;
  color: var(--text-bright);
  font-size: 14px;
  width: 200px;
  outline: none;
}

.search-button {
  background: transparent;
  border: none;
  color: var(--text-dim);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.search-button:hover {
  color: var(--primary);
}

.refresh-button {
  background: rgba(18, 18, 42, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.3);
  border-radius: var(--radius-full);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-dim);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.refresh-button:hover {
  background: rgba(123, 77, 255, 0.2);
  color: var(--text-bright);
  transform: rotate(180deg);
}

/* Market stats */
.market-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.market-stat-card {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 1px solid rgba(123, 77, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.market-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  opacity: 0.7;
}

.market-stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 242, 255, 0.4);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: rgba(10, 10, 26, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.3);
}

.market-cap-icon::before {
  content: '📊';
}

.volume-icon::before {
  content: '📈';
}

.btc-icon::before {
  content: '₿';
  color: #f7931a;
}

.eth-icon::before {
  content: 'Ξ';
  color: #627eea;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 14px;
  margin: 0 0 8px;
  color: var(--text-dim);
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: var(--text-bright);
}

.stat-change {
  font-size: 14px;
  font-weight: 500;
  margin: 4px 0 0;
}

.stat-change.positive {
  color: var(--success);
}

.stat-change.negative {
  color: var(--error);
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full);
  margin-top: 8px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, #f7931a, #ffc107);
  border-radius: var(--radius-full);
}

.eth-progress {
  background: linear-gradient(90deg, #627eea, #3c5be0);
}

/* Dashboard content */
.dashboard-main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

/* Chart section */
.chart-section {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(123, 77, 255, 0.2);
  padding: 20px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.chart-section:hover {
  border-color: rgba(0, 242, 255, 0.4);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-title img {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
}

.chart-title h3 {
  margin: 0;
  font-size: 18px;
  color: var(--text-bright);
}

.symbol {
  color: var(--text-dim);
  font-weight: normal;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-bright);
}

.price-change {
  font-size: 14px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: var(--radius-full);
}

.price-change.positive {
  background: rgba(5, 255, 161, 0.2);
  color: var(--success);
}

.price-change.negative {
  background: rgba(255, 42, 109, 0.2);
  color: var(--error);
}

.chart-period {
  display: flex;
  gap: 8px;
}

.period-button {
  background: rgba(10, 10, 26, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.2);
  border-radius: var(--radius-md);
  padding: 6px 12px;
  font-size: 12px;
  color: var(--text-dim);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.period-button:hover {
  background: rgba(123, 77, 255, 0.1);
  color: var(--text-bright);
}

.period-button.active {
  background: rgba(0, 242, 255, 0.1);
  border-color: rgba(0, 242, 255, 0.4);
  color: var(--primary);
}

.chart-container {
  position: relative;
}

.chart-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.stat-item {
  background: rgba(10, 10, 26, 0.5);
  border-radius: var(--radius-md);
  padding: 12px;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: var(--text-dim);
  display: block;
  margin-bottom: 4px;
}

/* Crypto list */
.crypto-list-section {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(123, 77, 255, 0.2);
  padding: 20px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.crypto-list-section:hover {
  border-color: rgba(0, 242, 255, 0.4);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.crypto-list-section h3 {
  margin: 0 0 16px;
  font-size: 18px;
  color: var(--text-bright);
}

.crypto-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.crypto-card {
  background: rgba(10, 10, 26, 0.5);
  border-radius: var(--radius-md);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid transparent;
}

.crypto-card:hover {
  background: rgba(123, 77, 255, 0.1);
  transform: translateX(5px);
}

.crypto-card.selected {
  background: rgba(0, 242, 255, 0.1);
  border-color: rgba(0, 242, 255, 0.4);
  box-shadow: 0 0 15px rgba(0, 242, 255, 0.2);
}

.crypto-icon {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  border-radius: var(--radius-full);
  vertical-align: middle !important;
  object-fit: contain !important;
}

.crypto-info {
  flex: 1;
}

.crypto-info h4 {
  margin: 0;
  font-size: 16px;
  color: var(--text-bright);
}

.crypto-name {
  margin: 4px 0 0;
  font-size: 12px;
  color: var(--text-dim);
}

.crypto-price {
  text-align: right;
}

.price-value {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-bright);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 242, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Portfolio styles */
.portfolio-content {
  padding: 20px;
}

.portfolio-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.summary-card {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 1px solid rgba(123, 77, 255, 0.2);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.summary-card h3 {
  font-size: 14px;
  margin: 0 0 8px;
  color: var(--text-dim);
}

.summary-value {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: var(--text-bright);
}

.summary-value.positive {
  color: var(--success);
}

.summary-value.negative {
  color: var(--error);
}

.portfolio-assets {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(123, 77, 255, 0.2);
  padding: 20px;
  box-shadow: var(--shadow-md);
}

.asset-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid rgba(123, 77, 255, 0.1);
  transition: all var(--transition-normal);
}

.asset-card:last-child {
  border-bottom: none;
}

.asset-card:hover {
  background: rgba(123, 77, 255, 0.05);
}

.asset-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
}

.asset-info {
  flex: 1;
}

.asset-info h4 {
  margin: 0;
  font-size: 16px;
  color: var(--text-bright);
}

.asset-amount {
  margin: 4px 0 0;
  font-size: 14px;
  color: var(--text-dim);
}

.asset-value {
  text-align: right;
}

.asset-value .value {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-bright);
}

.asset-value .change {
  margin: 4px 0 0;
  font-size: 14px;
}

.add-asset-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(5, 255, 161, 0.1);
  border: 1px solid rgba(5, 255, 161, 0.3);
  border-radius: var(--radius-full);
  padding: 8px 16px;
  color: var(--success);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.add-asset-button:hover {
  background: rgba(5, 255, 161, 0.2);
  transform: translateY(-2px);
}

.add-icon {
  font-size: 16px;
  font-weight: bold;
}

/* News styles */
.news-content {
  padding: 20px;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news-card {
  display: flex;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(123, 77, 255, 0.2);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.news-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 242, 255, 0.4);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.news-image {
  width: 200px;
  background-size: cover;
  background-position: center;
}

.news-content {
  padding: 20px;
  flex: 1;
}

.news-source {
  display: inline-block;
  font-size: 12px;
  color: var(--primary);
  margin-bottom: 8px;
}

.news-title {
  margin: 0 0 12px;
  font-size: 18px;
  color: var(--text-bright);
}

.news-excerpt {
  margin: 0 0 16px;
  font-size: 14px;
  color: var(--text-medium);
  line-height: 1.5;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-time {
  font-size: 12px;
  color: var(--text-dim);
}

.news-link {
  font-size: 14px;
  color: var(--primary);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.news-link:hover {
  color: var(--secondary);
  text-decoration: underline;
}

/* Chat styles */
.chat-content {
  padding: 20px;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(123, 77, 255, 0.2);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  animation: fadeIn 0.3s ease-out;
}

.ai-message {
  align-self: flex-start;
  background: rgba(123, 77, 255, 0.1);
  border: 1px solid rgba(123, 77, 255, 0.2);
}

.user-message {
  align-self: flex-end;
  background: rgba(0, 242, 255, 0.1);
  border: 1px solid rgba(0, 242, 255, 0.2);
}

.message p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-bright);
}

.chat-input {
  display: flex;
  padding: 16px;
  background: rgba(10, 10, 26, 0.7);
  border-top: 1px solid rgba(123, 77, 255, 0.2);
}

.chat-input input {
  flex: 1;
  background: rgba(18, 18, 42, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.3);
  border-radius: var(--radius-full);
  padding: 12px 16px;
  color: var(--text-bright);
  font-size: 14px;
  outline: none;
  transition: all var(--transition-normal);
}

.chat-input input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 15px var(--primary-glow);
}

.send-button {
  width: 40px;
  height: 40px;
  margin-left: 12px;
  background: var(--gradient-glass);
  border: 1px solid rgba(0, 242, 255, 0.3);
  border-radius: var(--radius-full);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.send-button:hover {
  background: rgba(0, 242, 255, 0.2);
  transform: scale(1.05);
}

/* MCP styles */
.mcp-content {
  padding: 20px;
}

.mcp-container {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(123, 77, 255, 0.2);
  padding: 24px;
  box-shadow: var(--shadow-md);
  display: grid;
  gap: 24px;
}

.mcp-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(10, 10, 26, 0.5);
  border-radius: var(--radius-lg);
  padding: 16px;
  border: 1px solid rgba(123, 77, 255, 0.2);
}

.mcp-info h3 {
  margin: 0 0 12px;
  font-size: 18px;
  color: var(--text-bright);
}

.mcp-info p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-medium);
}

.mcp-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.mcp-button {
  background: rgba(10, 10, 26, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.3);
  border-radius: var(--radius-md);
  padding: 12px 20px;
  color: var(--text-bright);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.mcp-button:hover {
  background: rgba(123, 77, 255, 0.1);
  border-color: rgba(123, 77, 255, 0.5);
  transform: translateY(-2px);
}

.mcp-button.primary {
  background: linear-gradient(45deg, var(--primary), var(--secondary));
  color: var(--text-bright);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 242, 255, 0.3);
}

.mcp-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 242, 255, 0.4);
}

.mcp-offline-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  background: rgba(10, 10, 26, 0.5);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 42, 109, 0.2);
  min-height: 300px;
}

.mcp-offline-message h3 {
  font-size: 24px;
  margin: 0 0 16px;
  color: var(--error);
}

.mcp-offline-message p {
  color: var(--text-medium);
  margin: 0 0 24px;
  max-width: 400px;
}

.mcp-status h3 {
  margin: 0 0 12px;
  font-size: 18px;
  color: var(--text-bright);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: var(--radius-md);
  background: rgba(10, 10, 26, 0.5);
  width: fit-content;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  background: var(--error);
}

.status-indicator.online .status-dot {
  background: var(--success);
  box-shadow: 0 0 10px var(--success);
  animation: pulse 2s infinite;
}

.status-indicator.offline .status-dot {
  background: var(--error);
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-medium);
}

.status-indicator.online .status-text {
  color: var(--success);
}

.status-indicator.offline .status-text {
  color: var(--error);
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(5, 255, 161, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(5, 255, 161, 0); }
  100% { box-shadow: 0 0 0 0 rgba(5, 255, 161, 0); }
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard-main-content {
    grid-template-columns: 1fr;
  }

  .news-card {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 200px;
  }
}

@media (max-width: 992px) {
  .futuristic-dashboard {
    grid-template-columns: 1fr;
  }

  .dashboard-sidebar {
    display: none;
  }

  .portfolio-summary {
    grid-template-columns: 1fr;
  }

  .mcp-actions {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .market-stats {
    grid-template-columns: 1fr;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart-stats {
    grid-template-columns: 1fr 1fr;
  }

  .message {
    max-width: 90%;
  }
}
