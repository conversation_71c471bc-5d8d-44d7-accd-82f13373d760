const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Verificar si existe el directorio dist
if (!fs.existsSync(path.join(__dirname, 'dist'))) {
  fs.mkdirSync(path.join(__dirname, 'dist'));
}

// Compilar el proyecto
console.log('Compilando el proyecto...');
try {
  execSync('npx tsc', { stdio: 'inherit' });
  console.log('Compilación completada con éxito.');
} catch (error) {
  console.error('Error al compilar el proyecto:', error);
  process.exit(1);
}

// Añadir shebang a index.js
console.log('Añadiendo shebang a server.js...');
try {
  const serverJsPath = path.join(__dirname, 'dist', 'server.js');
  let content = fs.readFileSync(serverJsPath, 'utf8');
  
  if (!content.startsWith('#!/usr/bin/env node')) {
    content = '#!/usr/bin/env node\n' + content;
    fs.writeFileSync(serverJsPath, content);
  }
  
  console.log('Shebang añadido con éxito.');
} catch (error) {
  console.error('Error al añadir shebang:', error);
  process.exit(1);
}

// Ejecutar el servidor
console.log('Iniciando el servidor MCP Orchestrator...');
try {
  execSync('node dist/server.js', { stdio: 'inherit' });
} catch (error) {
  console.error('Error al iniciar el servidor:', error);
  process.exit(1);
}
