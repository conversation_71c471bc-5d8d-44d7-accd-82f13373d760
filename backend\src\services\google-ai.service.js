/**
 * Google AI Service
 *
 * Este servicio proporciona funciones para interactuar con la API de Google AI
 * y simular las capacidades de los agentes ADK.
 */

const axios = require('axios');
require('dotenv').config();

// Configuración de Google AI
const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY || 'AIzaSyCashyNX3fNv-bj5xOCtTDMfL_-vGm9Yag';
const GOOGLE_AI_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

/**
 * Envía una solicitud a Google AI
 * @param {Array} messages - Los mensajes de la conversación
 * @param {Object} options - Opciones adicionales
 * @returns {Promise<Object>} - La respuesta de Google AI
 */
async function callGoogleAI(messages, options = {}) {
  try {
    // Convertir mensajes de formato OpenAI a formato Google
    const contents = [];

    for (const msg of messages) {
      if (msg.role === 'system') {
        // Para mensajes de sistema, usamos un mensaje de usuario con un prefijo especial
        contents.push({
          role: 'user',
          parts: [{ text: `<system>${msg.content}</system>` }]
        });
      } else {
        contents.push({
          role: msg.role === 'assistant' ? 'model' : msg.role,
          parts: [{ text: msg.content }]
        });
      }
    }

    const response = await axios.post(
      `${GOOGLE_AI_URL}?key=${GOOGLE_API_KEY}`,
      {
        contents,
        generationConfig: {
          maxOutputTokens: 500,
          temperature: 0.7,
          topP: 0.95,
          topK: 40,
          ...options
        }
      }
    );

    // Convertir respuesta de Google a formato OpenAI
    return {
      choices: [
        {
          message: {
            role: 'assistant',
            content: response.data.candidates[0].content.parts[0].text
          }
        }
      ]
    };
  } catch (error) {
    console.error('Error calling Google AI:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Simula el agente de análisis técnico
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function technicalAnalysisAgent(query) {
  const systemPrompt = `
    You are a cryptocurrency technical analysis expert. Your task is to:

    1. Analyze technical indicators for the cryptocurrency mentioned in the query
    2. Identify the current trend (bullish, bearish, neutral)
    3. Explain what the indicators suggest about future price movements
    4. Highlight key support and resistance levels
    5. Provide a clear, concise technical analysis summary

    When responding:
    - Be specific about what the indicators mean
    - Explain the significance of moving averages, RSI, and price changes
    - Mention support and resistance levels
    - Provide a conclusion about the overall technical outlook
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callGoogleAI(messages);
}

/**
 * Simula el agente de análisis de sentimiento
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function sentimentAnalysisAgent(query) {
  const systemPrompt = `
    You are a cryptocurrency sentiment analysis expert. Your task is to:

    1. Analyze the market sentiment for the cryptocurrency mentioned in the query
    2. Explain what the news sentiment indicates about market perception
    3. Interpret the social media sentiment and its implications
    4. Explain the Fear & Greed Index and its significance
    5. Provide a clear, concise sentiment analysis summary

    When responding:
    - Be specific about what the sentiment metrics mean
    - Explain the significance of the Fear & Greed Index
    - Highlight any discrepancies between news and social media sentiment
    - Provide a conclusion about the overall market sentiment
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callGoogleAI(messages);
}

/**
 * Simula el agente de análisis on-chain
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function onchainAnalysisAgent(query) {
  const systemPrompt = `
    You are a blockchain and on-chain data analysis expert. Your task is to:

    1. Analyze on-chain data for the cryptocurrency mentioned in the query
    2. Explain what the whale activity indicates about large holder behavior
    3. Interpret the gas prices and their implications for network activity
    4. Identify significant patterns in token transfers
    5. Provide a clear, concise on-chain analysis summary

    When responding:
    - Be specific about what the on-chain metrics mean
    - Explain the significance of whale accumulation or distribution
    - Highlight any unusual patterns in the data
    - Provide a conclusion about what the on-chain data suggests for the token
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callGoogleAI(messages);
}

/**
 * Simula el agente Guru Cripto
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function guruCriptoAgent(query) {
  const systemPrompt = `
    You are Guru Cripto, a cryptocurrency expert with deep knowledge of technical analysis, market sentiment, and on-chain metrics.

    Your capabilities include:
    1. Providing technical analysis of cryptocurrency price movements
    2. Analyzing market sentiment from news and social media
    3. Interpreting on-chain data and blockchain metrics
    4. Making informed price predictions based on comprehensive analysis
    5. Explaining complex cryptocurrency concepts in simple terms

    When responding to queries:
    - Be clear, concise, and informative
    - Support your analysis with specific data points
    - Explain technical terms when necessary
    - Provide balanced perspectives, acknowledging both bullish and bearish factors
    - For price predictions, emphasize that they are estimates based on current data, not guarantees

    Remember that you are an educational resource, not a financial advisor. Always remind users to do their own research and not to make investment decisions solely based on your analysis.
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callGoogleAI(messages);
}

/**
 * Genera un análisis completo para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para el análisis
 * @returns {Promise<Object>} - El análisis completo
 */
async function generateComprehensiveAnalysis(cryptoName, timeframe) {
  const query = `Generate a comprehensive analysis for ${cryptoName} over the last ${timeframe}. Include technical analysis, sentiment analysis, and on-chain analysis.`;

  const response = await guruCriptoAgent(query);

  return {
    cryptoName,
    timeframe,
    analysis: response,
    timestamp: new Date().toISOString()
  };
}

/**
 * Genera una predicción de precio para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para la predicción
 * @returns {Promise<Object>} - La predicción de precio
 */
async function generatePricePrediction(cryptoName, timeframe) {
  const query = `What's your prediction for ${cryptoName} price in the next ${timeframe}? Base your prediction on technical analysis, sentiment analysis, and on-chain data.`;

  const response = await guruCriptoAgent(query);

  return {
    cryptoName,
    timeframe,
    prediction: response,
    timestamp: new Date().toISOString()
  };
}

module.exports = {
  technicalAnalysisAgent,
  sentimentAnalysisAgent,
  onchainAnalysisAgent,
  guruCriptoAgent,
  generateComprehensiveAnalysis,
  generatePricePrediction
};
