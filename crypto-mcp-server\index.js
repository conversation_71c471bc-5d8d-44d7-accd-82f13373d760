import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';

// Importar las funciones del servidor HTTP
import {
  getCryptoPrice,
  getTopCryptocurrencies,
  getCryptoHistoricalData
} from './http-server.js';

// Crear un router de Express para el servidor MCP
const router = express.Router();

// Configurar las rutas para las herramientas MCP
router.post('/tools/getCryptoPrice', async (req, res) => {
  try {
    const { cryptoId } = req.body;
    if (!cryptoId) {
      return res.status(400).json({
        content: [{ type: 'text', text: 'El parámetro cryptoId es obligatorio' }]
      });
    }
    const data = await getCryptoPrice(cryptoId.toLowerCase());
    return res.json({
      content: [{ type: 'text', text: JSON.stringify(data, null, 2) }]
    });
  } catch (error) {
    console.error(`<PERSON>rror al obtener el precio:`, error);
    return res.status(500).json({
      content: [{ type: 'text', text: `Error al obtener el precio: ${error.message}` }]
    });
  }
});

router.post('/tools/getTopCryptocurrencies', async (req, res) => {
  try {
    const { limit = 10, page = 1 } = req.body;
    const data = await getTopCryptocurrencies(limit, page);
    return res.json({
      content: [{ type: 'text', text: JSON.stringify(data, null, 2) }]
    });
  } catch (error) {
    console.error(`Error al obtener las principales criptomonedas:`, error);
    return res.status(500).json({
      content: [{ type: 'text', text: `Error al obtener las principales criptomonedas: ${error.message}` }]
    });
  }
});

router.post('/tools/getCryptoHistoricalData', async (req, res) => {
  try {
    const { cryptoId, days = 7 } = req.body;
    if (!cryptoId) {
      return res.status(400).json({
        content: [{ type: 'text', text: 'El parámetro cryptoId es obligatorio' }]
      });
    }
    const data = await getCryptoHistoricalData(cryptoId.toLowerCase(), days);
    return res.json({
      content: [{ type: 'text', text: JSON.stringify(data, null, 2) }]
    });
  } catch (error) {
    console.error(`Error al obtener datos históricos:`, error);
    return res.status(500).json({
      content: [{ type: 'text', text: `Error al obtener datos históricos: ${error.message}` }]
    });
  }
});

// Obtener la lista de herramientas disponibles
router.get('/tools', (req, res) => {
  const tools = [
    {
      name: 'getCryptoPrice',
      description: 'Obtener el precio y detalles de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)'
      }
    },
    {
      name: 'getTopCryptocurrencies',
      description: 'Obtener las principales criptomonedas por capitalización de mercado',
      parameters: {
        limit: 'Número de criptomonedas a obtener (1-100, por defecto 10)',
        page: 'Página de resultados (por defecto 1)'
      }
    },
    {
      name: 'getCryptoHistoricalData',
      description: 'Obtener datos históricos de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)',
        days: 'Número de días de datos históricos (1-365, por defecto 7)'
      }
    }
  ];

  res.json({
    status: 'success',
    tools
  });
});

// Configurar el puerto
const PORT = process.env.PORT || 3200;

// Crear la aplicación Express
const app = express();

// Configurar CORS
app.use(cors());

// Configurar middleware para parsear JSON
app.use(express.json());

// Montar el router MCP en la ruta /mcp
app.use('/mcp', router);

// Iniciar el servidor Express
app.listen(PORT, () => {
  console.log(`Servidor MCP iniciado en http://localhost:${PORT}/mcp`);
});
