/**
 * Configuración global para el backend de Criptokens
 */

module.exports = {
  // Configuración de la API de Ultravox
  ultravox: {
    apiUrl: 'https://api.ultravox.ai/api',
    defaultVoiceId: 'en-US-Neural2-F',
    spanishVoiceId: 'es-ES-Neural2-A'
  },
  
  // Configuración de CORS
  cors: {
    origins: ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:3001'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: false
  },
  
  // Configuración de puertos
  ports: {
    backend: 3001,
    cryptoMcp: 3101,
    braveMcp: 3102,
    playwrightMcp: 3103
  },
  
  // Configuración de API keys (para desarrollo)
  apiKeys: {
    ultravox: process.env.ULTRAVOX_API_KEY || '',
    openRouter: process.env.OPENROUTER_API_KEY || '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',
    coinMarketCap: process.env.COINMARKETCAP_API_KEY || '37f9968e-6ab7-431f-80d7-0ac6686319f3',
    braveSearch: process.env.BRAVE_API_KEY || 'BSAccS820UUfffNOAD7yLACz9htlbe9',
    etherscan: process.env.ETHERSCAN_API_KEY || '**********************************'
  }
};
