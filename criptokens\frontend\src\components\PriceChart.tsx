import { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { getCryptoHistoricalData } from '../services/api';
import '../styles/PriceChart.css';

// Registrar los componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface PriceChartProps {
  data?: any;
  timeRange: string;
  priceChange: number;
  cryptoId?: string;
  name?: string;
  color?: string;
}

const PriceChart = ({ data, timeRange, priceChange, cryptoId, name = 'Cryptocurrency', color = '#0084ff' }: PriceChartProps) => {
  const [chartData, setChartData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setIsLoading(true);
    setError(null);

    try {
      if (data && data.prices) {
        // Formatear los datos para Chart.js
        const prices = data.prices;
        const labels = prices.map((price: [number, number]) => {
          const date = new Date(price[0]);
          // Formatear la fecha según el rango de tiempo
          if (timeRange === '24h') {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
          }
        });

        const priceValues = prices.map((price: [number, number]) => price[1]);

        setChartData({
          labels,
          datasets: [
            {
              label: `${name} Price (USD)`,
              data: priceValues,
              borderColor: priceChange >= 0 ? '#16c784' : '#ea3943',
              backgroundColor: `${priceChange >= 0 ? '#16c78420' : '#ea394320'}`,
              borderWidth: 2,
              pointRadius: 0,
              pointHoverRadius: 4,
              tension: 0.4,
              fill: true,
            },
          ],
        });
      } else if (cryptoId) {
        // Si no hay datos pero sí hay cryptoId, intentar obtenerlos
        const fetchData = async () => {
          try {
            // Convertir timeRange a número de días
            let days = 7;
            if (timeRange === '24h') days = 1;
            else if (timeRange === '7d') days = 7;
            else if (timeRange === '14d') days = 14;
            else if (timeRange === '30d') days = 30;
            else if (timeRange === '90d') days = 90;
            else if (timeRange === '1y') days = 365;
            else if (timeRange === 'max') days = 'max';

            const result = await getCryptoHistoricalData(cryptoId, days);

            if (result && result.prices) {
              const prices = result.prices;
              const labels = prices.map((price: [number, number]) => {
                const date = new Date(price[0]);
                return days === 1
                  ? date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                  : date.toLocaleDateString([], { month: 'short', day: 'numeric' });
              });

              const priceValues = prices.map((price: [number, number]) => price[1]);

              setChartData({
                labels,
                datasets: [
                  {
                    label: `${name} Price (USD)`,
                    data: priceValues,
                    borderColor: priceChange >= 0 ? '#16c784' : '#ea3943',
                    backgroundColor: `${priceChange >= 0 ? '#16c78420' : '#ea394320'}`,
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 4,
                    tension: 0.4,
                    fill: true,
                  },
                ],
              });
            }
          } catch (err) {
            console.error('Error fetching historical data:', err);
            setError('Failed to load chart data. Please try again later.');
          }
        };

        fetchData();
      } else {
        setError('No data available for chart');
      }
    } catch (err) {
      console.error('Error processing chart data:', err);
      setError('Failed to process chart data. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [data, timeRange, cryptoId, name, priceChange]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function(context: any) {
            return `$${context.parsed.y.toFixed(2)}`;
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          maxRotation: 0,
          autoSkip: true,
          maxTicksLimit: 8,
        }
      },
      y: {
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          callback: function(value: any) {
            return '$' + value.toLocaleString();
          }
        }
      }
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    elements: {
      point: {
        radius: 0,
      }
    },
  };

  // Ya no necesitamos esta función porque el timeRange viene como prop
  // const handleTimeRangeChange = (days: number) => {
  //   setTimeRange(days);
  // };

  if (isLoading) {
    return <div className="price-chart-loading">Loading chart data...</div>;
  }

  if (error) {
    return <div className="price-chart-error">{error}</div>;
  }

  return (
    <div className="price-chart-container">
      <div className="price-chart">
        {chartData && <Line data={chartData} options={chartOptions} height={300} />}
      </div>
    </div>
  );
};

export default PriceChart;
