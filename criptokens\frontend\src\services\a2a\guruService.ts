/**
 * Service for interacting with the Guru Cripto A2A agent
 */
import axios from 'axios';
import { API_URLS } from '../../config/api.config';
import { v4 as uuidv4 } from 'uuid';

// Interface for prediction request
export interface PredictionRequest {
  query: string;
  sessionId?: string;
}

// Interface for prediction response
export interface PredictionResponse {
  query: string;
  crypto: string;
  timeframe: string;
  technical_analysis?: any;
  sentiment_analysis?: any;
  onchain_analysis?: any;
  prediction: {
    summary: string;
    detailed_analysis: {
      technical_factors: string;
      sentiment_factors: string;
      on_chain_factors: string;
      market_conditions: string;
    };
    price_prediction: {
      direction: 'up' | 'down' | 'sideways';
      confidence: number;
      percentage_range: string;
      key_levels: {
        support: number[];
        resistance: number[];
      };
    };
    risks: string[];
    opportunities: string[];
    recommendation: 'buy' | 'sell' | 'hold' | 'wait';
    context?: any;
  };
}

/**
 * Get a prediction from the Guru Cripto A2A agent
 * @param request Prediction request
 * @returns Prediction response
 */
export const getPrediction = async (request: PredictionRequest): Promise<PredictionResponse> => {
  try {
    // Generate session ID if not provided
    const sessionId = request.sessionId || uuidv4();
    
    // Call the backend endpoint
    const response = await axios.post(`${API_URLS.backend}/api/a2a/guru/predict`, {
      query: request.query,
      sessionId
    });
    
    return response.data;
  } catch (error) {
    console.error('Error getting prediction from Guru Cripto A2A agent:', error);
    throw error;
  }
};

/**
 * Stream a prediction from the Guru Cripto A2A agent
 * @param request Prediction request
 * @param onUpdate Callback for updates
 * @returns Promise that resolves when the prediction is complete
 */
export const streamPrediction = async (
  request: PredictionRequest,
  onUpdate: (update: { is_task_complete: boolean; updates?: string; content?: PredictionResponse }) => void
): Promise<void> => {
  try {
    // Generate session ID if not provided
    const sessionId = request.sessionId || uuidv4();
    
    // Create EventSource for streaming
    const eventSource = new EventSource(
      `${API_URLS.backend}/api/a2a/guru/predict/stream?query=${encodeURIComponent(request.query)}&sessionId=${sessionId}`
    );
    
    return new Promise((resolve, reject) => {
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          // Call the update callback
          onUpdate(data);
          
          // Close the connection when the task is complete
          if (data.is_task_complete) {
            eventSource.close();
            resolve();
          }
        } catch (error) {
          console.error('Error parsing event data:', error);
          eventSource.close();
          reject(error);
        }
      };
      
      eventSource.onerror = (error) => {
        console.error('EventSource error:', error);
        eventSource.close();
        reject(error);
      };
    });
  } catch (error) {
    console.error('Error streaming prediction from Guru Cripto A2A agent:', error);
    throw error;
  }
};
