.voice-controls {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  border-radius: 8px;
  background-color: rgba(30, 30, 40, 0.6);
  padding: 10px;
  width: 100%;
}

.voice-controls-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.voice-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: #2c3e50;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.voice-button:hover {
  background-color: #34495e;
  transform: scale(1.05);
}

.voice-button.listening {
  background-color: #e74c3c;
  animation: pulse 1.5s infinite;
}

.voice-button.speaking {
  background-color: #3498db;
  animation: wave 1.5s infinite;
}

.voice-button.disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
  opacity: 0.7;
}

.voice-status {
  flex: 1;
  margin: 0 10px;
  font-size: 14px;
  color: #ecf0f1;
}

.listening-indicator {
  color: #e74c3c;
  animation: blink 1s infinite;
}

.speaking-indicator {
  color: #3498db;
  animation: wave 1.5s infinite;
}

.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background-color: #2c3e50;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.settings-button:hover {
  background-color: #34495e;
}

.voice-controls-settings {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
  margin-bottom: 10px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  color: #ecf0f1;
}

.slider {
  width: 100%;
  height: 5px;
  border-radius: 5px;
  background: #34495e;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
  border: none;
}

/* Animaciones */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

@keyframes wave {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
