"""
Sentiment Analysis Agent using Google ADK with MCP Integration
"""
import os
import json
import asyncio
from typing import Dict, Any, List, Optional

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from google.adk.runtime import InvocationContext

# Import MCP tools
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
from mcp_tools.brave_mcp_tool import BraveMcpTool
from mcp_tools.utils import extract_crypto_id, extract_timeframe, store_in_session, get_from_session

# API Keys
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")

# Function tools for the agent
async def analyze_sentiment_with_mcp(query: str, ctx: InvocationContext) -> str:
    """
    Analyze sentiment for a cryptocurrency based on user query using MCP tools.

    Args:
        query: User query about cryptocurrency sentiment
        ctx: Invocation context

    Returns:
        Sentiment analysis result
    """
    try:
        # Extract crypto ID and name from query
        crypto_id = extract_crypto_id(query)
        crypto_name = crypto_id.capitalize()  # Simple capitalization for display
        
        # Extract timeframe from query
        days, _ = extract_timeframe(query)
        
        # Store in session state
        store_in_session(ctx, "crypto_id", crypto_id)
        store_in_session(ctx, "crypto_name", crypto_name)
        store_in_session(ctx, "days", days)
        
        # Create Brave MCP tool
        brave_tool = BraveMcpTool()
        
        # Fetch news articles
        freshness = "pd" if days <= 1 else "pw" if days <= 7 else "pm"
        news_articles = await brave_tool.get_coin_news(crypto_id, count=10, freshness=freshness)
        
        # Analyze news sentiment
        news_sentiment = await analyze_news_sentiment(news_articles)
        
        # Fetch social media sentiment (simulated for now)
        social_sentiment = generate_simulated_social_sentiment(crypto_name)
        
        # Calculate Fear & Greed Index
        fear_greed_index = calculate_fear_greed_index(news_sentiment, social_sentiment)
        fear_greed_classification = get_fear_greed_classification(fear_greed_index)
        
        # Prepare result
        result = {
            "crypto_name": crypto_name,
            "crypto_id": crypto_id,
            "timeframe_days": days,
            "news_sentiment": news_sentiment,
            "social_sentiment": social_sentiment,
            "fear_greed_index": fear_greed_index,
            "fear_greed_classification": fear_greed_classification,
            "news_articles": news_articles[:5]  # Include top 5 articles
        }
        
        # Store results in session state
        store_in_session(ctx, "sentiment_analysis", result)
        
        # Close the MCP session
        await brave_tool.close_session()
        
        # Return structured data for the LLM to format
        return json.dumps(result)
    except Exception as e:
        print(f"Error in analyze_sentiment_with_mcp: {e}")
        # Return error message
        return json.dumps({
            "error": f"Error analyzing sentiment: {str(e)}",
            "crypto_id": crypto_id,
            "crypto_name": crypto_name,
            "days": days
        })

async def analyze_news_sentiment(news_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze sentiment of news articles.

    Args:
        news_articles: List of news articles

    Returns:
        Sentiment analysis results
    """
    try:
        # Prepare articles for analysis
        articles_text = []
        for article in news_articles[:5]:  # Limit to 5 articles to avoid token limits
            title = article.get("title", "")
            description = article.get("description", "")
            articles_text.append(f"Title: {title}\nDescription: {description}")

        combined_text = "\n\n".join(articles_text)

        # Use OpenRouter API to analyze sentiment
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://criptokens.app",
                "X-Title": "Criptokens - Sentiment Analysis"
            }

            payload = {
                "model": "anthropic/claude-3-haiku-20240307",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a sentiment analysis expert. Analyze the sentiment of cryptocurrency news articles and provide a detailed breakdown."
                    },
                    {
                        "role": "user",
                        "content": f"Analyze the sentiment of these cryptocurrency news articles. Provide a sentiment score from -100 (extremely negative) to +100 (extremely positive), with 0 being neutral. Also categorize the overall sentiment as positive, neutral, or negative, and provide a brief explanation of your analysis.\n\n{combined_text}"
                    }
                ],
                "response_format": {
                    "type": "json_object"
                }
            }

            async with session.post(
                "https://openrouter.ai/api/v1/chat/completions",
                json=payload,
                headers=headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")
                    try:
                        sentiment_analysis = json.loads(content)
                        return sentiment_analysis
                    except json.JSONDecodeError:
                        print(f"Failed to parse sentiment analysis result: {content}")

        # Fallback to simulated sentiment analysis
        print("Failed to analyze sentiment with OpenRouter, using simulated analysis")
        return generate_simulated_sentiment(news_articles)
    except Exception as e:
        print(f"Error analyzing sentiment: {e}")
        return generate_simulated_sentiment(news_articles)

def generate_simulated_sentiment(news_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate simulated sentiment analysis."""
    import random
    
    # Generate random sentiment score
    sentiment_score = random.uniform(-70, 70)
    
    # Determine sentiment category
    if sentiment_score > 20:
        overall_sentiment = "positive"
    elif sentiment_score < -20:
        overall_sentiment = "negative"
    else:
        overall_sentiment = "neutral"
    
    # Generate explanation
    explanation = f"Analysis based on {len(news_articles)} news articles. "
    
    if overall_sentiment == "positive":
        explanation += "The overall sentiment is positive, indicating optimistic market outlook."
    elif overall_sentiment == "negative":
        explanation += "The overall sentiment is negative, suggesting caution in the market."
    else:
        explanation += "The overall sentiment is neutral, showing balanced market perspectives."
    
    return {
        "sentiment_score": round(sentiment_score, 2),
        "overall_sentiment": overall_sentiment,
        "explanation": explanation
    }

def generate_simulated_social_sentiment(crypto_name: str) -> Dict[str, Any]:
    """Generate simulated social media sentiment."""
    import random

    # Generate random sentiment metrics
    twitter_sentiment = random.uniform(-100, 100)
    reddit_sentiment = random.uniform(-100, 100)

    # Calculate average sentiment
    average_sentiment = (twitter_sentiment + reddit_sentiment) / 2

    # Determine sentiment category
    if average_sentiment > 20:
        overall_sentiment = "positive"
    elif average_sentiment < -20:
        overall_sentiment = "negative"
    else:
        overall_sentiment = "neutral"

    # Generate random metrics
    twitter_mentions = random.randint(1000, 10000)
    reddit_mentions = random.randint(500, 5000)

    # Generate random trending hashtags
    hashtags = [
        f"#{crypto_name}",
        f"#{crypto_name}ToTheMoon",
        f"#Buy{crypto_name}",
        f"#{crypto_name}News",
        f"#Crypto"
    ]
    random.shuffle(hashtags)
    trending_hashtags = hashtags[:3]

    return {
        "twitter": {
            "sentiment_score": round(twitter_sentiment, 2),
            "mentions": twitter_mentions,
            "trending_hashtags": trending_hashtags
        },
        "reddit": {
            "sentiment_score": round(reddit_sentiment, 2),
            "mentions": reddit_mentions,
            "active_subreddits": [f"r/{crypto_name}", "r/CryptoCurrency", "r/CryptoMarkets"]
        },
        "overall": {
            "sentiment_score": round(average_sentiment, 2),
            "sentiment_category": overall_sentiment,
            "total_mentions": twitter_mentions + reddit_mentions
        }
    }

def calculate_fear_greed_index(news_sentiment: Dict[str, Any], social_sentiment: Dict[str, Any]) -> int:
    """
    Calculate Fear & Greed Index.

    Args:
        news_sentiment: News sentiment analysis
        social_sentiment: Social media sentiment analysis

    Returns:
        Fear & Greed Index (0-100)
    """
    # Extract sentiment scores
    news_score = news_sentiment.get("sentiment_score", 0)
    social_score = social_sentiment.get("overall", {}).get("sentiment_score", 0)

    # Convert from -100/+100 scale to 0-100 scale
    news_score_normalized = (news_score + 100) / 2
    social_score_normalized = (social_score + 100) / 2

    # Calculate weighted average (60% news, 40% social)
    fear_greed_index = (news_score_normalized * 0.6) + (social_score_normalized * 0.4)

    # Ensure it's within 0-100 range
    fear_greed_index = max(0, min(100, fear_greed_index))

    return round(fear_greed_index)

def get_fear_greed_classification(index: int) -> str:
    """Get classification for Fear & Greed Index."""
    if index >= 0 and index < 25:
        return "Extreme Fear"
    elif index >= 25 and index < 40:
        return "Fear"
    elif index >= 40 and index < 60:
        return "Neutral"
    elif index >= 60 and index < 75:
        return "Greed"
    else:
        return "Extreme Greed"

# Create the sentiment analysis agent with MCP integration
sentiment_agent_mcp = LlmAgent(
    name="sentiment_analysis_agent",
    model="gemini-1.5-pro",
    description="Analyzes sentiment of cryptocurrency news and social media with MCP integration",
    instruction="""
    You are a cryptocurrency sentiment analysis expert. Your task is to:

    1. Analyze the sentiment data provided to you
    2. Explain what the news sentiment indicates about market perception
    3. Interpret the social media sentiment and its implications
    4. Explain the Fear & Greed Index and its significance
    5. Provide a clear, concise sentiment analysis summary

    When responding:
    - Be specific about what the sentiment metrics mean
    - Explain the significance of the Fear & Greed Index
    - Highlight any discrepancies between news and social media sentiment
    - Provide a conclusion about the overall market sentiment

    The sentiment data will be provided to you as JSON data.
    """,
    tools=[FunctionTool(func=analyze_sentiment_with_mcp)],
    output_key="sentiment_analysis"
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime
    
    async def main():
        runtime = Runtime()
        session = runtime.new_session()
        
        # Test the agent with a query
        response = await sentiment_agent_mcp.run_async(
            session=session,
            query="What's the sentiment for Bitcoin right now?"
        )
        
        print(response)
    
    asyncio.run(main())
