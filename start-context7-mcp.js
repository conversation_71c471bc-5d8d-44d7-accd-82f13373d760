/**
 * Script para iniciar Context7 MCP Server
 * 
 * Este script inicia el servidor Context7 MCP que proporciona documentación actualizada
 * para bibliotecas y frameworks, mejorando las capacidades del Gurú Cripto.
 */

const { spawn } = require('child_process');
const http = require('http');

// Puerto predeterminado para Context7 MCP
const DEFAULT_PORT = 7777;

// Función para verificar si un puerto está en uso
function isPortInUse(port) {
  return new Promise((resolve) => {
    const server = http.createServer();
    
    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        resolve(true);
      } else {
        resolve(false);
      }
    });
    
    server.once('listening', () => {
      server.close();
      resolve(false);
    });
    
    server.listen(port);
  });
}

// Función para iniciar Context7 MCP
async function startContext7Mcp() {
  console.log('Iniciando Context7 MCP Server...');
  
  // Verificar si el puerto predeterminado está en uso
  const portInUse = await isPortInUse(DEFAULT_PORT);
  if (portInUse) {
    console.log(`ADVERTENCIA: El puerto ${DEFAULT_PORT} ya está en uso. Context7 MCP podría usar un puerto diferente.`);
  }
  
  // Iniciar Context7 MCP usando npx
  const context7Process = spawn('npx', ['-y', '@upstash/context7-mcp@latest'], {
    stdio: 'pipe',
    shell: true
  });
  
  // Manejar salida estándar
  context7Process.stdout.on('data', (data) => {
    const output = data.toString().trim();
    console.log(`[Context7 MCP] ${output}`);
    
    // Detectar si el servidor está en ejecución
    if (output.includes('running on')) {
      console.log('\n¡Context7 MCP Server iniciado correctamente!');
      console.log('Ahora puedes usar Context7 en tus consultas al Gurú Cripto añadiendo "use context7" al final de tu prompt.');
      console.log('Ejemplo: "Crea un componente React que muestre un gráfico de precios de Bitcoin usando Chart.js. use context7"\n');
    }
  });
  
  // Manejar errores
  context7Process.stderr.on('data', (data) => {
    console.error(`[Context7 MCP] Error: ${data.toString().trim()}`);
  });
  
  // Manejar cierre del proceso
  context7Process.on('close', (code) => {
    if (code !== 0) {
      console.error(`Context7 MCP Server se cerró con código de error: ${code}`);
    } else {
      console.log('Context7 MCP Server se cerró correctamente.');
    }
  });
  
  // Devolver el proceso para que pueda ser manejado externamente
  return context7Process;
}

// Iniciar Context7 MCP si este script se ejecuta directamente
if (require.main === module) {
  startContext7Mcp();
}

module.exports = { startContext7Mcp };
