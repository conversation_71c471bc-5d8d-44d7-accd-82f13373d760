/* Variables de color para el avatar */
:root {
  --avatar-primary: #00f2ff;
  --avatar-secondary: #4657ce;
  --avatar-tertiary: #7b4dff;
  --avatar-positive: #00ff9d;
  --avatar-negative: #ff3a6e;
  --avatar-neutral: #ffcc00;
  --avatar-concerned: #ff6b3d;
  --avatar-background: #0f1123;

  /* Variables de color para estados del avatar */
  --avatar-color-neutral: var(--avatar-primary);
  --avatar-color-positive: var(--avatar-positive);
  --avatar-color-negative: var(--avatar-negative);
  --avatar-color-concerned: var(--avatar-concerned);

  /* Variables para animaciones */
  --animation-speed-slow: 3s;
  --animation-speed-medium: 2s;
  --animation-speed-fast: 1s;
  --animation-speed-very-fast: 0.5s;
}

.cripto-agent-avatar {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: default;
}

.cripto-agent-avatar.interactive {
  cursor: pointer;
}

.cripto-agent-avatar.activated {
  transform: scale(1.05);
}

.avatar-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* Tamaños del avatar */
.cripto-agent-avatar.small .avatar-container {
  width: 60px;
  height: 60px;
}

.cripto-agent-avatar.medium .avatar-container {
  width: 100px;
  height: 100px;
}

.cripto-agent-avatar.large .avatar-container {
  width: 150px;
  height: 150px;
}

.cripto-agent-avatar.xlarge .avatar-container {
  width: 200px;
  height: 200px;
}

/* Núcleo del avatar */
.avatar-core {
  position: absolute;
  background: radial-gradient(circle, #4657ce 0%, #00f2ff 100%);
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 0 15px rgba(0, 242, 255, 0.5);
  transition: all 0.3s ease;
}

/* Modos de poder */
.power-enhanced .avatar-core {
  background: radial-gradient(circle, #00c2cc 0%, #00f2ff 100%);
  box-shadow: 0 0 25px rgba(0, 242, 255, 0.8);
}

.power-predictive .avatar-core {
  background: radial-gradient(circle, #5e3dcc 0%, #7b4dff 100%);
  box-shadow: 0 0 25px rgba(123, 77, 255, 0.8);
}

.power-analytical .avatar-core {
  background: radial-gradient(circle, #cc2357 0%, #ff2a6d 100%);
  box-shadow: 0 0 25px rgba(255, 42, 109, 0.8);
}

/* Estados de criptomonedas */
.crypto-very-positive .avatar-core {
  background: radial-gradient(circle, #00cc7a 0%, #00ff9d 100%);
  box-shadow: 0 0 25px rgba(0, 255, 157, 0.8);
  animation: pulse-positive 1.5s infinite alternate;
}

.crypto-positive .avatar-core {
  background: radial-gradient(circle, #00c2cc 0%, #00f2ff 100%);
  box-shadow: 0 0 20px rgba(0, 242, 255, 0.7);
  animation: pulse-positive 2s infinite alternate;
}

.crypto-very-negative .avatar-core {
  background: radial-gradient(circle, #cc2357 0%, #ff3a6e 100%);
  box-shadow: 0 0 25px rgba(255, 58, 110, 0.8);
  animation: pulse-negative 1.5s infinite alternate;
}

.crypto-negative .avatar-core {
  background: radial-gradient(circle, #cc5430 0%, #ff6b3d 100%);
  box-shadow: 0 0 20px rgba(255, 107, 61, 0.7);
  animation: pulse-negative 2s infinite alternate;
}

.crypto-neutral .avatar-core {
  background: radial-gradient(circle, #5e3dcc 0%, #7b4dff 100%);
  box-shadow: 0 0 15px rgba(123, 77, 255, 0.6);
  animation: pulse-neutral 3s infinite alternate;
}

@keyframes pulse-positive {
  0% { transform: scale(1); opacity: 0.9; }
  100% { transform: scale(1.05); opacity: 1; }
}

@keyframes pulse-negative {
  0% { transform: scale(1); opacity: 0.9; }
  100% { transform: scale(0.98); opacity: 1; }
}

@keyframes pulse-neutral {
  0% { transform: scale(1); opacity: 0.9; }
  100% { transform: scale(1.02); opacity: 1; }
}

.power-aura {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  z-index: 1;
  opacity: 0.2;
  animation: pulse-aura 2s infinite alternate;
}

.power-enhanced .power-aura {
  background: radial-gradient(circle, transparent 30%, rgba(0, 242, 255, 0.3) 70%);
  box-shadow: 0 0 30px rgba(0, 242, 255, 0.5);
}

.power-predictive .power-aura {
  background: radial-gradient(circle, transparent 30%, rgba(123, 77, 255, 0.3) 70%);
  box-shadow: 0 0 30px rgba(123, 77, 255, 0.5);
}

.power-analytical .power-aura {
  background: radial-gradient(circle, transparent 30%, rgba(255, 42, 109, 0.3) 70%);
  box-shadow: 0 0 30px rgba(255, 42, 109, 0.5);
}

@keyframes pulse-aura {
  0% { transform: scale(1); opacity: 0.1; }
  100% { transform: scale(1.05); opacity: 0.2; }
}

.small .avatar-core {
  width: 30px;
  height: 30px;
}

.medium .avatar-core {
  width: 50px;
  height: 50px;
}

.large .avatar-core {
  width: 75px;
  height: 75px;
}

/* Anillos del avatar */
.avatar-ring {
  position: absolute;
  border-radius: 50%;
  border: 2px solid transparent;
  transition: all 0.5s ease;
  /* Animación de rotación eliminada para reducir distracciones */
}

.avatar-ring.ring1 {
  border-top-color: rgba(0, 242, 255, 0.8);
  border-left-color: rgba(0, 242, 255, 0.4);
  border-right-color: rgba(0, 242, 255, 0.4);
  border-bottom-color: rgba(0, 242, 255, 0.1);
}

.avatar-ring.ring2 {
  border-top-color: rgba(70, 87, 206, 0.8);
  border-right-color: rgba(70, 87, 206, 0.4);
  border-left-color: rgba(70, 87, 206, 0.4);
  border-bottom-color: rgba(70, 87, 206, 0.1);
}

.avatar-ring.ring3 {
  border-top-color: rgba(123, 77, 255, 0.8);
  border-left-color: rgba(123, 77, 255, 0.4);
  border-right-color: rgba(123, 77, 255, 0.1);
  border-bottom-color: rgba(123, 77, 255, 0.4);
}

/* Anillos en modo de poder */
.power-enhanced .avatar-ring {
  border-width: 3px;
  box-shadow: 0 0 10px rgba(0, 242, 255, 0.3);
}

.power-predictive .avatar-ring {
  border-width: 3px;
  box-shadow: 0 0 10px rgba(123, 77, 255, 0.3);
}

.power-analytical .avatar-ring {
  border-width: 3px;
  box-shadow: 0 0 10px rgba(255, 42, 109, 0.3);
}

.small .avatar-ring.ring1 {
  width: 45px;
  height: 45px;
}

.small .avatar-ring.ring2 {
  width: 55px;
  height: 55px;
}

.small .avatar-ring.ring3 {
  width: 65px;
  height: 65px;
}

.medium .avatar-ring.ring1 {
  width: 75px;
  height: 75px;
}

.medium .avatar-ring.ring2 {
  width: 90px;
  height: 90px;
}

.medium .avatar-ring.ring3 {
  width: 105px;
  height: 105px;
}

.large .avatar-ring.ring1 {
  width: 110px;
  height: 110px;
}

.large .avatar-ring.ring2 {
  width: 140px;
  height: 140px;
}

.large .avatar-ring.ring3 {
  width: 170px;
  height: 170px;
}

.xlarge .avatar-ring.ring1 {
  width: 150px;
  height: 150px;
}

.xlarge .avatar-ring.ring2 {
  width: 180px;
  height: 180px;
}

.xlarge .avatar-ring.ring3 {
  width: 210px;
  height: 210px;
}

/* Cara del avatar */
.avatar-face {
  position: absolute;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Ojos */
.avatar-eyes {
  display: flex;
  justify-content: space-between;
  transition: transform 0.2s ease;
}

.small .avatar-eyes {
  width: 20px;
  margin-bottom: 2px;
}

.medium .avatar-eyes {
  width: 30px;
  margin-bottom: 4px;
}

.large .avatar-eyes {
  width: 45px;
  margin-bottom: 6px;
}

.eye {
  background-color: rgba(10, 10, 26, 0.9);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.small .eye {
  width: 4px;
  height: 4px;
}

.medium .eye {
  width: 6px;
  height: 6px;
}

.large .eye {
  width: 10px;
  height: 10px;
}

/* Expresiones de ojos */
.happy-eyes .eye {
  height: 1px;
  border-radius: 0;
  transform: translateY(2px);
}

.thinking-eyes .eye.left {
  height: 1px;
  border-radius: 0;
  transform: translateY(2px);
}

.excited-eyes .eye {
  transform: scale(1.5);
}

.concerned-eyes .eye {
  transform: scaleY(0.7);
}

.analyzing-eyes .eye {
  background-color: rgba(0, 242, 255, 0.9);
  box-shadow: 0 0 5px rgba(0, 242, 255, 0.8);
  animation: blink-analyzing 1.5s infinite;
}

.predicting-eyes .eye {
  background-color: rgba(123, 77, 255, 0.9);
  box-shadow: 0 0 5px rgba(123, 77, 255, 0.8);
  animation: pulse-predicting 2s infinite;
}

/* Nuevas expresiones para estados de criptomonedas */
.crypto-very-positive .eye,
.market-bullish .eye {
  background-color: rgba(0, 255, 157, 0.9);
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.8);
  transform: scale(1.3);
}

.crypto-very-negative .eye,
.market-bearish .eye {
  background-color: rgba(255, 58, 110, 0.9);
  box-shadow: 0 0 5px rgba(255, 58, 110, 0.8);
  transform: scaleY(0.6);
}

.market-volatile .eye {
  background-color: rgba(255, 204, 0, 0.9);
  box-shadow: 0 0 5px rgba(255, 204, 0, 0.8);
  animation: volatile-eyes 0.8s infinite alternate;
}

@keyframes volatile-eyes {
  0% { transform: translate(-2px, -1px); }
  100% { transform: translate(2px, 1px); }
}

@keyframes blink-analyzing {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes pulse-predicting {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); }
}

/* Boca */
.avatar-mouth {
  background-color: rgba(10, 10, 26, 0.9);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.small .avatar-mouth {
  width: 10px;
  height: 2px;
}

.medium .avatar-mouth {
  width: 15px;
  height: 3px;
}

.large .avatar-mouth {
  width: 25px;
  height: 4px;
}

/* Expresiones de boca */
.happy-mouth {
  border-radius: 0 0 10px 10px;
  transform: translateY(-2px);
}

.thinking-mouth {
  width: 7px !important;
  transform: translateX(-5px);
}

.excited-mouth {
  height: 6px !important;
  border-radius: 50%;
}

.concerned-mouth {
  border-radius: 10px 10px 0 0;
  transform: translateY(2px);
}

/* Nuevas expresiones de boca para estados de criptomonedas */
.crypto-very-positive .avatar-mouth,
.market-bullish .avatar-mouth {
  border-radius: 0 0 12px 12px;
  transform: translateY(-3px) scaleX(1.2);
  height: 5px !important;
}

.crypto-positive .avatar-mouth {
  border-radius: 0 0 10px 10px;
  transform: translateY(-2px) scaleX(1.1);
}

.crypto-very-negative .avatar-mouth,
.market-bearish .avatar-mouth {
  border-radius: 12px 12px 0 0;
  transform: translateY(3px) scaleX(1.2);
  height: 5px !important;
}

.crypto-negative .avatar-mouth {
  border-radius: 10px 10px 0 0;
  transform: translateY(2px) scaleX(1.1);
}

.market-volatile .avatar-mouth {
  animation: volatile-mouth 0.8s infinite alternate;
}

@keyframes volatile-mouth {
  0% { transform: translateX(-3px) rotate(-5deg); }
  100% { transform: translateX(3px) rotate(5deg); }
}

/* Animación de habla */
.avatar-mouth.speaking {
  animation: speak 0.5s infinite alternate;
}

@keyframes speak {
  0% {
    height: 2px;
    border-radius: 3px;
  }
  100% {
    height: 6px;
    border-radius: 50%;
  }
}

/* Animaciones expresivas para el avatar */
@keyframes jump {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

@keyframes sway {
  0% { transform: rotate(-3deg); }
  50% { transform: rotate(3deg); }
  100% { transform: rotate(-3deg); }
}

@keyframes vibrate {
  0% { transform: translateX(-3px); }
  25% { transform: translateX(3px); }
  50% { transform: translateX(-3px); }
  75% { transform: translateX(3px); }
  100% { transform: translateX(-3px); }
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px rgba(0, 255, 157, 0.3); }
  50% { box-shadow: 0 0 15px rgba(0, 255, 157, 0.7); }
  100% { box-shadow: 0 0 5px rgba(0, 255, 157, 0.3); }
}

@keyframes blink-eyes {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.03); }
}

/* Aplicar animaciones a estados específicos */
.avatar--positive .avatar-container {
  animation: breathe var(--animation-speed-medium) ease-in-out infinite;
}

.avatar--negative .avatar-container {
  animation: vibrate var(--animation-speed-very-fast) ease-in-out infinite;
}

.avatar--concerned .avatar-container {
  animation: pulse-glow var(--animation-speed-medium) ease-in-out infinite;
}

/* Animaciones para intensidad de cambio de precio - Simplificadas */
.intensity-medium .avatar-core {
  animation-duration: 2s !important;
}

.intensity-high .avatar-core {
  animation-duration: 1.5s !important;
  animation-timing-function: ease-in-out !important;
}

.intensity-extreme .avatar-core {
  animation-duration: 1.2s !important;
  animation-timing-function: ease-in-out !important;
}

/* Interfaz de comandos */
.command-interface {
  position: absolute;
  top: calc(100% + 15px);
  width: 250px;
  background: rgba(18, 18, 42, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 12px;
  border: 1px solid rgba(0, 242, 255, 0.3);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 242, 255, 0.3);
  z-index: 10;
  animation: fade-in 0.3s ease-out;
}

.command-interface form {
  display: flex;
  gap: 8px;
}

.command-interface input {
  flex: 1;
  background: rgba(10, 10, 26, 0.8);
  border: 1px solid rgba(0, 242, 255, 0.3);
  border-radius: 8px;
  color: white;
  padding: 8px 12px;
  font-size: 14px;
}

.command-interface input:focus {
  outline: none;
  border-color: rgba(0, 242, 255, 0.6);
  box-shadow: 0 0 10px rgba(0, 242, 255, 0.3);
}

.command-interface button {
  background: linear-gradient(135deg, #00f2ff, #4657ce);
  border: none;
  border-radius: 8px;
  color: white;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.command-interface button:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(0, 242, 255, 0.4);
}

.command-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.command-suggestions span {
  background: rgba(70, 87, 206, 0.2);
  border: 1px solid rgba(70, 87, 206, 0.4);
  border-radius: 16px;
  padding: 4px 10px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
}

.command-suggestions span:hover {
  background: rgba(70, 87, 206, 0.4);
  border-color: rgba(0, 242, 255, 0.6);
  transform: translateY(-1px);
}

.agent-tooltip {
  position: absolute;
  top: calc(100% + 10px);
  background: rgba(18, 18, 42, 0.9);
  backdrop-filter: blur(5px);
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 12px;
  color: white;
  white-space: nowrap;
  border: 1px solid rgba(0, 242, 255, 0.3);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  animation: fade-in 0.2s ease-out;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Indicador de habla */
.speech-indicator {
  display: flex;
  justify-content: center;
  gap: 3px;
  margin-top: 10px;
}

.speech-bar {
  width: 3px;
  height: 15px;
  background: linear-gradient(to top, #00f2ff, #4657ce);
  border-radius: 3px;
  animation: soundWave 0.8s infinite ease;
}

.speech-bar:nth-child(1) {
  animation-delay: 0s;
}

.speech-bar:nth-child(2) {
  animation-delay: 0.2s;
}

.speech-bar:nth-child(3) {
  animation-delay: 0.4s;
}

.speech-bar:nth-child(4) {
  animation-delay: 0.6s;
}

@keyframes soundWave {
  0%, 100% {
    height: 5px;
  }
  50% {
    height: 15px;
  }
}

/* Partículas del avatar */
.avatar-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
  pointer-events: none;
  overflow: visible;
}

/* Órbitas del avatar */
.avatar-orbit {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(0, 242, 255, 0.3);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1; /* Asegurar que las órbitas estén detrás del avatar */
  box-sizing: border-box;
}

.avatar-orbit-1 {
  width: 70px;
  height: 70px;
  border-color: rgba(123, 77, 255, 0.4);
}

.avatar-orbit-2 {
  width: 90px;
  height: 90px;
  border-color: rgba(0, 242, 255, 0.4);
}

.avatar-orbit-3 {
  width: 110px;
  height: 110px;
  border-color: rgba(123, 77, 255, 0.3);
}

.large .avatar-orbit-1 {
  width: 100px;
  height: 100px;
}

.large .avatar-orbit-2 {
  width: 130px;
  height: 130px;
}

.large .avatar-orbit-3 {
  width: 160px;
  height: 160px;
}

.avatar-particle {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(0, 242, 255, 0.7);
  pointer-events: none;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  width: 4px !important;
  height: 4px !important;
  box-shadow: 0 0 8px rgba(0, 242, 255, 0.6);
  z-index: 2;
}

/* Colores de partículas según el estado */
.avatar-particle.positive,
.avatar--positive .avatar-particle {
  background-color: rgba(0, 255, 157, 0.7);
  box-shadow: 0 0 8px rgba(0, 255, 157, 0.6);
}

.avatar-particle.negative,
.avatar--negative .avatar-particle {
  background-color: rgba(255, 58, 110, 0.7);
  box-shadow: 0 0 8px rgba(255, 58, 110, 0.6);
}

.avatar-particle.concerned,
.avatar--concerned .avatar-particle {
  background-color: rgba(255, 204, 0, 0.7);
  box-shadow: 0 0 8px rgba(255, 204, 0, 0.6);
}

.avatar-particle.thinking,
.avatar--thinking .avatar-particle {
  background-color: rgba(123, 77, 255, 0.7);
  box-shadow: 0 0 8px rgba(123, 77, 255, 0.6);
}

.avatar-particle.neutral,
.avatar-particle.idle,
.avatar--neutral .avatar-particle {
  background-color: rgba(0, 242, 255, 0.7);
  box-shadow: 0 0 8px rgba(0, 242, 255, 0.6);
}

/* Efecto de pulso */
.cripto-agent-avatar.pulse .avatar-core {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 242, 255, 0.5);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(0, 242, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 242, 255, 0);
  }
}

/* Estilos para modos de poder específicos */
.power-enhanced .avatar-core {
  animation: pulse-enhanced 2s infinite;
}

.power-predictive .avatar-core {
  animation: pulse-predictive 2s infinite;
}

.power-analytical .avatar-core {
  animation: pulse-analytical 2s infinite;
}

@keyframes pulse-enhanced {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 242, 255, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 242, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 242, 255, 0);
  }
}

@keyframes pulse-predictive {
  0% {
    box-shadow: 0 0 0 0 rgba(123, 77, 255, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(123, 77, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(123, 77, 255, 0);
  }
}

@keyframes pulse-analytical {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 42, 109, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 42, 109, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 42, 109, 0);
  }
}

/* Animación de rotación */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Estados del avatar basados en el rendimiento de la criptomoneda */
.avatar--neutral .avatar-core {
  background: radial-gradient(circle, #4657ce 0%, var(--avatar-color-neutral) 100%);
  transition: background 0.5s ease-in-out;
}

.avatar--positive .avatar-core {
  background: radial-gradient(circle, #00cc7a 0%, var(--avatar-color-positive) 100%);
  transition: background 0.5s ease-in-out;
}

.avatar--negative .avatar-core {
  background: radial-gradient(circle, #cc2357 0%, var(--avatar-color-negative) 100%);
  transition: background 0.5s ease-in-out;
}

.avatar--concerned .avatar-core {
  background: radial-gradient(circle, #cc5430 0%, var(--avatar-color-concerned) 100%);
  transition: background 0.5s ease-in-out;
}

/* Anillos del avatar según el estado */
.avatar--neutral .avatar-ring {
  border-color: var(--avatar-color-neutral);
  transition: border-color 0.5s ease-in-out;
}

.avatar--positive .avatar-ring {
  border-color: var(--avatar-color-positive);
  transition: border-color 0.5s ease-in-out;
}

.avatar--negative .avatar-ring {
  border-color: var(--avatar-color-negative);
  transition: border-color 0.5s ease-in-out;
}

.avatar--concerned .avatar-ring {
  border-color: var(--avatar-color-concerned);
  transition: border-color 0.5s ease-in-out;
}
