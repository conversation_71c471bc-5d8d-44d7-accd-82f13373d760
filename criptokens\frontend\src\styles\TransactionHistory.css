.transaction-history-container {
  background-color: rgba(15, 23, 42, 0.7);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(64, 220, 255, 0.1);
  margin-bottom: 30px;
}

.transaction-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid rgba(64, 220, 255, 0.2);
  padding-bottom: 16px;
}

.transaction-history-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  margin: 0;
  background: linear-gradient(90deg, #40dcff, #8a7cff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.filter-button {
  background-color: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(64, 220, 255, 0.2);
  color: #cbd5e1;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.filter-button:hover {
  background-color: rgba(30, 41, 59, 0.9);
  border-color: rgba(64, 220, 255, 0.4);
}

.filter-button.active {
  background-color: rgba(64, 220, 255, 0.2);
  border-color: rgba(64, 220, 255, 0.6);
  color: #fff;
}

.transactions-list {
  overflow-x: auto;
}

.transactions-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.transactions-table th {
  text-align: left;
  padding: 12px 16px;
  font-weight: 600;
  color: #94a3b8;
  border-bottom: 1px solid rgba(64, 220, 255, 0.2);
  font-size: 14px;
}

.transactions-table td {
  padding: 16px;
  border-bottom: 1px solid rgba(64, 220, 255, 0.1);
  color: #e2e8f0;
  font-size: 14px;
}

.transaction-row {
  transition: background-color 0.3s ease;
}

.transaction-row:hover {
  background-color: rgba(30, 41, 59, 0.5);
}

.transaction-row.buy {
  background-color: rgba(34, 197, 94, 0.05);
}

.transaction-row.sell {
  background-color: rgba(239, 68, 68, 0.05);
}

.transaction-type {
  text-align: center;
}

.type-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.type-badge.buy {
  background-color: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}

.type-badge.sell {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.asset-info {
  display: flex;
  flex-direction: column;
}

.asset-name {
  font-weight: 600;
  color: #fff;
}

.asset-symbol {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

.transaction-notes {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #94a3b8;
}

.empty-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #94a3b8;
  text-align: center;
}

.show-all-button {
  margin-top: 16px;
  background-color: rgba(64, 220, 255, 0.1);
  border: 1px solid rgba(64, 220, 255, 0.3);
  color: #40dcff;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.show-all-button:hover {
  background-color: rgba(64, 220, 255, 0.2);
  border-color: rgba(64, 220, 255, 0.5);
}

.transaction-history-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #94a3b8;
}

.not-logged-in {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #94a3b8;
  text-align: center;
}

.not-logged-in h3 {
  color: #fff;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .transaction-history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .transactions-table th:nth-child(5),
  .transactions-table td:nth-child(5),
  .transactions-table th:nth-child(7),
  .transactions-table td:nth-child(7) {
    display: none;
  }
}
