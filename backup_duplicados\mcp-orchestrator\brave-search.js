const axios = require('axios');
require('dotenv').config();

// API Key para Brave Search
const BRAVE_API_KEY = process.env.BRAVE_API_KEY || 'BSA-RKcBTuhF2QjiyMKHj-XlDgR4vvX';

/**
 * Realiza una búsqueda en Brave Search
 * @param {string} query - Consulta de búsqueda
 * @param {number} count - Número de resultados a devolver
 * @param {string} freshness - Filtro de tiempo
 * @returns {Promise<Array>} - Resultados de la búsqueda
 */
async function braveSearch(query, count = 5, freshness = 'pm') {
  try {
    console.log(`Realizando búsqueda en Brave para: "${query}" (count: ${count}, freshness: ${freshness})`);
    
    // Realizar la petición a la API de Brave Search
    const response = await axios.get('https://api.search.brave.com/res/v1/web/search', {
      params: {
        q: query,
        count: Math.min(count, 10), // Máximo 10 resultados por petición
        freshness: freshness,
        search_lang: 'es',
        country: 'ES',
        safesearch: 'moderate'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': BRAVE_API_KEY
      },
      timeout: 5000 // 5 segundos de timeout
    });
    
    if (response.data && response.data.web && response.data.web.results) {
      console.log(`Recibidos ${response.data.web.results.length} resultados de Brave Search`);
      
      // Transformar los resultados al formato esperado
      return response.data.web.results.map(result => ({
        title: result.title,
        url: result.url,
        description: result.description || '',
        publishedDate: result.age || '',
        source: result.url ? new URL(result.url).hostname : 'Unknown'
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Error al usar la API de Brave Search:', error.message);
    return [];
  }
}

// Crear un servidor HTTP simple para exponer la API
const http = require('http');

const server = http.createServer(async (req, res) => {
  // Configurar CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Manejar preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }
  
  // Solo aceptar POST en /search
  if (req.method === 'POST' && req.url === '/search') {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', async () => {
      try {
        const { query, count, freshness } = JSON.parse(body);
        
        if (!query) {
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'Se requiere una consulta de búsqueda' }));
          return;
        }
        
        const results = await braveSearch(query, count, freshness);
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(results));
      } catch (error) {
        console.error('Error al procesar la solicitud:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Error interno del servidor' }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Ruta no encontrada' }));
  }
});

const PORT = process.env.PORT || 3102;

server.listen(PORT, () => {
  console.log(`Servidor Brave Search iniciado en el puerto ${PORT}`);
  console.log(`Endpoint: http://localhost:${PORT}/search`);
  console.log(`Ejemplo de uso: curl -X POST http://localhost:${PORT}/search -H "Content-Type: application/json" -d '{"query": "bitcoin", "count": 5, "freshness": "pd"}'`);
});
