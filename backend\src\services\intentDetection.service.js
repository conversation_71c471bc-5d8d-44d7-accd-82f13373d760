/**
 * Servicio para detectar intenciones en las preguntas del usuario
 */

/**
 * Detecta la intención de la pregunta del usuario
 * @param {string} question - Pregunta del usuario
 * @returns {Object} - Objeto con la intención detectada y metadatos
 */
function detectIntent(question) {
  const lowerQuestion = question.toLowerCase();

  // Detectar intención de análisis técnico
  if (isTechnicalAnalysisQuestion(lowerQuestion)) {
    return {
      intent: 'technical_analysis',
      confidence: 0.9,
      metadata: {
        timeframe: detectTimeframe(lowerQuestion),
        indicators: detectIndicators(lowerQuestion)
      }
    };
  }

  // Detectar intención de precio
  if (isPriceQuestion(lowerQuestion)) {
    return {
      intent: 'price',
      confidence: 0.9,
      metadata: {}
    };
  }

  // Detectar intención de noticias
  if (isNewsQuestion(lowerQuestion)) {
    return {
      intent: 'news',
      confidence: 0.8,
      metadata: {
        topic: detectNewsTopic(lowerQuestion)
      }
    };
  }

  // Detectar intención de análisis fundamental
  if (isFundamentalAnalysisQuestion(lowerQuestion)) {
    return {
      intent: 'fundamental_analysis',
      confidence: 0.8,
      metadata: {}
    };
  }

  // Detectar intención de predicción
  if (isPredictionQuestion(lowerQuestion)) {
    return {
      intent: 'prediction',
      confidence: 0.7,
      metadata: {
        timeframe: detectPredictionTimeframe(lowerQuestion)
      }
    };
  }

  // Detectar intención de explicación
  if (isExplanationQuestion(lowerQuestion)) {
    return {
      intent: 'explanation',
      confidence: 0.8,
      metadata: {
        concept: detectConcept(lowerQuestion)
      }
    };
  }

  // Intención general (conversación)
  return {
    intent: 'general',
    confidence: 0.5,
    metadata: {}
  };
}

/**
 * Detecta si la pregunta es sobre análisis técnico
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {boolean} - True si es una pregunta de análisis técnico
 */
function isTechnicalAnalysisQuestion(question) {
  const technicalAnalysisRegex = /an[aá]lisis t[eé]cnico|rsi|macd|bandas de bollinger|indicadores t[eé]cnicos|patrones de velas|comprar o vender|soporte|resistencia|tendencia|gr[aá]fico|chart|velas|candlestick|oscilador|divergencia|retroceso|fibonacci|estoc[aá]stico|momentum|volumen|media m[oó]vil|ema|sma/i;

  return technicalAnalysisRegex.test(question) ||
         question.includes('técnico') ||
         question.includes('tecnico');
}

/**
 * Detecta si la pregunta es sobre precios
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {boolean} - True si es una pregunta de precio
 */
function isPriceQuestion(question) {
  const priceRegex = /precio|cotizaci[oó]n|valor|cu[aá]nto vale|cu[aá]nto cuesta|c[oó]mo est[aá]|precio actual|valor actual/i;

  return priceRegex.test(question);
}

/**
 * Detecta si la pregunta es sobre noticias
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {boolean} - True si es una pregunta de noticias
 */
function isNewsQuestion(question) {
  const newsRegex = /noticias|novedades|[uú]ltimas noticias|actualidad|qu[eé] ha pasado|eventos recientes|anuncios|comunicados|prensa|blog|art[ií]culos/i;

  return newsRegex.test(question);
}

/**
 * Detecta si la pregunta es sobre análisis fundamental
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {boolean} - True si es una pregunta de análisis fundamental
 */
function isFundamentalAnalysisQuestion(question) {
  const fundamentalRegex = /an[aá]lisis fundamental|fundamentos|equipo|tecnolog[ií]a|caso de uso|tokenomics|suministro|inflaci[oó]n|quema|utilidad|adopci[oó]n|competencia|ventajas|desventajas|comparaci[oó]n|vs|versus|mejor que|peor que|roadmap|hoja de ruta|whitepaper|libro blanco|inversi[oó]n a largo plazo|capitalizaci[oó]n|market cap|volumen|dominancia|fortalezas|debilidades|oportunidades|amenazas|swot|foda/i;

  return fundamentalRegex.test(question) ||
         question.includes('fundamental') ||
         question.includes('fundamentos') ||
         (question.includes('análisis') && !isTechnicalAnalysisQuestion(question) && question.includes('largo plazo'));
}

/**
 * Detecta si la pregunta es sobre predicciones
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {boolean} - True si es una pregunta de predicción
 */
function isPredictionQuestion(question) {
  const predictionRegex = /predicci[oó]n|pron[oó]stico|futuro|llegar[aá]|alcanzar[aá]|cu[aá]nto valdr[aá]|precio futuro|estimaci[oó]n|proyecci[oó]n|potencial|crecimiento|ca[ií]da|subir[aá]|bajar[aá]|cu[aá]l ser[aá]|c[oó]mo evolucionar[aá]|qu[eé] esperar/i;

  return predictionRegex.test(question);
}

/**
 * Detecta si la pregunta es sobre explicaciones de conceptos
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {boolean} - True si es una pregunta de explicación
 */
function isExplanationQuestion(question) {
  const explanationRegex = /qu[eé] es|c[oó]mo funciona|explica|explicaci[oó]n|definici[oó]n|significado|para qu[eé] sirve|cu[aá]l es el prop[oó]sito|entender|comprender|diferencia entre|comparado con|versus|vs|mejor que|peor que|ventajas|desventajas|pros y contras|beneficios|riesgos|c[oó]mo puedo|tutorial/i;

  return explanationRegex.test(question);
}

/**
 * Detecta el timeframe mencionado en la pregunta
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {string|null} - Timeframe detectado o null
 */
function detectTimeframe(question) {
  if (question.includes('1 minuto') || question.includes('1m') || question.includes('un minuto')) {
    return '1m';
  } else if (question.includes('5 minutos') || question.includes('5m') || question.includes('cinco minutos')) {
    return '5m';
  } else if (question.includes('15 minutos') || question.includes('15m') || question.includes('quince minutos')) {
    return '15m';
  } else if (question.includes('30 minutos') || question.includes('30m') || question.includes('treinta minutos')) {
    return '30m';
  } else if (question.includes('1 hora') || question.includes('1h') || question.includes('una hora')) {
    return '1h';
  } else if (question.includes('4 horas') || question.includes('4h') || question.includes('cuatro horas')) {
    return '4h';
  } else if (question.includes('1 día') || question.includes('1d') || question.includes('diario') || question.includes('un día')) {
    return '1d';
  } else if (question.includes('1 semana') || question.includes('1w') || question.includes('semanal') || question.includes('una semana')) {
    return '1w';
  } else if (question.includes('1 mes') || question.includes('1M') || question.includes('mensual') || question.includes('un mes')) {
    return '1M';
  }

  // Timeframe por defecto
  return '1d';
}

/**
 * Detecta los indicadores mencionados en la pregunta
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {string[]} - Array de indicadores detectados
 */
function detectIndicators(question) {
  const indicators = [];

  if (question.includes('rsi') || question.includes('índice de fuerza relativa') || question.includes('indice de fuerza relativa')) {
    indicators.push('rsi');
  }

  if (question.includes('macd') || question.includes('convergencia') || question.includes('divergencia')) {
    indicators.push('macd');
  }

  if (question.includes('bollinger') || question.includes('bandas')) {
    indicators.push('bollinger');
  }

  if (question.includes('media móvil') || question.includes('media movil') || question.includes('moving average') || question.includes('ma')) {
    indicators.push('ma');
  }

  if (question.includes('volumen') || question.includes('volume')) {
    indicators.push('volume');
  }

  if (question.includes('estocástico') || question.includes('estocastico') || question.includes('stochastic')) {
    indicators.push('stochastic');
  }

  if (question.includes('fibonacci') || question.includes('retroceso') || question.includes('retracement')) {
    indicators.push('fibonacci');
  }

  return indicators;
}

/**
 * Detecta el tema de noticias mencionado en la pregunta
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {string|null} - Tema detectado o null
 */
function detectNewsTopic(question) {
  // Extraer el tema después de palabras clave
  const keywordsRegex = /noticias (sobre|de|acerca de) ([a-zA-Z0-9 ]+)|novedades (sobre|de|acerca de) ([a-zA-Z0-9 ]+)|actualidad (sobre|de|acerca de) ([a-zA-Z0-9 ]+)/i;
  const match = question.match(keywordsRegex);

  if (match) {
    // El tema estará en el grupo 2, 4 o 6 dependiendo de qué palabra clave se encontró
    return match[2] || match[4] || match[6];
  }

  return null;
}

/**
 * Detecta el timeframe de predicción mencionado en la pregunta
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {string} - Timeframe detectado
 */
function detectPredictionTimeframe(question) {
  if (question.includes('hoy') || question.includes('ahora') || question.includes('inmediato') || question.includes('corto plazo')) {
    return 'short';
  } else if (question.includes('esta semana') || question.includes('próximos días') || question.includes('proximos dias')) {
    return 'medium-short';
  } else if (question.includes('este mes') || question.includes('próximas semanas') || question.includes('proximas semanas')) {
    return 'medium';
  } else if (question.includes('este año') || question.includes('próximos meses') || question.includes('proximos meses')) {
    return 'medium-long';
  } else if (question.includes('largo plazo') || question.includes('años') || question.includes('futuro')) {
    return 'long';
  }

  // Timeframe por defecto
  return 'medium';
}

/**
 * Detecta el concepto mencionado en la pregunta
 * @param {string} question - Pregunta del usuario en minúsculas
 * @returns {string|null} - Concepto detectado o null
 */
function detectConcept(question) {
  // Extraer el concepto después de palabras clave
  const keywordsRegex = /qu[eé] es ([a-zA-Z0-9 ]+)|c[oó]mo funciona ([a-zA-Z0-9 ]+)|explica ([a-zA-Z0-9 ]+)|explicaci[oó]n de ([a-zA-Z0-9 ]+)|definici[oó]n de ([a-zA-Z0-9 ]+)/i;
  const match = question.match(keywordsRegex);

  if (match) {
    // El concepto estará en el grupo 1, 2, 3, 4 o 5 dependiendo de qué palabra clave se encontró
    return match[1] || match[2] || match[3] || match[4] || match[5];
  }

  return null;
}

module.exports = {
  detectIntent,
  isTechnicalAnalysisQuestion,
  isPriceQuestion,
  isNewsQuestion,
  isFundamentalAnalysisQuestion,
  isPredictionQuestion,
  isExplanationQuestion,
  detectTimeframe,
  detectIndicators,
  detectNewsTopic,
  detectPredictionTimeframe,
  detectConcept
};
