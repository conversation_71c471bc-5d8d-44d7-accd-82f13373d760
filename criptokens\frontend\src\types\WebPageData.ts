/**
 * Interfaz para los datos de una página web visualizada
 */
export interface WebPageData {
  /** URL de la página web */
  url: string;

  /** Título de la página web (si está disponible) */
  title?: string;

  /** Captura de pantalla de la página web en formato base64 */
  screenshot?: string;

  /** Contenido estructurado de la página web */
  content?: {
    /** Encabezados de la página */
    headings?: Array<{level: number, text: string}>;

    /** Párrafos de la página */
    paragraphs?: string[];

    /** Listas de la página */
    lists?: Array<{type: string, items: string[]}>;

    /** Tablas de la página */
    tables?: Array<{headers: string[], rows: string[][]}>;

    /** Enlaces importantes de la página */
    links?: Array<{text: string, href: string}>;

    /** Texto plano de la página */
    plainText?: string;

    /** Datos de criptomonedas detectados */
    cryptoData?: {
      price?: string;
      priceChange?: string;
      marketCap?: string;
    };
  };

  /** Texto extraído de la página web */
  extractedText?: string;

  /** Resumen generado por el LLM basado en el contenido */
  summary?: string;

  /** Timestamp de cuando se visualizó la página */
  timestamp?: string;

  /** ID de sesión para navegación interactiva */
  sessionId?: string;

  /** Índice actual en el historial de navegación */
  historyIndex?: number;

  /** Longitud total del historial de navegación */
  historyLength?: number;
}
