import { auth, db } from '../firebase-init';
import { collection, getDocs } from 'firebase/firestore';

// Función para verificar la conexión con Firebase Auth
export const testFirebaseAuth = async () => {
  try {
    const currentUser = auth.currentUser;
    console.log('Estado de autenticación:', currentUser ? 'Autenticado' : 'No autenticado');
    return { success: true, user: currentUser };
  } catch (error) {
    console.error('Error al verificar la autenticación:', error);
    return { success: false, error };
  }
};

// Función para verificar la conexión con Firestore
export const testFirestore = async () => {
  try {
    // Probar la conexión con las colecciones que estamos utilizando
    const usersSnapshot = await getDocs(collection(db, 'Users'));
    const portfolioSnapshot = await getDocs(collection(db, 'Portafolio'));

    console.log('Conexión a Firestore exitosa');
    console.log('Documentos en Users:', usersSnapshot.size);
    console.log('Documentos en Portafolio:', portfolioSnapshot.size);

    // Mostrar los documentos encontrados
    console.log('Documentos en Users:');
    usersSnapshot.forEach(doc => {
      console.log(doc.id, ' => ', doc.data());
    });

    console.log('Documentos en Portafolio:');
    portfolioSnapshot.forEach(doc => {
      console.log(doc.id, ' => ', doc.data());
    });

    return {
      success: true,
      usersCount: usersSnapshot.size,
      portfolioCount: portfolioSnapshot.size,
      totalCount: usersSnapshot.size + portfolioSnapshot.size
    };
  } catch (error) {
    console.error('Error al conectar con Firestore:', error);
    return { success: false, error };
  }
};
