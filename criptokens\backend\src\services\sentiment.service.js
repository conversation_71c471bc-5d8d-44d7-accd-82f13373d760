/**
 * Servicio para análisis de sentimiento del mercado de criptomonedas
 * 
 * Este servicio proporciona funciones para analizar el sentimiento del mercado
 * basado en noticias, redes sociales y datos de mercado.
 */

const axios = require('axios');
const { searchNews } = require('./brave.service');
const { visualizeWebPage } = require('./playwright-mcp.service');

// Palabras clave positivas y negativas para análisis simple de sentimiento
const SENTIMENT_KEYWORDS = {
  positive: [
    'bullish', 'rally', 'surge', 'soar', 'gain', 'rise', 'climb', 'jump', 'recover',
    'breakthrough', 'adoption', 'partnership', 'launch', 'upgrade', 'innovation',
    'growth', 'opportunity', 'potential', 'success', 'profit', 'optimistic',
    'confidence', 'support', 'strength', 'momentum', 'progress', 'development',
    'improvement', 'advance', 'milestone', 'achievement', 'positive', 'promising'
  ],
  negative: [
    'bearish', 'crash', 'plunge', 'tumble', 'drop', 'fall', 'decline', 'slump', 'correction',
    'sell-off', 'dump', 'fear', 'risk', 'concern', 'warning', 'threat', 'problem',
    'issue', 'challenge', 'difficulty', 'uncertainty', 'volatility', 'regulation',
    'ban', 'restriction', 'hack', 'scam', 'fraud', 'attack', 'vulnerability',
    'criticism', 'skepticism', 'doubt', 'negative', 'pessimistic', 'weakness'
  ],
  neutral: [
    'stable', 'steady', 'unchanged', 'flat', 'consolidation', 'sideways',
    'range-bound', 'equilibrium', 'balance', 'neutral', 'mixed', 'moderate',
    'average', 'normal', 'typical', 'standard', 'expected', 'anticipated',
    'predicted', 'forecast', 'projection', 'estimate', 'analysis', 'review',
    'assessment', 'evaluation', 'consideration', 'examination', 'study'
  ]
};

/**
 * Analiza el sentimiento de un texto
 * @param {string} text - Texto a analizar
 * @returns {Object} - Análisis de sentimiento
 */
function analyzeSentiment(text) {
  if (!text) return { score: 0, sentiment: 'neutral', confidence: 0 };
  
  const lowerText = text.toLowerCase();
  let positiveCount = 0;
  let negativeCount = 0;
  let neutralCount = 0;
  
  // Contar palabras clave
  SENTIMENT_KEYWORDS.positive.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = lowerText.match(regex);
    if (matches) positiveCount += matches.length;
  });
  
  SENTIMENT_KEYWORDS.negative.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = lowerText.match(regex);
    if (matches) negativeCount += matches.length;
  });
  
  SENTIMENT_KEYWORDS.neutral.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = lowerText.match(regex);
    if (matches) neutralCount += matches.length;
  });
  
  // Calcular puntuación de sentimiento (-1 a 1)
  const total = positiveCount + negativeCount + neutralCount;
  if (total === 0) return { score: 0, sentiment: 'neutral', confidence: 0 };
  
  const score = (positiveCount - negativeCount) / total;
  
  // Determinar sentimiento
  let sentiment = 'neutral';
  if (score > 0.2) sentiment = 'positive';
  if (score < -0.2) sentiment = 'negative';
  
  // Calcular confianza
  const confidence = Math.abs(score);
  
  return {
    score: parseFloat(score.toFixed(2)),
    sentiment,
    confidence: parseFloat(confidence.toFixed(2)),
    details: {
      positiveCount,
      negativeCount,
      neutralCount,
      total
    }
  };
}

/**
 * Analiza el sentimiento de noticias recientes sobre una criptomoneda
 * @param {string} crypto - Nombre de la criptomoneda
 * @param {number} count - Número de noticias a analizar
 * @returns {Promise<Object>} - Análisis de sentimiento
 */
async function analyzeNewsSentiment(crypto, count = 10) {
  try {
    console.log(`Analizando sentimiento de noticias para ${crypto}...`);
    
    // Buscar noticias recientes
    const newsResults = await searchNews(`${crypto} cryptocurrency`, count, 'pd');
    
    if (!newsResults || !newsResults.results || newsResults.results.length === 0) {
      return {
        sentiment: 'neutral',
        score: 0,
        confidence: 0,
        sources: 0,
        latestNews: []
      };
    }
    
    // Analizar sentimiento de cada noticia
    const newsWithSentiment = newsResults.results.map(news => {
      const combinedText = `${news.title} ${news.description}`;
      const sentiment = analyzeSentiment(combinedText);
      
      return {
        title: news.title,
        url: news.url,
        date: news.published_date || 'Desconocido',
        source: news.source || 'Desconocido',
        sentiment: sentiment.sentiment,
        score: sentiment.score
      };
    });
    
    // Calcular sentimiento general
    const totalScore = newsWithSentiment.reduce((sum, news) => sum + news.score, 0);
    const averageScore = totalScore / newsWithSentiment.length;
    
    // Determinar sentimiento general
    let overallSentiment = 'neutral';
    if (averageScore > 0.2) overallSentiment = 'positive';
    if (averageScore < -0.2) overallSentiment = 'negative';
    
    // Calcular confianza
    const confidence = Math.abs(averageScore);
    
    return {
      sentiment: overallSentiment,
      score: parseFloat(averageScore.toFixed(2)),
      confidence: parseFloat(confidence.toFixed(2)),
      sources: newsWithSentiment.length,
      latestNews: newsWithSentiment.slice(0, 5)
    };
  } catch (error) {
    console.error(`Error al analizar sentimiento de noticias para ${crypto}:`, error);
    return {
      sentiment: 'neutral',
      score: 0,
      confidence: 0,
      sources: 0,
      error: error.message,
      latestNews: []
    };
  }
}

/**
 * Analiza el sentimiento de una página web específica
 * @param {string} url - URL de la página web
 * @returns {Promise<Object>} - Análisis de sentimiento
 */
async function analyzeWebPageSentiment(url) {
  try {
    console.log(`Analizando sentimiento de página web: ${url}`);
    
    // Visualizar la página web
    const pageData = await visualizeWebPage(url);
    
    if (!pageData || !pageData.text) {
      return {
        sentiment: 'neutral',
        score: 0,
        confidence: 0,
        url,
        title: pageData?.title || url
      };
    }
    
    // Analizar sentimiento del texto
    const sentiment = analyzeSentiment(pageData.text);
    
    return {
      ...sentiment,
      url,
      title: pageData.title || url,
      textLength: pageData.text.length
    };
  } catch (error) {
    console.error(`Error al analizar sentimiento de página web ${url}:`, error);
    return {
      sentiment: 'neutral',
      score: 0,
      confidence: 0,
      url,
      error: error.message
    };
  }
}

/**
 * Analiza el sentimiento general del mercado de criptomonedas
 * @returns {Promise<Object>} - Análisis de sentimiento del mercado
 */
async function analyzeMarketSentiment() {
  try {
    console.log('Analizando sentimiento general del mercado de criptomonedas...');
    
    // Analizar sentimiento de las principales criptomonedas
    const cryptos = ['Bitcoin', 'Ethereum', 'Solana', 'Cardano', 'Binance Coin'];
    
    const sentimentResults = await Promise.all(
      cryptos.map(async crypto => {
        const sentiment = await analyzeNewsSentiment(crypto, 5);
        return {
          crypto,
          ...sentiment
        };
      })
    );
    
    // Calcular sentimiento general del mercado
    const totalScore = sentimentResults.reduce((sum, result) => sum + result.score, 0);
    const averageScore = totalScore / sentimentResults.length;
    
    // Determinar sentimiento general
    let overallSentiment = 'neutral';
    if (averageScore > 0.2) overallSentiment = 'positive';
    if (averageScore < -0.2) overallSentiment = 'negative';
    
    // Calcular confianza
    const confidence = Math.abs(averageScore);
    
    // Obtener noticias generales del mercado
    const marketNews = await searchNews('cryptocurrency market', 5, 'pd');
    
    // Analizar sentimiento de noticias generales
    const marketNewsSentiment = marketNews && marketNews.results ? 
      marketNews.results.map(news => {
        const combinedText = `${news.title} ${news.description}`;
        const sentiment = analyzeSentiment(combinedText);
        
        return {
          title: news.title,
          url: news.url,
          date: news.published_date || 'Desconocido',
          source: news.source || 'Desconocido',
          sentiment: sentiment.sentiment,
          score: sentiment.score
        };
      }) : [];
    
    return {
      marketSentiment: overallSentiment,
      score: parseFloat(averageScore.toFixed(2)),
      confidence: parseFloat(confidence.toFixed(2)),
      cryptoSentiments: sentimentResults,
      marketNews: marketNewsSentiment.slice(0, 5),
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error al analizar sentimiento del mercado:', error);
    return {
      marketSentiment: 'neutral',
      score: 0,
      confidence: 0,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Analiza el sentimiento de un tema específico relacionado con criptomonedas
 * @param {string} topic - Tema a analizar
 * @param {number} count - Número de fuentes a analizar
 * @returns {Promise<Object>} - Análisis de sentimiento
 */
async function analyzeTopicSentiment(topic, count = 10) {
  try {
    console.log(`Analizando sentimiento para el tema: ${topic}`);
    
    // Buscar noticias sobre el tema
    const newsResults = await searchNews(`${topic} cryptocurrency`, count, 'pd');
    
    if (!newsResults || !newsResults.results || newsResults.results.length === 0) {
      return {
        sentiment: 'neutral',
        score: 0,
        confidence: 0,
        sources: 0,
        topic,
        latestNews: []
      };
    }
    
    // Analizar sentimiento de cada noticia
    const newsWithSentiment = newsResults.results.map(news => {
      const combinedText = `${news.title} ${news.description}`;
      const sentiment = analyzeSentiment(combinedText);
      
      return {
        title: news.title,
        url: news.url,
        date: news.published_date || 'Desconocido',
        source: news.source || 'Desconocido',
        sentiment: sentiment.sentiment,
        score: sentiment.score
      };
    });
    
    // Calcular sentimiento general
    const totalScore = newsWithSentiment.reduce((sum, news) => sum + news.score, 0);
    const averageScore = totalScore / newsWithSentiment.length;
    
    // Determinar sentimiento general
    let overallSentiment = 'neutral';
    if (averageScore > 0.2) overallSentiment = 'positive';
    if (averageScore < -0.2) overallSentiment = 'negative';
    
    // Calcular confianza
    const confidence = Math.abs(averageScore);
    
    // Analizar una página web relevante para obtener más contexto
    let webPageSentiment = null;
    if (newsWithSentiment.length > 0) {
      try {
        const topNewsUrl = newsWithSentiment[0].url;
        webPageSentiment = await analyzeWebPageSentiment(topNewsUrl);
      } catch (error) {
        console.error('Error al analizar página web:', error);
      }
    }
    
    return {
      topic,
      sentiment: overallSentiment,
      score: parseFloat(averageScore.toFixed(2)),
      confidence: parseFloat(confidence.toFixed(2)),
      sources: newsWithSentiment.length,
      latestNews: newsWithSentiment.slice(0, 5),
      webPageAnalysis: webPageSentiment,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error al analizar sentimiento para el tema ${topic}:`, error);
    return {
      topic,
      sentiment: 'neutral',
      score: 0,
      confidence: 0,
      sources: 0,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = {
  analyzeSentiment,
  analyzeNewsSentiment,
  analyzeWebPageSentiment,
  analyzeMarketSentiment,
  analyzeTopicSentiment
};
