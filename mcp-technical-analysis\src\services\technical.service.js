/**
 * Servicio de Análisis Técnico
 *
 * Este servicio proporciona funciones para calcular indicadores técnicos
 * y detectar patrones en datos de precios de criptomonedas.
 */

const {
  RSI,
  MACD,
  BollingerBands,
  SMA,
  EMA,
  bullish,
  bearish
} = require('technicalindicators');

/**
 * Calcula el RSI (Relative Strength Index)
 * @param {Array<number>} prices - Array de precios de cierre
 * @param {number} period - Período para el cálculo (por defecto 14)
 * @returns {Array<number>} - Valores del RSI
 */
function calculateRSI(prices, period = 14) {
  const input = {
    values: prices,
    period
  };

  return RSI.calculate(input);
}

/**
 * Calcula el MACD (Moving Average Convergence Divergence)
 * @param {Array<number>} prices - Array de precios de cierre
 * @param {number} fastPeriod - Período rápido (por defecto 12)
 * @param {number} slowPeriod - Período lento (por defecto 26)
 * @param {number} signalPeriod - Período de señal (por defecto 9)
 * @returns {Object} - Valores del MACD, señal e histograma
 */
function calculateMACD(prices, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
  const input = {
    values: prices,
    fastPeriod,
    slowPeriod,
    signalPeriod,
    SimpleMAOscillator: false,
    SimpleMASignal: false
  };

  return MACD.calculate(input);
}

/**
 * Calcula las Bandas de Bollinger
 * @param {Array<number>} prices - Array de precios de cierre
 * @param {number} period - Período para el cálculo (por defecto 20)
 * @param {number} stdDev - Desviación estándar (por defecto 2)
 * @returns {Object} - Valores de las bandas superior, media e inferior
 */
function calculateBollingerBands(prices, period = 20, stdDev = 2) {
  const input = {
    values: prices,
    period,
    stdDev
  };

  return BollingerBands.calculate(input);
}

/**
 * Calcula la Media Móvil Simple (SMA)
 * @param {Array<number>} prices - Array de precios de cierre
 * @param {number} period - Período para el cálculo
 * @returns {Array<number>} - Valores de la SMA
 */
function calculateSMA(prices, period) {
  const input = {
    values: prices,
    period
  };

  return SMA.calculate(input);
}

/**
 * Calcula la Media Móvil Exponencial (EMA)
 * @param {Array<number>} prices - Array de precios de cierre
 * @param {number} period - Período para el cálculo
 * @returns {Array<number>} - Valores de la EMA
 */
function calculateEMA(prices, period) {
  const input = {
    values: prices,
    period
  };

  return EMA.calculate(input);
}

/**
 * Detecta patrones de velas
 * @param {Array<Object>} candles - Array de velas (OHLC)
 * @returns {Object} - Patrones detectados
 */
function detectCandlePatterns(candles) {
  // Preparar los datos en el formato requerido
  const open = candles.map(c => c.open);
  const high = candles.map(c => c.high);
  const low = candles.map(c => c.low);
  const close = candles.map(c => c.close);

  // Patrones disponibles en la biblioteca
  const patterns = {
    bullish: {},
    bearish: {}
  };

  // Verificar qué patrones están disponibles en la biblioteca
  if (typeof bullish !== 'undefined') {
    // Patrones alcistas
    if (typeof bullish.engulfingPattern === 'function') {
      patterns.bullish.bullishEngulfing = bullish.engulfingPattern({ open, high, low, close });
    }

    // Añadir más patrones si están disponibles
  }

  if (typeof bearish !== 'undefined') {
    // Patrones bajistas
    if (typeof bearish.engulfingPattern === 'function') {
      patterns.bearish.bearishEngulfing = bearish.engulfingPattern({ open, high, low, close });
    }

    // Añadir más patrones si están disponibles
  }

  // Implementación simplificada de patrones básicos

  // Doji (apertura y cierre muy cercanos)
  const doji = [];
  for (let i = 0; i < candles.length; i++) {
    const c = candles[i];
    const bodySize = Math.abs(c.close - c.open);
    const totalRange = c.high - c.low;
    doji.push(bodySize / totalRange < 0.1); // Si el cuerpo es menos del 10% del rango total
  }
  patterns.bullish.doji = doji;

  // Tendencia alcista (3 velas consecutivas de cierre superior)
  const bullishTrend = [];
  for (let i = 0; i < candles.length; i++) {
    if (i >= 2) {
      bullishTrend.push(
        candles[i].close > candles[i-1].close &&
        candles[i-1].close > candles[i-2].close
      );
    } else {
      bullishTrend.push(false);
    }
  }
  patterns.bullish.bullishTrend = bullishTrend;

  // Tendencia bajista (3 velas consecutivas de cierre inferior)
  const bearishTrend = [];
  for (let i = 0; i < candles.length; i++) {
    if (i >= 2) {
      bearishTrend.push(
        candles[i].close < candles[i-1].close &&
        candles[i-1].close < candles[i-2].close
      );
    } else {
      bearishTrend.push(false);
    }
  }
  patterns.bearish.bearishTrend = bearishTrend;

  return patterns;
}

/**
 * Realiza un análisis técnico completo
 * @param {Array<Object>} candles - Array de velas (OHLC)
 * @returns {Object} - Resultados del análisis
 */
function performFullAnalysis(candles) {
  const prices = candles.map(c => c.close);

  // Calcular indicadores
  const rsi = calculateRSI(prices);
  const macd = calculateMACD(prices);
  const bollingerBands = calculateBollingerBands(prices);
  const sma50 = calculateSMA(prices, 50);
  const sma200 = calculateSMA(prices, 200);

  // Detectar patrones
  const patterns = detectCandlePatterns(candles);

  // Generar señales
  const signals = generateSignals(prices, rsi, macd, bollingerBands, sma50, sma200, patterns);

  return {
    indicators: {
      rsi: rsi[rsi.length - 1],
      macd: {
        macd: macd[macd.length - 1]?.MACD,
        signal: macd[macd.length - 1]?.signal,
        histogram: macd[macd.length - 1]?.histogram
      },
      bollingerBands: {
        upper: bollingerBands[bollingerBands.length - 1]?.upper,
        middle: bollingerBands[bollingerBands.length - 1]?.middle,
        lower: bollingerBands[bollingerBands.length - 1]?.lower
      },
      sma50: sma50[sma50.length - 1],
      sma200: sma200[sma200.length - 1]
    },
    patterns,
    signals
  };
}

/**
 * Genera señales de trading basadas en indicadores y patrones
 * @param {Array<number>} prices - Array de precios de cierre
 * @param {Array<number>} rsi - Valores del RSI
 * @param {Array<Object>} macd - Valores del MACD
 * @param {Array<Object>} bollingerBands - Valores de las Bandas de Bollinger
 * @param {Array<number>} sma50 - Valores de la SMA de 50 períodos
 * @param {Array<number>} sma200 - Valores de la SMA de 200 períodos
 * @param {Object} patterns - Patrones detectados
 * @returns {Object} - Señales generadas
 */
function generateSignals(prices, rsi, macd, bollingerBands, sma50, sma200, patterns) {
  const currentPrice = prices[prices.length - 1];
  const signals = {
    buy: [],
    sell: [],
    neutral: []
  };

  // Señales de RSI
  const currentRSI = rsi[rsi.length - 1];
  if (currentRSI < 30) {
    signals.buy.push('RSI en zona de sobreventa (< 30)');
  } else if (currentRSI > 70) {
    signals.sell.push('RSI en zona de sobrecompra (> 70)');
  } else {
    signals.neutral.push('RSI en zona neutral');
  }

  // Señales de MACD
  const currentMACD = macd[macd.length - 1];
  const previousMACD = macd[macd.length - 2];
  if (currentMACD && previousMACD) {
    if (currentMACD.MACD > currentMACD.signal && previousMACD.MACD <= previousMACD.signal) {
      signals.buy.push('Cruce alcista del MACD');
    } else if (currentMACD.MACD < currentMACD.signal && previousMACD.MACD >= previousMACD.signal) {
      signals.sell.push('Cruce bajista del MACD');
    }
  }

  // Señales de Bandas de Bollinger
  const currentBB = bollingerBands[bollingerBands.length - 1];
  if (currentBB) {
    if (currentPrice <= currentBB.lower) {
      signals.buy.push('Precio tocando la banda inferior de Bollinger');
    } else if (currentPrice >= currentBB.upper) {
      signals.sell.push('Precio tocando la banda superior de Bollinger');
    }
  }

  // Señales de cruce de medias móviles
  const currentSMA50 = sma50[sma50.length - 1];
  const currentSMA200 = sma200[sma200.length - 1];
  const previousSMA50 = sma50[sma50.length - 2];
  const previousSMA200 = sma200[sma200.length - 2];

  if (currentSMA50 && currentSMA200 && previousSMA50 && previousSMA200) {
    if (currentSMA50 > currentSMA200 && previousSMA50 <= previousSMA200) {
      signals.buy.push('Cruce dorado (SMA 50 cruza por encima de SMA 200)');
    } else if (currentSMA50 < currentSMA200 && previousSMA50 >= previousSMA200) {
      signals.sell.push('Cruce de la muerte (SMA 50 cruza por debajo de SMA 200)');
    }
  }

  // Señales de patrones de velas
  let hasBullishPattern = false;
  let hasBearishPattern = false;

  // Verificar patrones alcistas
  Object.entries(patterns.bullish).forEach(([pattern, values]) => {
    const lastValue = values[values.length - 1];
    if (lastValue) {
      signals.buy.push(`Patrón alcista detectado: ${formatPatternName(pattern)}`);
      hasBullishPattern = true;
    }
  });

  // Verificar patrones bajistas
  Object.entries(patterns.bearish).forEach(([pattern, values]) => {
    const lastValue = values[values.length - 1];
    if (lastValue) {
      signals.sell.push(`Patrón bajista detectado: ${formatPatternName(pattern)}`);
      hasBearishPattern = true;
    }
  });

  // Generar recomendación general
  let recommendation = 'NEUTRAL';
  const buySignals = signals.buy.length;
  const sellSignals = signals.sell.length;

  if (buySignals > sellSignals && buySignals >= 2) {
    recommendation = 'COMPRA';
  } else if (sellSignals > buySignals && sellSignals >= 2) {
    recommendation = 'VENTA';
  }

  return {
    recommendation,
    buySignals,
    sellSignals,
    neutralSignals: signals.neutral.length,
    details: signals
  };
}

/**
 * Formatea el nombre de un patrón para hacerlo más legible
 * @param {string} pattern - Nombre del patrón en camelCase
 * @returns {string} - Nombre formateado
 */
function formatPatternName(pattern) {
  return pattern
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase());
}

module.exports = {
  calculateRSI,
  calculateMACD,
  calculateBollingerBands,
  calculateSMA,
  calculateEMA,
  detectCandlePatterns,
  performFullAnalysis
};
