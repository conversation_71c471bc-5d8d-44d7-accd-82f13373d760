import React from 'react';
import { SentimentAnalysis, MarketSentiment } from '../../types/dashboard';
import '../../styles/dashboard/MarketSentimentWidget.css';

interface MarketSentimentWidgetProps {
  sentimentAnalysis: SentimentAnalysis | null;
  isLoading: boolean;
}

const MarketSentimentWidget: React.FC<MarketSentimentWidgetProps> = ({ 
  sentimentAnalysis,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="market-sentiment-widget loading" data-testid="market-sentiment-loading">
        <div className="widget-header">
          <h3>Sentimiento del Mercado</h3>
        </div>
        <div className="widget-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  if (!sentimentAnalysis) {
    return (
      <div className="market-sentiment-widget error" data-testid="market-sentiment-error">
        <div className="widget-header">
          <h3>Sentimiento del Mercado</h3>
        </div>
        <div className="widget-content">
          <div className="error-message">
            <i className="fas fa-exclamation-circle"></i>
            <p>No se pudo cargar el análisis de sentimiento.</p>
          </div>
        </div>
      </div>
    );
  }

  const { 
    overallSentiment, 
    fearGreedIndex, 
    sentimentFactors, 
    sentimentTrend 
  } = sentimentAnalysis;

  return (
    <div className="market-sentiment-widget" data-testid="market-sentiment-widget">
      <div className="widget-header">
        <h3>Sentimiento del Mercado</h3>
        <div className="sentiment-icon">
          <i className={`fas ${getSentimentIcon(overallSentiment)}`}></i>
        </div>
      </div>
      <div className="widget-content">
        <div className="sentiment-summary">
          <div className="sentiment-value">
            <span className={`sentiment-label ${overallSentiment}`}>
              {getSentimentLabel(overallSentiment)}
            </span>
            <span className="sentiment-index">{fearGreedIndex}</span>
          </div>
          <div className="sentiment-trend">
            <i className={`fas ${getTrendIcon(sentimentTrend)}`}></i>
            <span>{getTrendLabel(sentimentTrend)}</span>
          </div>
        </div>

        <div className="sentiment-meter">
          <div className="meter-labels">
            <span className="fear-label">Miedo Extremo</span>
            <span className="greed-label">Codicia Extrema</span>
          </div>
          <div className="meter-bar">
            <div 
              className="meter-indicator" 
              style={{ 
                left: `${fearGreedIndex}%`,
                backgroundColor: getSentimentColor(overallSentiment)
              }}
            ></div>
          </div>
        </div>
        
        <div className="sentiment-factors">
          <h4>Factores de Sentimiento</h4>
          <div className="factors-grid">
            <div className="factor">
              <div className="factor-label">Impulso</div>
              <div className="factor-value">
                <div className="factor-bar">
                  <div 
                    className={`factor-indicator ${sentimentFactors.marketMomentum >= 0 ? 'positive' : 'negative'}`}
                    style={{ 
                      width: `${Math.abs(sentimentFactors.marketMomentum)}%`,
                      marginLeft: sentimentFactors.marketMomentum >= 0 ? '50%' : `${50 - Math.abs(sentimentFactors.marketMomentum)}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="factor">
              <div className="factor-label">Volatilidad</div>
              <div className="factor-value">
                <div className="factor-bar">
                  <div 
                    className="factor-indicator"
                    style={{ width: `${sentimentFactors.volatility}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="factor">
              <div className="factor-label">Dominancia BTC</div>
              <div className="factor-value">
                <div className="factor-bar">
                  <div 
                    className="factor-indicator"
                    style={{ width: `${sentimentFactors.btcDominance}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="factor">
              <div className="factor-label">Redes Sociales</div>
              <div className="factor-value">
                <div className="factor-bar">
                  <div 
                    className={`factor-indicator ${sentimentFactors.socialMediaSentiment >= 0 ? 'positive' : 'negative'}`}
                    style={{ 
                      width: `${Math.abs(sentimentFactors.socialMediaSentiment)}%`,
                      marginLeft: sentimentFactors.socialMediaSentiment >= 0 ? '50%' : `${50 - Math.abs(sentimentFactors.socialMediaSentiment)}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="factor">
              <div className="factor-label">Volumen</div>
              <div className="factor-value">
                <div className="factor-bar">
                  <div 
                    className="factor-indicator"
                    style={{ width: `${sentimentFactors.tradingVolume}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="sentiment-advice">
          <p>{getSentimentAdvice(overallSentiment, sentimentTrend)}</p>
        </div>
      </div>
    </div>
  );
};

// Funciones auxiliares
const getSentimentIcon = (sentiment: MarketSentiment): string => {
  switch (sentiment) {
    case 'extreme_fear':
      return 'fa-face-dizzy';
    case 'fear':
      return 'fa-face-frown';
    case 'neutral':
      return 'fa-face-meh';
    case 'greed':
      return 'fa-face-smile';
    case 'extreme_greed':
      return 'fa-face-grin-stars';
    default:
      return 'fa-face-meh';
  }
};

const getSentimentLabel = (sentiment: MarketSentiment): string => {
  switch (sentiment) {
    case 'extreme_fear':
      return 'Miedo Extremo';
    case 'fear':
      return 'Miedo';
    case 'neutral':
      return 'Neutral';
    case 'greed':
      return 'Codicia';
    case 'extreme_greed':
      return 'Codicia Extrema';
    default:
      return 'Neutral';
  }
};

const getSentimentColor = (sentiment: MarketSentiment): string => {
  switch (sentiment) {
    case 'extreme_fear':
      return '#e74c3c';
    case 'fear':
      return '#e67e22';
    case 'neutral':
      return '#f1c40f';
    case 'greed':
      return '#2ecc71';
    case 'extreme_greed':
      return '#27ae60';
    default:
      return '#f1c40f';
  }
};

const getTrendIcon = (trend: 'improving' | 'stable' | 'worsening'): string => {
  switch (trend) {
    case 'improving':
      return 'fa-arrow-trend-up';
    case 'stable':
      return 'fa-arrow-right';
    case 'worsening':
      return 'fa-arrow-trend-down';
    default:
      return 'fa-arrow-right';
  }
};

const getTrendLabel = (trend: 'improving' | 'stable' | 'worsening'): string => {
  switch (trend) {
    case 'improving':
      return 'Mejorando';
    case 'stable':
      return 'Estable';
    case 'worsening':
      return 'Empeorando';
    default:
      return 'Estable';
  }
};

const getSentimentAdvice = (
  sentiment: MarketSentiment, 
  trend: 'improving' | 'stable' | 'worsening'
): string => {
  if (sentiment === 'extreme_fear') {
    return trend === 'improving'
      ? 'El miedo extremo está comenzando a disiparse. Históricamente, estos momentos han presentado buenas oportunidades de compra para inversores a largo plazo.'
      : 'El mercado muestra miedo extremo. Históricamente, estos momentos han presentado buenas oportunidades de compra para inversores a largo plazo, pero procede con cautela.';
  } else if (sentiment === 'fear') {
    return 'El mercado muestra miedo. Considera evaluar oportunidades de compra en activos fundamentalmente sólidos, especialmente si tienes un horizonte de inversión a largo plazo.';
  } else if (sentiment === 'neutral') {
    return 'El mercado está en equilibrio. Buen momento para revisar tu estrategia y asegurarte de que tu portafolio está alineado con tus objetivos.';
  } else if (sentiment === 'greed') {
    return 'El mercado muestra codicia. Considera ser más selectivo con nuevas inversiones y evalúa si es momento de asegurar algunas ganancias.';
  } else if (sentiment === 'extreme_greed') {
    return trend === 'worsening'
      ? 'La codicia extrema está aumentando. Históricamente, estos momentos han precedido a correcciones. Considera reducir exposición y asegurar ganancias.'
      : 'El mercado muestra codicia extrema. Históricamente, estos momentos han precedido a correcciones. Considera ser cauteloso y potencialmente asegurar algunas ganancias.';
  }
  
  return 'Evalúa el mercado en función de tus objetivos de inversión y horizonte temporal.';
};

export default MarketSentimentWidget;
