/**
 * Adaptador para el servidor MCP de Memory (interno)
 * 
 * Este adaptador proporciona funcionalidades de memoria persistente
 * implementadas internamente sin necesidad de un servidor MCP externo.
 */

const fs = require('fs').promises;
const path = require('path');
const BaseMcpAdapter = require('./base-mcp-adapter');
const logger = require('../utils/logger');
const { McpServerError } = require('../utils/error-handler');
const { generateId } = require('../utils/helpers');

class MemoryMcpAdapter extends BaseMcpAdapter {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   * @param {string} [options.memoryDir] - Directorio para almacenar las memorias
   */
  constructor(options = {}) {
    super('memory', { baseUrl: 'internal', ...options });
    
    // Directorio para almacenar las memorias
    this.memoryDir = options.memoryDir || path.join(process.cwd(), 'agents-mcp', 'data', 'memory');
    
    // Archivo de índice de memorias
    this.indexFile = path.join(this.memoryDir, 'memory-index.json');
    
    // Caché de índice de memorias
    this.memoryIndex = null;
    
    // Crear el directorio de memorias si no existe
    this._ensureMemoryDir();
  }
  
  /**
   * Asegura que el directorio de memorias exista
   * @private
   */
  async _ensureMemoryDir() {
    try {
      await fs.mkdir(this.memoryDir, { recursive: true });
      logger.debug('MemoryMcpAdapter', `Directorio de memorias creado: ${this.memoryDir}`);
    } catch (error) {
      logger.error('MemoryMcpAdapter', `Error al crear directorio de memorias: ${error.message}`);
    }
  }
  
  /**
   * Carga el índice de memorias
   * @returns {Promise<Object>} Índice de memorias
   * @private
   */
  async _loadMemoryIndex() {
    try {
      // Si ya tenemos el índice en caché, devolverlo
      if (this.memoryIndex) {
        return this.memoryIndex;
      }
      
      // Intentar leer el archivo de índice
      try {
        const indexData = await fs.readFile(this.indexFile, 'utf8');
        this.memoryIndex = JSON.parse(indexData);
      } catch (error) {
        // Si el archivo no existe, crear un índice vacío
        this.memoryIndex = {
          memories: {},
          tags: {},
          lastUpdated: new Date().toISOString()
        };
        
        // Guardar el índice vacío
        await this._saveMemoryIndex();
      }
      
      return this.memoryIndex;
    } catch (error) {
      throw new McpServerError(
        `Error al cargar índice de memorias: ${error.message}`,
        {},
        error
      );
    }
  }
  
  /**
   * Guarda el índice de memorias
   * @returns {Promise<void>}
   * @private
   */
  async _saveMemoryIndex() {
    try {
      if (!this.memoryIndex) {
        return;
      }
      
      // Actualizar la fecha de última actualización
      this.memoryIndex.lastUpdated = new Date().toISOString();
      
      // Guardar el índice
      await fs.writeFile(this.indexFile, JSON.stringify(this.memoryIndex, null, 2), 'utf8');
      
      logger.debug('MemoryMcpAdapter', 'Índice de memorias guardado');
    } catch (error) {
      throw new McpServerError(
        `Error al guardar índice de memorias: ${error.message}`,
        {},
        error
      );
    }
  }
  
  /**
   * Ejecuta una herramienta interna
   * @param {string} toolName - Nombre de la herramienta
   * @param {Object} input - Parámetros de entrada
   * @returns {Promise<any>} Resultado de la ejecución
   * @private
   */
  async _executeInternalTool(toolName, input) {
    switch (toolName) {
      case 'storeMemory':
        return await this._storeMemory(input.content, input.tags, input.metadata);
      case 'retrieveMemory':
        return await this._retrieveMemory(input.id);
      case 'searchMemories':
        return await this._searchMemories(input.query, input.tags, input.limit);
      case 'listMemories':
        return await this._listMemories(input.tags, input.limit, input.offset);
      case 'deleteMemory':
        return await this._deleteMemory(input.id);
      default:
        throw new McpServerError(
          `La herramienta ${toolName} no está implementada para el servidor MCP de Memory`
        );
    }
  }
  
  /**
   * Almacena una memoria
   * @param {string} content - Contenido de la memoria
   * @param {string[]} [tags=[]] - Etiquetas para categorizar la memoria
   * @param {Object} [metadata={}] - Metadatos adicionales
   * @returns {Promise<Object>} Información de la memoria almacenada
   * @private
   */
  async _storeMemory(content, tags = [], metadata = {}) {
    try {
      // Cargar el índice de memorias
      const index = await this._loadMemoryIndex();
      
      // Generar un ID único para la memoria
      const memoryId = generateId('mem-');
      
      // Crear el objeto de memoria
      const memory = {
        id: memoryId,
        content: content,
        tags: Array.isArray(tags) ? tags : [],
        metadata: metadata || {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Guardar la memoria en el índice
      index.memories[memoryId] = memory;
      
      // Actualizar las etiquetas
      for (const tag of memory.tags) {
        if (!index.tags[tag]) {
          index.tags[tag] = [];
        }
        
        if (!index.tags[tag].includes(memoryId)) {
          index.tags[tag].push(memoryId);
        }
      }
      
      // Guardar el índice
      await this._saveMemoryIndex();
      
      logger.debug('MemoryMcpAdapter', `Memoria almacenada con ID: ${memoryId}`);
      
      return memory;
    } catch (error) {
      throw new McpServerError(
        `Error al almacenar memoria: ${error.message}`,
        { content, tags, metadata },
        error
      );
    }
  }
  
  /**
   * Recupera una memoria por su ID
   * @param {string} id - ID de la memoria
   * @returns {Promise<Object>} Memoria recuperada
   * @private
   */
  async _retrieveMemory(id) {
    try {
      // Cargar el índice de memorias
      const index = await this._loadMemoryIndex();
      
      // Verificar que la memoria exista
      if (!index.memories[id]) {
        throw new McpServerError(
          `No se encontró la memoria con ID: ${id}`
        );
      }
      
      logger.debug('MemoryMcpAdapter', `Memoria recuperada con ID: ${id}`);
      
      return index.memories[id];
    } catch (error) {
      throw new McpServerError(
        `Error al recuperar memoria: ${error.message}`,
        { id },
        error
      );
    }
  }
  
  /**
   * Busca memorias por contenido y/o etiquetas
   * @param {string} [query] - Consulta de búsqueda en el contenido
   * @param {string[]} [tags] - Etiquetas para filtrar
   * @param {number} [limit=10] - Número máximo de resultados
   * @returns {Promise<Object[]>} Memorias encontradas
   * @private
   */
  async _searchMemories(query, tags, limit = 10) {
    try {
      // Cargar el índice de memorias
      const index = await this._loadMemoryIndex();
      
      // Obtener todas las memorias
      const allMemories = Object.values(index.memories);
      
      // Filtrar por etiquetas si se proporcionan
      let filteredMemories = allMemories;
      
      if (tags && Array.isArray(tags) && tags.length > 0) {
        filteredMemories = filteredMemories.filter(memory => {
          return tags.some(tag => memory.tags.includes(tag));
        });
      }
      
      // Filtrar por consulta si se proporciona
      if (query && typeof query === 'string' && query.trim() !== '') {
        const normalizedQuery = query.toLowerCase();
        
        filteredMemories = filteredMemories.filter(memory => {
          return memory.content.toLowerCase().includes(normalizedQuery);
        });
      }
      
      // Ordenar por fecha de actualización (más reciente primero)
      filteredMemories.sort((a, b) => {
        return new Date(b.updatedAt) - new Date(a.updatedAt);
      });
      
      // Limitar el número de resultados
      const limitedMemories = filteredMemories.slice(0, limit);
      
      logger.debug('MemoryMcpAdapter', `Búsqueda de memorias completada. Resultados: ${limitedMemories.length}`);
      
      return {
        query: query || null,
        tags: tags || [],
        total: filteredMemories.length,
        limit: limit,
        memories: limitedMemories
      };
    } catch (error) {
      throw new McpServerError(
        `Error al buscar memorias: ${error.message}`,
        { query, tags, limit },
        error
      );
    }
  }
  
  /**
   * Lista todas las memorias, opcionalmente filtradas por etiquetas
   * @param {string[]} [tags] - Etiquetas para filtrar
   * @param {number} [limit=20] - Número máximo de resultados
   * @param {number} [offset=0] - Desplazamiento para paginación
   * @returns {Promise<Object>} Lista de memorias
   * @private
   */
  async _listMemories(tags, limit = 20, offset = 0) {
    try {
      // Cargar el índice de memorias
      const index = await this._loadMemoryIndex();
      
      // Obtener todas las memorias
      const allMemories = Object.values(index.memories);
      
      // Filtrar por etiquetas si se proporcionan
      let filteredMemories = allMemories;
      
      if (tags && Array.isArray(tags) && tags.length > 0) {
        filteredMemories = filteredMemories.filter(memory => {
          return tags.some(tag => memory.tags.includes(tag));
        });
      }
      
      // Ordenar por fecha de creación (más reciente primero)
      filteredMemories.sort((a, b) => {
        return new Date(b.createdAt) - new Date(a.createdAt);
      });
      
      // Aplicar paginación
      const paginatedMemories = filteredMemories.slice(offset, offset + limit);
      
      logger.debug('MemoryMcpAdapter', `Listado de memorias completado. Total: ${filteredMemories.length}, Mostradas: ${paginatedMemories.length}`);
      
      return {
        total: filteredMemories.length,
        limit: limit,
        offset: offset,
        memories: paginatedMemories
      };
    } catch (error) {
      throw new McpServerError(
        `Error al listar memorias: ${error.message}`,
        { tags, limit, offset },
        error
      );
    }
  }
  
  /**
   * Elimina una memoria
   * @param {string} id - ID de la memoria
   * @returns {Promise<Object>} Resultado de la operación
   * @private
   */
  async _deleteMemory(id) {
    try {
      // Cargar el índice de memorias
      const index = await this._loadMemoryIndex();
      
      // Verificar que la memoria exista
      if (!index.memories[id]) {
        throw new McpServerError(
          `No se encontró la memoria con ID: ${id}`
        );
      }
      
      // Obtener la memoria antes de eliminarla
      const memory = index.memories[id];
      
      // Eliminar la memoria del índice
      delete index.memories[id];
      
      // Eliminar la memoria de las etiquetas
      for (const tag in index.tags) {
        index.tags[tag] = index.tags[tag].filter(memoryId => memoryId !== id);
        
        // Eliminar la etiqueta si no tiene memorias
        if (index.tags[tag].length === 0) {
          delete index.tags[tag];
        }
      }
      
      // Guardar el índice
      await this._saveMemoryIndex();
      
      logger.debug('MemoryMcpAdapter', `Memoria eliminada con ID: ${id}`);
      
      return {
        id: id,
        success: true,
        deletedMemory: memory
      };
    } catch (error) {
      throw new McpServerError(
        `Error al eliminar memoria: ${error.message}`,
        { id },
        error
      );
    }
  }
  
  /**
   * Almacena una memoria
   * @param {string} content - Contenido de la memoria
   * @param {string[]} [tags=[]] - Etiquetas para categorizar la memoria
   * @param {Object} [metadata={}] - Metadatos adicionales
   * @returns {Promise<Object>} Información de la memoria almacenada
   */
  async storeMemory(content, tags = [], metadata = {}) {
    return await this.executeTool('storeMemory', {
      content: content,
      tags: tags,
      metadata: metadata
    });
  }
  
  /**
   * Recupera una memoria por su ID
   * @param {string} id - ID de la memoria
   * @returns {Promise<Object>} Memoria recuperada
   */
  async retrieveMemory(id) {
    return await this.executeTool('retrieveMemory', {
      id: id
    });
  }
  
  /**
   * Busca memorias por contenido y/o etiquetas
   * @param {string} [query] - Consulta de búsqueda en el contenido
   * @param {string[]} [tags] - Etiquetas para filtrar
   * @param {number} [limit=10] - Número máximo de resultados
   * @returns {Promise<Object[]>} Memorias encontradas
   */
  async searchMemories(query, tags, limit = 10) {
    return await this.executeTool('searchMemories', {
      query: query,
      tags: tags,
      limit: limit
    });
  }
  
  /**
   * Lista todas las memorias, opcionalmente filtradas por etiquetas
   * @param {string[]} [tags] - Etiquetas para filtrar
   * @param {number} [limit=20] - Número máximo de resultados
   * @param {number} [offset=0] - Desplazamiento para paginación
   * @returns {Promise<Object>} Lista de memorias
   */
  async listMemories(tags, limit = 20, offset = 0) {
    return await this.executeTool('listMemories', {
      tags: tags,
      limit: limit,
      offset: offset
    });
  }
  
  /**
   * Elimina una memoria
   * @param {string} id - ID de la memoria
   * @returns {Promise<Object>} Resultado de la operación
   */
  async deleteMemory(id) {
    return await this.executeTool('deleteMemory', {
      id: id
    });
  }
}

module.exports = MemoryMcpAdapter;
