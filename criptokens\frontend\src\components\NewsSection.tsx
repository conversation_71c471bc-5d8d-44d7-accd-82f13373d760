import { useState, useEffect } from 'react';
import { getCryptoNews, type NewsArticle } from '../services/news';
import '../styles/NewsSection.css';

const NewsSection = () => {
  const [news, setNews] = useState<NewsArticle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [searchTopic, setSearchTopic] = useState<string>('criptomonedas');
  const [searchInput, setSearchInput] = useState<string>('');

  const fetchNews = async (topic: string) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`Buscando noticias sobre: ${topic}`);
      const newsData = await getCryptoNews(10, topic);
      setNews(newsData);
    } catch (err) {
      console.error('Error al cargar noticias:', err);
      setError('No se pudieron cargar las noticias. Por favor, intenta de nuevo más tarde.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNews(searchTopic);
  }, [searchTopic]);

  // Obtener todas las categorías únicas de las noticias
  const categories = news.reduce((cats: string[], article) => {
    article.categories.forEach(cat => {
      if (!cats.includes(cat)) {
        cats.push(cat);
      }
    });
    return cats;
  }, []).sort();

  // Filtrar noticias por categoría si hay una categoría activa
  const filteredNews = activeCategory
    ? news.filter(article => article.categories.includes(activeCategory))
    : news;

  // Formatear fecha para mostrar
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return <div className="news-loading">Cargando noticias...</div>;
  }

  if (error) {
    return <div className="news-error">{error}</div>;
  }

  // Manejar la búsqueda de noticias
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchInput.trim()) {
      setSearchTopic(searchInput.trim());
      setActiveCategory(null); // Resetear la categoría activa al buscar
    }
  };

  // Manejar el refresco de noticias
  const handleRefresh = () => {
    fetchNews(searchTopic);
  };

  return (
    <div className="news-section">
      <div className="news-header">
        <h2>Noticias de Criptomonedas</h2>

        <div className="news-search">
          <form onSubmit={handleSearch}>
            <input
              type="text"
              placeholder="Buscar noticias sobre..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
            />
            <button type="submit" className="search-button">Buscar</button>
          </form>
          <button onClick={handleRefresh} className="refresh-button" title="Actualizar noticias">↻</button>
        </div>

        <div className="news-topic">
          {searchTopic !== 'criptomonedas' && (
            <div className="current-topic">
              Mostrando noticias sobre: <span>{searchTopic}</span>
              <button
                onClick={() => setSearchTopic('criptomonedas')}
                className="reset-topic-button"
              >
                Volver a noticias generales
              </button>
            </div>
          )}
        </div>

        <div className="news-categories">
          <button
            className={`category-button ${activeCategory === null ? 'active' : ''}`}
            onClick={() => setActiveCategory(null)}
          >
            Todas
          </button>
          {categories.map(category => (
            <button
              key={category}
              className={`category-button ${activeCategory === category ? 'active' : ''}`}
              onClick={() => setActiveCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {filteredNews.length > 0 ? (
        <div className="news-grid">
          {filteredNews.map(article => (
            <div key={article.id} className="news-card">
              <div className="news-image">
                <img src={article.imageUrl} alt={article.title} />
              </div>
              <div className="news-content">
                <div className="news-meta">
                  <span className="news-source">{article.source}</span>
                  <span className="news-date">{formatDate(article.publishedAt)}</span>
                </div>
                <h3 className="news-title">{article.title}</h3>
                <p className="news-description">{article.description}</p>
                <div className="news-categories-tags">
                  {article.categories.map(category => (
                    <span
                      key={category}
                      className="category-tag"
                      onClick={() => setActiveCategory(category)}
                    >
                      {category}
                    </span>
                  ))}
                </div>
                <a
                  href={article.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="read-more-link"
                >
                  Leer más
                </a>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="no-news">
          <p>No hay noticias disponibles para la categoría seleccionada.</p>
          <button
            className="reset-filter-button"
            onClick={() => setActiveCategory(null)}
          >
            Ver todas las noticias
          </button>
        </div>
      )}
    </div>
  );
};

export default NewsSection;
