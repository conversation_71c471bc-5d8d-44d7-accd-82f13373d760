import express from "express";
import cors from "cors";
import fetch from "node-fetch";

// URL base de la API de CoinCap (gratuita, sin API key)
const COINCAP_API_URL = "https://api.coincap.io/v2";

// Sistema de caché simple
const cache = {
  data: {},
  timestamps: {},
  // Tiempo de caché en milisegundos (5 minutos)
  cacheDuration: 5 * 60 * 1000
};

// Control de tasa de solicitudes
const rateLimiter = {
  lastRequestTime: 0,
  // Tiempo mínimo entre solicitudes en milisegundos (1 segundo)
  minRequestInterval: 1000
};

// Función para obtener datos de la API de CoinCap con caché y control de tasa
async function fetchFromCoinCap(endpoint, params = {}) {
  try {
    // Construir la URL con los parámetros
    const url = new URL(`${COINCAP_API_URL}${endpoint}`);
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });
    
    const cacheKey = url.toString();
    
    // Verificar si tenemos datos en caché y si son válidos
    const now = Date.now();
    if (
      cache.data[cacheKey] && 
      cache.timestamps[cacheKey] && 
      (now - cache.timestamps[cacheKey]) < cache.cacheDuration
    ) {
      console.log(`Usando datos en caché para: ${cacheKey}`);
      return cache.data[cacheKey];
    }
    
    // Controlar la tasa de solicitudes
    const timeSinceLastRequest = now - rateLimiter.lastRequestTime;
    if (timeSinceLastRequest < rateLimiter.minRequestInterval) {
      const waitTime = rateLimiter.minRequestInterval - timeSinceLastRequest;
      console.log(`Esperando ${waitTime}ms antes de la siguiente solicitud...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    console.log(`Fetching data from CoinCap: ${url.toString()}`);
    
    // Actualizar el tiempo de la última solicitud
    rateLimiter.lastRequestTime = Date.now();
    
    // Realizar la petición
    const response = await fetch(url.toString());
    
    // Verificar si la respuesta es exitosa
    if (!response.ok) {
      throw new Error(`Error en la petición a CoinCap: ${response.status} ${response.statusText}`);
    }
    
    // Parsear la respuesta como JSON
    const data = await response.json();
    
    // Guardar en caché
    cache.data[cacheKey] = data;
    cache.timestamps[cacheKey] = Date.now();
    
    return data;
  } catch (error) {
    console.error(`Error al obtener datos de CoinCap:`, error);
    
    // Si hay un error, intentar usar datos en caché aunque estén caducados
    const cacheKey = new URL(`${COINCAP_API_URL}${endpoint}`).toString();
    if (cache.data[cacheKey]) {
      console.log(`Usando datos en caché caducados como fallback para: ${cacheKey}`);
      return cache.data[cacheKey];
    }
    
    throw error;
  }
}

// Función para obtener el precio de una criptomoneda desde CoinCap
async function getCryptoPrice(cryptoId) {
  try {
    // Usar la API de CoinCap para obtener datos detallados
    const data = await fetchFromCoinCap(`/assets/${cryptoId}`);

    // CoinCap devuelve los datos en un formato diferente 
    const asset = data.data; 

    // Calcular el cambio porcentual en 24h
    const changePercent24Hr = parseFloat(asset.changePercent24Hr);

    return {
      id: asset.id,
      name: asset.name,      
      symbol: asset.symbol.toUpperCase(),
      price: parseFloat(asset.priceUsd),
      price_change_24h: changePercent24Hr,
      market_cap: parseFloat(asset.marketCapUsd),
      total_volume: parseFloat(asset.volumeUsd24Hr),      
      high_24h: parseFloat(asset.priceUsd) * (1 + Math.abs(changePercent24Hr) / 100), // Estimación
      low_24h: parseFloat(asset.priceUsd) * (1 - Math.abs(changePercent24Hr) / 100), // Estimación
      image: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
      last_updated: new Date().toISOString(),
      // Datos adicionales para compatibilidad con el frontend
      market_data: {
        current_price: { usd: parseFloat(asset.priceUsd) },
        price_change_percentage_24h: changePercent24Hr,   
        market_cap: { usd: parseFloat(asset.marketCapUsd) },
        total_volume: { usd: parseFloat(asset.volumeUsd24Hr) },
        high_24h: { usd: parseFloat(asset.priceUsd) * (1 + Math.abs(changePercent24Hr) / 100) },
        low_24h: { usd: parseFloat(asset.priceUsd) * (1 - Math.abs(changePercent24Hr) / 100) }
      }
    };
  } catch (error) {
    console.error(`Error al obtener el precio de ${cryptoId}:`, error);
    
    // Generar datos simulados como fallback
    return generateMockCryptoData(cryptoId);
  }
}

// Función para obtener las principales criptomonedas     
async function getTopCryptocurrencies(limit = 10, page = 1) {
  try {
    // CoinCap no soporta paginación como CoinGecko, así que obtenemos todos y filtramos
    const data = await fetchFromCoinCap('/assets');       

    // Calcular índices para la paginación manual
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    // Filtrar y formatear los datos para que sean compatibles con el frontend
    const formattedData = data.data
      .slice(startIndex, endIndex)
      .map(asset => {        
        const changePercent24Hr = parseFloat(asset.changePercent24Hr);
        const price = parseFloat(asset.priceUsd);

        return {
          id: asset.id,      
          name: asset.name,  
          symbol: asset.symbol.toLowerCase(),
          current_price: price,
          price_change_percentage_24h: changePercent24Hr, 
          market_cap: parseFloat(asset.marketCapUsd),     
          total_volume: parseFloat(asset.volumeUsd24Hr),  
          circulating_supply: parseFloat(asset.supply),   
          image: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
          last_updated: new Date().toISOString(),
          // Datos adicionales estimados
          ath: price * 1.5, // Estimación del ATH
          sparkline_in_7d: { price: Array(7).fill(0).map(() => price * (0.9 + Math.random() * 0.2)) }
        };
      });

    return formattedData;    
  } catch (error) {
    console.error('Error al obtener las principales criptomonedas:', error);
    
    // Generar datos simulados como fallback
    return generateMockTopCryptos(limit);
  }
}

// Función para obtener datos históricos de una criptomoneda
async function getCryptoHistoricalData(cryptoId, days = 7) {
  try {
    // CoinCap tiene un endpoint diferente para datos históricos
    // Calculamos el intervalo de tiempo en milisegundos  
    const now = Date.now();  
    const start = now - (days * 24 * 60 * 60 * 1000);     

    // Obtener datos históricos de CoinCap
    const data = await fetchFromCoinCap(`/assets/${cryptoId}/history`, {
      interval: 'd1', // Intervalo diario
      start: start,
      end: now
    });

    // Formatear los datos para que sean compatibles con el formato de CoinGecko       
    const prices = [];       
    const market_caps = [];  
    const total_volumes = [];

    // CoinCap devuelve los datos en un formato diferente 
    if (data.data && Array.isArray(data.data)) {
      data.data.forEach(item => {
        const timestamp = new Date(item.time).getTime();  
        const price = parseFloat(item.priceUsd);
        const marketCap = parseFloat(item.marketCapUsd || '0');
        const volume = parseFloat(item.volumeUsd || '0'); 

        prices.push([timestamp, price]);
        market_caps.push([timestamp, marketCap]);
        total_volumes.push([timestamp, volume]);
      });
    } else {
      // Si no hay datos históricos disponibles, generamos datos simulados
      return generateMockHistoricalData(cryptoId, days);
    }

    return {
      prices,
      market_caps,
      total_volumes
    };
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${cryptoId}:`, error);
    
    // Generar datos simulados como fallback
    return generateMockHistoricalData(cryptoId, days);
  }
}

// Función para generar datos simulados de una criptomoneda
function generateMockCryptoData(cryptoId) {
  const mockCryptos = {
    'bitcoin': {
      id: 'bitcoin',
      name: 'Bitcoin',
      symbol: 'BTC',
      price: 85000 + (Math.random() * 2000 - 1000),
      change: Math.random() * 5 - 2.5,
    },
    'ethereum': {
      id: 'ethereum',
      name: 'Ethereum',
      symbol: 'ETH',
      price: 3500 + (Math.random() * 200 - 100),
      change: Math.random() * 6 - 3,
    },
    'tether': {
      id: 'tether',
      name: 'Tether',
      symbol: 'USDT',
      price: 1.0 + (Math.random() * 0.01 - 0.005),
      change: Math.random() * 0.2 - 0.1,
    },
    'binancecoin': {
      id: 'binancecoin',
      name: 'BNB',
      symbol: 'BNB',
      price: 600 + (Math.random() * 20 - 10),
      change: Math.random() * 4 - 2,
    },
    'solana': {
      id: 'solana',
      name: 'Solana',
      symbol: 'SOL',
      price: 150 + (Math.random() * 10 - 5),
      change: Math.random() * 8 - 4,
    }
  };
  
  // Si no tenemos datos para esta criptomoneda, generamos datos aleatorios
  const crypto = mockCryptos[cryptoId] || {
    id: cryptoId,
    name: cryptoId.charAt(0).toUpperCase() + cryptoId.slice(1),
    symbol: cryptoId.substring(0, 3).toUpperCase(),
    price: 10 + (Math.random() * 100),
    change: Math.random() * 10 - 5,
  };
  
  return {
    id: crypto.id,
    name: crypto.name,
    symbol: crypto.symbol,
    price: crypto.price,
    price_change_24h: crypto.change,
    market_cap: crypto.price * 1000000000,
    total_volume: crypto.price * 100000000,
    high_24h: crypto.price * (1 + Math.abs(crypto.change) / 100),
    low_24h: crypto.price * (1 - Math.abs(crypto.change) / 100),
    image: `https://assets.coincap.io/assets/icons/${crypto.symbol.toLowerCase()}@2x.png`,
    last_updated: new Date().toISOString(),
    market_data: {
      current_price: { usd: crypto.price },
      price_change_percentage_24h: crypto.change,
      market_cap: { usd: crypto.price * 1000000000 },
      total_volume: { usd: crypto.price * 100000000 },
      high_24h: { usd: crypto.price * (1 + Math.abs(crypto.change) / 100) },
      low_24h: { usd: crypto.price * (1 - Math.abs(crypto.change) / 100) }
    }
  };
}

// Función para generar datos simulados de las principales criptomonedas
function generateMockTopCryptos(limit = 10) {
  const baseCryptos = [
    { id: 'bitcoin', name: 'Bitcoin', symbol: 'btc', price: 85000, change: 0.5 },
    { id: 'ethereum', name: 'Ethereum', symbol: 'eth', price: 3500, change: 1.2 },
    { id: 'tether', name: 'Tether', symbol: 'usdt', price: 1.0, change: 0.01 },
    { id: 'binancecoin', name: 'BNB', symbol: 'bnb', price: 600, change: -0.8 },
    { id: 'solana', name: 'Solana', symbol: 'sol', price: 150, change: 2.5 },
    { id: 'ripple', name: 'XRP', symbol: 'xrp', price: 0.6, change: -1.3 },
    { id: 'cardano', name: 'Cardano', symbol: 'ada', price: 0.45, change: 0.7 },
    { id: 'dogecoin', name: 'Dogecoin', symbol: 'doge', price: 0.15, change: 3.2 },
    { id: 'polkadot', name: 'Polkadot', symbol: 'dot', price: 7.8, change: -0.5 },
    { id: 'litecoin', name: 'Litecoin', symbol: 'ltc', price: 80, change: 1.1 },
    { id: 'avalanche-2', name: 'Avalanche', symbol: 'avax', price: 35, change: 4.2 },
    { id: 'chainlink', name: 'Chainlink', symbol: 'link', price: 15, change: 2.8 },
    { id: 'uniswap', name: 'Uniswap', symbol: 'uni', price: 8.5, change: -1.5 },
    { id: 'stellar', name: 'Stellar', symbol: 'xlm', price: 0.12, change: 0.3 },
    { id: 'cosmos', name: 'Cosmos', symbol: 'atom', price: 9.2, change: 1.7 },
    { id: 'monero', name: 'Monero', symbol: 'xmr', price: 170, change: -0.2 },
    { id: 'algorand', name: 'Algorand', symbol: 'algo', price: 0.18, change: 0.9 },
    { id: 'filecoin', name: 'Filecoin', symbol: 'fil', price: 5.3, change: 2.1 },
    { id: 'tron', name: 'TRON', symbol: 'trx', price: 0.13, change: 1.4 },
    { id: 'near', name: 'NEAR Protocol', symbol: 'near', price: 6.8, change: 3.5 }
  ];
  
  // Añadir variación aleatoria a los precios y cambios
  return baseCryptos.slice(0, limit).map(crypto => {
    const priceVariation = (Math.random() * 0.1) - 0.05; // -5% a +5%
    const changeVariation = (Math.random() * 2) - 1; // -1% a +1%
    const price = crypto.price * (1 + priceVariation);
    const change = crypto.change + changeVariation;
    
    return {
      id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      current_price: price,
      price_change_percentage_24h: change,
      market_cap: price * 1000000000,
      total_volume: price * 100000000,
      circulating_supply: price * 10000000,
      image: `https://assets.coincap.io/assets/icons/${crypto.symbol}@2x.png`,
      last_updated: new Date().toISOString(),
      ath: price * 1.5,
      sparkline_in_7d: { price: Array(7).fill(0).map(() => price * (0.9 + Math.random() * 0.2)) }
    };
  });
}

// Función para generar datos históricos simulados
function generateMockHistoricalData(cryptoId, days = 7) {
  // Obtener un precio base según la criptomoneda
  let basePrice = 100;
  if (cryptoId === 'bitcoin') basePrice = 85000;
  else if (cryptoId === 'ethereum') basePrice = 3500;
  else if (cryptoId === 'tether') basePrice = 1.0;
  else if (cryptoId === 'binancecoin') basePrice = 600;
  else if (cryptoId === 'solana') basePrice = 150;
  
  const now = Date.now();
  const prices = [];
  const market_caps = [];
  const total_volumes = [];
  
  // Generar datos para cada día
  for (let i = days; i >= 0; i--) {
    const timestamp = now - (i * 24 * 60 * 60 * 1000);
    // Generar variación aleatoria del precio (-5% a +5%)
    const variation = (Math.random() * 0.1) - 0.05;
    const price = basePrice * (1 + variation);
    const marketCap = price * 1000000000;
    const volume = price * 100000000 * (0.8 + Math.random() * 0.4);
    
    prices.push([timestamp, price]);
    market_caps.push([timestamp, marketCap]);
    total_volumes.push([timestamp, volume]);
  }
  
  return {
    prices,
    market_caps,
    total_volumes
  };
}

// Configurar el puerto      
const PORT = process.env.PORT || 3100;

// Crear la aplicación Express
const app = express();       

// Configurar CORS
app.use(cors());

// Configurar middleware para parsear JSON
app.use(express.json());     

// Ruta para verificar si el servidor está en línea       
app.get('/tools', (req, res) => {
  const tools = [
    {
      name: 'getCryptoPrice',
      description: 'Obtener el precio y detalles de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)'
      }
    },
    {
      name: 'getTopCryptocurrencies',
      description: 'Obtener las principales criptomonedas por capitalización de mercado',
      parameters: {
        limit: 'Número de criptomonedas a obtener (1-100, por defecto 10)',
        page: 'Página de resultados (por defecto 1)'      
      }
    },
    {
      name: 'getCryptoHistoricalData',
      description: 'Obtener datos históricos de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)',        
        days: 'Número de días de datos históricos (1-365, por defecto 7)'
      }
    },
    {
      name: 'searchCryptocurrencies',
      description: 'Buscar criptomonedas por nombre o símbolo',
      parameters: {
        query: 'Término de búsqueda (nombre o símbolo)'   
      }
    }
  ];

  res.json({
    status: 'success',       
    tools
  });
});

// Ruta para obtener el precio de una criptomoneda        
app.post('/tools/getCryptoPrice', async (req, res) => {   
  try {
    const { cryptoId } = req.body;

    if (!cryptoId) {
      return res.status(400).json({
        status: 'error',     
        message: 'El parámetro cryptoId es obligatorio'   
      });
    }

    console.log(`Obteniendo precio de ${cryptoId}...`);   

    const data = await getCryptoPrice(cryptoId.toLowerCase());

    res.json({
      content: [
        {
          type: 'text',      
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener el precio:`, error);  
    res.status(500).json({   
      content: [
        {
          type: 'text',      
          text: `Error al obtener el precio: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para obtener las principales criptomonedas        
app.post('/tools/getTopCryptocurrencies', async (req, res) => {
  try {
    const { limit = 10, page = 1 } = req.body;

    console.log(`Obteniendo top ${limit} criptomonedas (página ${page})...`);

    const data = await getTopCryptocurrencies(limit, page);

    res.json({
      content: [
        {
          type: 'text',      
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener las principales criptomonedas:`, error);
    res.status(500).json({   
      content: [
        {
          type: 'text',      
          text: `Error al obtener las principales criptomonedas: ${error.message}`     
        }
      ]
    });
  }
});

// Ruta para obtener datos históricos de una criptomoneda 
app.post('/tools/getCryptoHistoricalData', async (req, res) => {
  try {
    const { cryptoId, days = 7 } = req.body;

    if (!cryptoId) {
      return res.status(400).json({
        status: 'error',     
        message: 'El parámetro cryptoId es obligatorio'   
      });
    }

    console.log(`Obteniendo datos históricos de ${cryptoId} para ${days} días...`);    

    const data = await getCryptoHistoricalData(cryptoId.toLowerCase(), days);

    res.json({
      content: [
        {
          type: 'text',      
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener datos históricos:`, error);
    res.status(500).json({   
      content: [
        {
          type: 'text',      
          text: `Error al obtener datos históricos: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para buscar criptomonedas
app.post('/tools/searchCryptocurrencies', async (req, res) => {
  try {
    const { query } = req.body;

    if (!query) {
      return res.status(400).json({
        status: 'error',     
        message: 'El parámetro query es obligatorio'      
      });
    }

    console.log(`Buscando criptomonedas con el término "${query}"...`);

    try {
      // CoinCap no tiene un endpoint de búsqueda, así que obtenemos todas las criptomonedas y filtramos
      const data = await fetchFromCoinCap('/assets');       

      // Filtrar por nombre o símbolo
      const searchTerm = query.toLowerCase();
      const filteredAssets = data.data.filter(asset =>      
        asset.id.toLowerCase().includes(searchTerm) ||      
        asset.name.toLowerCase().includes(searchTerm) ||    
        asset.symbol.toLowerCase().includes(searchTerm)     
      );

      // Formatear los resultados para que sean compatibles con el formato de CoinGecko  
      const formattedResults = {
        coins: filteredAssets.map(asset => ({
          id: asset.id,        
          name: asset.name,    
          symbol: asset.symbol.toLowerCase(),
          market_cap_rank: parseInt(asset.rank),
          thumb: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
          large: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`
        })),
        exchanges: [],
        icos: [],
        categories: [],        
        nfts: []
      };

      res.json({
        content: [
          {
            type: 'text',      
            text: JSON.stringify(formattedResults, null, 2) 
          }
        ]
      });
    } catch (error) {
      console.error(`Error al buscar criptomonedas:`, error);
      
      // Generar resultados de búsqueda simulados como fallback
      const mockCryptos = generateMockTopCryptos(20);
      const filteredMockCryptos = mockCryptos.filter(crypto => 
        crypto.id.includes(query.toLowerCase()) || 
        crypto.name.toLowerCase().includes(query.toLowerCase()) || 
        crypto.symbol.includes(query.toLowerCase())
      );
      
      const formattedResults = {
        coins: filteredMockCryptos.map(crypto => ({
          id: crypto.id,
          name: crypto.name,
          symbol: crypto.symbol,
          market_cap_rank: mockCryptos.findIndex(c => c.id === crypto.id) + 1,
          thumb: crypto.image,
          large: crypto.image
        })),
        exchanges: [],
        icos: [],
        categories: [],
        nfts: []
      };
      
      res.json({
        content: [
          {
            type: 'text',
            text: JSON.stringify(formattedResults, null, 2)
          }
        ]
      });
    }
  } catch (error) {
    console.error(`Error al buscar criptomonedas:`, error);
    res.status(500).json({   
      content: [
        {
          type: 'text',      
          text: `Error al buscar criptomonedas: ${error.message}`
        }
      ]
    });
  }
});

// Iniciar el servidor Express
app.listen(PORT, () => {     
  console.log(`Servidor de criptomonedas iniciado en http://localhost:${PORT}`);       
});
