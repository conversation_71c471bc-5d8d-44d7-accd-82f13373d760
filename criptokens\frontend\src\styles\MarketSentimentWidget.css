.market-sentiment-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.market-sentiment-widget.loading .widget-content {
  padding: 1.5rem;
  min-height: 150px;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.widget-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.sentiment-icon {
  font-size: 1.25rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-surface-light);
}

.widget-content {
  padding: 1rem;
}

.sentiment-meter {
  margin-bottom: 1.25rem;
}

.meter-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  font-size: 0.7rem;
  color: var(--text-tertiary);
}

.meter-bar {
  height: 8px;
  background: linear-gradient(90deg, 
    #e74c3c 0%, 
    #e67e22 25%, 
    #f1c40f 50%, 
    #2ecc71 75%, 
    #27ae60 100%
  );
  border-radius: 4px;
  position: relative;
  margin-bottom: 0.5rem;
}

.meter-indicator {
  position: absolute;
  top: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #f1c40f;
  transform: translateX(-50%);
  border: 2px solid white;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  transition: left 0.5s ease;
}

.meter-value {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

.value-label {
  color: var(--text-secondary);
}

.value-number {
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.value-text {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  background-color: var(--color-surface-light);
}

.sentiment-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.stat-value {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.sentiment-advice {
  padding: 0.75rem;
  background-color: rgba(52, 152, 219, 0.05);
  border-left: 3px solid #3498db;
  border-radius: 4px;
}

.sentiment-advice p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.skeleton-loading {
  width: 100%;
  height: 150px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .sentiment-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 576px) {
  .widget-header {
    padding: 0.75rem;
  }
  
  .widget-content {
    padding: 0.75rem;
  }
  
  .meter-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
