/**
 * Script para mover archivos duplicados a una carpeta de backup
 * 
 * Este script mueve los archivos duplicados o innecesarios a una carpeta de backup
 * para que puedan ser restaurados si es necesario.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Carpeta de backup
const BACKUP_DIR = path.join(__dirname, 'backup_duplicados');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para mover un archivo a la carpeta de backup
function moveToBackup(filePath) {
  try {
    // Crear la estructura de directorios en la carpeta de backup
    const relativePath = path.relative(__dirname, filePath);
    const backupPath = path.join(BACKUP_DIR, relativePath);
    const backupDir = path.dirname(backupPath);
    
    // Crear el directorio si no existe
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    // Mover el archivo
    fs.renameSync(filePath, backupPath);
    log(`✅ Movido: ${relativePath}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ Error al mover ${filePath}: ${error.message}`, colors.red);
    return false;
  }
}

// Función para verificar si un archivo existe
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Verificar que la carpeta de backup existe
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
  log(`Carpeta de backup creada: ${BACKUP_DIR}`, colors.blue);
}

// Lista de archivos duplicados a mover
const filesToMove = [
  // Scripts de inicio duplicados
  './start-all.js',
  './start-all-services.js',
  './start-mcp-servers.js',
  './start-criptokens-news.js',
  './start-etherscan-mcp.js',
  './criptokens/start-all.js',
  './criptokens/start-all-services.js',
  './criptokens/start-app.js',
  './criptokens/start-frontend.js',
  './criptokens/start-mcp-servers.js',
  './criptokens/start-services.js',
  
  // Servidores MCP duplicados o no utilizados
  './etherscan-mcp-server.js',
  
  // Servicios duplicados
  './services/etherscan.service.js',
  './guru-etherscan.service.js',
  
  // Scripts de instalación que ya no son necesarios
  './criptokens/install-brave-mcp.js',
  './criptokens/install-mcp.js',
  
  // Archivos de servidor duplicados
  './http-server-improved.js',
  './stop-services.js'
];

// Contador de archivos movidos
let movedCount = 0;
let errorCount = 0;

log('=== Iniciando limpieza de archivos duplicados ===', colors.bright + colors.blue);

// Mover cada archivo a la carpeta de backup
filesToMove.forEach(file => {
  const fullPath = path.join(__dirname, file);
  
  if (fileExists(fullPath)) {
    if (moveToBackup(fullPath)) {
      movedCount++;
    } else {
      errorCount++;
    }
  } else {
    log(`⚠️ Archivo no encontrado: ${file}`, colors.yellow);
  }
});

// Mover directorios duplicados
const dirsToMove = [
  // Orquestador MCP (parece ser una versión alternativa)
  './mcp-orchestrator'
];

dirsToMove.forEach(dir => {
  const fullPath = path.join(__dirname, dir);
  
  if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
    try {
      // Crear el directorio en la carpeta de backup
      const relativePath = path.relative(__dirname, fullPath);
      const backupPath = path.join(BACKUP_DIR, relativePath);
      
      // Crear el directorio padre si no existe
      const parentDir = path.dirname(backupPath);
      if (!fs.existsSync(parentDir)) {
        fs.mkdirSync(parentDir, { recursive: true });
      }
      
      // Mover el directorio completo
      fs.renameSync(fullPath, backupPath);
      log(`✅ Movido directorio: ${relativePath}`, colors.green);
      movedCount++;
    } catch (error) {
      log(`❌ Error al mover directorio ${dir}: ${error.message}`, colors.red);
      errorCount++;
    }
  } else {
    log(`⚠️ Directorio no encontrado: ${dir}`, colors.yellow);
  }
});

// Resumen
log('\n=== Resumen de la limpieza ===', colors.bright + colors.blue);
log(`✅ Archivos movidos: ${movedCount}`, colors.green);
log(`❌ Errores: ${errorCount}`, colors.red);
log(`\nTodos los archivos han sido movidos a: ${BACKUP_DIR}`, colors.blue);
log('Si necesitas restaurar algún archivo, puedes copiarlo desde la carpeta de backup.', colors.yellow);
log('\nPara probar que la aplicación sigue funcionando, ejecuta:', colors.cyan);
log('node start-criptokens.js', colors.cyan + colors.bright);
