const axios = require('axios');
require('dotenv').config();
// Importar la configuración directamente
const config = {
  apiKeys: {
    braveSearch: process.env.BRAVE_API_KEY || 'BSAccS820UUfffNOAD7yLACz9htlbe9'
  },
  urls: {
    braveSearchMcp: process.env.BRAVE_SEARCH_MCP_URL || 'http://localhost:3102'
  }
};

// API Key para Brave Search
const BRAVE_API_KEY = process.env.BRAVE_API_KEY || config.apiKeys.braveSearch;

// URL base de la API de Brave Search
const BRAVE_SEARCH_API_URL = 'https://api.search.brave.com/res/v1/web/search';

// URL del servidor MCP de Brave Search
const BRAVE_SEARCH_MCP_URL = process.env.BRAVE_SEARCH_MCP_URL || config.urls.braveSearchMcp;

/**
 * Busca noticias sobre criptomonedas usando Brave Search MCP
 * @param {string} query - Término de búsqueda
 * @param {string} freshness - Filtro de tiempo (pd: hoy, pw: semana, pm: mes, py: año)
 * @param {number} count - Número de resultados a devolver
 * @returns {Promise<Array>} - Resultados de la búsqueda
 */
async function searchCryptoNews(query = 'criptomonedas noticias', freshness = '', count = 10) {
  try {
    console.log(`Buscando noticias sobre: ${query}, freshness: ${freshness}, count: ${count}`);

    // Intentar usar el servidor MCP de Brave Search primero
    try {
      console.log(`Intentando usar el servidor MCP de Brave Search en ${BRAVE_SEARCH_MCP_URL}`);

      // Construir los parámetros para el servidor MCP
      const mcpParams = {
        query: query,
        count: count,
        freshness: freshness || undefined
      };

      // Realizar la solicitud al servidor MCP de Brave Search
      const mcpResponse = await axios.post(`${BRAVE_SEARCH_MCP_URL}/search`, mcpParams, {
        timeout: 5000 // Timeout de 5 segundos
      });

      console.log('Respuesta recibida del servidor MCP de Brave Search');

      // Procesar y transformar los resultados del MCP
      if (mcpResponse.data && mcpResponse.data.results) {
        const results = mcpResponse.data.results.map(result => {
          // Determinar el sentimiento basado en el título y la descripción
          const sentiment = determineSentiment(result.title + ' ' + (result.description || ''));

          return {
            title: result.title,
            description: result.description || '',
            url: result.url,
            publishedTime: result.published_timestamp || result.publishedDate || new Date().toISOString(),
            source: result.site_name || result.source || extractDomain(result.url),
            imageUrl: result.thumbnail?.src || result.imageUrl || null,
            sentiment
          };
        });

        return {
          results,
          totalCount: mcpResponse.data.totalCount || results.length,
          queryTimeSec: mcpResponse.data.queryTimeSec || 0,
          source: 'mcp'
        };
      }
    } catch (mcpError) {
      console.warn('Error al usar el servidor MCP de Brave Search:', mcpError.message);
      console.log('Fallback a la API directa de Brave Search...');
    }

    // Fallback a la API directa de Brave Search si el MCP falla
    // Construir los parámetros de búsqueda
    const params = {
      q: query,
      count: count,
      search_lang: 'es',
      country: 'ES',
      safesearch: 'moderate',
      text_format: 'raw',
      spellcheck: true,
      result_filter: 'news'
    };

    // Añadir filtro de tiempo si se especifica
    if (freshness) {
      params.freshness = freshness;
    }

    // Realizar la solicitud a Brave Search
    const response = await axios.get(BRAVE_SEARCH_API_URL, {
      params,
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': BRAVE_API_KEY
      }
    });

    // Procesar y transformar los resultados
    if (response.data && response.data.web && response.data.web.results) {
      const results = response.data.web.results.map(result => {
        // Determinar el sentimiento basado en el título y la descripción
        const sentiment = determineSentiment(result.title + ' ' + result.description);

        return {
          title: result.title,
          description: result.description,
          url: result.url,
          publishedTime: result.published_timestamp,
          source: result.site_name || extractDomain(result.url),
          imageUrl: result.thumbnail?.src || null,
          sentiment
        };
      });

      return {
        results,
        totalCount: response.data.web.total_count || results.length,
        queryTimeSec: response.data.query_time_secs || 0,
        source: 'api'
      };
    }

    return { results: [], totalCount: 0, queryTimeSec: 0, source: 'empty' };
  } catch (error) {
    console.error('Error al buscar noticias con Brave Search:', error.message);
    if (error.response) {
      console.error('Detalles del error:', {
        status: error.response.status,
        data: error.response.data
      });
    }

    // En caso de error, generar datos simulados
    console.log('Generando datos simulados como fallback...');
    const mockResults = generateMockNewsResults(query);
    return {
      results: mockResults,
      totalCount: mockResults.length,
      queryTimeSec: 0,
      source: 'mock'
    };
  }
}

/**
 * Determina el sentimiento de un texto (positivo, negativo o neutral)
 * @param {string} text - Texto a analizar
 * @returns {string} - Sentimiento: 'positive', 'negative' o 'neutral'
 */
function determineSentiment(text) {
  // Lista de palabras positivas en español
  const positiveWords = [
    'subida', 'ganancias', 'beneficios', 'éxito', 'crecimiento', 'alcista',
    'positivo', 'rentable', 'aumento', 'incremento', 'mejora', 'optimista',
    'bullish', 'oportunidad', 'innovación', 'avance', 'progreso', 'adopción',
    'revolucionario', 'prometedor', 'potencial', 'favorable', 'ventaja'
  ];

  // Lista de palabras negativas en español
  const negativeWords = [
    'caída', 'pérdidas', 'bajada', 'fracaso', 'colapso', 'bajista',
    'negativo', 'riesgo', 'descenso', 'disminución', 'problema', 'pesimista',
    'bearish', 'amenaza', 'crisis', 'retroceso', 'estafa', 'fraude',
    'preocupación', 'advertencia', 'peligro', 'volatilidad', 'incertidumbre'
  ];

  // Convertir a minúsculas para comparación
  const lowerText = text.toLowerCase();

  // Contar palabras positivas y negativas
  let positiveCount = 0;
  let negativeCount = 0;

  positiveWords.forEach(word => {
    if (lowerText.includes(word)) positiveCount++;
  });

  negativeWords.forEach(word => {
    if (lowerText.includes(word)) negativeCount++;
  });

  // Determinar sentimiento basado en conteo
  if (positiveCount > negativeCount) return 'positive';
  if (negativeCount > positiveCount) return 'negative';
  return 'neutral';
}

/**
 * Extrae el dominio de una URL
 * @param {string} url - URL completa
 * @returns {string} - Dominio extraído
 */
function extractDomain(url) {
  try {
    const domain = new URL(url).hostname;
    return domain.replace('www.', '');
  } catch (error) {
    return url;
  }
}

/**
 * Genera resultados de noticias simulados para un tema específico
 * @param {string} topic - Tema de búsqueda
 * @returns {Array} - Resultados simulados
 */
function generateMockNewsResults(topic) {
  const currentDate = new Date().toISOString();
  const normalizedTopic = topic.toLowerCase().includes('criptomoneda') ? topic : `${topic} criptomoneda`;

  return [
    {
      title: `Últimas noticias sobre ${topic}: Análisis del mercado actual`,
      url: `https://ejemplo.com/noticias/${topic.toLowerCase().replace(/\s+/g, '-')}`,
      description: `Un análisis detallado de la situación actual de ${topic} en el mercado de criptomonedas, incluyendo tendencias recientes y predicciones futuras.`,
      publishedTime: currentDate,
      source: 'CriptoNoticias',
      sentiment: 'neutral'
    },
    {
      title: `${topic} alcanza nuevo máximo histórico`,
      url: `https://ejemplo.com/mercado/${topic.toLowerCase().replace(/\s+/g, '-')}-maximo`,
      description: `El valor de ${topic} ha superado todas las expectativas al alcanzar un nuevo máximo histórico en las últimas 24 horas.`,
      publishedTime: currentDate,
      source: 'CoinDesk',
      sentiment: 'positive'
    },
    {
      title: `Expertos advierten sobre volatilidad en ${topic}`,
      url: `https://ejemplo.com/analisis/${topic.toLowerCase().replace(/\s+/g, '-')}-volatilidad`,
      description: `Analistas del mercado recomiendan precaución a los inversores interesados en ${topic} debido a la reciente volatilidad observada.`,
      publishedTime: currentDate,
      source: 'CryptoMarket',
      sentiment: 'negative'
    },
    {
      title: `Nuevas regulaciones podrían afectar a ${topic}`,
      url: `https://ejemplo.com/regulacion/${topic.toLowerCase().replace(/\s+/g, '-')}`,
      description: `Autoridades financieras están considerando nuevas regulaciones que podrían tener un impacto significativo en el futuro de ${topic}.`,
      publishedTime: currentDate,
      source: 'Regulación Cripto',
      sentiment: 'concerned'
    },
    {
      title: `Grandes inversores institucionales apuestan por ${topic}`,
      url: `https://ejemplo.com/inversiones/${topic.toLowerCase().replace(/\s+/g, '-')}-institucional`,
      description: `Fondos de inversión y empresas de capital riesgo están aumentando sus posiciones en ${topic}, señalando confianza en su futuro.`,
      publishedTime: currentDate,
      source: 'Institutional Crypto',
      sentiment: 'positive'
    }
  ];
}

module.exports = {
  searchCryptoNews
};
