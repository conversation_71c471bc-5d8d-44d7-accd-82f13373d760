const express = require('express');
const router = express.Router();
const {
  getUserConversations,
  getConversation,
  createConversation,
  updateConversationTitle,
  deleteConversation,
  addMessage
} = require('../services/conversation.service');

// Middleware para verificar que el usuario está autenticado
const requireAuth = (req, res, next) => {
  const userId = req.body.userId || req.query.userId || req.params.userId;
  
  if (!userId) {
    return res.status(401).json({ error: 'Se requiere autenticación' });
  }
  
  req.userId = userId;
  next();
};

// Obtener todas las conversaciones de un usuario
router.get('/', requireAuth, async (req, res) => {
  try {
    const conversations = await getUserConversations(req.userId);
    res.json(conversations);
  } catch (error) {
    console.error('Error al obtener conversaciones:', error);
    res.status(500).json({ error: error.message });
  }
});

// Obtener una conversación específica con sus mensajes
router.get('/:conversationId', requireAuth, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const conversation = await getConversation(req.userId, conversationId);
    res.json(conversation);
  } catch (error) {
    console.error('Error al obtener conversación:', error);
    res.status(500).json({ error: error.message });
  }
});

// Crear una nueva conversación
router.post('/', requireAuth, async (req, res) => {
  try {
    const { title } = req.body;
    const conversation = await createConversation(req.userId, title);
    res.status(201).json(conversation);
  } catch (error) {
    console.error('Error al crear conversación:', error);
    res.status(500).json({ error: error.message });
  }
});

// Actualizar el título de una conversación
router.patch('/:conversationId', requireAuth, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { title } = req.body;
    
    if (!title) {
      return res.status(400).json({ error: 'Se requiere un título' });
    }
    
    await updateConversationTitle(req.userId, conversationId, title);
    res.json({ success: true });
  } catch (error) {
    console.error('Error al actualizar conversación:', error);
    res.status(500).json({ error: error.message });
  }
});

// Eliminar una conversación
router.delete('/:conversationId', requireAuth, async (req, res) => {
  try {
    const { conversationId } = req.params;
    await deleteConversation(req.userId, conversationId);
    res.json({ success: true });
  } catch (error) {
    console.error('Error al eliminar conversación:', error);
    res.status(500).json({ error: error.message });
  }
});

// Añadir un mensaje a una conversación
router.post('/:conversationId/messages', requireAuth, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { role, content } = req.body;
    
    if (!role || !content) {
      return res.status(400).json({ error: 'Se requieren el rol y contenido del mensaje' });
    }
    
    const message = await addMessage(req.userId, conversationId, { role, content });
    res.status(201).json(message);
  } catch (error) {
    console.error('Error al añadir mensaje:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
