import os
import logging
import click
from dotenv import load_dotenv
from ...common.server import A2AServer
from ...common.types import Agent<PERSON>ard, AgentCapabilities, AgentSkill, AgentProvider
from .task_manager import SentimentAgentTaskManager
from .agent import SentimentAnalysisAgent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@click.command()
@click.option("--host", default="localhost", help="Host to bind the server to")
@click.option("--port", default=3202, help="Port to bind the server to")
def main(host, port):
    """Run the sentiment analysis agent server."""
    try:
        # Create agent capabilities
        capabilities = AgentCapabilities(streaming=True)
        
        # Create agent skills
        skill = AgentSkill(
            id="sentiment_analysis",
            name="Sentiment Analysis",
            description="Analyzes sentiment of cryptocurrency news and social media",
            tags=["crypto", "sentiment", "news", "social media"],
            examples=[
                "What's the sentiment for Bitcoin?",
                "Analyze Ethereum market sentiment",
                "Show me the Fear & Greed Index for Solana"
            ],
            inputModes=["text"],
            outputModes=["text", "data"]
        )
        
        # Create agent provider
        provider = AgentProvider(
            organization="Criptokens",
            url="https://criptokens.com"
        )
        
        # Create agent card
        agent_card = AgentCard(
            name="Sentiment Analysis Agent",
            description="This agent analyzes sentiment of cryptocurrency news and social media",
            url=f"http://{host}:{port}/",
            provider=provider,
            version="1.0.0",
            capabilities=capabilities,
            defaultInputModes=["text"],
            defaultOutputModes=["text", "data"],
            skills=[skill]
        )
        
        # Create agent and task manager
        agent = SentimentAnalysisAgent()
        task_manager = SentimentAgentTaskManager(agent=agent)
        
        # Create and start server
        server = A2AServer(
            agent_card=agent_card,
            task_manager=task_manager,
            host=host,
            port=port
        )
        
        logger.info(f"Starting Sentiment Analysis Agent server on http://{host}:{port}/")
        server.start()
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)

if __name__ == "__main__":
    main()
