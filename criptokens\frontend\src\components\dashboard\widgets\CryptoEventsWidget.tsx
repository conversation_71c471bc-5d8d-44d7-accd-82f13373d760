import React from 'react';
import { useNavigate } from 'react-router-dom';

interface CryptoEventsWidgetProps {
  isLoading: boolean;
  compact?: boolean;
  maxItems?: number;
}

interface EventItem {
  id: string;
  title: string;
  project: string;
  date: string;
  type: 'launch' | 'fork' | 'airdrop' | 'conference' | 'other';
}

const CryptoEventsWidget: React.FC<CryptoEventsWidgetProps> = ({ 
  isLoading, 
  compact = false,
  maxItems = 5
}) => {
  const navigate = useNavigate();
  
  // Datos simulados para eventos
  const mockEvents: EventItem[] = [
    {
      id: '1',
      title: 'Lanzamiento de Ethereum 2.0 Fase 2',
      project: 'Ethereum',
      date: '2023-11-15T00:00:00Z',
      type: 'launch'
    },
    {
      id: '2',
      title: 'Conferencia Bitcoin 2023',
      project: 'Bitcoin',
      date: '2023-11-10T00:00:00Z',
      type: 'conference'
    },
    {
      id: '3',
      title: 'Airdrop de tokens ARB',
      project: 'Arbitrum',
      date: '2023-11-05T00:00:00Z',
      type: 'airdrop'
    },
    {
      id: '4',
      title: 'Hard Fork de Cardano',
      project: 'Cardano',
      date: '2023-11-20T00:00:00Z',
      type: 'fork'
    },
    {
      id: '5',
      title: 'Lanzamiento de Solana Mobile',
      project: 'Solana',
      date: '2023-12-01T00:00:00Z',
      type: 'launch'
    }
  ];

  const displayedEvents = mockEvents.slice(0, maxItems);
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      day: 'numeric', 
      month: 'short'
    });
  };
  
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'launch':
        return <i className="fas fa-rocket"></i>;
      case 'fork':
        return <i className="fas fa-code-branch"></i>;
      case 'airdrop':
        return <i className="fas fa-parachute-box"></i>;
      case 'conference':
        return <i className="fas fa-microphone"></i>;
      default:
        return <i className="fas fa-calendar-day"></i>;
    }
  };
  
  const getDaysRemaining = (dateString: string) => {
    const eventDate = new Date(dateString);
    const today = new Date();
    const diffTime = eventDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Hoy';
    if (diffDays === 1) return 'Mañana';
    if (diffDays < 0) return 'Finalizado';
    return `En ${diffDays} días`;
  };

  if (isLoading) {
    return (
      <div className="widget-loading-state">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Cargando eventos...</p>
      </div>
    );
  }

  return (
    <div className="events-list">
      {displayedEvents.map(event => (
        <div key={event.id} className="event-item">
          <div className="event-icon">
            {getEventIcon(event.type)}
          </div>
          <div className="event-content">
            <div className="event-title">
              {event.title}
            </div>
            <div className="event-details">
              <span className="event-project">{event.project}</span>
              <span className="event-date">{formatDate(event.date)}</span>
            </div>
          </div>
          <div className="event-countdown">
            {getDaysRemaining(event.date)}
          </div>
        </div>
      ))}
      
      {!compact && displayedEvents.length > 0 && (
        <div className="view-more-events">
          <button 
            className="view-more-button"
            onClick={() => navigate('/events')}
          >
            Ver calendario completo <i className="fas fa-arrow-right"></i>
          </button>
        </div>
      )}
      
      {displayedEvents.length === 0 && (
        <div className="widget-empty-state">
          <p>No hay eventos próximos</p>
        </div>
      )}
    </div>
  );
};

export default CryptoEventsWidget;
