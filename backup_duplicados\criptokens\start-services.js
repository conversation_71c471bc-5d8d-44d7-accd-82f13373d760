#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para iniciar un proceso
function startProcess(name, command, cwd, color = colors.reset) {
  log(`Iniciando ${name}...`, color);
  
  const process = exec(command, { cwd });
  
  process.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        log(`[${name}] ${line}`, color);
      }
    });
  });
  
  process.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        log(`[${name}] ${line}`, colors.red);
      }
    });
  });
  
  process.on('error', (error) => {
    log(`Error al iniciar ${name}: ${error.message}`, colors.red);
  });
  
  process.on('close', (code) => {
    if (code !== 0) {
      log(`${name} se ha detenido con código ${code}`, colors.red);
    } else {
      log(`${name} se ha detenido correctamente`, colors.green);
    }
  });
  
  return process;
}

// Directorios
const rootDir = __dirname;
const frontendDir = path.join(rootDir, 'frontend');
const backendDir = path.join(rootDir, 'backend');

// Iniciar el frontend
const frontendProcess = startProcess(
  'Frontend',
  'npx vite',
  frontendDir,
  colors.yellow
);

// Iniciar el backend
const backendProcess = startProcess(
  'Backend',
  'node src/server.js',
  backendDir,
  colors.green
);

// Mostrar mensaje de éxito
setTimeout(() => {
  log('\n=== SERVICIOS PRINCIPALES INICIADOS ===', colors.green + colors.bright);
  log('Servicios disponibles:', colors.bright);
  log('- Backend: http://localhost:3001', colors.green);
  log('- Frontend: http://localhost:5173', colors.yellow);
  log('\nPresiona Ctrl+C para detener todos los servicios.', colors.bright);
}, 2000);

// Manejar la terminación del script
process.on('SIGINT', () => {
  log('\nDeteniendo todos los servicios...', colors.yellow);
  
  frontendProcess.kill();
  backendProcess.kill();
  
  setTimeout(() => {
    log('Todos los servicios han sido detenidos', colors.green);
    process.exit(0);
  }, 1000);
});
