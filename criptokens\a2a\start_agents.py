#!/usr/bin/env python3
"""
<PERSON>ript to start all A2A agents for Criptokens.
"""
import os
import sys
import subprocess
import time
import signal
import argparse
import logging
from typing import List, Dict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("a2a-starter")

# Define agent configurations
AGENTS = [
    {
        "name": "Technical Analysis Agent",
        "module": "technical_agent",
        "port": 3201,
        "dependencies": []
    },
    {
        "name": "Sentiment Analysis Agent",
        "module": "sentiment_agent",
        "port": 3202,
        "dependencies": []
    },
    {
        "name": "On-Chain Analysis Agent",
        "module": "onchain_agent",
        "port": 3203,
        "dependencies": []
    },
    {
        "name": "Guru Cripto Agent",
        "module": "guru_agent",
        "port": 3200,
        "dependencies": ["technical_agent", "sentiment_agent", "onchain_agent"]
    }
]

# Global variables
processes: Dict[str, subprocess.Popen] = {}
running = True

def signal_handler(sig, frame):
    """Handle termination signals."""
    global running
    logger.info("Received termination signal. Shutting down agents...")
    running = False
    stop_all_agents()
    sys.exit(0)

def start_agent(agent: Dict):
    """Start an A2A agent.
    
    Args:
        agent: Agent configuration
    
    Returns:
        Process object
    """
    module = agent["module"]
    port = agent["port"]
    
    # Construct command
    cmd = [
        sys.executable, 
        "-m", 
        f"criptokens.a2a.agents.{module}",
        "--port", 
        str(port)
    ]
    
    logger.info(f"Starting {agent['name']} on port {port}...")
    
    # Start process
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1
    )
    
    # Store process
    processes[module] = process
    
    return process

def stop_agent(module: str):
    """Stop an A2A agent.
    
    Args:
        module: Agent module name
    """
    if module in processes:
        logger.info(f"Stopping {module}...")
        try:
            processes[module].terminate()
            processes[module].wait(timeout=5)
        except subprocess.TimeoutExpired:
            logger.warning(f"Timeout waiting for {module} to terminate. Killing...")
            processes[module].kill()
        
        del processes[module]

def stop_all_agents():
    """Stop all running agents."""
    # Stop in reverse order of dependencies
    for agent in reversed(AGENTS):
        stop_agent(agent["module"])

def check_dependencies(agent: Dict) -> bool:
    """Check if agent dependencies are running.
    
    Args:
        agent: Agent configuration
        
    Returns:
        True if all dependencies are running, False otherwise
    """
    for dep in agent["dependencies"]:
        if dep not in processes:
            return False
    return True

def monitor_processes():
    """Monitor running processes and restart if needed."""
    for agent in AGENTS:
        module = agent["module"]
        if module in processes:
            # Check if process is still running
            if processes[module].poll() is not None:
                logger.warning(f"{agent['name']} has stopped. Restarting...")
                # Remove from processes
                del processes[module]

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Start A2A agents for Criptokens")
    parser.add_argument("--agent", help="Start only the specified agent")
    args = parser.parse_args()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if args.agent:
            # Start only the specified agent
            agent_found = False
            for agent in AGENTS:
                if agent["module"] == args.agent:
                    agent_found = True
                    start_agent(agent)
                    break
            
            if not agent_found:
                logger.error(f"Agent {args.agent} not found")
                sys.exit(1)
        else:
            # Start all agents in order of dependencies
            for agent in AGENTS:
                # Wait for dependencies
                while not check_dependencies(agent):
                    logger.info(f"Waiting for dependencies of {agent['name']}...")
                    time.sleep(2)
                
                # Start agent
                start_agent(agent)
                
                # Wait for agent to start
                time.sleep(2)
        
        # Monitor processes
        while running:
            monitor_processes()
            time.sleep(5)
    
    except KeyboardInterrupt:
        logger.info("Interrupted by user. Shutting down agents...")
    finally:
        stop_all_agents()

if __name__ == "__main__":
    main()
