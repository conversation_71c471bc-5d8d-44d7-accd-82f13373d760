"""
Simple ADK test script
"""
import os
import asyncio
from google.adk.agents.llm_agent import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai.types import Content, Part

# Set the Google API key
os.environ["GOOGLE_API_KEY"] = "AIzaSyCashyNX3fNv-bj5xOCtTDMfL_-vGm9Yag"

# Create a simple agent
agent = LlmAgent(
    name="test_agent",
    model="gemini-1.5-pro",
    description="A test agent",
    instruction="You are a helpful assistant."
)

# Create a session service
session_service = InMemorySessionService()

# Create a runner
runner = Runner(
    agent=agent,
    app_name="test_app",
    session_service=session_service
)

# Run the agent
async def main():
    # Create a session
    session = session_service.create_session(
        user_id="test_user",
        app_name="test_app"
    )

    # Create a message
    message = Content(
        role="user",
        parts=[Part(text="Hello, how are you?")]
    )

    # Run the agent with the session
    async for event in runner.run_async(
        user_id="test_user",
        session_id=session.id,
        new_message=message
    ):
        print(f"Event: {event}")

# Run the main function
if __name__ == "__main__":
    asyncio.run(main())
