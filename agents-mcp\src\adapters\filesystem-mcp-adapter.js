/**
 * Adaptador para el servidor MCP de Filesystem (interno)
 * 
 * Este adaptador proporciona funcionalidades de sistema de archivos
 * implementadas internamente sin necesidad de un servidor MCP externo.
 */

const fs = require('fs').promises;
const path = require('path');
const BaseMcpAdapter = require('./base-mcp-adapter');
const logger = require('../utils/logger');
const { McpServerError } = require('../utils/error-handler');

class FilesystemMcpAdapter extends BaseMcpAdapter {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   * @param {string} [options.baseDir] - Directorio base para las operaciones de archivos
   */
  constructor(options = {}) {
    super('filesystem', { baseUrl: 'internal', ...options });
    
    // Directorio base para las operaciones de archivos
    this.baseDir = options.baseDir || path.join(process.cwd(), 'agents-mcp', 'data');
    
    // Crear el directorio base si no existe
    this._ensureBaseDir();
  }
  
  /**
   * Asegura que el directorio base exista
   * @private
   */
  async _ensureBaseDir() {
    try {
      await fs.mkdir(this.baseDir, { recursive: true });
      logger.debug('FilesystemMcpAdapter', `Directorio base creado: ${this.baseDir}`);
    } catch (error) {
      logger.error('FilesystemMcpAdapter', `Error al crear directorio base: ${error.message}`);
    }
  }
  
  /**
   * Resuelve una ruta relativa al directorio base
   * @param {string} filePath - Ruta relativa
   * @returns {string} Ruta absoluta
   * @private
   */
  _resolvePath(filePath) {
    // Verificar que la ruta no intente salir del directorio base
    const normalizedPath = path.normalize(filePath);
    if (normalizedPath.startsWith('..') || path.isAbsolute(normalizedPath)) {
      throw new McpServerError(
        'Ruta de archivo no válida. No se permite salir del directorio base o usar rutas absolutas.',
        { filePath }
      );
    }
    
    return path.join(this.baseDir, normalizedPath);
  }
  
  /**
   * Ejecuta una herramienta interna
   * @param {string} toolName - Nombre de la herramienta
   * @param {Object} input - Parámetros de entrada
   * @returns {Promise<any>} Resultado de la ejecución
   * @private
   */
  async _executeInternalTool(toolName, input) {
    switch (toolName) {
      case 'readFile':
        return await this._readFile(input.path, input.encoding);
      case 'writeFile':
        return await this._writeFile(input.path, input.content, input.encoding);
      case 'listFiles':
        return await this._listFiles(input.directory);
      case 'deleteFile':
        return await this._deleteFile(input.path);
      default:
        throw new McpServerError(
          `La herramienta ${toolName} no está implementada para el servidor MCP de Filesystem`
        );
    }
  }
  
  /**
   * Lee un archivo
   * @param {string} filePath - Ruta del archivo
   * @param {string} [encoding='utf8'] - Codificación del archivo
   * @returns {Promise<Object>} Contenido del archivo
   * @private
   */
  async _readFile(filePath, encoding = 'utf8') {
    try {
      const resolvedPath = this._resolvePath(filePath);
      logger.debug('FilesystemMcpAdapter', `Leyendo archivo: ${resolvedPath}`);
      
      const content = await fs.readFile(resolvedPath, { encoding });
      
      return {
        path: filePath,
        content,
        encoding
      };
    } catch (error) {
      throw new McpServerError(
        `Error al leer archivo: ${error.message}`,
        { filePath, encoding },
        error
      );
    }
  }
  
  /**
   * Escribe un archivo
   * @param {string} filePath - Ruta del archivo
   * @param {string} content - Contenido a escribir
   * @param {string} [encoding='utf8'] - Codificación del archivo
   * @returns {Promise<Object>} Resultado de la operación
   * @private
   */
  async _writeFile(filePath, content, encoding = 'utf8') {
    try {
      const resolvedPath = this._resolvePath(filePath);
      logger.debug('FilesystemMcpAdapter', `Escribiendo archivo: ${resolvedPath}`);
      
      // Crear directorio si no existe
      const directory = path.dirname(resolvedPath);
      await fs.mkdir(directory, { recursive: true });
      
      await fs.writeFile(resolvedPath, content, { encoding });
      
      return {
        path: filePath,
        success: true,
        bytesWritten: Buffer.byteLength(content, encoding)
      };
    } catch (error) {
      throw new McpServerError(
        `Error al escribir archivo: ${error.message}`,
        { filePath, encoding },
        error
      );
    }
  }
  
  /**
   * Lista archivos en un directorio
   * @param {string} directory - Directorio a listar
   * @returns {Promise<Object>} Lista de archivos
   * @private
   */
  async _listFiles(directory = '') {
    try {
      const resolvedPath = this._resolvePath(directory);
      logger.debug('FilesystemMcpAdapter', `Listando archivos en directorio: ${resolvedPath}`);
      
      const entries = await fs.readdir(resolvedPath, { withFileTypes: true });
      
      const files = [];
      const directories = [];
      
      for (const entry of entries) {
        const entryPath = path.join(directory, entry.name);
        
        if (entry.isDirectory()) {
          directories.push({
            name: entry.name,
            path: entryPath,
            type: 'directory'
          });
        } else if (entry.isFile()) {
          const stats = await fs.stat(path.join(resolvedPath, entry.name));
          
          files.push({
            name: entry.name,
            path: entryPath,
            type: 'file',
            size: stats.size,
            modified: stats.mtime.toISOString()
          });
        }
      }
      
      return {
        directory,
        files,
        directories
      };
    } catch (error) {
      throw new McpServerError(
        `Error al listar archivos: ${error.message}`,
        { directory },
        error
      );
    }
  }
  
  /**
   * Elimina un archivo
   * @param {string} filePath - Ruta del archivo
   * @returns {Promise<Object>} Resultado de la operación
   * @private
   */
  async _deleteFile(filePath) {
    try {
      const resolvedPath = this._resolvePath(filePath);
      logger.debug('FilesystemMcpAdapter', `Eliminando archivo: ${resolvedPath}`);
      
      await fs.unlink(resolvedPath);
      
      return {
        path: filePath,
        success: true
      };
    } catch (error) {
      throw new McpServerError(
        `Error al eliminar archivo: ${error.message}`,
        { filePath },
        error
      );
    }
  }
  
  /**
   * Lee un archivo
   * @param {string} filePath - Ruta del archivo
   * @param {string} [encoding='utf8'] - Codificación del archivo
   * @returns {Promise<Object>} Contenido del archivo
   */
  async readFile(filePath, encoding = 'utf8') {
    return await this.executeTool('readFile', {
      path: filePath,
      encoding: encoding
    });
  }
  
  /**
   * Escribe un archivo
   * @param {string} filePath - Ruta del archivo
   * @param {string} content - Contenido a escribir
   * @param {string} [encoding='utf8'] - Codificación del archivo
   * @returns {Promise<Object>} Resultado de la operación
   */
  async writeFile(filePath, content, encoding = 'utf8') {
    return await this.executeTool('writeFile', {
      path: filePath,
      content: content,
      encoding: encoding
    });
  }
  
  /**
   * Lista archivos en un directorio
   * @param {string} [directory=''] - Directorio a listar
   * @returns {Promise<Object>} Lista de archivos
   */
  async listFiles(directory = '') {
    return await this.executeTool('listFiles', {
      directory: directory
    });
  }
  
  /**
   * Elimina un archivo
   * @param {string} filePath - Ruta del archivo
   * @returns {Promise<Object>} Resultado de la operación
   */
  async deleteFile(filePath) {
    return await this.executeTool('deleteFile', {
      path: filePath
    });
  }
}

module.exports = FilesystemMcpAdapter;
