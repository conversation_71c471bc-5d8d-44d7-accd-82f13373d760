import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getCoinDetails, getCoinMarkets, getHistoricalData } from '../services/api';
import PriceChart from './PriceChart';
import CoinStats from './CoinStats';
import CoinMarkets from './CoinMarkets';
import CoinConverter from './CoinConverter';
import CriptoAgentAvatar from './CriptoAgentAvatarExport';
import LoadingSkeleton from './LoadingSkeleton';
import FeaturedNews from './FeaturedNews';
import { useRadarCripto } from '../hooks/useRadarCripto';
import '../styles/CoinDetail.css';

const CoinDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [coinData, setCoinData] = useState<any>(null);
  const [marketData, setMarketData] = useState<any[]>([]);
  const [historicalData, setHistoricalData] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<string>('7d');
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { addToRadar, removeFromRadar, isInRadar } = useRadarCripto();
  const [showGuruModal, setShowGuruModal] = useState<boolean>(false);

  // Función para formatear números con separadores de miles
  const formatNumber = (num: number, maximumFractionDigits: number = 2): string => {
    // Validar que num sea un número válido
    if (num === null || num === undefined || isNaN(num) || !isFinite(num)) {
      return 'N/A';
    }

    // Asegurar que maximumFractionDigits esté dentro del rango válido (0-20)
    const validMaxDigits = Math.min(Math.max(0, Math.floor(maximumFractionDigits)), 20);

    // Determinar automáticamente el número de decimales para números muy pequeños
    let minDigits = 2;
    let maxDigits = validMaxDigits;

    // Para números muy pequeños, mostrar más decimales
    if (num !== 0 && Math.abs(num) < 0.01) {
      // Encontrar la primera cifra significativa
      const absNum = Math.abs(num);
      let significantDigits = 2;
      let tempNum = absNum;

      while (tempNum < 1 && significantDigits < 8) {
        tempNum *= 10;
        significantDigits++;
      }

      maxDigits = Math.min(significantDigits, 8); // Limitar a 8 decimales máximo
      minDigits = Math.min(2, maxDigits);
    }

    try {
      return new Intl.NumberFormat('es-ES', {
        minimumFractionDigits: minDigits,
        maximumFractionDigits: maxDigits
      }).format(num);
    } catch (error) {
      console.error('Error al formatear número:', error, { num, maxDigits });
      // Fallback simple en caso de error
      return num.toLocaleString('es-ES');
    }
  };

  // Función para formatear la capitalización de mercado
  const formatMarketCap = (marketCap: number): string => {
    if (marketCap >= 1_000_000_000_000) {
      return `$${(marketCap / 1_000_000_000_000).toFixed(2)}T`;
    } else if (marketCap >= 1_000_000_000) {
      return `$${(marketCap / 1_000_000_000).toFixed(2)}B`;
    } else if (marketCap >= 1_000_000) {
      return `$${(marketCap / 1_000_000).toFixed(2)}M`;
    } else {
      return `$${formatNumber(marketCap)}`;
    }
  };

  // Obtener datos de la criptomoneda
  useEffect(() => {
    const fetchCoinData = async () => {
      if (!id) return;

      setIsLoading(true);
      try {
        // Obtener detalles de la criptomoneda
        const details = await getCoinDetails(id);
        setCoinData(details);

        // Obtener mercados donde se comercia la criptomoneda
        const markets = await getCoinMarkets(id);
        setMarketData(markets);

        // Obtener datos históricos para el gráfico
        const historical = await getHistoricalData(id, timeRange);
        setHistoricalData(historical);
      } catch (error) {
        console.error('Error fetching coin data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCoinData();
  }, [id]);

  // Actualizar datos históricos cuando cambia el rango de tiempo
  useEffect(() => {
    const fetchHistoricalData = async () => {
      if (!id) return;

      try {
        // Convertir timeRange a número de días para la API
        let days;
        switch (timeRange) {
          case '24h': days = 1; break;
          case '7d': days = 7; break;
          case '14d': days = 14; break;
          case '30d': days = 30; break;
          case '90d': days = 90; break;
          case '1y': days = 365; break;
          case 'max': days = 'max'; break;
          default: days = 7;
        }

        const historical = await getHistoricalData(id, days);
        setHistoricalData(historical);
      } catch (error) {
        console.error('Error fetching historical data:', error);
      }
    };

    fetchHistoricalData();
  }, [id, timeRange]);

  // Función para alternar la criptomoneda en el radar
  const toggleRadar = () => {
    if (!coinData) return;

    if (isInRadar(coinData.id)) {
      removeFromRadar(coinData.id);
    } else {
      // Crear el objeto completo para añadir al radar
      const radarItem = {
        id: coinData.id,
        symbol: coinData.symbol.toUpperCase(),
        name: coinData.name,
        addedAt: new Date(),
        imageUrl: coinData.image?.large || coinData.image?.small
      };
      addToRadar(radarItem);
    }
  };

  // Función para abrir el modal del Guru
  const openGuruModal = () => {
    setShowGuruModal(true);
  };

  // Función para cerrar el modal del Guru
  const closeGuruModal = () => {
    setShowGuruModal(false);
  };

  // Renderizar un esqueleto de carga
  if (isLoading) {
    return (
      <div className="coin-detail-container">
        <div className="coin-detail-header skeleton">
          <div className="coin-basic-info">
            <div className="skeleton-circle"></div>
            <div className="skeleton-text-block">
              <div className="skeleton-text"></div>
              <div className="skeleton-text small"></div>
            </div>
          </div>
          <div className="coin-price-info">
            <div className="skeleton-text large"></div>
            <div className="skeleton-text medium"></div>
          </div>
        </div>
        <div className="coin-detail-tabs skeleton">
          <div className="skeleton-tab"></div>
          <div className="skeleton-tab"></div>
          <div className="skeleton-tab"></div>
          <div className="skeleton-tab"></div>
        </div>
        <div className="coin-detail-content">
          <div className="chart-container skeleton">
            <div className="skeleton-chart"></div>
          </div>
          <div className="stats-container skeleton">
            <div className="skeleton-stats-row"></div>
            <div className="skeleton-stats-row"></div>
            <div className="skeleton-stats-row"></div>
            <div className="skeleton-stats-row"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!coinData) {
    return (
      <div className="coin-detail-error">
        <h2>No se pudo cargar la información de la criptomoneda</h2>
        <button onClick={() => navigate('/dashboard')}>Volver al Dashboard</button>
      </div>
    );
  }

  // Calcular el cambio de precio en el período seleccionado
  const priceChange = coinData.market_data?.[`price_change_percentage_${timeRange}`] || 0;
  const priceChangeClass = priceChange >= 0 ? 'positive' : 'negative';

  return (
    <div className="coin-detail-container">
      {/* Cabecera con información básica de la criptomoneda */}
      <div className="coin-detail-header">
        <div className="coin-basic-info">
          <img
            src={coinData.image?.large}
            alt={coinData.name}
            className="coin-logo"
          />
          <div className="coin-name-container">
            <h1 className="coin-name">{coinData.name}</h1>
            <span className="coin-symbol">{coinData.symbol.toUpperCase()}</span>
            <div className="coin-rank">Rank #{coinData.market_cap_rank || 'N/A'}</div>
          </div>
        </div>
        <div className="coin-price-info">
          <div className="coin-current-price">
            ${formatNumber(coinData.market_data?.current_price?.usd || 0)}
          </div>
          <div className={`coin-price-change ${priceChangeClass}`}>
            <i className={`fas ${priceChange >= 0 ? 'fa-caret-up' : 'fa-caret-down'}`}></i>
            {priceChange.toFixed(2)}% ({timeRange})
          </div>
          <div className="coin-actions">
            <button
              className={`watchlist-button ${isInRadar(coinData.id) ? 'active' : ''}`}
              onClick={toggleRadar}
              title={isInRadar(coinData.id) ? "Quitar del Radar" : "Añadir al Radar"}
            >
              <i className={`fas ${isInRadar(coinData.id) ? 'fa-star' : 'fa-star'}`}></i>
              {isInRadar(coinData.id) ? 'En Mi Radar' : 'Añadir a Mi Radar'}
            </button>
            <button
              className="guru-button"
              onClick={openGuruModal}
              title="Preguntar al Guru sobre esta criptomoneda"
            >
              <i className="fas fa-robot"></i>
              Preguntar al Guru
            </button>
          </div>
        </div>
      </div>

      {/* Pestañas de navegación */}
      <div className="coin-detail-tabs">
        <button
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Visión General
        </button>
        <button
          className={`tab-button ${activeTab === 'markets' ? 'active' : ''}`}
          onClick={() => setActiveTab('markets')}
        >
          Mercados
        </button>
        <button
          className={`tab-button ${activeTab === 'historical' ? 'active' : ''}`}
          onClick={() => setActiveTab('historical')}
        >
          Datos Históricos
        </button>
        <button
          className={`tab-button ${activeTab === 'converter' ? 'active' : ''}`}
          onClick={() => setActiveTab('converter')}
        >
          Conversor
        </button>
      </div>

      {/* Contenido principal */}
      <div className="coin-detail-content">
        {/* Controles del gráfico */}
        <div className="chart-controls">
          <div className="time-range-buttons">
            <button
              className={`time-button ${timeRange === '24h' ? 'active' : ''}`}
              onClick={() => setTimeRange('24h')}
            >
              24h
            </button>
            <button
              className={`time-button ${timeRange === '7d' ? 'active' : ''}`}
              onClick={() => setTimeRange('7d')}
            >
              7d
            </button>
            <button
              className={`time-button ${timeRange === '14d' ? 'active' : ''}`}
              onClick={() => setTimeRange('14d')}
            >
              14d
            </button>
            <button
              className={`time-button ${timeRange === '30d' ? 'active' : ''}`}
              onClick={() => setTimeRange('30d')}
            >
              30d
            </button>
            <button
              className={`time-button ${timeRange === '90d' ? 'active' : ''}`}
              onClick={() => setTimeRange('90d')}
            >
              90d
            </button>
            <button
              className={`time-button ${timeRange === '1y' ? 'active' : ''}`}
              onClick={() => setTimeRange('1y')}
            >
              1a
            </button>
            <button
              className={`time-button ${timeRange === 'max' ? 'active' : ''}`}
              onClick={() => setTimeRange('max')}
            >
              Max
            </button>
          </div>
          <div className="chart-type-buttons">
            <button className="chart-type-button active">
              <i className="fas fa-chart-line"></i>
              Línea
            </button>
            <button className="chart-type-button">
              <i className="fas fa-chart-bar"></i>
              Velas
            </button>
          </div>
        </div>

        {/* Gráfico de precios */}
        <div className="chart-container">
          <PriceChart
            data={historicalData}
            timeRange={timeRange}
            priceChange={priceChange}
            cryptoId={id}
            name={coinData.name}
          />
        </div>

        {/* Contenido según la pestaña activa */}
        {activeTab === 'overview' && (
          <div className="overview-container">
            <div className="stats-and-info">
              <CoinStats
                coinData={coinData}
                formatNumber={formatNumber}
                formatMarketCap={formatMarketCap}
              />

              <div className="coin-description">
                <h3>Acerca de {coinData.name}</h3>
                <div
                  className="description-content"
                  dangerouslySetInnerHTML={{ __html: coinData.description?.es || coinData.description?.en || 'No hay descripción disponible.' }}
                />
              </div>

              <div className="coin-news">
                <FeaturedNews coinId={id} coinName={coinData.name} limit={3} />
              </div>
            </div>

            <div className="coin-links">
              <h3>Enlaces</h3>
              <div className="links-container">
                {coinData.links?.homepage?.[0] && (
                  <a href={coinData.links.homepage[0]} target="_blank" rel="noopener noreferrer" className="coin-link">
                    <i className="fas fa-globe"></i>
                    Sitio Web
                  </a>
                )}
                {coinData.links?.blockchain_site?.filter(Boolean)[0] && (
                  <a href={coinData.links.blockchain_site.filter(Boolean)[0]} target="_blank" rel="noopener noreferrer" className="coin-link">
                    <i className="fas fa-search"></i>
                    Explorador
                  </a>
                )}
                {coinData.links?.official_forum_url?.filter(Boolean)[0] && (
                  <a href={coinData.links.official_forum_url.filter(Boolean)[0]} target="_blank" rel="noopener noreferrer" className="coin-link">
                    <i className="fas fa-comments"></i>
                    Foro Oficial
                  </a>
                )}
                {coinData.links?.subreddit_url && (
                  <a href={coinData.links.subreddit_url} target="_blank" rel="noopener noreferrer" className="coin-link">
                    <i className="fab fa-reddit"></i>
                    Reddit
                  </a>
                )}
                {coinData.links?.twitter_screen_name && (
                  <a href={`https://twitter.com/${coinData.links.twitter_screen_name}`} target="_blank" rel="noopener noreferrer" className="coin-link">
                    <i className="fab fa-twitter"></i>
                    Twitter
                  </a>
                )}
                {coinData.links?.facebook_username && (
                  <a href={`https://facebook.com/${coinData.links.facebook_username}`} target="_blank" rel="noopener noreferrer" className="coin-link">
                    <i className="fab fa-facebook"></i>
                    Facebook
                  </a>
                )}
                {coinData.links?.repos_url?.github?.filter(Boolean)[0] && (
                  <a href={coinData.links.repos_url.github.filter(Boolean)[0]} target="_blank" rel="noopener noreferrer" className="coin-link">
                    <i className="fab fa-github"></i>
                    GitHub
                  </a>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'markets' && (
          <CoinMarkets
            markets={marketData}
            coinSymbol={coinData.symbol.toUpperCase()}
            formatNumber={formatNumber}
          />
        )}

        {activeTab === 'historical' && (
          <div className="historical-data-container">
            <h3>Datos Históricos de {coinData.name}</h3>
            <p>Próximamente: tabla de datos históricos con precios, volumen y capitalización de mercado.</p>
          </div>
        )}

        {activeTab === 'converter' && (
          <CoinConverter
            coinData={coinData}
            formatNumber={formatNumber}
          />
        )}
      </div>

      {/* Modal del Guru */}
      {showGuruModal && (
        <div className="guru-modal-overlay" onClick={closeGuruModal}>
          <div className="guru-modal" onClick={e => e.stopPropagation()}>
            <div className="guru-modal-header">
              <h3>Consulta al Guru sobre {coinData.name}</h3>
              <button className="close-button" onClick={closeGuruModal}>
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="guru-modal-content">
              <div className="guru-avatar-container">
                <CriptoAgentAvatar mood="happy" size="large" />
              </div>
              <div className="guru-message">
                <p>¡Hola! Soy el Guru de Criptokens. ¿Qué te gustaría saber sobre {coinData.name}?</p>
                <ul className="guru-suggestions">
                  <li>¿Cuál es tu análisis sobre {coinData.name} a corto plazo?</li>
                  <li>¿Debería invertir en {coinData.name} ahora?</li>
                  <li>¿Cuáles son los riesgos de invertir en {coinData.name}?</li>
                  <li>¿Cómo funciona la tecnología detrás de {coinData.name}?</li>
                </ul>
              </div>
            </div>
            <div className="guru-modal-footer">
              <input
                type="text"
                placeholder={`Pregunta al Guru sobre ${coinData.name}...`}
                className="guru-input"
              />
              <button className="send-button">
                <i className="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CoinDetail;
