/**
 * Script para probar el inicio y detención de un componente simple
 */
const { spawn } = require('child_process');
const path = require('path');

// Crear un componente simple
const component = {
  name: 'Servidor Simple',
  cmd: 'node',
  args: ['simple-server.js'],
  cwd: path.resolve(__dirname)
};

console.log(`Probando componente: ${component.name}`);

// Iniciar el componente
console.log(`\nIniciando ${component.name}...`);

const proc = spawn(component.cmd, component.args, {
  cwd: component.cwd,
  stdio: 'pipe',
  shell: true
});

proc.stdout.on('data', (data) => {
  console.log(`[${component.name}] ${data.toString().trim()}`);
});

proc.stderr.on('data', (data) => {
  console.error(`[${component.name} ERROR] ${data.toString().trim()}`);
});

// Esperar 10 segundos y luego detener el componente
console.log(`\nEsperando 10 segundos antes de detener ${component.name}...`);

setTimeout(() => {
  console.log(`\nDeteniendo ${component.name}...`);
  proc.kill();
  console.log(`${component.name} detenido.`);
}, 10000);
