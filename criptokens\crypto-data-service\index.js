const axios = require('axios');
const admin = require('firebase-admin');
const cron = require('node-cron');
const path = require('path');
const fs = require('fs');

// Ruta al archivo de credenciales de Firebase
const serviceAccountPath = path.join(__dirname, 'firebase-credentials.json');

// Verificar si el archivo de credenciales existe
if (!fs.existsSync(serviceAccountPath)) {
  console.error('Error: El archivo de credenciales de Firebase no existe.');
  console.error('Por favor, crea el archivo firebase-credentials.json con las credenciales de tu proyecto Firebase.');
  process.exit(1);
}

// Inicializar Firebase Admin
const serviceAccount = require(serviceAccountPath);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

// URL base de la API de CoinGecko
const COINGECKO_API_URL = 'https://api.coingecko.com/api/v3';

// Función para obtener datos de la API de CoinGecko
async function fetchFromCoinGecko(endpoint, params = {}) {
  try {
    // Construir la URL con los parámetros
    const url = new URL(`${COINGECKO_API_URL}${endpoint}`);
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });
    
    console.log(`Fetching data from: ${url.toString()}`);
    
    // Realizar la petición
    const response = await axios.get(url.toString());
    
    // Verificar si la respuesta es exitosa
    if (response.status !== 200) {
      throw new Error(`Error en la petición: ${response.status} ${response.statusText}`);
    }
    
    return response.data;
  } catch (error) {
    console.error(`Error al obtener datos de CoinGecko:`, error.message);
    throw error;
  }
}

// Función para obtener las principales criptomonedas
async function getTopCryptocurrencies(limit = 50, page = 1) {
  try {
    const data = await fetchFromCoinGecko('/coins/markets', {
      vs_currency: 'usd',
      order: 'market_cap_desc',
      per_page: limit,
      page: page,
      sparkline: true,
      price_change_percentage: '24h'
    });
    
    return data;
  } catch (error) {
    console.error('Error al obtener las principales criptomonedas:', error.message);
    throw error;
  }
}

// Función para obtener datos globales del mercado
async function getGlobalMarketData() {
  try {
    const data = await fetchFromCoinGecko('/global');
    return data;
  } catch (error) {
    console.error('Error al obtener datos globales del mercado:', error.message);
    throw error;
  }
}

// Función para almacenar datos en Firestore
async function storeDataInFirestore(data, globalData) {
  try {
    // Crear un objeto con los datos y la fecha de actualización
    const marketData = {
      data: data,
      global: globalData,
      last_updated: new Date().toISOString()
    };
    
    // Almacenar los datos en Firestore
    await db.collection('crypto_data').doc('market_data').set(marketData);
    
    console.log('Datos almacenados correctamente en Firestore');
    return true;
  } catch (error) {
    console.error('Error al almacenar datos en Firestore:', error.message);
    throw error;
  }
}

// Función para verificar alertas de precio
async function checkPriceAlerts(cryptoData) {
  try {
    // Obtener todas las alertas activas
    const alertsSnapshot = await db.collection('price_alerts')
      .where('active', '==', true)
      .where('triggered', '==', false)
      .get();
    
    if (alertsSnapshot.empty) {
      console.log('No hay alertas activas para verificar');
      return;
    }
    
    // Crear un mapa de criptomonedas para búsqueda rápida
    const cryptoMap = {};
    cryptoData.forEach(crypto => {
      cryptoMap[crypto.id] = crypto;
    });
    
    // Verificar cada alerta
    const batch = db.batch();
    const notifications = [];
    
    alertsSnapshot.forEach(doc => {
      const alert = doc.data();
      const crypto = cryptoMap[alert.cryptoId];
      
      if (!crypto) {
        console.log(`Criptomoneda ${alert.cryptoId} no encontrada en los datos actuales`);
        return;
      }
      
      const currentPrice = crypto.current_price;
      let isTriggered = false;
      
      // Verificar si se cumple la condición de la alerta
      if (alert.condition === 'above' && currentPrice >= alert.targetPrice) {
        isTriggered = true;
      } else if (alert.condition === 'below' && currentPrice <= alert.targetPrice) {
        isTriggered = true;
      }
      
      // Si la alerta se activó, actualizar el documento y crear una notificación
      if (isTriggered) {
        console.log(`Alerta activada para ${alert.cryptoName}: Precio actual $${currentPrice} (Objetivo: $${alert.targetPrice})`);
        
        // Actualizar la alerta
        const alertRef = db.collection('price_alerts').doc(doc.id);
        batch.update(alertRef, { 
          triggered: true,
          triggeredAt: new Date().toISOString(),
          triggerPrice: currentPrice
        });
        
        // Crear una notificación
        const notificationRef = db.collection('alert_notifications').doc();
        const notification = {
          userId: alert.userId,
          type: 'price_alert',
          title: `Alerta de precio: ${alert.cryptoName}`,
          message: `El precio de ${alert.cryptoName} ha ${alert.condition === 'above' ? 'superado' : 'bajado de'} $${alert.targetPrice}. Precio actual: $${currentPrice.toFixed(2)}.`,
          read: false,
          createdAt: new Date().toISOString(),
          alertId: doc.id,
          cryptoId: alert.cryptoId,
          cryptoName: alert.cryptoName,
          targetPrice: alert.targetPrice,
          currentPrice: currentPrice,
          condition: alert.condition
        };
        
        batch.set(notificationRef, notification);
        notifications.push(notification);
      }
    });
    
    // Ejecutar el batch si hay cambios
    if (notifications.length > 0) {
      await batch.commit();
      console.log(`${notifications.length} alertas activadas y notificaciones creadas`);
    } else {
      console.log('No se activaron alertas en esta verificación');
    }
  } catch (error) {
    console.error('Error al verificar alertas de precio:', error.message);
  }
}

// Función principal para actualizar datos
async function updateCryptoData() {
  try {
    console.log('Iniciando actualización de datos de criptomonedas...');
    
    // Obtener datos de criptomonedas
    const cryptoData = await getTopCryptocurrencies(100);
    console.log(`Obtenidos datos de ${cryptoData.length} criptomonedas`);
    
    // Obtener datos globales del mercado
    const globalData = await getGlobalMarketData();
    console.log('Obtenidos datos globales del mercado');
    
    // Almacenar datos en Firestore
    await storeDataInFirestore(cryptoData, globalData);
    
    // Verificar alertas de precio
    await checkPriceAlerts(cryptoData);
    
    console.log('Actualización de datos completada con éxito');
  } catch (error) {
    console.error('Error en la actualización de datos:', error.message);
  }
}

// Programar la tarea para ejecutarse cada 5 minutos
cron.schedule('*/5 * * * *', () => {
  console.log('Ejecutando tarea programada:', new Date().toISOString());
  updateCryptoData();
});

// Ejecutar la tarea inmediatamente al iniciar
console.log('Iniciando servicio de datos de criptomonedas...');
updateCryptoData();

console.log('Servicio iniciado. Presiona Ctrl+C para detener.');
