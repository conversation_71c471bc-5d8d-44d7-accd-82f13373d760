import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { 
  Container, 
  Typography, 
  Box, 
  TextField, 
  Button, 
  CircularProgress,
  Autocomplete,
  Paper,
  Grid,
  Divider,
  Chip,
  Card,
  CardContent,
  CardActionArea,
  CardMedia,
  IconButton,
  Tabs,
  Tab,
  Alert
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import NewspaperIcon from '@mui/icons-material/Newspaper';
import ShareIcon from '@mui/icons-material/Share';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import SentimentSatisfiedAltIcon from '@mui/icons-material/SentimentSatisfiedAlt';
import SentimentNeutralIcon from '@mui/icons-material/SentimentNeutral';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import '../styles/EnhancedNewsPage.css';

interface CryptoOption {
  id: string;
  name: string;
  symbol: string;
}

interface NewsItem {
  title: string;
  description: string;
  url: string;
  publishedDate: string;
  source?: string;
  imageUrl?: string;
  sentiment?: {
    score: number;
    label: string;
  };
}

interface NewsResponse {
  query: string;
  overallSentiment: {
    score: number;
    label: string;
  };
  results: NewsItem[];
}

const EnhancedNewsPage: React.FC = () => {
  const { symbol } = useParams<{ symbol?: string }>();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [newsData, setNewsData] = useState<NewsResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [cryptoOptions, setCryptoOptions] = useState<CryptoOption[]>([]);
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoOption | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);
  const [savedArticles, setSavedArticles] = useState<NewsItem[]>([]);

  // Cargar opciones de criptomonedas
  useEffect(() => {
    const fetchCryptoOptions = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/crypto/top');
        if (!response.ok) {
          throw new Error('Error al obtener lista de criptomonedas');
        }
        const data = await response.json();
        
        // Transformar los datos al formato requerido por Autocomplete
        const options = data.map((crypto: any) => ({
          id: crypto.id,
          name: crypto.name,
          symbol: crypto.symbol
        }));
        
        setCryptoOptions(options);
      } catch (error) {
        console.error('Error al cargar opciones de criptomonedas:', error);
        setError('No se pudieron cargar las opciones de criptomonedas');
      }
    };

    fetchCryptoOptions();
    
    // Cargar noticias tendencia al inicio
    fetchTrendingNews();
  }, []);

  // Cargar noticias si hay un símbolo en la URL
  useEffect(() => {
    if (symbol) {
      fetchNewsBySymbol(symbol);
      
      // Actualizar el crypto seleccionado en el buscador
      const matchingCrypto = cryptoOptions.find(crypto => 
        crypto.symbol.toLowerCase() === symbol.toLowerCase()
      );
      
      if (matchingCrypto) {
        setSelectedCrypto(matchingCrypto);
      }
    }
  }, [symbol, cryptoOptions]);

  const fetchNewsBySymbol = async (cryptoSymbol: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`http://localhost:3001/api/enhanced-news/crypto/${cryptoSymbol}`);
      
      if (!response.ok) {
        throw new Error('Error al obtener noticias');
      }
      
      const data = await response.json();
      setNewsData(data);
    } catch (error) {
      console.error('Error:', error);
      setError('No se pudieron cargar las noticias. Por favor, inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  const fetchNewsByQuery = async (query: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://localhost:3001/api/enhanced-news/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, count: 15 })
      });
      
      if (!response.ok) {
        throw new Error('Error al buscar noticias');
      }
      
      const data = await response.json();
      setNewsData(data);
    } catch (error) {
      console.error('Error:', error);
      setError('No se pudieron cargar las noticias. Por favor, inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  const fetchTrendingNews = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://localhost:3001/api/enhanced-news/trending');
      
      if (!response.ok) {
        throw new Error('Error al obtener noticias tendencia');
      }
      
      const data = await response.json();
      setNewsData(data);
    } catch (error) {
      console.error('Error:', error);
      setError('No se pudieron cargar las noticias tendencia. Por favor, inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (selectedCrypto) {
      navigate(`/news/${selectedCrypto.symbol}`);
      fetchNewsBySymbol(selectedCrypto.symbol);
    } else if (searchTerm.trim()) {
      fetchNewsByQuery(searchTerm);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    
    if (newValue === 0) {
      // Trending tab
      fetchTrendingNews();
    } else if (newValue === 1 && selectedCrypto) {
      // Crypto specific tab
      fetchNewsBySymbol(selectedCrypto.symbol);
    } else if (newValue === 2) {
      // Saved articles tab
      // No need to fetch, just display saved articles
    }
  };

  const toggleSaveArticle = (article: NewsItem) => {
    const isArticleSaved = savedArticles.some(
      savedArticle => savedArticle.url === article.url
    );
    
    if (isArticleSaved) {
      // Remove from saved articles
      setSavedArticles(savedArticles.filter(
        savedArticle => savedArticle.url !== article.url
      ));
    } else {
      // Add to saved articles
      setSavedArticles([...savedArticles, article]);
    }
  };

  const isArticleSaved = (article: NewsItem) => {
    return savedArticles.some(savedArticle => savedArticle.url === article.url);
  };

  const getSentimentIcon = (sentiment?: { label: string; score: number }) => {
    if (!sentiment) return <SentimentNeutralIcon />;
    
    switch (sentiment.label) {
      case 'positive':
        return <SentimentSatisfiedAltIcon style={{ color: '#4caf50' }} />;
      case 'negative':
        return <SentimentVeryDissatisfiedIcon style={{ color: '#f44336' }} />;
      default:
        return <SentimentNeutralIcon style={{ color: '#ff9800' }} />;
    }
  };

  const getSentimentColor = (sentiment?: { label: string; score: number }) => {
    if (!sentiment) return '#757575';
    
    switch (sentiment.label) {
      case 'positive':
        return '#4caf50';
      case 'negative':
        return '#f44336';
      default:
        return '#ff9800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      <Container maxWidth="lg" className="enhanced-news-page">
        <Box className="page-header">
          <Typography variant="h4" component="h1">
            Noticias Cripto
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Mantente informado con las últimas noticias del mundo de las criptomonedas
          </Typography>
        </Box>

        <Paper elevation={3} className="search-container">
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Autocomplete
                options={cryptoOptions}
                getOptionLabel={(option) => `${option.name} (${option.symbol})`}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Buscar noticias por criptomoneda"
                    variant="outlined"
                    fullWidth
                    onKeyPress={handleKeyPress}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                )}
                value={selectedCrypto}
                onChange={(event, newValue) => {
                  setSelectedCrypto(newValue);
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                variant="contained"
                color="primary"
                fullWidth
                onClick={handleSearch}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SearchIcon />}
              >
                {loading ? 'Buscando...' : 'Buscar'}
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {error && (
          <Alert severity="error" className="error-alert">
            {error}
          </Alert>
        )}

        <Box className="popular-searches">
          <Typography variant="subtitle1" gutterBottom>
            Búsquedas populares:
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            <Chip 
              label="Bitcoin (BTC)" 
              clickable 
              onClick={() => {
                navigate('/news/BTC');
                fetchNewsBySymbol('BTC');
              }} 
            />
            <Chip 
              label="Ethereum (ETH)" 
              clickable 
              onClick={() => {
                navigate('/news/ETH');
                fetchNewsBySymbol('ETH');
              }} 
            />
            <Chip 
              label="Binance Coin (BNB)" 
              clickable 
              onClick={() => {
                navigate('/news/BNB');
                fetchNewsBySymbol('BNB');
              }} 
            />
            <Chip 
              label="Solana (SOL)" 
              clickable 
              onClick={() => {
                navigate('/news/SOL');
                fetchNewsBySymbol('SOL');
              }} 
            />
            <Chip 
              label="Cardano (ADA)" 
              clickable 
              onClick={() => {
                navigate('/news/ADA');
                fetchNewsBySymbol('ADA');
              }} 
            />
          </Box>
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mt: 3 }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="news tabs"
            variant="fullWidth"
          >
            <Tab 
              icon={<TrendingUpIcon />} 
              label="Tendencias" 
              id="news-tab-0" 
              aria-controls="news-tabpanel-0" 
            />
            <Tab 
              icon={<NewspaperIcon />} 
              label="Por Criptomoneda" 
              id="news-tab-1" 
              aria-controls="news-tabpanel-1"
              disabled={!selectedCrypto}
            />
            <Tab 
              icon={<BookmarkIcon />} 
              label="Guardadas" 
              id="news-tab-2" 
              aria-controls="news-tabpanel-2"
              disabled={savedArticles.length === 0}
            />
          </Tabs>
        </Box>

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
            <CircularProgress />
          </Box>
        ) : (
          <div role="tabpanel" hidden={tabValue !== 0} id="news-tabpanel-0" aria-labelledby="news-tab-0">
            {tabValue === 0 && newsData && (
              <Box className="news-content">
                {newsData.overallSentiment && (
                  <Paper elevation={1} className="sentiment-summary">
                    <Box display="flex" alignItems="center" gap={2}>
                      {getSentimentIcon(newsData.overallSentiment)}
                      <Box>
                        <Typography variant="subtitle1">
                          Sentimiento general: <span style={{ color: getSentimentColor(newsData.overallSentiment) }}>
                            {newsData.overallSentiment.label === 'positive' ? 'Positivo' : 
                             newsData.overallSentiment.label === 'negative' ? 'Negativo' : 'Neutral'}
                          </span>
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Basado en el análisis de {newsData.results.length} artículos recientes
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                )}

                <Grid container spacing={3} className="news-grid">
                  {newsData.results.map((article, index) => (
                    <Grid item xs={12} md={6} lg={4} key={index}>
                      <Card className="news-card">
                        <CardActionArea 
                          component="a" 
                          href={article.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                        >
                          <CardMedia
                            component="img"
                            height="140"
                            image={article.imageUrl || `https://source.unsplash.com/random/300x200?crypto&sig=${index}`}
                            alt={article.title}
                          />
                          <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                              <Typography variant="h6" component="h2" className="news-title">
                                {article.title}
                              </Typography>
                              <Box display="flex" alignItems="center">
                                {getSentimentIcon(article.sentiment)}
                              </Box>
                            </Box>
                            <Typography variant="body2" color="textSecondary" className="news-description">
                              {article.description}
                            </Typography>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                              <Typography variant="caption" color="textSecondary">
                                {article.source || 'Fuente desconocida'} • {formatDate(article.publishedDate)}
                              </Typography>
                            </Box>
                          </CardContent>
                        </CardActionArea>
                        <Box display="flex" justifyContent="flex-end" p={1}>
                          <IconButton 
                            size="small" 
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleSaveArticle(article);
                            }}
                          >
                            {isArticleSaved(article) ? <BookmarkIcon color="primary" /> : <BookmarkBorderIcon />}
                          </IconButton>
                          <IconButton 
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigator.share({
                                title: article.title,
                                text: article.description,
                                url: article.url
                              }).catch(err => console.error('Error al compartir:', err));
                            }}
                          >
                            <ShareIcon />
                          </IconButton>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </div>
        )}

        <div role="tabpanel" hidden={tabValue !== 1} id="news-tabpanel-1" aria-labelledby="news-tab-1">
          {tabValue === 1 && selectedCrypto && newsData && (
            <Box className="news-content">
              <Typography variant="h5" component="h2" gutterBottom>
                Noticias sobre {selectedCrypto.name} ({selectedCrypto.symbol})
              </Typography>
              
              {newsData.overallSentiment && (
                <Paper elevation={1} className="sentiment-summary">
                  <Box display="flex" alignItems="center" gap={2}>
                    {getSentimentIcon(newsData.overallSentiment)}
                    <Box>
                      <Typography variant="subtitle1">
                        Sentimiento general: <span style={{ color: getSentimentColor(newsData.overallSentiment) }}>
                          {newsData.overallSentiment.label === 'positive' ? 'Positivo' : 
                           newsData.overallSentiment.label === 'negative' ? 'Negativo' : 'Neutral'}
                        </span>
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Basado en el análisis de {newsData.results.length} artículos recientes
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              )}

              <Grid container spacing={3} className="news-grid">
                {newsData.results.map((article, index) => (
                  <Grid item xs={12} md={6} lg={4} key={index}>
                    <Card className="news-card">
                      <CardActionArea 
                        component="a" 
                        href={article.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                      >
                        <CardMedia
                          component="img"
                          height="140"
                          image={article.imageUrl || `https://source.unsplash.com/random/300x200?${selectedCrypto.name.toLowerCase()}&sig=${index}`}
                          alt={article.title}
                        />
                        <CardContent>
                          <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                            <Typography variant="h6" component="h2" className="news-title">
                              {article.title}
                            </Typography>
                            <Box display="flex" alignItems="center">
                              {getSentimentIcon(article.sentiment)}
                            </Box>
                          </Box>
                          <Typography variant="body2" color="textSecondary" className="news-description">
                            {article.description}
                          </Typography>
                          <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                            <Typography variant="caption" color="textSecondary">
                              {article.source || 'Fuente desconocida'} • {formatDate(article.publishedDate)}
                            </Typography>
                          </Box>
                        </CardContent>
                      </CardActionArea>
                      <Box display="flex" justifyContent="flex-end" p={1}>
                        <IconButton 
                          size="small" 
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleSaveArticle(article);
                          }}
                        >
                          {isArticleSaved(article) ? <BookmarkIcon color="primary" /> : <BookmarkBorderIcon />}
                        </IconButton>
                        <IconButton 
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigator.share({
                              title: article.title,
                              text: article.description,
                              url: article.url
                            }).catch(err => console.error('Error al compartir:', err));
                          }}
                        >
                          <ShareIcon />
                        </IconButton>
                      </Box>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </div>

        <div role="tabpanel" hidden={tabValue !== 2} id="news-tabpanel-2" aria-labelledby="news-tab-2">
          {tabValue === 2 && (
            <Box className="news-content">
              <Typography variant="h5" component="h2" gutterBottom>
                Artículos guardados
              </Typography>
              
              {savedArticles.length === 0 ? (
                <Paper elevation={1} className="empty-state">
                  <Typography variant="body1" align="center">
                    No tienes artículos guardados. Guarda artículos haciendo clic en el icono de marcador.
                  </Typography>
                </Paper>
              ) : (
                <Grid container spacing={3} className="news-grid">
                  {savedArticles.map((article, index) => (
                    <Grid item xs={12} md={6} lg={4} key={index}>
                      <Card className="news-card">
                        <CardActionArea 
                          component="a" 
                          href={article.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                        >
                          <CardMedia
                            component="img"
                            height="140"
                            image={article.imageUrl || `https://source.unsplash.com/random/300x200?crypto&sig=${index}`}
                            alt={article.title}
                          />
                          <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                              <Typography variant="h6" component="h2" className="news-title">
                                {article.title}
                              </Typography>
                              <Box display="flex" alignItems="center">
                                {getSentimentIcon(article.sentiment)}
                              </Box>
                            </Box>
                            <Typography variant="body2" color="textSecondary" className="news-description">
                              {article.description}
                            </Typography>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                              <Typography variant="caption" color="textSecondary">
                                {article.source || 'Fuente desconocida'} • {formatDate(article.publishedDate)}
                              </Typography>
                            </Box>
                          </CardContent>
                        </CardActionArea>
                        <Box display="flex" justifyContent="flex-end" p={1}>
                          <IconButton 
                            size="small" 
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleSaveArticle(article);
                            }}
                          >
                            <BookmarkIcon color="primary" />
                          </IconButton>
                          <IconButton 
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigator.share({
                                title: article.title,
                                text: article.description,
                                url: article.url
                              }).catch(err => console.error('Error al compartir:', err));
                            }}
                          >
                            <ShareIcon />
                          </IconButton>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>
          )}
        </div>
      </Container>
    </Layout>
  );
};

export default EnhancedNewsPage;
