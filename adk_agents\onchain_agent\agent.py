"""
On-Chain Analysis Agent using Google ADK
"""
import os
import json
import aiohttp
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from google.adk.runtime import InvocationContext

# API Keys
ETHERSCAN_API_KEY = os.getenv("ETHERSCAN_API_KEY", "**********************************")

# Function tools for the agent
async def fetch_token_transfers(token_address: str, days: int = 7) -> Dict[str, Any]:
    """
    Fetch token transfers from blockchain.

    Args:
        token_address: The token contract address
        days: Number of days of data to fetch

    Returns:
        Token transfer data
    """
    try:
        # Calculate start timestamp
        end_timestamp = int(datetime.now().timestamp())
        start_timestamp = end_timestamp - (days * 24 * 60 * 60)

        # Etherscan API endpoint
        url = "https://api.etherscan.io/api"

        # Parameters for token transfers
        params = {
            "module": "account",
            "action": "tokentx",
            "contractaddress": token_address,
            "startblock": "0",
            "endblock": "*********",
            "sort": "desc",
            "apikey": ETHERSCAN_API_KEY
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == "1":
                        # Filter by timestamp
                        transfers = []
                        for tx in result.get("result", []):
                            tx_timestamp = int(tx.get("timeStamp", "0"))
                            if tx_timestamp >= start_timestamp:
                                transfers.append(tx)

                        return {
                            "status": "success",
                            "token_address": token_address,
                            "transfers": transfers[:100]  # Limit to 100 transfers
                        }

        # Fallback to simulated data
        print("Failed to fetch token transfers from Etherscan, using simulated data")
        return _generate_simulated_transfers(token_address, days)
    except Exception as e:
        print(f"Error fetching token transfers: {e}")
        return _generate_simulated_transfers(token_address, days)

def _generate_simulated_transfers(token_address: str, days: int) -> Dict[str, Any]:
    """Generate simulated token transfers."""
    import random

    # Generate random addresses
    addresses = [f"0x{i:040x}" for i in range(1, 21)]

    # Generate random transfers
    transfers = []
    end_timestamp = int(datetime.now().timestamp())

    for i in range(100):  # Generate 100 transfers
        # Random timestamp within the specified days
        timestamp = end_timestamp - random.randint(0, days * 24 * 60 * 60)

        # Random addresses
        from_address = random.choice(addresses)
        to_address = random.choice([addr for addr in addresses if addr != from_address])

        # Random value (in token units)
        value = random.uniform(0.1, 1000) * (10 ** 18)  # Simulating 18 decimals

        transfers.append({
            "blockNumber": str(random.randint(10000000, 20000000)),
            "timeStamp": str(timestamp),
            "hash": f"0x{random.getrandbits(256):064x}",
            "from": from_address,
            "to": to_address,
            "value": str(int(value)),
            "tokenName": "Simulated Token",
            "tokenSymbol": "SIM",
            "tokenDecimal": "18",
            "gas": str(random.randint(21000, 100000)),
            "gasPrice": str(random.randint(1, 100) * (10 ** 9)),  # Gwei to Wei
            "gasUsed": str(random.randint(21000, 100000)),
            "_simulated": True
        })

    # Sort by timestamp (newest first)
    transfers.sort(key=lambda x: int(x.get("timeStamp", "0")), reverse=True)

    return {
        "status": "success",
        "token_address": token_address,
        "transfers": transfers
    }

async def analyze_whale_activity(transfers: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze whale activity from token transfers.

    Args:
        transfers: List of token transfers

    Returns:
        Whale activity analysis
    """
    try:
        if not transfers:
            return {
                "status": "error",
                "message": "No transfers to analyze"
            }

        # Extract token details from first transfer
        token_name = transfers[0].get("tokenName", "Unknown")
        token_symbol = transfers[0].get("tokenSymbol", "???")
        token_decimal = int(transfers[0].get("tokenDecimal", "18"))

        # Calculate total volume
        total_volume = 0
        for tx in transfers:
            value = int(tx.get("value", "0"))
            total_volume += value

        # Convert to token units
        total_volume_tokens = total_volume / (10 ** token_decimal)

        # Identify large transfers (whales)
        whale_threshold = total_volume_tokens * 0.01  # 1% of total volume
        whale_transfers = []

        for tx in transfers:
            value = int(tx.get("value", "0"))
            value_tokens = value / (10 ** token_decimal)

            if value_tokens >= whale_threshold:
                whale_transfers.append({
                    "hash": tx.get("hash"),
                    "from": tx.get("from"),
                    "to": tx.get("to"),
                    "value": value_tokens,
                    "timestamp": datetime.fromtimestamp(int(tx.get("timeStamp", "0"))).isoformat()
                })

        # Analyze whale addresses
        addresses = {}
        for tx in transfers:
            from_addr = tx.get("from")
            to_addr = tx.get("to")
            value = int(tx.get("value", "0"))
            value_tokens = value / (10 ** token_decimal)

            # Track outgoing transfers
            if from_addr not in addresses:
                addresses[from_addr] = {"sent": 0, "received": 0}
            addresses[from_addr]["sent"] += value_tokens

            # Track incoming transfers
            if to_addr not in addresses:
                addresses[to_addr] = {"sent": 0, "received": 0}
            addresses[to_addr]["received"] += value_tokens

        # Calculate net flow for each address
        for addr, flows in addresses.items():
            flows["net"] = flows["received"] - flows["sent"]

        # Identify top accumulators and distributors
        sorted_by_net = sorted(addresses.items(), key=lambda x: x[1]["net"], reverse=True)
        accumulators = sorted_by_net[:5]  # Top 5 accumulators
        distributors = sorted_by_net[-5:]  # Top 5 distributors

        return {
            "status": "success",
            "token_name": token_name,
            "token_symbol": token_symbol,
            "total_transfers": len(transfers),
            "total_volume_tokens": total_volume_tokens,
            "whale_transfers": whale_transfers[:10],  # Top 10 whale transfers
            "top_accumulators": [
                {"address": addr, "net_flow": flows["net"]}
                for addr, flows in accumulators
            ],
            "top_distributors": [
                {"address": addr, "net_flow": flows["net"]}
                for addr, flows in distributors
            ]
        }
    except Exception as e:
        print(f"Error analyzing whale activity: {e}")
        return {
            "status": "error",
            "message": f"Error analyzing whale activity: {str(e)}"
        }

async def fetch_gas_prices() -> Dict[str, Any]:
    """
    Fetch current Ethereum gas prices.

    Returns:
        Gas price data
    """
    try:
        # Etherscan API endpoint
        url = "https://api.etherscan.io/api"

        # Parameters for gas oracle
        params = {
            "module": "gastracker",
            "action": "gasoracle",
            "apikey": ETHERSCAN_API_KEY
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == "1":
                        return {
                            "status": "success",
                            "gas_prices": result.get("result", {})
                        }

        # Fallback to simulated data
        print("Failed to fetch gas prices from Etherscan, using simulated data")
        return _generate_simulated_gas_prices()
    except Exception as e:
        print(f"Error fetching gas prices: {e}")
        return _generate_simulated_gas_prices()

def _generate_simulated_gas_prices() -> Dict[str, Any]:
    """Generate simulated gas prices."""
    import random

    base_price = random.randint(20, 50)

    return {
        "status": "success",
        "gas_prices": {
            "SafeGasPrice": str(base_price),
            "ProposeGasPrice": str(base_price + random.randint(5, 15)),
            "FastGasPrice": str(base_price + random.randint(20, 40)),
            "suggestBaseFee": str(random.uniform(1, 5)),
            "gasUsedRatio": ",".join([str(random.uniform(0.3, 0.9)) for _ in range(4)])
        }
    }

def get_token_address(crypto_name: str) -> str:
    """Get token address for a cryptocurrency."""
    # Common token addresses on Ethereum
    token_addresses = {
        "ethereum": "******************************************",  # WETH
        "eth": "******************************************",  # WETH
        "usdt": "******************************************",  # Tether
        "tether": "******************************************",  # Tether
        "usdc": "******************************************",  # USD Coin
        "usd coin": "******************************************",  # USD Coin
        "bnb": "******************************************",  # Binance Coin (BEP-2)
        "binance coin": "******************************************",  # Binance Coin (BEP-2)
        "link": "******************************************",  # Chainlink
        "chainlink": "******************************************",  # Chainlink
        "uni": "******************************************",  # Uniswap
        "uniswap": "******************************************",  # Uniswap
        "aave": "******************************************",  # Aave
        "shib": "******************************************",  # Shiba Inu
        "shiba inu": "******************************************",  # Shiba Inu
        "dai": "******************************************",  # Dai
    }

    crypto_name_lower = crypto_name.lower()

    # Check for exact matches
    for token_name, address in token_addresses.items():
        if token_name in crypto_name_lower:
            return address

    # Default to WETH if no match found
    return "******************************************"

def extract_crypto_name(query: str) -> str:
    """Extract cryptocurrency name from query."""
    # Common cryptocurrencies
    crypto_names = {
        "ethereum": "Ethereum",
        "eth": "Ethereum",
        "usdt": "Tether",
        "tether": "Tether",
        "usdc": "USD Coin",
        "usd coin": "USD Coin",
        "bnb": "Binance Coin",
        "binance coin": "Binance Coin",
        "link": "Chainlink",
        "chainlink": "Chainlink",
        "uni": "Uniswap",
        "uniswap": "Uniswap",
        "aave": "Aave",
        "shib": "Shiba Inu",
        "shiba inu": "Shiba Inu",
        "dai": "Dai"
    }

    query_lower = query.lower()

    # Check for exact matches
    for crypto_name_lower, crypto_name in crypto_names.items():
        if crypto_name_lower in query_lower:
            return crypto_name

    # Default to Ethereum if no match found
    return "Ethereum"

def extract_timeframe(query: str) -> int:
    """Extract timeframe from query."""
    query_lower = query.lower()

    if "year" in query_lower or "365" in query_lower:
        return 365
    elif "6 month" in query_lower or "180" in query_lower:
        return 180
    elif "3 month" in query_lower or "90" in query_lower:
        return 90
    elif "month" in query_lower or "30" in query_lower:
        return 30
    elif "week" in query_lower or "7" in query_lower:
        return 7
    elif "day" in query_lower or "24" in query_lower:
        return 1

    # Default to 7 days
    return 7

async def analyze_onchain_data(query: str, ctx: InvocationContext) -> str:
    """
    Analyze on-chain data for a cryptocurrency based on user query.

    Args:
        query: User query about cryptocurrency on-chain data
        ctx: Invocation context

    Returns:
        On-chain analysis result
    """
    # Extract crypto name and timeframe from query
    crypto_name = extract_crypto_name(query)
    days = extract_timeframe(query)

    # Get token address
    token_address = get_token_address(crypto_name.lower())

    # Fetch token transfers
    transfers_data = await fetch_token_transfers(token_address, days)

    # Analyze whale activity
    whale_analysis = await analyze_whale_activity(transfers_data.get("transfers", []))

    # Fetch gas prices
    gas_prices = await fetch_gas_prices()

    # Prepare result
    result = {
        "crypto_name": crypto_name,
        "token_address": token_address,
        "timeframe_days": days,
        "whale_analysis": whale_analysis,
        "gas_prices": gas_prices.get("gas_prices", {}),
        "transfers_count": len(transfers_data.get("transfers", []))
    }

    # Store results in session state
    ctx.session.state["crypto_name"] = crypto_name
    ctx.session.state["token_address"] = token_address
    ctx.session.state["timeframe_days"] = days
    ctx.session.state["onchain_analysis"] = result

    # Return structured data for the LLM to format
    return json.dumps(result)

# Create the on-chain analysis agent
onchain_agent = LlmAgent(
    name="onchain_analysis_agent",
    model="gemini-1.5-pro",
    description="Analyzes on-chain data for cryptocurrencies",
    instruction="""
    You are a blockchain and on-chain data analysis expert. Your task is to:

    1. Analyze the on-chain data provided to you
    2. Explain what the whale activity indicates about large holder behavior
    3. Interpret the gas prices and their implications for network activity
    4. Identify significant patterns in token transfers
    5. Provide a clear, concise on-chain analysis summary

    When responding:
    - Be specific about what the on-chain metrics mean
    - Explain the significance of whale accumulation or distribution
    - Highlight any unusual patterns in the data
    - Provide a conclusion about what the on-chain data suggests for the token

    The on-chain data will be provided to you as JSON data.
    """,
    tools=[FunctionTool(func=analyze_onchain_data)],
    output_key="onchain_analysis"
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime
    import asyncio

    async def main():
        runtime = Runtime()
        session = runtime.new_session()

        # Test the agent with a query
        response = await onchain_agent.run_async(
            session=session,
            query="Analyze on-chain data for Ethereum for the last week"
        )

        print(response)

    asyncio.run(main())
