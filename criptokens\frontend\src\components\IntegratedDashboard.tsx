import React, { useState, useEffect, useRef } from 'react';
import { getTopCryptocurrencies, getGlobalMarketData, getCryptoHistoricalData } from '../services/api';
import { useTopCryptocurrencies, useCryptoHistoricalData } from '../hooks/useMcpClient';
import { useRadarCripto } from '../hooks/useRadarCripto';
import { usePortfolio } from '../hooks/usePortfolio';
import { useNavigate } from 'react-router-dom';
import CriptoAgentAvatar from './CriptoAgentAvatarExport';
import EnhancedCrypto<PERSON>hart from './EnhancedCryptoChart';
import TechnicalAnalysisChart from './TechnicalAnalysisChart';
import AdvancedMarketVisualizations from './AdvancedMarketVisualizations';
import PortfolioFirebase from './PortfolioFirebase';
import MiRadarCripto from './MiRadarCripto';
import PriceAlerts from './PriceAlerts';
import AlertNotifications from './AlertNotifications';
import { useAlertNotifications } from '../hooks/useCryptoData';
import Modern<PERSON>ogo from './ModernLogo';
import PortfolioSummaryWidget from './PortfolioSummaryWidget';
import GuruInsightWidget from './GuruInsightWidget';
import EnhancedCryptoTable from './EnhancedCryptoTable';
import '../styles/DesignSystem.css';
import '../styles/FuturisticTheme.css';
import '../styles/FuturisticDashboard.css';
import '../styles/AlertsSection.css';
import '../styles/IntegratedDashboard.css';
import '../styles/AdvancedComponents.css';
import '../styles/DashboardImproved.css';
import '../styles/MiRadarCripto.css';
import '../styles/EnhancedCryptoTable.css';
import '../styles/PortfolioSummaryWidget.css';
import '../styles/GuruInsightWidget.css';
import '../styles/ProfessionalDashboard.css';

const IntegratedDashboard: React.FC = () => {
  // Estados para la navegación
  const [activeSection, setActiveSection] = useState<string>('dashboard');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const navigate = useNavigate();

  // Obtener funciones de radar
  const { addToRadar, removeFromRadar, isInRadar } = useRadarCripto();

  // Estados para los datos de criptomonedas
  const [cryptos, setCryptos] = useState<any[]>([]);
  const [marketData, setMarketData] = useState<any>(null);
  const [selectedCrypto, setSelectedCrypto] = useState<any>(null);
  const [historicalData, setHistoricalData] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<number | string>(7);
  const [chartData, setChartData] = useState<{ labels: string[], values: number[] }>({ labels: [], values: [] });

  // Estados para el agente virtual
  const [agentMood, setAgentMood] = useState<string>('neutral');
  const [agentMessage, setAgentMessage] = useState<string>('');
  const [showAgentMessage, setShowAgentMessage] = useState<boolean>(false);

  // Estados para la interfaz
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [mcpServerStatus, setMcpServerStatus] = useState<string>('online');

  // Referencias para animaciones
  const dashboardRef = useRef<HTMLDivElement>(null);

  // Obtener datos de criptomonedas al cargar el componente
  useEffect(() => {
    // Llamada inmediata
    fetchInitialData();

    // Forzar una segunda carga después de 2 segundos para asegurar que los datos se muestren
    const initialTimeout = setTimeout(() => {
      fetchInitialData();
    }, 2000);

    // Actualizar datos cada 60 segundos
    const interval = setInterval(fetchInitialData, 60000);

    return () => {
      clearInterval(interval);
      clearTimeout(initialTimeout);
    };
  }, []);

  // Estado para el análisis del mercado
  const [marketAnalysisData, setMarketAnalysisData] = useState<any>(null);

  // Actualizar el análisis del mercado periódicamente
  useEffect(() => {
    // Función para actualizar el análisis
    const updateMarketAnalysis = () => {
      const analysis = analyzeMarketBehavior();
      setMarketAnalysisData(analysis);
    };

    // Actualizar inmediatamente
    updateMarketAnalysis();

    // Actualizar cada 30 segundos
    const interval = setInterval(updateMarketAnalysis, 30000);
    return () => clearInterval(interval);
  }, [cryptos]);

  // Procesar datos históricos para el gráfico cuando cambian
  useEffect(() => {
    console.log('Efecto de formateo de datos históricos ejecutado, datos:', historicalData);

    if (historicalData) {
      try {
        // Si los datos tienen el formato de la API de CoinGecko
        if (historicalData.prices && Array.isArray(historicalData.prices)) {
          console.log('Formateando datos históricos con formato CoinGecko...');
          const prices = historicalData.prices;
          const formattedData = {
            labels: prices.map((price: [number, number]) => {
              const date = new Date(price[0]);
              return date.toLocaleDateString();
            }),
            values: prices.map((price: [number, number]) => price[1])
          };
          console.log('Datos formateados:', formattedData);
          setChartData(formattedData);
        }
        // Si los datos son un array simple (formato de datos simulados)
        else if (Array.isArray(historicalData)) {
          console.log('Formateando datos históricos con formato array simple...');
          const formattedData = {
            labels: historicalData.map((item: any) => {
              const date = new Date(item[0]);
              return date.toLocaleDateString();
            }),
            values: historicalData.map((item: any) => item[1])
          };
          console.log('Datos formateados:', formattedData);
          setChartData(formattedData);
        }
        // Si los datos tienen otro formato, intentar extraer la información relevante
        else if (typeof historicalData === 'object') {
          console.log('Intentando extraer datos históricos de un objeto...');
          // Buscar cualquier propiedad que pueda contener los precios
          const possiblePricesProps = ['prices', 'price', 'data', 'values'];
          let pricesData = null;

          for (const prop of possiblePricesProps) {
            if (historicalData[prop] && Array.isArray(historicalData[prop])) {
              pricesData = historicalData[prop];
              break;
            }
          }

          if (pricesData) {
            const formattedData = {
              labels: pricesData.map((item: any) => {
                const timestamp = Array.isArray(item) ? item[0] : item.timestamp || item.date || item.time;
                const date = new Date(timestamp);
                return date.toLocaleDateString();
              }),
              values: pricesData.map((item: any) => Array.isArray(item) ? item[1] : item.price || item.value || item)
            };
            console.log('Datos extraídos y formateados:', formattedData);
            setChartData(formattedData);
          } else {
            console.error('No se pudieron encontrar datos de precios en el objeto:', historicalData);
          }
        } else {
          console.error('Formato de datos históricos no reconocido:', historicalData);
        }
      } catch (error) {
        console.error('Error al procesar datos históricos:', error);
      }
    } else {
      console.warn('No hay datos históricos disponibles');
    }
  }, [historicalData]);

  // Función para obtener datos iniciales
  const fetchInitialData = async () => {
    try {
      setIsLoading(true);
      console.log('Iniciando carga de datos...');

      // Obtener las principales criptomonedas
      console.log('Obteniendo top criptomonedas...');
      const cryptoData = await getTopCryptocurrencies(20);
      console.log('Datos de criptomonedas recibidos:', cryptoData);

      // Verificar que los datos son válidos
      if (Array.isArray(cryptoData) && cryptoData.length > 0) {
        console.log('Datos válidos, actualizando estado...');
        setCryptos(cryptoData);

        // Seleccionar Bitcoin por defecto si no hay criptomoneda seleccionada
        if (!selectedCrypto) {
          console.log('Seleccionando Bitcoin por defecto...');
          const bitcoin = cryptoData.find((crypto: any) => crypto.id === 'bitcoin');
          if (bitcoin) {
            console.log('Bitcoin encontrado:', bitcoin);
            setSelectedCrypto(bitcoin);

            try {
              // Obtener datos históricos de Bitcoin
              console.log('Obteniendo datos históricos de Bitcoin...');
              const btcHistoricalData = await getCryptoHistoricalData('bitcoin', timeRange);
              console.log('Datos históricos recibidos:', btcHistoricalData);

              // Manejar diferentes formatos de datos históricos
              if (btcHistoricalData) {
                if (btcHistoricalData.prices && Array.isArray(btcHistoricalData.prices)) {
                  console.log('Actualizando datos históricos (formato CoinGecko)...');
                  setHistoricalData(btcHistoricalData);
                } else if (Array.isArray(btcHistoricalData)) {
                  console.log('Actualizando datos históricos (formato array)...');
                  setHistoricalData(btcHistoricalData);
                } else {
                  console.log('Formato de datos históricos desconocido, usando el objeto completo...');
                  setHistoricalData(btcHistoricalData);
                }
              }
            } catch (histError) {
              console.error('Error al cargar datos históricos:', histError);
            }
          }
        }

        // Actualizar datos del mercado global
        try {
          console.log('Obteniendo datos del mercado global...');
          const globalData = await getGlobalMarketData();
          console.log('Datos del mercado global recibidos:', globalData);
          if (globalData && globalData.data) {
            console.log('Actualizando datos del mercado global...');
            setMarketData(globalData.data);
          }
        } catch (marketError) {
          console.error('Error al cargar datos del mercado global:', marketError);
        }

        // Mostrar mensaje de bienvenida del agente
        if (!agentMessage) {
          setAgentMood('happy');
          setAgentMessage('¡Bienvenido al Dashboard Integrado de Criptokens!');
          setShowAgentMessage(true);

          // Ocultar el mensaje después de 3 segundos
          setTimeout(() => {
            setShowAgentMessage(false);
            setAgentMood('neutral');
          }, 3000);
        }
      } else {
        console.error('Los datos de criptomonedas no son válidos:', cryptoData);
        // Usar datos de respaldo si es necesario
        if (cryptos.length === 0) {
          // Cargar datos de respaldo desde mockData
          console.log('Cargando datos de respaldo desde mockData...');
          // Importar directamente desde mockData
          import('../services/mockData').then(mockData => {
            console.log('Datos de respaldo cargados:', mockData);
            setCryptos(mockData.mockTopCryptos);
            setMarketData(mockData.mockGlobalData.data);

            // Seleccionar Bitcoin por defecto
            const bitcoin = mockData.mockTopCryptos.find((crypto: any) => crypto.id === 'bitcoin');
            if (bitcoin) {
              setSelectedCrypto(bitcoin);
              setHistoricalData(mockData.mockHistoricalData.bitcoin);
            }

            setAgentMood('thinking');
            setAgentMessage('Usando datos en caché mientras se restablece la conexión...');
            setShowAgentMessage(true);
          }).catch(error => {
            console.error('Error al cargar datos de respaldo:', error);
          });
        }
      }
    } catch (error) {
      console.error('Error al cargar datos iniciales:', error);
      setAgentMood('sad');
      setAgentMessage('Error al cargar datos. Intentando usar datos en caché...');
      setShowAgentMessage(true);

      // Cargar datos de respaldo en caso de error
      import('../services/mockData').then(mockData => {
        console.log('Datos de respaldo cargados después de error:', mockData);
        setCryptos(mockData.mockTopCryptos);
        setMarketData(mockData.mockGlobalData.data);

        // Seleccionar Bitcoin por defecto
        const bitcoin = mockData.mockTopCryptos.find((crypto: any) => crypto.id === 'bitcoin');
        if (bitcoin) {
          setSelectedCrypto(bitcoin);
          setHistoricalData(mockData.mockHistoricalData.bitcoin);
        }
      }).catch(err => {
        console.error('Error al cargar datos de respaldo después de error:', err);
      });
    } finally {
      console.log('Finalizando carga de datos, isLoading = false');
      setIsLoading(false);
    }
  };

  // Función para manejar la selección de una criptomoneda
  const handleCryptoSelect = async (crypto: any) => {
    try {
      setSelectedCrypto(crypto);
      setAgentMood('thinking');
      setAgentMessage(`Obteniendo datos de ${crypto.name}...`);
      setShowAgentMessage(true);

      // Obtener datos históricos de la criptomoneda seleccionada
      console.log(`Obteniendo datos históricos para ${crypto.name} (${crypto.id})...`);
      const historicalData = await getCryptoHistoricalData(crypto.id, timeRange);
      console.log(`Datos históricos recibidos para ${crypto.name}:`, historicalData);

      // Manejar diferentes formatos de datos históricos
      if (historicalData) {
        if (historicalData.prices && Array.isArray(historicalData.prices)) {
          console.log(`Actualizando datos históricos para ${crypto.name} (formato CoinGecko)...`);
          setHistoricalData(historicalData);
        } else if (Array.isArray(historicalData)) {
          console.log(`Actualizando datos históricos para ${crypto.name} (formato array)...`);
          setHistoricalData(historicalData);
        } else {
          console.log(`Formato de datos históricos desconocido para ${crypto.name}, usando el objeto completo...`);
          setHistoricalData(historicalData);
        }
      } else {
        console.error(`No se recibieron datos históricos para ${crypto.name}`);
        // Intentar cargar datos de respaldo
        try {
          const mockDataModule = await import('../services/mockData');
          if (mockDataModule.mockHistoricalData[crypto.id as keyof typeof mockDataModule.mockHistoricalData]) {
            console.log(`Usando datos históricos de respaldo para ${crypto.name}...`);
            setHistoricalData(mockDataModule.mockHistoricalData[crypto.id as keyof typeof mockDataModule.mockHistoricalData]);
          } else {
            console.log(`Generando datos históricos para ${crypto.name}...`);
            const basePrice = crypto.current_price || 100;
            const volatility = basePrice * 0.1; // 10% de volatilidad
            setHistoricalData(mockDataModule.generateHistoricalData(30, basePrice, volatility));
          }
        } catch (mockError) {
          console.error(`Error al cargar datos históricos de respaldo para ${crypto.name}:`, mockError);
        }
      }

      // Actualizar el mensaje del agente con un análisis simple
      const priceChange = crypto.price_change_percentage_24h;
      let mood = 'neutral';
      let message = '';

      if (priceChange > 5) {
        mood = 'excited';
        message = `¡${crypto.name} está en alza! Ha subido un ${priceChange.toFixed(2)}% en las últimas 24 horas.`;
      } else if (priceChange > 0) {
        mood = 'happy';
        message = `${crypto.name} muestra una tendencia positiva con un aumento del ${priceChange.toFixed(2)}% en 24 horas.`;
      } else if (priceChange > -5) {
        mood = 'neutral';
        message = `${crypto.name} está relativamente estable con un cambio del ${priceChange.toFixed(2)}% en 24 horas.`;
      } else {
        mood = 'sad';
        message = `${crypto.name} ha caído un ${Math.abs(priceChange).toFixed(2)}% en las últimas 24 horas. Mantente atento a la evolución.`;
      }

      setAgentMood(mood);
      setAgentMessage(message);
    } catch (error) {
      console.error(`Error al obtener datos de ${crypto.name}:`, error);
      setAgentMood('sad');
      setAgentMessage(`Error al obtener datos de ${crypto.name}.`);

      // Intentar cargar datos de respaldo en caso de error
      try {
        const mockDataModule = await import('../services/mockData');
        if (mockDataModule.mockHistoricalData[crypto.id as keyof typeof mockDataModule.mockHistoricalData]) {
          console.log(`Usando datos históricos de respaldo para ${crypto.name} después de error...`);
          setHistoricalData(mockDataModule.mockHistoricalData[crypto.id as keyof typeof mockDataModule.mockHistoricalData]);
        } else {
          console.log(`Generando datos históricos para ${crypto.name} después de error...`);
          const basePrice = crypto.current_price || 100;
          const volatility = basePrice * 0.1; // 10% de volatilidad
          setHistoricalData(mockDataModule.generateHistoricalData(30, basePrice, volatility));
        }
      } catch (mockError) {
        console.error(`Error al cargar datos históricos de respaldo para ${crypto.name}:`, mockError);
      }
    } finally {
      // Ocultar el mensaje después de 3 segundos
      setTimeout(() => {
        setShowAgentMessage(false);
        setAgentMood('neutral');
      }, 3000);
    }
  };

  // Función para manejar el cambio de rango de tiempo
  const handleTimeRangeChange = async (range: number | string) => {
    if (!selectedCrypto) return;

    try {
      // Convertir el rango a número si es posible
      const days = typeof range === 'number' ? range : range === 'max' ? 'max' : parseInt(range as string);
      setTimeRange(typeof days === 'number' ? days : 'max');

      setAgentMood('thinking');
      setAgentMessage(`Actualizando datos para ${range}...`);
      setShowAgentMessage(true);

      // Obtener datos históricos con el nuevo rango de tiempo
      console.log(`Obteniendo datos históricos para ${selectedCrypto.name} con rango ${range}...`);
      const historicalData = await getCryptoHistoricalData(selectedCrypto.id, days);
      console.log(`Datos históricos recibidos para rango ${range}:`, historicalData);

      // Manejar diferentes formatos de datos históricos
      if (historicalData) {
        if (historicalData.prices && Array.isArray(historicalData.prices)) {
          console.log(`Actualizando datos históricos para rango ${range} (formato CoinGecko)...`);
          setHistoricalData(historicalData);
        } else if (Array.isArray(historicalData)) {
          console.log(`Actualizando datos históricos para rango ${range} (formato array)...`);
          setHistoricalData(historicalData);
        } else {
          console.log(`Formato de datos históricos desconocido para rango ${range}, usando el objeto completo...`);
          setHistoricalData(historicalData);
        }
      } else {
        console.error(`No se recibieron datos históricos para rango ${range}`);
        // Intentar cargar datos de respaldo
        try {
          const mockDataModule = await import('../services/mockData');
          console.log(`Generando datos históricos para rango ${range}...`);
          const basePrice = selectedCrypto.current_price || 100;
          const volatility = basePrice * 0.1; // 10% de volatilidad
          const daysNum = typeof days === 'number' ? days : 30;
          setHistoricalData(mockDataModule.generateHistoricalData(daysNum, basePrice, volatility));
        } catch (mockError) {
          console.error(`Error al generar datos históricos de respaldo para rango ${range}:`, mockError);
        }
      }

      setAgentMood('happy');
      setAgentMessage(`Datos actualizados para ${range}.`);
    } catch (error) {
      console.error(`Error al obtener datos históricos:`, error);
      setAgentMood('sad');
      setAgentMessage(`Error al actualizar datos.`);

      // Intentar cargar datos de respaldo en caso de error
      try {
        const mockDataModule = await import('../services/mockData');
        console.log(`Generando datos históricos de respaldo para rango ${range} después de error...`);
        const basePrice = selectedCrypto.current_price || 100;
        const volatility = basePrice * 0.1; // 10% de volatilidad
        const daysNum = typeof range === 'number' ? range : range === 'max' ? 365 : parseInt(range as string);
        setHistoricalData(mockDataModule.generateHistoricalData(daysNum, basePrice, volatility));
      } catch (mockError) {
        console.error(`Error al generar datos históricos de respaldo para rango ${range}:`, mockError);
      }
    } finally {
      // Ocultar el mensaje después de 3 segundos
      setTimeout(() => {
        setShowAgentMessage(false);
        setAgentMood('neutral');
      }, 3000);
    }
  };

  // Función para manejar la búsqueda
  const handleSearch = () => {
    if (!searchQuery.trim()) return;

    const query = searchQuery.toLowerCase();
    const filtered = cryptos.filter((crypto: any) =>
      crypto.name.toLowerCase().includes(query) ||
      crypto.symbol.toLowerCase().includes(query)
    );

    if (filtered.length > 0) {
      // Seleccionar la primera criptomoneda encontrada
      handleCryptoSelect(filtered[0]);
      setAgentMood('happy');
      setAgentMessage(`Encontré ${filtered.length} resultados para "${searchQuery}". Mostrando ${filtered[0].name}.`);
    } else {
      setAgentMood('thinking');
      setAgentMessage(`No encontré resultados para "${searchQuery}". Intenta con otro término.`);
    }

    setShowAgentMessage(true);

    // Ocultar el mensaje después de 3 segundos
    setTimeout(() => {
      setShowAgentMessage(false);
      setAgentMood('neutral');
    }, 3000);
  };

  // Función para formatear números grandes
  const formatNumber = (num: number): string => {
    if (num >= 1e12) return (num / 1e12).toFixed(2) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toFixed(2);
  };

  // Función para obtener el color según el cambio de precio
  const getPriceChangeColor = (change: number): string => {
    if (change > 3) return '#00ff9d'; // Verde fuerte para cambios muy positivos
    if (change > 0) return '#4caf50'; // Verde para cambios positivos
    if (change < -3) return '#ff3a6e'; // Rojo fuerte para cambios muy negativos
    if (change < 0) return '#f44336'; // Rojo para cambios negativos
    return '#ffab00'; // Amarillo para cambios neutros
  };

  // Función para analizar el comportamiento de las criptomonedas y generar reacciones del agente
  const analyzeMarketBehavior = () => {
    if (!cryptos || cryptos.length === 0) return null;

    // Calcular métricas generales del mercado
    const totalMarketChange = cryptos.reduce((sum, crypto: any) => sum + crypto.price_change_percentage_24h, 0) / cryptos.length;
    const volatilityIndex = cryptos.reduce((sum, crypto: any) => sum + Math.abs(crypto.price_change_percentage_24h), 0) / cryptos.length;

    // Identificar tendencias
    const positiveChanges = cryptos.filter((crypto: any) => crypto.price_change_percentage_24h > 0);
    const negativeChanges = cryptos.filter((crypto: any) => crypto.price_change_percentage_24h < 0);
    const marketSentiment = positiveChanges.length > negativeChanges.length ? 'bullish' : 'bearish';

    // Buscar criptomonedas con cambios significativos
    const significantChanges = cryptos.filter((crypto: any) =>
      Math.abs(crypto.price_change_percentage_24h) > 5
    );

    // Buscar patrones de mercado
    const topCryptos = [...cryptos].sort((a: any, b: any) => b.market_cap - a.market_cap).slice(0, 5);
    const topCryptosAvgChange = topCryptos.reduce((sum, crypto: any) => sum + crypto.price_change_percentage_24h, 0) / topCryptos.length;
    const altCoins = cryptos.filter((crypto: any) => !topCryptos.includes(crypto));
    const altCoinsAvgChange = altCoins.length > 0 ? altCoins.reduce((sum, crypto: any) => sum + crypto.price_change_percentage_24h, 0) / altCoins.length : 0;

    // Detectar rotación de capital
    const capitalRotation = topCryptosAvgChange < altCoinsAvgChange ? 'hacia altcoins' : 'hacia blue chips';

    // Determinar el estado de ánimo y mensaje del agente
    let mood = 'neutral';
    let message = '';
    let highlightedCrypto = null;

    // Priorizar análisis basado en diferentes factores
    if (significantChanges.length > 0) {
      // Ordenar por magnitud del cambio (absoluto)
      significantChanges.sort((a: any, b: any) =>
        Math.abs(b.price_change_percentage_24h) - Math.abs(a.price_change_percentage_24h)
      );

      const topChange = significantChanges[0];
      const isPositive = topChange.price_change_percentage_24h > 0;

      mood = isPositive ? 'happy' : 'sad';
      highlightedCrypto = topChange;

      if (isPositive) {
        message = `¡${topChange.name} está subiendo un ${topChange.price_change_percentage_24h.toFixed(2)}%! Detectando patrón alcista con volumen ${formatNumber(topChange.total_volume)} en 24h.`;
      } else {
        message = `${topChange.name} ha caído un ${Math.abs(topChange.price_change_percentage_24h).toFixed(2)}%. Observando soporte en $${(topChange.current_price * 0.9).toFixed(2)}.`;
      }
    } else if (Math.abs(totalMarketChange) > 3) {
      // Movimiento general del mercado
      if (totalMarketChange > 0) {
        mood = 'happy';
        message = `Mercado alcista con ${totalMarketChange.toFixed(2)}% de subida promedio. La rotación de capital va ${capitalRotation}.`;
      } else {
        mood = 'sad';
        message = `Mercado bajista con ${Math.abs(totalMarketChange).toFixed(2)}% de caída promedio. Recomiendo cautela.`;
      }
    } else if (volatilityIndex > 4) {
      // Alta volatilidad sin dirección clara
      mood = 'thinking';
      message = `Alta volatilidad (${volatilityIndex.toFixed(2)}%) sin dirección clara. Posible acumulación antes de movimiento importante.`;
    } else {
      // Mercado estable
      mood = 'neutral';
      message = `Mercado estable con volatilidad de ${volatilityIndex.toFixed(2)}%. Buen momento para analizar fundamentales.`;
    }

    // Si no hay una criptomoneda destacada pero hay una tendencia clara en el top 5
    if (!highlightedCrypto && Math.abs(topCryptosAvgChange) > 2) {
      const bestPerformer = [...topCryptos].sort((a: any, b: any) =>
        b.price_change_percentage_24h - a.price_change_percentage_24h
      )[0];

      highlightedCrypto = bestPerformer;
    }

    return {
      mood,
      message,
      crypto: highlightedCrypto,
      marketSentiment,
      volatilityIndex
    };
  };

  // Renderizar el contenido principal del dashboard
  const renderDashboardContent = () => {
    return (
      <div className="dashboard-content">
        <div className="dashboard-header">
          <h2 className="dashboard-title">Dashboard</h2>
          <div className="header-actions">
            <div className="search-bar">
              <input
                type="text"
                placeholder="Buscar criptomoneda..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <button className="search-button" onClick={handleSearch}>
                <i className="fas fa-search"></i>
              </button>
            </div>
            <button className="refresh-button" onClick={fetchInitialData} title="Actualizar datos">
              <i className="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>

        <div className="dashboard-container">
          {/* Fila superior: Resumen de Cartera y Resumen del Mercado */}
          <div className="dashboard-row top-row">
            {/* Panel de Resumen de Cartera */}
            <div className="dashboard-column portfolio-column">
              <PortfolioSummaryWidget
                onViewFullPortfolio={() => setActiveSection('portfolio')}
              />
            </div>

            {/* Panel de Resumen del Mercado */}
            <div className="dashboard-column market-column">
              <div className="market-summary-widget">
                <div className="widget-header">
                  <h3>Resumen del Mercado</h3>
                  <span className="last-updated">Actualizado: {new Date().toLocaleTimeString()}</span>
                </div>
                {marketData ? (
                  <div className="widget-content">
                    <div className="market-stats-grid">
                      <div className="market-stat-item">
                        <div className="stat-icon">
                          <i className="fas fa-chart-pie"></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Capitalización Total</span>
                          <span className="stat-value">
                            ${formatNumber(marketData.total_market_cap?.usd || 0)}
                          </span>
                        </div>
                      </div>
                      <div className="market-stat-item">
                        <div className="stat-icon">
                          <i className="fas fa-chart-line"></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Volumen 24h</span>
                          <span className="stat-value">
                            ${formatNumber(marketData.total_volume?.usd || 0)}
                          </span>
                        </div>
                      </div>
                      <div className="market-stat-item">
                        <div className="stat-icon">
                          <i className="fab fa-bitcoin"></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Dominancia BTC</span>
                          <span className="stat-value">
                            {(marketData.market_cap_percentage?.btc || 0).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      <div className="market-stat-item">
                        <div className="stat-icon" style={{
                          color: marketData.market_cap_change_percentage_24h_usd >= 0 ? 'var(--color-positive)' : 'var(--color-negative)'
                        }}>
                          <i className={`fas fa-${marketData.market_cap_change_percentage_24h_usd >= 0 ? 'arrow-up' : 'arrow-down'}`}></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Cambio 24h</span>
                          <span
                            className={`stat-value ${marketData.market_cap_change_percentage_24h_usd >= 0 ? 'positive' : 'negative'}`}
                          >
                            {marketData.market_cap_change_percentage_24h_usd >= 0 ? '+' : ''}
                            {marketData.market_cap_change_percentage_24h_usd.toFixed(2)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="widget-content">
                    <div className="loading-indicator">
                      <div className="loading-spinner"></div>
                      <span>Cargando datos del mercado...</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Fila media: Tabla de criptomonedas y Gráfico */}
          <div className="dashboard-row middle-row">
            {/* Tabla de criptomonedas */}
            <div className="dashboard-column table-column">
              <div className="crypto-table-widget">
                <div className="widget-header">
                  <h3>Top Criptomonedas</h3>
                </div>
                <div className="widget-content">
                  <EnhancedCryptoTable
                    cryptos={cryptos}
                    onSelectCrypto={(id) => {
                      const crypto = cryptos.find((c) => c.id === id);
                      if (crypto) handleCryptoSelect(crypto);
                    }}
                    isLoading={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Gráfico de análisis técnico */}
            <div className="dashboard-column chart-column">
              <div className="chart-widget">
                {selectedCrypto ? (
                  <>
                    <div className="widget-header crypto-header">
                      <div className="crypto-title">
                        {selectedCrypto.image && <img src={selectedCrypto.image} alt={selectedCrypto.name} className="crypto-icon" />}
                        <h3>{selectedCrypto.name} <span className="crypto-symbol">({selectedCrypto.symbol ? selectedCrypto.symbol.toUpperCase() : ''})</span></h3>
                      </div>
                      <div className="crypto-price-container">
                        <span className="price-value">${selectedCrypto.current_price ? selectedCrypto.current_price.toLocaleString() : 'N/A'}</span>
                        {selectedCrypto.price_change_percentage_24h !== undefined && (
                          <span
                            className={`price-change ${selectedCrypto.price_change_percentage_24h >= 0 ? 'text-positive' : 'text-negative'}`}
                          >
                            <i className={`fas fa-${selectedCrypto.price_change_percentage_24h >= 0 ? 'arrow-up' : 'arrow-down'}`}></i>
                            {Math.abs(selectedCrypto.price_change_percentage_24h).toFixed(2)}%
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="chart-controls">
                      <div className="time-range-selector">
                        {['1d', '7d', '30d', '90d', '1y', 'max'].map((range) => (
                          <button
                            key={range}
                            className={timeRange === range || (typeof timeRange === 'number' && range === '1d' && timeRange === 1) || (typeof timeRange === 'number' && range === '7d' && timeRange === 7) ? 'active' : ''}
                            onClick={() => handleTimeRangeChange(range === '1d' ? 1 : range === '7d' ? 7 : range === '30d' ? 30 : range === '90d' ? 90 : range === '1y' ? 365 : 'max')}
                          >
                            {range}
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="chart-container">
                      {historicalData ? (
                        <TechnicalAnalysisChart
                          data={chartData}
                          cryptoId={selectedCrypto.id}
                          cryptoName={selectedCrypto.name}
                          cryptoSymbol={selectedCrypto.symbol}
                          color={selectedCrypto.price_change_percentage_24h >= 0 ? 'var(--color-positive)' : 'var(--color-negative)'}
                        />
                      ) : (
                        <div className="loading-indicator">
                          <div className="loading-spinner"></div>
                          <span>Cargando datos históricos...</span>
                        </div>
                      )}
                    </div>

                    <div className="crypto-stats-grid">
                      <div className="crypto-stat-item">
                        <div className="stat-icon">
                          <i className="fas fa-dollar-sign"></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Capitalización</span>
                          <span className="stat-value">${selectedCrypto.market_cap ? formatNumber(selectedCrypto.market_cap) : 'N/A'}</span>
                        </div>
                      </div>
                      <div className="crypto-stat-item">
                        <div className="stat-icon">
                          <i className="fas fa-exchange-alt"></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Volumen 24h</span>
                          <span className="stat-value">${selectedCrypto.total_volume ? formatNumber(selectedCrypto.total_volume) : 'N/A'}</span>
                        </div>
                      </div>
                      <div className="crypto-stat-item">
                        <div className="stat-icon">
                          <i className="fas fa-arrow-up"></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Máximo 24h</span>
                          <span className="stat-value">${selectedCrypto.high_24h ? selectedCrypto.high_24h.toLocaleString() : 'N/A'}</span>
                        </div>
                      </div>
                      <div className="crypto-stat-item">
                        <div className="stat-icon">
                          <i className="fas fa-arrow-down"></i>
                        </div>
                        <div className="stat-info">
                          <span className="stat-label">Mínimo 24h</span>
                          <span className="stat-value">${selectedCrypto.low_24h ? selectedCrypto.low_24h.toLocaleString() : 'N/A'}</span>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="widget-header">
                      <h3>Análisis Técnico</h3>
                    </div>
                    <div className="no-crypto-selected">
                      <i className="fas fa-chart-line empty-state-icon"></i>
                      <h3>Selecciona una criptomoneda</h3>
                      <p>Haz clic en cualquier criptomoneda de la lista para ver su gráfico y detalles</p>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Eliminamos el widget del Guru para evitar duplicidad con el sidebar */}
        </div>
      </div>
    );
  };

  // Renderizar el contenido de la sección de mercado
  const renderMarketContent = () => {
    return (
      <div className="market-content">
        <div className="market-header">
          <h2>Mercado de Criptomonedas</h2>
          <button className="back-button" onClick={() => setActiveSection('dashboard')}>
            Volver al Dashboard
          </button>
        </div>

        <div className="market-table">
          <table>
            <thead>
              <tr>
                <th>#</th>
                <th>Nombre</th>
                <th>Precio</th>
                <th>24h %</th>
                <th>Cap. de Mercado</th>
                <th>Volumen (24h)</th>
                <th>Suministro Circulante</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              {cryptos.map((crypto: any, index: number) => (
                <tr
                  key={crypto.id}
                  onClick={() => {
                    handleCryptoSelect(crypto);
                    setActiveSection('dashboard');
                  }}
                >
                  <td>{index + 1}</td>
                  <td>
                    <div className="crypto-name-cell">
                      {crypto.image && (
                        <img src={crypto.image} alt={crypto.name} className="crypto-icon-small" />
                      )}
                      <div>
                        <span className="crypto-name">{crypto.name}</span>
                        <span className="crypto-symbol">{crypto.symbol.toUpperCase()}</span>
                      </div>
                    </div>
                  </td>
                  <td>${crypto.current_price.toLocaleString()}</td>
                  <td
                    style={{ color: getPriceChangeColor(crypto.price_change_percentage_24h) }}
                  >
                    {crypto.price_change_percentage_24h >= 0 ? '▲' : '▼'}
                    {Math.abs(crypto.price_change_percentage_24h).toFixed(2)}%
                  </td>
                  <td>${formatNumber(crypto.market_cap)}</td>
                  <td>${formatNumber(crypto.total_volume)}</td>
                  <td>{formatNumber(crypto.circulating_supply)} {crypto.symbol.toUpperCase()}</td>
                  <td>
                    <button
                      className={`watchlist-button ${isInWatchlist(crypto.id) ? 'in-watchlist' : ''}`}
                      onClick={(e) => {
                        e.stopPropagation(); // Evitar que se active el onClick del tr
                        if (isInRadar(crypto.id)) {
                          removeFromRadar(crypto.id);
                        } else {
                          addToRadar(crypto);
                        }
                      }}
                    >
                      {isInRadar(crypto.id) ? (
                        <>
                          <i className="fas fa-star"></i> En Mi Radar
                        </>
                      ) : (
                        <>
                          <i className="far fa-star"></i> Añadir
                        </>
                      )}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // Renderizar el contenido de la sección de portafolio
  const renderPortfolioContent = () => {
    return (
      <div className="portfolio-content">
        <div className="portfolio-header">
          <h2>Mi Portafolio</h2>
          <button className="back-button" onClick={() => setActiveSection('dashboard')}>
            Volver al Dashboard
          </button>
        </div>

        <PortfolioFirebase />
      </div>
    );
  };

  // Renderizar el contenido de la sección de alertas
  const renderAlertsContent = () => {
    return (
      <div className="alerts-content">
        <div className="alerts-header">
          <h2>Alertas de Precios</h2>
          <button className="back-button" onClick={() => setActiveSection('dashboard')}>
            Volver al Dashboard
          </button>
        </div>

        <PriceAlerts cryptos={cryptos} />
      </div>
    );
  };

  // Renderizar el contenido de la sección del asistente
  const renderAssistantContent = () => {
    return (
      <div className="assistant-content">
        <div className="assistant-header">
          <h2>Asistente IA</h2>
          <button className="back-button" onClick={() => setActiveSection('dashboard')}>
            Volver al Dashboard
          </button>
        </div>

        <div className="assistant-container">
          <div className="assistant-avatar">
            <CriptoAgentAvatar mood="neutral" size="large" />
          </div>

          <div className="chat-container">
            {/* Aquí iría el componente de chat */}
            <div className="chat-placeholder">
              <p>El componente de chat se integrará aquí.</p>
              <p>Pregunta sobre criptomonedas, blockchain o cualquier duda que tengas.</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Renderizar el contenido de la sección de API
  const renderApiContent = () => {
    return (
      <div className="api-content">
        <div className="api-header">
          <h2>Conexión API</h2>
          <button className="back-button" onClick={() => setActiveSection('dashboard')}>
            Volver al Dashboard
          </button>
        </div>

        <div className="api-container">
          <div className="api-section">
            <h3>Estado de la API</h3>
            <div className={`api-status ${mcpServerStatus}`}>
              <div className="status-indicator">
                <span className="status-dot"></span>
                <span className="status-text">
                  {mcpServerStatus === 'online' ? 'API Conectada' : 'API Desconectada'}
                </span>
              </div>
            </div>

            <div className="api-actions">
              <button
                className="connect-button"
                onClick={() => {
                  setMcpServerStatus('online');
                  setAgentMood('happy');
                  setAgentMessage('Conexión con API establecida correctamente. Datos de criptomonedas actualizados.');
                  setShowAgentMessage(true);
                  setTimeout(() => {
                    setShowAgentMessage(false);
                    setAgentMood('neutral');
                    fetchInitialData();
                  }, 3000);
                }}
              >
                Conectar a API
              </button>
              <button
                className="test-button"
                onClick={() => {
                  setAgentMood('thinking');
                  setAgentMessage('Probando conexión con API...');
                  setShowAgentMessage(true);
                  setTimeout(() => {
                    setAgentMood('happy');
                    setAgentMessage('Conexión exitosa. Datos recibidos correctamente.');
                    setTimeout(() => {
                      setShowAgentMessage(false);
                      setAgentMood('neutral');
                    }, 2000);
                  }, 1500);
                }}
              >
                Probar Conexión
              </button>
            </div>
          </div>

          <div className="api-info">
            <h3>Información de la API</h3>
            <div className="api-details">
              <div className="api-detail-item">
                <span className="detail-label">Proveedor:</span>
                <span className="detail-value">CoinCap API</span>
              </div>
              <div className="api-detail-item">
                <span className="detail-label">Endpoint:</span>
                <span className="detail-value">https://api.coincap.io/v2/</span>
              </div>
              <div className="api-detail-item">
                <span className="detail-label">Frecuencia de actualización:</span>
                <span className="detail-value">60 segundos</span>
              </div>
              <div className="api-detail-item">
                <span className="detail-label">Límite de peticiones:</span>
                <span className="detail-value">200 por minuto</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Renderizar el contenido de la sección de radar
  const renderRadarContent = () => {
    return (
      <div className="radar-content">
        <div className="radar-header">
          <h2>Mi Radar Cripto</h2>
          <button className="back-button" onClick={() => setActiveSection('dashboard')}>
            Volver al Dashboard
          </button>
        </div>

        <MiRadarCripto />
      </div>
    );
  };

  // Renderizar el contenido según la sección activa
  const renderContent = () => {
    // Si estamos en la sección dashboard y no hay datos, mostrar un indicador de carga
    if (activeSection === 'dashboard' && isLoading && (!cryptos || cryptos.length === 0)) {
      return (
        <div className="global-loading-container">
          <div className="loading-spinner-large"></div>
          <h3>Cargando datos de criptomonedas...</h3>
          <p>Por favor espera mientras conectamos con las APIs</p>
        </div>
      );
    }

    switch (activeSection) {
      case 'dashboard':
        return renderDashboardContent();
      case 'market':
        return renderMarketContent();
      case 'portfolio':
        return renderPortfolioContent();
      case 'radar':
        return renderRadarContent();
      case 'alerts':
        return renderAlertsContent();
      case 'assistant':
        return renderAssistantContent();
      case 'api':
        return renderApiContent();
      default:
        return renderDashboardContent();
    }
  };

  // Usar el análisis del mercado almacenado en el estado

  return (
    <div className="integrated-dashboard" ref={dashboardRef}>
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <ModernLogo size="medium" />
        </div>

        <nav className="sidebar-nav">
          <ul>
            <li
              className={activeSection === 'dashboard' ? 'active' : ''}
              onClick={() => setActiveSection('dashboard')}
            >
              <span className="nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 13H11V3H3V13ZM3 21H11V15H3V21ZM13 21H21V11H13V21ZM13 3V9H21V3H13Z" fill="currentColor"/>
                </svg>
              </span>
              <span className="nav-text">Dashboard</span>
            </li>
            <li
              className={activeSection === 'market' ? 'active' : ''}
              onClick={() => setActiveSection('market')}
            >
              <span className="nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3.5 18.49L9.5 12.48L13.5 16.48L22 6.92001L20.59 5.51001L13.5 13.48L9.5 9.48001L2 16.99L3.5 18.49Z" fill="currentColor"/>
                </svg>
              </span>
              <span className="nav-text">Mercado</span>
            </li>
            <li
              className={activeSection === 'portfolio' ? 'active' : ''}
              onClick={() => setActiveSection('portfolio')}
            >
              <span className="nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 6H16V4C16 2.89 15.11 2 14 2H10C8.89 2 8 2.89 8 4V6H4C2.89 6 2.01 6.89 2.01 8L2 19C2 20.11 2.89 21 4 21H20C21.11 21 22 20.11 22 19V8C22 6.89 21.11 6 20 6ZM10 4H14V6H10V4ZM20 19H4V8H20V19Z" fill="currentColor"/>
                </svg>
              </span>
              <span className="nav-text">Portafolio</span>
            </li>
            <li
              className={activeSection === 'radar' ? 'active' : ''}
              onClick={() => {
                // Opción 1: Navegar dentro del dashboard
                setActiveSection('radar');

                // Opción 2: Abrir en una nueva pestaña
                // window.open('/radar', '_blank');
              }}
            >
              <span className="nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15l-5-2.18L7 18V5h10v13z" fill="currentColor"/>
                </svg>
              </span>
              <span className="nav-text">Mi Radar Cripto</span>
              <a href="/radar" target="_blank" className="external-link" title="Abrir en nueva pestaña" onClick={(e) => e.stopPropagation()}>
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 19H5V5H12V3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V12H19V19ZM14 3V5H17.59L7.76 14.83L9.17 16.24L19 6.41V10H21V3H14Z" fill="currentColor"/>
                </svg>
              </a>
            </li>
            <li
              className={activeSection === 'alerts' ? 'active' : ''}
              onClick={() => setActiveSection('alerts')}
            >
              <span className="nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22ZM18 16V11C18 7.93 16.37 5.36 13.5 4.68V4C13.5 3.17 12.83 2.5 12 2.5C11.17 2.5 10.5 3.17 10.5 4V4.68C7.64 5.36 6 7.92 6 11V16L4 18V19H20V18L18 16ZM16 17H8V11C8 8.52 9.51 6.5 12 6.5C14.49 6.5 16 8.52 16 11V17Z" fill="currentColor"/>
                </svg>
              </span>
              <span className="nav-text">Alertas</span>
            </li>
            <li
              className={activeSection === 'assistant' ? 'active' : ''}
              onClick={() => setActiveSection('assistant')}
            >
              <span className="nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="currentColor"/>
                </svg>
              </span>
              <span className="nav-text">Asistente IA</span>
              <a href="/guru" target="_blank" className="external-link" title="Abrir Gurú Cripto" onClick={(e) => e.stopPropagation()}>
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 19H5V5H12V3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V12H19V19ZM14 3V5H17.59L7.76 14.83L9.17 16.24L19 6.41V10H21V3H14Z" fill="currentColor"/>
                </svg>
              </a>
            </li>
          </ul>
        </nav>



        <div className="sidebar-footer">
          <div className="server-status">
            <div className={`status-indicator ${mcpServerStatus}`}>
              <span className="status-dot"></span>
              <span className="status-text">
                {mcpServerStatus === 'online' ? 'API Conectada' : 'API Desconectada'}
              </span>
            </div>
          </div>
          <button
            className="api-button"
            onClick={() => setActiveSection('api')}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '8px 15px',
              margin: '15px auto',
              width: '80%',
              backgroundColor: 'rgba(0, 224, 255, 0.3)',
              border: '1px solid rgba(0, 224, 255, 0.7)',
              borderRadius: '8px',
              color: '#ffffff',
              fontWeight: '500',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 0 10px rgba(0, 224, 255, 0.3)',
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '8px' }}>
              <path d="M20 4H4C2.89 4 2.01 4.89 2.01 6L2 18C2 19.11 2.89 20 4 20H20C21.11 20 22 19.11 22 18V6C22 4.89 21.11 4 20 4ZM20 18H4V12H20V18ZM20 8H4V6H20V8Z" fill="currentColor"/>
            </svg>
            Conectar API
          </button>
        </div>
      </div>

      <div className="dashboard-main">
        {renderContent()}
      </div>
    </div>
  );
};

export default IntegratedDashboard;
