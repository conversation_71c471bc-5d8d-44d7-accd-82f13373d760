import { db } from '../firebase-init';
import { collection, doc, setDoc, getDoc, getDocs, deleteDoc, updateDoc, query, where } from 'firebase/firestore';
import { User } from 'firebase/auth';

export type CryptoCategory = 'defi' | 'nft' | 'layer1' | 'layer2' | 'stablecoin' | 'meme' | 'exchange' | 'other';

export interface RadarItem {
  id: string;
  name: string;
  symbol: string;
  imageUrl: string;
  addedAt: Date;
  notes?: string;
  category?: CryptoCategory;
  isFavorite?: boolean;
  customAlertPrice?: number | null;
}

// Usar la colección Watchlist (estructura antigua) en lugar de users/{userId}/radar
const getWatchlistDoc = (userId: string) => {
  return doc(db, 'Watchlist', userId);
};

// Añadir una criptomoneda al radar
export const addToRadar = async (
  user: User,
  crypto: {
    id: string;
    name: string;
    symbol: string;
    imageUrl: string;
  }
): Promise<void> => {
  if (!user) throw new Error('Usuario no autenticado');

  const watchlistDoc = getWatchlistDoc(user.uid);
  
  // Obtener el documento actual o crear uno nuevo
  const docSnap = await getDoc(watchlistDoc);
  
  let watchlist: Record<string, any> = {};
  if (docSnap.exists()) {
    watchlist = docSnap.data();
  }
  
  // Verificar si ya existe en el radar
  if (watchlist[crypto.id]) {
    throw new Error('Esta criptomoneda ya está en tu radar');
  }
  
  // Añadir la nueva criptomoneda al watchlist
  watchlist[crypto.id] = {
    ...crypto,
    addedAt: new Date(),
    notes: '',
    category: 'other',
    isFavorite: false,
    customAlertPrice: null
  };
  
  // Guardar el documento actualizado
  await setDoc(watchlistDoc, watchlist);
};

// Obtener todas las criptomonedas del radar
export const getRadarItems = async (userId: string): Promise<RadarItem[]> => {
  if (!userId) return [];

  const watchlistDoc = getWatchlistDoc(userId);
  const docSnap = await getDoc(watchlistDoc);
  
  if (!docSnap.exists()) {
    return [];
  }
  
  const watchlist = docSnap.data();
  
  // Convertir el objeto a un array de RadarItem
  return Object.entries(watchlist).map(([id, data]: [string, any]) => {
    return {
      id,
      name: data.name,
      symbol: data.symbol,
      imageUrl: data.imageUrl,
      addedAt: data.addedAt?.toDate() || new Date(),
      notes: data.notes || '',
      category: data.category || 'other',
      isFavorite: data.isFavorite || false,
      customAlertPrice: data.customAlertPrice || null
    } as RadarItem;
  });
};

// Eliminar una criptomoneda del radar
export const removeFromRadar = async (userId: string, cryptoId: string): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const watchlistDoc = getWatchlistDoc(userId);
  const docSnap = await getDoc(watchlistDoc);
  
  if (!docSnap.exists()) {
    return;
  }
  
  const watchlist = docSnap.data();
  
  // Eliminar la criptomoneda del objeto
  if (watchlist[cryptoId]) {
    delete watchlist[cryptoId];
    await setDoc(watchlistDoc, watchlist);
  }
};

// Actualizar las notas de una criptomoneda
export const updateNotes = async (userId: string, cryptoId: string, notes: string): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const watchlistDoc = getWatchlistDoc(userId);
  const docSnap = await getDoc(watchlistDoc);
  
  if (!docSnap.exists()) {
    return;
  }
  
  const watchlist = docSnap.data();
  
  // Actualizar las notas de la criptomoneda
  if (watchlist[cryptoId]) {
    watchlist[cryptoId].notes = notes;
    await setDoc(watchlistDoc, watchlist);
  }
};

// Actualizar la categoría de una criptomoneda
export const updateCategory = async (userId: string, cryptoId: string, category: CryptoCategory): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const watchlistDoc = getWatchlistDoc(userId);
  const docSnap = await getDoc(watchlistDoc);
  
  if (!docSnap.exists()) {
    return;
  }
  
  const watchlist = docSnap.data();
  
  // Actualizar la categoría de la criptomoneda
  if (watchlist[cryptoId]) {
    watchlist[cryptoId].category = category;
    await setDoc(watchlistDoc, watchlist);
  }
};

// Marcar/desmarcar como favorito
export const toggleFavorite = async (userId: string, cryptoId: string, isFavorite: boolean): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const watchlistDoc = getWatchlistDoc(userId);
  const docSnap = await getDoc(watchlistDoc);
  
  if (!docSnap.exists()) {
    return;
  }
  
  const watchlist = docSnap.data();
  
  // Actualizar el estado de favorito de la criptomoneda
  if (watchlist[cryptoId]) {
    watchlist[cryptoId].isFavorite = isFavorite;
    await setDoc(watchlistDoc, watchlist);
  }
};

// Establecer precio de alerta personalizado
export const setAlertPrice = async (userId: string, cryptoId: string, price: number | null): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const watchlistDoc = getWatchlistDoc(userId);
  const docSnap = await getDoc(watchlistDoc);
  
  if (!docSnap.exists()) {
    return;
  }
  
  const watchlist = docSnap.data();
  
  // Actualizar el precio de alerta de la criptomoneda
  if (watchlist[cryptoId]) {
    watchlist[cryptoId].customAlertPrice = price;
    await setDoc(watchlistDoc, watchlist);
  }
};

// Verificar si una criptomoneda está en el radar
export const isInRadar = async (userId: string, cryptoId: string): Promise<boolean> => {
  if (!userId) return false;

  const watchlistDoc = getWatchlistDoc(userId);
  const docSnap = await getDoc(watchlistDoc);
  
  if (!docSnap.exists()) {
    return false;
  }
  
  const watchlist = docSnap.data();
  
  return !!watchlist[cryptoId];
};

// Obtener criptomonedas por categoría
export const getByCategory = async (userId: string, category: CryptoCategory): Promise<RadarItem[]> => {
  if (!userId) return [];

  const items = await getRadarItems(userId);
  return items.filter(item => item.category === category);
};

// Obtener criptomonedas favoritas
export const getFavorites = async (userId: string): Promise<RadarItem[]> => {
  if (!userId) return [];

  const items = await getRadarItems(userId);
  return items.filter(item => item.isFavorite);
};

// Migrar datos del formato antiguo al nuevo
export const migrateWatchlistToRadar = async (userId: string): Promise<boolean> => {
  if (!userId) return false;
  
  try {
    // Obtener datos del formato antiguo
    const watchlistDoc = getWatchlistDoc(userId);
    const docSnap = await getDoc(watchlistDoc);
    
    if (!docSnap.exists()) {
      return false; // No hay datos para migrar
    }
    
    const watchlist = docSnap.data();
    
    // Crear la colección de radar en el nuevo formato
    const radarCollection = collection(db, 'users', userId, 'radar');
    
    // Migrar cada criptomoneda
    for (const [id, data] of Object.entries(watchlist)) {
      const cryptoDoc = doc(radarCollection, id);
      await setDoc(cryptoDoc, data);
    }
    
    return true;
  } catch (error) {
    console.error('Error al migrar datos:', error);
    return false;
  }
};
