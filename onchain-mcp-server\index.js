/**
 * <PERSON><PERSON><PERSON><PERSON> MCP Server
 * 
 * Servidor MCP para análisis on-chain de criptomonedas.
 */
const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const config = require('./config');

// Importar herramientas MCP
const walletTools = require('./tools/wallet');
const transactionTools = require('./tools/transaction');
const contractTools = require('./tools/contract');
const tokenTools = require('./tools/token');
const networkTools = require('./tools/network');

// Crear aplicación Express
const app = express();
app.use(cors());
app.use(express.json());

// Almacenamiento de sesiones
const sessions = new Map();

// Combinar todas las herramientas
const tools = {
  ...walletTools,
  ...transactionTools,
  ...contractTools,
  ...tokenTools,
  ...networkTools
};

// Endpoint MCP
app.post('/mcp', async (req, res) => {
  try {
    const { jsonrpc, method, params, id } = req.body;
    const sessionId = req.headers['mcp-session-id'];
    
    // Verificar versión de JSON-RPC
    if (jsonrpc !== '2.0') {
      return res.status(400).json({
        jsonrpc: '2.0',
        error: {
          code: -32600,
          message: 'Invalid JSON-RPC version'
        },
        id
      });
    }
    
    // Manejar métodos de sesión
    if (method === 'session.create') {
      // Crear nueva sesión
      const newSessionId = `session-${uuidv4()}`;
      sessions.set(newSessionId, {
        id: newSessionId,
        createdAt: Date.now(),
        lastUsed: Date.now()
      });
      
      console.log(`Created new session: ${newSessionId}`);
      
      return res.json({
        jsonrpc: '2.0',
        result: { sessionId: newSessionId },
        id
      });
    }
    
    if (method === 'session.close') {
      // Cerrar sesión
      if (sessionId && sessions.has(sessionId)) {
        sessions.delete(sessionId);
        console.log(`Closed session: ${sessionId}`);
      }
      
      return res.json({
        jsonrpc: '2.0',
        result: { success: true },
        id
      });
    }
    
    // Para otros métodos, verificar que la sesión existe
    if (!sessionId || !sessions.has(sessionId)) {
      return res.status(401).json({
        jsonrpc: '2.0',
        error: {
          code: -32000,
          message: 'Invalid or expired session'
        },
        id
      });
    }
    
    // Actualizar timestamp de último uso
    const session = sessions.get(sessionId);
    session.lastUsed = Date.now();
    
    // Manejar método execute
    if (method === 'execute') {
      const { tool, input } = params;
      
      // Verificar que se proporcionó una herramienta
      if (!tool) {
        return res.status(400).json({
          jsonrpc: '2.0',
          error: {
            code: -32602,
            message: 'Missing tool parameter'
          },
          id
        });
      }
      
      // Verificar que la herramienta existe
      if (!tools[tool]) {
        return res.status(404).json({
          jsonrpc: '2.0',
          error: {
            code: -32601,
            message: `Tool ${tool} not found`
          },
          id
        });
      }
      
      try {
        console.log(`Executing tool ${tool} with session ${sessionId}`);
        
        // Ejecutar la herramienta
        const result = await tools[tool].handler(input || {});
        
        return res.json({
          jsonrpc: '2.0',
          result,
          id
        });
      } catch (error) {
        console.error(`Error executing tool ${tool}:`, error);
        
        return res.status(500).json({
          jsonrpc: '2.0',
          error: {
            code: -32603,
            message: error.message
          },
          id
        });
      }
    }
    
    // Método no soportado
    return res.status(400).json({
      jsonrpc: '2.0',
      error: {
        code: -32601,
        message: `Method ${method} not supported`
      },
      id
    });
  } catch (error) {
    console.error('Error processing request:', error);
    
    return res.status(500).json({
      jsonrpc: '2.0',
      error: {
        code: -32700,
        message: 'Parse error'
      },
      id: null
    });
  }
});

// Endpoint de información
app.get('/', (req, res) => {
  res.json({
    name: 'OnChain MCP Server',
    version: '1.0.0',
    description: 'MCP server for on-chain analysis of cryptocurrencies',
    endpoints: {
      mcp: '/mcp'
    },
    tools: Object.keys(tools)
  });
});

// Limpiar sesiones expiradas cada hora
setInterval(() => {
  const now = Date.now();
  const expireTime = 3600000; // 1 hora
  
  for (const [sessionId, session] of sessions.entries()) {
    if (now - session.lastUsed > expireTime) {
      sessions.delete(sessionId);
      console.log(`Expired session: ${sessionId}`);
    }
  }
}, 3600000);

// Iniciar servidor
const PORT = config.port;
app.listen(PORT, () => {
  console.log(`OnChain MCP Server running on port ${PORT}`);
  console.log(`Available tools: ${Object.keys(tools).join(', ')}`);
});
