import React, { useState, useEffect } from 'react';
import { useTopCryptocurrencies, useCryptoHistoricalData } from '../hooks/useMcpClient';
import EnhancedCryptoChart from './EnhancedCryptoChart';
import '../styles/RealTimeCryptoData.css';

interface RealTimeCryptoDataProps {
  limit?: number;
}

const RealTimeCryptoData: React.FC<RealTimeCryptoDataProps> = ({ limit = 5 }) => {
  const [selectedCryptoId, setSelectedCryptoId] = useState<string>('bitcoin');
  const [timeRange, setTimeRange] = useState<number>(7);
  const { data: topCryptos, loading: loadingCryptos, error: cryptosError } = useTopCryptocurrencies(limit);
  const { data: historicalData, loading: loadingHistory, error: historyError } = useCryptoHistoricalData(selectedCryptoId, timeRange);
  const [chartData, setChartData] = useState<{ labels: string[], values: number[] }>({ labels: [], values: [] });

  // Procesar datos históricos para el gráfico
  useEffect(() => {
    if (historicalData && historicalData.prices) {
      const prices = historicalData.prices;
      const formattedData = {
        labels: prices.map((price: [number, number]) => {
          const date = new Date(price[0]);
          return date.toLocaleDateString();
        }),
        values: prices.map((price: [number, number]) => price[1])
      };
      setChartData(formattedData);
    }
  }, [historicalData]);

  // Manejar cambio de criptomoneda seleccionada
  const handleCryptoSelect = (id: string) => {
    setSelectedCryptoId(id);
  };

  // Manejar cambio de rango de tiempo
  const handleTimeRangeChange = (days: number) => {
    setTimeRange(days);
  };

  // Formatear números para mostrar
  const formatNumber = (num: number, decimals: number = 2) => {
    return new Intl.NumberFormat('es-ES', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num);
  };

  // Formatear porcentajes para mostrar
  const formatPercentage = (num: number) => {
    return `${num > 0 ? '+' : ''}${formatNumber(num)}%`;
  };

  // Determinar el color basado en el cambio de precio
  const getPriceChangeColor = (change: number) => {
    return change >= 0 ? '#4caf50' : '#f44336';
  };

  if (loadingCryptos && !topCryptos) {
    return <div className="loading-container">Cargando datos de criptomonedas...</div>;
  }

  if (cryptosError) {
    return <div className="error-container">Error: {cryptosError}</div>;
  }

  return (
    <div className="real-time-crypto-container">
      <div className="crypto-header">
        <h2>Datos de Criptomonedas en Tiempo Real</h2>
        <div className="time-range-selector">
          <button 
            className={timeRange === 1 ? 'active' : ''} 
            onClick={() => handleTimeRangeChange(1)}
          >
            1D
          </button>
          <button 
            className={timeRange === 7 ? 'active' : ''} 
            onClick={() => handleTimeRangeChange(7)}
          >
            7D
          </button>
          <button 
            className={timeRange === 30 ? 'active' : ''} 
            onClick={() => handleTimeRangeChange(30)}
          >
            1M
          </button>
          <button 
            className={timeRange === 90 ? 'active' : ''} 
            onClick={() => handleTimeRangeChange(90)}
          >
            3M
          </button>
          <button 
            className={timeRange === 365 ? 'active' : ''} 
            onClick={() => handleTimeRangeChange(365)}
          >
            1A
          </button>
        </div>
      </div>

      <div className="crypto-content">
        <div className="crypto-list">
          <h3>Top Criptomonedas</h3>
          <ul>
            {topCryptos && topCryptos.map((crypto: any) => (
              <li 
                key={crypto.id} 
                className={selectedCryptoId === crypto.id ? 'selected' : ''}
                onClick={() => handleCryptoSelect(crypto.id)}
              >
                <div className="crypto-item">
                  <img src={crypto.image} alt={crypto.name} className="crypto-icon" />
                  <div className="crypto-info">
                    <span className="crypto-name">{crypto.name}</span>
                    <span className="crypto-symbol">{crypto.symbol.toUpperCase()}</span>
                  </div>
                  <div className="crypto-price-info">
                    <span className="crypto-price">${formatNumber(crypto.current_price)}</span>
                    <span 
                      className="crypto-change" 
                      style={{ color: getPriceChangeColor(crypto.price_change_percentage_24h) }}
                    >
                      {formatPercentage(crypto.price_change_percentage_24h)}
                    </span>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>

        <div className="crypto-detail">
          {loadingHistory ? (
            <div className="loading-container">Cargando datos históricos...</div>
          ) : historyError ? (
            <div className="error-container">Error: {historyError}</div>
          ) : (
            <>
              <h3>
                {topCryptos && topCryptos.find((c: any) => c.id === selectedCryptoId)?.name} 
                ({topCryptos && topCryptos.find((c: any) => c.id === selectedCryptoId)?.symbol.toUpperCase()})
              </h3>
              
              <div className="chart-container">
                <EnhancedCryptoChart 
                  data={chartData}
                  color={getPriceChangeColor(
                    topCryptos && topCryptos.find((c: any) => c.id === selectedCryptoId)?.price_change_percentage_24h || 0
                  )}
                  height={400}
                  width={800}
                  animate={true}
                  showLabels={true}
                  showGrid={true}
                  showTooltip={true}
                />
              </div>
              
              {topCryptos && (
                <div className="crypto-stats">
                  <div className="stat-item">
                    <span className="stat-label">Precio Actual:</span>
                    <span className="stat-value">
                      ${formatNumber(topCryptos.find((c: any) => c.id === selectedCryptoId)?.current_price || 0)}
                    </span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Cambio 24h:</span>
                    <span 
                      className="stat-value" 
                      style={{ 
                        color: getPriceChangeColor(
                          topCryptos.find((c: any) => c.id === selectedCryptoId)?.price_change_percentage_24h || 0
                        ) 
                      }}
                    >
                      {formatPercentage(topCryptos.find((c: any) => c.id === selectedCryptoId)?.price_change_percentage_24h || 0)}
                    </span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Cap. de Mercado:</span>
                    <span className="stat-value">
                      ${formatNumber(topCryptos.find((c: any) => c.id === selectedCryptoId)?.market_cap || 0, 0)}
                    </span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Volumen 24h:</span>
                    <span className="stat-value">
                      ${formatNumber(topCryptos.find((c: any) => c.id === selectedCryptoId)?.total_volume || 0, 0)}
                    </span>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default RealTimeCryptoData;
