/**
 * Servicio para interactuar con datos on-chain de Ethereum
 *
 * Este servicio utiliza directamente el servicio guru-etherscan.service.js
 * en lugar de comunicarse con el servidor MCP de Etherscan.
 */

const etherscanService = require('./etherscan.service');
const guruEtherscanService = require('./guru-etherscan.service');

/**
 * Obtiene el balance de ETH de una dirección
 * @param {string} address - Dirección Ethereum
 * @returns {Promise<Object>} - Información del balance
 */
async function getAddressBalance(address) {
  try {
    const balance = await etherscanService.getAddressBalance(address);
    const ethPrice = await etherscanService.getEthPrice();
    const balanceUSD = parseFloat(balance) * ethPrice.ethusd;

    return {
      address,
      balance,
      balanceUSD,
      ethPrice: ethPrice.ethusd
    };
  } catch (error) {
    console.error(`Error al obtener balance de ${address}:`, error);
    return null;
  }
}

/**
 * Obtiene los tokens ERC20 de una dirección
 * @param {string} address - Dirección Ethereum
 * @param {number} page - Número de página
 * @param {number} offset - Resultados por página
 * @returns {Promise<Object>} - Información de tokens
 */
async function getAddressTokens(address, page = 1, offset = 10) {
  try {
    const tokens = await etherscanService.getAddressERC20Transfers(
      address,
      null,
      parseInt(page),
      parseInt(offset)
    );

    return {
      address,
      tokens
    };
  } catch (error) {
    console.error(`Error al obtener tokens de ${address}:`, error);
    return null;
  }
}

/**
 * Obtiene información de un token ERC20
 * @param {string} address - Dirección del contrato del token
 * @returns {Promise<Object>} - Información del token
 */
async function getTokenInfo(address) {
  try {
    const info = await etherscanService.getTokenInfo(address);
    const supply = await etherscanService.getTokenSupply(address);

    return {
      address,
      info,
      supply
    };
  } catch (error) {
    console.error(`Error al obtener información del token ${address}:`, error);
    return null;
  }
}

/**
 * Obtiene el precio actual de ETH
 * @returns {Promise<Object>} - Información del precio
 */
async function getEthPrice() {
  try {
    const price = await etherscanService.getEthPrice();
    const stats = await etherscanService.getEthStats();

    return {
      price,
      stats
    };
  } catch (error) {
    console.error('Error al obtener precio de ETH:', error);
    return null;
  }
}

/**
 * Obtiene información sobre los precios de gas
 * @returns {Promise<Object>} - Información de gas
 */
async function getGasOracle() {
  try {
    return await etherscanService.getGasOracle();
  } catch (error) {
    console.error('Error al obtener información de gas:', error);
    return null;
  }
}

/**
 * Analiza una dirección Ethereum
 * @param {string} address - Dirección Ethereum
 * @returns {Promise<Object>} - Análisis de la dirección
 */
async function analyzeAddress(address) {
  try {
    return await guruEtherscanService.analyzeEthereumAddress(address);
  } catch (error) {
    console.error(`Error al analizar dirección ${address}:`, error);
    return null;
  }
}

/**
 * Analiza un contrato inteligente
 * @param {string} address - Dirección del contrato
 * @returns {Promise<Object>} - Análisis del contrato
 */
async function analyzeSmartContract(address) {
  try {
    return await guruEtherscanService.analyzeSmartContract(address);
  } catch (error) {
    console.error(`Error al analizar contrato ${address}:`, error);
    return null;
  }
}

/**
 * Obtiene información sobre los principales protocolos DeFi
 * @returns {Promise<Array>} - Lista de protocolos DeFi
 */
async function getDefiProtocols() {
  try {
    const defiState = await guruEtherscanService.analyzeEthereumDefiState();
    return defiState;
  } catch (error) {
    console.error('Error al obtener protocolos DeFi:', error);
    return null;
  }
}

/**
 * Genera una proyección de precio para ETH
 * @param {number} months - Número de meses para la proyección
 * @returns {Promise<Object>} - Proyección de precio
 */
async function generateEthPriceProjection(months = 6) {
  try {
    return await guruEtherscanService.generateEthPriceProjection(months);
  } catch (error) {
    console.error('Error al generar proyección de precio para ETH:', error);
    return null;
  }
}

/**
 * Analiza el impacto de The Merge en protocolos de staking
 * @returns {Promise<Object>} - Análisis detallado del impacto
 */
async function analyzeTheMergeImpact() {
  try {
    return await guruEtherscanService.analyzeTheMergeImpact();
  } catch (error) {
    console.error('Error al analizar el impacto de The Merge:', error);
    return null;
  }
}

module.exports = {
  getAddressBalance,
  getAddressTokens,
  getTokenInfo,
  getEthPrice,
  getGasOracle,
  analyzeAddress,
  analyzeSmartContract,
  getDefiProtocols,
  generateEthPriceProjection,
  analyzeTheMergeImpact
};
