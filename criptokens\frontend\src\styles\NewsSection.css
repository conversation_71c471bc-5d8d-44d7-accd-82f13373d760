.news-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.news-header {
  margin-bottom: 1.5rem;
}

.news-header h2 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.news-search {
  display: flex;
  margin-bottom: 1rem;
}

.news-search form {
  display: flex;
  flex: 1;
}

.news-search input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px 0 0 4px;
  font-size: 0.9375rem;
}

.news-search .search-button {
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.news-search .search-button:hover {
  background-color: #0077e6;
}

.news-search .refresh-button {
  background-color: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  margin-left: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.news-search .refresh-button:hover {
  background-color: #e0e0e0;
}

.news-topic {
  margin-bottom: 1rem;
}

.current-topic {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.9375rem;
  color: #666;
}

.current-topic span {
  font-weight: 600;
  color: #0084ff;
}

.reset-topic-button {
  background: none;
  border: 1px solid #0084ff;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8125rem;
  color: #0084ff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-topic-button:hover {
  background-color: #0084ff;
  color: white;
}

.news-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.category-button {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-button:hover {
  background-color: #f5f5f5;
}

.category-button.active {
  background-color: #0084ff;
  color: white;
  border-color: #0084ff;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.news-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-image {
  height: 180px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
  transform: scale(1.05);
}

.news-content {
  padding: 1.25rem;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 0.8125rem;
}

.news-source {
  color: #0084ff;
  font-weight: 600;
}

.news-date {
  color: #666;
}

.news-title {
  font-size: 1.125rem;
  margin: 0 0 0.75rem 0;
  color: #333;
  line-height: 1.4;
}

.news-description {
  font-size: 0.9375rem;
  color: #555;
  margin-bottom: 1rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-categories-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.category-tag {
  background-color: #f0f0f0;
  border-radius: 20px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  color: #666;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.category-tag:hover {
  background-color: #e0e0e0;
}

.read-more-link {
  display: inline-block;
  color: #0084ff;
  font-size: 0.9375rem;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
}

.read-more-link:hover {
  color: #0077e6;
  text-decoration: underline;
}

.no-news {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.reset-filter-button {
  margin-top: 1rem;
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.reset-filter-button:hover {
  background-color: #0077e6;
}

.news-loading,
.news-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.news-error {
  color: #d32f2f;
  background-color: #ffebee;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .news-grid {
    grid-template-columns: 1fr;
  }

  .news-categories {
    overflow-x: auto;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  .news-categories::-webkit-scrollbar {
    height: 4px;
  }

  .news-categories::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px;
  }
}
