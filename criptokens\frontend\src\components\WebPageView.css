.webpage-view-container {
  background-color: rgba(30, 41, 59, 0.8);
  border-radius: 8px;
  margin: 12px 0;
  overflow: hidden;
  border: 1px solid rgba(99, 102, 241, 0.3);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
}

/* Modo pantalla completa */
.webpage-view-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  border-radius: 0;
  z-index: 9999;
  background-color: rgba(15, 23, 42, 0.95);
  overflow-y: auto;
}

.webpage-view-container:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(99, 102, 241, 0.5);
}

.webpage-view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(30, 41, 59, 0.9);
  border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

.webpage-view-title {
  flex: 1;
}

.webpage-view-title h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #e2e8f0;
}

.webpage-view-url {
  font-size: 12px;
  color: #94a3b8;
  text-decoration: none;
  display: block;
  margin-top: 2px;
}

.webpage-view-url:hover {
  color: #38bdf8;
  text-decoration: underline;
}

.webpage-view-actions {
  display: flex;
  gap: 8px;
}

.webpage-view-toggle-btn,
.webpage-view-fullscreen-btn,
.webpage-view-open-btn,
.webpage-nav-btn,
.webpage-nav-go-btn,
.webpage-screenshot-controls button {
  background-color: rgba(99, 102, 241, 0.1);
  color: #94a3b8;
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.webpage-view-toggle-btn:hover,
.webpage-view-fullscreen-btn:hover,
.webpage-view-open-btn:hover,
.webpage-nav-btn:hover,
.webpage-nav-go-btn:hover,
.webpage-screenshot-controls button:hover {
  background-color: rgba(99, 102, 241, 0.2);
  color: #e2e8f0;
}

.webpage-view-toggle-btn:disabled,
.webpage-view-fullscreen-btn:disabled,
.webpage-nav-btn:disabled,
.webpage-nav-go-btn:disabled,
.webpage-screenshot-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.webpage-view-open-btn {
  background-color: rgba(99, 102, 241, 0.2);
  color: #e2e8f0;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Barra de navegación */
.webpage-view-navbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: rgba(15, 23, 42, 0.7);
  border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

.webpage-nav-url {
  flex: 1;
  background-color: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 4px;
  padding: 6px 12px;
  margin: 0 8px;
  color: #e2e8f0;
  font-size: 14px;
}

.webpage-nav-url:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.6);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* Pestañas */
.webpage-view-tabs {
  display: flex;
  background-color: rgba(15, 23, 42, 0.7);
  border-bottom: 1px solid rgba(99, 102, 241, 0.2);
  padding: 0 16px;
}

.webpage-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.webpage-tab:hover {
  color: #e2e8f0;
}

.webpage-tab.active {
  color: #e2e8f0;
  border-bottom-color: rgba(99, 102, 241, 0.8);
}

/* Captura de pantalla */
.webpage-view-screenshot {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a2e;
}

.webpage-screenshot-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  gap: 8px;
  background-color: rgba(15, 23, 42, 0.7);
  border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

.webpage-screenshot-controls span {
  color: #e2e8f0;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
}

.webpage-screenshot-container {
  max-height: 500px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.fullscreen .webpage-screenshot-container {
  max-height: calc(100vh - 200px);
}

.webpage-screenshot-img {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

.webpage-view-content {
  padding: 16px;
}

.webpage-view-summary,
.webpage-view-text {
  margin-bottom: 16px;
}

.webpage-view-summary h4,
.webpage-view-text h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #94a3b8;
}

.webpage-view-summary p {
  margin: 0;
  color: #e2e8f0;
  line-height: 1.5;
}

.webpage-extracted-text {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background-color: rgba(15, 23, 42, 0.5);
  border-radius: 4px;
  color: #e2e8f0;
  font-size: 14px;
  line-height: 1.5;
}

.webpage-view-metadata {
  font-size: 12px;
  color: #64748b;
  text-align: right;
  margin-top: 8px;
}

.webpage-view-timestamp {
  margin: 0;
}

/* Indicador de carga */
.webpage-view-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(15, 23, 42, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.webpage-view-loading p {
  color: #e2e8f0;
  margin-top: 16px;
  font-size: 14px;
}

.webpage-view-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  border-top-color: rgba(99, 102, 241, 0.8);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
