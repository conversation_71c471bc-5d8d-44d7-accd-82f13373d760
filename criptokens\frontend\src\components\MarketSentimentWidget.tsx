import React from 'react';
import '../styles/MarketSentimentWidget.css';

interface MarketSentimentWidgetProps {
  sentiment: 'fear' | 'neutral' | 'greed';
  marketData: any;
}

const MarketSentimentWidget: React.FC<MarketSentimentWidgetProps> = ({ sentiment, marketData }) => {
  // Calcular el valor del índice de miedo y codicia (0-100)
  const calculateFearGreedIndex = (): number => {
    if (!marketData) return 50;
    
    // Usar el cambio porcentual de capitalización de mercado como indicador
    const marketCapChange = marketData.market_cap_change_percentage_24h_usd || 0;
    
    // Convertir a un índice de 0-100 donde:
    // -10% o menos = 0 (miedo extremo)
    // +10% o más = 100 (codicia extrema)
    const index = ((marketCapChange + 10) / 20) * 100;
    
    // Limitar entre 0 y 100
    return Math.max(0, Math.min(100, index));
  };

  const fearGreedIndex = calculateFearGreedIndex();
  
  // Determinar la etiqueta del sentimiento basada en el índice
  const getSentimentLabel = (index: number): string => {
    if (index <= 25) return 'Miedo Extremo';
    if (index <= 40) return 'Miedo';
    if (index <= 60) return 'Neutral';
    if (index <= 80) return 'Codicia';
    return 'Codicia Extrema';
  };
  
  // Determinar el color del indicador
  const getIndicatorColor = (index: number): string => {
    if (index <= 25) return '#e74c3c'; // Rojo
    if (index <= 40) return '#e67e22'; // Naranja
    if (index <= 60) return '#f1c40f'; // Amarillo
    if (index <= 80) return '#2ecc71'; // Verde
    return '#27ae60'; // Verde oscuro
  };

  // Determinar el icono basado en el sentimiento
  const getSentimentIcon = (sentiment: 'fear' | 'neutral' | 'greed'): string => {
    switch (sentiment) {
      case 'fear':
        return 'fa-frown';
      case 'neutral':
        return 'fa-meh';
      case 'greed':
        return 'fa-smile';
      default:
        return 'fa-meh';
    }
  };

  if (!marketData) {
    return (
      <div className="market-sentiment-widget loading">
        <div className="widget-header">
          <h3>Sentimiento del Mercado</h3>
        </div>
        <div className="widget-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="market-sentiment-widget">
      <div className="widget-header">
        <h3>Sentimiento del Mercado</h3>
        <div className="sentiment-icon">
          <i className={`far ${getSentimentIcon(sentiment)}`}></i>
        </div>
      </div>
      <div className="widget-content">
        <div className="sentiment-meter">
          <div className="meter-labels">
            <span className="fear-label">Miedo Extremo</span>
            <span className="greed-label">Codicia Extrema</span>
          </div>
          <div className="meter-bar">
            <div className="meter-indicator" style={{ 
              left: `${fearGreedIndex}%`,
              backgroundColor: getIndicatorColor(fearGreedIndex)
            }}></div>
          </div>
          <div className="meter-value">
            <span className="value-label">Índice:</span>
            <span className="value-number">{Math.round(fearGreedIndex)}</span>
            <span className="value-text">{getSentimentLabel(fearGreedIndex)}</span>
          </div>
        </div>
        
        <div className="sentiment-stats">
          <div className="stat-item">
            <span className="stat-label">Volatilidad 24h:</span>
            <span className="stat-value">
              {marketData.market_cap_change_percentage_24h_usd 
                ? Math.abs(marketData.market_cap_change_percentage_24h_usd).toFixed(2) + '%'
                : 'N/A'}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Dominancia BTC:</span>
            <span className="stat-value">
              {marketData.market_cap_percentage?.btc 
                ? marketData.market_cap_percentage.btc.toFixed(2) + '%'
                : 'N/A'}
            </span>
          </div>
        </div>
        
        <div className="sentiment-advice">
          {sentiment === 'fear' && (
            <p>Cuando hay miedo en el mercado, puede ser momento de considerar comprar si tienes una estrategia a largo plazo.</p>
          )}
          {sentiment === 'neutral' && (
            <p>El mercado está en equilibrio. Es buen momento para revisar tu estrategia y hacer ajustes si es necesario.</p>
          )}
          {sentiment === 'greed' && (
            <p>Cuando hay codicia en el mercado, considera ser cauteloso y evaluar si es momento de asegurar algunas ganancias.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketSentimentWidget;
