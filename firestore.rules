rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Funciones de utilidad para las reglas de seguridad
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Regla por defecto: denegar todo el acceso
    match /{document=**} {
      allow read, write: if false;
    }

    // Reglas para la colección Users
    match /Users/<USER>
      // Permitir lectura si el usuario está autenticado y es el propietario
      allow read: if isOwner(userId);

      // Permitir creación si el usuario está autenticado y el ID del documento coincide con su UID
      allow create: if isOwner(userId) &&
                     request.resource.data.uid == userId &&
                     request.resource.data.email == request.auth.token.email;

      // Permitir actualización si el usuario está autenticado y es el propietario
      // y no está intentando cambiar su UID o email
      allow update: if isOwner(userId) &&
                     request.resource.data.uid == resource.data.uid &&
                     request.resource.data.email == resource.data.email;

      // No permitir eliminación de usuarios
      allow delete: if false;
    }

    // Reglas para la colección Portafolio
    match /Portafolio/{userId} {
      // Permitir lectura si el usuario está autenticado y es el propietario
      allow read: if isOwner(userId);

      // Permitir creación si el usuario está autenticado y el ID del documento coincide con su UID
      allow create: if isOwner(userId);

      // Permitir actualización si el usuario está autenticado y es el propietario
      allow update: if isOwner(userId);

      // Permitir eliminación si el usuario está autenticado y es el propietario
      allow delete: if isOwner(userId);
    }
  }
}
