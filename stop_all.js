/**
 * Script para detener procesos que estén utilizando los puertos necesarios
 */
const { exec } = require('child_process');
const os = require('os');

// Puertos utilizados por los componentes del sistema
const ports = [3101, 3102, 3103, 3104, 3001, 7777, 8000, 5173, 12345];

// Función para ejecutar un comando y devolver la salida
const execCommand = (command) => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
        return;
      }
      resolve(stdout.trim());
    });
  });
};

// Función para detener procesos en Windows
const stopProcessesWindows = async () => {
  console.log('Deteniendo procesos en Windows...');

  for (const port of ports) {
    try {
      // Obtener el PID del proceso que está utilizando el puerto
      // Usamos un comando más específico para obtener solo las conexiones TCP en estado LISTENING
      const output = await execCommand(`netstat -ano | findstr /i "listening" | findstr ":${port}"`);

      if (output) {
        console.log(`Encontrados procesos utilizando el puerto ${port}:`);
        console.log(output);

        // Extraer el PID del resultado
        const lines = output.split('\n');
        for (const line of lines) {
          // Verificar que la línea contiene el puerto correcto
          if (!line.includes(`:${port}`)) {
            continue;
          }

          // Extraer el PID del resultado
          // La salida de netstat -ano es algo como:
          // TCP    0.0.0.0:3101           0.0.0.0:0              LISTENING       12345
          // Donde el último número es el PID
          const parts = line.trim().split(/\s+/);

          // El PID debería estar en la última posición
          const pid = parts[parts.length - 1];

          // Verificar que el PID es un número válido y mayor que un umbral razonable
          const pidNum = parseInt(pid, 10);
          if (isNaN(pidNum) || pidNum <= 100) {
            console.warn(`Advertencia: PID inválido o potencialmente peligroso: ${pid}. Ignorando.`);
            continue;
          }

          console.log(`Deteniendo proceso con PID ${pid} (puerto ${port})...`);

          try {
            await execCommand(`taskkill /F /PID ${pid}`);
            console.log(`Proceso con PID ${pid} detenido correctamente.`);
          } catch (err) {
            console.error(`Error al detener el proceso con PID ${pid}: ${err.message}`);
          }
        }
      } else {
        console.log(`No se encontró ningún proceso utilizando el puerto ${port}.`);
      }
    } catch (err) {
      console.error(`Error al buscar procesos para el puerto ${port}: ${err.message}`);
    }
  }
};

// Función para detener procesos en Unix (Linux/macOS)
const stopProcessesUnix = async () => {
  console.log('Deteniendo procesos en Unix...');

  for (const port of ports) {
    try {
      // Obtener el PID del proceso que está utilizando el puerto
      // Usamos -n para mostrar números de puerto en lugar de nombres
      const output = await execCommand(`lsof -i :${port} -n -P -t`);

      if (output) {
        console.log(`Encontrados procesos utilizando el puerto ${port}:`);

        // Extraer los PIDs del resultado
        const pids = output.split('\n').filter(pid => pid.trim() !== '');

        console.log(`PIDs encontrados: ${pids.join(', ')}`);

        for (const pid of pids) {
          // Verificar que el PID es un número válido y mayor que un umbral razonable
          const pidNum = parseInt(pid, 10);
          if (isNaN(pidNum) || pidNum <= 100) {
            console.warn(`Advertencia: PID inválido o potencialmente peligroso: ${pid}. Ignorando.`);
            continue;
          }

          console.log(`Deteniendo proceso con PID ${pid} (puerto ${port})...`);

          try {
            // Primero intentamos un SIGTERM para una terminación más limpia
            await execCommand(`kill ${pid}`);

            // Esperamos un momento para que el proceso termine
            await new Promise(resolve => setTimeout(resolve, 500));

            // Verificamos si el proceso sigue en ejecución
            try {
              await execCommand(`ps -p ${pid} -o pid=`);
              // Si llegamos aquí, el proceso sigue en ejecución, usamos SIGKILL
              console.log(`El proceso ${pid} no respondió a SIGTERM, usando SIGKILL...`);
              await execCommand(`kill -9 ${pid}`);
            } catch (err) {
              // Si hay un error, probablemente el proceso ya terminó
            }

            console.log(`Proceso con PID ${pid} detenido correctamente.`);
          } catch (err) {
            console.error(`Error al detener el proceso con PID ${pid}: ${err.message}`);
          }
        }
      } else {
        console.log(`No se encontró ningún proceso utilizando el puerto ${port}.`);
      }
    } catch (err) {
      console.error(`Error al buscar procesos para el puerto ${port}: ${err.message}`);
    }
  }
};

// Función para verificar si un puerto está en uso
const isPortInUse = (port) => {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();

    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        resolve(true); // Puerto en uso
      } else {
        resolve(false);
      }
    });

    server.once('listening', () => {
      server.close();
      resolve(false); // Puerto libre
    });

    server.listen(port);
  });
};

// Función principal
const stopAllProcesses = async () => {
  console.log('=== Deteniendo todos los procesos existentes ===\n');

  // Verificar qué puertos están en uso antes de intentar detener los procesos
  console.log('Verificando puertos en uso...');
  for (const port of ports) {
    const inUse = await isPortInUse(port);
    console.log(`Puerto ${port}: ${inUse ? 'En uso' : 'Libre'}`);
  }

  // Determinar el sistema operativo
  const platform = os.platform();

  if (platform === 'win32') {
    await stopProcessesWindows();
  } else {
    await stopProcessesUnix();
  }

  // Verificar qué puertos siguen en uso después de intentar detener los procesos
  console.log('\nVerificando puertos después de detener los procesos...');
  for (const port of ports) {
    const inUse = await isPortInUse(port);
    console.log(`Puerto ${port}: ${inUse ? 'Sigue en uso' : 'Libre'}`);
  }

  console.log('\n=== Todos los procesos han sido detenidos ===');
};

// Ejecutar la función principal
stopAllProcesses().catch(err => {
  console.error('Error al detener los procesos:', err);
  process.exit(1);
});
