/**
 * ADK Agents Routes
 *
 * Este módulo proporciona rutas API para interactuar con los agentes ADK simulados.
 */

const express = require('express');
const router = express.Router();
const adkAgentsService = require('../services/adk-agents.service');
const googleAIService = require('../services/google-ai-simple.service');
const mockAgentsService = require('../services/mock-agents.service');
const adkMcpIntegrationService = require('../services/adk-mcp-integration.service');
const adkIntegration = require('../adk_integration');

// Middleware para manejar errores
const asyncHandler = fn => (req, res, next) =>
    Promise.resolve(fn(req, res, next)).catch(next);

/**
 * @route POST /api/adk-agents/guru
 * @desc Consultar al agente Guru Cripto
 * @access Public
 */
router.post('/guru', asyncHandler(async (req, res) => {
    const { question } = req.body;

    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }

    // Intentar primero con el servicio ADK, luego con el servicio de integración MCP, y finalmente con el servicio simulado
    let response;
    try {
        response = await adkIntegration.queryGuruAgent(question);
    } catch (adkError) {
        console.error('Error usando el servicio ADK:', adkError);
        try {
            response = await adkMcpIntegrationService.guruCriptoAgent(question);
        } catch (mcpError) {
            console.error('Error usando el servicio de integración MCP, usando servicio simulado:', mcpError);
            response = await mockAgentsService.guruCriptoAgent(question);
        }
    }
    res.json(response);
}));

/**
 * @route POST /api/adk-agents/technical
 * @desc Consultar al agente de análisis técnico
 * @access Public
 */
router.post('/technical', asyncHandler(async (req, res) => {
    const { question } = req.body;

    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }

    // Intentar primero con el servicio ADK, luego con el servicio de integración MCP, y finalmente con el servicio simulado
    let response;
    try {
        response = await adkIntegration.queryTechnicalAgent(question);
    } catch (adkError) {
        console.error('Error usando el servicio ADK:', adkError);
        try {
            response = await adkMcpIntegrationService.technicalAnalysisAgent(question);
        } catch (mcpError) {
            console.error('Error usando el servicio de integración MCP, usando servicio simulado:', mcpError);
            response = await mockAgentsService.technicalAnalysisAgent(question);
        }
    }
    res.json(response);
}));

/**
 * @route POST /api/adk-agents/sentiment
 * @desc Consultar al agente de análisis de sentimiento
 * @access Public
 */
router.post('/sentiment', asyncHandler(async (req, res) => {
    const { question } = req.body;

    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }

    // Intentar primero con el servicio ADK, luego con el servicio de integración MCP, y finalmente con el servicio simulado
    let response;
    try {
        response = await adkIntegration.querySentimentAgent(question);
    } catch (adkError) {
        console.error('Error usando el servicio ADK:', adkError);
        try {
            response = await adkMcpIntegrationService.sentimentAnalysisAgent(question);
        } catch (mcpError) {
            console.error('Error usando el servicio de integración MCP, usando servicio simulado:', mcpError);
            response = await mockAgentsService.sentimentAnalysisAgent(question);
        }
    }
    res.json(response);
}));

/**
 * @route POST /api/adk-agents/onchain
 * @desc Consultar al agente de análisis on-chain
 * @access Public
 */
router.post('/onchain', asyncHandler(async (req, res) => {
    const { question } = req.body;

    if (!question) {
        return res.status(400).json({ error: 'Question is required' });
    }

    // Intentar primero con el servicio ADK, luego con el servicio de integración MCP, y finalmente con el servicio simulado
    let response;
    try {
        response = await adkIntegration.queryOnchainAgent(question);
    } catch (adkError) {
        console.error('Error usando el servicio ADK:', adkError);
        try {
            response = await adkMcpIntegrationService.onchainAnalysisAgent(question);
        } catch (mcpError) {
            console.error('Error usando el servicio de integración MCP, usando servicio simulado:', mcpError);
            response = await mockAgentsService.onchainAnalysisAgent(question);
        }
    }
    res.json(response);
}));

/**
 * @route POST /api/adk-agents/analysis
 * @desc Generar un análisis completo para una criptomoneda
 * @access Public
 */
router.post('/analysis', asyncHandler(async (req, res) => {
    const { cryptoName, timeframe } = req.body;

    if (!cryptoName) {
        return res.status(400).json({ error: 'Cryptocurrency name is required' });
    }

    // Intentar primero con el servicio ADK, luego con el servicio de integración MCP, y finalmente con el servicio simulado
    let response;
    try {
        response = await adkIntegration.generateComprehensiveAnalysis(
            cryptoName,
            timeframe || '7d'
        );
    } catch (adkError) {
        console.error('Error usando el servicio ADK:', adkError);
        try {
            response = await adkMcpIntegrationService.generateComprehensiveAnalysis(
                cryptoName,
                timeframe || '7d'
            );
        } catch (mcpError) {
            console.error('Error usando el servicio de integración MCP, usando servicio simulado:', mcpError);
            response = await mockAgentsService.generateComprehensiveAnalysis(
                cryptoName,
                timeframe || '7d'
            );
        }
    }

    res.json(response);
}));

/**
 * @route POST /api/adk-agents/prediction
 * @desc Generar una predicción de precio para una criptomoneda
 * @access Public
 */
router.post('/prediction', asyncHandler(async (req, res) => {
    const { cryptoName, timeframe } = req.body;

    if (!cryptoName) {
        return res.status(400).json({ error: 'Cryptocurrency name is required' });
    }

    // Intentar primero con el servicio ADK, luego con el servicio de integración MCP, y finalmente con el servicio simulado
    let response;
    try {
        response = await adkIntegration.generatePricePrediction(
            cryptoName,
            timeframe || '7d'
        );
    } catch (adkError) {
        console.error('Error usando el servicio ADK:', adkError);
        try {
            response = await adkMcpIntegrationService.generatePricePrediction(
                cryptoName,
                timeframe || '7d'
            );
        } catch (mcpError) {
            console.error('Error usando el servicio de integración MCP, usando servicio simulado:', mcpError);
            response = await mockAgentsService.generatePricePrediction(
                cryptoName,
                timeframe || '7d'
            );
        }
    }

    res.json(response);
}));

module.exports = router;
