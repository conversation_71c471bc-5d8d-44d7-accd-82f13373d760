/**
 * Servicio para interactuar con el servidor MCP de Criptokens
 * 
 * Este servicio proporciona funciones para realizar peticiones al servidor MCP
 * que ofrece herramientas y recursos para análisis de criptomonedas.
 */

// URL del servidor MCP desde las variables de entorno
const MCP_SERVER_URL = import.meta.env.VITE_MCP_SERVER_URL || 'http://localhost:3101';

/**
 * Realiza una petición al servidor MCP
 * @param server Servidor MCP a utilizar ('crypto', 'brave', 'playwright')
 * @param endpoint Endpoint a llamar
 * @param params Parámetros para la petición
 * @returns Respuesta del servidor MCP
 */
export const fetchFromMCP = async (server: string, endpoint: string, params: any = {}): Promise<any> => {
  try {
    // Determinar la URL base según el servidor
    let baseUrl = MCP_SERVER_URL;
    
    // Ajustar URL según el servidor solicitado
    if (server === 'brave') {
      baseUrl = import.meta.env.VITE_BRAVE_MCP_URL || 'http://localhost:3102';
    } else if (server === 'playwright') {
      baseUrl = import.meta.env.VITE_PLAYWRIGHT_MCP_URL || 'http://localhost:3103';
    }
    
    // Construir la URL completa
    const url = `${baseUrl}${endpoint}`;
    
    // Realizar la petición
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    });
    
    if (!response.ok) {
      throw new Error(`Error en la petición al servidor MCP ${server}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error al realizar petición a MCP (${server}${endpoint}):`, error);
    throw error;
  }
};
