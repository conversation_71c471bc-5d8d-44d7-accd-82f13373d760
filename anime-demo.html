<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Criptokens - Animated Demo</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #1a1f2c, #121620);
      color: white;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px 20px;
    }

    .container {
      max-width: 1200px;
      width: 100%;
      position: relative;
    }

    h1 {
      text-align: center;
      margin-bottom: 50px;
      font-size: 36px;
      background: linear-gradient(90deg, #6366f1, #a5b4fc);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .animation-section {
      margin-bottom: 60px;
    }

    .animation-section h2 {
      margin-bottom: 20px;
      font-size: 24px;
      color: #a5b4fc;
    }

    .box-container {
      height: 300px;
      position: relative;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.2);
      overflow: hidden;
    }

    .box {
      width: 100px;
      height: 100px;
      background-color: #E91E63;
      position: absolute;
      left: 50px;
      top: 100px;
    }

    .circle {
      width: 80px;
      height: 80px;
      background-color: #4CAF50;
      border-radius: 50%;
      position: absolute;
      left: 200px;
      top: 110px;
    }

    .square {
      width: 60px;
      height: 60px;
      background-color: #FFC107;
      position: absolute;
      left: 350px;
      top: 120px;
      transform: rotate(45deg);
    }

    .buttons {
      display: flex;
      gap: 15px;
      margin-top: 20px;
      flex-wrap: wrap;
      justify-content: center;
    }

    button {
      padding: 12px 24px;
      background: #6366f1;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    button:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
    }

    .timeline-demo {
      height: 200px;
      position: relative;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.2);
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .timeline-element {
      width: 50px;
      height: 50px;
      background: #9C27B0;
      margin: 0 10px;
    }

    .stagger-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }

    .stagger-element {
      width: 20px;
      height: 80px;
      background: #2196F3;
      margin: 0 5px;
      transform-origin: bottom;
    }

    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.4);
      transform: scale(0);
      pointer-events: none;
    }

    .confetti {
      position: absolute;
      width: 10px;
      height: 10px;
      top: -10px;
      border-radius: 0;
    }

    @keyframes shimmer {
      0% {
        background-position: -100% 0;
      }
      100% {
        background-position: 200% 0;
      }
    }

    h1 {
      animation: shimmer 2s infinite;
      background-size: 200% 100%;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Demostración de Anime.js</h1>

    <div class="animation-section">
      <h2>Animaciones Básicas</h2>
      <div class="box-container">
        <div class="box" id="box"></div>
        <div class="circle" id="circle"></div>
        <div class="square" id="square"></div>
      </div>
      <div class="buttons">
        <button id="play-basic">Reproducir</button>
        <button id="pause-basic">Pausar</button>
        <button id="restart-basic">Reiniciar</button>
      </div>
    </div>

    <div class="animation-section">
      <h2>Timeline (Línea de Tiempo)</h2>
      <div class="timeline-demo">
        <div class="timeline-element" id="elem1"></div>
        <div class="timeline-element" id="elem2"></div>
        <div class="timeline-element" id="elem3"></div>
      </div>
      <div class="buttons">
        <button id="play-timeline">Reproducir Timeline</button>
        <button id="reverse-timeline">Invertir Timeline</button>
      </div>
    </div>

    <div class="animation-section">
      <h2>Staggering (Escalonamiento)</h2>
      <div class="stagger-container">
        <div class="stagger-element"></div>
        <div class="stagger-element"></div>
        <div class="stagger-element"></div>
        <div class="stagger-element"></div>
        <div class="stagger-element"></div>
        <div class="stagger-element"></div>
        <div class="stagger-element"></div>
        <div class="stagger-element"></div>
      </div>
      <div class="buttons">
        <button id="play-stagger">Animar Escalonadamente</button>
      </div>
    </div>

    <div class="animation-section">
      <h2>Efectos Especiales</h2>
      <div class="buttons">
        <button id="ripple-effect">Efecto de Onda</button>
        <button id="confetti-effect">Lanzar Confeti</button>
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
  <script>
    // Animaciones Básicas
    const boxAnimation = anime({
      targets: '#box',
      translateX: 250,
      rotate: '1turn',
      backgroundColor: '#FFC107',
      duration: 2000,
      loop: true,
      direction: 'alternate',
      easing: 'easeInOutQuad',
      autoplay: false
    });

    const circleAnimation = anime({
      targets: '#circle',
      translateX: 150,
      translateY: 50,
      scale: [1, 1.5, 1],
      backgroundColor: ['#4CAF50', '#2196F3', '#4CAF50'],
      duration: 3000,
      loop: true,
      direction: 'alternate',
      easing: 'easeInOutSine',
      autoplay: false
    });

    const squareAnimation = anime({
      targets: '#square',
      translateX: -150,
      translateY: -50,
      rotate: '2turn',
      backgroundColor: '#9C27B0',
      duration: 4000,
      loop: true,
      direction: 'alternate',
      easing: 'easeOutElastic(1, .5)',
      autoplay: false
    });

    // Timeline
    const timelineAnimation = anime.timeline({
      easing: 'easeOutExpo',
      duration: 750,
      autoplay: false
    });

    timelineAnimation
      .add({
        targets: '#elem1',
        translateY: -50,
        backgroundColor: '#FF5722',
        borderRadius: ['0%', '50%']
      })
      .add({
        targets: '#elem2',
        translateY: -50,
        backgroundColor: '#3F51B5',
        borderRadius: ['0%', '50%']
      }, '-=600')
      .add({
        targets: '#elem3',
        translateY: -50,
        backgroundColor: '#009688',
        borderRadius: ['0%', '50%']
      }, '-=600');

    // Staggering
    const staggerAnimation = anime({
      targets: '.stagger-element',
      scaleY: [0, 1],
      duration: 1000,
      delay: anime.stagger(100),
      easing: 'easeOutElastic(1, .5)',
      autoplay: false
    });

    // Event Listeners
    document.getElementById('play-basic').addEventListener('click', () => {
      boxAnimation.play();
      circleAnimation.play();
      squareAnimation.play();
    });

    document.getElementById('pause-basic').addEventListener('click', () => {
      boxAnimation.pause();
      circleAnimation.pause();
      squareAnimation.pause();
    });

    document.getElementById('restart-basic').addEventListener('click', () => {
      boxAnimation.restart();
      circleAnimation.restart();
      squareAnimation.restart();
    });

    document.getElementById('play-timeline').addEventListener('click', () => {
      timelineAnimation.play();
    });

    document.getElementById('reverse-timeline').addEventListener('click', () => {
      timelineAnimation.reverse();
      timelineAnimation.play();
    });

    document.getElementById('play-stagger').addEventListener('click', () => {
      staggerAnimation.restart();
    });

    // Ripple Effect
    document.getElementById('ripple-effect').addEventListener('click', (e) => {
      const button = e.currentTarget;
      const rect = button.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const ripple = document.createElement('span');
      ripple.className = 'ripple';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';

      button.appendChild(ripple);

      anime({
        targets: ripple,
        scale: 3,
        opacity: [1, 0],
        duration: 800,
        easing: 'easeOutExpo',
        complete: () => {
          button.removeChild(ripple);
        }
      });
    });

    // Confetti Effect
    document.getElementById('confetti-effect').addEventListener('click', () => {
      const container = document.querySelector('.container');
      const confettiCount = 50;
      const colors = ['#FFC107', '#2196F3', '#4CAF50', '#E91E63', '#9C27B0'];

      for (let i = 0; i < confettiCount; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.left = Math.random() * 100 + '%';
        container.appendChild(confetti);

        anime({
          targets: confetti,
          top: '100%',
          left: '+=' + anime.random(-100, 100) + '%',
          rotate: anime.random(-360, 360),
          duration: anime.random(1000, 3000),
          delay: anime.random(0, 1000),
          easing: 'easeOutCirc',
          complete: () => {
            container.removeChild(confetti);
          }
        });
      }
    });

    // Auto-play some animations on load
    window.addEventListener('load', () => {
      setTimeout(() => {
        boxAnimation.play();
        circleAnimation.play();
        squareAnimation.play();
      }, 1000);
    });
  </script>
</body>
</html>
