.conversation-sidebar {
  width: 280px;
  height: 100%;
  background-color: var(--color-surface-dark);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h2 {
  font-size: 1.2rem;
  margin: 0;
  color: var(--text-primary);
}

.new-chat-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.new-chat-button:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
}

.conversation-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex: 1;
}

.conversation-item {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;
}

.conversation-item:hover {
  background-color: var(--color-surface);
}

.conversation-item.active {
  background-color: var(--color-surface-light);
  border-left: 3px solid var(--color-primary);
}

.conversation-icon {
  color: var(--color-primary);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.conversation-details {
  flex: 1;
  overflow: hidden;
}

.conversation-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.conversation-date {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.delete-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  opacity: 0;
  transition: var(--transition-fast);
}

.conversation-item:hover .delete-button {
  opacity: 1;
}

.delete-button:hover {
  color: var(--color-error);
  background-color: rgba(255, 0, 0, 0.1);
}

.loading-container, .error-container, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  text-align: center;
  height: 100%;
  gap: var(--spacing-md);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-container i, .empty-state i {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
  color: var(--color-primary);
}

.error-container button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  margin-top: var(--spacing-sm);
}

.empty-state p {
  margin: var(--spacing-xs) 0;
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
  .conversation-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 250px;
  }
  
  .conversation-sidebar.open {
    transform: translateX(0);
  }
}
