import React, { useState, useEffect } from 'react';
import MarketSummary from './MarketSummary';
import CryptoDetail from './CryptoDetail';
import NewsSection from './NewsSection';
import EnhancedPortfolio from './EnhancedPortfolio';
import EnhancedChatInterface from './EnhancedChatInterface';
import CryptoGrid from './CryptoGrid';
import CryptoMarketInsights from './CryptoMarketInsights';
import { getTopCryptocurrencies } from '../services/api';
import '../styles/EnhancedDashboard.css';

const BasicDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCryptoId, setSelectedCryptoId] = useState<string | null>(null);
  const [cryptos, setCryptos] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Cargar datos de criptomonedas
  useEffect(() => {
    const fetchCryptos = async () => {
      try {
        setIsLoading(true);
        const data = await getTopCryptocurrencies(20);
        setCryptos(data);
      } catch (error) {
        console.error('Error al cargar criptomonedas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCryptos();

    // Actualizar datos cada 60 segundos
    const interval = setInterval(fetchCryptos, 60000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="dashboard-container">
      <div className="dashboard-sidebar">
        <div className="logo">
          <div className="logo-icon">
            <div className="logo-core"></div>
            <div className="logo-ring ring1"></div>
            <div className="logo-ring ring2"></div>
          </div>
          <span className="logo-text">Cryptokens</span>
        </div>

        <nav className="sidebar-nav">
          <button
            className={`nav-item ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            <span className="nav-icon">📊</span>
            <span className="nav-text">Dashboard</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'portfolio' ? 'active' : ''}`}
            onClick={() => setActiveTab('portfolio')}
          >
            <span className="nav-icon">💼</span>
            <span className="nav-text">Mi Cartera</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'news' ? 'active' : ''}`}
            onClick={() => setActiveTab('news')}
          >
            <span className="nav-icon">📰</span>
            <span className="nav-text">Noticias</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'chat' ? 'active' : ''}`}
            onClick={() => setActiveTab('chat')}
          >
            <span className="nav-icon">🤖</span>
            <span className="nav-text">Asistente IA</span>
          </button>
        </nav>
      </div>

      <div className="dashboard-main">
        <div className="dashboard-content">
          {activeTab === 'dashboard' && (
            selectedCryptoId ? (
              <CryptoDetail
                cryptoId={selectedCryptoId}
                onBack={() => setSelectedCryptoId(null)}
              />
            ) : (
              <div className="dashboard-overview">
                <MarketSummary />
                <CryptoMarketInsights />
                <div className="dashboard-main-content">
                  <div className="main-section">
                    {isLoading ? (
                      <div className="loading-container">
                        <div className="loading-spinner"></div>
                        <p>Cargando criptomonedas...</p>
                      </div>
                    ) : (
                      <CryptoGrid
                        cryptos={cryptos}
                        onSelectCrypto={(id) => setSelectedCryptoId(id)}
                      />
                    )}
                  </div>
                  <div className="side-section">
                    <div className="chat-widget">
                      <div className="widget-header">
                        <div className="widget-icon">
                          <div className="agent-core"></div>
                          <div className="agent-ring ring-1"></div>
                          <div className="agent-ring ring-2"></div>
                        </div>
                        <h3>Asistente Cripto</h3>
                      </div>
                      <p>Pregunta sobre criptomonedas, blockchain o cualquier duda que tengas.</p>
                      <button
                        className="open-chat-button"
                        onClick={() => setActiveTab('chat')}
                      >
                        Abrir Chat
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}

          {activeTab === 'portfolio' && (
            <div style={{ padding: '20px' }}>
              <h2>Mi Cartera</h2>
              <p>Aquí podrás ver y gestionar tus inversiones en criptomonedas.</p>
            </div>
          )}

          {activeTab === 'news' && (
            <NewsSection />
          )}

          {activeTab === 'chat' && (
            <div className="chat-fullscreen">
              <div className="chat-header">
                <div className="agent-avatar">
                  <div className="agent-core"></div>
                  <div className="agent-ring ring-1"></div>
                  <div className="agent-ring ring-2"></div>
                </div>
                <div className="agent-info">
                  <h2>Asistente Cripto</h2>
                  <p>Tu experto en criptomonedas y blockchain</p>
                </div>
              </div>
              <div style={{ padding: '20px' }}>
                <h3>Chat con el Asistente</h3>
                <p>Aquí podrás chatear con el asistente de criptomonedas.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BasicDashboard;
