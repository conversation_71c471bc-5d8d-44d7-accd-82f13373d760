const express = require('express');
const router = express.Router();
const ultravoxService = require('../services/ultravox.service');

/**
 * @route GET /api/ultravox/status
 * @desc Verifica el estado de la API de Ultravox
 * @access Public
 */
router.get('/status', async (req, res) => {
  try {
    console.log('Verificando estado de Ultravox API...');

    // Usar el servicio para verificar el estado de la API
    const status = await ultravoxService.checkApiStatus();

    return res.status(200).json(status);
  } catch (error) {
    console.error('Error verificando estado de Ultravox:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al verificar el estado de la API de Ultravox',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/apikey
 * @desc Actualiza la clave de API de Ultravox
 * @access Private
 */
router.post('/apikey', async (req, res) => {
  try {
    const { apiKey } = req.body;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        message: 'La clave de API es requerida'
      });
    }

    // Actualizar la clave de API en el servicio
    try {
      // Actualizar la API key en el servicio
      ultravoxService.updateApiKey(apiKey);

      // Reiniciar el modo de fallback
      process.env.ULTRAVOX_FORCE_FALLBACK = 'false';

      // Verificar si la nueva clave es válida
      const status = await ultravoxService.checkApiStatus();

      return res.status(200).json({
        success: true,
        isValid: status.isValid,
        message: 'Clave de API actualizada correctamente',
        status
      });
    } catch (error) {
      console.error('Error verificando la nueva clave de API:', error);
      return res.status(200).json({
        success: true,
        isValid: false,
        message: 'Clave de API actualizada, pero no se pudo verificar su validez',
        error: error.message
      });
    }
  } catch (error) {
    console.error('Error actualizando clave de API de Ultravox:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al actualizar la clave de API',
      error: error.message
    });
  }
});

/**
 * @route GET /api/ultravox/credits
 * @desc Obtiene el saldo de créditos
 * @access Private
 */
router.get('/credits', async (req, res) => {
  try {
    // Simulamos que hay 100 créditos disponibles
    return res.status(200).json({
      success: true,
      credits: 100
    });
  } catch (error) {
    console.error('Error obteniendo créditos:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener el saldo de créditos',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/calls
 * @desc Inicia una nueva llamada de voz
 * @access Public
 */
router.post('/calls', async (req, res) => {
  try {
    console.log('Iniciando nueva llamada de voz...');
    const { language } = req.body;

    // Prompt por defecto para el Guru Cripto
    const defaultPrompt = "Eres el Guru Cripto, un experto en criptomonedas y blockchain. Ayudas a los usuarios con información sobre precios, tecnología, tendencias y consejos sobre inversiones en criptomonedas.";

    // Usar el servicio para iniciar una nueva llamada simulada
    const result = await ultravoxService.startCall(defaultPrompt, language || 'es');

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error iniciando llamada de voz:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al iniciar la llamada de voz',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/calls/:callId/end
 * @desc Finaliza una llamada de voz
 * @access Public
 */
router.post('/calls/:callId/end', async (req, res) => {
  try {
    const { callId } = req.params;
    console.log(`Finalizando llamada ${callId}...`);

    // Usar el servicio para finalizar la llamada
    const result = await ultravoxService.endCall(callId);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error finalizando llamada de voz:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al finalizar la llamada de voz',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/calls/:callId/messages
 * @desc Envía un mensaje en una llamada de voz
 * @access Public
 */
router.post('/calls/:callId/messages', async (req, res) => {
  try {
    const { callId } = req.params;
    const { message } = req.body;
    console.log(`Enviando mensaje en llamada ${callId}: ${message}`);

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'El mensaje es requerido'
      });
    }

    // Usar el servicio para enviar el mensaje y obtener la respuesta
    const result = await ultravoxService.sendMessage(callId, message);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error enviando mensaje en llamada de voz:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al enviar el mensaje',
      error: error.message
    });
  }
});

/**
 * @route GET /api/ultravox/calls/:callId/messages
 * @desc Obtiene los mensajes de una llamada de voz
 * @access Public
 */
router.get('/calls/:callId/messages', async (req, res) => {
  try {
    const { callId } = req.params;
    console.log(`Obteniendo mensajes de llamada ${callId}...`);

    // Usar el servicio para obtener los mensajes de la llamada
    const result = await ultravoxService.getMessages(callId);

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error obteniendo mensajes de llamada de voz:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener los mensajes',
      error: error.message
    });
  }
});

/**
 * @route GET /api/ultravox/audio/:id
 * @desc Obtiene un archivo de audio predefinido
 * @access Public
 */
router.get('/audio/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Obteniendo audio predefinido ${id}...`);

    // Establecer los encabezados CORS
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Texto para generar el audio según el ID
    let text = '';
    switch (id) {
      case 'welcome':
        text = 'Hola, soy el Guru Cripto. Estoy aquí para ayudarte con tus consultas sobre criptomonedas y blockchain. ¿En qué puedo ayudarte hoy?';
        break;
      case 'bitcoin':
        text = 'Bitcoin es la primera y más conocida criptomoneda. Actualmente tiene un precio aproximado de $63,000 USD y una capitalización de mercado de más de 1 billón de dólares.';
        break;
      case 'ethereum':
        text = 'Ethereum es la segunda criptomoneda más grande por capitalización de mercado. Es una plataforma descentralizada que permite la creación de contratos inteligentes y aplicaciones descentralizadas.';
        break;
      case 'precios':
        text = 'Los precios de las criptomonedas son muy volátiles. Bitcoin está alrededor de $63,000 USD, Ethereum cerca de $3,000 USD, y Binance Coin aproximadamente $500 USD.';
        break;
      case 'test':
        text = 'Este es un audio de prueba para verificar que la reproducción de audio funciona correctamente.';
        break;
      default:
        text = 'Entiendo tu consulta. Como Guru Cripto, puedo ayudarte con información sobre criptomonedas, blockchain, DeFi y más. ¿Hay algo específico sobre lo que quieras saber?';
    }

    console.log(`Generando audio para texto: "${text.substring(0, 50)}..."`);

    // Usar el servicio para generar el audio
    const audioBuffer = await ultravoxService.generateAudio(text);

    console.log(`Audio generado correctamente, tamaño: ${audioBuffer.length} bytes`);

    // Establecer los encabezados de respuesta
    res.set('Content-Type', 'audio/mpeg');
    res.set('Content-Length', audioBuffer.length);

    // Añadir encabezados de caché para evitar problemas de reproducción
    res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.set('Pragma', 'no-cache');
    res.set('Expires', '0');

    // Añadir encabezados adicionales para mejorar la compatibilidad
    res.set('Accept-Ranges', 'bytes');
    res.set('Connection', 'keep-alive');

    // Enviar el archivo de audio
    console.log('Enviando respuesta de audio al cliente');
    return res.send(audioBuffer);
  } catch (error) {
    console.error('Error obteniendo audio predefinido:', error);

    // En caso de error, enviar un audio de fallback muy simple
    try {
      console.log('Generando audio de fallback debido al error');

      // Crear un MP3 muy simple
      const fallbackAudio = Buffer.from([
        0xFF, 0xFB, 0x90, 0x44, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
      ]);

      // Establecer los encabezados de respuesta
      res.set('Content-Type', 'audio/mpeg');
      res.set('Content-Length', fallbackAudio.length);
      res.set('Cache-Control', 'no-cache, no-store, must-revalidate');

      console.log('Enviando audio de fallback al cliente');
      return res.send(fallbackAudio);
    } catch (fallbackError) {
      console.error('Error al generar audio de fallback:', fallbackError);
      return res.status(500).json({
        success: false,
        message: 'Error al obtener el audio',
        error: error.message
      });
    }
  }
});

/**
 * @route GET /api/ultravox/audio/response
 * @desc Genera un archivo de audio a partir de texto
 * @access Public
 */
router.get('/audio/response', async (req, res) => {
  try {
    const { text, voice, speed, pitch } = req.query;

    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'El parámetro text es requerido'
      });
    }

    console.log(`Generando audio para texto: ${text.substring(0, 50)}...`);

    // Opciones para la generación de audio
    const options = {};

    // Añadir opciones si están presentes
    if (speed) options.speed = parseFloat(speed);
    if (pitch) options.pitch = parseFloat(pitch);

    // Establecer los encabezados CORS
    res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:5174');
    res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');

    // Usar el servicio para generar el audio con la voz especificada
    const audioBuffer = await ultravoxService.generateAudio(text, voice, options);

    // Establecer los encabezados de respuesta
    res.set('Content-Type', 'audio/mpeg');
    res.set('Content-Length', audioBuffer.length);

    // Añadir encabezados de caché para evitar problemas de reproducción
    res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.set('Pragma', 'no-cache');
    res.set('Expires', '0');

    // Añadir encabezados adicionales para mejorar la compatibilidad
    res.set('Accept-Ranges', 'bytes');
    res.set('Connection', 'keep-alive');

    // Enviar el archivo de audio
    console.log(`Enviando audio generado, tamaño: ${audioBuffer.length} bytes`);
    return res.send(audioBuffer);
  } catch (error) {
    console.error('Error generando audio de respuesta:', error);

    // En caso de error, intentar generar un audio de fallback
    try {
      console.log('Generando audio de fallback debido al error');
      const fallbackAudio = await ultravoxService.generateAudio(
        "Lo siento, ha ocurrido un error al generar el audio. Por favor, inténtalo de nuevo."
      );

      // Establecer los encabezados de respuesta
      res.set('Content-Type', 'audio/mpeg');
      res.set('Content-Length', fallbackAudio.length);
      res.set('Cache-Control', 'no-cache, no-store, must-revalidate');

      return res.send(fallbackAudio);
    } catch (fallbackError) {
      console.error('Error al generar audio de fallback:', fallbackError);
      return res.status(500).json({
        success: false,
        message: 'Error al generar el audio de respuesta',
        error: error.message
      });
    }
  }
});

/**
 * @route GET /api/ultravox/text/:id
 * @desc Obtiene el texto correspondiente a un ID de audio
 * @access Public
 */
router.get('/text/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`Obteniendo texto para audio ${id}...`);

    // Establecer los encabezados CORS
    res.header('Access-Control-Allow-Origin', req.headers.origin || 'http://localhost:5174');
    res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');

    // Texto según el ID
    let text = '';
    switch (id) {
      case 'welcome':
        text = 'Hola, soy el Guru Cripto. Estoy aquí para ayudarte con tus consultas sobre criptomonedas y blockchain. ¿En qué puedo ayudarte hoy?';
        break;
      case 'bitcoin':
        text = 'Bitcoin es la primera y más conocida criptomoneda. Actualmente tiene un precio aproximado de $63,000 USD y una capitalización de mercado de más de 1 billón de dólares.';
        break;
      case 'ethereum':
        text = 'Ethereum es la segunda criptomoneda más grande por capitalización de mercado. Es una plataforma descentralizada que permite la creación de contratos inteligentes y aplicaciones descentralizadas.';
        break;
      case 'precios':
        text = 'Los precios de las criptomonedas son muy volátiles. Bitcoin está alrededor de $63,000 USD, Ethereum cerca de $3,000 USD, y Binance Coin aproximadamente $500 USD.';
        break;
      case 'test':
        text = 'Este es un audio de prueba para verificar que la reproducción de audio funciona correctamente.';
        break;
      default:
        text = 'Entiendo tu consulta. Como Guru Cripto, puedo ayudarte con información sobre criptomonedas, blockchain, DeFi y más. ¿Hay algo específico sobre lo que quieras saber?';
    }

    return res.json({
      success: true,
      text
    });
  } catch (error) {
    console.error('Error obteniendo texto para audio:', error);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener el texto para el audio',
      error: error.message
    });
  }
});

module.exports = router;
