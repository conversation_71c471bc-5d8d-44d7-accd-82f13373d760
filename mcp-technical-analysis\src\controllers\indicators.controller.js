/**
 * Controlador de Indicadores Técnicos
 * 
 * Este controlador maneja las solicitudes HTTP para calcular
 * indicadores técnicos y realizar análisis de datos de criptomonedas.
 */

const technicalService = require('../services/technical.service');
const { validatePrices, validateCandles } = require('../utils/validation');

/**
 * Calcula el RSI (Relative Strength Index)
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function calculateRSI(req, res) {
  try {
    const { prices, period = 14 } = req.body;
    
    // Validar datos de entrada
    if (!validatePrices(prices)) {
      return res.status(400).json({
        error: 'Datos de precios inválidos',
        message: 'Se requiere un array de precios numéricos'
      });
    }
    
    // Calcular RSI
    const result = technicalService.calculateRSI(prices, period);
    
    // Devolver resultado
    res.json({
      indicator: 'RSI',
      period,
      values: result,
      currentValue: result[result.length - 1],
      interpretation: interpretRSI(result[result.length - 1])
    });
  } catch (error) {
    console.error('Error al calcular RSI:', error);
    res.status(500).json({
      error: 'Error al calcular RSI',
      message: error.message
    });
  }
}

/**
 * Calcula el MACD (Moving Average Convergence Divergence)
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function calculateMACD(req, res) {
  try {
    const {
      prices,
      fastPeriod = 12,
      slowPeriod = 26,
      signalPeriod = 9
    } = req.body;
    
    // Validar datos de entrada
    if (!validatePrices(prices)) {
      return res.status(400).json({
        error: 'Datos de precios inválidos',
        message: 'Se requiere un array de precios numéricos'
      });
    }
    
    // Calcular MACD
    const result = technicalService.calculateMACD(
      prices,
      fastPeriod,
      slowPeriod,
      signalPeriod
    );
    
    // Obtener el último valor
    const lastValue = result[result.length - 1];
    const previousValue = result[result.length - 2];
    
    // Determinar señal
    let signal = 'NEUTRAL';
    if (lastValue && previousValue) {
      if (lastValue.MACD > lastValue.signal && previousValue.MACD <= previousValue.signal) {
        signal = 'COMPRA';
      } else if (lastValue.MACD < lastValue.signal && previousValue.MACD >= previousValue.signal) {
        signal = 'VENTA';
      }
    }
    
    // Devolver resultado
    res.json({
      indicator: 'MACD',
      parameters: {
        fastPeriod,
        slowPeriod,
        signalPeriod
      },
      values: result,
      currentValue: lastValue,
      signal,
      interpretation: interpretMACD(lastValue, previousValue)
    });
  } catch (error) {
    console.error('Error al calcular MACD:', error);
    res.status(500).json({
      error: 'Error al calcular MACD',
      message: error.message
    });
  }
}

/**
 * Calcula las Bandas de Bollinger
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function calculateBollingerBands(req, res) {
  try {
    const {
      prices,
      period = 20,
      stdDev = 2
    } = req.body;
    
    // Validar datos de entrada
    if (!validatePrices(prices)) {
      return res.status(400).json({
        error: 'Datos de precios inválidos',
        message: 'Se requiere un array de precios numéricos'
      });
    }
    
    // Calcular Bandas de Bollinger
    const result = technicalService.calculateBollingerBands(
      prices,
      period,
      stdDev
    );
    
    // Obtener el último valor
    const lastValue = result[result.length - 1];
    const currentPrice = prices[prices.length - 1];
    
    // Determinar posición del precio
    let position = 'DENTRO';
    let signal = 'NEUTRAL';
    
    if (lastValue) {
      if (currentPrice >= lastValue.upper) {
        position = 'SUPERIOR';
        signal = 'VENTA';
      } else if (currentPrice <= lastValue.lower) {
        position = 'INFERIOR';
        signal = 'COMPRA';
      }
    }
    
    // Devolver resultado
    res.json({
      indicator: 'Bollinger Bands',
      parameters: {
        period,
        stdDev
      },
      values: result,
      currentValue: lastValue,
      currentPrice,
      position,
      signal,
      interpretation: interpretBollingerBands(currentPrice, lastValue)
    });
  } catch (error) {
    console.error('Error al calcular Bandas de Bollinger:', error);
    res.status(500).json({
      error: 'Error al calcular Bandas de Bollinger',
      message: error.message
    });
  }
}

/**
 * Calcula la Media Móvil Simple (SMA)
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function calculateSMA(req, res) {
  try {
    const { prices, period = 20 } = req.body;
    
    // Validar datos de entrada
    if (!validatePrices(prices)) {
      return res.status(400).json({
        error: 'Datos de precios inválidos',
        message: 'Se requiere un array de precios numéricos'
      });
    }
    
    // Calcular SMA
    const result = technicalService.calculateSMA(prices, period);
    
    // Obtener el último valor
    const lastValue = result[result.length - 1];
    const currentPrice = prices[prices.length - 1];
    
    // Determinar señal
    let signal = 'NEUTRAL';
    if (currentPrice > lastValue) {
      signal = 'ALCISTA';
    } else if (currentPrice < lastValue) {
      signal = 'BAJISTA';
    }
    
    // Devolver resultado
    res.json({
      indicator: 'SMA',
      period,
      values: result,
      currentValue: lastValue,
      currentPrice,
      signal,
      interpretation: interpretSMA(currentPrice, lastValue, period)
    });
  } catch (error) {
    console.error('Error al calcular SMA:', error);
    res.status(500).json({
      error: 'Error al calcular SMA',
      message: error.message
    });
  }
}

/**
 * Calcula la Media Móvil Exponencial (EMA)
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function calculateEMA(req, res) {
  try {
    const { prices, period = 20 } = req.body;
    
    // Validar datos de entrada
    if (!validatePrices(prices)) {
      return res.status(400).json({
        error: 'Datos de precios inválidos',
        message: 'Se requiere un array de precios numéricos'
      });
    }
    
    // Calcular EMA
    const result = technicalService.calculateEMA(prices, period);
    
    // Obtener el último valor
    const lastValue = result[result.length - 1];
    const currentPrice = prices[prices.length - 1];
    
    // Determinar señal
    let signal = 'NEUTRAL';
    if (currentPrice > lastValue) {
      signal = 'ALCISTA';
    } else if (currentPrice < lastValue) {
      signal = 'BAJISTA';
    }
    
    // Devolver resultado
    res.json({
      indicator: 'EMA',
      period,
      values: result,
      currentValue: lastValue,
      currentPrice,
      signal,
      interpretation: interpretEMA(currentPrice, lastValue, period)
    });
  } catch (error) {
    console.error('Error al calcular EMA:', error);
    res.status(500).json({
      error: 'Error al calcular EMA',
      message: error.message
    });
  }
}

/**
 * Calcula múltiples indicadores a la vez
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function calculateMultipleIndicators(req, res) {
  try {
    const {
      prices,
      indicators = ['rsi', 'macd', 'bollinger']
    } = req.body;
    
    // Validar datos de entrada
    if (!validatePrices(prices)) {
      return res.status(400).json({
        error: 'Datos de precios inválidos',
        message: 'Se requiere un array de precios numéricos'
      });
    }
    
    const result = {};
    
    // Calcular cada indicador solicitado
    if (indicators.includes('rsi')) {
      result.rsi = technicalService.calculateRSI(prices);
    }
    
    if (indicators.includes('macd')) {
      result.macd = technicalService.calculateMACD(prices);
    }
    
    if (indicators.includes('bollinger')) {
      result.bollinger = technicalService.calculateBollingerBands(prices);
    }
    
    if (indicators.includes('sma')) {
      result.sma = {
        sma20: technicalService.calculateSMA(prices, 20),
        sma50: technicalService.calculateSMA(prices, 50),
        sma200: technicalService.calculateSMA(prices, 200)
      };
    }
    
    if (indicators.includes('ema')) {
      result.ema = {
        ema12: technicalService.calculateEMA(prices, 12),
        ema26: technicalService.calculateEMA(prices, 26),
        ema50: technicalService.calculateEMA(prices, 50)
      };
    }
    
    // Devolver resultado
    res.json({
      indicators: result
    });
  } catch (error) {
    console.error('Error al calcular múltiples indicadores:', error);
    res.status(500).json({
      error: 'Error al calcular múltiples indicadores',
      message: error.message
    });
  }
}

/**
 * Detecta patrones de velas
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function detectCandlePatterns(req, res) {
  try {
    const { candles } = req.body;
    
    // Validar datos de entrada
    if (!validateCandles(candles)) {
      return res.status(400).json({
        error: 'Datos de velas inválidos',
        message: 'Se requiere un array de velas con propiedades open, high, low, close'
      });
    }
    
    // Detectar patrones
    const result = technicalService.detectCandlePatterns(candles);
    
    // Contar patrones detectados
    let bullishCount = 0;
    let bearishCount = 0;
    
    Object.values(result.bullish).forEach(values => {
      bullishCount += values[values.length - 1] ? 1 : 0;
    });
    
    Object.values(result.bearish).forEach(values => {
      bearishCount += values[values.length - 1] ? 1 : 0;
    });
    
    // Determinar tendencia
    let trend = 'NEUTRAL';
    if (bullishCount > bearishCount) {
      trend = 'ALCISTA';
    } else if (bearishCount > bullishCount) {
      trend = 'BAJISTA';
    }
    
    // Devolver resultado
    res.json({
      patterns: result,
      summary: {
        bullishPatterns: bullishCount,
        bearishPatterns: bearishCount,
        trend
      }
    });
  } catch (error) {
    console.error('Error al detectar patrones de velas:', error);
    res.status(500).json({
      error: 'Error al detectar patrones de velas',
      message: error.message
    });
  }
}

/**
 * Realiza un análisis técnico completo
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 */
async function performFullAnalysis(req, res) {
  try {
    const { candles } = req.body;
    
    // Validar datos de entrada
    if (!validateCandles(candles)) {
      return res.status(400).json({
        error: 'Datos de velas inválidos',
        message: 'Se requiere un array de velas con propiedades open, high, low, close'
      });
    }
    
    // Realizar análisis completo
    const result = technicalService.performFullAnalysis(candles);
    
    // Devolver resultado
    res.json(result);
  } catch (error) {
    console.error('Error al realizar análisis completo:', error);
    res.status(500).json({
      error: 'Error al realizar análisis completo',
      message: error.message
    });
  }
}

/**
 * Interpreta el valor del RSI
 * @param {number} value - Valor del RSI
 * @returns {string} - Interpretación
 */
function interpretRSI(value) {
  if (value < 30) {
    return 'Sobreventa. Posible señal de compra.';
  } else if (value > 70) {
    return 'Sobrecompra. Posible señal de venta.';
  } else if (value >= 40 && value <= 60) {
    return 'Zona neutral. Sin señal clara.';
  } else if (value > 60 && value <= 70) {
    return 'Acercándose a sobrecompra. Precaución.';
  } else {
    return 'Acercándose a sobreventa. Observar.';
  }
}

/**
 * Interpreta los valores del MACD
 * @param {Object} current - Valor actual del MACD
 * @param {Object} previous - Valor anterior del MACD
 * @returns {string} - Interpretación
 */
function interpretMACD(current, previous) {
  if (!current || !previous) {
    return 'Datos insuficientes para interpretación.';
  }
  
  if (current.MACD > current.signal && previous.MACD <= previous.signal) {
    return 'Cruce alcista. Señal de compra.';
  } else if (current.MACD < current.signal && previous.MACD >= previous.signal) {
    return 'Cruce bajista. Señal de venta.';
  } else if (current.MACD > current.signal) {
    return 'MACD por encima de la línea de señal. Tendencia alcista.';
  } else if (current.MACD < current.signal) {
    return 'MACD por debajo de la línea de señal. Tendencia bajista.';
  } else {
    return 'MACD y línea de señal alineados. Sin tendencia clara.';
  }
}

/**
 * Interpreta los valores de las Bandas de Bollinger
 * @param {number} price - Precio actual
 * @param {Object} bands - Valores de las bandas
 * @returns {string} - Interpretación
 */
function interpretBollingerBands(price, bands) {
  if (!bands) {
    return 'Datos insuficientes para interpretación.';
  }
  
  if (price >= bands.upper) {
    return 'Precio en la banda superior o por encima. Posible sobrecompra o fuerte tendencia alcista.';
  } else if (price <= bands.lower) {
    return 'Precio en la banda inferior o por debajo. Posible sobreventa o fuerte tendencia bajista.';
  } else if (price > bands.middle && price < bands.upper) {
    return 'Precio entre la banda media y superior. Tendencia alcista moderada.';
  } else if (price < bands.middle && price > bands.lower) {
    return 'Precio entre la banda media e inferior. Tendencia bajista moderada.';
  } else {
    return 'Precio cerca de la banda media. Volatilidad reducida o consolidación.';
  }
}

/**
 * Interpreta los valores de la SMA
 * @param {number} price - Precio actual
 * @param {number} sma - Valor de la SMA
 * @param {number} period - Período de la SMA
 * @returns {string} - Interpretación
 */
function interpretSMA(price, sma, period) {
  if (!sma) {
    return 'Datos insuficientes para interpretación.';
  }
  
  if (price > sma) {
    return `Precio por encima de la SMA ${period}. Tendencia alcista.`;
  } else if (price < sma) {
    return `Precio por debajo de la SMA ${period}. Tendencia bajista.`;
  } else {
    return `Precio en la SMA ${period}. Posible cambio de tendencia o consolidación.`;
  }
}

/**
 * Interpreta los valores de la EMA
 * @param {number} price - Precio actual
 * @param {number} ema - Valor de la EMA
 * @param {number} period - Período de la EMA
 * @returns {string} - Interpretación
 */
function interpretEMA(price, ema, period) {
  if (!ema) {
    return 'Datos insuficientes para interpretación.';
  }
  
  if (price > ema) {
    return `Precio por encima de la EMA ${period}. Tendencia alcista.`;
  } else if (price < ema) {
    return `Precio por debajo de la EMA ${period}. Tendencia bajista.`;
  } else {
    return `Precio en la EMA ${period}. Posible cambio de tendencia o consolidación.`;
  }
}

module.exports = {
  calculateRSI,
  calculateMACD,
  calculateBollingerBands,
  calculateSMA,
  calculateEMA,
  calculateMultipleIndicators,
  detectCandlePatterns,
  performFullAnalysis
};
