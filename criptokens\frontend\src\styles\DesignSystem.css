/* Sistema de diseño para Criptokens
   Este archivo define variables CSS globales para mantener consistencia */

:root {
  /* Paleta de colores */
  --color-background: #0f1528;
  --color-surface: rgba(23, 27, 54, 0.7);
  --color-surface-light: rgba(30, 35, 60, 0.7);
  --color-surface-dark: rgba(15, 20, 40, 0.8);
  
  /* Colores de acento */
  --color-primary: #4099ff;
  --color-primary-light: #73b4ff;
  --color-secondary: #8a2be2;
  --color-secondary-light: #9370db;
  
  /* Colores semánticos */
  --color-positive: #00e676;
  --color-negative: #ff3a6e;
  --color-warning: #ffab00;
  --color-info: #00e5ff;
  
  /* Colores de texto */
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);
  --text-disabled: rgba(255, 255, 255, 0.3);
  
  /* Espaciado - sistema de 8px */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Bordes y radios */
  --border-radius-sm: 6px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-width: 1px;
  --border-color: rgba(255, 255, 255, 0.1);
  --border-color-hover: rgba(255, 255, 255, 0.2);
  
  /* Sombras */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
  
  /* Tipografía */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-md: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  
  /* Pesos de fuente */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Transiciones */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index */
  --z-index-base: 1;
  --z-index-dropdown: 10;
  --z-index-sticky: 100;
  --z-index-modal: 1000;
}

/* Clases de utilidad */
.text-positive {
  color: var(--color-positive) !important;
}

.text-negative {
  color: var(--color-negative) !important;
}

.text-warning {
  color: var(--color-warning) !important;
}

.text-info {
  color: var(--color-info) !important;
}

.bg-surface {
  background-color: var(--color-surface) !important;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.card {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  border: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.card:hover {
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Mejoras para la visualización de datos */
.data-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.data-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-change-positive {
  color: var(--color-positive);
  font-weight: var(--font-weight-semibold);
}

.data-change-negative {
  color: var(--color-negative);
  font-weight: var(--font-weight-semibold);
}
