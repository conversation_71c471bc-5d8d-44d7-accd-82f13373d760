.radar-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  color: var(--text-color);
  position: relative;
}

.radar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.radar-header h2 {
  font-size: 24px;
  margin: 0;
  background: linear-gradient(90deg, #00c9ff, #92fe9d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.radar-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-container {
  position: relative;
  margin-right: 10px;
}

.search-input {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--text-color);
  padding: 8px 12px;
  width: 200px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #00c9ff;
  box-shadow: 0 0 0 2px rgba(0, 201, 255, 0.2);
  width: 250px;
}

.filter-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-tab {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: #aaa;
  padding: 6px 14px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
}

.filter-tab.active {
  background: linear-gradient(90deg, #00c9ff, #92fe9d);
  color: #000;
  font-weight: 600;
  border: none;
}

.refresh-button {
  background: linear-gradient(90deg, #00c9ff, #92fe9d);
  border: none;
  border-radius: 4px;
  color: #000;
  padding: 8px 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 201, 255, 0.3);
}

.refresh-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.radar-cryptos {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cryptos-table {
  width: 100%;
  border-collapse: collapse;
}

.cryptos-table th {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cryptos-table th:hover {
  background: rgba(0, 0, 0, 0.4);
}

.cryptos-table td {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cryptos-table tr:last-child td {
  border-bottom: none;
}

.cryptos-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

.crypto-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.favorite-button {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  color: #aaa;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 6px;
  margin-right: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.favorite-button:hover {
  color: #ffcc00;
  transform: scale(1.08);
  background: rgba(255, 204, 0, 0.08);
  border-color: rgba(255, 204, 0, 0.2);
  box-shadow: 0 2px 5px rgba(255, 204, 0, 0.2);
}

.favorite-button.active {
  color: #ffcc00;
  background: rgba(255, 204, 0, 0.1);
  border-color: rgba(255, 204, 0, 0.3);
  box-shadow: 0 1px 4px rgba(255, 204, 0, 0.25);
}

.category-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.category-defi {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  color: white;
}

.category-nft {
  background: linear-gradient(90deg, #ff3a6e, #ff7eb3);
  color: white;
}

.category-layer1 {
  background: linear-gradient(90deg, #00c9ff, #92fe9d);
  color: black;
}

.category-layer2 {
  background: linear-gradient(90deg, #00c9ff, #00e5ff);
  color: black;
}

.category-stablecoin {
  background: linear-gradient(90deg, #ffcc00, #ffeb3b);
  color: black;
}

.category-meme {
  background: linear-gradient(90deg, #ff9800, #ff5722);
  color: white;
}

.category-exchange {
  background: linear-gradient(90deg, #3f51b5, #2196f3);
  color: white;
}

.category-other {
  background: linear-gradient(90deg, #9e9e9e, #607d8b);
  color: white;
}

.crypto-icon {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  border-radius: 50%;
  vertical-align: middle !important;
  object-fit: contain !important;
}

.crypto-name {
  display: block;
  font-weight: 600;
}

.crypto-symbol {
  display: block;
  font-size: 12px;
  color: #aaa;
}

.positive {
  color: #00ff9d;
}

.negative {
  color: #ff3a6e;
}

.remove-crypto-button {
  background: rgba(255, 58, 110, 0.2);
  border: 1px solid #ff3a6e;
  border-radius: 4px;
  color: #ff3a6e;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.remove-crypto-button:hover {
  background: rgba(255, 58, 110, 0.4);
}

.empty-radar {
  text-align: center;
  padding: 40px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.empty-radar p {
  margin: 10px 0;
  color: #aaa;
}

.radar-loading {
  text-align: center;
  padding: 40px 20px;
  color: #aaa;
}

.radar-error {
  text-align: center;
  padding: 40px 20px;
  color: #ff3a6e;
  background: rgba(255, 58, 110, 0.1);
  border-radius: 8px;
}

.radar-not-logged-in {
  text-align: center;
  padding: 40px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.radar-not-logged-in h3 {
  margin-top: 0;
  color: #ff3a6e;
}

/* Notas y alertas */
.notes-editor, .alert-editor {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 10px;
  margin-top: 5px;
  opacity: 0;
  height: 0;
  overflow: hidden;
}

.notes-editor textarea {
  width: 100%;
  min-height: 80px;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--text-color);
  padding: 8px;
  resize: vertical;
}

.alert-editor input {
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--text-color);
  padding: 8px;
}

.notes-actions, .alert-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.notes-actions button, .alert-actions button {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: #ccc;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.notes-actions button:hover, .alert-actions button:hover {
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
}

.notes-display, .alert-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.note-content, .alert-content {
  font-size: 13px;
  color: #ccc;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: 5px;
}

.resources-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(67, 97, 238, 0.2);
  color: #4361ee;
  border-radius: 10px;
  padding: 2px 5px;
  font-size: 10px;
  margin-left: 5px;
}

.no-notes, .no-alert {
  font-size: 13px;
  color: #666;
  font-style: italic;
}

.edit-notes-button, .edit-alert-button {
  background: none;
  border: none;
  color: #00c9ff;
  font-size: 12px;
  cursor: pointer;
  padding: 0;
}

.edit-notes-button:hover, .edit-alert-button:hover {
  text-decoration: underline;
}

/* Modal de categorías */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.category-modal {
  background: rgba(20, 20, 30, 0.95);
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  opacity: 0;
  transform: scale(0.9);
}

.category-modal h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #fff;
  text-align: center;
}

.category-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.category-button {
  padding: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.category-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close-modal-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  color: #ccc;
  padding: 8px 16px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s;
}

.close-modal-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

/* Modales para notas y alertas */
.modal-content {
  background: rgba(20, 20, 30, 0.95);
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  margin: 0;
  color: #fff;
}

.modal-close {
  background: none;
  border: none;
  color: #ccc;
  font-size: 20px;
  cursor: pointer;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #fff;
}

.modal-body {
  margin-bottom: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal-footer button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: linear-gradient(90deg, #00c9ff, #92fe9d);
  border: none;
  color: #000;
  font-weight: 600;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 201, 255, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ccc;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #ccc;
}

.form-control {
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--text-color);
  padding: 10px;
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

.form-control:focus {
  outline: none;
  border-color: #00c9ff;
  box-shadow: 0 0 0 2px rgba(0, 201, 255, 0.2);
}

/* Botón de análisis con Gurú */
.analyze-with-guru {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 12px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin: 0 auto 20px auto;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(110, 66, 202, 0.3);
  width: 100%;
  max-width: 350px;
}

.analyze-with-guru::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}

.analyze-with-guru:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(110, 66, 202, 0.5);
}

.analyze-with-guru:hover::before {
  left: 100%;
}

.analyze-with-guru:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(110, 66, 202, 0.4);
}

.analyze-with-guru i {
  font-size: 20px;
  animation: pulse 2s infinite;
}

.filter-badge {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  margin-left: 8px;
  font-weight: normal;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.radar-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.crypto-row {
  transition: all 0.3s ease;
}

.crypto-row:hover {
  background: rgba(0, 201, 255, 0.05) !important;
}

/* Estilos para la página independiente */
.standalone-page {
  background-color: #0f1123;
  min-height: 100vh;
  color: var(--text-color);
  padding: 20px;
}

.standalone-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.standalone-header h1 {
  margin: 0;
  font-size: 28px;
  background: linear-gradient(90deg, #00c9ff, #92fe9d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #00c9ff;
  text-decoration: none;
  margin-right: 20px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.back-link:hover {
  color: #92fe9d;
  transform: translateX(-5px);
}

.standalone-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Responsive */
@media (max-width: 1200px) {
  .cryptos-table {
    font-size: 14px;
  }

  .category-options {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}

@media (max-width: 992px) {
  .cryptos-table th:nth-child(4),
  .cryptos-table td:nth-child(4),
  .cryptos-table th:nth-child(5),
  .cryptos-table td:nth-child(5) {
    display: none;
  }

  .filter-tabs {
    overflow-x: auto;
    padding-bottom: 15px;
    flex-wrap: nowrap;
  }

  .filter-tab {
    flex-shrink: 0;
  }
}

@media (max-width: 768px) {
  .radar-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .search-container {
    width: 100%;
    margin-bottom: 10px;
  }

  .search-input {
    width: 100%;
  }

  .search-input:focus {
    width: 100%;
  }

  .radar-actions {
    width: 100%;
    justify-content: space-between;
  }

  .cryptos-table th:nth-child(6),
  .cryptos-table td:nth-child(6),
  .cryptos-table th:nth-child(7),
  .cryptos-table td:nth-child(7) {
    display: none;
  }

  .category-modal {
    width: 95%;
    padding: 15px;
  }

  .category-options {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .cryptos-table th:nth-child(8),
  .cryptos-table td:nth-child(8),
  .cryptos-table th:nth-child(9),
  .cryptos-table td:nth-child(9) {
    display: none;
  }

  .crypto-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .favorite-button {
    position: relative;
    width: 28px;
    height: 28px;
    font-size: 16px;
    padding: 5px;
    margin-right: 6px;
  }

  .crypto-icon {
    width: 20px;
    height: 20px;
  }
}
