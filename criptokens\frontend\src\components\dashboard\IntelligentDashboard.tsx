import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/NewAuthContext';
import { usePortfolio } from '../../hooks/usePortfolio';
import { useRadarCripto } from '../../hooks/useRadarCripto';
import { useTopCryptocurrencies } from '../../hooks/useMcpClient';
import { getGlobalMarketData } from '../../services/api';
import CoinTable from '../CoinTable';
import MarketOverview from '../MarketOverview';
import PortfolioSummaryWidget from './widgets/PortfolioSummaryWidget';

import SmartInsightsPanel from './widgets/SmartInsightsPanel';
import RecommendedAssetsWidget from './widgets/RecommendedAssetsWidget';
import CryptoNewsWidget from './widgets/CryptoNewsWidget';
import MarketTrendsWidget from './widgets/MarketTrendsWidget';
import CryptoEventsWidget from './widgets/CryptoEventsWidget';
import CustomAlertsWidget from './widgets/CustomAlertsWidget';

import {
  DashboardState,
  UserPreferences,
  PortfolioStats,
  GlobalMarketData,
  SentimentAnalysis,
  AIInsight,
  RecommendedAsset,
  RiskProfile,
  AssetAllocation
} from '../../types/dashboard';
import { generateInsights } from '../../services/ai/insightGenerator';
import { generateRecommendations } from '../../services/ai/assetRecommender';
import { analyzeSentiment } from '../../services/ai/sentimentAnalyzer';
import '../../styles/dashboard/ThreeColumnDashboard.css';

const IntelligentDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [timeRange, setTimeRange] = useState<string>('24h');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showMobileFilters, setShowMobileFilters] = useState<boolean>(false);

  // Estado principal del dashboard
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    userPreferences: {
      favoriteAssets: [],
      preferredCategories: ['defi', 'layer1'],
      riskProfile: 'moderate',
      dashboardLayout: [
        'portfolio_summary',
        'smart_insights',
        'market_sentiment'
      ],
      theme: 'dark'
    },
    portfolioStats: {
      totalValue: 0,
      totalInvestment: 0,
      totalProfitLoss: 0,
      totalProfitLossPercentage: 0,
      assetCount: 0,
      lastUpdated: new Date(),
      assetAllocation: [],
      riskScore: 5
    },
    globalMarketData: null,
    sentimentAnalysis: null,
    insights: [],
    recommendedAssets: [],
    isLoading: {
      portfolio: true,
      market: true,
      insights: true,
      recommendations: true
    },
    errors: {
      portfolio: null,
      market: null,
      insights: null,
      recommendations: null
    }
  });

  // Hooks para obtener datos
  const { data: cryptos, isLoading: isCryptosLoading } = useTopCryptocurrencies();
  const {
    portfolio,
    portfolioStats: rawPortfolioStats,
    isLoading: isPortfolioLoading
  } = usePortfolio();
  const {
    radarWithPrices,
    isLoading: isRadarLoading
  } = useRadarCripto();

  // Cargar preferencias del usuario
  useEffect(() => {
    if (currentUser) {
      try {
        const savedPreferences = localStorage.getItem(`userPreferences_${currentUser.uid}`);
        if (savedPreferences) {
          const preferences = JSON.parse(savedPreferences) as UserPreferences;
          setDashboardState(prev => ({
            ...prev,
            userPreferences: preferences
          }));
        }
      } catch (error) {
        console.error('Error loading user preferences:', error);
      }
    }
  }, [currentUser]);

  // Guardar preferencias del usuario cuando cambien
  useEffect(() => {
    if (currentUser) {
      try {
        localStorage.setItem(
          `userPreferences_${currentUser.uid}`,
          JSON.stringify(dashboardState.userPreferences)
        );
      } catch (error) {
        console.error('Error saving user preferences:', error);
      }
    }
  }, [dashboardState.userPreferences, currentUser]);

  // Función para cargar todos los datos del dashboard
  const fetchDashboardData = useCallback(async () => {
    try {
      setDashboardState(prev => ({
        ...prev,
        isLoading: { ...prev.isLoading, market: true },
        errors: { ...prev.errors, market: null }
      }));

      const data = await getGlobalMarketData();

      setDashboardState(prev => ({
        ...prev,
        globalMarketData: data,
        isLoading: { ...prev.isLoading, market: false }
      }));
    } catch (error) {
      console.error('Error fetching global market data:', error);
      setDashboardState(prev => ({
        ...prev,
        isLoading: { ...prev.isLoading, market: false },
        errors: { ...prev.errors, market: 'Error al cargar datos del mercado' }
      }));
    }
  }, []);

  // Obtener datos globales del mercado
  useEffect(() => {
    fetchDashboardData();

    // Actualizar cada 5 minutos
    const intervalId = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(intervalId);
  }, [fetchDashboardData]);

  // Procesar datos del portafolio
  useEffect(() => {
    if (!isPortfolioLoading && portfolio) {
      try {
        setDashboardState(prev => ({
          ...prev,
          isLoading: { ...prev.isLoading, portfolio: true },
          errors: { ...prev.errors, portfolio: null }
        }));

        // Calcular asignación de activos por categoría
        const categoryMap = new Map<string, number>();
        portfolio.forEach(asset => {
          const category = asset.category || 'Otros';
          const currentValue = categoryMap.get(category) || 0;
          categoryMap.set(category, currentValue + asset.currentValue);
        });

        const totalValue = rawPortfolioStats.totalValue || 0;
        const assetAllocation: AssetAllocation[] = Array.from(categoryMap.entries())
          .map(([category, value]) => ({
            category,
            value,
            percentage: totalValue > 0 ? (value / totalValue) * 100 : 0
          }))
          .sort((a, b) => b.value - a.value);

        // Calcular puntuación de riesgo
        const riskScore = calculatePortfolioRiskScore(portfolio, assetAllocation);

        // Actualizar estadísticas del portafolio
        const enhancedPortfolioStats: PortfolioStats = {
          ...rawPortfolioStats,
          assetAllocation,
          riskScore,
          lastUpdated: new Date()
        };

        setDashboardState(prev => ({
          ...prev,
          portfolioStats: enhancedPortfolioStats,
          isLoading: { ...prev.isLoading, portfolio: false }
        }));
      } catch (error) {
        console.error('Error processing portfolio data:', error);
        setDashboardState(prev => ({
          ...prev,
          isLoading: { ...prev.isLoading, portfolio: false },
          errors: { ...prev.errors, portfolio: 'Error al procesar datos del portafolio' }
        }));
      }
    }
  }, [isPortfolioLoading, portfolio, rawPortfolioStats]);

  // Analizar sentimiento del mercado
  useEffect(() => {
    if (dashboardState.globalMarketData) {
      try {
        const sentiment = analyzeSentiment(dashboardState.globalMarketData);

        setDashboardState(prev => ({
          ...prev,
          sentimentAnalysis: sentiment
        }));
      } catch (error) {
        console.error('Error analyzing market sentiment:', error);
      }
    }
  }, [dashboardState.globalMarketData]);

  // Generar insights personalizados
  useEffect(() => {
    const generatePersonalizedInsights = () => {
      try {
        setDashboardState(prev => ({
          ...prev,
          isLoading: { ...prev.isLoading, insights: true }
        }));

        if (!dashboardState.globalMarketData || isPortfolioLoading || isRadarLoading) {
          return;
        }

        const insights = generateInsights(
          dashboardState.portfolioStats,
          dashboardState.globalMarketData,
          dashboardState.sentimentAnalysis,
          dashboardState.userPreferences.riskProfile,
          portfolio,
          radarWithPrices
        );

        setDashboardState(prev => ({
          ...prev,
          insights,
          isLoading: { ...prev.isLoading, insights: false }
        }));
      } catch (error) {
        console.error('Error generating insights:', error);
        setDashboardState(prev => ({
          ...prev,
          isLoading: { ...prev.isLoading, insights: false },
          errors: { ...prev.errors, insights: 'Error al generar insights' }
        }));
      }
    };

    generatePersonalizedInsights();
  }, [
    dashboardState.globalMarketData,
    dashboardState.portfolioStats,
    dashboardState.sentimentAnalysis,
    dashboardState.userPreferences.riskProfile,
    isPortfolioLoading,
    isRadarLoading,
    portfolio,
    radarWithPrices
  ]);

  // Generar recomendaciones de activos
  useEffect(() => {
    const generateAssetRecommendations = () => {
      try {
        setDashboardState(prev => ({
          ...prev,
          isLoading: { ...prev.isLoading, recommendations: true }
        }));

        if (!cryptos || isPortfolioLoading) {
          return;
        }

        const recommendations = generateRecommendations(
          cryptos,
          portfolio,
          dashboardState.userPreferences,
          dashboardState.sentimentAnalysis
        );

        setDashboardState(prev => ({
          ...prev,
          recommendedAssets: recommendations,
          isLoading: { ...prev.isLoading, recommendations: false }
        }));
      } catch (error) {
        console.error('Error generating recommendations:', error);
        setDashboardState(prev => ({
          ...prev,
          isLoading: { ...prev.isLoading, recommendations: false },
          errors: { ...prev.errors, recommendations: 'Error al generar recomendaciones' }
        }));
      }
    };

    generateAssetRecommendations();
  }, [
    cryptos,
    isPortfolioLoading,
    portfolio,
    dashboardState.userPreferences,
    dashboardState.sentimentAnalysis
  ]);

  // Filtrar criptomonedas según la categoría seleccionada
  const filteredCryptos = useMemo(() => {
    if (!cryptos) return [];

    if (selectedCategory === 'all') {
      return cryptos;
    }

    return cryptos.filter(crypto =>
      crypto.category?.toLowerCase() === selectedCategory.toLowerCase()
    );
  }, [cryptos, selectedCategory]);

  // Manejar la búsqueda
  const handleSearch = useCallback(() => {
    if (searchQuery.trim()) {
      // Buscar en criptomonedas
      const matchingCrypto = cryptos?.find(crypto =>
        crypto.name.toLowerCase() === searchQuery.toLowerCase() ||
        crypto.symbol.toLowerCase() === searchQuery.toLowerCase()
      );

      if (matchingCrypto) {
        navigate(`/coins/${matchingCrypto.id}`);
        return;
      }

      // Si no se encuentra, navegar a la página de búsqueda
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  }, [searchQuery, cryptos, navigate]);

  // Manejar clic en acción de insight
  const handleInsightAction = useCallback((path: string, insightId: string) => {
    // Aquí podrías registrar que el usuario hizo clic en un insight
    navigate(path);
  }, [navigate]);

  // Descartar un insight
  const handleDismissInsight = useCallback((insightId: string) => {
    setDashboardState(prev => ({
      ...prev,
      insights: prev.insights.filter(insight => insight.id !== insightId)
    }));
  }, []);

  // Manejar clic en activo recomendado
  const handleAssetClick = useCallback((assetId: string) => {
    navigate(`/coins/${assetId}`);
  }, [navigate]);

  return (
    <div className="dashboard-container" data-testid="intelligent-dashboard">
      {/* Header con estadísticas globales y búsqueda */}
      <div className="dashboard-header">
        <div className="global-stats">
          {currentUser && (
            <div className="welcome-message">
              <span>Bienvenido, {currentUser.displayName || 'Inversor'}</span>
            </div>
          )}
        </div>
      </div>

      {/* Botones de navegación centrales */}
      <div className="navigation-buttons">
        <a href="/portfolio" className="nav-button active">
          <i className="fas fa-wallet"></i>
          <span>Mi Portafolio</span>
        </a>
        <a href="/radar" className="nav-button active">
          <i className="fas fa-search"></i>
          <span>Mi Radar</span>
        </a>
        <a href="/news" className="nav-button active">
          <i className="fas fa-newspaper"></i>
          <span>Noticias</span>
        </a>
        <a href="/guru" className="nav-button active">
          <i className="fas fa-robot"></i>
          <span>Gurú Cripto</span>
        </a>
        <a href="/analysis" className="nav-button active">
          <i className="fas fa-chart-line"></i>
          <span>Análisis</span>
        </a>
        <a href="/academy" className="nav-button active">
          <i className="fas fa-graduation-cap"></i>
          <span>Academia</span>
        </a>
        <a href="/defi" className="nav-button active">
          <i className="fas fa-coins"></i>
          <span>Centro DeFi</span>
        </a>
        <a href="/technical" className="nav-button active">
          <i className="fas fa-chart-bar"></i>
          <span>Análisis Técnico</span>
        </a>
        <a href="/fundamental" className="nav-button active">
          <i className="fas fa-balance-scale"></i>
          <span>Análisis Fundamental</span>
        </a>
      </div>

      {/* Contenido principal con estructura inspirada en CoinGecko */}
      <div className="dashboard-content">
        {/* Sección Superior - Resumen Global (similar a CoinGecko) */}
        <div className="dashboard-global-summary">
          <div className="global-metrics">
            {dashboardState.globalMarketData && (
              <>
                <div className="metric-card">
                  <div className="metric-title">Capitalización Total</div>
                  <div className="metric-value">
                    ${dashboardState.globalMarketData.total_market_cap?.usd
                      ? (dashboardState.globalMarketData.total_market_cap.usd / 1000000000000).toFixed(2)
                      : '0'}T
                    <span className={`change ${(dashboardState.globalMarketData.market_cap_change_percentage_24h_usd || 0) >= 0 ? 'positive' : 'negative'}`}>
                      {(dashboardState.globalMarketData.market_cap_change_percentage_24h_usd || 0) >= 0 ? '+' : ''}
                      {(dashboardState.globalMarketData.market_cap_change_percentage_24h_usd || 0).toFixed(2)}%
                    </span>
                  </div>
                </div>
                <div className="metric-card">
                  <div className="metric-title">Volumen 24h</div>
                  <div className="metric-value">
                    ${dashboardState.globalMarketData.total_volume?.usd
                      ? (dashboardState.globalMarketData.total_volume.usd / 1000000000).toFixed(2)
                      : '0'}B
                  </div>
                </div>
                <div className="metric-card">
                  <div className="metric-title">Dominancia BTC</div>
                  <div className="metric-value">
                    {dashboardState.globalMarketData.market_cap_percentage?.btc
                      ? dashboardState.globalMarketData.market_cap_percentage.btc.toFixed(1)
                      : '0'}%
                  </div>
                </div>
                <div className="metric-card">
                  <div className="metric-title">Criptomonedas Activas</div>
                  <div className="metric-value">
                    {dashboardState.globalMarketData.active_cryptocurrencies || '0'}
                  </div>
                </div>
              </>
            )}
          </div>

          {currentUser && (
            <div className="portfolio-summary-card">
              <div className="card-title">
                <h3>Mi Portafolio</h3>
                <button
                  className="view-all-button"
                  onClick={() => navigate('/portfolio')}
                >
                  Ver Completo <i className="fas fa-arrow-right"></i>
                </button>
              </div>
              <div className="card-content">
                {dashboardState.isLoading.portfolio ? (
                  <div className="loading-indicator">
                    <i className="fas fa-spinner fa-spin"></i>
                  </div>
                ) : dashboardState.portfolioStats.assetCount > 0 ? (
                  <div className="portfolio-value">
                    <span className="value-label">Valor Total:</span>
                    <span className="value-amount">
                      ${dashboardState.portfolioStats.totalValue.toLocaleString()}
                    </span>
                    <span className={`value-change ${dashboardState.portfolioStats.totalProfitLoss >= 0 ? 'positive' : 'negative'}`}>
                      {dashboardState.portfolioStats.totalProfitLoss >= 0 ? '+' : ''}
                      {dashboardState.portfolioStats.totalProfitLossPercentage.toFixed(2)}%
                    </span>
                  </div>
                ) : (
                  <div className="empty-portfolio">
                    <span>No tienes activos en tu portafolio</span>
                    <div className="portfolio-actions">
                      <button onClick={() => navigate('/portfolio')}>Ver Portafolio</button>
                      <button onClick={() => navigate('/portfolio/add')}>Añadir Activos</button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Contenido Principal - Grid de 2 columnas (similar a CoinGecko) */}
        <div className="dashboard-main-content">
          {/* Columna Principal (70%) - Tabla de Criptomonedas como elemento dominante */}
          <div className="main-column">
            {/* Tabla de Criptomonedas - Elemento dominante como en CoinGecko */}
            <div className="dashboard-widget crypto-table-widget widget-lg">
              <div className="widget-header with-filters">
                <div className="header-main">
                  <h2>Criptomonedas por Capitalización de Mercado</h2>
                  <div className="widget-actions">
                    <button
                      className="widget-action-button"
                      onClick={() => fetchDashboardData()}
                      disabled={dashboardState.isLoading.market}
                      title="Actualizar datos"
                    >
                      <i className="fas fa-sync-alt"></i>
                    </button>
                    <button
                      className="widget-action-button"
                      onClick={() => navigate('/market')}
                      title="Ver todas las criptomonedas"
                    >
                      <i className="fas fa-external-link-alt"></i>
                    </button>
                  </div>
                </div>

                <div className="filter-controls">
                  <div className="time-filter">
                    <button
                      className={timeRange === '1h' ? 'active' : ''}
                      onClick={() => setTimeRange('1h')}
                      data-testid="time-filter-1h"
                    >
                      1h
                    </button>
                    <button
                      className={timeRange === '24h' ? 'active' : ''}
                      onClick={() => setTimeRange('24h')}
                      data-testid="time-filter-24h"
                    >
                      24h
                    </button>
                    <button
                      className={timeRange === '7d' ? 'active' : ''}
                      onClick={() => setTimeRange('7d')}
                      data-testid="time-filter-7d"
                    >
                      7d
                    </button>
                    <button
                      className={timeRange === '30d' ? 'active' : ''}
                      onClick={() => setTimeRange('30d')}
                      data-testid="time-filter-30d"
                    >
                      30d
                    </button>
                  </div>

                  <div className="category-filter">
                    <button
                      className={selectedCategory === 'all' ? 'active' : ''}
                      onClick={() => setSelectedCategory('all')}
                      data-testid="category-filter-all"
                    >
                      Todas
                    </button>
                    <button
                      className={selectedCategory === 'defi' ? 'active' : ''}
                      onClick={() => setSelectedCategory('defi')}
                      data-testid="category-filter-defi"
                    >
                      DeFi
                    </button>
                    <button
                      className={selectedCategory === 'nft' ? 'active' : ''}
                      onClick={() => setSelectedCategory('nft')}
                      data-testid="category-filter-nft"
                    >
                      NFT
                    </button>
                    <button
                      className={selectedCategory === 'layer1' ? 'active' : ''}
                      onClick={() => setSelectedCategory('layer1')}
                      data-testid="category-filter-layer1"
                    >
                      Layer 1
                    </button>
                  </div>
                </div>
              </div>

              <div className="widget-content">
                <div className="table-container">
                  <CoinTable
                    cryptos={filteredCryptos}
                    isLoading={isCryptosLoading}
                    timeRange={timeRange}
                    onSelectCrypto={(id) => navigate(`/coins/${id}`)}
                  />
                </div>
              </div>
            </div>

            {/* Sección de Noticias y Eventos (similar a CoinGecko) */}
            <div className="news-events-section">
              <div className="section-header">
                <h2>Noticias y Eventos</h2>
              </div>

              <div className="news-events-grid">
                {/* Noticias Destacadas */}
                <div className="dashboard-widget news-widget">
                  <div className="widget-header">
                    <h3>Noticias Destacadas</h3>
                    <div className="widget-actions">
                      <button
                        className="widget-action-button"
                        onClick={() => navigate('/news')}
                        title="Ver todas las noticias"
                      >
                        <i className="fas fa-external-link-alt"></i>
                      </button>
                    </div>
                  </div>
                  <div className="widget-content">
                    <CryptoNewsWidget
                      isLoading={dashboardState.isLoading.market}
                      compact={false}
                      maxItems={3}
                    />
                  </div>
                </div>

                {/* Próximos Eventos */}
                <div className="dashboard-widget events-widget">
                  <div className="widget-header">
                    <h3>Próximos Eventos</h3>
                    <div className="widget-actions">
                      <button
                        className="widget-action-button"
                        onClick={() => navigate('/events')}
                        title="Ver calendario completo"
                      >
                        <i className="fas fa-external-link-alt"></i>
                      </button>
                    </div>
                  </div>
                  <div className="widget-content">
                    <CryptoEventsWidget
                      isLoading={dashboardState.isLoading.market}
                      compact={false}
                      maxItems={3}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Columna Lateral (30%) - Widgets secundarios */}
          <div className="sidebar-column">
            {/* Widgets de IA agrupados */}
            <div className="ai-widgets-group">
              {/* Recomendaciones Personalizadas */}
              {currentUser && (
                <div className="dashboard-widget recommendations-widget">
                  <div className="widget-header">
                    <h3>Recomendaciones</h3>
                  </div>
                  <div className="widget-content">
                    <RecommendedAssetsWidget
                      assets={dashboardState.recommendedAssets}
                      isLoading={dashboardState.isLoading.recommendations}
                      onAssetClick={handleAssetClick}
                    />
                  </div>
                </div>
              )}

              {/* Insights Inteligentes */}
              <div className="dashboard-widget insights-widget">
                <div className="widget-header">
                  <h3>Insights Inteligentes</h3>
                </div>
                <div className="widget-content">
                  <SmartInsightsPanel
                    insights={dashboardState.insights}
                    isLoading={dashboardState.isLoading.insights}
                    onActionClick={handleInsightAction}
                    onDismiss={handleDismissInsight}
                  />
                </div>
              </div>
            </div>

            {/* Alertas Personalizadas */}
            {currentUser && (
              <div className="dashboard-widget alerts-widget">
                <div className="widget-header">
                  <h3>Mis Alertas</h3>
                  <div className="widget-actions">
                    <button
                      className="widget-action-button"
                      onClick={() => navigate('/alerts')}
                      title="Gestionar alertas"
                    >
                      <i className="fas fa-cog"></i>
                    </button>
                  </div>
                </div>
                <div className="widget-content">
                  <CustomAlertsWidget
                    isLoading={dashboardState.isLoading.market}
                    compact={true}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Funciones auxiliares
const formatNumber = (num: number): string => {
  if (num >= 1_000_000_000_000) {
    return `${(num / 1_000_000_000_000).toFixed(2)}T`;
  } else if (num >= 1_000_000_000) {
    return `${(num / 1_000_000_000).toFixed(2)}B`;
  } else if (num >= 1_000_000) {
    return `${(num / 1_000_000).toFixed(2)}M`;
  } else if (num >= 1_000) {
    return `${(num / 1_000).toFixed(2)}K`;
  }
  return num.toFixed(2);
};

// Función para formatear números grandes con separadores de miles
const formatLargeNumber = (num: number): string => {
  return new Intl.NumberFormat('es-ES', {
    maximumFractionDigits: 0
  }).format(num);
};

// La función getSentimentLabel ya está definida arriba

const calculatePortfolioRiskScore = (
  portfolio: any[],
  assetAllocation: AssetAllocation[]
): number => {
  if (portfolio.length === 0) return 5;

  // Factores de riesgo
  let riskScore = 5; // Puntuación base

  // 1. Diversificación - menos diversificación = más riesgo
  if (assetAllocation.length <= 2) {
    riskScore += 2;
  } else if (assetAllocation.length >= 5) {
    riskScore -= 1;
  }

  // 2. Concentración - si más del 50% está en una sola categoría = más riesgo
  const highestAllocation = assetAllocation[0]?.percentage || 0;
  if (highestAllocation > 70) {
    riskScore += 2;
  } else if (highestAllocation > 50) {
    riskScore += 1;
  } else if (highestAllocation < 30) {
    riskScore -= 1;
  }

  // 3. Capitalización de mercado - más activos de baja capitalización = más riesgo
  const lowCapCount = portfolio.filter(asset => asset.market_cap_rank > 50).length;
  const lowCapPercentage = lowCapCount / portfolio.length;

  if (lowCapPercentage > 0.5) {
    riskScore += 1;
  } else if (lowCapPercentage < 0.2) {
    riskScore -= 1;
  }

  // Limitar entre 1 y 10
  return Math.max(1, Math.min(10, riskScore));
};

export default IntelligentDashboard;
