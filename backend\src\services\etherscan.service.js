/**
 * Servicio para interactuar con la API de Etherscan a través del MCP
 */

const axios = require('axios');
const { ETHERSCAN_MCP_URL } = process.env;

/**
 * Clase para interactuar con la API de Etherscan a través del MCP
 */
class EtherscanService {
  constructor() {
    this.baseUrl = ETHERSCAN_MCP_URL || 'http://localhost:3103';
  }

  /**
   * Obtiene el balance de una dirección Ethereum
   * @param {string} address - Dirección Ethereum
   * @returns {Promise<Object>} - Respuesta con el balance
   */
  async getAddressBalance(address) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getAddressBalance',
        address
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el balance de la dirección:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el balance de la dirección',
        data: null
      };
    }
  }

  /**
   * Obtiene las transacciones de una dirección Ethereum
   * @param {string} address - Dirección Ethereum
   * @returns {Promise<Object>} - Respuesta con las transacciones
   */
  async getAddressTransactions(address) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getAddressTransactions',
        address
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener las transacciones de la dirección:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener las transacciones de la dirección',
        data: null
      };
    }
  }

  /**
   * Obtiene el precio actual de Ethereum
   * @returns {Promise<Object>} - Respuesta con el precio
   */
  async getEthereumPrice() {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getEthereumPrice'
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el precio de Ethereum:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el precio de Ethereum',
        data: null
      };
    }
  }

  /**
   * Obtiene información sobre un token ERC-20
   * @param {string} contractAddress - Dirección del contrato del token
   * @returns {Promise<Object>} - Respuesta con la información del token
   */
  async getTokenInfo(contractAddress) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getTokenInfo',
        contractAddress
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener información del token:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener información del token',
        data: null
      };
    }
  }

  /**
   * Obtiene el balance de un token ERC-20 para una dirección
   * @param {string} address - Dirección Ethereum
   * @param {string} contractAddress - Dirección del contrato del token
   * @returns {Promise<Object>} - Respuesta con el balance del token
   */
  async getTokenBalance(address, contractAddress) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getTokenBalance',
        address,
        contractAddress
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el balance del token:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el balance del token',
        data: null
      };
    }
  }

  /**
   * Obtiene el precio actual de ETH
   * @returns {Promise<Object>} - Información del precio
   */
  async getEthPrice() {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getEthPrice'
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el precio de ETH:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el precio de ETH',
        data: null
      };
    }
  }

  /**
   * Obtiene estadísticas de Ethereum
   * @returns {Promise<Object>} - Estadísticas de la red
   */
  async getEthStats() {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getEthStats'
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener estadísticas de ETH:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener estadísticas de ETH',
        data: null
      };
    }
  }

  /**
   * Obtiene información sobre los precios de gas
   * @returns {Promise<Object>} - Información de gas
   */
  async getGasOracle() {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getGasOracle'
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener información de gas:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener información de gas',
        data: null
      };
    }
  }

  /**
   * Obtiene el suministro total de un token
   * @param {string} contractAddress - Dirección del contrato del token
   * @returns {Promise<Object>} - Información del suministro
   */
  async getTokenSupply(contractAddress) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getTokenSupply',
        contractAddress
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el suministro del token:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el suministro del token',
        data: null
      };
    }
  }

  /**
   * Obtiene las transferencias ERC-20 de una dirección
   * @param {string} address - Dirección Ethereum
   * @param {string} contractAddress - Dirección del contrato (opcional)
   * @param {number} page - Número de página
   * @param {number} offset - Resultados por página
   * @returns {Promise<Object>} - Transferencias ERC-20
   */
  async getAddressERC20Transfers(address, contractAddress = null, page = 1, offset = 10) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getAddressERC20Transfers',
        address,
        contractAddress,
        page,
        offset
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener transferencias ERC-20:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener transferencias ERC-20',
        data: null
      };
    }
  }
}

module.exports = new EtherscanService();
