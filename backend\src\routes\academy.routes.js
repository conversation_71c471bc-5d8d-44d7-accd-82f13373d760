const express = require('express');
const router = express.Router();
const academyService = require('../services/academy.service');
const { authenticateUser } = require('../middleware/auth');

/**
 * @route GET /api/academy/courses
 * @desc Obtiene todos los cursos disponibles
 * @access Public
 */
router.get('/courses', async (req, res) => {
  try {
    const courses = await academyService.getAllCourses();
    res.json({ success: true, data: courses });
  } catch (error) {
    console.error('Error al obtener cursos:', error);
    res.status(500).json({ success: false, message: 'Error al obtener cursos' });
  }
});

/**
 * @route GET /api/academy/courses/:courseId
 * @desc Obtiene un curso por su ID
 * @access Public
 */
router.get('/courses/:courseId', async (req, res) => {
  try {
    const { courseId } = req.params;
    const course = await academyService.getCourseById(courseId);

    if (!course) {
      return res.status(404).json({ success: false, message: 'Curso no encontrado' });
    }

    res.json({ success: true, data: course });
  } catch (error) {
    console.error(`Error al obtener curso ${req.params.courseId}:`, error);
    res.status(500).json({ success: false, message: 'Error al obtener curso' });
  }
});

/**
 * @route GET /api/academy/progress/:courseId
 * @desc Obtiene el progreso de un usuario en un curso
 * @access Private
 */
router.get('/progress/:courseId', authenticateUser, async (req, res) => {
  try {
    const { courseId } = req.params;
    const userId = req.user.uid;

    const progress = await academyService.getUserCourseProgress(userId, courseId);
    res.json({ success: true, data: progress });
  } catch (error) {
    console.error(`Error al obtener progreso:`, error);
    res.status(500).json({ success: false, message: 'Error al obtener progreso' });
  }
});

/**
 * @route POST /api/academy/progress/:courseId/lesson/:lessonId
 * @desc Marca una lección como completada
 * @access Private
 */
router.post('/progress/:courseId/lesson/:lessonId', authenticateUser, async (req, res) => {
  try {
    const { courseId, lessonId } = req.params;
    const userId = req.user.uid;

    const success = await academyService.markLessonAsCompleted(userId, courseId, lessonId);

    if (!success) {
      return res.status(400).json({ success: false, message: 'Error al marcar lección como completada' });
    }

    res.json({ success: true, message: 'Lección marcada como completada' });
  } catch (error) {
    console.error(`Error al marcar lección como completada:`, error);
    res.status(500).json({ success: false, message: 'Error al marcar lección como completada' });
  }
});

/**
 * @route POST /api/academy/progress/:courseId/quiz/:moduleId
 * @desc Guarda la puntuación de un quiz
 * @access Private
 */
router.post('/progress/:courseId/quiz/:moduleId', authenticateUser, async (req, res) => {
  try {
    const { courseId, moduleId } = req.params;
    const { score } = req.body;
    const userId = req.user.uid;

    if (typeof score !== 'number' || score < 0 || score > 100) {
      return res.status(400).json({ success: false, message: 'Puntuación inválida' });
    }

    const success = await academyService.saveQuizScore(userId, courseId, moduleId, score);

    if (!success) {
      return res.status(400).json({ success: false, message: 'Error al guardar puntuación' });
    }

    res.json({ success: true, message: 'Puntuación guardada correctamente' });
  } catch (error) {
    console.error(`Error al guardar puntuación de quiz:`, error);
    res.status(500).json({ success: false, message: 'Error al guardar puntuación' });
  }
});

/**
 * @route GET /api/academy/certificates
 * @desc Obtiene los certificados de un usuario
 * @access Private
 */
router.get('/certificates', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.uid;
    const certificates = await academyService.getUserCertificates(userId);

    res.json({ success: true, data: certificates });
  } catch (error) {
    console.error(`Error al obtener certificados:`, error);
    res.status(500).json({ success: false, message: 'Error al obtener certificados' });
  }
});

/**
 * @route GET /api/academy/enrolled
 * @desc Obtiene los cursos en los que un usuario está inscrito
 * @access Private
 */
router.get('/enrolled', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.uid;
    const courses = await academyService.getUserEnrolledCourses(userId);

    res.json({ success: true, data: courses });
  } catch (error) {
    console.error(`Error al obtener cursos inscritos:`, error);
    res.status(500).json({ success: false, message: 'Error al obtener cursos inscritos' });
  }
});

/**
 * @route POST /api/academy/enroll/:courseId
 * @desc Inscribe a un usuario en un curso
 * @access Private
 */
router.post('/enroll/:courseId', authenticateUser, async (req, res) => {
  try {
    const { courseId } = req.params;
    const userId = req.user.uid;

    const success = await academyService.enrollUserInCourse(userId, courseId);

    if (!success) {
      return res.status(400).json({ success: false, message: 'Error al inscribir en el curso' });
    }

    res.json({ success: true, message: 'Inscripción exitosa' });
  } catch (error) {
    console.error(`Error al inscribir en curso:`, error);
    res.status(500).json({ success: false, message: 'Error al inscribir en el curso' });
  }
});

module.exports = router;
