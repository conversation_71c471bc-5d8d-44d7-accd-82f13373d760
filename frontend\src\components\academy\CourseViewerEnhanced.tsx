import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/NewAuthContext';
import '../../styles/academy/CourseViewer.css';
import ReactMarkdown from 'react-markdown';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
}

interface Quiz {
  id: string;
  title: string;
  description: string;
  passingScore: number;
  questions: Question[];
}

interface Lesson {
  id: string;
  title: string;
  content: string;
  videoUrl?: string;
  duration: number;
  order: number;
}

interface Module {
  id: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  quiz?: Quiz;
}

interface Resource {
  id: string;
  title: string;
  type: string;
  url: string;
  description: string;
}

interface Instructor {
  name: string;
  bio: string;
  avatar: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
  duration: string;
  modules: Module[];
  resources: Resource[];
  instructor: Instructor;
}

interface Progress {
  completedLessons: string[];
  quizScores: Record<string, number>;
  percentComplete: number;
  lastAccessed: string;
}

const CourseViewerEnhanced: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  const [course, setCourse] = useState<Course | null>(null);
  const [progress, setProgress] = useState<Progress | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeModule, setActiveModule] = useState<string>('');
  const [activeLesson, setActiveLesson] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'content' | 'resources' | 'discussion'>('content');
  const [quizMode, setQuizMode] = useState<boolean>(false);
  const [quizAnswers, setQuizAnswers] = useState<Record<string, number>>({});
  const [quizSubmitted, setQuizSubmitted] = useState<boolean>(false);
  const [quizScore, setQuizScore] = useState<number>(0);
  const [showSidebar, setShowSidebar] = useState<boolean>(true);

  useEffect(() => {
    if (!courseId) return;
    
    const loadCourse = async () => {
      try {
        setLoading(true);
        
        // En una implementación real, esto sería una llamada a la API
        // Por ahora, simulamos con datos de ejemplo
        const mockCourse: Course = {
          id: 'crypto-fundamentals',
          title: 'Fundamentos de Criptomonedas',
          description: 'Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.',
          level: 'beginner',
          duration: '4 horas',
          instructor: {
            name: 'Alex Rodríguez',
            bio: 'Experto en criptomonedas con más de 5 años de experiencia en el sector. Fundador de CryptoEdu y asesor de múltiples proyectos blockchain.',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
          },
          modules: [
            {
              id: 'module-1',
              title: '¿Qué son las Criptomonedas?',
              description: 'Introducción al concepto de criptomonedas y su lugar en el sistema financiero global.',
              order: 1,
              lessons: [
                {
                  id: 'lesson-1-1',
                  title: 'Definición y Características',
                  content: '# Definición y Características de las Criptomonedas\n\nLas criptomonedas son activos digitales diseñados para funcionar como medio de intercambio que utiliza criptografía para asegurar las transacciones, controlar la creación de unidades adicionales y verificar la transferencia de activos.\n\n## Características principales\n\n- **Descentralización**: No están controladas por ningún banco central o gobierno\n- **Seguridad**: Utilizan criptografía avanzada para proteger las transacciones\n- **Transparencia**: Todas las transacciones son públicas y verificables\n- **Limitación**: Muchas tienen un suministro limitado (como Bitcoin con 21 millones)\n- **Pseudoanonimato**: Las transacciones no están vinculadas directamente a identidades reales',
                  duration: 15,
                  order: 1
                },
                {
                  id: 'lesson-1-2',
                  title: 'Historia de las Criptomonedas',
                  content: '# Historia de las Criptomonedas\n\n## Orígenes y Desarrollo\n\nLa historia de las criptomonedas comienza mucho antes de Bitcoin, con los primeros conceptos de dinero digital y criptografía aplicada a las finanzas.\n\n### Precursores (1980s-1990s)\n\n- **DigiCash (1989)**: Creada por David Chaum, fue uno de los primeros intentos de crear dinero electrónico usando criptografía\n- **B-Money (1998)**: Propuesta por Wei Dai, introdujo la idea de crear dinero mediante la resolución de problemas computacionales\n- **Bit Gold (1998)**: Diseñado por Nick Szabo, considerado el precursor directo de Bitcoin',
                  duration: 20,
                  order: 2
                }
              ],
              quiz: {
                id: 'quiz-module-1',
                title: 'Evaluación: Introducción a las Criptomonedas',
                description: 'Comprueba tu comprensión de los conceptos básicos de las criptomonedas',
                passingScore: 70,
                questions: [
                  {
                    id: 'q1-m1',
                    question: '¿Cuál de las siguientes NO es una característica de las criptomonedas?',
                    options: [
                      'Descentralización',
                      'Respaldo gubernamental',
                      'Uso de criptografía',
                      'Transparencia de transacciones'
                    ],
                    correctAnswer: 1,
                    explanation: 'Las criptomonedas no están respaldadas por gobiernos o bancos centrales. De hecho, la descentralización y la independencia de autoridades centrales es una de sus características fundamentales.'
                  },
                  {
                    id: 'q2-m1',
                    question: '¿Quién creó Bitcoin?',
                    options: [
                      'Vitalik Buterin',
                      'Satoshi Nakamoto',
                      'Charlie Lee',
                      'Elon Musk'
                    ],
                    correctAnswer: 1,
                    explanation: 'Bitcoin fue creado por una persona o grupo bajo el seudónimo de Satoshi Nakamoto, cuya identidad real sigue siendo desconocida.'
                  }
                ]
              }
            },
            {
              id: 'module-2',
              title: 'Blockchain: La Tecnología Detrás',
              description: 'Comprende los fundamentos de la tecnología blockchain y cómo funciona para hacer posibles las criptomonedas.',
              order: 2,
              lessons: [
                {
                  id: 'lesson-2-1',
                  title: '¿Qué es Blockchain?',
                  content: '# ¿Qué es Blockchain?\n\nLa blockchain (cadena de bloques) es una tecnología de registro distribuido que permite almacenar información de manera segura, transparente e inmutable.\n\n## Definición\n\nUna blockchain es esencialmente una base de datos distribuida que mantiene una lista creciente de registros (bloques) que están enlazados y asegurados mediante criptografía. Cada bloque contiene un hash criptográfico del bloque anterior, una marca de tiempo y datos de transacciones.',
                  duration: 20,
                  order: 1
                }
              ],
              quiz: {
                id: 'quiz-module-2',
                title: 'Evaluación: Blockchain',
                description: 'Comprueba tu comprensión de la tecnología blockchain',
                passingScore: 70,
                questions: [
                  {
                    id: 'q1-m2',
                    question: '¿Qué característica NO es propia de una blockchain?',
                    options: [
                      'Inmutabilidad',
                      'Centralización',
                      'Transparencia',
                      'Seguridad criptográfica'
                    ],
                    correctAnswer: 1,
                    explanation: 'La centralización es lo opuesto a una de las características fundamentales de blockchain, que es la descentralización. Las blockchains están diseñadas para operar sin una autoridad central.'
                  }
                ]
              }
            }
          ],
          resources: [
            {
              id: 'resource-1',
              title: 'Whitepaper de Bitcoin',
              type: 'document',
              url: 'https://bitcoin.org/bitcoin.pdf',
              description: 'El documento original escrito por Satoshi Nakamoto que describe el funcionamiento de Bitcoin.'
            },
            {
              id: 'resource-2',
              title: 'Glosario de Términos Cripto',
              type: 'article',
              url: 'https://academy.binance.com/es/glossary',
              description: 'Glosario completo de términos relacionados con criptomonedas y blockchain.'
            }
          ]
        };
        
        setCourse(mockCourse);
        
        // Simular progreso del usuario
        const mockProgress: Progress = {
          completedLessons: ['lesson-1-1'],
          quizScores: {},
          percentComplete: 15,
          lastAccessed: new Date().toISOString()
        };
        
        setProgress(mockProgress);
        
        // Establecer el primer módulo y lección como activos por defecto
        if (mockCourse.modules && mockCourse.modules.length > 0) {
          setActiveModule(mockCourse.modules[0].id);
          
          if (mockCourse.modules[0].lessons && mockCourse.modules[0].lessons.length > 0) {
            setActiveLesson(mockCourse.modules[0].lessons[0].id);
          }
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Error loading course:', error);
        setLoading(false);
      }
    };
    
    loadCourse();
  }, [courseId]);

  const handleModuleClick = (moduleId: string) => {
    setActiveModule(moduleId);
    const module = course?.modules.find(m => m.id === moduleId);
    if (module && module.lessons && module.lessons.length > 0) {
      setActiveLesson(module.lessons[0].id);
    }
    setQuizMode(false);
    setQuizSubmitted(false);
  };

  const handleLessonClick = (lessonId: string) => {
    setActiveLesson(lessonId);
    setQuizMode(false);
    
    // Marcar la lección como completada
    if (progress && !progress.completedLessons.includes(lessonId)) {
      const updatedProgress = {
        ...progress,
        completedLessons: [...progress.completedLessons, lessonId],
        lastAccessed: new Date().toISOString()
      };
      
      // Calcular nuevo porcentaje de progreso
      if (course) {
        let totalLessons = 0;
        course.modules.forEach(module => {
          totalLessons += module.lessons.length;
        });
        
        updatedProgress.percentComplete = Math.round((updatedProgress.completedLessons.length / totalLessons) * 100);
      }
      
      setProgress(updatedProgress);
      
      // En una implementación real, aquí se enviaría el progreso al backend
    }
  };

  const handleQuizClick = () => {
    setQuizMode(true);
    setQuizSubmitted(false);
    setQuizAnswers({});
  };

  const handleQuizAnswerChange = (questionId: string, answerIndex: number) => {
    setQuizAnswers(prev => ({
      ...prev,
      [questionId]: answerIndex
    }));
  };

  const handleQuizSubmit = () => {
    const currentModule = course?.modules.find(m => m.id === activeModule);
    if (!currentModule || !currentModule.quiz) return;
    
    // Calcular puntuación
    let correctAnswers = 0;
    currentModule.quiz.questions.forEach(question => {
      if (quizAnswers[question.id] === question.correctAnswer) {
        correctAnswers++;
      }
    });
    
    const score = Math.round((correctAnswers / currentModule.quiz.questions.length) * 100);
    setQuizScore(score);
    setQuizSubmitted(true);
    
    // Guardar puntuación en el progreso
    if (progress) {
      const updatedProgress = {
        ...progress,
        quizScores: {
          ...progress.quizScores,
          [currentModule.id]: score
        },
        lastAccessed: new Date().toISOString()
      };
      
      setProgress(updatedProgress);
      
      // En una implementación real, aquí se enviaría el progreso al backend
    }
  };

  const getCurrentLesson = () => {
    const currentModule = course?.modules.find(m => m.id === activeModule);
    if (!currentModule) return null;
    
    return currentModule.lessons.find(l => l.id === activeLesson);
  };

  const isLessonCompleted = (lessonId: string) => {
    return progress?.completedLessons.includes(lessonId) || false;
  };

  const getQuizScore = (moduleId: string) => {
    return progress?.quizScores[moduleId] || 0;
  };

  const isQuizPassed = (moduleId: string) => {
    const module = course?.modules.find(m => m.id === moduleId);
    if (!module || !module.quiz) return false;
    
    const score = getQuizScore(moduleId);
    return score >= module.quiz.passingScore;
  };

  const renderQuiz = () => {
    const currentModule = course?.modules.find(m => m.id === activeModule);
    if (!currentModule || !currentModule.quiz) return null;
    
    const { quiz } = currentModule;
    
    return (
      <div className="quiz-container">
        <h2>{quiz.title}</h2>
        <p className="quiz-description">{quiz.description}</p>
        <p className="quiz-instructions">Puntuación para aprobar: {quiz.passingScore}%</p>
        
        {quizSubmitted ? (
          <div className="quiz-results">
            <div className={`quiz-score ${quizScore >= quiz.passingScore ? 'passed' : 'failed'}`}>
              <h3>Tu puntuación: {quizScore}%</h3>
              <p>{quizScore >= quiz.passingScore ? '¡Felicidades! Has aprobado el quiz.' : 'No has alcanzado la puntuación mínima. Intenta de nuevo.'}</p>
            </div>
            
            <div className="quiz-answers-review">
              <h3>Revisión de respuestas</h3>
              {quiz.questions.map((question, index) => (
                <div 
                  key={question.id} 
                  className={`quiz-question-review ${quizAnswers[question.id] === question.correctAnswer ? 'correct' : 'incorrect'}`}
                >
                  <h4>Pregunta {index + 1}: {question.question}</h4>
                  <p>Tu respuesta: {question.options[quizAnswers[question.id]]}</p>
                  <p>Respuesta correcta: {question.options[question.correctAnswer]}</p>
                  <p className="explanation">{question.explanation}</p>
                </div>
              ))}
            </div>
            
            <div className="quiz-actions">
              <button 
                className="btn-secondary" 
                onClick={() => setQuizSubmitted(false)}
              >
                Intentar de nuevo
              </button>
              <button 
                className="btn-primary" 
                onClick={() => setQuizMode(false)}
              >
                Volver al contenido
              </button>
            </div>
          </div>
        ) : (
          <div className="quiz-questions">
            {quiz.questions.map((question, index) => (
              <div key={question.id} className="quiz-question">
                <h3>Pregunta {index + 1}: {question.question}</h3>
                <div className="quiz-options">
                  {question.options.map((option, optionIndex) => (
                    <label key={optionIndex} className="quiz-option">
                      <input 
                        type="radio" 
                        name={question.id} 
                        value={optionIndex} 
                        checked={quizAnswers[question.id] === optionIndex}
                        onChange={() => handleQuizAnswerChange(question.id, optionIndex)}
                      />
                      <span>{option}</span>
                    </label>
                  ))}
                </div>
              </div>
            ))}
            
            <button 
              className="btn-primary submit-quiz" 
              onClick={handleQuizSubmit}
              disabled={Object.keys(quizAnswers).length < quiz.questions.length}
            >
              Enviar respuestas
            </button>
          </div>
        )}
      </div>
    );
  };

  const renderResources = () => {
    if (!course || !course.resources || course.resources.length === 0) {
      return <p>No hay recursos disponibles para este curso.</p>;
    }
    
    return (
      <div className="resources-container">
        <h2>Recursos del Curso</h2>
        <div className="resources-list">
          {course.resources.map(resource => (
            <div key={resource.id} className="resource-card">
              <div className={`resource-icon ${resource.type}`}>
                {resource.type === 'document' && <i className="fas fa-file-pdf"></i>}
                {resource.type === 'article' && <i className="fas fa-newspaper"></i>}
                {resource.type === 'video' && <i className="fas fa-video"></i>}
                {resource.type === 'tool' && <i className="fas fa-tools"></i>}
                {resource.type === 'link' && <i className="fas fa-link"></i>}
              </div>
              <div className="resource-content">
                <h3>{resource.title}</h3>
                <p>{resource.description}</p>
                <a 
                  href={resource.url} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="resource-link"
                >
                  Acceder al recurso <i className="fas fa-external-link-alt"></i>
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderDiscussion = () => {
    return (
      <div className="discussion-container">
        <h2>Discusión del Curso</h2>
        <div className="discussion-placeholder">
          <p>La sección de discusión estará disponible próximamente.</p>
          <p>Aquí podrás interactuar con otros estudiantes y el instructor del curso.</p>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="course-viewer loading">
        <div className="loading-spinner"></div>
        <p>Cargando curso...</p>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="course-viewer error">
        <h2>Error al cargar el curso</h2>
        <p>No se pudo encontrar el curso solicitado.</p>
        <button 
          className="btn-primary" 
          onClick={() => navigate('/academy')}
        >
          Volver a la Academia
        </button>
      </div>
    );
  }

  const currentLesson = getCurrentLesson();

  return (
    <div className={`course-viewer ${showSidebar ? '' : 'sidebar-hidden'}`}>
      <div className="course-header">
        <button 
          className="back-button" 
          onClick={() => navigate('/academy')}
        >
          <i className="fas fa-arrow-left"></i> Volver a la Academia
        </button>
        <h1>{course.title}</h1>
        <div className="course-meta-info">
          <span className={`course-level ${course.level}`}>
            {course.level === 'beginner' && 'Principiante'}
            {course.level === 'intermediate' && 'Intermedio'}
            {course.level === 'advanced' && 'Avanzado'}
          </span>
          <span className="course-duration">
            <i className="fas fa-clock"></i> {course.duration}
          </span>
        </div>
      </div>
      
      <div className="course-container">
        <button 
          className="toggle-sidebar" 
          onClick={() => setShowSidebar(!showSidebar)}
          title={showSidebar ? 'Ocultar menú' : 'Mostrar menú'}
        >
          <i className={`fas fa-chevron-${showSidebar ? 'left' : 'right'}`}></i>
        </button>
        
        <div className="course-sidebar">
          <div className="course-progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${progress?.percentComplete || 0}%` }}
            ></div>
            <span className="progress-text">{progress?.percentComplete || 0}% completado</span>
          </div>
          
          <div className="modules-list">
            {course.modules.map(module => (
              <div key={module.id} className="module-item">
                <div 
                  className={`module-header ${activeModule === module.id ? 'active' : ''}`}
                  onClick={() => handleModuleClick(module.id)}
                >
                  <h3>{module.title}</h3>
                  <div className="module-status">
                    {module.quiz && isQuizPassed(module.id) && (
                      <span className="quiz-passed" title="Quiz aprobado">
                        <i className="fas fa-check-circle"></i>
                      </span>
                    )}
                    <i className={`fas fa-chevron-${activeModule === module.id ? 'down' : 'right'}`}></i>
                  </div>
                </div>
                
                {activeModule === module.id && (
                  <div className="lessons-list">
                    {module.lessons.map(lesson => (
                      <div 
                        key={lesson.id} 
                        className={`lesson-item ${activeLesson === lesson.id ? 'active' : ''} ${isLessonCompleted(lesson.id) ? 'completed' : ''}`}
                        onClick={() => handleLessonClick(lesson.id)}
                      >
                        <span className="lesson-title">{lesson.title}</span>
                        <span className="lesson-duration">{lesson.duration} min</span>
                        {isLessonCompleted(lesson.id) && (
                          <span className="lesson-completed-icon">
                            <i className="fas fa-check"></i>
                          </span>
                        )}
                      </div>
                    ))}
                    
                    {module.quiz && (
                      <div 
                        className={`quiz-item ${quizMode && activeModule === module.id ? 'active' : ''} ${isQuizPassed(module.id) ? 'passed' : ''}`}
                        onClick={handleQuizClick}
                      >
                        <span className="quiz-title">
                          <i className="fas fa-question-circle"></i> {module.quiz.title}
                        </span>
                        {isQuizPassed(module.id) && (
                          <span className="quiz-score-badge">
                            {getQuizScore(module.id)}%
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        <div className="course-content">
          <div className="content-tabs">
            <button 
              className={`tab-button ${activeTab === 'content' ? 'active' : ''}`}
              onClick={() => setActiveTab('content')}
            >
              Contenido
            </button>
            <button 
              className={`tab-button ${activeTab === 'resources' ? 'active' : ''}`}
              onClick={() => setActiveTab('resources')}
            >
              Recursos
            </button>
            <button 
              className={`tab-button ${activeTab === 'discussion' ? 'active' : ''}`}
              onClick={() => setActiveTab('discussion')}
            >
              Discusión
            </button>
          </div>
          
          <div className="tab-content">
            {activeTab === 'content' && (
              quizMode ? (
                renderQuiz()
              ) : (
                currentLesson ? (
                  <div className="lesson-content">
                    <h2>{currentLesson.title}</h2>
                    {currentLesson.videoUrl && (
                      <div className="lesson-video">
                        <iframe 
                          src={currentLesson.videoUrl} 
                          title={currentLesson.title}
                          allowFullScreen
                        ></iframe>
                      </div>
                    )}
                    <div className="lesson-text">
                      <ReactMarkdown>{currentLesson.content}</ReactMarkdown>
                    </div>
                    
                    <div className="lesson-navigation">
                      <button 
                        className="btn-secondary prev-lesson"
                        onClick={() => {
                          const currentModule = course.modules.find(m => m.id === activeModule);
                          if (!currentModule) return;
                          
                          const currentLessonIndex = currentModule.lessons.findIndex(l => l.id === activeLesson);
                          if (currentLessonIndex > 0) {
                            handleLessonClick(currentModule.lessons[currentLessonIndex - 1].id);
                          } else {
                            // Si es la primera lección del módulo, ir al módulo anterior
                            const currentModuleIndex = course.modules.findIndex(m => m.id === activeModule);
                            if (currentModuleIndex > 0) {
                              const prevModule = course.modules[currentModuleIndex - 1];
                              setActiveModule(prevModule.id);
                              if (prevModule.lessons.length > 0) {
                                handleLessonClick(prevModule.lessons[prevModule.lessons.length - 1].id);
                              }
                            }
                          }
                        }}
                        disabled={
                          (() => {
                            const currentModule = course.modules.find(m => m.id === activeModule);
                            if (!currentModule) return true;
                            
                            const currentLessonIndex = currentModule.lessons.findIndex(l => l.id === activeLesson);
                            return currentLessonIndex === 0 && course.modules.findIndex(m => m.id === activeModule) === 0;
                          })()
                        }
                      >
                        <i className="fas fa-arrow-left"></i> Anterior
                      </button>
                      
                      <button 
                        className="btn-primary mark-completed"
                        onClick={() => {
                          if (!isLessonCompleted(activeLesson)) {
                            handleLessonClick(activeLesson); // Esto marcará la lección como completada
                          }
                          
                          // Ir a la siguiente lección o al quiz
                          const currentModule = course.modules.find(m => m.id === activeModule);
                          if (!currentModule) return;
                          
                          const currentLessonIndex = currentModule.lessons.findIndex(l => l.id === activeLesson);
                          if (currentLessonIndex < currentModule.lessons.length - 1) {
                            // Ir a la siguiente lección
                            handleLessonClick(currentModule.lessons[currentLessonIndex + 1].id);
                          } else if (currentModule.quiz) {
                            // Ir al quiz si existe
                            handleQuizClick();
                          } else {
                            // Ir al siguiente módulo
                            const currentModuleIndex = course.modules.findIndex(m => m.id === activeModule);
                            if (currentModuleIndex < course.modules.length - 1) {
                              const nextModule = course.modules[currentModuleIndex + 1];
                              setActiveModule(nextModule.id);
                              if (nextModule.lessons.length > 0) {
                                handleLessonClick(nextModule.lessons[0].id);
                              }
                            }
                          }
                        }}
                      >
                        {isLessonCompleted(activeLesson) ? 'Siguiente' : 'Marcar como completado y continuar'} <i className="fas fa-arrow-right"></i>
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="no-lesson-selected">
                    <p>Selecciona una lección para comenzar.</p>
                  </div>
                )
              )
            )}
            
            {activeTab === 'resources' && renderResources()}
            {activeTab === 'discussion' && renderDiscussion()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseViewerEnhanced;
