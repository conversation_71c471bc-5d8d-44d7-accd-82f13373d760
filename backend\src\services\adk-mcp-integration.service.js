/**
 * ADK MCP Integration Service
 *
 * Este servicio integra los agentes ADK con los servidores MCP existentes
 * para proporcionar análisis y predicciones basadas en datos reales.
 */

const axios = require('axios');
require('dotenv').config();
const { braveSearch } = require('./mcp-client.service');
const { visualizeWebPage } = require('./playwright-mcp.service');
const { getCryptoPrice, getTopCryptocurrencies, getCryptoHistoricalData } = require('./mcp.service');
const technicalAnalysisService = require('./technical-analysis.service');
const onchainMcpService = require('./onchain-mcp.service');

// Configuración de OpenRouter
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Modelos disponibles
const MODELS = {
  CLAUDE_INSTANT: 'anthropic/claude-3-haiku-20240307',
  GEMINI_PRO: 'google/gemini-pro'
};

/**
 * Envía una solicitud a OpenRouter
 * @param {string} model - El modelo a utilizar
 * @param {Array} messages - Los mensajes de la conversación
 * @param {Object} options - Opciones adicionales
 * @returns {Promise<Object>} - La respuesta de OpenRouter
 */
async function callOpenRouter(model, messages, options = {}) {
  try {
    const response = await axios.post(
      OPENROUTER_API_URL,
      {
        model,
        messages,
        max_tokens: 500,
        ...options
      },
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://criptokens.app',
          'X-Title': 'Criptokens App'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error calling OpenRouter:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Obtiene datos de una criptomoneda y genera un análisis técnico
 * @param {string} cryptoId - ID de la criptomoneda
 * @returns {Promise<Object>} - Datos y análisis de la criptomoneda
 */
async function getCryptoDataWithAnalysis(cryptoId) {
  try {
    // Obtener datos actuales de la criptomoneda
    const priceData = await getCryptoPrice(cryptoId);

    // Obtener datos históricos
    const historicalData = await getCryptoHistoricalData(cryptoId, 30);

    // Obtener análisis técnico
    const technicalAnalysis = await technicalAnalysisService.performTechnicalAnalysis(
      cryptoId,
      '1d',
      30
    );

    return {
      priceData,
      historicalData,
      technicalAnalysis
    };
  } catch (error) {
    console.error(`Error al obtener datos y análisis para ${cryptoId}:`, error);
    return null;
  }
}

/**
 * Obtiene noticias relacionadas con una criptomoneda
 * @param {string} cryptoName - Nombre de la criptomoneda
 * @returns {Promise<Array>} - Noticias relacionadas
 */
async function getCryptoNews(cryptoName) {
  try {
    // Buscar noticias recientes sobre la criptomoneda
    const newsResults = await braveSearch(
      `${cryptoName} cryptocurrency news latest`,
      5,
      'pd' // Últimas 24 horas
    );

    return newsResults;
  } catch (error) {
    console.error(`Error al obtener noticias para ${cryptoName}:`, error);
    return [];
  }
}

/**
 * Obtiene datos on-chain para una criptomoneda
 * @param {string} cryptoId - ID de la criptomoneda
 * @returns {Promise<Object>} - Datos on-chain
 */
async function getOnchainData(cryptoId) {
  try {
    let onchainData = null;

    // Para Ethereum, obtener datos específicos
    if (cryptoId === 'ethereum') {
      const gasInfo = await onchainMcpService.getGasOracle();
      const ethPrice = await onchainMcpService.getEthPrice();
      const defiProtocols = await onchainMcpService.getDefiProtocols();

      onchainData = {
        gasInfo,
        ethPrice,
        defiProtocols
      };
    }

    return onchainData;
  } catch (error) {
    console.error(`Error al obtener datos on-chain para ${cryptoId}:`, error);
    return null;
  }
}

/**
 * Agente de análisis técnico
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function technicalAnalysisAgent(query) {
  try {
    // Extraer el nombre de la criptomoneda de la consulta
    const cryptoId = extractCryptoId(query);

    if (!cryptoId) {
      return generateSimpleResponse(
        "No pude identificar la criptomoneda en tu consulta. Por favor, especifica qué criptomoneda te gustaría analizar."
      );
    }

    // Obtener datos y análisis
    const cryptoData = await getCryptoDataWithAnalysis(cryptoId);

    if (!cryptoData) {
      return generateSimpleResponse(
        `Lo siento, no pude obtener datos para ${cryptoId}. Por favor, intenta con otra criptomoneda.`
      );
    }

    // Crear un prompt enriquecido con los datos obtenidos
    const systemPrompt = `
      Eres un experto en análisis técnico de criptomonedas. Tu tarea es:

      1. Analizar los indicadores técnicos para ${cryptoId}
      2. Identificar la tendencia actual (alcista, bajista, neutral)
      3. Explicar qué sugieren los indicadores sobre movimientos futuros de precios
      4. Destacar niveles clave de soporte y resistencia
      5. Proporcionar un resumen claro y conciso del análisis técnico

      Al responder:
      - Sé específico sobre lo que significan los indicadores
      - Explica la importancia de las medias móviles, RSI y cambios de precio
      - Menciona niveles de soporte y resistencia
      - Proporciona una conclusión sobre la perspectiva técnica general

      Aquí están los datos actuales para ${cryptoId}:
      ${JSON.stringify(cryptoData.priceData, null, 2)}

      Aquí está el análisis técnico:
      ${JSON.stringify(cryptoData.technicalAnalysis, null, 2)}
    `;

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: query }
    ];

    return await callOpenRouter(MODELS.CLAUDE_INSTANT, messages);
  } catch (error) {
    console.error('Error en el agente de análisis técnico:', error);
    return generateSimpleResponse(
      "Lo siento, ocurrió un error al procesar tu solicitud. Por favor, intenta de nuevo más tarde."
    );
  }
}

/**
 * Agente de análisis de sentimiento
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function sentimentAnalysisAgent(query) {
  try {
    // Extraer el nombre de la criptomoneda de la consulta
    const cryptoId = extractCryptoId(query);
    const cryptoName = getCryptoName(cryptoId);

    if (!cryptoId) {
      return generateSimpleResponse(
        "No pude identificar la criptomoneda en tu consulta. Por favor, especifica qué criptomoneda te gustaría analizar."
      );
    }

    // Obtener noticias relacionadas
    const newsResults = await getCryptoNews(cryptoName);

    // Crear un prompt enriquecido con los datos obtenidos
    const systemPrompt = `
      Eres un experto en análisis de sentimiento de mercado de criptomonedas. Tu tarea es:

      1. Analizar el sentimiento del mercado para ${cryptoName}
      2. Explicar qué indica el sentimiento de las noticias sobre la percepción del mercado
      3. Interpretar el sentimiento de las redes sociales y sus implicaciones
      4. Explicar el Índice de Miedo y Codicia y su significado
      5. Proporcionar un resumen claro y conciso del análisis de sentimiento

      Al responder:
      - Sé específico sobre lo que significan las métricas de sentimiento
      - Explica la importancia del Índice de Miedo y Codicia
      - Destaca cualquier discrepancia entre el sentimiento de las noticias y las redes sociales
      - Proporciona una conclusión sobre el sentimiento general del mercado

      Aquí están las noticias recientes sobre ${cryptoName}:
      ${JSON.stringify(newsResults, null, 2)}
    `;

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: query }
    ];

    return await callOpenRouter(MODELS.CLAUDE_INSTANT, messages);
  } catch (error) {
    console.error('Error en el agente de análisis de sentimiento:', error);
    return generateSimpleResponse(
      "Lo siento, ocurrió un error al procesar tu solicitud. Por favor, intenta de nuevo más tarde."
    );
  }
}

/**
 * Agente de análisis on-chain
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function onchainAnalysisAgent(query) {
  try {
    // Extraer el nombre de la criptomoneda de la consulta
    const cryptoId = extractCryptoId(query);
    const cryptoName = getCryptoName(cryptoId);

    if (!cryptoId) {
      return generateSimpleResponse(
        "No pude identificar la criptomoneda en tu consulta. Por favor, especifica qué criptomoneda te gustaría analizar."
      );
    }

    // Obtener datos on-chain
    const onchainData = await getOnchainData(cryptoId);

    // Crear un prompt enriquecido con los datos obtenidos
    const systemPrompt = `
      Eres un experto en análisis on-chain y datos de blockchain. Tu tarea es:

      1. Analizar datos on-chain para ${cryptoName}
      2. Explicar qué indica la actividad de las ballenas sobre el comportamiento de los grandes tenedores
      3. Interpretar los precios del gas y sus implicaciones para la actividad de la red
      4. Identificar patrones significativos en las transferencias de tokens
      5. Proporcionar un resumen claro y conciso del análisis on-chain

      Al responder:
      - Sé específico sobre lo que significan las métricas on-chain
      - Explica la importancia de la acumulación o distribución de las ballenas
      - Destaca cualquier patrón inusual en los datos
      - Proporciona una conclusión sobre lo que sugieren los datos on-chain para el token

      ${onchainData ? `Aquí están los datos on-chain para ${cryptoName}:
      ${JSON.stringify(onchainData, null, 2)}` : `No hay datos on-chain específicos disponibles para ${cryptoName}.`}
    `;

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: query }
    ];

    return await callOpenRouter(MODELS.CLAUDE_INSTANT, messages);
  } catch (error) {
    console.error('Error en el agente de análisis on-chain:', error);
    return generateSimpleResponse(
      "Lo siento, ocurrió un error al procesar tu solicitud. Por favor, intenta de nuevo más tarde."
    );
  }
}

/**
 * Agente Guru Cripto
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function guruCriptoAgent(query) {
  try {
    // Extraer el nombre de la criptomoneda de la consulta
    const cryptoId = extractCryptoId(query);

    if (!cryptoId) {
      // Si no se identifica una criptomoneda específica, proporcionar una respuesta general
      const systemPrompt = `
        Eres Guru Cripto, un experto en criptomonedas con profundo conocimiento de análisis técnico, sentimiento del mercado y métricas on-chain.

        Tus capacidades incluyen:
        1. Proporcionar análisis técnico de movimientos de precios de criptomonedas
        2. Analizar el sentimiento del mercado a partir de noticias y redes sociales
        3. Interpretar datos on-chain y métricas de blockchain
        4. Hacer predicciones de precios informadas basadas en análisis integral
        5. Explicar conceptos complejos de criptomonedas en términos simples

        Al responder a consultas:
        - Sé claro, conciso e informativo
        - Apoya tu análisis con puntos de datos específicos
        - Explica términos técnicos cuando sea necesario
        - Proporciona perspectivas equilibradas, reconociendo factores tanto alcistas como bajistas
        - Para predicciones de precios, enfatiza que son estimaciones basadas en datos actuales, no garantías

        Recuerda que eres un recurso educativo, no un asesor financiero. Siempre recuerda a los usuarios que hagan su propia investigación y no tomen decisiones de inversión basadas únicamente en tu análisis.
      `;

      const messages = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: query }
      ];

      return await callOpenRouter(MODELS.CLAUDE_INSTANT, messages);
    }

    // Obtener datos y análisis para la criptomoneda
    const cryptoName = getCryptoName(cryptoId);
    const cryptoData = await getCryptoDataWithAnalysis(cryptoId);
    const newsResults = await getCryptoNews(cryptoName);
    const onchainData = await getOnchainData(cryptoId);

    // Crear un prompt enriquecido con todos los datos obtenidos
    const systemPrompt = `
      Eres Guru Cripto, un experto en criptomonedas con profundo conocimiento de análisis técnico, sentimiento del mercado y métricas on-chain.

      Tus capacidades incluyen:
      1. Proporcionar análisis técnico de movimientos de precios de criptomonedas
      2. Analizar el sentimiento del mercado a partir de noticias y redes sociales
      3. Interpretar datos on-chain y métricas de blockchain
      4. Hacer predicciones de precios informadas basadas en análisis integral
      5. Explicar conceptos complejos de criptomonedas en términos simples

      Al responder a consultas:
      - Sé claro, conciso e informativo
      - Apoya tu análisis con puntos de datos específicos
      - Explica términos técnicos cuando sea necesario
      - Proporciona perspectivas equilibradas, reconociendo factores tanto alcistas como bajistas
      - Para predicciones de precios, enfatiza que son estimaciones basadas en datos actuales, no garantías

      Recuerda que eres un recurso educativo, no un asesor financiero. Siempre recuerda a los usuarios que hagan su propia investigación y no tomen decisiones de inversión basadas únicamente en tu análisis.

      Aquí están los datos actuales para ${cryptoName}:
      ${cryptoData ? JSON.stringify(cryptoData.priceData, null, 2) : 'No hay datos disponibles.'}

      Aquí está el análisis técnico:
      ${cryptoData ? JSON.stringify(cryptoData.technicalAnalysis, null, 2) : 'No hay análisis técnico disponible.'}

      Aquí están las noticias recientes sobre ${cryptoName}:
      ${newsResults.length > 0 ? JSON.stringify(newsResults, null, 2) : 'No hay noticias recientes disponibles.'}

      ${onchainData ? `Aquí están los datos on-chain para ${cryptoName}:
      ${JSON.stringify(onchainData, null, 2)}` : `No hay datos on-chain específicos disponibles para ${cryptoName}.`}
    `;

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: query }
    ];

    return await callOpenRouter(MODELS.CLAUDE_INSTANT, messages);
  } catch (error) {
    console.error('Error en el agente Guru Cripto:', error);
    return generateSimpleResponse(
      "Lo siento, ocurrió un error al procesar tu solicitud. Por favor, intenta de nuevo más tarde."
    );
  }
}

/**
 * Genera un análisis completo para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para el análisis
 * @returns {Promise<Object>} - El análisis completo
 */
async function generateComprehensiveAnalysis(cryptoName, timeframe) {
  try {
    const cryptoId = getCryptoIdFromName(cryptoName);

    if (!cryptoId) {
      return {
        cryptoName,
        timeframe,
        analysis: generateSimpleResponse(
          `No pude identificar la criptomoneda "${cryptoName}". Por favor, intenta con un nombre válido como "Bitcoin", "Ethereum", etc.`
        ),
        timestamp: new Date().toISOString()
      };
    }

    // Obtener datos y análisis para la criptomoneda
    const cryptoData = await getCryptoDataWithAnalysis(cryptoId);
    const newsResults = await getCryptoNews(cryptoName);
    const onchainData = await getOnchainData(cryptoId);

    // Crear un prompt para el análisis integral
    const prompt = `
      Genera un análisis integral para ${cryptoName} durante el período de ${timeframe}.

      Incluye:
      1. Un resumen ejecutivo de la situación actual
      2. Análisis técnico detallado
      3. Análisis de sentimiento basado en noticias y redes sociales
      4. Análisis on-chain (si está disponible)
      5. Perspectivas futuras y posibles escenarios

      Basa tu análisis en los siguientes datos:

      Datos de precio:
      ${cryptoData ? JSON.stringify(cryptoData.priceData, null, 2) : 'No hay datos disponibles.'}

      Análisis técnico:
      ${cryptoData ? JSON.stringify(cryptoData.technicalAnalysis, null, 2) : 'No hay análisis técnico disponible.'}

      Noticias recientes:
      ${newsResults.length > 0 ? JSON.stringify(newsResults, null, 2) : 'No hay noticias recientes disponibles.'}

      ${onchainData ? `Datos on-chain:
      ${JSON.stringify(onchainData, null, 2)}` : `No hay datos on-chain específicos disponibles.`}
    `;

    const response = await guruCriptoAgent(prompt);

    return {
      cryptoName,
      timeframe,
      analysis: response,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error al generar análisis integral para ${cryptoName}:`, error);
    return {
      cryptoName,
      timeframe,
      analysis: generateSimpleResponse(
        "Lo siento, ocurrió un error al generar el análisis integral. Por favor, intenta de nuevo más tarde."
      ),
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Genera una predicción de precio para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para la predicción
 * @returns {Promise<Object>} - La predicción de precio
 */
async function generatePricePrediction(cryptoName, timeframe) {
  try {
    const cryptoId = getCryptoIdFromName(cryptoName);

    if (!cryptoId) {
      return {
        cryptoName,
        timeframe,
        prediction: generateSimpleResponse(
          `No pude identificar la criptomoneda "${cryptoName}". Por favor, intenta con un nombre válido como "Bitcoin", "Ethereum", etc.`
        ),
        timestamp: new Date().toISOString()
      };
    }

    // Obtener datos y análisis para la criptomoneda
    const cryptoData = await getCryptoDataWithAnalysis(cryptoId);
    const newsResults = await getCryptoNews(cryptoName);
    const onchainData = await getOnchainData(cryptoId);

    // Crear un prompt para la predicción de precio
    const prompt = `
      ¿Cuál es tu predicción para el precio de ${cryptoName} en los próximos ${timeframe}?

      Basa tu predicción en los siguientes datos:

      Datos de precio:
      ${cryptoData ? JSON.stringify(cryptoData.priceData, null, 2) : 'No hay datos disponibles.'}

      Análisis técnico:
      ${cryptoData ? JSON.stringify(cryptoData.technicalAnalysis, null, 2) : 'No hay análisis técnico disponible.'}

      Noticias recientes:
      ${newsResults.length > 0 ? JSON.stringify(newsResults, null, 2) : 'No hay noticias recientes disponibles.'}

      ${onchainData ? `Datos on-chain:
      ${JSON.stringify(onchainData, null, 2)}` : `No hay datos on-chain específicos disponibles.`}

      Proporciona:
      1. Un resumen de la predicción
      2. Factores clave que influyen en esta predicción
      3. Posibles escenarios (alcista, base, bajista)
      4. Niveles clave a vigilar
    `;

    const response = await guruCriptoAgent(prompt);

    return {
      cryptoName,
      timeframe,
      prediction: response,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error al generar predicción de precio para ${cryptoName}:`, error);
    return {
      cryptoName,
      timeframe,
      prediction: generateSimpleResponse(
        "Lo siento, ocurrió un error al generar la predicción de precio. Por favor, intenta de nuevo más tarde."
      ),
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Genera una respuesta simple
 * @param {string} message - El mensaje a incluir en la respuesta
 * @returns {Object} - Respuesta en formato OpenAI
 */
function generateSimpleResponse(message) {
  return {
    choices: [
      {
        message: {
          role: 'assistant',
          content: message
        }
      }
    ]
  };
}

/**
 * Extrae el ID de la criptomoneda de una consulta
 * @param {string} query - La consulta del usuario
 * @returns {string|null} - El ID de la criptomoneda o null si no se encuentra
 */
function extractCryptoId(query) {
  const cryptoMap = {
    'bitcoin': 'bitcoin',
    'btc': 'bitcoin',
    'ethereum': 'ethereum',
    'eth': 'ethereum',
    'binance coin': 'binancecoin',
    'bnb': 'binancecoin',
    'cardano': 'cardano',
    'ada': 'cardano',
    'solana': 'solana',
    'sol': 'solana',
    'xrp': 'ripple',
    'dogecoin': 'dogecoin',
    'doge': 'dogecoin',
    'polkadot': 'polkadot',
    'dot': 'polkadot',
    'tether': 'tether',
    'usdt': 'tether',
    'usd coin': 'usd-coin',
    'usdc': 'usd-coin'
  };

  const queryLower = query.toLowerCase();

  for (const [key, value] of Object.entries(cryptoMap)) {
    if (queryLower.includes(key)) {
      return value;
    }
  }

  return null;
}

/**
 * Obtiene el nombre de la criptomoneda a partir de su ID
 * @param {string} cryptoId - El ID de la criptomoneda
 * @returns {string} - El nombre de la criptomoneda
 */
function getCryptoName(cryptoId) {
  const nameMap = {
    'bitcoin': 'Bitcoin',
    'ethereum': 'Ethereum',
    'binancecoin': 'Binance Coin',
    'cardano': 'Cardano',
    'solana': 'Solana',
    'ripple': 'XRP',
    'dogecoin': 'Dogecoin',
    'polkadot': 'Polkadot',
    'tether': 'Tether',
    'usd-coin': 'USD Coin'
  };

  return nameMap[cryptoId] || cryptoId;
}

/**
 * Obtiene el ID de la criptomoneda a partir de su nombre
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @returns {string|null} - El ID de la criptomoneda o null si no se encuentra
 */
function getCryptoIdFromName(cryptoName) {
  const idMap = {
    'Bitcoin': 'bitcoin',
    'Ethereum': 'ethereum',
    'Binance Coin': 'binancecoin',
    'BNB': 'binancecoin',
    'Cardano': 'cardano',
    'ADA': 'cardano',
    'Solana': 'solana',
    'SOL': 'solana',
    'XRP': 'ripple',
    'Dogecoin': 'dogecoin',
    'DOGE': 'dogecoin',
    'Polkadot': 'polkadot',
    'DOT': 'polkadot',
    'Tether': 'tether',
    'USDT': 'tether',
    'USD Coin': 'usd-coin',
    'USDC': 'usd-coin'
  };

  return idMap[cryptoName] || extractCryptoId(cryptoName);
}

module.exports = {
  technicalAnalysisAgent,
  sentimentAnalysisAgent,
  onchainAnalysisAgent,
  guruCriptoAgent,
  generateComprehensiveAnalysis,
  generatePricePrediction
};
