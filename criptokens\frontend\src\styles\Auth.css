.auth-wrapper {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-dark, #0a0a1a);
  padding: 2rem 1rem;
}

.auth-container {
  width: 100%;
  max-width: 400px;
  padding: 1.5rem;
}

.auth-card {
  background-color: var(--bg-medium, #141432);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  padding: 2rem;
  border: 1px solid rgba(64, 220, 255, 0.2);
  color: var(--text-bright, #ffffff);
}

.auth-card h2 {
  font-size: 1.5rem;
  color: var(--primary, #00f2ff);
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.auth-error {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: 0.9375rem;
}

.auth-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: 0.9375rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.9375rem;
  color: var(--text-medium, #e0e0ff);
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid rgba(64, 220, 255, 0.3);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: rgba(10, 10, 26, 0.5);
  color: var(--text-bright, #ffffff);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary, #00f2ff);
  box-shadow: 0 0 10px rgba(0, 242, 255, 0.3);
}

.auth-button {
  background: linear-gradient(90deg, var(--secondary, #4657ce), var(--primary, #00f2ff));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.875rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
  box-shadow: 0 4px 10px rgba(0, 242, 255, 0.2);
}

.auth-button:hover {
  background-color: #0077e6;
}

.auth-button:disabled {
  background-color: #66b2ff;
  cursor: not-allowed;
}

.auth-links {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.text-link {
  background: none;
  border: none;
  color: #0084ff;
  font-size: 0.9375rem;
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 0;
}

.text-link:hover {
  color: #0077e6;
  text-decoration: underline;
}

.auth-separator {
  width: 100%;
  text-align: center;
  margin: 0.5rem 0;
  position: relative;
}

.auth-separator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #eee;
  z-index: 1;
}

.auth-separator span {
  position: relative;
  z-index: 2;
  background-color: white;
  padding: 0 0.75rem;
  color: #777;
  font-size: 0.875rem;
}

.secondary-button {
  background-color: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9375rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.secondary-button:hover {
  background-color: #e0e0e0;
}
