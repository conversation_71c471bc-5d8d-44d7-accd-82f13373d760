import { useState, useEffect, useRef } from 'react';
import anime from 'animejs/lib/anime.es.js';
import MarketSummary from './MarketSummary';
import CryptoTable from './CryptoTable';
import CryptoDetail from './CryptoDetail';
import ChatInterface from './ChatInterface';
import EnhancedChatInterface from './EnhancedChatInterface';
import PortfolioFirebase from './PortfolioFirebase';
import EnhancedPortfolio from './EnhancedPortfolio';
import NewsSection from './NewsSection';
import '../styles/EnhancedDashboard.css';

const EnhancedDashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCryptoId, setSelectedCryptoId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const networkRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Inicializar animaciones cuando el componente se monta (simplificadas)
  useEffect(() => {
    // Animación de carga simplificada
    anime.timeline({
      easing: 'easeOutSine',
    })
    .add({
      targets: '.dashboard-loading',
      opacity: [1, 0],
      duration: 500,
      delay: 300,
      complete: function() {
        setIsLoading(false);
      }
    })
    .add({
      targets: '.dashboard-container',
      opacity: [0.8, 1],
      duration: 400,
    }, '-=200');

    // Inicializar animación de red de fondo
    if (networkRef.current) {
      initNetworkAnimation();
    }
  }, []);

  // Animar cambio de pestaña (simplificado)
  useEffect(() => {
    if (!isLoading && contentRef.current) {
      anime({
        targets: contentRef.current,
        opacity: [0.8, 1],
        translateY: [5, 0],
        duration: 300,
        easing: 'easeOutSine'
      });
    }
  }, [activeTab, isLoading]);

  // Inicializar animación de red de fondo
  const initNetworkAnimation = () => {
    if (!networkRef.current) return;

    const container = networkRef.current;
    const width = container.offsetWidth;
    const height = container.offsetHeight;
    const nodeCount = 30;

    // Limpiar nodos existentes
    container.innerHTML = '';

    // Crear nodos
    const nodes = [];
    for (let i = 0; i < nodeCount; i++) {
      const node = document.createElement('div');
      node.classList.add('network-node');

      // Posición aleatoria
      const x = Math.random() * width;
      const y = Math.random() * height;
      node.style.left = `${x}px`;
      node.style.top = `${y}px`;

      // Tamaño aleatorio
      const size = Math.random() * 8 + 4;
      node.style.width = `${size}px`;
      node.style.height = `${size}px`;

      container.appendChild(node);
      nodes.push({ element: node, x, y, size });
    }

    // Crear conexiones
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        if (Math.random() > 0.7) continue; // Solo conectar algunos nodos

        const connection = document.createElement('div');
        connection.classList.add('network-connection');

        // Calcular distancia y posición
        const x1 = nodes[i].x + nodes[i].size / 2;
        const y1 = nodes[i].y + nodes[i].size / 2;
        const x2 = nodes[j].x + nodes[j].size / 2;
        const y2 = nodes[j].y + nodes[j].size / 2;

        const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

        connection.style.width = `${length}px`;
        connection.style.left = `${x1}px`;
        connection.style.top = `${y1}px`;
        connection.style.transform = `rotate(${angle}deg)`;

        container.appendChild(connection);
      }
    }

    // Animar nodos con movimientos más sutiles
    anime({
      targets: '.network-node',
      translateX: () => anime.random(-5, 5),
      translateY: () => anime.random(-5, 5),
      scale: () => [1, anime.random(0.95, 1.05), 1],
      easing: 'easeInOutSine',
      duration: () => anime.random(5000, 8000),
      loop: true,
      delay: () => anime.random(0, 1000)
    });

    // Animar conexiones con cambios más sutiles
    anime({
      targets: '.network-connection',
      opacity: [0.1, 0.2, 0.1],
      easing: 'easeInOutSine',
      duration: 5000,
      loop: true
    });
  };

  // Cambiar pestaña con animación simplificada
  const handleTabChange = (tab: string) => {
    if (contentRef.current) {
      anime({
        targets: contentRef.current,
        opacity: [1, 0.8],
        translateY: [0, 5],
        duration: 200,
        easing: 'easeInOutSine',
        complete: () => {
          setActiveTab(tab);
          if (tab === 'dashboard') {
            setSelectedCryptoId(null);
          }
        }
      });
    } else {
      setActiveTab(tab);
      if (tab === 'dashboard') {
        setSelectedCryptoId(null);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-logo">
          <div className="logo-circle"></div>
          <div className="logo-ring ring1"></div>
          <div className="logo-ring ring2"></div>
        </div>
        <div className="loading-text">
          {Array.from('CRIPTOKENS').map((letter, index) => (
            <span key={index}>{letter}</span>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-sidebar">
        <div className="logo">
          <div className="logo-icon">
            <div className="logo-core"></div>
            <div className="logo-ring ring1"></div>
            <div className="logo-ring ring2"></div>
          </div>
          <span className="logo-text">Criptokens</span>
        </div>

        <nav className="sidebar-nav">
          <button
            className={`nav-item ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => handleTabChange('dashboard')}
          >
            <span className="nav-icon">📊</span>
            <span className="nav-text">Dashboard</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'portfolio' ? 'active' : ''}`}
            onClick={() => handleTabChange('portfolio')}
          >
            <span className="nav-icon">💼</span>
            <span className="nav-text">Mi Cartera</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'news' ? 'active' : ''}`}
            onClick={() => handleTabChange('news')}
          >
            <span className="nav-icon">📰</span>
            <span className="nav-text">Noticias</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'chat' ? 'active' : ''}`}
            onClick={() => handleTabChange('chat')}
          >
            <span className="nav-icon">🤖</span>
            <span className="nav-text">Asistente IA</span>
          </button>
        </nav>
      </div>

      <div className="dashboard-main">
        <div ref={networkRef} className="network-background"></div>

        <div ref={contentRef} className="dashboard-content">
          {activeTab === 'dashboard' && (
            selectedCryptoId ? (
              <CryptoDetail
                cryptoId={selectedCryptoId}
                onBack={() => setSelectedCryptoId(null)}
              />
            ) : (
              <div className="dashboard-overview">
                <MarketSummary />
                <div className="dashboard-main-content">
                  <div className="main-section">
                    <CryptoTable onSelectCrypto={(id) => setSelectedCryptoId(id)} />
                  </div>
                  <div className="side-section">
                    <div className="chat-widget">
                      <div className="widget-header">
                        <div className="widget-icon">
                          <div className="agent-core"></div>
                          <div className="agent-ring ring-1"></div>
                          <div className="agent-ring ring-2"></div>
                        </div>
                        <h3>Asistente Cripto</h3>
                      </div>
                      <p>Pregunta sobre criptomonedas, blockchain o cualquier duda que tengas.</p>
                      <button
                        className="open-chat-button"
                        onClick={() => handleTabChange('chat')}
                      >
                        Abrir Chat
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}

          {activeTab === 'portfolio' && (
            <EnhancedPortfolio />
          )}

          {activeTab === 'news' && (
            <NewsSection />
          )}

          {activeTab === 'chat' && (
            <div className="chat-fullscreen">
              <div className="chat-header">
                <div className="agent-avatar">
                  <div className="agent-core"></div>
                  <div className="agent-ring ring-1"></div>
                  <div className="agent-ring ring-2"></div>
                </div>
                <div className="agent-info">
                  <h2>Asistente Cripto</h2>
                  <p>Tu experto en criptomonedas y blockchain</p>
                </div>
              </div>
              <EnhancedChatInterface />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedDashboard;
