import React, { useEffect, useState } from 'react';
import { mockTopCryptos, mockGlobalData, mockHistoricalData } from '../services/mockData';

const DataTestComponent: React.FC = () => {
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    // Verificar que los datos simulados están disponibles
    console.log('Datos simulados disponibles:');
    console.log('mockTopCryptos:', mockTopCryptos);
    console.log('mockGlobalData:', mockGlobalData);
    console.log('mockHistoricalData:', mockHistoricalData);
    
    setLoaded(true);
  }, []);

  return (
    <div style={{ padding: '20px', color: 'white', backgroundColor: '#0f1123', minHeight: '100vh' }}>
      <h1>Componente de Prueba de Datos</h1>
      
      <div>
        <h2>Estado de carga: {loaded ? 'Datos cargados' : 'Cargando...'}</h2>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h2>Top Criptomonedas (primeras 3)</h2>
        {mockTopCryptos.slice(0, 3).map(crypto => (
          <div key={crypto.id} style={{ marginBottom: '10px', padding: '10px', backgroundColor: '#1a1f3d', borderRadius: '8px' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <img src={crypto.image} alt={crypto.name} style={{ width: '32px', height: '32px', marginRight: '10px' }} />
              <div>
                <div style={{ fontWeight: 'bold' }}>{crypto.name} ({crypto.symbol.toUpperCase()})</div>
                <div>${crypto.current_price.toLocaleString()}</div>
              </div>
              <div style={{ 
                marginLeft: 'auto', 
                color: crypto.price_change_percentage_24h >= 0 ? '#00ff9d' : '#ff3a6e' 
              }}>
                {crypto.price_change_percentage_24h >= 0 ? '+' : ''}
                {crypto.price_change_percentage_24h.toFixed(2)}%
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h2>Datos del Mercado Global</h2>
        <div style={{ padding: '10px', backgroundColor: '#1a1f3d', borderRadius: '8px' }}>
          <div>Capitalización Total: ${mockGlobalData.data.total_market_cap.usd.toLocaleString()}</div>
          <div>Volumen 24h: ${mockGlobalData.data.total_volume.usd.toLocaleString()}</div>
          <div>Dominancia BTC: {mockGlobalData.data.market_cap_percentage.btc.toFixed(2)}%</div>
        </div>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h2>Datos Históricos de Bitcoin (primeros 5 puntos)</h2>
        <div style={{ padding: '10px', backgroundColor: '#1a1f3d', borderRadius: '8px' }}>
          {mockHistoricalData.bitcoin.prices.slice(0, 5).map((price, index) => (
            <div key={index}>
              {new Date(price[0]).toLocaleDateString()}: ${price[1].toLocaleString()}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DataTestComponent;
