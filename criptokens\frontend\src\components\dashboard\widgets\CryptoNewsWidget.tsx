import React from 'react';
import { useNavigate } from 'react-router-dom';

interface CryptoNewsWidgetProps {
  isLoading: boolean;
  compact?: boolean;
  maxItems?: number;
}

interface NewsItem {
  id: string;
  title: string;
  source: string;
  url: string;
  publishedAt: string;
  imageUrl?: string;
}

const CryptoNewsWidget: React.FC<CryptoNewsWidgetProps> = ({ 
  isLoading, 
  compact = false,
  maxItems = 5
}) => {
  const navigate = useNavigate();
  
  // Datos simulados para noticias
  const mockNews: NewsItem[] = [
    {
      id: '1',
      title: 'Bitcoin supera los $60,000 por primera vez en dos años',
      source: 'CoinDesk',
      url: '#',
      publishedAt: '2023-10-28T14:30:00Z',
      imageUrl: 'https://via.placeholder.com/60'
    },
    {
      id: '2',
      title: 'Ethereum completa actualización importante mejorando escalabilidad',
      source: 'CryptoNews',
      url: '#',
      publishedAt: '2023-10-27T10:15:00Z',
      imageUrl: 'https://via.placeholder.com/60'
    },
    {
      id: '3',
      title: 'Reguladores europeos proponen nuevo marco para criptomonedas',
      source: 'Bloomberg',
      url: '#',
      publishedAt: '2023-10-26T08:45:00Z',
      imageUrl: 'https://via.placeholder.com/60'
    },
    {
      id: '4',
      title: 'Binance lanza nueva plataforma de trading institucional',
      source: 'CoinTelegraph',
      url: '#',
      publishedAt: '2023-10-25T16:20:00Z',
      imageUrl: 'https://via.placeholder.com/60'
    },
    {
      id: '5',
      title: 'El Salvador anuncia nueva ciudad Bitcoin financiada con bonos',
      source: 'Reuters',
      url: '#',
      publishedAt: '2023-10-24T12:10:00Z',
      imageUrl: 'https://via.placeholder.com/60'
    },
    {
      id: '6',
      title: 'Cardano implementa actualización para mejorar contratos inteligentes',
      source: 'Decrypt',
      url: '#',
      publishedAt: '2023-10-23T09:30:00Z',
      imageUrl: 'https://via.placeholder.com/60'
    }
  ];

  const displayedNews = mockNews.slice(0, maxItems);
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      day: 'numeric', 
      month: 'short'
    });
  };

  if (isLoading) {
    return (
      <div className="widget-loading-state">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Cargando noticias...</p>
      </div>
    );
  }

  return (
    <div className="news-list">
      {displayedNews.map(news => (
        <div key={news.id} className="news-item">
          {!compact && news.imageUrl && (
            <div className="news-image">
              <img src={news.imageUrl} alt={news.title} />
            </div>
          )}
          <div className="news-content">
            <div className="news-title" onClick={() => window.open(news.url, '_blank')}>
              {news.title}
            </div>
            <div className="news-source">
              {news.source} • {formatDate(news.publishedAt)}
            </div>
          </div>
        </div>
      ))}
      
      {!compact && (
        <div className="view-more-news">
          <button 
            className="view-more-button"
            onClick={() => navigate('/news')}
          >
            Ver más noticias <i className="fas fa-arrow-right"></i>
          </button>
        </div>
      )}
    </div>
  );
};

export default CryptoNewsWidget;
