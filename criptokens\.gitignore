# Dependencias
node_modules/
package-lock.json
yarn.lock

# Variables de entorno
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Directorios de compilación
dist/
build/
dist-ssr/

# Archivos del sistema
.DS_Store
Thumbs.db

# Directorios de cobertura de pruebas
coverage/

# Archivos temporales
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
