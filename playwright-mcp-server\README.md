# Playwright MCP Server para Criptokens

Este servidor proporciona capacidades de automatización de navegador utilizando Playwright a través del Protocolo de Contexto de Modelo (MCP). Permite que el Gurú Cripto visualice y analice páginas web completas para proporcionar respuestas más informadas y contextualizadas.

## Características

- **Visualización de Páginas Web**: Permite navegar a URLs específicas y obtener snapshots estructurados del contenido.
- **Captura de Pantalla**: Toma capturas de pantalla de las páginas visitadas para referencia visual.
- **Análisis de Contenido**: Proporciona datos estructurados que el LLM puede analizar para extraer información relevante.
- **Integración con OpenRouter**: Se integra con el servicio OpenRouter para permitir que el LLM utilice las capacidades de navegación web.

## Requisitos

- Node.js 16+
- NPM 7+

## Instalación

```bash
# Instalar dependencias
npm install
```

## Uso

### Iniciar el servidor

```bash
# Modo desarrollo
npm run dev

# Modo producción
npm run build
npm start
```

El servidor se iniciará en el puerto 3103 por defecto. Puedes cambiar el puerto en el archivo `.env`.

### Endpoints

- `GET /status`: Verifica el estado del servidor.
- `GET /sse`: Endpoint para conexiones Server-Sent Events (SSE).

## Integración con el Backend

El servidor Playwright-MCP se integra con el backend de Criptokens a través del servicio `playwright-mcp.service.js`, que proporciona las siguientes funciones:

- `browseWebPage(url)`: Navega a una URL y obtiene un snapshot de la página.
- `takeScreenshot(raw)`: Toma una captura de pantalla de la página actual.
- `visualizeWebPage(url)`: Combina las funciones anteriores para proporcionar datos completos de una página web.

## Integración con el LLM

El LLM puede utilizar la herramienta `browseWebPage` para navegar a páginas web y analizar su contenido. Esta herramienta está definida en el servicio `openrouter.service.js` y se utiliza a través del endpoint `/api/guru/ask`.

## Licencia

ISC
