/**
 * Servicio para realizar búsquedas web utilizando Brave Search a través del backend
 */
import axios from 'axios';
import { API_URLS, ENDPOINTS, TIMEOUTS, FALLBACKS } from '../config/api.config';
import { mockNewsData, getMockNewsForCoin } from './mockNewsData';

// Cliente de axios para nuestro backend
const newsApiClient = axios.create({
  baseURL: API_URLS.backend,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  timeout: TIMEOUTS.short,
  withCredentials: true
});

// Variable para controlar si estamos en modo offline
let isOfflineMode = FALLBACKS.useMockData;

// Interfaz para los resultados de noticias
export interface NewsItem {
  title: string;
  url: string;
  description: string;
  publishedTime?: string;
  source: string;
  imageUrl?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
}

// Función para verificar la conectividad
const checkConnectivity = async (): Promise<boolean> => {
  if (isOfflineMode) return false;

  try {
    // Intentar hacer una petición simple al backend
    await axios.get(`${API_URLS.backend}/api/status`, { timeout: TIMEOUTS.short });
    return true;
  } catch (error) {
    console.warn('Parece que estamos sin conexión, usando datos simulados');
    isOfflineMode = true;
    return false;
  }
};

/**
 * Busca noticias relacionadas con criptomonedas
 * @param query Consulta de búsqueda adicional (opcional)
 * @param count Número de resultados a devolver
 * @param offset Desplazamiento para paginación
 * @param freshness Filtro de tiempo
 * @returns Lista de noticias
 */
export const searchCryptoNews = async (
  query: string = '',
  count: number = 10,
  offset: number = 0,
  freshness: string = ''
): Promise<NewsItem[]> => {
  try {
    console.log(`Buscando noticias con: query=${query}, count=${count}, offset=${offset}, freshness=${freshness}`);

    // Verificar conectividad
    const isOnline = await checkConnectivity();

    // Si estamos offline, usar datos simulados
    if (!isOnline) {
      console.log('Usando datos simulados para noticias generales');
      return mockNewsData.slice(offset, offset + count);
    }

    // Construir la consulta base
    let searchTopic = 'criptomonedas noticias';

    // Añadir la consulta adicional si se proporciona
    if (query && query.trim() !== '') {
      searchTopic = `${query} criptomonedas noticias`;
    }

    console.log('Usando el endpoint del backend para noticias...');

    // Realizar la petición a nuestro backend
    const response = await newsApiClient.post(ENDPOINTS.guru.news, {
      topic: searchTopic,
      freshness,
      count: Math.min(count, 20) // Máximo 20 resultados por petición
    });

    // Procesar los resultados
    if (response.data && response.data.results) {
      return response.data.results;
    }

    return [];
  } catch (error) {
    console.error('Error al buscar noticias de criptomonedas:', error);
    console.log('Fallback a datos simulados debido a error');
    return mockNewsData.slice(offset, offset + count);
  }
};

/**
 * Busca noticias específicas para una criptomoneda
 * @param coinId ID o nombre de la criptomoneda
 * @param coinName Nombre completo de la criptomoneda
 * @param count Número de resultados a devolver
 * @param offset Desplazamiento para paginación
 * @param freshness Filtro de tiempo
 * @returns Lista de noticias
 */
export const searchCoinNews = async (
  coinId: string,
  coinName: string,
  count: number = 10,
  offset: number = 0,
  freshness: string = ''
): Promise<NewsItem[]> => {
  try {
    console.log(`Buscando noticias para ${coinName} (${coinId})`);

    // Verificar conectividad
    const isOnline = await checkConnectivity();

    // Si estamos offline, usar datos simulados
    if (!isOnline) {
      console.log(`Usando datos simulados para ${coinName}`);
      return getMockNewsForCoin(coinId, count);
    }

    // Construir la consulta específica para la criptomoneda
    const searchTopic = `${coinName} ${coinId} criptomoneda noticias`;

    console.log(`Usando el endpoint del backend para noticias de ${coinName}...`);

    // Realizar la petición a nuestro backend
    const response = await newsApiClient.post(ENDPOINTS.guru.news, {
      topic: searchTopic,
      freshness,
      count: Math.min(count, 20) // Máximo 20 resultados por petición
    });

    // Procesar los resultados
    if (response.data && response.data.results) {
      return response.data.results;
    }

    return [];
  } catch (error) {
    console.error(`Error al buscar noticias para ${coinName}:`, error);
    console.log(`Fallback a datos simulados para ${coinName} debido a error`);
    return getMockNewsForCoin(coinId, count);
  }
};

export default {
  searchCryptoNews,
  searchCoinNews
};
