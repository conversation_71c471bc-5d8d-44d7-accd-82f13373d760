# Criptokens A2A Implementation

This directory contains the implementation of Google's Agent-to-Agent (A2A) protocol for Criptokens. The A2A protocol enables different AI agents to communicate and collaborate with each other to provide comprehensive cryptocurrency analysis and predictions.

## Architecture

The A2A implementation consists of the following components:

1. **Common Components**
   - `types.py`: Defines the data types used in the A2A protocol
   - `client/client.py`: Implements the A2A client for communicating with agents
   - `server/server.py`: Implements the A2A server for hosting agents
   - `server/task_manager.py`: Manages tasks for agents
   - `server/utils.py`: Utility functions for the server

2. **Specialized Agents**
   - **Technical Analysis Agent**: Analyzes cryptocurrency price data using technical indicators
   - **Sentiment Analysis Agent**: Analyzes sentiment of cryptocurrency news and social media
   - **On-Chain Analysis Agent**: Analyzes on-chain data for cryptocurrencies
   - **Guru Cripto Agent**: Coordinates the specialized agents and generates comprehensive predictions

## Setup

### Prerequisites

- Python 3.8 or higher
- Required Python packages (install with `pip install -r requirements.txt`):
  - httpx
  - httpx-sse
  - pydantic
  - starlette
  - uvicorn
  - sse-starlette
  - click
  - python-dotenv
  - aiohttp
  - numpy

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/CreastilloQRGen-AI/Criptokens.git
   ```

2. Install dependencies:
   ```
   cd Criptokens
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   - Create a `.env` file in the `criptokens/a2a` directory with the following variables:
     ```
     OPENROUTER_API_KEY=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
     COINMARKETCAP_API_KEY=37f9968e-6ab7-431f-80d7-0ac6686319f3
     BRAVE_API_KEY=BSAccS820UUfffNOAD7yLACz9htlbe9
     ETHERSCAN_API_KEY=**********************************
     ```

## Usage

### Starting the Agents

You can start all agents using the `start_agents.py` script:

```
python -m criptokens.a2a.start_agents
```

This will start all agents in the correct order:
1. Technical Analysis Agent (port 3201)
2. Sentiment Analysis Agent (port 3202)
3. On-Chain Analysis Agent (port 3203)
4. Guru Cripto Agent (port 3200)

To start a specific agent:

```
python -m criptokens.a2a.start_agents --agent technical_agent
```

### Starting Individual Agents

You can also start each agent individually:

```
# Technical Analysis Agent
python -m criptokens.a2a.agents.technical_agent --port 3201

# Sentiment Analysis Agent
python -m criptokens.a2a.agents.sentiment_agent --port 3202

# On-Chain Analysis Agent
python -m criptokens.a2a.agents.onchain_agent --port 3203

# Guru Cripto Agent
python -m criptokens.a2a.agents.guru_agent --port 3200
```

### Interacting with Agents

You can interact with the agents using the A2A client:

```python
from criptokens.a2a.common.client.client import A2AClient

# Create a client for the Guru Cripto agent
client = A2AClient(url="http://localhost:3200")

# Send a task to the agent
response = await client.send_task({
    "id": "task_1",
    "sessionId": "session_1",
    "message": {
        "role": "user",
        "parts": [{"type": "text", "text": "Predict Bitcoin price for the next week"}]
    }
})

# Print the response
print(response.result)
```

## Agent Capabilities

### Technical Analysis Agent

- Analyzes cryptocurrency price data using technical indicators
- Calculates moving averages, RSI, support/resistance levels, etc.
- Identifies trends and patterns in price data

### Sentiment Analysis Agent

- Analyzes sentiment of cryptocurrency news and social media
- Calculates Fear & Greed Index
- Tracks social media mentions and sentiment

### On-Chain Analysis Agent

- Analyzes on-chain data for cryptocurrencies
- Tracks whale transactions and wallet activity
- Monitors network metrics (fees, hashrate, etc.)

### Guru Cripto Agent

- Coordinates the specialized agents
- Integrates analyses from all agents
- Generates comprehensive predictions and recommendations

## Integration with Criptokens

The A2A implementation is integrated with the Criptokens frontend through the backend API. The frontend can send requests to the Guru Cripto agent via the backend, which will forward the requests to the appropriate agent and return the results.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
