import { initializeApp } from 'firebase/app';
import {
  getAuth,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import {
  getFirestore,
  collection,
  doc,
  setDoc,
  getDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  query,
  where,
  getDocs
} from 'firebase/firestore';
import { firebaseConfig } from '../config/firebase.config';

// La configuración de Firebase ahora se importa desde el archivo config/firebase.config.ts

// Inicializar Firebase
console.log('Inicializando Firebase con la configuración:', firebaseConfig);
const app = initializeApp(firebaseConfig);
console.log('Firebase inicializado correctamente');

// Inicializar servicios
const auth = getAuth(app);
console.log('Firebase Auth inicializado');

const db = getFirestore(app);
console.log('Firestore inicializado');

// Tipos para los datos de usuario
export interface UserData {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  createdAt: Date;
}

// Tipos para los activos de la cartera
export interface PortfolioAsset {
  id: string;
  symbol: string;
  name: string;
  amount: number;
  purchasePrice: number;
  purchaseDate: Date;
}

// Funciones de autenticación

// Registrar un nuevo usuario
export const registerUser = async (email: string, password: string, displayName: string): Promise<UserData> => {
  try {
    // Crear usuario en Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Actualizar el perfil con el nombre de usuario
    await updateProfile(user, { displayName });

    // Crear documento de usuario en Firestore
    const userData: UserData = {
      uid: user.uid,
      email: user.email || email,
      displayName: displayName,
      createdAt: new Date()
    };

    await setDoc(doc(db, 'Users', user.uid), userData);

    // Crear colección de cartera vacía para el usuario
    await setDoc(doc(db, 'Portafolio', user.uid), {
      assets: [],
      lastUpdated: new Date()
    });

    return userData;
  } catch (error: any) {
    console.error('Error al registrar usuario:', error);
    throw new Error(error.message);
  }
};

// Iniciar sesión
export const loginUser = async (email: string, password: string): Promise<User> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error: any) {
    console.error('Error al iniciar sesión:', error);
    throw new Error(error.message);
  }
};

// Cerrar sesión
export const logoutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error: any) {
    console.error('Error al cerrar sesión:', error);
    throw new Error(error.message);
  }
};

// Restablecer contraseña
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error: any) {
    console.error('Error al enviar correo de restablecimiento:', error);
    throw new Error(error.message);
  }
};

// Obtener usuario actual
export const getCurrentUser = (): User | null => {
  return auth.currentUser;
};

// Escuchar cambios en el estado de autenticación
export const onAuthStateChange = (callback: (user: User | null) => void): (() => void) => {
  return onAuthStateChanged(auth, callback);
};

// Funciones de la base de datos

// Obtener datos del usuario
export const getUserData = async (uid: string): Promise<UserData | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'Users', uid));
    if (userDoc.exists()) {
      return userDoc.data() as UserData;
    }
    return null;
  } catch (error: any) {
    console.error('Error al obtener datos del usuario:', error);
    throw new Error(error.message);
  }
};

// Obtener la cartera del usuario
export const getUserPortfolio = async (uid: string): Promise<PortfolioAsset[]> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));
    if (portfolioDoc.exists()) {
      const data = portfolioDoc.data();
      return data.assets || [];
    }
    return [];
  } catch (error: any) {
    console.error('Error al obtener cartera del usuario:', error);
    throw new Error(error.message);
  }
};

// Añadir activo a la cartera
export const addAssetToPortfolio = async (
  uid: string,
  asset: PortfolioAsset
): Promise<void> => {
  try {
    // Primero verificamos si el activo ya existe en la cartera
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const portfolio = portfolioDoc.data();
      const assets = portfolio.assets || [];
      const existingAssetIndex = assets.findIndex((a: PortfolioAsset) => a.id === asset.id);

      if (existingAssetIndex !== -1) {
        // Si el activo ya existe, actualizamos la cantidad y el precio promedio
        const existingAsset = assets[existingAssetIndex];
        const totalAmount = existingAsset.amount + asset.amount;
        const avgPrice = ((existingAsset.amount * existingAsset.purchasePrice) +
                         (asset.amount * asset.purchasePrice)) / totalAmount;

        assets[existingAssetIndex] = {
          ...existingAsset,
          amount: totalAmount,
          purchasePrice: avgPrice
        };

        await updateDoc(doc(db, 'Portafolio', uid), {
          assets: assets,
          lastUpdated: new Date()
        });
      } else {
        // Si es un nuevo activo, lo añadimos a la cartera
        await updateDoc(doc(db, 'Portafolio', uid), {
          assets: arrayUnion(asset),
          lastUpdated: new Date()
        });
      }
    }
  } catch (error: any) {
    console.error('Error al añadir activo a la cartera:', error);
    throw new Error(error.message);
  }
};

// Eliminar activo de la cartera
export const removeAssetFromPortfolio = async (
  uid: string,
  assetId: string
): Promise<void> => {
  try {
    const portfolioDoc = await getDoc(doc(db, 'Portafolio', uid));

    if (portfolioDoc.exists()) {
      const portfolio = portfolioDoc.data();
      const assets = portfolio.assets || [];
      const assetToRemove = assets.find((a: PortfolioAsset) => a.id === assetId);

      if (assetToRemove) {
        await updateDoc(doc(db, 'Portafolio', uid), {
          assets: assets.filter((a: PortfolioAsset) => a.id !== assetId),
          lastUpdated: new Date()
        });
      }
    }
  } catch (error: any) {
    console.error('Error al eliminar activo de la cartera:', error);
    throw new Error(error.message);
  }
};

// Actualizar la cartera completa
export const updateUserPortfolio = async (
  uid: string,
  assets: PortfolioAsset[]
): Promise<void> => {
  try {
    await updateDoc(doc(db, 'Portafolio', uid), {
      assets: assets,
      lastUpdated: new Date()
    });
  } catch (error: any) {
    console.error('Error al actualizar cartera del usuario:', error);
    throw new Error(error.message);
  }
};

export { auth, db };
