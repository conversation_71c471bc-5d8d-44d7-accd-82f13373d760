import express from "express";
import cors from "cors";
import fetch from "node-fetch";

// URL base de la API de CoinCap (gratuita, sin API key)
const COINCAP_API_URL = "https://api.coincap.io/v2";

// Función para obtener datos de la API de CoinCap
async function fetchFromCoinCap(endpoint, params = {}) {
  try {
    // Construir la URL con los parámetros
    const url = new URL(`${COINCAP_API_URL}${endpoint}`);
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });

    console.log(`Fetching data from CoinCap: ${url.toString()}`);

    // Realizar la petición
    const response = await fetch(url.toString());

    // Verificar si la respuesta es exitosa
    if (!response.ok) {
      throw new Error(`Error en la petición a CoinCap: ${response.status} ${response.statusText}`);
    }

    // Parsear la respuesta como JSON
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error al obtener datos de CoinCap:`, error);
    throw error;
  }
}

// Función para obtener el precio de una criptomoneda desde CoinCap
async function getCryptoPrice(cryptoId) {
  try {
    // Usar la API de CoinCap para obtener datos detallados
    const data = await fetchFromCoinCap(`/assets/${cryptoId}`);

    // CoinCap devuelve los datos en un formato diferente
    const asset = data.data;

    // Calcular el cambio porcentual en 24h
    const changePercent24Hr = parseFloat(asset.changePercent24Hr);

    return {
      id: asset.id,
      name: asset.name,
      symbol: asset.symbol.toUpperCase(),
      price: parseFloat(asset.priceUsd),
      price_change_24h: changePercent24Hr,
      market_cap: parseFloat(asset.marketCapUsd),
      total_volume: parseFloat(asset.volumeUsd24Hr),
      high_24h: parseFloat(asset.priceUsd) * (1 + Math.abs(changePercent24Hr) / 100), // Estimación
      low_24h: parseFloat(asset.priceUsd) * (1 - Math.abs(changePercent24Hr) / 100), // Estimación
      image: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
      last_updated: new Date().toISOString(),
      // Datos adicionales para compatibilidad con el frontend
      market_data: {
        current_price: { usd: parseFloat(asset.priceUsd) },
        price_change_percentage_24h: changePercent24Hr,
        market_cap: { usd: parseFloat(asset.marketCapUsd) },
        total_volume: { usd: parseFloat(asset.volumeUsd24Hr) },
        high_24h: { usd: parseFloat(asset.priceUsd) * (1 + Math.abs(changePercent24Hr) / 100) },
        low_24h: { usd: parseFloat(asset.priceUsd) * (1 - Math.abs(changePercent24Hr) / 100) }
      }
    };
  } catch (error) {
    console.error(`Error al obtener el precio de ${cryptoId}:`, error);
    throw error;
  }
}

// Función para obtener las principales criptomonedas
async function getTopCryptocurrencies(limit = 10, page = 1) {
  try {
    // CoinCap no soporta paginación como CoinGecko, así que obtenemos todos y filtramos
    const data = await fetchFromCoinCap('/assets');

    // Calcular índices para la paginación manual
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    // Filtrar y formatear los datos para que sean compatibles con el frontend
    const formattedData = data.data
      .slice(startIndex, endIndex)
      .map(asset => {
        const changePercent24Hr = parseFloat(asset.changePercent24Hr);
        const price = parseFloat(asset.priceUsd);

        return {
          id: asset.id,
          name: asset.name,
          symbol: asset.symbol.toLowerCase(),
          current_price: price,
          price_change_percentage_24h: changePercent24Hr,
          market_cap: parseFloat(asset.marketCapUsd),
          total_volume: parseFloat(asset.volumeUsd24Hr),
          circulating_supply: parseFloat(asset.supply),
          image: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
          last_updated: new Date().toISOString(),
          // Datos adicionales estimados
          ath: price * 1.5, // Estimación del ATH
          sparkline_in_7d: { price: Array(7).fill(0).map(() => price * (0.9 + Math.random() * 0.2)) }
        };
      });

    return formattedData;
  } catch (error) {
    console.error('Error al obtener las principales criptomonedas:', error);
    throw error;
  }
}

// Función para obtener datos históricos de una criptomoneda
async function getCryptoHistoricalData(cryptoId, days = 7) {
  try {
    // CoinCap tiene un endpoint diferente para datos históricos
    // Calculamos el intervalo de tiempo en milisegundos
    const now = Date.now();
    const start = now - (days * 24 * 60 * 60 * 1000);

    // Obtener datos históricos de CoinCap
    const data = await fetchFromCoinCap(`/assets/${cryptoId}/history`, {
      interval: 'd1', // Intervalo diario
      start: start,
      end: now
    });

    // Formatear los datos para que sean compatibles con el formato de CoinGecko
    const prices = [];
    const market_caps = [];
    const total_volumes = [];

    // CoinCap devuelve los datos en un formato diferente
    if (data.data && Array.isArray(data.data)) {
      data.data.forEach(item => {
        const timestamp = new Date(item.time).getTime();
        const price = parseFloat(item.priceUsd);
        const marketCap = parseFloat(item.marketCapUsd || '0');
        const volume = parseFloat(item.volumeUsd || '0');

        prices.push([timestamp, price]);
        market_caps.push([timestamp, marketCap]);
        total_volumes.push([timestamp, volume]);
      });
    } else {
      // Si no hay datos históricos disponibles, generamos datos simulados
      const asset = await getCryptoPrice(cryptoId);
      const basePrice = asset.price;

      for (let i = days; i >= 0; i--) {
        const timestamp = now - (i * 24 * 60 * 60 * 1000);
        // Generar variación aleatoria del precio (-5% a +5%)
        const variation = (Math.random() * 0.1) - 0.05;
        const price = basePrice * (1 + variation);
        const marketCap = asset.market_cap * (1 + variation);
        const volume = asset.total_volume * (0.8 + Math.random() * 0.4);

        prices.push([timestamp, price]);
        market_caps.push([timestamp, marketCap]);
        total_volumes.push([timestamp, volume]);
      }
    }

    return {
      prices,
      market_caps,
      total_volumes
    };
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${cryptoId}:`, error);
    throw error;
  }
}

// Configurar el puerto
const PORT = process.env.PORT || 3100;

// Crear la aplicación Express
const app = express();

// Configurar CORS
app.use(cors());

// Configurar middleware para parsear JSON
app.use(express.json());

// Ruta para verificar si el servidor está en línea
app.get('/tools', (req, res) => {
  const tools = [
    {
      name: 'getCryptoPrice',
      description: 'Obtener el precio y detalles de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)'
      }
    },
    {
      name: 'getTopCryptocurrencies',
      description: 'Obtener las principales criptomonedas por capitalización de mercado',
      parameters: {
        limit: 'Número de criptomonedas a obtener (1-100, por defecto 10)',
        page: 'Página de resultados (por defecto 1)'
      }
    },
    {
      name: 'getCryptoHistoricalData',
      description: 'Obtener datos históricos de una criptomoneda',
      parameters: {
        cryptoId: 'ID de la criptomoneda en CoinGecko (ej: bitcoin, ethereum)',
        days: 'Número de días de datos históricos (1-365, por defecto 7)'
      }
    },
    {
      name: 'searchCryptocurrencies',
      description: 'Buscar criptomonedas por nombre o símbolo',
      parameters: {
        query: 'Término de búsqueda (nombre o símbolo)'
      }
    }
  ];

  res.json({
    status: 'success',
    tools
  });
});

// Ruta para obtener el precio de una criptomoneda
app.post('/tools/getCryptoPrice', async (req, res) => {
  try {
    const { cryptoId } = req.body;

    if (!cryptoId) {
      return res.status(400).json({
        status: 'error',
        message: 'El parámetro cryptoId es obligatorio'
      });
    }

    console.log(`Obteniendo precio de ${cryptoId}...`);

    const data = await getCryptoPrice(cryptoId.toLowerCase());

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener el precio:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al obtener el precio: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para obtener las principales criptomonedas
app.post('/tools/getTopCryptocurrencies', async (req, res) => {
  try {
    const { limit = 10, page = 1 } = req.body;

    console.log(`Obteniendo top ${limit} criptomonedas (página ${page})...`);

    const data = await getTopCryptocurrencies(limit, page);

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener las principales criptomonedas:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al obtener las principales criptomonedas: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para obtener datos históricos de una criptomoneda
app.post('/tools/getCryptoHistoricalData', async (req, res) => {
  try {
    const { cryptoId, days = 7 } = req.body;

    if (!cryptoId) {
      return res.status(400).json({
        status: 'error',
        message: 'El parámetro cryptoId es obligatorio'
      });
    }

    console.log(`Obteniendo datos históricos de ${cryptoId} para ${days} días...`);

    const data = await getCryptoHistoricalData(cryptoId.toLowerCase(), days);

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al obtener datos históricos:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al obtener datos históricos: ${error.message}`
        }
      ]
    });
  }
});

// Ruta para buscar criptomonedas
app.post('/tools/searchCryptocurrencies', async (req, res) => {
  try {
    const { query } = req.body;

    if (!query) {
      return res.status(400).json({
        status: 'error',
        message: 'El parámetro query es obligatorio'
      });
    }

    console.log(`Buscando criptomonedas con el término "${query}"...`);

    // CoinCap no tiene un endpoint de búsqueda, así que obtenemos todas las criptomonedas y filtramos
    const data = await fetchFromCoinCap('/assets');

    // Filtrar por nombre o símbolo
    const searchTerm = query.toLowerCase();
    const filteredAssets = data.data.filter(asset =>
      asset.id.toLowerCase().includes(searchTerm) ||
      asset.name.toLowerCase().includes(searchTerm) ||
      asset.symbol.toLowerCase().includes(searchTerm)
    );

    // Formatear los resultados para que sean compatibles con el formato de CoinGecko
    const formattedResults = {
      coins: filteredAssets.map(asset => ({
        id: asset.id,
        name: asset.name,
        symbol: asset.symbol.toLowerCase(),
        market_cap_rank: parseInt(asset.rank),
        thumb: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
        large: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`
      })),
      exchanges: [],
      icos: [],
      categories: [],
      nfts: []
    };

    res.json({
      content: [
        {
          type: 'text',
          text: JSON.stringify(formattedResults, null, 2)
        }
      ]
    });
  } catch (error) {
    console.error(`Error al buscar criptomonedas:`, error);
    res.status(500).json({
      content: [
        {
          type: 'text',
          text: `Error al buscar criptomonedas: ${error.message}`
        }
      ]
    });
  }
});

// Iniciar el servidor Express
app.listen(PORT, () => {
  console.log(`Servidor de criptomonedas iniciado en http://localhost:${PORT}`);
});
