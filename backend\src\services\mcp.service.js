const axios = require('axios');
require('dotenv').config();

// URL del servidor MCP
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3101';

/**
 * Obtiene el precio de una criptomoneda específica
 * @param {string} cryptoId - ID de la criptomoneda
 * @returns {Promise<Object>} - Datos de la criptomoneda
 */
async function getCryptoPrice(cryptoId) {
  try {
    // Realizar la petición al servidor MCP
    const response = await axios.post(`${MCP_SERVER_URL}/tools/getCryptoPrice`, {
      cryptoId
    });

    // Extraer y parsear los datos
    if (response.data && response.data.content && response.data.content[0]) {
      const dataText = response.data.content[0].text;
      return JSON.parse(dataText);
    }

    throw new Error('Formato de respuesta inesperado del servidor MCP');
  } catch (error) {
    console.error(`Error al obtener datos de criptomoneda ${cryptoId}:`, error);
    return null;
  }
}

/**
 * Obtiene las principales criptomonedas por capitalización de mercado
 * @param {number} limit - Número de criptomonedas a obtener
 * @param {number} page - Página de resultados
 * @returns {Promise<Array>} - Lista de criptomonedas
 */
async function getTopCryptocurrencies(limit = 5, page = 1) {
  try {
    // Realizar la petición al servidor MCP
    const response = await axios.post(`${MCP_SERVER_URL}/tools/getTopCryptocurrencies`, {
      limit,
      page
    });

    // Extraer y parsear los datos
    if (response.data && response.data.content && response.data.content[0]) {
      const dataText = response.data.content[0].text;
      return JSON.parse(dataText);
    }

    throw new Error('Formato de respuesta inesperado del servidor MCP');
  } catch (error) {
    console.error(`Error al obtener las principales criptomonedas:`, error);
    return [];
  }
}

/**
 * Obtiene datos históricos de una criptomoneda
 * @param {string} cryptoId - ID de la criptomoneda
 * @param {number} days - Número de días de datos históricos
 * @returns {Promise<Object>} - Datos históricos de la criptomoneda
 */
async function getCryptoHistoricalData(cryptoId, days = 1) {
  try {
    // Realizar la petición al servidor MCP
    const response = await axios.post(`${MCP_SERVER_URL}/tools/getCryptoHistoricalData`, {
      cryptoId,
      days
    });

    // Extraer y parsear los datos
    if (response.data && response.data.content && response.data.content[0]) {
      const dataText = response.data.content[0].text;
      return JSON.parse(dataText);
    }

    throw new Error('Formato de respuesta inesperado del servidor MCP');
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${cryptoId}:`, error);
    return null;
  }
}

/**
 * Calcula un índice de sentimiento del mercado basado en los datos de las principales criptomonedas
 * @returns {Promise<Object>} - Datos de sentimiento del mercado
 */
async function getMarketSentimentIndex() {
  try {
    // Obtener las principales criptomonedas para calcular el sentimiento
    const topCryptos = await getTopCryptocurrencies(10);
    
    if (!topCryptos || topCryptos.length === 0) {
      throw new Error('No se pudieron obtener datos de criptomonedas para calcular el sentimiento');
    }
    
    // Calcular el cambio porcentual promedio
    const avgChange = topCryptos.reduce((sum, crypto) => 
      sum + crypto.price_change_percentage_24h, 0) / topCryptos.length;
    
    // Calcular un índice de miedo/codicia simple (0-100)
    // 0 = miedo extremo, 100 = codicia extrema
    const fearGreedIndex = Math.min(100, Math.max(0, 50 + avgChange * 2));
    
    // Determinar la tendencia del mercado
    let marketTrend = 'neutral';
    if (avgChange > 3) marketTrend = 'bullish';
    else if (avgChange < -3) marketTrend = 'bearish';
    
    // Calcular la dominancia de Bitcoin
    const totalMarketCap = topCryptos.reduce((sum, crypto) => sum + crypto.market_cap, 0);
    const btcData = topCryptos.find(c => c.id === 'bitcoin');
    const btcMarketCap = btcData ? btcData.market_cap : 0;
    const btcDominance = (btcMarketCap / totalMarketCap) * 100;
    
    // Determinar la clasificación del sentimiento
    let sentimentClassification = 'neutral';
    if (fearGreedIndex >= 75) sentimentClassification = 'codicia extrema';
    else if (fearGreedIndex >= 60) sentimentClassification = 'codicia';
    else if (fearGreedIndex <= 25) sentimentClassification = 'miedo extremo';
    else if (fearGreedIndex <= 40) sentimentClassification = 'miedo';
    
    return {
      fearGreedIndex,
      sentimentClassification,
      marketTrend,
      avgChange,
      btcDominance,
      topGainers: topCryptos.filter(c => c.price_change_percentage_24h > 0)
        .sort((a, b) => b.price_change_percentage_24h - a.price_change_percentage_24h)
        .slice(0, 3)
        .map(c => ({ 
          id: c.id, 
          symbol: c.symbol, 
          change: c.price_change_percentage_24h 
        })),
      topLosers: topCryptos.filter(c => c.price_change_percentage_24h < 0)
        .sort((a, b) => a.price_change_percentage_24h - b.price_change_percentage_24h)
        .slice(0, 3)
        .map(c => ({ 
          id: c.id, 
          symbol: c.symbol, 
          change: c.price_change_percentage_24h 
        }))
    };
  } catch (error) {
    console.error('Error al calcular el índice de sentimiento del mercado:', error);
    return {
      fearGreedIndex: 50,
      sentimentClassification: 'neutral',
      marketTrend: 'neutral',
      avgChange: 0,
      btcDominance: 50,
      topGainers: [],
      topLosers: []
    };
  }
}

module.exports = {
  getCryptoPrice,
  getTopCryptocurrencies,
  getCryptoHistoricalData,
  getMarketSentimentIndex
};
