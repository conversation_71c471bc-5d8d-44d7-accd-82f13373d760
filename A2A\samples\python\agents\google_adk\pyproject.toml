[project]
name = "a2a-sample-agent-adk"
version = "0.1.0"
description = "Sample Google ADK-based Expense Reimbursement agent hosted as an A2A server." 
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "a2a-samples",
    "click>=8.1.8",
    "google-adk>=0.0.3",
    "google-genai>=1.9.0",
    "python-dotenv>=1.1.0",
]

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.uv.sources]
a2a-samples = { workspace = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
