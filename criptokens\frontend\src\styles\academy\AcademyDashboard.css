.academy-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  font-size: 1.8rem;
  margin: 0;
  color: var(--text-primary);
}

.dashboard-actions {
  display: flex;
  gap: 1rem;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: var(--color-primary-transparent);
}

.dashboard-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.25rem;
  color: var(--text-secondary);
  font-size: 1rem;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: rgba(123, 97, 255, 0.1);
}

.tab-button.active {
  color: var(--color-primary);
  border-bottom: 3px solid var(--color-primary);
  font-weight: 500;
}

.dashboard-content {
  min-height: 400px;
}

/* Enrolled Courses */
.enrolled-courses {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.course-card.enrolled {
  display: flex;
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.course-card.enrolled:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.course-image {
  width: 30%;
  position: relative;
  background-color: var(--color-primary-transparent);
  min-height: 180px;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
}

.course-level {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.course-level.beginner {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.course-level.intermediate {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.course-level.advanced {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.course-content {
  width: 70%;
  padding: 1.5rem;
}

.course-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.course-progress {
  margin-bottom: 1.25rem;
}

.progress-bar {
  height: 8px;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 4px;
}

.progress-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.course-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.course-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-last-access {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 1.25rem;
}

.continue-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.continue-button:hover {
  background-color: var(--color-primary-dark);
}

/* Recommended Courses */
.recommended-courses {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.course-card.recommended {
  display: flex;
  flex-direction: column;
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  height: 100%;
}

.course-card.recommended:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.course-card.recommended .course-image {
  width: 100%;
  height: 180px;
}

.course-card.recommended .course-content {
  width: 100%;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.course-description {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
  flex-grow: 1;
}

.course-instructor {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.course-instructor img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.course-instructor span {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.view-course-button {
  background: none;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;
}

.view-course-button:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Certificates */
.certificates-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.certificate-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.certificate-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.certificate-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  flex-shrink: 0;
}

.certificate-content {
  flex: 1;
}

.certificate-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.certificate-content p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.certificate-actions {
  display: flex;
  gap: 1rem;
}

/* Empty States */
.no-courses,
.no-certificates {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.no-courses p,
.no-certificates p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Loading State */
.academy-dashboard.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(123, 97, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .recommended-courses {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .dashboard-tabs {
    flex-wrap: wrap;
  }
  
  .tab-button {
    flex: 1 0 calc(50% - 0.5rem);
  }
  
  .course-card.enrolled {
    flex-direction: column;
  }
  
  .course-card.enrolled .course-image,
  .course-card.enrolled .course-content {
    width: 100%;
  }
  
  .certificate-card {
    flex-direction: column;
    text-align: center;
  }
  
  .certificate-actions {
    justify-content: center;
  }
}
