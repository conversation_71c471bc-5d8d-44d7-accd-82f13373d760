/**
 * Configuración de Firebase para la aplicación
 */

// Importar las dependencias necesarias
const { initializeApp } = require('firebase/app');
const { getFirestore } = require('firebase/firestore');
const { getAuth } = require('firebase/auth');
const { getStorage } = require('firebase/storage');

// Configuración de Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDJQ1F-uSPjYsyt9ZxjIvd8QjGWqUCiSJ4",
  authDomain: "criptokens-app.firebaseapp.com",
  projectId: "criptokens-app",
  storageBucket: "criptokens-app.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef1234567890abcdef",
  measurementId: "G-ABCDEFGHIJ"
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);

// Obtener instancias de los servicios
const db = getFirestore(app);
const auth = getAuth(app);
const storage = getStorage(app);

// Exportar las instancias
module.exports = {
  app,
  db,
  auth,
  storage,
  firebaseConfig
};
