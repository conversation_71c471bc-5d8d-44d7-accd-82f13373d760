/* Estilos para la lista de activos del portafolio */

.portfolio-assets-list {
  width: 100%;
}

.assets-list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.search-container {
  position: relative;
}

.search-input {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  color: var(--text-bright);
  width: 100%;
  min-width: 250px;
  font-size: 0.9375rem;
}

.search-input::placeholder {
  color: var(--text-dim);
}

.search-container::before {
  content: '🔍';
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-dim);
  font-size: 0.875rem;
}

.assets-table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: var(--border-light);
}

.assets-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9375rem;
  background: var(--gradient-card);
  overflow: hidden;
}

.assets-table th {
  text-align: left;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-dim);
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

.assets-table th.sortable {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.assets-table th.sortable:hover {
  color: var(--text-bright);
  background-color: rgba(0, 0, 0, 0.3);
}

.assets-table td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-medium);
  vertical-align: middle;
}

.assets-table tr:last-child td {
  border-bottom: none;
}

.assets-table tr:hover td {
  background-color: rgba(255, 255, 255, 0.03);
}

.asset-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.asset-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
}

.asset-name-container {
  display: flex;
  flex-direction: column;
}

.asset-name {
  font-weight: 600;
  color: var(--text-bright);
}

.asset-symbol {
  font-size: 0.8125rem;
  color: var(--text-dim);
}

.positive {
  color: var(--success) !important;
}

.negative {
  color: var(--error) !important;
}

.percentage {
  font-size: 0.8125rem;
  margin-left: 0.5rem;
  opacity: 0.8;
}

.remove-asset-button {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.2);
  color: var(--error);
  border-radius: var(--radius-sm);
  padding: 0.5rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.remove-asset-button:hover {
  background: rgba(255, 0, 0, 0.2);
}

/* Estilos para la versión compacta */
.portfolio-assets-list.compact .assets-table td {
  padding: 0.75rem 1rem;
}

.portfolio-assets-list.compact .asset-icon {
  width: 1.5rem;
  height: 1.5rem;
}

/* Mensaje de activos vacíos */
.empty-assets-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.empty-assets-message p {
  margin: 0.5rem 0;
  color: var(--text-medium);
}

/* Responsive */
@media (max-width: 768px) {
  .assets-list-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .search-container {
    width: 100%;
  }
  
  .search-input {
    width: 100%;
  }
  
  .assets-table th,
  .assets-table td {
    padding: 0.75rem 1rem;
  }
}
