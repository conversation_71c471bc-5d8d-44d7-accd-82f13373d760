import React, { useState, useEffect } from 'react';
import { Box, Card, CardContent, Typography, Divider, Tabs, Tab, Grid } from '@mui/material';
import TechnicalAnalysisCard from '../TechnicalAnalysisCard';
import TechnicalAnalysisChart from './TechnicalAnalysisChart';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`technical-tabpanel-${index}`}
      aria-labelledby={`technical-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `technical-tab-${index}`,
    'aria-controls': `technical-tabpanel-${index}`,
  };
}

interface EnhancedTechnicalAnalysisProps {
  analysis: any;
  symbol: string;
}

const EnhancedTechnicalAnalysis: React.FC<EnhancedTechnicalAnalysisProps> = ({ analysis, symbol }) => {
  const [value, setValue] = useState(0);
  const [historicalData, setHistoricalData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  useEffect(() => {
    const fetchHistoricalData = async () => {
      if (!symbol) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`http://localhost:3001/api/crypto/historical/${symbol}`);
        
        if (!response.ok) {
          throw new Error(`Error al obtener datos históricos: ${response.statusText}`);
        }
        
        const data = await response.json();
        setHistoricalData(data);
      } catch (err) {
        console.error('Error al obtener datos históricos:', err);
        setError('No se pudieron cargar los datos históricos. Por favor, inténtalo de nuevo más tarde.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchHistoricalData();
  }, [symbol]);

  if (!analysis) return null;

  return (
    <Card sx={{ mb: 3, borderRadius: 2, boxShadow: 3 }}>
      <CardContent>
        <Typography variant="h5" component="h2" gutterBottom>
          Análisis Técnico de {symbol}
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={value} onChange={handleChange} aria-label="technical analysis tabs">
            <Tab label="Resumen" {...a11yProps(0)} />
            <Tab label="Gráfico" {...a11yProps(1)} />
            <Tab label="Detalles" {...a11yProps(2)} />
          </Tabs>
        </Box>
        
        <TabPanel value={value} index={0}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TechnicalAnalysisCard analysis={analysis} />
            </Grid>
            <Grid item xs={12} md={6}>
              {isLoading ? (
                <Typography>Cargando datos históricos...</Typography>
              ) : error ? (
                <Typography color="error">{error}</Typography>
              ) : (
                <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="h6" gutterBottom>
                    Recomendación: {analysis.signals.recommendation}
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {analysis.signals.recommendation === 'COMPRA' ? (
                      'Los indicadores técnicos sugieren una tendencia alcista. Considera una posición de compra, pero recuerda diversificar y gestionar el riesgo.'
                    ) : analysis.signals.recommendation === 'VENTA' ? (
                      'Los indicadores técnicos sugieren una tendencia bajista. Considera asegurar ganancias o limitar pérdidas, pero recuerda que el timing del mercado es difícil.'
                    ) : (
                      'Los indicadores técnicos muestran señales mixtas. Considera mantener posiciones actuales y esperar señales más claras antes de tomar decisiones.'
                    )}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Este análisis se basa en indicadores técnicos y no constituye asesoramiento financiero.
                  </Typography>
                </Box>
              )}
            </Grid>
          </Grid>
        </TabPanel>
        
        <TabPanel value={value} index={1}>
          {isLoading ? (
            <Typography>Cargando datos históricos...</Typography>
          ) : error ? (
            <Typography color="error">{error}</Typography>
          ) : (
            <TechnicalAnalysisChart analysis={analysis} historicalData={historicalData} />
          )}
        </TabPanel>
        
        <TabPanel value={value} index={2}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Indicadores Técnicos</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1">RSI (14): {analysis.indicators.rsi.toFixed(2)}</Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {analysis.indicators.rsi < 30 ? (
                    'El RSI indica condiciones de sobreventa. Históricamente, esto ha sido una señal de posible reversión alcista.'
                  ) : analysis.indicators.rsi > 70 ? (
                    'El RSI indica condiciones de sobrecompra. Históricamente, esto ha sido una señal de posible reversión bajista.'
                  ) : (
                    'El RSI está en un rango neutral, sin indicar condiciones extremas de mercado.'
                  )}
                </Typography>
                
                <Typography variant="subtitle1">MACD: {analysis.indicators.macd.macd.toFixed(2)}</Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {analysis.indicators.macd.macd > 0 ? (
                    'El MACD es positivo, indicando momentum alcista en el precio.'
                  ) : (
                    'El MACD es negativo, indicando momentum bajista en el precio.'
                  )}
                </Typography>
                
                <Typography variant="subtitle1">Bandas de Bollinger</Typography>
                <Typography variant="body2" color="text.secondary">
                  Superior: {analysis.indicators.bollingerBands.upper.toFixed(2)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Media: {analysis.indicators.bollingerBands.middle.toFixed(2)}
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Inferior: {analysis.indicators.bollingerBands.lower.toFixed(2)}
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Patrones y Señales</Typography>
              
              {/* Señales de compra */}
              {analysis.signals.details.buy.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" color="success.main">Señales de Compra</Typography>
                  <ul>
                    {analysis.signals.details.buy.map((signal: string, index: number) => (
                      <li key={`buy-${index}`}>
                        <Typography variant="body2">{signal}</Typography>
                      </li>
                    ))}
                  </ul>
                </Box>
              )}
              
              {/* Señales de venta */}
              {analysis.signals.details.sell.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" color="error.main">Señales de Venta</Typography>
                  <ul>
                    {analysis.signals.details.sell.map((signal: string, index: number) => (
                      <li key={`sell-${index}`}>
                        <Typography variant="body2">{signal}</Typography>
                      </li>
                    ))}
                  </ul>
                </Box>
              )}
              
              {/* Señales neutrales */}
              {analysis.signals.details.neutral.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" color="info.main">Señales Neutrales</Typography>
                  <ul>
                    {analysis.signals.details.neutral.map((signal: string, index: number) => (
                      <li key={`neutral-${index}`}>
                        <Typography variant="body2">{signal}</Typography>
                      </li>
                    ))}
                  </ul>
                </Box>
              )}
            </Grid>
          </Grid>
        </TabPanel>
      </CardContent>
    </Card>
  );
};

export default EnhancedTechnicalAnalysis;
