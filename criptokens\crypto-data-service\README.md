# Servicio de Datos de Criptomonedas

Este servicio obtiene datos de criptomonedas de la API de CoinGecko y los almacena en Firebase Firestore. También verifica alertas de precio y crea notificaciones cuando se cumplen las condiciones.

## Configuración

1. Instala las dependencias:
   ```
   npm install
   ```

2. Crea un archivo `firebase-credentials.json` con las credenciales de tu proyecto Firebase:
   - Ve a la consola de Firebase: https://console.firebase.google.com/
   - Selecciona tu proyecto
   - Ve a Configuración del proyecto > Cuentas de servicio
   - Haz clic en "Generar nueva clave privada"
   - Guarda el archivo JSON descargado como `firebase-credentials.json` en este directorio

## Uso

Para iniciar el servicio:
```
npm start
```

El servicio realizará las siguientes tareas:
- Obtener datos de las 100 principales criptomonedas de CoinGecko
- Obtener datos globales del mercado de criptomonedas
- Almacenar los datos en Firestore
- Verificar alertas de precio y crear notificaciones cuando se cumplan las condiciones
- Repetir este proceso cada 5 minutos

## Estructura de datos en Firestore

### Colección: crypto_data
- Documento: market_data
  - data: Array de datos de criptomonedas
  - global: Datos globales del mercado
  - last_updated: Fecha de la última actualización

### Colección: price_alerts
- Documentos de alertas de precio
  - userId: ID del usuario que creó la alerta
  - cryptoId: ID de la criptomoneda
  - cryptoName: Nombre de la criptomoneda
  - targetPrice: Precio objetivo
  - condition: Condición ('above' o 'below')
  - active: Estado de la alerta (true/false)
  - triggered: Si la alerta se ha activado (true/false)
  - createdAt: Fecha de creación
  - triggeredAt: Fecha de activación (si se ha activado)
  - triggerPrice: Precio al que se activó la alerta (si se ha activado)

### Colección: alert_notifications
- Documentos de notificaciones
  - userId: ID del usuario destinatario
  - type: Tipo de notificación ('price_alert')
  - title: Título de la notificación
  - message: Mensaje de la notificación
  - read: Si la notificación ha sido leída (true/false)
  - createdAt: Fecha de creación
  - alertId: ID de la alerta relacionada
  - cryptoId: ID de la criptomoneda
  - cryptoName: Nombre de la criptomoneda
  - targetPrice: Precio objetivo
  - currentPrice: Precio actual
  - condition: Condición ('above' o 'below')
