.app-header {
  background-color: #1a1a2e;
  color: white;
  padding: 0.75rem 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1440px;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #0084ff, #6c63ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-nav {
  display: flex;
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
}

.nav-links li {
  margin: 0 0.25rem;
}

.nav-links a {
  color: #e5e5ea;
  text-decoration: none;
  padding: 0.4rem 0.6rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.nav-links a:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-links a.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.15);
}

/* Estilos para las estadísticas de mercado */
.market-stats {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.5rem 0;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin-left: auto;
  margin-right: auto;
}

.market-stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.6);
}

.stat-value {
  font-weight: 500;
}

.stat-change {
  font-size: 0.75rem;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-weight: 500;
}

.stat-change.positive {
  color: #00b897;
  background-color: rgba(0, 184, 151, 0.1);
}

.stat-change.negative {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
}

.search-container {
  position: relative;
  margin-right: 1rem;
}

.search-container input {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 20px;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  color: white;
  width: 200px;
  transition: all 0.3s ease;
}

.search-container input:focus {
  background-color: rgba(255, 255, 255, 0.15);
  outline: none;
  width: 250px;
}

.search-container input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0;
}

.login-button {
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.login-button:hover {
  background-color: #0077e6;
}

/* Estilos para el menú de usuario */
.user-menu-container {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 20px;
  transition: background-color 0.2s ease;
}

.user-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 0.5rem;
  object-fit: cover;
}

.user-avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #0084ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 0.5rem;
  text-transform: uppercase;
}

.user-avatar-placeholder.large {
  width: 48px;
  height: 48px;
  font-size: 1.25rem;
}

.user-name {
  color: white;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 240px;
  z-index: 100;
  overflow: hidden;
}

.user-info {
  display: flex;
  padding: 1rem;
  background-color: #f8f9fa;
  align-items: center;
}

.user-avatar-large {
  margin-right: 0.75rem;
}

.user-avatar-large img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-display-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.8125rem;
  color: #666;
}

.user-dropdown-divider {
  height: 1px;
  background-color: #eee;
  margin: 0.25rem 0;
}

.user-dropdown-item {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: #333;
  font-size: 0.9375rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
}

.user-dropdown-item:hover {
  background-color: #f5f5f5;
}

.user-dropdown-item.logout {
  color: #d32f2f;
}

.user-dropdown-item.logout:hover {
  background-color: #ffebee;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 1rem;
}

.mobile-menu-toggle span {
  display: block;
  height: 3px;
  width: 100%;
  background-color: white;
  border-radius: 3px;
}

@media (max-width: 1200px) {
  .market-stats {
    order: 3;
    width: 100%;
    justify-content: center;
    padding: 0.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
  }
}

@media (max-width: 768px) {
  .main-nav {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background-color: #1a1a2e;
    height: 0;
    overflow: hidden;
    transition: height 0.3s ease;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }

  .main-nav.active {
    height: auto;
  }

  .nav-links {
    flex-direction: column;
    padding: 1rem;
  }

  .nav-links li {
    margin: 0.5rem 0;
  }

  .market-stats {
    display: none;
  }

  .search-container {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }
}
