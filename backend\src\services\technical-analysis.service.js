/**
 * Servicio de Análisis Técnico
 *
 * Este servicio se comunica con el MCP Technical Analysis para
 * obtener análisis técnico de criptomonedas.
 */

const axios = require('axios');
require('dotenv').config();

// URL del MCP Technical Analysis
const TECHNICAL_MCP_URL = process.env.TECHNICAL_MCP_URL || 'http://localhost:3104';

/**
 * Realiza un análisis técnico completo para una criptomoneda
 * @param {string} symbol - Símbolo de la criptomoneda (ej: BTC)
 * @param {string} interval - Intervalo de tiempo (ej: 1d, 4h, 1w)
 * @param {number} limit - Número de velas a analizar
 * @returns {Object} - Resultados del análisis técnico
 */
async function performTechnicalAnalysis(symbol, interval = '1d', limit = 30) {
  try {
    // Obtener datos históricos
    const historicalData = await getHistoricalData(symbol, interval, limit);

    // Convertir a formato de velas
    const candles = historicalData.map(item => ({
      open: parseFloat(item.open),
      high: parseFloat(item.high),
      low: parseFloat(item.low),
      close: parseFloat(item.close),
      volume: parseFloat(item.volume),
      timestamp: item.timestamp
    }));

    // Realizar análisis técnico
    const response = await axios.post(`${TECHNICAL_MCP_URL}/analysis/full`, { candles });

    return response.data;
  } catch (error) {
    console.error(`Error al realizar análisis técnico para ${symbol}:`, error);
    throw new Error(`Error al realizar análisis técnico: ${error.message}`);
  }
}

/**
 * Calcula un indicador técnico específico
 * @param {string} indicator - Nombre del indicador (rsi, macd, bollinger, sma, ema)
 * @param {Array<number>} prices - Array de precios de cierre
 * @param {Object} params - Parámetros adicionales
 * @returns {Object} - Resultados del cálculo
 */
async function calculateIndicator(indicator, prices, params = {}) {
  try {
    const validIndicators = ['rsi', 'macd', 'bollinger', 'sma', 'ema'];

    if (!validIndicators.includes(indicator)) {
      throw new Error(`Indicador no válido: ${indicator}`);
    }

    const response = await axios.post(`${TECHNICAL_MCP_URL}/indicators/${indicator}`, {
      prices,
      ...params
    });

    return response.data;
  } catch (error) {
    console.error(`Error al calcular indicador ${indicator}:`, error);
    throw new Error(`Error al calcular indicador: ${error.message}`);
  }
}

/**
 * Detecta patrones de velas
 * @param {Array<Object>} candles - Array de velas (OHLC)
 * @returns {Object} - Patrones detectados
 */
async function detectCandlePatterns(candles) {
  try {
    const response = await axios.post(`${TECHNICAL_MCP_URL}/patterns/detect`, { candles });
    return response.data;
  } catch (error) {
    console.error('Error al detectar patrones de velas:', error);
    throw new Error(`Error al detectar patrones: ${error.message}`);
  }
}

/**
 * Obtiene datos históricos de una criptomoneda
 * @param {string} symbol - Símbolo de la criptomoneda (ej: BTC)
 * @param {string} interval - Intervalo de tiempo (ej: 1d, 4h, 1w)
 * @param {number} limit - Número de velas a obtener
 * @returns {Array<Object>} - Datos históricos
 */
async function getHistoricalData(symbol, interval, limit) {
  try {
    // Intentar obtener datos del MCP Cripto
    try {
      const CRYPTO_MCP_URL = process.env.CRYPTO_MCP_URL || 'http://localhost:3101';
      const response = await axios.get(`${CRYPTO_MCP_URL}/historical`, {
        params: {
          symbol,
          interval,
          limit
        }
      });

      return response.data;
    } catch (error) {
      console.warn(`Error al obtener datos históricos del MCP Cripto: ${error.message}`);
      console.warn('Usando datos simulados...');

      // Si falla, generar datos simulados
      return generateSimulatedData(limit);
    }
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${symbol}:`, error);
    throw new Error(`Error al obtener datos históricos: ${error.message}`);
  }
}

/**
 * Genera datos históricos simulados
 * @param {number} count - Número de velas a generar
 * @returns {Array<Object>} - Datos históricos simulados
 */
function generateSimulatedData(count) {
  const data = [];
  let price = 10000 + Math.random() * 5000; // Precio inicial aleatorio

  const now = new Date();
  let timestamp = now.getTime();

  for (let i = 0; i < count; i++) {
    // Generar cambio de precio aleatorio (-5% a +5%)
    const change = price * (Math.random() * 0.1 - 0.05);

    // Calcular precios
    const close = price + change;
    const open = price;
    const high = Math.max(open, close) + Math.random() * Math.abs(change);
    const low = Math.min(open, close) - Math.random() * Math.abs(change);
    const volume = 1000000 + Math.random() * 9000000;

    // Añadir vela
    data.unshift({
      timestamp,
      open,
      high,
      low,
      close,
      volume
    });

    // Actualizar para la siguiente iteración
    price = close;
    timestamp -= 86400000; // Restar un día
  }

  return data;
}

module.exports = {
  performTechnicalAnalysis,
  calculateIndicator,
  detectCandlePatterns,
  getHistoricalData
};
