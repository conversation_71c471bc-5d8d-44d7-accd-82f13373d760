import { MCPServer } from "mcp-framework";
import ExampleTool from "./tools/ExampleTool";
import CalculatorTool from "./tools/CalculatorTool";

const server = new MCPServer({
  transport: {
    type: "http-stream",
    options: {
      port: 1337,
      cors: {
        allowOrigin: "*"
      }
    }
  }
});

// Registrar herramientas
server.registerTool(new ExampleTool());
server.registerTool(new CalculatorTool());

server.start();
