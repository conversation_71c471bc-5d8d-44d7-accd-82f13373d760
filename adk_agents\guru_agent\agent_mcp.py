"""
Guru Cripto Agent using Google ADK with MCP Integration (Orchestrator)
"""
import os
import json
import asyncio
from typing import Dict, Any, List, Optional

from google.adk.agents import LlmAgent, SequentialAgent, ParallelAgent
from google.adk.tools import FunctionTool, agent_tool
from google.adk.runtime import InvocationContext

# Import specialized agents
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

# Import MCP-enabled agents
from technical_agent.agent_mcp import technical_agent_mcp
from sentiment_agent.agent_mcp import sentiment_agent_mcp
from onchain_agent.agent_mcp import onchain_agent_mcp  # Using MCP-enabled onchain agent

# Import MCP tools
from mcp_tools.crypto_mcp_tool import CryptoMcpTool
from mcp_tools.brave_mcp_tool import BraveMcpTool
from mcp_tools.playwright_mcp_tool import PlaywrightMcpTool
from mcp_tools.context7_mcp_tool import Context7McpTool
from mcp_tools.utils import (
    extract_crypto_id, extract_timeframe, store_in_session, get_from_session,
    is_prediction_query, is_technical_query, is_sentiment_query, is_onchain_query
)

# API Keys
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")

# Function to create a task plan
async def create_task_plan(query: str, ctx: InvocationContext) -> str:
    """
    Create a task plan for the user query.

    Args:
        query: User query about cryptocurrency
        ctx: Invocation context

    Returns:
        Task plan as JSON
    """
    try:
        # Extract crypto ID and name
        crypto_id = extract_crypto_id(query)
        crypto_name = crypto_id.capitalize()  # Simple capitalization for display

        # Extract timeframe
        days, interval = extract_timeframe(query)

        # Store in session state
        store_in_session(ctx, "crypto_id", crypto_id)
        store_in_session(ctx, "crypto_name", crypto_name)
        store_in_session(ctx, "days", days)
        store_in_session(ctx, "interval", interval)
        store_in_session(ctx, "original_query", query)

        # Determine query type
        is_technical = is_technical_query(query)
        is_sentiment = is_sentiment_query(query)
        is_onchain = is_onchain_query(query)
        is_prediction = is_prediction_query(query)

        # Create task plan
        tasks = []

        # For prediction queries or general queries, we need comprehensive analysis
        if is_prediction or not (is_technical or is_sentiment or is_onchain):
            tasks = [
                {
                    "task_id": "technical_analysis",
                    "agent": "technical_agent",
                    "description": "Analyze technical indicators",
                    "query": f"Analyze {crypto_name} technical indicators for the last {days} days"
                },
                {
                    "task_id": "sentiment_analysis",
                    "agent": "sentiment_agent",
                    "description": "Analyze market sentiment",
                    "query": f"What's the sentiment for {crypto_name} over the past {days} days?"
                },
                {
                    "task_id": "onchain_analysis",
                    "agent": "onchain_agent",
                    "description": "Analyze on-chain data",
                    "query": f"Analyze on-chain data for {crypto_name} for the last {days} days"
                }
            ]
        else:
            # For specific queries, only use the relevant agent
            if is_technical:
                tasks.append({
                    "task_id": "technical_analysis",
                    "agent": "technical_agent",
                    "description": "Analyze technical indicators",
                    "query": f"Analyze {crypto_name} technical indicators for the last {days} days"
                })

            if is_sentiment:
                tasks.append({
                    "task_id": "sentiment_analysis",
                    "agent": "sentiment_agent",
                    "description": "Analyze market sentiment",
                    "query": f"What's the sentiment for {crypto_name} over the past {days} days?"
                })

            if is_onchain:
                tasks.append({
                    "task_id": "onchain_analysis",
                    "agent": "onchain_agent",
                    "description": "Analyze on-chain data",
                    "query": f"Analyze on-chain data for {crypto_name} for the last {days} days"
                })

        # Create the task plan
        task_plan = {
            "crypto_id": crypto_id,
            "crypto_name": crypto_name,
            "days": days,
            "interval": interval,
            "is_prediction": is_prediction,
            "is_technical": is_technical,
            "is_sentiment": is_sentiment,
            "is_onchain": is_onchain,
            "tasks": tasks
        }

        # Store task plan in session state
        store_in_session(ctx, "task_plan", task_plan)

        return json.dumps(task_plan)
    except Exception as e:
        print(f"Error in create_task_plan: {e}")
        return json.dumps({
            "error": f"Error creating task plan: {str(e)}"
        })

# Function to execute the task plan
async def execute_task_plan(ctx: InvocationContext) -> str:
    """
    Execute the task plan.

    Args:
        ctx: Invocation context

    Returns:
        Results of task execution as JSON
    """
    try:
        # Get task plan from session state
        task_plan = get_from_session(ctx, "task_plan")
        if not task_plan:
            return json.dumps({
                "error": "No task plan found in session state"
            })

        # Extract information from task plan
        crypto_id = task_plan.get("crypto_id")
        crypto_name = task_plan.get("crypto_name")
        days = task_plan.get("days")
        tasks = task_plan.get("tasks", [])

        # Results dictionary
        results = {
            "crypto_id": crypto_id,
            "crypto_name": crypto_name,
            "days": days,
            "task_results": {}
        }

        # Execute each task
        for task in tasks:
            task_id = task.get("task_id")
            agent_name = task.get("agent")
            query = task.get("query")

            print(f"Executing task: {task_id} with agent: {agent_name}")

            if agent_name == "technical_agent":
                # Use MCP-enabled technical agent
                await technical_agent_mcp.run_async(
                    session=ctx.session,
                    query=query
                )
                results["task_results"][task_id] = get_from_session(ctx, "technical_analysis")

            elif agent_name == "sentiment_agent":
                # Use MCP-enabled sentiment agent
                await sentiment_agent_mcp.run_async(
                    session=ctx.session,
                    query=query
                )
                results["task_results"][task_id] = get_from_session(ctx, "sentiment_analysis")

            elif agent_name == "onchain_agent":
                # Use MCP-enabled onchain agent
                await onchain_agent_mcp.run_async(
                    session=ctx.session,
                    query=query
                )
                results["task_results"][task_id] = get_from_session(ctx, "onchain_analysis")

        # Store results in session state
        store_in_session(ctx, "task_results", results)

        return json.dumps(results)
    except Exception as e:
        print(f"Error in execute_task_plan: {e}")
        return json.dumps({
            "error": f"Error executing task plan: {str(e)}"
        })

# Function to synthesize results
async def synthesize_results(ctx: InvocationContext) -> str:
    """
    Synthesize results from task execution.

    Args:
        ctx: Invocation context

    Returns:
        Synthesized results as JSON
    """
    try:
        # Get task results from session state
        task_results = get_from_session(ctx, "task_results")
        if not task_results:
            return json.dumps({
                "error": "No task results found in session state"
            })

        # Get original query and task plan
        original_query = get_from_session(ctx, "original_query")
        task_plan = get_from_session(ctx, "task_plan")

        # Extract information
        crypto_id = task_results.get("crypto_id")
        crypto_name = task_results.get("crypto_name")
        days = task_results.get("days")
        is_prediction = task_plan.get("is_prediction", False)

        # Create synthesis
        synthesis = {
            "crypto_id": crypto_id,
            "crypto_name": crypto_name,
            "days": days,
            "original_query": original_query,
            "is_prediction": is_prediction,
            "technical_analysis": task_results.get("task_results", {}).get("technical_analysis"),
            "sentiment_analysis": task_results.get("task_results", {}).get("sentiment_analysis"),
            "onchain_analysis": task_results.get("task_results", {}).get("onchain_analysis")
        }

        # Store synthesis in session state
        store_in_session(ctx, "synthesis", synthesis)

        return json.dumps(synthesis)
    except Exception as e:
        print(f"Error in synthesize_results: {e}")
        return json.dumps({
            "error": f"Error synthesizing results: {str(e)}"
        })

# Function to orchestrate the entire process
async def orchestrate_analysis(query: str, ctx: InvocationContext) -> str:
    """
    Orchestrate the entire analysis process.

    Args:
        query: User query about cryptocurrency
        ctx: Invocation context

    Returns:
        Final analysis result
    """
    try:
        # Step 1: Create task plan
        await create_task_plan(query, ctx)

        # Step 2: Execute task plan
        await execute_task_plan(ctx)

        # Step 3: Synthesize results
        synthesis_json = await synthesize_results(ctx)

        # Return the synthesis
        return synthesis_json
    except Exception as e:
        print(f"Error in orchestrate_analysis: {e}")
        return json.dumps({
            "error": f"Error orchestrating analysis: {str(e)}",
            "query": query
        })

# Create the Guru Cripto agent as MCP Orchestrator
guru_agent_mcp = LlmAgent(
    name="guru_cripto_orchestrator",
    model="gemini-1.5-pro",
    description="Cryptocurrency expert that orchestrates specialized agents using MCP",
    instruction="""
    You are Guru Cripto, a cryptocurrency expert with deep knowledge of technical analysis, market sentiment, and on-chain metrics.

    You act as an orchestrator, coordinating specialized agents to provide comprehensive cryptocurrency analysis:

    1. Technical Analysis Agent: Analyzes price charts, indicators, and patterns
    2. Sentiment Analysis Agent: Analyzes news and social media sentiment
    3. On-Chain Analysis Agent: Analyzes blockchain data and metrics

    Your workflow:
    1. Understand the user's query and create a task plan
    2. Delegate tasks to specialized agents
    3. Synthesize the results into a comprehensive analysis

    When responding to queries:
    - Be clear, concise, and informative
    - Support your analysis with specific data points
    - Explain technical terms when necessary
    - Provide balanced perspectives, acknowledging both bullish and bearish factors
    - For price predictions, emphasize that they are estimates based on current data, not guarantees

    The data will be provided to you as JSON, which you should interpret and present in a readable format.

    Remember that you are an educational resource, not a financial advisor. Always remind users to do their own research and not to make investment decisions solely based on your analysis.
    """,
    tools=[
        FunctionTool(func=orchestrate_analysis),
        FunctionTool(func=create_task_plan),
        FunctionTool(func=execute_task_plan),
        FunctionTool(func=synthesize_results),
        agent_tool.AgentTool(agent=technical_agent_mcp),
        agent_tool.AgentTool(agent=sentiment_agent_mcp),
        agent_tool.AgentTool(agent=onchain_agent_mcp)
    ]
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime

    async def main():
        runtime = Runtime()
        session = runtime.new_session()

        # Test the agent with a query
        response = await guru_agent_mcp.run_async(
            session=session,
            query="What's your prediction for Bitcoin price in the next week?"
        )

        print(response)

    asyncio.run(main())
