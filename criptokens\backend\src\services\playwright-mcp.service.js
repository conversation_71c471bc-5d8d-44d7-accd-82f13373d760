/**
 * Servicio para interactuar con el servidor Playwright-MCP
 *
 * Este servicio proporciona funciones para visualizar y analizar páginas web
 * utilizando el servidor Playwright-MCP.
 */
const axios = require('axios');

// URL del servidor Playwright-MCP
const PLAYWRIGHT_MCP_URL = process.env.PLAYWRIGHT_MCP_URL || 'http://localhost:3103';

// Almacenamiento de sesiones activas
const activeSessions = new Map();

/**
 * Crea una nueva sesión de navegación
 * @returns {Promise<string>} - ID de la sesión creada
 */
async function createSession() {
  try {
    console.log('Creando nueva sesión de navegación');

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/session`, {});

    if (response.data && response.data.sessionId) {
      const sessionId = response.data.sessionId;
      activeSessions.set(sessionId, { createdAt: Date.now() });
      return sessionId;
    }

    throw new Error('No se pudo crear una sesión');
  } catch (error) {
    console.error('Error al crear sesión de navegación:', error);
    return null;
  }
}

/**
 * Navega a una URL y obtiene un snapshot de la página
 * @param {string} url - URL de la página a visualizar
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Datos de la página visualizada
 */
async function browseWebPage(url, sessionId = null) {
  try {
    console.log(`Navegando a URL: ${url}`);

    // Si no se proporciona un ID de sesión, intentar usar uno existente o crear uno nuevo
    if (!sessionId) {
      // Usar la primera sesión activa o crear una nueva
      if (activeSessions.size > 0) {
        sessionId = Array.from(activeSessions.keys())[0];
      } else {
        sessionId = await createSession();
      }
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/browse`, {
      url,
      sessionId
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al navegar a la página web:', error);

    // Devolver datos simulados para pruebas
    return {
      url,
      title: 'Página Web Simulada',
      content: {
        plainText: 'Este es un texto simulado para pruebas. El servidor Playwright-MCP no está disponible.',
        links: []
      },
      summary: 'No se pudo cargar la página web. Esto es un contenido simulado para pruebas.',
      sessionId
    };
  }
}

/**
 * Toma una captura de pantalla de la página actual
 * @param {boolean} fullPage - Si es true, captura la página completa
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Datos de la captura de pantalla
 */
async function takeScreenshot(fullPage = false, sessionId = null) {
  try {
    console.log('Tomando captura de pantalla');

    // Si no se proporciona un ID de sesión, intentar usar uno existente
    if (!sessionId && activeSessions.size > 0) {
      sessionId = Array.from(activeSessions.keys())[0];
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/screenshot`, {
      fullPage,
      sessionId
    });

    return response.data;
  } catch (error) {
    console.error('Error al tomar captura de pantalla:', error);

    // Devolver datos simulados para pruebas
    return {
      screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAIAAADTED8xAAADMElEQVR4nOzVMQEAIAzAMMC/5yFjRxMFfXpnZg5Eve8A2GQA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwA2Q9hTQzDfzgDHAAAAABJRU5ErkJggg==',
      url: 'about:blank',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Hace clic en un elemento de la página
 * @param {string} selector - Selector CSS del elemento
 * @param {string} text - Texto del elemento (alternativa al selector)
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Datos de la página después del clic
 */
async function clickElement(selector = null, text = null, sessionId = null) {
  try {
    if (!selector && !text) {
      throw new Error('Se requiere un selector o texto para hacer clic');
    }

    console.log(`Haciendo clic en elemento: ${selector || `texto="${text}"`}`);

    // Si no se proporciona un ID de sesión, intentar usar uno existente
    if (!sessionId && activeSessions.size > 0) {
      sessionId = Array.from(activeSessions.keys())[0];
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/click`, {
      selector,
      text,
      sessionId
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: response.data.url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al hacer clic en elemento:', error);

    // Devolver datos simulados para pruebas
    return {
      url: 'about:blank',
      title: 'Error al hacer clic',
      content: {
        plainText: `No se pudo hacer clic en el elemento ${selector || text}. ${error.message}`
      },
      summary: 'No se pudo hacer clic en el elemento solicitado.',
      sessionId
    };
  }
}

/**
 * Navega hacia atrás en el historial
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Datos de la página anterior
 */
async function navigateBack(sessionId = null) {
  try {
    console.log('Navegando hacia atrás en el historial');

    // Si no se proporciona un ID de sesión, intentar usar uno existente
    if (!sessionId && activeSessions.size > 0) {
      sessionId = Array.from(activeSessions.keys())[0];
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/back`, {
      sessionId
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: response.data.url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al navegar hacia atrás:', error);

    // Devolver datos simulados para pruebas
    return {
      url: 'about:blank',
      title: 'Error al navegar hacia atrás',
      content: {
        plainText: `No se pudo navegar hacia atrás. ${error.message}`
      },
      summary: 'No se pudo navegar hacia atrás en el historial.',
      sessionId
    };
  }
}

/**
 * Navega hacia adelante en el historial
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Datos de la página siguiente
 */
async function navigateForward(sessionId = null) {
  try {
    console.log('Navegando hacia adelante en el historial');

    // Si no se proporciona un ID de sesión, intentar usar uno existente
    if (!sessionId && activeSessions.size > 0) {
      sessionId = Array.from(activeSessions.keys())[0];
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/forward`, {
      sessionId
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: response.data.url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al navegar hacia adelante:', error);

    // Devolver datos simulados para pruebas
    return {
      url: 'about:blank',
      title: 'Error al navegar hacia adelante',
      content: {
        plainText: `No se pudo navegar hacia adelante. ${error.message}`
      },
      summary: 'No se pudo navegar hacia adelante en el historial.',
      sessionId
    };
  }
}

/**
 * Visualiza una página web y obtiene datos completos
 * @param {string} url - URL de la página a visualizar
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Datos completos de la página visualizada
 */
async function visualizeWebPage(url, sessionId = null) {
  try {
    console.log(`Visualizando página web: ${url}`);

    // Crear una nueva sesión si no se proporciona una
    if (!sessionId) {
      sessionId = await createSession();
    }

    // Navegar a la página
    const pageData = await browseWebPage(url, sessionId);

    // Extraer y procesar los datos
    const extractedText = pageData.content?.plainText || pageData.content?.paragraphs?.join('\n') || 'No se pudo extraer texto de la página.';

    // Generar un resumen más informativo
    let summary = pageData.summary || '';

    // Si hay datos de criptomonedas, añadirlos al resumen
    if (pageData.content?.cryptoData) {
      const { price, priceChange, marketCap } = pageData.content.cryptoData;
      summary += ' Datos detectados: ';
      if (price) summary += `Precio: ${price}. `;
      if (priceChange) summary += `Cambio: ${priceChange}. `;
      if (marketCap) summary += `Capitalización: ${marketCap}.`;
    }

    // Combinar datos
    return {
      url: pageData.url || url,
      title: pageData.title || 'Página Web Visualizada',
      screenshot: pageData.screenshot,
      extractedText,
      summary,
      timestamp: new Date().toLocaleString(),
      sessionId
    };
  } catch (error) {
    console.error('Error al visualizar página web:', error);

    // Devolver datos simulados para pruebas
    return {
      url,
      title: 'Página Web Simulada',
      screenshot: 'iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAIAAADTED8xAAADMElEQVR4nOzVMQEAIAzAMMC/5yFjRxMFfXpnZg5Eve8A2GQA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwAmgFAMwBoBgDNAKAZADQDgGYA0AwA2Q9hTQzDfzgDHAAAAABJRU5ErkJggg==',
      extractedText: 'Este es un texto simulado para pruebas. El servidor Playwright-MCP no está disponible.',
      summary: `Esta es una página web simulada sobre ${url.includes('bitcoin') ? 'Bitcoin' : 'criptomonedas'}.`,
      timestamp: new Date().toLocaleString(),
      sessionId
    };
  }
}

/**
 * Rellena un campo de formulario
 * @param {string} selector - Selector CSS del campo
 * @param {string} value - Valor a introducir
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Resultado de la operación
 */
async function fillFormField(selector, value, sessionId = null) {
  try {
    if (!selector || value === undefined) {
      throw new Error('Se requieren selector y valor para rellenar el campo');
    }

    console.log(`Rellenando campo ${selector} con valor: ${value}`);

    // Si no se proporciona un ID de sesión, intentar usar uno existente
    if (!sessionId && activeSessions.size > 0) {
      sessionId = Array.from(activeSessions.keys())[0];
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/fill`, {
      selector,
      value,
      sessionId
    });

    return response.data;
  } catch (error) {
    console.error('Error al rellenar campo de formulario:', error);

    // Devolver datos simulados para pruebas
    return {
      success: false,
      message: `No se pudo rellenar el campo ${selector}. ${error.message}`,
      sessionId
    };
  }
}

/**
 * Obtiene el historial de navegación de una sesión
 * @param {string} sessionId - ID de sesión
 * @returns {Promise<Object>} - Historial de navegación
 */
async function getHistory(sessionId) {
  try {
    if (!sessionId) {
      throw new Error('Se requiere un ID de sesión');
    }

    console.log(`Obteniendo historial para sesión: ${sessionId}`);

    const response = await axios.get(`${PLAYWRIGHT_MCP_URL}/history/${sessionId}`);

    return response.data;
  } catch (error) {
    console.error('Error al obtener historial:', error);

    // Devolver datos simulados para pruebas
    return {
      history: [],
      currentIndex: -1
    };
  }
}

/**
 * Cierra una sesión de navegación
 * @param {string} sessionId - ID de sesión
 * @returns {Promise<Object>} - Resultado de la operación
 */
async function closeSession(sessionId) {
  try {
    if (!sessionId) {
      throw new Error('Se requiere un ID de sesión');
    }

    console.log(`Cerrando sesión: ${sessionId}`);

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/close`, {
      sessionId
    });

    // Eliminar la sesión del almacenamiento local
    if (activeSessions.has(sessionId)) {
      activeSessions.delete(sessionId);
    }

    return response.data;
  } catch (error) {
    console.error('Error al cerrar sesión:', error);

    // Eliminar la sesión del almacenamiento local de todos modos
    if (activeSessions.has(sessionId)) {
      activeSessions.delete(sessionId);
    }

    // Devolver datos simulados para pruebas
    return {
      success: false,
      message: `Error al cerrar sesión: ${error.message}`
    };
  }
}

module.exports = {
  createSession,
  browseWebPage,
  takeScreenshot,
  clickElement,
  navigateBack,
  navigateForward,
  fillFormField,
  getHistory,
  closeSession,
  visualizeWebPage
};
