#!/bin/bash

# Colores para la salida
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Función para manejar la terminación
cleanup() {
    echo -e "${RED}Deteniendo servidores MCP...${NC}"
    kill $CRYPTO_MCP_PID 2>/dev/null
    kill $BRAVE_SEARCH_PID 2>/dev/null
    kill $PLAYWRIGHT_MCP_PID 2>/dev/null
    exit 0
}

# Registrar la función de limpieza para señales de terminación
trap cleanup SIGINT SIGTERM

echo -e "${GREEN}=== INICIANDO SERVIDORES MCP ===${NC}"
echo ""

# Iniciar el servidor MCP de crypto
echo -e "${MAGENTA}Iniciando Crypto MCP Server...${NC}"
cd ../crypto-mcp-server
node http-server.js > /dev/null 2>&1 &
CRYPTO_MCP_PID=$!
cd - > /dev/null

# Esperar a que el servidor MCP se inicie
echo "Esperando a que el servidor MCP se inicie..."
sleep 2

# Iniciar el servidor Brave Search
echo -e "${BLUE}Iniciando Brave Search Server...${NC}"
cd ..
node brave-search-server.js > /dev/null 2>&1 &
BRAVE_SEARCH_PID=$!
cd - > /dev/null

# Iniciar el servidor Playwright MCP
echo -e "${CYAN}Iniciando Playwright MCP Server...${NC}"
cd ../playwright-mcp-server
node dist/server.js > /dev/null 2>&1 &
PLAYWRIGHT_MCP_PID=$!
cd - > /dev/null

# Mostrar mensaje de éxito
echo ""
echo -e "${GREEN}=== TODOS LOS SERVIDORES MCP INICIADOS ===${NC}"
echo "Servidores disponibles:"
echo -e "${MAGENTA}- Crypto MCP Server: http://localhost:3101${NC}"
echo -e "${BLUE}- Brave Search Server: http://localhost:3102${NC}"
echo -e "${CYAN}- Playwright MCP Server: http://localhost:3103${NC}"
echo ""
echo -e "${YELLOW}Presiona Ctrl+C para detener todos los servidores${NC}"

# Mantener el script en ejecución
wait
