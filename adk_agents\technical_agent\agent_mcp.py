"""
Technical Analysis Agent using Google ADK with MCP Integration
"""
import os
import json
import asyncio
from typing import Dict, Any, List, Optional

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from google.adk.runtime import InvocationContext

# Import MCP tools
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
from mcp_tools.crypto_mcp_tool import CryptoMcpTool
from mcp_tools.utils import extract_crypto_id, extract_timeframe, store_in_session, get_from_session

# Function tools for the agent
async def analyze_crypto_with_mcp(query: str, ctx: InvocationContext) -> str:
    """
    Analyze cryptocurrency based on user query using MCP tools.

    Args:
        query: User query about cryptocurrency
        ctx: Invocation context

    Returns:
        Analysis result
    """
    try:
        # Extract crypto ID and timeframe from query
        crypto_id = extract_crypto_id(query)
        days, interval = extract_timeframe(query)
        
        # Store in session state
        store_in_session(ctx, "crypto_id", crypto_id)
        store_in_session(ctx, "days", days)
        store_in_session(ctx, "interval", interval)
        
        # Create Crypto MCP tool
        crypto_tool = CryptoMcpTool()
        
        # Get historical data
        historical_data = await crypto_tool.get_historical_data(crypto_id, days, interval)
        
        # Get current price data
        price_data = await crypto_tool.get_price(crypto_id)
        
        # Calculate technical indicators
        indicators = calculate_indicators(historical_data, price_data)
        
        # Store results in session state
        store_in_session(ctx, "historical_data", historical_data)
        store_in_session(ctx, "price_data", price_data)
        store_in_session(ctx, "technical_indicators", indicators)
        
        # Close the MCP session
        await crypto_tool.close_session()
        
        # Return structured data for the LLM to format
        return json.dumps(indicators)
    except Exception as e:
        print(f"Error in analyze_crypto_with_mcp: {e}")
        # Return error message
        return json.dumps({
            "error": f"Error analyzing cryptocurrency: {str(e)}",
            "crypto_id": crypto_id,
            "days": days,
            "interval": interval
        })

def calculate_indicators(historical_data: Dict[str, Any], price_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate technical indicators from historical data.

    Args:
        historical_data: Historical price data
        price_data: Current price data

    Returns:
        Technical indicators
    """
    try:
        # Extract price data
        prices = historical_data.get("prices", [])
        if not prices:
            return {"error": "No historical data available"}
        
        # Extract closing prices (prices are [timestamp, price] pairs)
        closing_prices = [price[1] for price in prices]
        
        # Calculate simple moving averages
        sma_7 = sum(closing_prices[:7]) / 7 if len(closing_prices) >= 7 else None
        sma_14 = sum(closing_prices[:14]) / 14 if len(closing_prices) >= 14 else None
        sma_30 = sum(closing_prices[:30]) / 30 if len(closing_prices) >= 30 else None
        
        # Calculate price change percentages
        current_price = price_data.get("price", closing_prices[0] if closing_prices else 0)
        price_change_24h = price_data.get("price_change_24h", 0)
        
        price_change_7d = ((closing_prices[0] / closing_prices[6]) - 1) * 100 if len(closing_prices) >= 7 else None
        price_change_30d = ((closing_prices[0] / closing_prices[29]) - 1) * 100 if len(closing_prices) >= 30 else None
        
        # Calculate volatility (standard deviation of daily returns)
        daily_returns = []
        for i in range(1, len(closing_prices)):
            daily_return = (closing_prices[i-1] / closing_prices[i]) - 1
            daily_returns.append(daily_return)
        
        import numpy as np
        volatility = np.std(daily_returns) * 100 if daily_returns else None
        
        # Calculate RSI (Relative Strength Index)
        rsi = calculate_rsi(closing_prices, 14) if len(closing_prices) >= 14 else None
        
        # Determine trend
        trend = "neutral"
        if sma_7 and sma_30:
            if sma_7 > sma_30:
                trend = "bullish"
            elif sma_7 < sma_30:
                trend = "bearish"
        
        # Determine support and resistance levels
        support, resistance = calculate_support_resistance(prices)
        
        return {
            "current_price": current_price,
            "sma_7": sma_7,
            "sma_14": sma_14,
            "sma_30": sma_30,
            "price_change_24h": price_change_24h,
            "price_change_7d": price_change_7d,
            "price_change_30d": price_change_30d,
            "volatility": volatility,
            "rsi": rsi,
            "trend": trend,
            "support": support,
            "resistance": resistance
        }
    except Exception as e:
        print(f"Error analyzing technical indicators: {e}")
        return {"error": f"Error analyzing technical indicators: {str(e)}"}

def calculate_rsi(prices: list, period: int = 14) -> float:
    """Calculate the Relative Strength Index."""
    if len(prices) <= period:
        return 50  # Default neutral value
    
    # Calculate price changes
    deltas = [prices[i-1] - prices[i] for i in range(1, len(prices))]
    
    # Get gains and losses
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]
    
    # Calculate average gains and losses
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    if avg_loss == 0:
        return 100  # No losses, RSI is 100
    
    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def calculate_support_resistance(prices: list) -> tuple:
    """Calculate support and resistance levels."""
    # Extract price values (prices are [timestamp, price] pairs)
    price_values = [price[1] for price in prices]
    
    # Simple approach: support is recent low, resistance is recent high
    support = min(price_values[:14]) if len(price_values) >= 14 else min(price_values)
    resistance = max(price_values[:14]) if len(price_values) >= 14 else max(price_values)
    
    return support, resistance

# Create the technical analysis agent with MCP integration
technical_agent_mcp = LlmAgent(
    name="technical_analysis_agent",
    model="gemini-1.5-pro",
    description="Analyzes cryptocurrency price data using technical indicators with MCP integration",
    instruction="""
    You are a cryptocurrency technical analysis expert. Your task is to:

    1. Analyze the technical indicators provided to you
    2. Identify the current trend (bullish, bearish, neutral)
    3. Explain what the indicators suggest about future price movements
    4. Highlight key support and resistance levels
    5. Provide a clear, concise technical analysis summary

    When responding:
    - Be specific about what the indicators mean
    - Explain the significance of moving averages, RSI, and price changes
    - Mention support and resistance levels
    - Provide a conclusion about the overall technical outlook

    The technical indicators will be provided to you as JSON data.
    """,
    tools=[FunctionTool(func=analyze_crypto_with_mcp)],
    output_key="technical_analysis"
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime
    
    async def main():
        runtime = Runtime()
        session = runtime.new_session()
        
        # Test the agent with a query
        response = await technical_agent_mcp.run_async(
            session=session,
            query="Analyze Bitcoin technical indicators for the last week"
        )
        
        print(response)
    
    asyncio.run(main())
