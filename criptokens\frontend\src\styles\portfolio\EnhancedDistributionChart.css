/* Estilos para el componente de distribución mejorada del portafolio */

.enhanced-distribution-chart {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.enhanced-distribution-chart h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.view-mode-toggle {
  display: flex;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 0.25rem;
}

.view-mode-toggle button {
  background: transparent;
  border: none;
  color: var(--text-dim);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all var(--transition-normal);
}

.view-mode-toggle button:hover {
  color: var(--text-bright);
  background: rgba(255, 255, 255, 0.05);
}

.view-mode-toggle button.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-bright);
}

.chart-filters {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.sort-options {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-options label {
  color: var(--text-medium);
  font-size: 0.9rem;
}

.sort-options select {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-bright);
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.9rem;
  cursor: pointer;
}

.category-filter {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-filter label {
  color: var(--text-medium);
  font-size: 0.9rem;
}

.category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.category-buttons button {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  padding: 0.4rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.category-buttons button:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-bright);
}

.category-buttons button.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-bright);
  border-color: rgba(255, 255, 255, 0.2);
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

/* Estilos para el gráfico circular */
.pie-chart-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
}

.pie-chart {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  margin: 0 auto;
  background: rgba(0, 0, 0, 0.2);
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  clip-path: polygon(50% 50%, 50% 0%, calc(50% + 50% * sin(var(--rotation) + 3.6deg * var(--percentage))) calc(50% - 50% * cos(var(--rotation) + 3.6deg * var(--percentage))), 50% 50%);
  background-color: var(--color);
  transform: rotate(var(--rotation));
  transform-origin: center;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.pie-segment:hover, .pie-segment.selected {
  transform: rotate(var(--rotation)) scale(1.05);
  filter: brightness(1.2);
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-bright);
  text-align: center;
  font-size: 0.9rem;
  padding: 0.5rem;
}

.selected-asset-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.asset-symbol {
  font-weight: 600;
  font-size: 1rem;
}

.asset-percentage {
  font-size: 0.85rem;
  color: var(--text-medium);
  margin-top: 0.25rem;
}

.total-assets {
  font-size: 0.9rem;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 150px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.legend-item:hover, .legend-item.selected {
  background-color: rgba(255, 255, 255, 0.05);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-name {
  flex: 1;
  font-size: 0.9rem;
  color: var(--text-medium);
}

.legend-percentage {
  font-size: 0.9rem;
  color: var(--text-bright);
  font-weight: 500;
}

/* Estilos para el gráfico de barras */
.bar-chart-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.bar-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bar-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bar-item:hover, .bar-item.selected {
  background-color: rgba(255, 255, 255, 0.05);
}

.bar-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bar-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.bar-name {
  font-size: 0.9rem;
  color: var(--text-medium);
}

.bar-container {
  height: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.bar-value {
  font-size: 0.85rem;
  color: var(--text-bright);
  align-self: flex-end;
  margin-top: 0.25rem;
}

/* Detalles del activo seleccionado */
.asset-details {
  margin-top: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.asset-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.asset-details-header h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-bright);
}

.close-details {
  background: transparent;
  border: none;
  color: var(--text-dim);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-details:hover {
  color: var(--text-bright);
}

.asset-details-content {
  padding: 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.85rem;
  color: var(--text-dim);
}

.detail-value {
  font-size: 0.95rem;
  color: var(--text-bright);
  font-weight: 500;
}

/* Mensaje cuando no hay activos */
.no-assets-message {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-dim);
  text-align: center;
  padding: 2rem;
}

/* Estilos para el gráfico vacío */
.empty-chart {
  display: flex;
  flex-direction: column;
}

.empty-chart-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  padding: 2rem;
  text-align: center;
  min-height: 200px;
  margin-top: 1rem;
}

.empty-chart-message p {
  color: var(--text-dim);
  margin: 0.5rem 0;
}

/* Responsive */
@media (max-width: 768px) {
  .chart-filters {
    gap: 1rem;
  }
  
  .sort-options, .category-filter {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .category-buttons {
    width: 100%;
    justify-content: space-between;
  }
  
  .pie-chart {
    width: 180px;
    height: 180px;
  }
  
  .pie-center {
    width: 70px;
    height: 70px;
  }
  
  .asset-details-content {
    grid-template-columns: 1fr;
  }
}
