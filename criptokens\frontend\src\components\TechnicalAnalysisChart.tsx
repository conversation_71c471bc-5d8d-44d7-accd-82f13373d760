import React, { useState, useEffect, useRef } from 'react';
import anime from '../utils/simpleAnime';

interface TechnicalAnalysisChartProps {
  data: {
    labels: string[];
    values: number[];
  };
  cryptoId: string;
  cryptoName: string;
  cryptoSymbol: string;
  color?: string;
  height?: number;
  width?: number;
}

const TechnicalAnalysisChart: React.FC<TechnicalAnalysisChartProps> = ({
  data,
  cryptoId,
  cryptoName,
  cryptoSymbol,
  color = '#00e0ff',
  height = 300,
  width = 600
}) => {
  const [chartDimensions, setChartDimensions] = useState({ width, height });

  // Efecto para ajustar el tamaño del gráfico al tamaño del contenedor
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        const containerHeight = containerRef.current.clientHeight || 300;

        // Calcular dimensiones responsivas
        const calculatedWidth = containerWidth - 20; // Menos padding
        const calculatedHeight = Math.min(
          Math.max(200, containerHeight - 80), // Al menos 200px, restar espacio para controles
          calculatedWidth * 0.5 // Mantener proporción
        );

        setChartDimensions({
          width: calculatedWidth,
          height: calculatedHeight
        });
      }
    };

    handleResize(); // Llamar al inicio
    window.addEventListener('resize', handleResize);

    // Crear un ResizeObserver para detectar cambios en el tamaño del contenedor
    if (containerRef.current && window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(containerRef.current);
      return () => {
        resizeObserver.disconnect();
        window.removeEventListener('resize', handleResize);
      };
    }

    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const [activeIndicator, setActiveIndicator] = useState<string>('none');
  const [timeframe, setTimeframe] = useState<string>('1d');
  const [chartType, setChartType] = useState<string>('line');
  const chartRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Efecto para renderizar el gráfico
  useEffect(() => {
    if (chartRef.current && data.labels.length > 0 && data.values.length > 0) {
      renderChart();
    }
  }, [data, activeIndicator, timeframe, chartType]);

  // Función para renderizar el gráfico
  const renderChart = () => {
    if (!chartRef.current) return;

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // Limpiar el canvas
    ctx.clearRect(0, 0, chartRef.current.width, chartRef.current.height);

    // Configurar dimensiones
    const padding = { top: 40, right: 40, bottom: 40, left: 60 };
    const chartWidth = chartRef.current.width - padding.left - padding.right;
    const chartHeight = chartRef.current.height - padding.top - padding.bottom;

    // Encontrar valores mínimos y máximos
    const values = [...data.values];
    let minValue = Math.min(...values) * 0.995;
    let maxValue = Math.max(...values) * 1.005;

    // Añadir indicadores técnicos si están activos
    let smaValues: number[] = [];
    let bollingerUpper: number[] = [];
    let bollingerLower: number[] = [];
    let rsiValues: number[] = [];

    if (activeIndicator === 'sma' || activeIndicator === 'bollinger') {
      // Calcular SMA (Simple Moving Average) de 20 períodos
      smaValues = calculateSMA(values, 20);

      if (activeIndicator === 'bollinger') {
        // Calcular bandas de Bollinger (20 períodos, 2 desviaciones estándar)
        const bollingerBands = calculateBollingerBands(values, smaValues, 20, 2);
        bollingerUpper = bollingerBands.upper;
        bollingerLower = bollingerBands.lower;

        // Actualizar min/max para incluir las bandas
        minValue = Math.min(minValue, ...bollingerLower);
        maxValue = Math.max(maxValue, ...bollingerUpper);
      }
    } else if (activeIndicator === 'rsi') {
      // Calcular RSI (Relative Strength Index) de 14 períodos
      rsiValues = calculateRSI(values, 14);
    }

    // Calcular escalas
    const xScale = chartWidth / (data.labels.length - 1);
    const yScale = chartHeight / (maxValue - minValue);

    // Dibujar ejes
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;

    // Eje Y
    ctx.beginPath();
    ctx.moveTo(padding.left, padding.top);
    ctx.lineTo(padding.left, chartRef.current.height - padding.bottom);
    ctx.stroke();

    // Eje X
    ctx.beginPath();
    ctx.moveTo(padding.left, chartRef.current.height - padding.bottom);
    ctx.lineTo(chartRef.current.width - padding.right, chartRef.current.height - padding.bottom);
    ctx.stroke();

    // Dibujar líneas de cuadrícula
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';

    // Líneas horizontales
    const yTickCount = 5;
    const yTickStep = (maxValue - minValue) / yTickCount;

    for (let i = 0; i <= yTickCount; i++) {
      const value = minValue + i * yTickStep;
      const y = chartRef.current.height - padding.bottom - (value - minValue) * yScale;

      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(chartRef.current.width - padding.right, y);
      ctx.stroke();

      // Etiquetas del eje Y
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.font = '10px Arial';
      ctx.textAlign = 'right';
      ctx.textBaseline = 'middle';
      ctx.fillText(`$${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`, padding.left - 10, y);
    }

    // Líneas verticales (para fechas)
    const xTickCount = Math.min(7, data.labels.length);
    const xTickStep = Math.floor(data.labels.length / xTickCount);

    for (let i = 0; i < data.labels.length; i += xTickStep) {
      const x = padding.left + i * xScale;

      ctx.beginPath();
      ctx.moveTo(x, padding.top);
      ctx.lineTo(x, chartRef.current.height - padding.bottom);
      ctx.stroke();

      // Etiquetas del eje X
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      ctx.fillText(data.labels[i], x, chartRef.current.height - padding.bottom + 10);
    }

    // Dibujar el gráfico principal según el tipo seleccionado
    if (chartType === 'line') {
      // Dibujar línea de precio
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.beginPath();

      for (let i = 0; i < values.length; i++) {
        const x = padding.left + i * xScale;
        const y = chartRef.current.height - padding.bottom - (values[i] - minValue) * yScale;

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }

      ctx.stroke();

      // Añadir área bajo la curva
      ctx.fillStyle = `${color}20`; // Color con 20% de opacidad
      ctx.beginPath();

      // Empezar desde la esquina inferior izquierda
      ctx.moveTo(padding.left, chartRef.current.height - padding.bottom);

      // Dibujar la línea de nuevo
      for (let i = 0; i < values.length; i++) {
        const x = padding.left + i * xScale;
        const y = chartRef.current.height - padding.bottom - (values[i] - minValue) * yScale;

        if (i === 0) {
          ctx.lineTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }

      // Cerrar el path hasta la esquina inferior derecha
      ctx.lineTo(padding.left + (values.length - 1) * xScale, chartRef.current.height - padding.bottom);
      ctx.closePath();
      ctx.fill();
    } else if (chartType === 'candle') {
      // Simulación de datos de velas (en un caso real, estos datos vendrían de la API)
      const candleData = generateCandleData(values);

      // Dibujar velas
      const candleWidth = Math.min(8, xScale * 0.8);

      for (let i = 0; i < candleData.length; i++) {
        const candle = candleData[i];
        const x = padding.left + i * xScale;

        const openY = chartRef.current.height - padding.bottom - (candle.open - minValue) * yScale;
        const closeY = chartRef.current.height - padding.bottom - (candle.close - minValue) * yScale;
        const highY = chartRef.current.height - padding.bottom - (candle.high - minValue) * yScale;
        const lowY = chartRef.current.height - padding.bottom - (candle.low - minValue) * yScale;

        // Dibujar línea de máximo a mínimo
        ctx.strokeStyle = candle.close >= candle.open ? '#00ff9d' : '#ff3a6e';
        ctx.beginPath();
        ctx.moveTo(x, highY);
        ctx.lineTo(x, lowY);
        ctx.stroke();

        // Dibujar cuerpo de la vela
        ctx.fillStyle = candle.close >= candle.open ? '#00ff9d' : '#ff3a6e';
        const candleHeight = Math.abs(closeY - openY);
        const candleY = Math.min(openY, closeY);

        ctx.fillRect(x - candleWidth / 2, candleY, candleWidth, candleHeight);
      }
    }

    // Dibujar indicadores técnicos
    if (activeIndicator === 'sma' || activeIndicator === 'bollinger') {
      // Dibujar SMA
      ctx.strokeStyle = '#ffcc00';
      ctx.lineWidth = 2;
      ctx.beginPath();

      for (let i = 0; i < smaValues.length; i++) {
        const x = padding.left + (i + 19) * xScale; // Offset por el período de 20
        const y = chartRef.current.height - padding.bottom - (smaValues[i] - minValue) * yScale;

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }

      ctx.stroke();

      if (activeIndicator === 'bollinger') {
        // Dibujar bandas de Bollinger
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.lineWidth = 1;

        // Banda superior
        ctx.beginPath();
        for (let i = 0; i < bollingerUpper.length; i++) {
          const x = padding.left + (i + 19) * xScale;
          const y = chartRef.current.height - padding.bottom - (bollingerUpper[i] - minValue) * yScale;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.stroke();

        // Banda inferior
        ctx.beginPath();
        for (let i = 0; i < bollingerLower.length; i++) {
          const x = padding.left + (i + 19) * xScale;
          const y = chartRef.current.height - padding.bottom - (bollingerLower[i] - minValue) * yScale;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.stroke();

        // Área entre bandas
        ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.beginPath();

        // Dibujar desde la banda superior
        for (let i = 0; i < bollingerUpper.length; i++) {
          const x = padding.left + (i + 19) * xScale;
          const y = chartRef.current.height - padding.bottom - (bollingerUpper[i] - minValue) * yScale;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }

        // Continuar con la banda inferior en orden inverso
        for (let i = bollingerLower.length - 1; i >= 0; i--) {
          const x = padding.left + (i + 19) * xScale;
          const y = chartRef.current.height - padding.bottom - (bollingerLower[i] - minValue) * yScale;

          ctx.lineTo(x, y);
        }

        ctx.closePath();
        ctx.fill();
      }
    } else if (activeIndicator === 'rsi') {
      // Dibujar RSI en un panel separado
      const rsiHeight = 80;
      const rsiTop = chartRef.current.height - padding.bottom + 30;

      // Fondo del panel RSI
      ctx.fillStyle = 'rgba(15, 17, 35, 0.5)';
      ctx.fillRect(padding.left, rsiTop, chartWidth, rsiHeight);

      // Líneas de referencia RSI (30 y 70)
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
      ctx.lineWidth = 1;

      // Línea RSI 30
      const rsi30Y = rsiTop + rsiHeight * 0.7;
      ctx.beginPath();
      ctx.moveTo(padding.left, rsi30Y);
      ctx.lineTo(padding.left + chartWidth, rsi30Y);
      ctx.stroke();

      // Línea RSI 70
      const rsi70Y = rsiTop + rsiHeight * 0.3;
      ctx.beginPath();
      ctx.moveTo(padding.left, rsi70Y);
      ctx.lineTo(padding.left + chartWidth, rsi70Y);
      ctx.stroke();

      // Etiquetas RSI
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.font = '10px Arial';
      ctx.textAlign = 'right';
      ctx.textBaseline = 'middle';
      ctx.fillText('RSI', padding.left - 10, rsiTop + rsiHeight / 2);
      ctx.fillText('70', padding.left - 10, rsi70Y);
      ctx.fillText('30', padding.left - 10, rsi30Y);

      // Dibujar línea RSI
      ctx.strokeStyle = '#9d00ff';
      ctx.lineWidth = 2;
      ctx.beginPath();

      for (let i = 0; i < rsiValues.length; i++) {
        const x = padding.left + (i + 13) * xScale; // Offset por el período de 14
        const y = rsiTop + rsiHeight * (1 - rsiValues[i] / 100);

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }

      ctx.stroke();
    }

    // Título del gráfico
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.fillText(`${cryptoName} (${cryptoSymbol.toUpperCase()})`, padding.left, 15);

    // Información del indicador activo
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '12px Arial';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'top';

    let indicatorText = '';
    switch (activeIndicator) {
      case 'sma':
        indicatorText = 'SMA (20)';
        break;
      case 'bollinger':
        indicatorText = 'Bollinger Bands (20, 2σ)';
        break;
      case 'rsi':
        indicatorText = 'RSI (14)';
        break;
      default:
        indicatorText = 'Precio';
    }

    ctx.fillText(indicatorText, chartRef.current.width - padding.right, 15);
  };

  // Función para calcular SMA (Simple Moving Average)
  const calculateSMA = (data: number[], period: number): number[] => {
    const result: number[] = [];

    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }

    return result;
  };

  // Función para calcular bandas de Bollinger
  const calculateBollingerBands = (data: number[], sma: number[], period: number, multiplier: number): { upper: number[], lower: number[] } => {
    const upper: number[] = [];
    const lower: number[] = [];

    for (let i = 0; i < sma.length; i++) {
      const slice = data.slice(i, i + period);
      const mean = sma[i];

      // Calcular desviación estándar
      const squaredDiffs = slice.map(value => Math.pow(value - mean, 2));
      const variance = squaredDiffs.reduce((a, b) => a + b, 0) / period;
      const stdDev = Math.sqrt(variance);

      upper.push(mean + multiplier * stdDev);
      lower.push(mean - multiplier * stdDev);
    }

    return { upper, lower };
  };

  // Función para calcular RSI (Relative Strength Index)
  const calculateRSI = (data: number[], period: number): number[] => {
    const result: number[] = [];
    const gains: number[] = [];
    const losses: number[] = [];

    // Calcular ganancias y pérdidas
    for (let i = 1; i < data.length; i++) {
      const change = data[i] - data[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    // Calcular RS y RSI
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;

      const rs = avgGain / (avgLoss === 0 ? 0.001 : avgLoss); // Evitar división por cero
      const rsi = 100 - (100 / (1 + rs));

      result.push(rsi);
    }

    return result;
  };

  // Función para generar datos de velas simulados
  const generateCandleData = (prices: number[]): { open: number, high: number, low: number, close: number }[] => {
    const result = [];

    for (let i = 0; i < prices.length; i++) {
      const price = prices[i];
      const volatility = price * 0.02; // 2% de volatilidad

      const open = i === 0 ? price : result[i - 1].close;
      const close = price;
      const high = Math.max(open, close) + Math.random() * volatility;
      const low = Math.min(open, close) - Math.random() * volatility;

      result.push({ open, high, low, close });
    }

    return result;
  };

  return (
    <div className="technical-analysis-chart" ref={containerRef}>
      <div className="chart-controls">
        <div className="control-group">
          <label>Tipo:</label>
          <div className="button-group">
            <button
              className={`control-button ${chartType === 'line' ? 'active' : ''}`}
              onClick={() => setChartType('line')}
            >
              Línea
            </button>
            <button
              className={`control-button ${chartType === 'candle' ? 'active' : ''}`}
              onClick={() => setChartType('candle')}
            >
              Velas
            </button>
          </div>
        </div>

        <div className="control-group">
          <label>Indicador:</label>
          <div className="button-group">
            <button
              className={`control-button ${activeIndicator === 'none' ? 'active' : ''}`}
              onClick={() => setActiveIndicator('none')}
            >
              Ninguno
            </button>
            <button
              className={`control-button ${activeIndicator === 'sma' ? 'active' : ''}`}
              onClick={() => setActiveIndicator('sma')}
            >
              SMA
            </button>
            <button
              className={`control-button ${activeIndicator === 'bollinger' ? 'active' : ''}`}
              onClick={() => setActiveIndicator('bollinger')}
            >
              Bollinger
            </button>
            <button
              className={`control-button ${activeIndicator === 'rsi' ? 'active' : ''}`}
              onClick={() => setActiveIndicator('rsi')}
            >
              RSI
            </button>
          </div>
        </div>

        <div className="control-group">
          <label>Período:</label>
          <div className="button-group">
            <button
              className={`control-button ${timeframe === '1d' ? 'active' : ''}`}
              onClick={() => setTimeframe('1d')}
            >
              1D
            </button>
            <button
              className={`control-button ${timeframe === '1w' ? 'active' : ''}`}
              onClick={() => setTimeframe('1w')}
            >
              1S
            </button>
            <button
              className={`control-button ${timeframe === '1m' ? 'active' : ''}`}
              onClick={() => setTimeframe('1m')}
            >
              1M
            </button>
            <button
              className={`control-button ${timeframe === '3m' ? 'active' : ''}`}
              onClick={() => setTimeframe('3m')}
            >
              3M
            </button>
            <button
              className={`control-button ${timeframe === '1y' ? 'active' : ''}`}
              onClick={() => setTimeframe('1y')}
            >
              1A
            </button>
          </div>
        </div>
      </div>

      <div className="chart-container">
        <canvas
          ref={chartRef}
          width={chartDimensions.width}
          height={chartDimensions.height}
          style={{ width: '100%', height: '100%', maxWidth: '100%', maxHeight: '100%' }}
        />
      </div>

      <div className="chart-legend">
        {activeIndicator === 'sma' && (
          <div className="legend-item">
            <span className="legend-color" style={{ backgroundColor: '#ffcc00' }}></span>
            <span className="legend-text">SMA (20)</span>
          </div>
        )}

        {activeIndicator === 'bollinger' && (
          <>
            <div className="legend-item">
              <span className="legend-color" style={{ backgroundColor: '#ffcc00' }}></span>
              <span className="legend-text">SMA (20)</span>
            </div>
            <div className="legend-item">
              <span className="legend-color" style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)' }}></span>
              <span className="legend-text">Bandas de Bollinger (2σ)</span>
            </div>
          </>
        )}

        {activeIndicator === 'rsi' && (
          <div className="legend-item">
            <span className="legend-color" style={{ backgroundColor: '#9d00ff' }}></span>
            <span className="legend-text">RSI (14)</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default TechnicalAnalysisChart;
