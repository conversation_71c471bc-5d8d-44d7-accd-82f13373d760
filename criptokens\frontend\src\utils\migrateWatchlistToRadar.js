// Script para migrar datos de Watchlist a Radar en Firestore
import { db } from '../firebase-init';
import { collection, doc, getDoc, setDoc, getDocs } from 'firebase/firestore';

/**
 * Migra los datos de la colección Watchlist a la nueva estructura de Radar
 * Este script debe ejecutarse una sola vez para migrar los datos existentes
 */
export const migrateWatchlistToRadar = async () => {
  try {
    // Obtener todos los documentos de la colección Watchlist
    const watchlistSnapshot = await getDocs(collection(db, 'Watchlist'));

    // Iterar sobre cada documento (cada usuario)
    for (const watchlistDoc of watchlistSnapshot.docs) {
      const userId = watchlistDoc.id;
      const watchlistData = watchlistDoc.data();

      // Verificar si hay elementos en la watchlist
      if (watchlistData.items && watchlistData.items.length > 0) {
        // Crear la colección de radar para el usuario
        const radarCollection = collection(db, 'users', userId, 'radar');

        // Migrar cada elemento de la watchlist a la colección de radar
        for (const item of watchlistData.items) {
          // Crear un documento en la colección de radar con el ID de la criptomoneda
          await setDoc(doc(radarCollection, item.id), {
            id: item.id,
            symbol: item.symbol,
            name: item.name,
            addedAt: item.addedAt,
            imageUrl: item.imageUrl || null,
            notes: item.notes || '',
            category: item.category || 'other',
            isFavorite: item.isFavorite || false,
            customAlertPrice: item.customAlertPrice || null
          });
        }

        console.log(`Migración completada para el usuario ${userId}`);
      } else {
        console.log(`No hay elementos para migrar para el usuario ${userId}`);
      }
    }

    console.log('Migración completada con éxito');
    return true;
  } catch (error) {
    console.error('Error durante la migración:', error);
    return false;
  }
};

// Función para verificar si un usuario ya tiene datos en la colección de radar
export const hasRadarData = async (userId) => {
  try {
    const radarCollection = collection(db, 'users', userId, 'radar');
    const radarSnapshot = await getDocs(radarCollection);
    return !radarSnapshot.empty;
  } catch (error) {
    console.error('Error al verificar datos de radar:', error);
    return false;
  }
};

// Función para migrar datos de un solo usuario
export const migrateUserWatchlistToRadar = async (userId) => {
  try {
    // Verificar si el usuario ya tiene datos en la colección de radar
    const hasData = await hasRadarData(userId);
    if (hasData) {
      console.log(`El usuario ${userId} ya tiene datos en la colección de radar`);
      return false;
    }

    // Obtener el documento de watchlist del usuario
    const watchlistDoc = await getDoc(doc(db, 'Watchlist', userId));

    if (watchlistDoc.exists()) {
      const watchlistData = watchlistDoc.data();

      // Verificar si hay elementos en la watchlist
      if (watchlistData.items && watchlistData.items.length > 0) {
        // Crear la colección de radar para el usuario
        const radarCollection = collection(db, 'users', userId, 'radar');

        // Migrar cada elemento de la watchlist a la colección de radar
        for (const item of watchlistData.items) {
          // Crear un documento en la colección de radar con el ID de la criptomoneda
          await setDoc(doc(radarCollection, item.id), {
            id: item.id,
            symbol: item.symbol,
            name: item.name,
            addedAt: item.addedAt,
            imageUrl: item.imageUrl || null,
            notes: item.notes || '',
            category: item.category || 'other',
            isFavorite: item.isFavorite || false,
            customAlertPrice: item.customAlertPrice || null
          });
        }

        console.log(`Migración completada para el usuario ${userId}`);
        return true;
      } else {
        console.log(`No hay elementos para migrar para el usuario ${userId}`);
        return false;
      }
    } else {
      console.log(`No se encontró el documento de watchlist para el usuario ${userId}`);
      return false;
    }
  } catch (error) {
    console.error('Error durante la migración del usuario:', error);
    return false;
  }
};
