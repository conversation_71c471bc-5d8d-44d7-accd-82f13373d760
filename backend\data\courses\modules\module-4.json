{"id": "module-4", "title": "Wallets y Seguridad", "description": "Aprende a almacenar y proteger tus criptomonedas de manera segura utilizando diferentes tipos de wallets.", "order": 4, "lessons": [{"id": "lesson-4-1", "title": "Tipos de Wallets", "duration": 20, "order": 1, "content": "# Tipos de Wallets\n\nLas wallets (monederos) de criptomonedas son herramientas que permiten almacenar, enviar y recibir criptomonedas. En esta lección, exploraremos los diferentes tipos de wallets disponibles.\n\n## ¿Qué es una Wallet de Criptomonedas?\n\nUna wallet de criptomonedas no almacena realmente las criptomonedas (que existen solo en la blockchain), sino las claves criptográficas que te dan acceso a ellas:\n\n- **Clave privada**: Permite firmar transacciones y es lo que realmente \"posee\" tus criptomonedas\n- **Clave pública**: Se deriva de la clave privada y se usa para generar direcciones de recepción\n\n## Clasificación por Conectividad\n\n### Hot Wallets (Calientes)\n\nConectadas a internet, convenientes pero menos seguras.\n\n#### Ventajas:\n- Fácil acceso y uso\n- Convenientes para transacciones frecuentes\n- Generalmente gratuitas\n\n#### Desventajas:\n- Mayor vulnerabilidad a hackeos\n- Riesgo de malware y phishing\n- No recomendadas para grandes cantidades\n\n### Cold Wallets (Frías)\n\nNo conectadas a internet, más seguras pero menos convenientes.\n\n#### Ventajas:\n- Mayor seguridad contra ataques online\n- Ideal para almacenamiento a largo plazo\n- Protección contra malware\n\n#### Desventajas:\n- Menos convenientes para uso frecuente\n- Posible costo de adquisición\n- Riesgo de pérdida física\n\n## Tipos de Wallets por Implementación\n\n### 1. Wallets de Hardware\n\nDispositivos físicos especialmente diseñados para almacenar claves privadas de forma segura.\n\n#### Ejemplos:\n- Ledger Nano S/X\n- Trezor Model T/One\n- KeepKey\n\n#### Características:\n- Mantienen las claves privadas aisladas de internet\n- Requieren confirmación física para transacciones\n- Suelen tener respaldo con frase semilla\n- Costo aproximado: $50-200\n\n### 2. Wallets de Software\n\n#### Desktop Wallets\nAplicaciones instaladas en computadoras.\n\n**Ejemplos:**\n- Electrum (Bitcoin)\n- Exodus (Multi-cripto)\n- Bitcoin Core (Bitcoin, wallet completa)\n\n**Características:**\n- Control total sobre tus claves\n- Seguridad dependiente de la seguridad del computador\n- Algunas permiten conexión con hardware wallets\n\n#### Mobile Wallets\nAplicaciones para smartphones.\n\n**Ejemplos:**\n- Trust Wallet\n- Coinbase Wallet\n- BlueWallet (Bitcoin)\n\n**Características:**\n- Convenientes para uso diario\n- Muchas incluyen escaneo de códigos QR\n- Algunas ofrecen integración con DApps\n\n#### Web Wallets\nAccesibles a través de navegadores web.\n\n**Ejemplos:**\n- MetaMask (principalmente Ethereum)\n- MyEtherWallet\n- Interfaces web de exchanges\n\n**Características:**\n- Accesibles desde cualquier dispositivo con navegador\n- Convenientes pero generalmente menos seguras\n- Algunas como MetaMask funcionan como extensiones del navegador\n\n### 3. Wallets de Papel\n\nClaves privadas impresas físicamente en papel.\n\n#### Características:\n- Completamente offline\n- Inmunes a hackeos online\n- Vulnerables a daños físicos (agua, fuego)\n- Requieren importar la clave a una wallet digital para usar los fondos\n\n### 4. Wallets Cerebrales\n\nClaves privadas memorizadas en forma de frase o contraseña.\n\n#### Características:\n- No hay registro físico ni digital\n- Máxima portabilidad\n- Alto riesgo de pérdida por olvido\n- No recomendadas excepto para expertos\n\n## Wallets Custodiales vs. No-Custodiales\n\n### Wallets Custodiales\n\nUn tercero (como un exchange) mantiene las claves privadas por ti.\n\n#### Ventajas:\n- Fáciles de usar\n- Recuperación de contraseña posible\n- No hay riesgo de pérdida de claves\n\n#### Desventajas:\n- No tienes control real sobre tus criptomonedas\n- Vulnerables a hackeos del custodio\n- Posible congelamiento de fondos\n- \"Not your keys, not your coins\"\n\n### Wallets No-Custodiales\n\nTú mantienes el control total de tus claves privadas.\n\n#### Ventajas:\n- Control total sobre tus fondos\n- Independencia de terceros\n- Mayor privacidad\n\n#### Desventajas:\n- Responsabilidad total por la seguridad\n- Sin posibilidad de recuperación si pierdes las claves\n- Mayor complejidad de uso"}, {"id": "lesson-4-2", "title": "Creación y Gestión de Wallets", "duration": 25, "order": 2, "content": "# Creación y Gestión de Wallets\n\nEn esta lección, aprenderemos cómo crear y gestionar wallets de criptomonedas de manera segura y eficiente.\n\n## Creación de una Wallet\n\n### Proceso General\n\n1. **Selección del tipo de wallet** adecuado a tus necesidades\n2. **Instalación o adquisición** de la wallet\n3. **Configuración inicial** y creación de una nueva wallet\n4. **Respaldo de la frase semilla** (seed phrase)\n5. **Verificación del respaldo** para asegurar que está correcto\n\n### Frase Semilla (Seed Phrase)\n\n- También llamada frase mnemónica o palabras de recuperación\n- Generalmente 12 o 24 palabras en un orden específico\n- Permite recuperar todas tus claves privadas y fondos\n- Estándar BIP-39 utilizado por la mayoría de wallets\n\n**Ejemplo de frase semilla de 12 palabras:**\n```\nwagon glass favorite gentle response stamp artwork cancel long short episode release\n```\n\n### Contraseña Adicional (Passphrase)\n\n- Capa adicional de seguridad opcional\n- Actúa como \"palabra 13/25\" que no se almacena en el dispositivo\n- Crea una wallet completamente diferente con las mismas palabras semilla\n- Útil como protección contra robo físico o como \"wallet señuelo\"\n\n## Configuración de Wallets Específicas\n\n### Hardware Wallet (Ejemplo: Ledger Nano X)\n\n1. **Adquisición**: Comprar solo del fabricante oficial o distribuidores autorizados\n2. **Verificación**: Comprobar que el dispositivo no ha sido manipulado\n3. **Instalación**: Instalar Ledger Live en tu computadora/smartphone\n4. **Inicialización**: Seguir el asistente para crear una nueva wallet\n5. **Respaldo**: Anotar la frase semilla en la tarjeta proporcionada\n6. **Verificación**: Confirmar las palabras en el dispositivo\n7. **PIN**: Establecer un código PIN seguro\n\n### Software Wallet (Ejemplo: Exodus)\n\n1. **Descarga**: Obtener la aplicación desde el sitio web oficial\n2. **Instalación**: Seguir el asistente de instalación\n3. **Creación**: Iniciar la aplicación y crear una nueva wallet\n4. **Respaldo**: Anotar la frase semilla en un medio seguro\n5. **Contraseña**: Establecer una contraseña fuerte para la aplicación\n6. **Verificación**: Confirmar que puedes recibir y enviar una pequeña cantidad\n\n### Mobile Wallet (Ejemplo: Trust Wallet)\n\n1. **Descarga**: Instalar desde App Store/Google Play oficial\n2. **Creación**: Abrir la app y crear una nueva wallet\n3. **Respaldo**: Anotar la frase semilla en un medio físico seguro\n4. **Seguridad**: Activar autenticación biométrica si está disponible\n5. **Configuración**: Seleccionar las criptomonedas que deseas gestionar\n\n## Gestión Segura de Wallets\n\n### Respaldo de la Frase Semilla\n\n#### Métodos de Respaldo:\n\n- **Papel**: Escribir en papel de alta calidad y almacenar en lugar seguro\n- **Placa metálica**: Grabar en metal resistente al fuego y agua\n- **Múltiples copias**: Almacenar en diferentes ubicaciones físicas\n- **Fragmentación**: Dividir la frase en partes y almacenar por separado\n\n#### Qué NO hacer:\n\n- NO guardar en archivo digital o nube\n- NO tomar foto o screenshot\n- NO enviar por email o mensajería\n- NO compartir con nadie\n\n### Actualización y Mantenimiento\n\n- Mantener el software de la wallet actualizado\n- Verificar periódicamente que los respaldos siguen accesibles\n- Considerar migrar a nuevas wallets si la actual queda obsoleta\n- Revisar regularmente la actividad de la wallet\n\n### Consideraciones para Grandes Cantidades\n\n- Utilizar múltiples wallets para diversificar el riesgo\n- Implementar esquemas multifirma (multisig) cuando sea posible\n- Considerar soluciones de custodia institucional para montos muy grandes\n- Establecer un plan de herencia digital\n\n## Transacciones Seguras\n\n### Antes de Enviar\n\n1. **Verificar dirección**: Comprobar múltiples veces la dirección de destino\n2. **Comprobar red**: Asegurarse de usar la red correcta (especialmente para tokens)\n3. **Transacción de prueba**: Para montos grandes, enviar primero una cantidad pequeña\n4. **Revisar comisiones**: Verificar que las comisiones son apropiadas\n\n### Después de Enviar\n\n1. **Verificar hash**: Guardar el ID de transacción\n2. **Confirmar recepción**: Verificar en un explorador de blockchain\n3. **Documentar**: Mantener registro de transacciones importantes"}, {"id": "lesson-4-3", "title": "Mejores Prácticas de Seguridad", "duration": 20, "order": 3, "content": "# Mejores Prácticas de Seguridad\n\nProteger tus criptomonedas requiere un enfoque integral de seguridad. En esta lección, aprenderás las mejores prácticas para mantener tus activos digitales seguros.\n\n## Principios Fundamentales de Seguridad\n\n### 1. Seguridad por Capas\n\nImplementar múltiples capas de seguridad para que si una falla, otras sigan protegiendo tus activos.\n\n### 2. Principio del Menor Privilegio\n\nCada wallet o sistema debe tener acceso solo a los fondos que necesita para su propósito específico.\n\n### 3. Separación de Responsabilidades\n\nDistribuir claves y accesos entre diferentes personas o sistemas cuando sea posible.\n\n### 4. Actualización Regular\n\nMantener todos los sistemas y software actualizados con los últimos parches de seguridad.\n\n## Estrategias de Almacenamiento\n\n### Sistema de Tres Wallets\n\n1. **Wallet de Gastos Diarios** (Hot Wallet)\n   - Pequeñas cantidades para uso cotidiano\n   - En smartphone o computadora\n   - Ejemplo: 5% de tus fondos totales\n\n2. **Wallet de Ahorro** (Semi-Cold)\n   - Cantidades medianas, acceso ocasional\n   - Hardware wallet guardado en casa\n   - Ejemplo: 15% de tus fondos totales\n\n3. **Wallet de Reserva** (Deep Cold Storage)\n   - Grandes cantidades, acceso muy infrecuente\n   - Hardware wallet o multisig en ubicación segura\n   - Ejemplo: 80% de tus fondos totales\n\n### Almacenamiento en Frío Avanzado\n\n#### Multisig (Múltiples Firmas)\n\n- Requiere múltiples claves para autorizar transacciones (ej. 2 de 3)\n- Distribuye el riesgo entre múltiples dispositivos o personas\n- Ejemplos: Electrum multisig, Casa, Unchained Capital\n\n#### Bóvedas de Tiempo (Timelock)\n\n- Fondos bloqueados por un período específico\n- Proporciona tiempo para reaccionar ante intentos de robo\n- Implementable mediante contratos inteligentes o scripts de Bitcoin\n\n## Protección de la Frase Semilla\n\n### Métodos Físicos\n\n#### Placas Metálicas\n\n- Resistentes al fuego, agua y deterioro\n- Opciones: Cryptosteel, Billfodl, ColdTi, SimpleMetalSeed\n- Consideraciones: durabilidad, discreción, facilidad de uso\n\n#### Fragmentación Geográfica\n\n- Dividir la frase en múltiples partes (ej. 2 de 3 esquema Shamir)\n- Almacenar en diferentes ubicaciones físicas\n- Aumenta resistencia a desastres naturales o robos\n\n### Consideraciones Adicionales\n\n- **Testamento Digital**: Instrucciones para herederos sobre cómo acceder a tus criptomonedas\n- **Prueba de Vida**: Mecanismos para verificar que el acceso es legítimo\n- **Redundancia**: Múltiples copias en diferentes formatos y ubicaciones\n\n## Seguridad Operacional (OpSec)\n\n### Protección de Dispositivos\n\n- Usar sistemas operativos seguros (Linux, macOS)\n- Mantener software actualizado\n- Utilizar antivirus y firewall\n- Considerar sistemas dedicados solo para criptomonedas\n\n### Seguridad en Línea\n\n- Usar contraseñas únicas y fuertes para cada servicio\n- Implementar autenticación de dos factores (2FA)\n- Preferir autenticadores basados en app sobre SMS\n- Utilizar gestor de contraseñas (Bitwarden, 1Password, KeePass)\n- Usar VPN para conexiones públicas\n\n### Privacidad\n\n- Evitar revelar tenencias de criptomonedas\n- Considerar el uso de herramientas de privacidad (CoinJoin, Monero)\n- Usar diferentes direcciones para cada transacción\n- Tener cuidado con la información compartida en redes sociales\n\n## Amenazas Comunes y Cómo Evitarlas\n\n### Phishing\n\n- Verificar siempre URLs (bookmarks para sitios importantes)\n- No hacer clic en enlaces sospechosos\n- Verificar certificados SSL (candado en navegador)\n- Desconfiar de ofertas demasiado buenas\n\n### Malware\n\n- No descargar software de fuentes no oficiales\n- Verificar checksums de descargas\n- Usar hardware wallets para firmar transacciones\n- Considerar sistema operativo dedicado (Tails, Ubuntu)\n\n### Ataques de Ingeniería Social\n\n- Desconfiar de solicitudes urgentes o presión\n- Verificar identidades por canales alternativos\n- Nunca compartir frases semilla, incluso con \"soporte técnico\"\n- Establecer protocolos de comunicación con colaboradores\n\n### Ataques Físicos\n\n- Mantener discreción sobre tus tenencias\n- Considerar cámaras y alarmas para almacenamiento en casa\n- Usar cajas de seguridad bancarias para respaldos\n- Tener una \"wallet señuelo\" con pequeñas cantidades\n\n## Plan de Recuperación ante Desastres\n\n### Documentación\n\n- Inventario de wallets y activos\n- Instrucciones paso a paso para recuperación\n- Contactos de emergencia\n\n### Simulacros\n\n- Practicar recuperación de wallets periódicamente\n- Verificar que los respaldos funcionan correctamente\n- Actualizar procedimientos según sea necesario\n\n### Escenarios a Considerar\n\n- Pérdida o robo de dispositivos\n- Desastres naturales\n- Incapacidad personal\n- Fallecimiento (plan de herencia)"}], "quiz": {"id": "quiz-module-4", "title": "Evaluación: Wallets y Seguridad", "description": "Comprueba tu comprensión sobre wallets y seguridad de criptomonedas", "passingScore": 70, "questions": [{"id": "q1-m4", "question": "¿Cuál es la principal diferencia entre una 'hot wallet' y una 'cold wallet'?", "options": ["Las hot wallets son de pago y las cold wallets son gratuitas", "Las hot wallets están conectadas a internet mientras que las cold wallets no", "Las hot wallets solo funcionan en climas cálidos", "Las cold wallets solo pueden almacenar Bitcoin"], "correctAnswer": 1, "explanation": "La principal diferencia es la conectividad: las hot wallets están conectadas a internet (como aplicaciones móviles o web), lo que las hace convenientes pero más vulnerables a ataques online. Las cold wallets no están conectadas a internet (como hardware wallets o paper wallets), lo que las hace más seguras pero menos convenientes para uso frecuente."}, {"id": "q2-m4", "question": "¿Qué es una frase semilla (seed phrase)?", "options": ["Una contraseña para acceder a exchanges de criptomonedas", "Un conjunto de 12-24 palabras que permite recuperar todas las claves privadas de una wallet", "Un código QR que contiene tu dirección de Bitcoin", "Un software antivirus especializado para wallets de criptomonedas"], "correctAnswer": 1, "explanation": "Una frase semilla (también llamada frase mnemónica o palabras de recuperación) es un conjunto de 12-24 palabras en un orden específico que permite recuperar todas las claves privadas de una wallet. Es esencialmente un respaldo de tu wallet y debe mantenerse extremadamente segura, ya que cualquiera que tenga acceso a ella puede controlar tus fondos."}, {"id": "q3-m4", "question": "¿Cuál de las siguientes es la forma MÁS segura de almacenar tu frase semilla?", "options": ["En un archivo de texto encriptado en tu computadora", "En una nota en tu smartphone", "Grabada en una placa metálica resistente al fuego guardada en un lugar seguro", "En un correo electrónico enviado a ti mismo"], "correctAnswer": 2, "explanation": "Grabar tu frase semilla en una placa metálica resistente al fuego y agua, y guardarla en un lugar seguro, es la opción más segura. Las opciones digitales (archivo de texto, nota en smartphone, correo electrónico) son vulnerables a hackeos, malware y acceso no autorizado. El almacenamiento físico offline, especialmente en materiales duraderos, ofrece la mejor protección."}, {"id": "q4-m4", "question": "¿Qué significa la frase 'Not your keys, not your coins'?", "options": ["<PERSON> pierdes tus claves privadas, pierdes tus criptomonedas", "Si no controlas tus claves privadas (como en exchanges), no tienes control real sobre tus criptomonedas", "Las criptomonedas solo pueden ser usadas por quienes tienen llaves físicas especiales", "Las criptomonedas no son realmente tuyas hasta que las vendes por dinero fiat"], "correctAnswer": 1, "explanation": "La frase 'Not your keys, not your coins' significa que si no controlas tus claves privadas (como cuando mantienes criptomonedas en un exchange o wallet custodial), no tienes control real sobre tus activos. El tercero que custodia tus claves podría restringir tu acceso, ser hackeado, o incluso quebrar, poniendo en riesgo tus fondos."}, {"id": "q5-m4", "question": "¿Qué es una wallet multisig (múltiples firmas)?", "options": ["Una wallet que puede almacenar múltiples tipos de criptomonedas", "Una wallet que requiere múltiples claves para autorizar transacciones", "Una wallet que genera una nueva dirección para cada transacción", "Una wallet que permite firmar transacciones desde múltiples dispositivos"], "correctAnswer": 1, "explanation": "Una wallet multisig (múltiples firmas) requiere múltiples claves para autorizar transacciones, típicamente en un esquema como '2 de 3' donde se necesitan al menos 2 de 3 claves posibles. Esto distribuye el riesgo y aumenta la seguridad, ya que un atacante necesitaría comprometer múltiples dispositivos o ubicaciones para acceder a los fondos."}]}}