const express = require('express');
const router = express.Router();
const axios = require('axios');
const NodeCache = require('node-cache');

// Crear caché con tiempo de vida de 5 minutos
const cache = new NodeCache({ stdTTL: 300 });

/**
 * @route GET /api/crypto/historical/:symbol
 * @description Obtiene datos históricos de una criptomoneda
 * @param {string} symbol - Símbolo de la criptomoneda (ej. BTC, ETH)
 * @param {number} days - Número de días de datos históricos (por defecto 30)
 * @access Public
 */
router.get('/historical/:symbol', async (req, res) => {
  try {
    const { symbol } = req.params;
    const days = req.query.days || 30;
    
    // Clave de caché única para esta combinación de símbolo y días
    const cacheKey = `historical_${symbol.toUpperCase()}_${days}`;
    
    // Verificar si los datos están en caché
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Usando datos en caché para ${symbol} (${days} días)`);
      return res.json(cachedData);
    }
    
    // Si no están en caché, obtener de la API
    console.log(`Obteniendo datos históricos para ${symbol} (${days} días)`);
    
    // Usar CoinCap API para obtener datos históricos
    const apiKey = process.env.COINCAP_API_KEY || '37f9968e-6ab7-431f-80d7-0ac6686319f3';
    const interval = days <= 1 ? 'm5' : days <= 7 ? 'h1' : 'd1'; // Ajustar intervalo según el período
    
    // Calcular timestamp de inicio (días atrás en milisegundos)
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);
    
    const response = await axios.get(`https://api.coincap.io/v2/assets/${symbol.toLowerCase()}/history`, {
      params: {
        interval,
        start: startTime,
        end: Date.now()
      },
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    // Transformar los datos al formato esperado
    const historicalData = response.data.data.map(item => ({
      timestamp: item.time,
      price: parseFloat(item.priceUsd),
      volume: parseFloat(item.volumeUsd || 0),
      date: new Date(item.time).toISOString()
    }));
    
    // Guardar en caché
    cache.set(cacheKey, historicalData);
    
    res.json(historicalData);
  } catch (error) {
    console.error('Error al obtener datos históricos:', error.message);
    
    // Si hay un error, intentar generar datos simulados
    try {
      const { symbol } = req.params;
      const days = req.query.days || 30;
      
      console.log(`Generando datos simulados para ${symbol} (${days} días)`);
      
      // Generar datos simulados
      const simulatedData = generateSimulatedHistoricalData(symbol, parseInt(days));
      
      res.json(simulatedData);
    } catch (simError) {
      console.error('Error al generar datos simulados:', simError.message);
      res.status(500).json({ error: 'Error al obtener datos históricos' });
    }
  }
});

/**
 * Genera datos históricos simulados para una criptomoneda
 * @param {string} symbol - Símbolo de la criptomoneda
 * @param {number} days - Número de días
 * @returns {Array} - Datos históricos simulados
 */
function generateSimulatedHistoricalData(symbol, days) {
  // Precios base para diferentes criptomonedas
  const basePrices = {
    'btc': 50000,
    'eth': 3000,
    'usdt': 1,
    'bnb': 400,
    'sol': 100,
    'xrp': 0.5,
    'ada': 0.4,
    'doge': 0.1,
    'dot': 10,
    'ltc': 100
  };
  
  // Precio base para la criptomoneda (o un valor predeterminado si no está en la lista)
  const basePrice = basePrices[symbol.toLowerCase()] || 100;
  
  // Generar datos para cada día
  const data = [];
  const now = Date.now();
  const dayInMs = 24 * 60 * 60 * 1000;
  
  // Volatilidad (mayor para BTC y ETH)
  const volatility = ['btc', 'eth'].includes(symbol.toLowerCase()) ? 0.05 : 0.03;
  
  let currentPrice = basePrice;
  
  for (let i = days; i >= 0; i--) {
    const timestamp = now - (i * dayInMs);
    
    // Simular cambio de precio con tendencia y volatilidad
    const change = (Math.random() - 0.5) * volatility * currentPrice;
    currentPrice += change;
    
    // Asegurar que el precio no sea negativo
    if (currentPrice <= 0) currentPrice = basePrice * 0.01;
    
    // Simular volumen
    const volume = basePrice * 1000000 * (0.5 + Math.random());
    
    data.push({
      timestamp,
      price: currentPrice,
      volume,
      date: new Date(timestamp).toISOString()
    });
  }
  
  return data;
}

module.exports = router;
