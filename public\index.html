<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cryptokens</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Roboto', sans-serif;
            background-color: #0a0a1a;
            color: #e0e0ff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .app {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            border-bottom: 1px solid rgba(64, 220, 255, 0.3);
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(90deg, #00f2ff, #4657ce);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 3s infinite;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #a0a0ff;
        }
        
        .loading {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 300px;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(64, 220, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00f2ff;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .crypto-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .crypto-card {
            background: rgba(26, 26, 58, 0.7);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(64, 220, 255, 0.2);
            animation: fadeIn 0.5s ease forwards;
        }
        
        .crypto-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            border-color: rgba(64, 220, 255, 0.5);
        }
        
        .crypto-card h2 {
            font-size: 1.5rem;
            margin-bottom: 5px;
            color: #ffffff;
        }
        
        .crypto-card .symbol {
            font-size: 0.9rem;
            color: #a0a0ff;
            margin-bottom: 15px;
        }
        
        .crypto-card .price {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .crypto-card .change {
            font-size: 1rem;
            margin-bottom: 20px;
        }
        
        .crypto-card .positive {
            color: #00ff9d;
        }
        
        .crypto-card .negative {
            color: #ff5e5e;
        }
        
        .crypto-card button {
            background: linear-gradient(90deg, #4657ce, #00f2ff);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .crypto-card button:hover {
            background: linear-gradient(90deg, #00f2ff, #4657ce);
            transform: scale(1.05);
        }
        
        .counter-section {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: rgba(26, 26, 58, 0.5);
            border-radius: 10px;
            border: 1px solid rgba(64, 220, 255, 0.2);
        }
        
        .counter-section p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .counter-section button {
            background: linear-gradient(90deg, #4657ce, #00f2ff);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .counter-section button:hover {
            background: linear-gradient(90deg, #00f2ff, #4657ce);
            transform: scale(1.05);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes glow {
            0% { text-shadow: 0 0 5px rgba(0, 242, 255, 0.5); }
            50% { text-shadow: 0 0 20px rgba(0, 242, 255, 0.8); }
            100% { text-shadow: 0 0 5px rgba(0, 242, 255, 0.5); }
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="header">
            <h1>Criptokens</h1>
            <p>Tu plataforma de criptomonedas</p>
        </header>

        <main id="main-content">
            <div class="loading">
                <div class="spinner"></div>
                <p>Cargando datos...</p>
            </div>
        </main>

        <div class="counter-section">
            <p>Contador de visitas: <span id="counter">0</span></p>
            <button id="increment-btn">Incrementar</button>
        </div>
    </div>

    <script>
        // Datos simulados de criptomonedas
        const cryptoData = [
            { id: 'bitcoin', name: 'Bitcoin', symbol: 'BTC', price: 63245.32, change24h: 2.5 },
            { id: 'ethereum', name: 'Ethereum', symbol: 'ETH', price: 3089.45, change24h: 1.8 },
            { id: 'ripple', name: 'Ripple', symbol: 'XRP', price: 0.52, change24h: -0.7 },
            { id: 'cardano', name: 'Cardano', symbol: 'ADA', price: 0.43, change24h: 0.3 },
        ];

        // Contador
        let count = 0;
        const counterElement = document.getElementById('counter');
        const incrementBtn = document.getElementById('increment-btn');
        
        incrementBtn.addEventListener('click', () => {
            count++;
            counterElement.textContent = count;
        });

        // Simular carga de datos
        setTimeout(() => {
            const mainContent = document.getElementById('main-content');
            
            // Crear grid de criptomonedas
            const cryptoGrid = document.createElement('div');
            cryptoGrid.className = 'crypto-grid';
            
            // Crear tarjetas para cada criptomoneda
            cryptoData.forEach(crypto => {
                const card = document.createElement('div');
                card.className = 'crypto-card';
                
                const name = document.createElement('h2');
                name.textContent = crypto.name;
                
                const symbol = document.createElement('p');
                symbol.className = 'symbol';
                symbol.textContent = crypto.symbol;
                
                const price = document.createElement('p');
                price.className = 'price';
                price.textContent = `$${crypto.price.toLocaleString()}`;
                
                const change = document.createElement('p');
                change.className = `change ${crypto.change24h >= 0 ? 'positive' : 'negative'}`;
                change.textContent = `${crypto.change24h >= 0 ? '+' : ''}${crypto.change24h}%`;
                
                const button = document.createElement('button');
                button.textContent = 'Comprar';
                button.addEventListener('click', () => {
                    alert(`Compraste ${crypto.symbol}!`);
                });
                
                card.appendChild(name);
                card.appendChild(symbol);
                card.appendChild(price);
                card.appendChild(change);
                card.appendChild(button);
                
                cryptoGrid.appendChild(card);
            });
            
            // Reemplazar el contenido de carga con el grid
            mainContent.innerHTML = '';
            mainContent.appendChild(cryptoGrid);
        }, 1500);
    </script>
</body>
</html>
