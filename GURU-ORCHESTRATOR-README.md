# Guru Cripto Orchestrator

El Guru Cripto Orchestrator es un sistema avanzado que utiliza el Model Context Protocol (MCP) para coordinar múltiples agentes especializados y proporcionar análisis completos de criptomonedas.

## Características

- **Orquestación de Agentes**: Coordina múltiples agentes especializados para realizar análisis completos.
- **Integración MCP**: Utiliza servidores MCP para acceder a datos reales y actualizados.
- **Análisis Técnico**: Proporciona análisis técnico detallado utilizando indicadores como RSI, medias móviles, etc.
- **Análisis de Sentimiento**: Analiza noticias y redes sociales para determinar el sentimiento del mercado.
- **Análisis On-Chain**: Examina datos de la blockchain para proporcionar insights sobre la actividad de la red.
- **Predicciones**: Combina múltiples fuentes de datos para hacer predicciones informadas.

## Arquitectura

El sistema está compuesto por:

1. **Guru Cripto Orchestrator**: Agente principal que coordina a los demás agentes.
2. **Agentes Especializados**:
   - **Technical Agent**: Especializado en análisis técnico.
   - **Sentiment Agent**: Especializado en análisis de sentimiento.
   - **OnChain Agent**: Especializado en análisis on-chain.
3. **Servidores MCP**:
   - **Crypto MCP**: Proporciona datos de precios y mercado.
   - **Brave MCP**: Proporciona búsquedas web y noticias.
   - **Playwright MCP**: Permite navegar y analizar páginas web.
   - **Context7 MCP**: Proporciona documentación actualizada.

## Instalación y Uso

### Requisitos Previos

- Python 3.8 o superior
- Node.js 14 o superior
- Google ADK instalado (`pip install google-adk`)

### Iniciar el Sistema

#### Opción 1: Script Batch (Windows)

```bash
start_mcp_orchestrator.bat
```

#### Opción 2: Script PowerShell (Windows)

```powershell
.\start_mcp_orchestrator.ps1
```

#### Opción 3: Iniciar Componentes Individualmente

1. Iniciar servidores MCP:
   ```bash
   node crypto-mcp-server.js
   node brave-search-server.js
   node playwright-mcp-server.js
   npx -y @upstash/context7-mcp@latest
   ```

2. Iniciar Guru Cripto Orchestrator:
   ```bash
   python start_guru_orchestrator.py
   ```

### API REST

El Guru Cripto Orchestrator expone una API REST a través del backend:

- **POST /api/guru-orchestrator/query**: Envía una consulta al orquestador.
  ```json
  {
    "query": "¿Cuál es tu predicción para Bitcoin en la próxima semana?",
    "sessionId": "optional-session-id"
  }
  ```

- **POST /api/guru-orchestrator/start**: Inicia el orquestador.
- **POST /api/guru-orchestrator/stop**: Detiene el orquestador.
- **GET /api/guru-orchestrator/status**: Obtiene el estado del orquestador.

## Ejemplos de Consultas

- **Análisis Técnico**: "Analiza los indicadores técnicos de Bitcoin para la última semana"
- **Análisis de Sentimiento**: "¿Cuál es el sentimiento actual para Ethereum?"
- **Análisis On-Chain**: "Analiza los datos on-chain de Solana"
- **Predicción**: "¿Cuál es tu predicción para el precio de Bitcoin en la próxima semana?"
- **Análisis Completo**: "Dame un análisis completo de Cardano"

## Integración con el Frontend

El Guru Cripto Orchestrator se integra con el frontend a través del backend. Puedes usar el componente `GuruCripto` existente para interactuar con el orquestador.

## Solución de Problemas

Si encuentras problemas al iniciar o usar el Guru Cripto Orchestrator, verifica:

1. Que todos los servidores MCP estén en ejecución.
2. Que las claves API estén configuradas correctamente.
3. Que el ADK API esté en ejecución.
4. Los logs para identificar errores específicos.

## Desarrollo Futuro

- Implementar más agentes especializados.
- Mejorar la integración con más fuentes de datos.
- Implementar un sistema de memoria persistente.
- Añadir capacidades de aprendizaje automático para mejorar las predicciones.
