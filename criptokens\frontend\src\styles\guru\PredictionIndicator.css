.prediction-indicator {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
  transition: all 0.3s ease;
}

.prediction-indicator.up {
  border-left: 4px solid var(--success);
}

.prediction-indicator.down {
  border-left: 4px solid var(--error);
}

.prediction-indicator.sideways {
  border-left: 4px solid var(--warning);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prediction-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.prediction-timeframe {
  font-size: 0.85rem;
  color: var(--text-dim);
  background: rgba(0, 0, 0, 0.2);
  padding: 0.4rem 0.75rem;
  border-radius: var(--radius-md);
}

.prediction-main {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Gauge styles */
.prediction-gauge {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
}

.gauge-container {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.gauge-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%);
  transform-origin: center;
  transform: rotate(-90deg);
  transition: transform 1s ease-out;
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.gauge-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-bright);
}

.gauge-label {
  font-size: 0.75rem;
  color: var(--text-dim);
}

.prediction-direction {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.direction-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.direction-arrow {
  font-size: 2.5rem;
  font-weight: 700;
}

.direction-indicator.up .direction-arrow {
  color: var(--success);
}

.direction-indicator.down .direction-arrow {
  color: var(--error);
}

.direction-indicator.sideways .direction-arrow {
  color: var(--warning);
}

.direction-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-bright);
}

.potential-value {
  font-size: 1.1rem;
  font-weight: 600;
}

.direction-indicator.up .potential-value {
  color: var(--success);
}

.direction-indicator.down .potential-value {
  color: var(--error);
}

/* Signals styles */
.prediction-signals {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.prediction-signals h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.signals-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.signal-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.signal-label {
  width: 90px;
  font-size: 0.9rem;
  color: var(--text-dim);
}

.signal-bar-container {
  flex: 1;
  height: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;
}

.signal-zero-line {
  position: absolute;
  top: 0;
  left: 50%;
  height: 100%;
  width: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.signal-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease-out, margin-left 1s ease-out;
}

.signal-bar.positive {
  background: var(--success);
}

.signal-bar.negative {
  background: var(--error);
}

.signal-bar.neutral {
  background: var(--warning);
}

.signal-value {
  width: 40px;
  text-align: right;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-bright);
}

/* Reasoning styles */
.prediction-reasoning {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.prediction-reasoning h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.reasoning-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.reasoning-item {
  position: relative;
  padding-left: 1.5rem;
  font-size: 0.95rem;
  color: var(--text-medium);
  line-height: 1.4;
}

.reasoning-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.up .reasoning-item::before {
  background-color: var(--success);
}

.down .reasoning-item::before {
  background-color: var(--error);
}

.sideways .reasoning-item::before {
  background-color: var(--warning);
}

.view-details-button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  margin-top: 1.5rem;
  background: rgba(var(--primary-rgb), 0.2);
  color: var(--primary);
  border: 1px solid var(--primary);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-details-button:hover {
  background: rgba(var(--primary-rgb), 0.3);
}

.view-details-button:disabled {
  opacity: 0.7;
  cursor: wait;
}

/* Disclaimer */
.prediction-disclaimer {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.prediction-disclaimer p {
  font-size: 0.8rem;
  color: var(--text-dim);
  margin: 0;
  font-style: italic;
}

/* Loading state */
.prediction-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 1.5rem;
}

.prediction-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.prediction-loading p {
  color: var(--text-medium);
  font-size: 0.95rem;
}

/* Error state */
.prediction-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 1.5rem;
}

.prediction-error p {
  color: var(--error);
  font-size: 0.95rem;
}

.prediction-error button {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  color: var(--text-bright);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.prediction-error button:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Empty state */
.prediction-indicator.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.prediction-indicator.empty p {
  color: var(--text-dim);
  font-size: 0.95rem;
  text-align: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .prediction-gauge {
    flex-direction: column;
  }

  .gauge-container {
    width: 100px;
    height: 100px;
  }

  .gauge-center {
    width: 70px;
    height: 70px;
  }

  .gauge-value {
    font-size: 1.25rem;
  }

  .signal-item {
    flex-wrap: wrap;
  }

  .signal-label {
    width: auto;
    flex: 1;
  }

  .signal-bar-container {
    width: 100%;
    order: 3;
    margin-top: 0.5rem;
  }

  .signal-value {
    width: auto;
  }
}
