"""
Guru Cripto Agent using Google ADK
"""
import os
import json
from typing import Dict, Any, List, Optional

from google.adk.agents import LlmAgent, SequentialAgent, ParallelAgent
from google.adk.tools import FunctionTool, agent_tool
from google.adk.runtime import InvocationContext

# Import specialized agents
import sys
sys.path.append("C:/Users/<USER>/OneDrive/Escritorio/Criptokens/adk_agents")

from technical_agent.agent import technical_agent
from sentiment_agent.agent import sentiment_agent
from onchain_agent.agent import onchain_agent

# API Keys
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")

# Function to extract cryptocurrency name from query
def extract_crypto_name(query: str) -> str:
    """Extract cryptocurrency name from query."""
    # Common cryptocurrencies
    crypto_names = {
        "bitcoin": "Bitcoin",
        "btc": "Bitcoin",
        "ethereum": "Ethereum",
        "eth": "Ethereum",
        "binance coin": "Binance Coin",
        "bnb": "Binance Coin",
        "cardano": "Cardano",
        "ada": "Cardano",
        "solana": "Solana",
        "sol": "Solana",
        "xrp": "XRP",
        "dogecoin": "Dogecoin",
        "doge": "Dogecoin",
        "polkadot": "Polkadot",
        "dot": "Polkadot",
        "tether": "Tether",
        "usdt": "Tether",
        "usd coin": "USD Coin",
        "usdc": "USD Coin"
    }

    query_lower = query.lower()

    # Check for exact matches
    for crypto_name_lower, crypto_name in crypto_names.items():
        if crypto_name_lower in query_lower:
            return crypto_name

    # Default to Bitcoin if no match found
    return "Bitcoin"

# Function to extract timeframe from query
def extract_timeframe(query: str) -> str:
    """Extract timeframe from query."""
    query_lower = query.lower()

    if "year" in query_lower or "365" in query_lower:
        return "365d"
    elif "6 month" in query_lower or "180" in query_lower:
        return "90d"  # Using 90d as closest match
    elif "3 month" in query_lower or "90" in query_lower:
        return "90d"
    elif "month" in query_lower or "30" in query_lower:
        return "30d"
    elif "week" in query_lower or "7" in query_lower:
        return "7d"
    elif "day" in query_lower or "24" in query_lower:
        return "1d"

    # Default to 7 days
    return "7d"

# Function to determine if query is about price prediction
def is_prediction_query(query: str) -> bool:
    """Determine if query is about price prediction."""
    prediction_keywords = [
        "predict", "prediction", "forecast", "future price", "will go",
        "price target", "where will", "how high", "how low", "potential",
        "outlook", "projection", "estimate", "expected price"
    ]

    query_lower = query.lower()

    for keyword in prediction_keywords:
        if keyword in query_lower:
            return True

    return False

# Function to determine if query is about technical analysis
def is_technical_query(query: str) -> bool:
    """Determine if query is about technical analysis."""
    technical_keywords = [
        "technical", "chart", "pattern", "indicator", "moving average",
        "support", "resistance", "rsi", "macd", "bollinger", "trend",
        "volume", "price action", "fibonacci", "oscillator", "momentum"
    ]

    query_lower = query.lower()

    for keyword in technical_keywords:
        if keyword in query_lower:
            return True

    return False

# Function to determine if query is about sentiment analysis
def is_sentiment_query(query: str) -> bool:
    """Determine if query is about sentiment analysis."""
    sentiment_keywords = [
        "sentiment", "news", "social", "twitter", "reddit", "media",
        "feeling", "perception", "opinion", "fear", "greed", "index",
        "market sentiment", "bullish", "bearish", "optimism", "pessimism"
    ]

    query_lower = query.lower()

    for keyword in sentiment_keywords:
        if keyword in query_lower:
            return True

    return False

# Function to determine if query is about on-chain analysis
def is_onchain_query(query: str) -> bool:
    """Determine if query is about on-chain analysis."""
    onchain_keywords = [
        "on-chain", "onchain", "blockchain", "whale", "transaction",
        "wallet", "address", "holder", "holding", "accumulation",
        "distribution", "gas", "fee", "network", "activity", "transfer"
    ]

    query_lower = query.lower()

    for keyword in onchain_keywords:
        if keyword in query_lower:
            return True

    return False

# Function to generate comprehensive analysis
async def generate_comprehensive_analysis(query: str, ctx: InvocationContext) -> str:
    """
    Generate comprehensive analysis for a cryptocurrency.

    Args:
        query: User query about cryptocurrency
        ctx: Invocation context

    Returns:
        Comprehensive analysis
    """
    # Extract crypto name and timeframe
    crypto_name = extract_crypto_name(query)
    timeframe = extract_timeframe(query)

    # Store in session state
    ctx.session.state["crypto_name"] = crypto_name
    ctx.session.state["timeframe"] = timeframe
    ctx.session.state["original_query"] = query

    # Create analysis queries for specialized agents
    technical_query = f"Analyze {crypto_name} technical indicators for the last {timeframe}"
    sentiment_query = f"What's the sentiment for {crypto_name} over the past {timeframe}?"
    onchain_query = f"Analyze on-chain data for {crypto_name} for the last {timeframe}"

    # Create parallel analysis agent
    parallel_analysis = ParallelAgent(
        name="parallel_analysis",
        sub_agents=[
            technical_agent,
            sentiment_agent,
            onchain_agent
        ]
    )

    # Run parallel analysis
    await parallel_analysis.run_async(
        session=ctx.session,
        queries={
            "technical_analysis_agent": technical_query,
            "sentiment_analysis_agent": sentiment_query,
            "onchain_analysis_agent": onchain_query
        }
    )

    # Retrieve results from session state
    technical_result = ctx.session.state.get("technical_analysis", "No technical analysis available")
    sentiment_result = ctx.session.state.get("sentiment_analysis", "No sentiment analysis available")
    onchain_result = ctx.session.state.get("onchain_analysis", "No on-chain analysis available")

    # Prepare comprehensive result
    result = {
        "crypto_name": crypto_name,
        "timeframe": timeframe,
        "is_prediction_query": is_prediction_query(query),
        "technical_analysis": technical_result,
        "sentiment_analysis": sentiment_result,
        "onchain_analysis": onchain_result
    }

    # Store comprehensive result in session state
    ctx.session.state["comprehensive_analysis"] = result

    # Return structured data for the LLM to format
    return json.dumps(result)

# Function to route query to appropriate specialized agent
async def route_query(query: str, ctx: InvocationContext) -> str:
    """
    Route query to appropriate specialized agent.

    Args:
        query: User query about cryptocurrency
        ctx: Invocation context

    Returns:
        Analysis result
    """
    # Extract crypto name and timeframe
    crypto_name = extract_crypto_name(query)
    timeframe = extract_timeframe(query)

    # Store in session state
    ctx.session.state["crypto_name"] = crypto_name
    ctx.session.state["timeframe"] = timeframe
    ctx.session.state["original_query"] = query

    # Determine query type and route to appropriate agent
    if is_technical_query(query):
        technical_query = f"Analyze {crypto_name} technical indicators for the last {timeframe}"
        await technical_agent.run_async(session=ctx.session, query=technical_query)
        result = ctx.session.state.get("technical_analysis", "No technical analysis available")
        ctx.session.state["routed_to"] = "technical_analysis_agent"
    elif is_sentiment_query(query):
        sentiment_query = f"What's the sentiment for {crypto_name} over the past {timeframe}?"
        await sentiment_agent.run_async(session=ctx.session, query=sentiment_query)
        result = ctx.session.state.get("sentiment_analysis", "No sentiment analysis available")
        ctx.session.state["routed_to"] = "sentiment_analysis_agent"
    elif is_onchain_query(query):
        onchain_query = f"Analyze on-chain data for {crypto_name} for the last {timeframe}"
        await onchain_agent.run_async(session=ctx.session, query=onchain_query)
        result = ctx.session.state.get("onchain_analysis", "No on-chain analysis available")
        ctx.session.state["routed_to"] = "onchain_analysis_agent"
    else:
        # For general or prediction queries, run comprehensive analysis
        return await generate_comprehensive_analysis(query, ctx)

    # Return the result
    return json.dumps({
        "crypto_name": crypto_name,
        "timeframe": timeframe,
        "routed_to": ctx.session.state.get("routed_to"),
        "result": result
    })

# Create the Guru Cripto agent
guru_agent = LlmAgent(
    name="guru_cripto_agent",
    model="gemini-1.5-pro",
    description="Cryptocurrency expert that provides comprehensive analysis and predictions",
    instruction="""
    You are Guru Cripto, a cryptocurrency expert with deep knowledge of technical analysis, market sentiment, and on-chain metrics.

    Your capabilities include:
    1. Providing technical analysis of cryptocurrency price movements
    2. Analyzing market sentiment from news and social media
    3. Interpreting on-chain data and blockchain metrics
    4. Making informed price predictions based on comprehensive analysis
    5. Explaining complex cryptocurrency concepts in simple terms

    When responding to queries:
    - Be clear, concise, and informative
    - Support your analysis with specific data points
    - Explain technical terms when necessary
    - Provide balanced perspectives, acknowledging both bullish and bearish factors
    - For price predictions, emphasize that they are estimates based on current data, not guarantees

    You have access to specialized analysis tools that provide technical, sentiment, and on-chain data.
    The data will be provided to you as JSON, which you should interpret and present in a readable format.

    Remember that you are an educational resource, not a financial advisor. Always remind users to do their own research and not to make investment decisions solely based on your analysis.
    """,
    tools=[
        FunctionTool(func=route_query),
        FunctionTool(func=generate_comprehensive_analysis),
        agent_tool.AgentTool(agent=technical_agent),
        agent_tool.AgentTool(agent=sentiment_agent),
        agent_tool.AgentTool(agent=onchain_agent)
    ]
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime
    import asyncio

    async def main():
        runtime = Runtime()
        session = runtime.new_session()

        # Test the agent with a query
        response = await guru_agent.run_async(
            session=session,
            query="What's your prediction for Bitcoin price in the next week?"
        )

        print(response)

    asyncio.run(main())
