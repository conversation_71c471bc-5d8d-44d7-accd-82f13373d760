import React, { useState } from 'react';
import { useRadarCripto } from '../hooks/useRadarCripto';
import '../styles/EnhancedCryptoTable.css';

interface EnhancedCryptoTableProps {
  cryptos: any[];
  onSelectCrypto: (id: string) => void;
  isLoading?: boolean;
}

const EnhancedCryptoTable: React.FC<EnhancedCryptoTableProps> = ({
  cryptos,
  onSelectCrypto,
  isLoading = false
}) => {
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useWatchlist();
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'ascending' | 'descending';
  }>({
    key: 'market_cap',
    direction: 'descending'
  });

  // Función para formatear números con separadores de miles
  const formatNumber = (num: number, maximumFractionDigits: number = 2): string => {
    return new Intl.NumberFormat('es-ES', {
      minimumFractionDigits: 2,
      maximumFractionDigits
    }).format(num);
  };

  // Función para formatear la capitalización de mercado
  const formatMarketCap = (marketCap: number): string => {
    if (marketCap >= 1_000_000_000_000) {
      return `$${(marketCap / 1_000_000_000_000).toFixed(2)}T`;
    } else if (marketCap >= 1_000_000_000) {
      return `$${(marketCap / 1_000_000_000).toFixed(2)}B`;
    } else if (marketCap >= 1_000_000) {
      return `$${(marketCap / 1_000_000).toFixed(2)}M`;
    } else {
      return `$${formatNumber(marketCap)}`;
    }
  };

  // Función para manejar el cambio de ordenación
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Función para ordenar los datos
  const sortedData = React.useMemo(() => {
    if (!cryptos || cryptos.length === 0) return [];

    const sortableItems = [...cryptos];
    sortableItems.sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? 1 : -1;
      }
      return 0;
    });
    return sortableItems;
  }, [cryptos, sortConfig]);

  // Función para renderizar el mini gráfico sparkline
  const renderSparkline = (sparklineData: number[] | undefined) => {
    if (!sparklineData || sparklineData.length === 0) return null;

    const min = Math.min(...sparklineData);
    const max = Math.max(...sparklineData);
    const range = max - min;
    const width = 100; // Ancho del SVG
    const height = 30; // Altura del SVG
    const padding = 2; // Padding para que no toque los bordes

    // Calcular los puntos del gráfico
    const points = sparklineData.map((value, index) => {
      const x = (index / (sparklineData.length - 1)) * (width - 2 * padding) + padding;
      const y = height - padding - ((value - min) / range) * (height - 2 * padding);
      return `${x},${y}`;
    }).join(' ');

    // Determinar el color basado en la tendencia (último valor vs primer valor)
    const isPositive = sparklineData[sparklineData.length - 1] >= sparklineData[0];
    const strokeColor = isPositive ? '#00e676' : '#ff5252';

    return (
      <svg width={width} height={height} className="sparkline">
        <polyline
          fill="none"
          stroke={strokeColor}
          strokeWidth="1.5"
          points={points}
        />
      </svg>
    );
  };

  // Renderizar un esqueleto de carga
  const renderSkeleton = () => {
    return Array(10).fill(0).map((_, index) => (
      <tr key={`skeleton-${index}`} className="skeleton-row">
        <td><div className="skeleton-cell rank"></div></td>
        <td><div className="skeleton-cell name"></div></td>
        <td><div className="skeleton-cell price"></div></td>
        <td><div className="skeleton-cell change"></div></td>
        <td><div className="skeleton-cell market-cap"></div></td>
        <td><div className="skeleton-cell sparkline"></div></td>
        <td><div className="skeleton-cell actions"></div></td>
      </tr>
    ));
  };

  // Función para alternar una criptomoneda en la lista de seguimiento
  const toggleWatchlist = (e: React.MouseEvent, cryptoId: string) => {
    e.stopPropagation(); // Evitar que se propague al tr y active la selección

    if (isInWatchlist(cryptoId)) {
      removeFromWatchlist(cryptoId);
    } else {
      addToWatchlist(cryptoId);
    }
  };

  return (
    <div className="enhanced-crypto-table-container">
      <div className="table-actions">
        <span className="crypto-count">{cryptos.length} monedas</span>
      </div>
      <div className="table-wrapper">
        <table className="enhanced-crypto-table">
          <thead>
            <tr>
              <th onClick={() => requestSort('market_cap_rank')} className={sortConfig.key === 'market_cap_rank' ? sortConfig.direction : ''}>
                #
              </th>
              <th onClick={() => requestSort('name')} className={sortConfig.key === 'name' ? sortConfig.direction : ''}>
                Nombre
              </th>
              <th onClick={() => requestSort('current_price')} className={sortConfig.key === 'current_price' ? sortConfig.direction : ''}>
                Precio
              </th>
              <th onClick={() => requestSort('price_change_percentage_24h')} className={sortConfig.key === 'price_change_percentage_24h' ? sortConfig.direction : ''}>
                24h %
              </th>
              <th onClick={() => requestSort('market_cap')} className={sortConfig.key === 'market_cap' ? sortConfig.direction : ''}>
                Cap. Mercado
              </th>
              <th>
                Últimos 7 días
              </th>
              <th>
                Acciones
              </th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              renderSkeleton()
            ) : (
              sortedData.map((crypto) => (
                <tr
                  key={crypto.id}
                  onClick={() => onSelectCrypto(crypto.id)}
                  className="crypto-row"
                >
                  <td className="rank-cell">{crypto.market_cap_rank || '-'}</td>
                  <td className="name-cell">
                    <div className="crypto-info">
                      <img
                        src={crypto.image}
                        alt={crypto.name}
                        className="crypto-icon"
                        style={{
                          width: '16px',
                          height: '16px',
                          maxWidth: '16px',
                          maxHeight: '16px',
                          borderRadius: '50%',
                          verticalAlign: 'middle',
                          objectFit: 'contain'
                        }}
                      />
                      <div className="crypto-name-container">
                        <span className="crypto-name">{crypto.name}</span>
                        <span className="crypto-symbol">{crypto.symbol.toUpperCase()}</span>
                      </div>
                    </div>
                  </td>
                  <td className="price-cell">${formatNumber(crypto.current_price)}</td>
                  <td className={`change-cell ${crypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                    {crypto.price_change_percentage_24h >= 0 ? '+' : ''}{crypto.price_change_percentage_24h?.toFixed(2)}%
                  </td>
                  <td className="market-cap-cell">{formatMarketCap(crypto.market_cap)}</td>
                  <td className="sparkline-cell">
                    {renderSparkline(crypto.sparkline_in_7d?.price)}
                  </td>
                  <td className="actions-cell">
                    <button
                      className={`watchlist-button ${isInWatchlist(crypto.id) ? 'active' : ''}`}
                      onClick={(e) => toggleWatchlist(e, crypto.id)}
                      title={isInWatchlist(crypto.id) ? "Quitar de Watchlist" : "Añadir a Watchlist"}
                    >
                      <i className={`fas ${isInWatchlist(crypto.id) ? 'fa-star' : 'fa-star'}`}></i>
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default EnhancedCryptoTable;
