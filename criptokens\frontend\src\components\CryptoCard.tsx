import React, { useEffect, useRef } from 'react';
import EnhancedCryptoChart from './EnhancedCryptoChart';
import '../styles/CryptoCard.css';

interface CryptoCardProps {
  crypto: {
    id: string;
    name: string;
    symbol: string;
    current_price: number;
    price_change_percentage_24h: number;
    market_cap: number;
    total_volume: number;
    image: string;
    sparkline_in_7d?: { price: number[] };
  };
  onClick?: () => void;
}

const CryptoCard: React.FC<CryptoCardProps> = ({ crypto, onClick }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);

  // Generar datos para el gráfico
  const chartData = {
    labels: Array(24).fill('').map((_, i) => {
      const date = new Date();
      date.setHours(date.getHours() - (24 - i));
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }),
    values: crypto.sparkline_in_7d?.price.slice(-24) || 
            Array(24).fill(0).map((_, i) => {
              // Si no hay datos reales, generamos datos aleatorios basados en el precio actual
              const randomFactor = 0.98 + (Math.random() * 0.04); // Entre 0.98 y 1.02
              return crypto.current_price * randomFactor;
            })
  };

  // Determinar el color del gráfico basado en el cambio de precio
  const chartColor = crypto.price_change_percentage_24h >= 0 ? '#00c853' : '#ff5252';

  // Efecto para la animación de hover 3D
  useEffect(() => {
    if (!cardRef.current || !glowRef.current) return;

    const card = cardRef.current;
    const glow = glowRef.current;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // Calcular la rotación basada en la posición del mouse
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = (y - centerY) / 20;
      const rotateY = (centerX - x) / 20;
      
      // Aplicar la transformación
      card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
      
      // Mover el efecto de brillo
      glow.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(64, 220, 255, 0.15) 0%, rgba(10, 10, 26, 0) 70%)`;
    };

    const handleMouseLeave = () => {
      card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
      glow.style.background = 'none';
    };

    card.addEventListener('mousemove', handleMouseMove);
    card.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      card.removeEventListener('mousemove', handleMouseMove);
      card.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <div className="crypto-card" ref={cardRef} onClick={onClick}>
      <div className="crypto-card-glow" ref={glowRef}></div>
      
      <div className="crypto-card-header">
        <div className="crypto-info">
          <img src={crypto.image} alt={crypto.name} className="crypto-icon" />
          <div className="crypto-name-container">
            <h3 className="crypto-name">{crypto.name}</h3>
            <span className="crypto-symbol">{crypto.symbol.toUpperCase()}</span>
          </div>
        </div>
        <div className="crypto-price-container">
          <div className="crypto-price">${crypto.current_price.toLocaleString()}</div>
          <div className={`crypto-change ${crypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
            {crypto.price_change_percentage_24h >= 0 ? '▲' : '▼'} 
            {Math.abs(crypto.price_change_percentage_24h).toFixed(2)}%
          </div>
        </div>
      </div>
      
      <div className="crypto-chart-container">
        <EnhancedCryptoChart 
          data={chartData}
          color={chartColor}
          height={120}
          width={300}
          showLabels={false}
          showGrid={false}
        />
      </div>
      
      <div className="crypto-stats">
        <div className="stat-item">
          <span className="stat-label">Cap. Mercado</span>
          <span className="stat-value">${(crypto.market_cap / 1e9).toFixed(2)}B</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Volumen 24h</span>
          <span className="stat-value">${(crypto.total_volume / 1e9).toFixed(2)}B</span>
        </div>
      </div>
      
      <div className="crypto-card-actions">
        <button className="action-button details-button">Ver Detalles</button>
        <button className="action-button add-button">+ Añadir</button>
      </div>
    </div>
  );
};

export default CryptoCard;
