.price-alerts-container {
  background-color: #1a1a2e;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  color: #e6e6e6;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  overflow: hidden;
}

.price-alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(45deg, #16162a, #1e1e3a);
  border-bottom: 1px solid rgba(123, 77, 255, 0.2);
}

.price-alerts-header h2 {
  margin: 0;
  font-size: 1.5rem;
  background: linear-gradient(45deg, #00f2ff, #4facfe);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.close-button {
  background: none;
  border: none;
  color: #a0a0a0;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close-button:hover {
  color: #ffffff;
}

.price-alerts-content {
  padding: 20px;
}

.no-alerts-message {
  text-align: center;
  padding: 30px 0;
}

.no-alerts-message p {
  margin-bottom: 20px;
  color: #a0a0a0;
}

.add-alert-button {
  background: linear-gradient(45deg, #00f2ff, #4facfe);
  border: none;
  border-radius: 6px;
  color: #ffffff;
  padding: 10px 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.add-alert-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 242, 255, 0.3);
}

.add-alert-form {
  background: rgba(10, 10, 26, 0.5);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(123, 77, 255, 0.2);
}

.add-alert-form h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #e6e6e6;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #a0a0a0;
  font-size: 0.9rem;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 10px 12px;
  background: rgba(10, 10, 26, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.3);
  border-radius: 6px;
  color: #e6e6e6;
  font-size: 1rem;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #4facfe;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.cancel-button {
  background: rgba(10, 10, 26, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.3);
  border-radius: 6px;
  color: #a0a0a0;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  background: rgba(123, 77, 255, 0.1);
  color: #e6e6e6;
}

.submit-button {
  background: linear-gradient(45deg, #00f2ff, #4facfe);
  border: none;
  border-radius: 6px;
  color: #ffffff;
  padding: 10px 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 242, 255, 0.3);
}

.alerts-list {
  margin-top: 20px;
}

.alerts-list h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #e6e6e6;
}

.alert-item {
  background: rgba(10, 10, 26, 0.5);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(123, 77, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.alert-item:hover {
  background: rgba(10, 10, 26, 0.7);
  transform: translateY(-2px);
}

.alert-item.triggered {
  border-color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
}

.alert-item.inactive {
  opacity: 0.6;
}

.alert-info {
  flex: 1;
}

.alert-crypto {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.crypto-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.crypto-name {
  font-weight: 600;
  color: #e6e6e6;
}

.alert-condition {
  color: #a0a0a0;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.target-price {
  color: #e6e6e6;
  font-weight: 600;
  margin-left: 4px;
}

.current-price {
  color: #a0a0a0;
  font-size: 0.9rem;
}

.price-up {
  color: #4caf50;
  font-weight: 600;
  margin-left: 4px;
}

.price-down {
  color: #f44336;
  font-weight: 600;
  margin-left: 4px;
}

.alert-status {
  margin-top: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

.alert-status.triggered {
  color: #4caf50;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toggle-button {
  background: rgba(10, 10, 26, 0.7);
  border: 1px solid rgba(123, 77, 255, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-button.active {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
  color: #4caf50;
}

.toggle-button.inactive {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #f44336;
}

.delete-button {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 6px;
  color: #f44336;
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-button:hover {
  background: rgba(244, 67, 54, 0.2);
}

.price-alerts-loading {
  text-align: center;
  padding: 30px;
  color: #a0a0a0;
}

.price-alerts-error {
  text-align: center;
  padding: 30px;
  color: #f44336;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
  .alert-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .alert-actions {
    flex-direction: row;
    margin-top: 12px;
    width: 100%;
  }
  
  .toggle-button,
  .delete-button {
    flex: 1;
    text-align: center;
  }
}
