import json
from typing import AsyncIterable, Union
import logging
from ...common.types import (
    SendTaskRequest,
    TaskSendParams,
    Message,
    TaskStatus,
    Artifact,
    TaskStatusUpdateEvent,
    TaskArtifactUpdateEvent,
    TextPart,
    TaskState,
    Task,
    SendTaskResponse,
    InternalError,
    JSONRPCResponse,
    SendTaskStreamingRequest,
    SendTaskStreamingResponse,
    DataPart,
)
from ...common.server.task_manager import InMemoryTaskManager
from .agent import TechnicalAnalysisAgent
import ...common.server.utils as utils

logger = logging.getLogger(__name__)

class TechnicalAgentTaskManager(InMemoryTaskManager):
    """Task manager for the technical analysis agent."""

    def __init__(self, agent: TechnicalAnalysisAgent):
        super().__init__()
        self.agent = agent

    async def _stream_generator(
        self, request: SendTaskStreamingRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        """Generate streaming responses for a task.
        
        Args:
            request: The streaming request
            
        Yields:
            Streaming responses
        """
        task_send_params: TaskSendParams = request.params
        query = self._get_user_query(task_send_params)
        try:
            async for item in self.agent.stream(query, task_send_params.sessionId):
                is_task_complete = item["is_task_complete"]
                artifacts = None
                
                if not is_task_complete:
                    task_state = TaskState.WORKING
                    parts = [{"type": "text", "text": item["updates"]}]
                else:
                    content = item["content"]
                    if isinstance(content, dict):
                        # Return structured data
                        task_state = TaskState.COMPLETED
                        parts = [{"type": "data", "data": content}]
                    else:
                        # Return text
                        task_state = TaskState.COMPLETED
                        parts = [{"type": "text", "text": content}]
                    
                    artifacts = [Artifact(parts=parts, index=0, append=False)]
                
                message = Message(role="agent", parts=parts)
                task_status = TaskStatus(state=task_state, message=message)
                await self._update_store(task_send_params.id, task_status, artifacts)
                
                # Yield status update
                task_update_event = TaskStatusUpdateEvent(
                    id=task_send_params.id,
                    status=task_status,
                    final=False,
                )
                yield SendTaskStreamingResponse(id=request.id, result=task_update_event)
                
                # Yield artifacts if available
                if artifacts:
                    for artifact in artifacts:
                        yield SendTaskStreamingResponse(
                            id=request.id,
                            result=TaskArtifactUpdateEvent(
                                id=task_send_params.id,
                                artifact=artifact,
                            )
                        )
                
                # Yield final status update if task is complete
                if is_task_complete:
                    yield SendTaskStreamingResponse(
                        id=request.id,
                        result=TaskStatusUpdateEvent(
                            id=task_send_params.id,
                            status=TaskStatus(
                                state=task_status.state,
                            ),
                            final=True
                        )
                    )
        except Exception as e:
            logger.error(f"An error occurred while streaming the response: {e}")
            yield JSONRPCResponse(
                id=request.id,
                error=InternalError(
                    message=f"An error occurred while streaming the response: {str(e)}"
                ),
            )

    def _validate_request(
        self, request: Union[SendTaskRequest, SendTaskStreamingRequest]
    ) -> JSONRPCResponse | None:
        """Validate a request.
        
        Args:
            request: The request to validate
            
        Returns:
            Error response if validation fails, None otherwise
        """
        task_send_params: TaskSendParams = request.params
        if not utils.are_modalities_compatible(
            task_send_params.acceptedOutputModes, TechnicalAnalysisAgent.SUPPORTED_CONTENT_TYPES
        ):
            logger.warning(
                "Unsupported output mode. Received %s, Support %s",
                task_send_params.acceptedOutputModes,
                TechnicalAnalysisAgent.SUPPORTED_CONTENT_TYPES,
            )
            return utils.new_incompatible_types_error(request.id)
        return None

    async def on_send_task(self, request: SendTaskRequest) -> SendTaskResponse:
        """Handle a send task request.
        
        Args:
            request: The request
            
        Returns:
            Response
        """
        error = self._validate_request(request)
        if error:
            return error
        
        await self.upsert_task(request.params)
        return await self._invoke(request)

    async def on_send_task_subscribe(
        self, request: SendTaskStreamingRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        """Handle a send task streaming request.
        
        Args:
            request: The request
            
        Returns:
            Streaming response
        """
        error = self._validate_request(request)
        if error:
            return error
        
        await self.upsert_task(request.params)
        return self._stream_generator(request)

    async def _update_store(
        self, task_id: str, status: TaskStatus, artifacts: list[Artifact] = None
    ) -> Task:
        """Update a task in the store.
        
        Args:
            task_id: The task ID
            status: The new status
            artifacts: New artifacts
            
        Returns:
            The updated task
        """
        async with self.lock:
            try:
                task = self.tasks[task_id]
            except KeyError:
                logger.error(f"Task {task_id} not found for updating the task")
                raise ValueError(f"Task {task_id} not found")
            
            task.status = status
            
            if artifacts is not None:
                if task.artifacts is None:
                    task.artifacts = []
                task.artifacts.extend(artifacts)
            
            return task

    async def _invoke(self, request: SendTaskRequest) -> SendTaskResponse:
        """Invoke the agent to process a request.
        
        Args:
            request: The request
            
        Returns:
            Response
        """
        task_send_params: TaskSendParams = request.params
        query = self._get_user_query(task_send_params)
        
        try:
            # Process the query
            result = await self.agent.process_query(query, task_send_params.sessionId)
            
            # Create response parts based on result type
            if isinstance(result, dict):
                # Return structured data
                parts = [DataPart(type="data", data=result)]
            else:
                # Return text
                parts = [TextPart(type="text", text=str(result))]
            
            # Update task status
            task = await self._update_store(
                task_send_params.id,
                TaskStatus(
                    state=TaskState.COMPLETED,
                    message=Message(role="agent", parts=parts)
                ),
                [Artifact(parts=parts)],
            )
            
            return SendTaskResponse(id=request.id, result=task)
        except Exception as e:
            logger.error(f"Error invoking agent: {e}")
            
            # Create error response
            error_message = f"Error processing request: {str(e)}"
            parts = [TextPart(type="text", text=error_message)]
            
            # Update task status to failed
            task = await self._update_store(
                task_send_params.id,
                TaskStatus(
                    state=TaskState.FAILED,
                    message=Message(role="agent", parts=parts)
                ),
                [Artifact(parts=parts)],
            )
            
            return SendTaskResponse(id=request.id, result=task)

    def _get_user_query(self, task_send_params: TaskSendParams) -> str:
        """Extract the user query from a task.
        
        Args:
            task_send_params: The task parameters
            
        Returns:
            The user query
        """
        for part in task_send_params.message.parts:
            if isinstance(part, TextPart):
                return part.text
            elif hasattr(part, 'type') and part.type == 'text':
                return part.text
        
        raise ValueError("No text part found in message")
