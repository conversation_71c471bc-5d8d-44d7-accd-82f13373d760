/**
 * Servicio para interactuar con la API de Ultravox a través del backend
 */

// URL base del backend
const API_BASE_URL = 'http://localhost:3003/api/ultravox';

// Interfaz para la respuesta de estado de la API
interface ApiStatusResponse {
  success: boolean;
  isValid: boolean;
  credits: number;
  message: string;
  simulation?: boolean;
}

// Interfaz para la respuesta de inicio de llamada
interface CallResponse {
  success: boolean;
  call?: {
    id: string;
    realCallId?: string;
    status: string;
    createdAt: string;
  };
  credits?: number;
  message?: string;
  error?: string;
}

// Interfaz para la respuesta de mensaje
interface MessageResponse {
  success: boolean;
  message?: string;
  credits?: number;
  response?: {
    id: string;
    content: string;
    audioUrl: string | null;
  };
  pending?: boolean;
  error?: string;
}

// Interfaz para la respuesta de mensajes
interface MessagesResponse {
  success: boolean;
  messages?: Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
    createdAt: string;
    audioUrl: string | null;
  }>;
  message?: string;
  error?: string;
}

/**
 * Verifica el estado de la API de Ultravox
 * @returns Promesa con el estado de la API
 */
export const checkApiStatus = async (): Promise<ApiStatusResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/status`);

    if (!response.ok) {
      throw new Error(`Error al verificar el estado de la API: ${response.statusText}`);
    }

    return await response.json();
  } catch (error: any) {
    console.error('Error verificando estado de Ultravox:', error);
    return {
      success: false,
      isValid: false,
      credits: 0,
      message: error.message
    };
  }
};

/**
 * Inicia una nueva llamada de voz con el Guru Cripto
 * @param language Idioma de la llamada (es, en, etc.)
 * @returns Promesa con la información de la llamada
 */
export const startCall = async (language = 'es'): Promise<CallResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/calls`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ language })
    });

    if (!response.ok) {
      throw new Error(`Error al iniciar la llamada: ${response.statusText}`);
    }

    return await response.json();
  } catch (error: any) {
    console.error('Error iniciando llamada de voz:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * Finaliza una llamada de voz
 * @param callId ID de la llamada a finalizar
 * @returns Promesa con el resultado de la operación
 */
export const endCall = async (callId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${API_BASE_URL}/calls/${callId}/end`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Error al finalizar la llamada: ${response.statusText}`);
    }

    return await response.json();
  } catch (error: any) {
    console.error('Error finalizando llamada de voz:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * Envía un mensaje en una llamada de voz
 * @param callId ID de la llamada
 * @param message Mensaje a enviar
 * @returns Promesa con la respuesta del asistente
 */
export const sendMessage = async (callId: string, message: string): Promise<MessageResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/calls/${callId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ message })
    });

    if (!response.ok) {
      throw new Error(`Error al enviar el mensaje: ${response.statusText}`);
    }

    return await response.json();
  } catch (error: any) {
    console.error('Error enviando mensaje en llamada de voz:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * Obtiene los mensajes de una llamada de voz
 * @param callId ID de la llamada
 * @returns Promesa con los mensajes de la llamada
 */
export const getMessages = async (callId: string): Promise<MessagesResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/calls/${callId}/messages`);

    if (!response.ok) {
      throw new Error(`Error al obtener los mensajes: ${response.statusText}`);
    }

    return await response.json();
  } catch (error: any) {
    console.error('Error obteniendo mensajes de llamada de voz:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * Obtiene la URL de audio para un texto
 * @param text Texto a convertir en audio
 * @param voice Voz a utilizar (opcional)
 * @param options Opciones adicionales (velocidad, tono, etc.)
 * @returns URL del audio generado
 */
export const getAudioUrl = (
  text: string,
  voice?: string,
  options?: { speed?: number; pitch?: number }
): string => {
  // Construir la URL con los parámetros
  let url = `${API_BASE_URL}/audio/response?text=${encodeURIComponent(text)}`;

  // Añadir la voz si está presente
  if (voice) {
    url += `&voice=${encodeURIComponent(voice)}`;
  }

  // Añadir opciones adicionales si están presentes
  if (options) {
    if (options.speed !== undefined) {
      url += `&speed=${options.speed}`;
    }
    if (options.pitch !== undefined) {
      url += `&pitch=${options.pitch}`;
    }
  }

  return url;
};

/**
 * Reproduce un audio a partir de texto utilizando la API de Ultravox
 * @param text Texto a reproducir
 * @param voice Voz a utilizar (opcional)
 * @param options Opciones adicionales (velocidad, tono, etc.)
 * @returns Promesa que se resuelve cuando el audio termina de reproducirse
 */
export const playAudio = (
  text: string,
  voice?: string,
  options?: {
    speed?: number;
    pitch?: number;
    volume?: number;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: any) => void;
  }
): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      // Obtener la URL del audio
      const audioUrl = getAudioUrl(text, voice, options);

      // Crear un elemento de audio
      const audio = new Audio(audioUrl);

      // Establecer el volumen si está presente
      if (options?.volume !== undefined) {
        audio.volume = Math.max(0, Math.min(1, options.volume));
      }

      // Manejar eventos
      audio.onplay = () => {
        console.log('Audio iniciado');
        options?.onStart?.();
      };

      audio.onended = () => {
        console.log('Audio finalizado');
        options?.onEnd?.();
        resolve();
      };

      audio.onerror = (error) => {
        console.error('Error reproduciendo audio:', error);
        options?.onError?.(error);
        reject(error);
      };

      // Reproducir el audio
      audio.play().catch((error) => {
        console.error('Error iniciando reproducción de audio:', error);

        // Si hay un error, intentar usar la API Web Speech como fallback
        if ('speechSynthesis' in window) {
          console.log('Usando Web Speech API como fallback');

          const utterance = new SpeechSynthesisUtterance(text);

          // Configurar la voz en español si está disponible
          const voices = window.speechSynthesis.getVoices();
          const spanishVoice = voices.find(v => v.lang.startsWith('es'));
          if (spanishVoice) {
            utterance.voice = spanishVoice;
          }

          // Configurar opciones
          if (options?.speed !== undefined) {
            utterance.rate = options.speed;
          }
          if (options?.pitch !== undefined) {
            utterance.pitch = options.pitch;
          }
          if (options?.volume !== undefined) {
            utterance.volume = options.volume;
          }

          // Manejar eventos
          utterance.onstart = () => {
            console.log('Fallback audio iniciado');
            options?.onStart?.();
          };

          utterance.onend = () => {
            console.log('Fallback audio finalizado');
            options?.onEnd?.();
            resolve();
          };

          utterance.onerror = (error) => {
            console.error('Error en fallback audio:', error);
            options?.onError?.(error);
            reject(error);
          };

          // Reproducir el audio
          window.speechSynthesis.speak(utterance);
        } else {
          // Si no hay soporte para Web Speech API, rechazar la promesa
          reject(new Error('No se pudo reproducir el audio y no hay fallback disponible'));
        }
      });
    } catch (error) {
      console.error('Error en playAudio:', error);
      reject(error);
    }
  });
};

// Exportar todas las funciones
export default {
  checkApiStatus,
  startCall,
  endCall,
  sendMessage,
  getMessages,
  getAudioUrl,
  playAudio
};
