/**
 * Sistema de logging para el sistema de agentes MCP
 * 
 * Este módulo proporciona funciones para registrar mensajes de log
 * con diferentes niveles de severidad.
 */

const config = require('../config/config');

// Niveles de log en orden de severidad
const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
  none: 4
};

// Obtener el nivel de log configurado
const configuredLevel = config.system.logLevel || 'info';
const currentLogLevel = LOG_LEVELS[configuredLevel] || LOG_LEVELS.info;

// Colores para los diferentes niveles de log
const COLORS = {
  debug: '\x1b[36m', // Cyan
  info: '\x1b[32m',  // Verde
  warn: '\x1b[33m',  // Amarillo
  error: '\x1b[31m', // Rojo
  reset: '\x1b[0m'   // Reset
};

/**
 * Registra un mensaje de log si el nivel de severidad es suficiente
 * @param {string} level - Nivel de log (debug, info, warn, error)
 * @param {string} source - Fuente del mensaje (nombre del módulo)
 * @param {string} message - Mensaje a registrar
 * @param {Object} [data] - Datos adicionales a registrar
 */
function log(level, source, message, data = null) {
  // Verificar si el nivel de log es suficiente para registrar el mensaje
  if (LOG_LEVELS[level] < currentLogLevel) {
    return;
  }

  const timestamp = new Date().toISOString();
  const color = COLORS[level] || COLORS.reset;
  
  // Formatear el mensaje
  let logMessage = `${color}[${timestamp}] [${level.toUpperCase()}] [${source}] ${message}${COLORS.reset}`;
  
  // Imprimir el mensaje
  console.log(logMessage);
  
  // Imprimir datos adicionales si existen y estamos en modo debug
  if (data !== null && (level === 'debug' || config.system.debug)) {
    console.log(data);
  }
}

// Funciones específicas para cada nivel de log
const logger = {
  debug: (source, message, data = null) => log('debug', source, message, data),
  info: (source, message, data = null) => log('info', source, message, data),
  warn: (source, message, data = null) => log('warn', source, message, data),
  error: (source, message, data = null) => log('error', source, message, data)
};

module.exports = logger;
