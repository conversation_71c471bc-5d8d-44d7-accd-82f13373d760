import { useState } from 'react';
import { testFirebaseAuth, testFirestore } from '../utils/firebase-test';

const FirebaseTest = () => {
  const [authStatus, setAuthStatus] = useState<string>('No probado');
  const [firestoreStatus, setFirestoreStatus] = useState<string>('No probado');
  const [loading, setLoading] = useState<boolean>(false);

  const handleTestAuth = async () => {
    setLoading(true);
    try {
      const result = await testFirebaseAuth();
      if (result.success) {
        setAuthStatus('Conexión exitosa a Firebase Auth');
      } else {
        setAuthStatus(`Error: ${JSON.stringify(result.error)}`);
      }
    } catch (error) {
      setAuthStatus(`Error inesperado: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestFirestore = async () => {
    setLoading(true);
    try {
      const result = await testFirestore();
      if (result.success) {
        setFirestoreStatus(`Conexión exitosa a Firestore. Documentos en Users: ${result.usersCount}, Documentos en Portafolio: ${result.portfolioCount}`);
      } else {
        setFirestoreStatus(`Error: ${JSON.stringify(result.error)}`);
      }
    } catch (error) {
      setFirestoreStatus(`Error inesperado: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>Prueba de Conexión a Firebase</h2>

      <div style={{ marginBottom: '20px' }}>
        <h3>Firebase Auth</h3>
        <p>Estado: {authStatus}</p>
        <button
          onClick={handleTestAuth}
          disabled={loading}
          style={{ padding: '8px 16px', marginRight: '10px' }}
        >
          Probar Auth
        </button>
      </div>

      <div>
        <h3>Firestore</h3>
        <p>Estado: {firestoreStatus}</p>
        <button
          onClick={handleTestFirestore}
          disabled={loading}
          style={{ padding: '8px 16px' }}
        >
          Probar Firestore
        </button>
      </div>
    </div>
  );
};

export default FirebaseTest;
