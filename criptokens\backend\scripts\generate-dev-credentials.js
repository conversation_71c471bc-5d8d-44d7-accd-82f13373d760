/**
 * Script para generar credenciales de desarrollo para Firebase Admin
 * 
 * Este script crea un archivo de credenciales de Firebase Admin para desarrollo.
 * NOTA: Estas credenciales NO son válidas para producción y solo deben usarse en desarrollo.
 */

const fs = require('fs');
const path = require('path');

// Ruta al archivo de credenciales
const credentialsPath = path.join(__dirname, '../firebase-credentials.json');

// Verificar si el archivo ya existe
if (fs.existsSync(credentialsPath)) {
  console.log('El archivo de credenciales ya existe. Si deseas regenerarlo, elimina el archivo existente primero.');
  process.exit(0);
}

// Credenciales de desarrollo
const devCredentials = {
  "type": "service_account",
  "project_id": "criptoken-11c3b",
  "private_key_id": "development-key-" + Math.random().toString(36).substring(2, 15),
  "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKj\nMzEfYyjiWA4R4/M2bS1GB4t7NXp98C3SC6dVMvDuictGeurT8jNbvJZHtCSuYEvu\nNMoSfm76oqFvAp8Gy0iz5sxjZmSnXyCdPEovGhLa0VzMaQ8s+CLOyS56YyCFGeJZ\n-----END PRIVATE KEY-----\n",
  "client_email": "<EMAIL>",
  "client_id": "dev-client-id-" + Math.random().toString(36).substring(2, 15),
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-dev%40criptoken-11c3b.iam.gserviceaccount.com",
  "universe_domain": "googleapis.com"
};

// Guardar el archivo
fs.writeFileSync(credentialsPath, JSON.stringify(devCredentials, null, 2));

console.log('Archivo de credenciales de desarrollo generado en:', credentialsPath);
console.log('NOTA: Estas credenciales NO son válidas para producción y solo deben usarse en desarrollo.');
console.log('Para usar credenciales reales, sigue las instrucciones en README_FIREBASE.md');
