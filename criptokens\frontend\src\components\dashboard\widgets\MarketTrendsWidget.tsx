import React from 'react';
import '../../../styles/dashboard/MarketTrendsWidget.css';

interface MarketTrendsWidgetProps {
  isLoading: boolean;
  compact?: boolean;
}

interface TrendItem {
  id: string;
  name: string;
  symbol: string;
  change: number;
  volume: number;
  trend: 'up' | 'down' | 'neutral';
  reason: string;
}

const MarketTrendsWidget: React.FC<MarketTrendsWidgetProps> = ({ 
  isLoading,
  compact = false
}) => {
  // Datos simulados para tendencias
  const mockTrends: TrendItem[] = [
    {
      id: '1',
      name: 'Bitcoin',
      symbol: 'BTC',
      change: 5.2,
      volume: 28500000000,
      trend: 'up',
      reason: 'Aumento de adopción institucional'
    },
    {
      id: '2',
      name: 'Ethereum',
      symbol: 'ETH',
      change: 3.8,
      volume: 15700000000,
      trend: 'up',
      reason: 'Actualización de red exitosa'
    },
    {
      id: '3',
      name: '<PERSON><PERSON>',
      symbol: 'SOL',
      change: 12.5,
      volume: 3200000000,
      trend: 'up',
      reason: 'Crecimiento en ecosistema DeFi'
    },
    {
      id: '4',
      name: 'Cardano',
      symbol: 'ADA',
      change: -2.1,
      volume: 1800000000,
      trend: 'down',
      reason: 'Retrasos en desarrollo'
    },
    {
      id: '5',
      name: 'Polkadot',
      symbol: 'DOT',
      change: 7.3,
      volume: 1200000000,
      trend: 'up',
      reason: 'Nuevas integraciones de parachain'
    }
  ];

  const formatVolume = (volume: number): string => {
    if (volume >= 1000000000) {
      return `$${(volume / 1000000000).toFixed(1)}B`;
    } else if (volume >= 1000000) {
      return `$${(volume / 1000000).toFixed(1)}M`;
    } else {
      return `$${(volume / 1000).toFixed(1)}K`;
    }
  };

  if (isLoading) {
    return (
      <div className="widget-loading-state">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Cargando tendencias...</p>
      </div>
    );
  }

  return (
    <div className="trends-list">
      {mockTrends.map(trend => (
        <div key={trend.id} className="trend-item">
          <div className="trend-header">
            <div className="trend-symbol">{trend.symbol}</div>
            <div className={`trend-change ${trend.change >= 0 ? 'positive' : 'negative'}`}>
              {trend.change >= 0 ? '+' : ''}{trend.change}%
              <i className={`fas fa-arrow-${trend.change >= 0 ? 'up' : 'down'}`}></i>
            </div>
          </div>
          <div className="trend-details">
            <div className="trend-name">{trend.name}</div>
            <div className="trend-volume">Vol: {formatVolume(trend.volume)}</div>
          </div>
          {!compact && (
            <div className="trend-reason">
              <i className="fas fa-info-circle"></i>
              <span>{trend.reason}</span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default MarketTrendsWidget;
