import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../styles/DefiCenterPage.css';

// Datos simulados para el dashboard de DeFi
const defiProtocolsData = [
  {
    id: 'aave',
    name: 'Aave',
    category: 'Lending',
    tvl: 5240000000,
    apy: 3.2,
    chain: 'Ethereum',
    riskLevel: 'Medium',
    logo: 'https://cryptologos.cc/logos/aave-aave-logo.png'
  },
  {
    id: 'compound',
    name: 'Compound',
    category: 'Lending',
    tvl: 3180000000,
    apy: 2.8,
    chain: 'Ethereum',
    riskLevel: 'Medium',
    logo: 'https://cryptologos.cc/logos/compound-comp-logo.png'
  },
  {
    id: 'curve',
    name: 'Curve Finance',
    category: 'DEX',
    tvl: 4950000000,
    apy: 4.5,
    chain: 'Ethereum',
    riskLevel: 'Medium',
    logo: 'https://cryptologos.cc/logos/curve-dao-token-crv-logo.png'
  },
  {
    id: 'uniswap',
    name: 'Uniswap',
    category: 'DEX',
    tvl: 5780000000,
    apy: 2.1,
    chain: 'Ethereum',
    riskLevel: 'Medium',
    logo: 'https://cryptologos.cc/logos/uniswap-uni-logo.png'
  },
  {
    id: 'pancakeswap',
    name: 'PancakeSwap',
    category: 'DEX',
    tvl: 2340000000,
    apy: 8.4,
    chain: 'BSC',
    riskLevel: 'Medium-High',
    logo: 'https://cryptologos.cc/logos/pancakeswap-cake-logo.png'
  },
  {
    id: 'lido',
    name: 'Lido',
    category: 'Staking',
    tvl: 14250000000,
    apy: 3.8,
    chain: 'Ethereum',
    riskLevel: 'Low',
    logo: 'https://cryptologos.cc/logos/lido-dao-ldo-logo.png'
  },
  {
    id: 'yearn',
    name: 'Yearn Finance',
    category: 'Yield',
    tvl: 480000000,
    apy: 7.2,
    chain: 'Ethereum',
    riskLevel: 'Medium-High',
    logo: 'https://cryptologos.cc/logos/yearn-finance-yfi-logo.png'
  },
  {
    id: 'maker',
    name: 'MakerDAO',
    category: 'Lending',
    tvl: **********,
    apy: 1.5,
    chain: 'Ethereum',
    riskLevel: 'Low',
    logo: 'https://cryptologos.cc/logos/maker-mkr-logo.png'
  }
];

// Datos simulados para staking
const stakingOptionsData = [
  {
    id: 'eth',
    name: 'Ethereum',
    symbol: 'ETH',
    apy: 4.2,
    minStake: 0.1,
    lockPeriod: 'None',
    provider: 'Lido',
    logo: 'https://cryptologos.cc/logos/ethereum-eth-logo.png'
  },
  {
    id: 'sol',
    name: 'Solana',
    symbol: 'SOL',
    apy: 6.8,
    minStake: 1,
    lockPeriod: 'None',
    provider: 'Marinade',
    logo: 'https://cryptologos.cc/logos/solana-sol-logo.png'
  },
  {
    id: 'dot',
    name: 'Polkadot',
    symbol: 'DOT',
    apy: 14.5,
    minStake: 5,
    lockPeriod: '28 days',
    provider: 'Kraken',
    logo: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png'
  },
  {
    id: 'ada',
    name: 'Cardano',
    symbol: 'ADA',
    apy: 5.2,
    minStake: 10,
    lockPeriod: 'None',
    provider: 'Daedalus',
    logo: 'https://cryptologos.cc/logos/cardano-ada-logo.png'
  },
  {
    id: 'atom',
    name: 'Cosmos',
    symbol: 'ATOM',
    apy: 12.8,
    minStake: 1,
    lockPeriod: '21 days',
    provider: 'Keplr',
    logo: 'https://cryptologos.cc/logos/cosmos-atom-logo.png'
  },
  {
    id: 'algo',
    name: 'Algorand',
    symbol: 'ALGO',
    apy: 5.7,
    minStake: 1,
    lockPeriod: 'None',
    provider: 'Algorand Wallet',
    logo: 'https://cryptologos.cc/logos/algorand-algo-logo.png'
  }
];

// Datos simulados para préstamos
const lendingOptionsData = [
  {
    id: 'aave-usdc',
    platform: 'Aave',
    asset: 'USDC',
    depositAPY: 3.2,
    borrowAPY: 4.8,
    ltv: 80,
    totalDeposited: **********,
    totalBorrowed: 820000000,
    logo: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png'
  },
  {
    id: 'compound-dai',
    platform: 'Compound',
    asset: 'DAI',
    depositAPY: 2.9,
    borrowAPY: 4.5,
    ltv: 75,
    totalDeposited: 980000000,
    totalBorrowed: 620000000,
    logo: 'https://cryptologos.cc/logos/multi-collateral-dai-dai-logo.png'
  },
  {
    id: 'aave-eth',
    platform: 'Aave',
    asset: 'ETH',
    depositAPY: 1.8,
    borrowAPY: 3.2,
    ltv: 82.5,
    totalDeposited: **********,
    totalBorrowed: **********,
    logo: 'https://cryptologos.cc/logos/ethereum-eth-logo.png'
  },
  {
    id: 'compound-usdt',
    platform: 'Compound',
    asset: 'USDT',
    depositAPY: 3.1,
    borrowAPY: 4.7,
    ltv: 75,
    totalDeposited: 1120000000,
    totalBorrowed: 780000000,
    logo: 'https://cryptologos.cc/logos/tether-usdt-logo.png'
  },
  {
    id: 'maker-eth',
    platform: 'MakerDAO',
    asset: 'ETH',
    depositAPY: 0,
    borrowAPY: 2.5,
    ltv: 66.6,
    totalDeposited: 3250000000,
    totalBorrowed: 1950000000,
    logo: 'https://cryptologos.cc/logos/ethereum-eth-logo.png'
  },
  {
    id: 'aave-wbtc',
    platform: 'Aave',
    asset: 'WBTC',
    depositAPY: 1.2,
    borrowAPY: 2.8,
    ltv: 70,
    totalDeposited: 980000000,
    totalBorrowed: 520000000,
    logo: 'https://cryptologos.cc/logos/wrapped-bitcoin-wbtc-logo.png'
  }
];

// Datos simulados para yield farming
const yieldFarmingData = [
  {
    id: 'curve-3pool',
    name: 'Curve 3Pool',
    platform: 'Curve Finance',
    assets: ['USDC', 'USDT', 'DAI'],
    apy: 4.8,
    tvl: 580000000,
    risk: 'Low',
    rewards: ['CRV'],
    logo: 'https://cryptologos.cc/logos/curve-dao-token-crv-logo.png'
  },
  {
    id: 'sushi-eth-usdc',
    name: 'ETH-USDC',
    platform: 'SushiSwap',
    assets: ['ETH', 'USDC'],
    apy: 12.5,
    tvl: 245000000,
    risk: 'Medium',
    rewards: ['SUSHI'],
    logo: 'https://cryptologos.cc/logos/sushiswap-sushi-logo.png'
  },
  {
    id: 'pancake-cake-bnb',
    name: 'CAKE-BNB',
    platform: 'PancakeSwap',
    assets: ['CAKE', 'BNB'],
    apy: 28.4,
    tvl: 180000000,
    risk: 'Medium-High',
    rewards: ['CAKE'],
    logo: 'https://cryptologos.cc/logos/pancakeswap-cake-logo.png'
  },
  {
    id: 'yearn-usdc',
    name: 'USDC Vault',
    platform: 'Yearn Finance',
    assets: ['USDC'],
    apy: 8.2,
    tvl: 320000000,
    risk: 'Medium',
    rewards: ['Yield'],
    logo: 'https://cryptologos.cc/logos/yearn-finance-yfi-logo.png'
  },
  {
    id: 'convex-tricrypto',
    name: 'Tricrypto',
    platform: 'Convex Finance',
    assets: ['USDT', 'WBTC', 'ETH'],
    apy: 15.7,
    tvl: 210000000,
    risk: 'Medium-High',
    rewards: ['CRV', 'CVX'],
    logo: 'https://cryptologos.cc/logos/convex-finance-cvx-logo.png'
  },
  {
    id: 'balancer-eth-dai-usdc',
    name: 'ETH-DAI-USDC',
    platform: 'Balancer',
    assets: ['ETH', 'DAI', 'USDC'],
    apy: 6.9,
    tvl: 175000000,
    risk: 'Medium',
    rewards: ['BAL'],
    logo: 'https://cryptologos.cc/logos/balancer-bal-logo.png'
  }
];

const DefiCenterPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('dashboard');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedChain, setSelectedChain] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('tvl');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [investmentAmount, setInvestmentAmount] = useState<number>(1000);
  const [stakingPeriod, setStakingPeriod] = useState<number>(12);

  // Formatear números grandes
  const formatNumber = (num: number): string => {
    if (num >= 1_000_000_000) {
      return `$${(num / 1_000_000_000).toFixed(2)}B`;
    } else if (num >= 1_000_000) {
      return `$${(num / 1_000_000).toFixed(2)}M`;
    } else if (num >= 1_000) {
      return `$${(num / 1_000).toFixed(2)}K`;
    }
    return `$${num.toFixed(2)}`;
  };

  // Filtrar protocolos DeFi
  const filteredProtocols = defiProtocolsData.filter(protocol => {
    if (selectedCategory !== 'all' && protocol.category !== selectedCategory) {
      return false;
    }
    if (selectedChain !== 'all' && protocol.chain !== selectedChain) {
      return false;
    }
    return true;
  }).sort((a, b) => {
    if (sortBy === 'tvl') {
      return sortOrder === 'desc' ? b.tvl - a.tvl : a.tvl - b.tvl;
    } else if (sortBy === 'apy') {
      return sortOrder === 'desc' ? b.apy - a.apy : a.apy - b.apy;
    } else if (sortBy === 'name') {
      return sortOrder === 'desc' ? b.name.localeCompare(a.name) : a.name.localeCompare(b.name);
    }
    return 0;
  });

  // Calcular ganancias proyectadas para staking
  const calculateStakingReturns = (apy: number, amount: number, months: number): number => {
    // Fórmula simple de interés compuesto mensual
    const monthlyRate = apy / 100 / 12;
    return amount * Math.pow(1 + monthlyRate, months);
  };

  return (
    <div className="defi-center-page">
      <header className="defi-header">
        <Link to="/" className="back-link">
          ← Volver al Dashboard
        </Link>
        <h1>Centro DeFi</h1>
        <div className="defi-actions">
          <button className="connect-wallet-button">
            <i className="fas fa-wallet"></i> Conectar Wallet
          </button>
        </div>
      </header>

      <div className="defi-tabs">
        <button 
          className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}
          onClick={() => setActiveTab('dashboard')}
        >
          Dashboard de Protocolos
        </button>
        <button 
          className={`tab-button ${activeTab === 'staking' ? 'active' : ''}`}
          onClick={() => setActiveTab('staking')}
        >
          Staking
        </button>
        <button 
          className={`tab-button ${activeTab === 'yield' ? 'active' : ''}`}
          onClick={() => setActiveTab('yield')}
        >
          Yield Farming
        </button>
        <button 
          className={`tab-button ${activeTab === 'lending' ? 'active' : ''}`}
          onClick={() => setActiveTab('lending')}
        >
          Préstamos
        </button>
      </div>

      <div className="defi-content">
        {activeTab === 'dashboard' && (
          <div className="protocols-dashboard">
            <div className="dashboard-header">
              <div className="filter-controls">
                <div className="filter-group">
                  <label>Categoría:</label>
                  <select 
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    <option value="all">Todas</option>
                    <option value="Lending">Préstamos</option>
                    <option value="DEX">Exchanges</option>
                    <option value="Yield">Yield</option>
                    <option value="Staking">Staking</option>
                  </select>
                </div>
                <div className="filter-group">
                  <label>Blockchain:</label>
                  <select 
                    value={selectedChain}
                    onChange={(e) => setSelectedChain(e.target.value)}
                  >
                    <option value="all">Todas</option>
                    <option value="Ethereum">Ethereum</option>
                    <option value="BSC">BSC</option>
                    <option value="Polygon">Polygon</option>
                    <option value="Solana">Solana</option>
                  </select>
                </div>
                <div className="filter-group">
                  <label>Ordenar por:</label>
                  <select 
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                  >
                    <option value="tvl">TVL</option>
                    <option value="apy">APY</option>
                    <option value="name">Nombre</option>
                  </select>
                  <button 
                    className="sort-order-button"
                    onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                  >
                    {sortOrder === 'desc' ? '↓' : '↑'}
                  </button>
                </div>
              </div>
            </div>

            <div className="protocols-grid">
              {filteredProtocols.map(protocol => (
                <div className="protocol-card" key={protocol.id}>
                  <div className="protocol-header">
                    <img src={protocol.logo} alt={protocol.name} className="protocol-logo" />
                    <div className="protocol-title">
                      <h3>{protocol.name}</h3>
                      <span className="protocol-category">{protocol.category}</span>
                    </div>
                  </div>
                  <div className="protocol-stats">
                    <div className="stat-item">
                      <span className="stat-label">TVL</span>
                      <span className="stat-value">{formatNumber(protocol.tvl)}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">APY</span>
                      <span className="stat-value">{protocol.apy.toFixed(2)}%</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Blockchain</span>
                      <span className="stat-value">{protocol.chain}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Riesgo</span>
                      <span className={`stat-value risk-${protocol.riskLevel.toLowerCase().replace('-', '')}`}>
                        {protocol.riskLevel}
                      </span>
                    </div>
                  </div>
                  <div className="protocol-actions">
                    <button className="protocol-button">Explorar</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'staking' && (
          <div className="staking-section">
            <div className="staking-calculator">
              <h3>Calculadora de Staking</h3>
              <div className="calculator-form">
                <div className="form-group">
                  <label>Cantidad a invertir (USD)</label>
                  <input 
                    type="number" 
                    value={investmentAmount}
                    onChange={(e) => setInvestmentAmount(Number(e.target.value))}
                    min="1"
                  />
                </div>
                <div className="form-group">
                  <label>Período de staking (meses)</label>
                  <input 
                    type="number" 
                    value={stakingPeriod}
                    onChange={(e) => setStakingPeriod(Number(e.target.value))}
                    min="1"
                    max="60"
                  />
                </div>
              </div>
            </div>

            <h3>Opciones de Staking</h3>
            <div className="staking-options-grid">
              {stakingOptionsData.map(option => {
                const projectedReturn = calculateStakingReturns(option.apy, investmentAmount, stakingPeriod);
                const profit = projectedReturn - investmentAmount;
                
                return (
                  <div className="staking-option-card" key={option.id}>
                    <div className="option-header">
                      <img src={option.logo} alt={option.name} className="option-logo" />
                      <div className="option-title">
                        <h3>{option.name}</h3>
                        <span className="option-symbol">{option.symbol}</span>
                      </div>
                    </div>
                    <div className="option-stats">
                      <div className="stat-item">
                        <span className="stat-label">APY</span>
                        <span className="stat-value highlight">{option.apy.toFixed(2)}%</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Stake Mínimo</span>
                        <span className="stat-value">{option.minStake} {option.symbol}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Período de Bloqueo</span>
                        <span className="stat-value">{option.lockPeriod}</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-label">Proveedor</span>
                        <span className="stat-value">{option.provider}</span>
                      </div>
                    </div>
                    <div className="projected-returns">
                      <div className="returns-header">Retorno Proyectado ({stakingPeriod} meses)</div>
                      <div className="returns-value">${projectedReturn.toFixed(2)}</div>
                      <div className="returns-profit">+${profit.toFixed(2)} ({(profit / investmentAmount * 100).toFixed(2)}%)</div>
                    </div>
                    <div className="option-actions">
                      <button className="stake-button">Hacer Staking</button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {activeTab === 'yield' && (
          <div className="yield-farming-section">
            <div className="yield-info-banner">
              <div className="info-icon">
                <i className="fas fa-info-circle"></i>
              </div>
              <div className="info-content">
                <h3>¿Qué es Yield Farming?</h3>
                <p>Yield Farming es una estrategia donde los usuarios prestan o depositan sus criptomonedas en un protocolo DeFi y reciben recompensas. Estas recompensas pueden venir en forma de tasas de interés o tokens de gobernanza adicionales.</p>
                <div className="risk-warning">
                  <strong>Advertencia de Riesgo:</strong> El yield farming puede implicar riesgos significativos, incluyendo riesgo de impermanent loss, riesgos de smart contracts, y volatilidad de mercado.
                </div>
              </div>
            </div>

            <h3>Oportunidades de Yield Farming</h3>
            <div className="yield-farming-grid">
              {yieldFarmingData.map(pool => (
                <div className="yield-pool-card" key={pool.id}>
                  <div className="pool-header">
                    <img src={pool.logo} alt={pool.platform} className="pool-logo" />
                    <div className="pool-title">
                      <h3>{pool.name}</h3>
                      <span className="pool-platform">{pool.platform}</span>
                    </div>
                  </div>
                  <div className="pool-assets">
                    {pool.assets.map((asset, index) => (
                      <span key={index} className="asset-tag">{asset}</span>
                    ))}
                  </div>
                  <div className="pool-stats">
                    <div className="stat-item">
                      <span className="stat-label">APY</span>
                      <span className="stat-value highlight">{pool.apy.toFixed(2)}%</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">TVL</span>
                      <span className="stat-value">{formatNumber(pool.tvl)}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Riesgo</span>
                      <span className={`stat-value risk-${pool.risk.toLowerCase().replace('-', '')}`}>
                        {pool.risk}
                      </span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Recompensas</span>
                      <div className="rewards-list">
                        {pool.rewards.map((reward, index) => (
                          <span key={index} className="reward-tag">{reward}</span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="pool-actions">
                    <button className="pool-button">Depositar</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'lending' && (
          <div className="lending-section">
            <div className="lending-overview">
              <div className="overview-card deposit">
                <h3>Depósitos</h3>
                <div className="overview-value">
                  <i className="fas fa-arrow-down"></i>
                  <span>Gana intereses depositando tus activos</span>
                </div>
                <p>Presta tus criptomonedas a protocolos DeFi y gana intereses pasivos.</p>
              </div>
              <div className="overview-card borrow">
                <h3>Préstamos</h3>
                <div className="overview-value">
                  <i className="fas fa-arrow-up"></i>
                  <span>Toma prestado contra tu colateral</span>
                </div>
                <p>Usa tus activos como colateral para tomar préstamos sin vender tus criptomonedas.</p>
              </div>
            </div>

            <h3>Mercados de Préstamos</h3>
            <div className="lending-markets-table">
              <div className="table-header">
                <div className="header-cell asset">Activo</div>
                <div className="header-cell platform">Plataforma</div>
                <div className="header-cell deposit">APY Depósito</div>
                <div className="header-cell borrow">APY Préstamo</div>
                <div className="header-cell ltv">LTV</div>
                <div className="header-cell liquidity">Liquidez</div>
                <div className="header-cell actions">Acciones</div>
              </div>
              <div className="table-body">
                {lendingOptionsData.map(market => (
                  <div className="table-row" key={market.id}>
                    <div className="cell asset">
                      <img src={market.logo} alt={market.asset} className="asset-logo" />
                      <span>{market.asset}</span>
                    </div>
                    <div className="cell platform">{market.platform}</div>
                    <div className="cell deposit">{market.depositAPY.toFixed(2)}%</div>
                    <div className="cell borrow">{market.borrowAPY.toFixed(2)}%</div>
                    <div className="cell ltv">{market.ltv}%</div>
                    <div className="cell liquidity">
                      <div className="liquidity-bars">
                        <div className="liquidity-bar">
                          <div className="bar-label">Depositado</div>
                          <div className="bar-value">{formatNumber(market.totalDeposited)}</div>
                        </div>
                        <div className="liquidity-bar">
                          <div className="bar-label">Prestado</div>
                          <div className="bar-value">{formatNumber(market.totalBorrowed)}</div>
                        </div>
                      </div>
                    </div>
                    <div className="cell actions">
                      <button className="action-button deposit">Depositar</button>
                      <button className="action-button borrow">Pedir Préstamo</button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DefiCenterPage;
