/**
 * Módulo para cargar y procesar la configuración
 */
const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * Carga la configuración desde un archivo JSON
 * @param {string} configPath - Ruta al archivo de configuración
 * @returns {Object} - Configuración cargada y procesada
 */
function loadConfig(configPath = './config.json') {
  try {
    // Verificar si el archivo existe
    if (!fs.existsSync(configPath)) {
      console.error(`Error: El archivo de configuración ${configPath} no existe.`);
      process.exit(1);
    }

    // Cargar y parsear el archivo JSON
    const configData = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configData);

    // Procesar la configuración
    return processConfig(config);
  } catch (err) {
    console.error(`Error al cargar la configuración: ${err.message}`);
    process.exit(1);
  }
}

/**
 * Procesa y valida la configuración
 * @param {Object} config - Configuración cargada
 * @returns {Object} - Configuración procesada
 */
function processConfig(config) {
  // Verificar estructura básica
  if (!config.components || !Array.isArray(config.components)) {
    throw new Error('La configuración debe contener un array "components"');
  }

  // Determinar la plataforma
  const isWindows = os.platform() === 'win32';

  // Determinar el comando npm correcto según la plataforma
  const npmCmd = isWindows ? 'npm.cmd' : 'npm';

  // Determinar la ruta al ejecutable Python
  let pythonPath = isWindows 
    ? config.python?.venvPath?.windows 
    : config.python?.venvPath?.unix;

  // Verificar si la ruta al ejecutable Python existe
  if (pythonPath && !fs.existsSync(path.resolve(pythonPath))) {
    console.warn(`Advertencia: No se encontró el ejecutable Python en ${pythonPath}`);
    pythonPath = config.python?.fallback || 'python';
  }

  // Procesar cada componente
  for (const component of config.components) {
    // Validar campos requeridos
    if (!component.id || !component.name || !component.cmd || !component.cwd) {
      throw new Error(`Componente inválido: ${JSON.stringify(component)}`);
    }

    // Resolver variables en los comandos
    if (component.cmd === '${npm}') {
      component.cmd = npmCmd;
    } else if (component.cmd === '${python}') {
      component.cmd = pythonPath;
    }

    // Asegurarse de que cwd sea una ruta absoluta
    component.cwd = path.resolve(component.cwd);

    // Verificar si el directorio existe
    if (!fs.existsSync(component.cwd)) {
      console.warn(`Advertencia: El directorio ${component.cwd} no existe para el componente ${component.name}`);
    }

    // Establecer valores predeterminados
    component.delay = component.delay ?? config.system?.defaultDelay ?? 2000;
    component.dependencies = component.dependencies ?? [];
    component.healthCheck = component.healthCheck ?? { type: 'none', timeout: 5000 };
  }

  return config;
}

module.exports = { loadConfig };
