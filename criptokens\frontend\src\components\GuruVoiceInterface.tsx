import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/NewAuthContext';
import { io, Socket } from 'socket.io-client';
import '../styles/GuruVoiceInterface.css';

interface GuruVoiceInterfaceProps {
  onSendMessage: (message: string) => void;
  onReceiveResponse: (response: string) => void;
  isProcessing: boolean;
}

const GuruVoiceInterface: React.FC<GuruVoiceInterfaceProps> = ({
  onSendMessage,
  onReceiveResponse,
  isProcessing
}) => {
  const { currentUser } = useAuth();
  const [isListening, setIsListening] = useState<boolean>(false);
  const [transcript, setTranscript] = useState<string>('');
  const [callId, setCallId] = useState<string | null>(null);
  const [isCallActive, setIsCallActive] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [apiStatus, setApiStatus] = useState<'checking' | 'available' | 'unavailable'>('checking');
  const [credits, setCredits] = useState<number>(0);
  const [isPlayingAudio, setIsPlayingAudio] = useState<boolean>(false);
  const [audioMethod, setAudioMethod] = useState<'native' | 'webSpeech' | null>(null);
  const [showAudioOptions, setShowAudioOptions] = useState<boolean>(false);
  const [showApiKeyForm, setShowApiKeyForm] = useState<boolean>(false);
  const [newApiKey, setNewApiKey] = useState<string>('');
  const [isUpdatingApiKey, setIsUpdatingApiKey] = useState<boolean>(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Estados para los controles de audio
  const [volume, setVolume] = useState<number>(1.0); // Volumen (0.0 a 1.0)
  const [speechRate, setSpeechRate] = useState<number>(1.0); // Velocidad (0.5 a 2.0)
  const [showAudioControls, setShowAudioControls] = useState<boolean>(false);

  // Estado para mostrar el estado actual del Guru
  const [guruState, setGuruState] = useState<'idle' | 'listening' | 'processing' | 'speaking'>('idle');

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Verificar el estado de la API de Ultravox al cargar el componente
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        if (!currentUser) {
          console.log('No hay usuario autenticado');
          setApiStatus('unavailable');
          return;
        }

        const token = await currentUser.getIdToken();
        console.log('Token obtenido, verificando API de Ultravox...');

        const response = await fetch(`http://localhost:3001/api/ultravox/status`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'omit' // Cambiado de 'include' a 'omit' para evitar problemas de CORS
        });

        console.log('Respuesta recibida:', response.status, response.statusText);

        // Verificar si la respuesta es JSON válido
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('La respuesta no es JSON válido:', contentType);
          setApiStatus('unavailable');
          return;
        }

        const data = await response.json();
        console.log('Datos de respuesta:', data);

        if (data.success && data.isValid) {
          setApiStatus('available');
          setCredits(data.credits || 0);

          // Verificar si estamos en modo de simulación
          if (data.simulation) {
            console.log('API en modo de simulación:', data.message);
            // Mostrar un mensaje al usuario
            setError(`Nota: ${data.message}. Se utilizará la síntesis de voz del navegador.`);
          }
        } else {
          console.log('API no disponible según respuesta:', data);
          setApiStatus('unavailable');
        }
      } catch (error) {
        console.error('Error al verificar el estado de la API de Ultravox:', error);
        setApiStatus('unavailable');
      }
    };

    if (currentUser) {
      checkApiStatus();
    } else {
      setApiStatus('unavailable');
    }
  }, [currentUser]);

  // Inicializar el reconocimiento de voz
  useEffect(() => {
    console.log('Inicializando reconocimiento de voz...');

    // Verificar si el navegador soporta la API de reconocimiento de voz
    const hasSpeechRecognition = 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
    console.log('¿El navegador soporta SpeechRecognition?', hasSpeechRecognition);

    if (hasSpeechRecognition) {
      try {
        // Obtener la implementación correcta según el navegador
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        // Crear una nueva instancia
        recognitionRef.current = new SpeechRecognition();

        // Configurar opciones
        recognitionRef.current.continuous = true; // Reconocimiento continuo
        recognitionRef.current.interimResults = true; // Resultados intermedios
        recognitionRef.current.lang = 'es-ES'; // Idioma español

        console.log('Reconocimiento de voz configurado correctamente');

        // Configurar manejadores de eventos
        recognitionRef.current.onstart = () => {
          console.log('Reconocimiento de voz iniciado');
          setIsListening(true);
          setGuruState('listening');
        };

        recognitionRef.current.onresult = (event) => {
          console.log('Resultado de reconocimiento recibido:', event.results);
          const current = event.resultIndex;
          const result = event.results[current];
          const transcriptText = result[0].transcript;

          console.log('Transcripción:', transcriptText, 'Es final:', result.isFinal);
          setTranscript(transcriptText);

          // Mantener el estado de escucha mientras se reciben resultados
          setGuruState('listening');
        };

        recognitionRef.current.onerror = (event) => {
          console.error('Error en el reconocimiento de voz:', event.error);
          setError(`Error en el reconocimiento de voz: ${event.error}`);
          setIsListening(false);
          setGuruState('idle');
        };

        recognitionRef.current.onend = () => {
          console.log('Reconocimiento de voz finalizado, isListening:', isListening);

          // Si todavía estamos en modo de escucha, reiniciar el reconocimiento
          if (isListening) {
            console.log('Reiniciando reconocimiento de voz...');
            setTimeout(() => {
              recognitionRef.current?.start();
            }, 100);
          } else {
            // Si no estamos escuchando y no estamos reproduciendo audio, volver a idle
            if (!isPlayingAudio) {
              setGuruState('idle');
            }
          }
        };

        console.log('Manejadores de eventos configurados correctamente');
      } catch (error) {
        console.error('Error al inicializar el reconocimiento de voz:', error);
        setError(`Error al inicializar el reconocimiento de voz: ${error.message}`);
      }
    } else {
      console.error('El reconocimiento de voz no está soportado en este navegador');
      setError('El reconocimiento de voz no está soportado en este navegador.');
    }

    // Limpiar al desmontar
    return () => {
      console.log('Limpiando reconocimiento de voz...');

      if (recognitionRef.current) {
        try {
          // Eliminar manejadores de eventos
          recognitionRef.current.onstart = null;
          recognitionRef.current.onresult = null;
          recognitionRef.current.onerror = null;
          recognitionRef.current.onend = null;

          // Detener el reconocimiento si está activo
          if (isListening) {
            console.log('Deteniendo reconocimiento de voz al desmontar');
            recognitionRef.current.stop();
          }
        } catch (error) {
          console.error('Error al limpiar reconocimiento de voz:', error);
        }
      }
    };
  }, [isListening]);

  // Inicializar el elemento de audio
  useEffect(() => {
    audioRef.current = new Audio();

    // Configurar eventos para el elemento de audio
    if (audioRef.current) {
      audioRef.current.onplay = () => {
        console.log('Audio iniciado');
      };

      audioRef.current.onended = () => {
        console.log('Audio finalizado');
      };

      audioRef.current.onerror = (e) => {
        console.error('Error en la reproducción de audio:', e);
        setError('Error al reproducir el audio. Inténtalo de nuevo más tarde.');
      };
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.onplay = null;
        audioRef.current.onended = null;
        audioRef.current.onerror = null;
        audioRef.current = null;
      }
    };
  }, []);

  // Conectar a Socket.IO
  useEffect(() => {
    // Crear conexión a Socket.IO
    const newSocket = io('http://localhost:3008');
    setSocket(newSocket);

    // Configurar eventos
    newSocket.on('connect', () => {
      console.log('Conectado a Socket.IO:', newSocket.id);
    });

    newSocket.on('disconnect', () => {
      console.log('Desconectado de Socket.IO');
    });

    newSocket.on('transcript', (data) => {
      console.log('Transcripción recibida:', data);

      // Actualizar la interfaz con la transcripción
      if (data.callId === callId) {
        // Enviar la respuesta al componente padre
        onReceiveResponse(data.text);
      }
    });

    newSocket.on('audio', (data) => {
      console.log('Audio recibido:', data);

      // Reproducir el audio
      if (data.callId === callId) {
        playAudio(data.audioUrl);
      }
    });

    return () => {
      newSocket.disconnect();
    };
  }, []);

  // Función para iniciar una llamada de voz
  const startVoiceCall = async () => {
    if (!currentUser) {
      setError('Debes iniciar sesión para usar esta función.');
      return;
    }

    if (apiStatus !== 'available') {
      setError('La API de voz no está disponible en este momento.');
      return;
    }

    if (credits <= 0) {
      setError('No tienes suficientes créditos para usar esta función.');
      return;
    }

    try {
      const token = await currentUser.getIdToken();
      console.log('Iniciando llamada de voz...');

      const response = await fetch(`http://localhost:3008/api/ultravox/calls`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'omit',
        body: JSON.stringify({
          systemPrompt: `Eres el Guru Cripto, un experto en criptomonedas y blockchain.
          Estás interactuando con el usuario por voz, así que habla de manera casual y natural.
          Mantén tus respuestas cortas y directas, como lo harías en una conversación real.
          No uses listas, viñetas, emojis u otros elementos que no se traduzcan bien a voz.
          Proporciona información precisa y útil sobre criptomonedas, blockchain, DeFi, NFTs y temas relacionados.`,
          language: 'es'
        })
      });

      console.log('Respuesta de inicio de llamada:', response.status, response.statusText);

      // Verificar si la respuesta es JSON válido
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('La respuesta no es JSON válido:', contentType);
        setError('Error al iniciar la llamada: respuesta no válida del servidor');
        return;
      }

      const data = await response.json();
      console.log('Datos de respuesta de inicio de llamada:', data);

      if (data.success && data.call) {
        const newCallId = data.call.id;
        setCallId(newCallId);
        setIsCallActive(true);
        setCredits(data.credits || 0);

        // Unirse a la sala de Socket.IO para esta llamada
        if (socket) {
          socket.emit('join-call', newCallId);
          console.log('Unido a sala de Socket.IO para llamada:', newCallId);
        }

        // Iniciar el reconocimiento de voz
        startListening();
      } else {
        setError(data.message || 'Error al iniciar la llamada de voz.');
      }
    } catch (error) {
      console.error('Error al iniciar la llamada de voz:', error);
      setError('Error al iniciar la llamada de voz. Inténtalo de nuevo más tarde.');
    }
  };

  // Función para actualizar la API key de Ultravox
  const updateApiKey = async () => {
    if (!newApiKey.trim()) {
      setError('La API key no puede estar vacía');
      return;
    }

    try {
      setIsUpdatingApiKey(true);
      setError(null);

      console.log('Actualizando API key de Ultravox...');

      const response = await fetch(`http://localhost:3008/api/ultravox/apikey`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'omit',
        body: JSON.stringify({
          apiKey: newApiKey
        })
      });

      console.log('Respuesta de actualización de API key:', response.status, response.statusText);

      // Verificar si la respuesta es JSON válido
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('La respuesta no es JSON válido:', contentType);
        setError('Error al actualizar la API key: respuesta no válida del servidor');
        setIsUpdatingApiKey(false);
        return;
      }

      const data = await response.json();
      console.log('Datos de respuesta de actualización de API key:', data);

      if (data.success) {
        // Limpiar el formulario
        setNewApiKey('');
        setShowApiKeyForm(false);

        // Mostrar mensaje de éxito
        setError(`API key actualizada correctamente. ${data.status?.message || ''}`);

        // Verificar el estado de la API
        if (data.status?.isValid) {
          setApiStatus('available');
          setCredits(data.status.credits || 0);
        } else {
          setApiStatus('unavailable');
        }
      } else {
        setError(data.message || 'Error al actualizar la API key');
      }
    } catch (error) {
      console.error('Error al actualizar la API key:', error);
      setError('Error al actualizar la API key. Inténtalo de nuevo más tarde.');
    } finally {
      setIsUpdatingApiKey(false);
    }
  };

  // Función para finalizar una llamada de voz
  const endVoiceCall = async () => {
    if (!callId) return;

    try {
      const token = await currentUser?.getIdToken();
      console.log('Finalizando llamada de voz...');

      const response = await fetch(`http://localhost:3008/api/ultravox/calls/${callId}/end`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'omit'
      });

      console.log('Respuesta de finalización de llamada:', response.status, response.statusText);

      setCallId(null);
      setIsCallActive(false);
      stopListening();
    } catch (error) {
      console.error('Error al finalizar la llamada de voz:', error);
      setError('Error al finalizar la llamada. La sesión se cerrará de todos modos.');
      setCallId(null);
      setIsCallActive(false);
      stopListening();
    }
  };

  // Función para reproducir audio
  const playAudio = async (audioUrl: string) => {
    try {
      console.log('Reproduciendo audio:', audioUrl);

      // Indicar que se está reproduciendo audio
      setIsPlayingAudio(true);
      setAudioMethod('native');
      setGuruState('speaking');

      // Añadir un timestamp para evitar problemas de caché
      let fixedAudioUrl = audioUrl;
      const audioUrlWithTimestamp = `${fixedAudioUrl}?t=${new Date().getTime()}`;

      // Crear un nuevo elemento de audio para cada reproducción
      const audio = new Audio();

      // Aplicar el volumen configurado
      audio.volume = volume;

      // Configurar eventos antes de establecer la fuente
      audio.onloadedmetadata = () => {
        console.log('Audio metadata cargada, duración:', audio.duration);

        // Verificar si el audio es muy pequeño (probablemente un fallback)
        if (audio.duration < 0.1) {
          console.log('Audio detectado como fallback (duración muy corta)');
          // Cancelar la reproducción nativa y usar Web Speech API
          audio.pause();
          setIsPlayingAudio(false);
          setGuruState('idle');

          // Intentar reproducir usando Web Speech API como fallback
          tryWebSpeechFallback(audioUrl);
          return;
        }
      };

      audio.oncanplaythrough = () => {
        console.log('Audio listo para reproducir sin interrupciones');
        // Intentar reproducir el audio cuando esté listo
        audio.play().catch(error => {
          console.error('Error al reproducir el audio:', error);
          setError(`Error al reproducir el audio: ${error.message}`);
          setIsPlayingAudio(false);
          setGuruState('idle');

          // Intentar reproducir usando Web Speech API como fallback
          tryWebSpeechFallback(audioUrl);
        });
      };

      audio.onplay = () => {
        console.log('Audio iniciado');
        setIsPlayingAudio(true);
        setGuruState('speaking');
      };

      audio.onended = () => {
        console.log('Audio finalizado');
        setIsPlayingAudio(false);
        setAudioMethod(null);
        setGuruState('idle');
      };

      audio.onerror = (e) => {
        console.error('Error en la reproducción de audio:', e);
        setError('Error al reproducir el audio. Intentando método alternativo...');
        setIsPlayingAudio(false);
        setGuruState('idle');

        // Intentar reproducir usando Web Speech API como fallback
        tryWebSpeechFallback(audioUrl);
      };

      // Establecer la fuente después de configurar los eventos
      audio.src = audioUrlWithTimestamp;

      // Forzar la carga del audio
      audio.load();
    } catch (error) {
      console.error('Error al reproducir audio:', error);
      setError(`Error al reproducir audio: ${error}`);
      setIsPlayingAudio(false);
      setGuruState('idle');

      // Intentar reproducir usando Web Speech API como fallback
      tryWebSpeechFallback(audioUrl);
    }
  };

  // Función para intentar reproducir texto usando Web Speech API como fallback
  const tryWebSpeechFallback = async (audioUrl: string) => {
    try {
      console.log('Intentando reproducir con Web Speech API como fallback');

      // Indicar que se está reproduciendo audio con Web Speech API
      setIsPlayingAudio(true);
      setAudioMethod('webSpeech');
      setGuruState('speaking');

      // Extraer el ID del audio de la URL
      const audioId = audioUrl.split('/').pop()?.split('?')[0];

      if (!audioId) {
        console.error('No se pudo extraer el ID del audio de la URL:', audioUrl);
        setIsPlayingAudio(false);
        setAudioMethod(null);
        setGuruState('idle');
        return;
      }

      // Obtener el texto correspondiente al ID del audio
      let text = '';
      switch (audioId) {
        case 'welcome':
          text = 'Hola, soy el Guru Cripto. Estoy aquí para ayudarte con tus consultas sobre criptomonedas y blockchain. ¿En qué puedo ayudarte hoy?';
          break;
        case 'bitcoin':
          text = 'Bitcoin es la primera y más conocida criptomoneda. Actualmente tiene un precio aproximado de $63,000 USD y una capitalización de mercado de más de 1 billón de dólares.';
          break;
        case 'ethereum':
          text = 'Ethereum es la segunda criptomoneda más grande por capitalización de mercado. Es una plataforma descentralizada que permite la creación de contratos inteligentes y aplicaciones descentralizadas.';
          break;
        case 'precios':
          text = 'Los precios de las criptomonedas son muy volátiles. Bitcoin está alrededor de $63,000 USD, Ethereum cerca de $3,000 USD, y Binance Coin aproximadamente $500 USD.';
          break;
        case 'test':
          text = 'Este es un audio de prueba para verificar que la reproducción de audio funciona correctamente.';
          break;
        default:
          // Si no podemos determinar el texto, intentar obtenerlo del servidor
          try {
            const response = await fetch(`http://localhost:3008/api/ultravox/text/${audioId}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              credentials: 'omit'
            });

            if (response.ok) {
              const data = await response.json();
              if (data.success && data.text) {
                text = data.text;
              } else {
                text = 'No se pudo obtener el texto para este audio.';
              }
            } else {
              text = 'No se pudo obtener el texto para este audio.';
            }
          } catch (error) {
            console.error('Error al obtener el texto del audio:', error);
            text = 'No se pudo obtener el texto para este audio.';
          }
      }

      // Usar Web Speech API para reproducir el texto
      if ('speechSynthesis' in window && text) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'es-ES';
        utterance.rate = speechRate; // Usar la velocidad configurada por el usuario
        utterance.volume = volume; // Usar el volumen configurado por el usuario
        utterance.pitch = 1.0;

        // Intentar encontrar una voz en español
        let voices = window.speechSynthesis.getVoices();

        // Si no hay voces disponibles, esperar a que se carguen
        if (voices.length === 0) {
          console.log('Esperando a que se carguen las voces...');

          // Esperar a que se carguen las voces
          await new Promise<void>((resolve) => {
            window.speechSynthesis.onvoiceschanged = () => {
              voices = window.speechSynthesis.getVoices();
              console.log('Voces cargadas:', voices.length);
              resolve();
            };

            // Timeout por si no se dispara el evento
            setTimeout(() => {
              console.log('Timeout esperando voces');
              resolve();
            }, 1000);
          });
        }

        console.log('Voces disponibles:', voices.map(v => `${v.name} (${v.lang})`).join(', '));

        // Buscar voces en español, priorizando las de mejor calidad
        const spanishVoices = voices.filter(voice => voice.lang.includes('es'));
        console.log('Voces en español:', spanishVoices.map(v => `${v.name} (${v.lang})`).join(', '));

        // Priorizar voces neurales o premium si están disponibles
        const premiumVoice = spanishVoices.find(voice =>
          voice.name.toLowerCase().includes('neural') ||
          voice.name.toLowerCase().includes('premium') ||
          voice.name.toLowerCase().includes('enhanced')
        );

        if (premiumVoice) {
          console.log('Usando voz premium:', premiumVoice.name);
          utterance.voice = premiumVoice;
        } else if (spanishVoices.length > 0) {
          console.log('Usando voz en español estándar:', spanishVoices[0].name);
          utterance.voice = spanishVoices[0];
        } else {
          console.log('No se encontraron voces en español, usando voz por defecto');
        }

        // Configurar eventos para la síntesis de voz
        utterance.onstart = () => {
          console.log('Síntesis de voz iniciada');
          setIsPlayingAudio(true);
          setGuruState('speaking');
        };

        utterance.onend = () => {
          console.log('Síntesis de voz finalizada');
          setIsPlayingAudio(false);
          setAudioMethod(null);
          setGuruState('idle');

          // Limpiar el error después de que termine la síntesis
          setError(null);
        };

        utterance.onerror = (event) => {
          console.error('Error en la síntesis de voz:', event);
          setIsPlayingAudio(false);
          setAudioMethod(null);
          setGuruState('idle');
          setError('Error en la síntesis de voz. Inténtalo de nuevo más tarde.');
        };

        window.speechSynthesis.speak(utterance);
        console.log('Reproduciendo audio con Web Speech API');
      } else {
        console.error('Web Speech API no está disponible en este navegador');
        setIsPlayingAudio(false);
        setAudioMethod(null);
        setGuruState('idle');
        setError('No se pudo reproducir el audio. Web Speech API no está disponible.');
      }
    } catch (error) {
      console.error('Error al intentar reproducir con Web Speech API:', error);
      setIsPlayingAudio(false);
      setAudioMethod(null);
      setGuruState('idle');
      setError('No se pudo reproducir el audio con ningún método disponible.');
    }
  };

  // Función para enviar un mensaje de voz
  const sendVoiceMessage = async () => {
    if (!callId || !transcript.trim()) return;

    try {
      console.log('Enviando mensaje de voz...');
      setGuruState('processing');

      // Enviar el mensaje al componente padre
      onSendMessage(transcript);

      // Enviar el mensaje al backend
      const response = await fetch(`http://localhost:3008/api/ultravox/calls/${callId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'omit',
        body: JSON.stringify({
          message: transcript
        })
      });

      console.log('Respuesta de envío de mensaje:', response.status, response.statusText);

      // Verificar si la respuesta es JSON válido
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('La respuesta no es JSON válido:', contentType);
        setError('Error al enviar el mensaje: respuesta no válida del servidor');
        return;
      }

      const data = await response.json();
      console.log('Datos de respuesta de envío de mensaje:', data);

      if (data.success) {
        setCredits(data.credits || 0);

        // Procesar la respuesta directa si está disponible
        if (data.response) {
          console.log('Respuesta directa recibida:', data.response);

          // Enviar la respuesta al componente padre
          onReceiveResponse(data.response.content);

          // Reproducir el audio si está disponible
          if (data.response.audioUrl) {
            await playAudio(data.response.audioUrl);
          }
        } else {
          // Si no hay respuesta directa, obtener los mensajes de la llamada
          console.log('Obteniendo mensajes de la llamada...');
          const messagesResponse = await fetch(`http://localhost:3001/api/ultravox/calls/${callId}/messages`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            credentials: 'omit'
          });

          console.log('Respuesta de obtención de mensajes:', messagesResponse.status, messagesResponse.statusText);

          // Verificar si la respuesta es JSON válido
          const messagesContentType = messagesResponse.headers.get('content-type');
          if (!messagesContentType || !messagesContentType.includes('application/json')) {
            console.error('La respuesta de mensajes no es JSON válido:', messagesContentType);
            setError('Error al obtener mensajes: respuesta no válida del servidor');
            return;
          }

          const messagesData = await messagesResponse.json();
          console.log('Datos de respuesta de obtención de mensajes:', messagesData);

          if (messagesData.success && messagesData.messages) {
            // Obtener la última respuesta del asistente
            const assistantMessages = messagesData.messages.filter(
              (msg: any) => msg.role === 'assistant'
            );

            if (assistantMessages.length > 0) {
              const lastMessage = assistantMessages[assistantMessages.length - 1];
              onReceiveResponse(lastMessage.content);

              // Reproducir el audio si está disponible
              if (lastMessage.audioUrl) {
                await playAudio(lastMessage.audioUrl);
              }
            }
          } else {
            console.warn('No se pudieron obtener los mensajes o no hay mensajes disponibles');
          }
        }
      } else {
        setError(data.message || 'Error al enviar el mensaje de voz.');
      }

      // Limpiar el transcript
      setTranscript('');
    } catch (error) {
      console.error('Error al enviar el mensaje de voz:', error);
      setError('Error al enviar el mensaje de voz. Inténtalo de nuevo más tarde.');
    }
  };

  // Función para iniciar el reconocimiento de voz
  const startListening = async () => {
    try {
      console.log('Solicitando permisos de micrófono...');

      // Solicitar permisos de micrófono explícitamente antes de iniciar el reconocimiento
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('Permisos de micrófono concedidos:', stream.getAudioTracks());

      // Detener el stream inmediatamente, solo lo necesitamos para solicitar permisos
      stream.getTracks().forEach(track => track.stop());

      // Iniciar el reconocimiento de voz
      if (recognitionRef.current) {
        console.log('Iniciando reconocimiento de voz...');
        recognitionRef.current.start();
        setIsListening(true);
        setError(null);
      } else {
        console.error('El objeto de reconocimiento de voz no está inicializado');
        setError('Error al iniciar el reconocimiento de voz. Inténtalo de nuevo más tarde.');
      }
    } catch (error) {
      console.error('Error al solicitar permisos de micrófono:', error);
      setError(`Error al solicitar permisos de micrófono: ${error.message || 'Permiso denegado'}`);
    }
  };

  // Función para detener el reconocimiento de voz
  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  };

  // Función para alternar el reconocimiento de voz
  const toggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      if (!isCallActive) {
        startVoiceCall();
      } else {
        startListening();
      }
    }
  };

  // Función para manejar el envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (transcript.trim()) {
      sendVoiceMessage();
    }
  };

  // Opciones de audio para probar
  const audioOptions = [
    { id: 'welcome', name: 'Bienvenida' },
    { id: 'bitcoin', name: 'Bitcoin' },
    { id: 'ethereum', name: 'Ethereum' },
    { id: 'precios', name: 'Precios' },
    { id: 'test', name: 'Audio de prueba' }
  ];

  // Función para probar la reproducción de audio
  const testAudio = async () => {
    try {
      console.log('Mostrando opciones de audio...');
      setShowAudioOptions(!showAudioOptions);
    } catch (error) {
      console.error('Error al mostrar opciones de audio:', error);
      setError(`Error al mostrar opciones de audio: ${error}`);
    }
  };

  // Función para iniciar una conversación con el Guru
  const startGuruConversation = async () => {
    try {
      console.log('Iniciando conversación con el Guru...');

      // Ocultar el menú de opciones si está visible
      if (showAudioOptions) {
        setShowAudioOptions(false);
      }

      // Reproducir el audio de bienvenida del Guru
      await playAudio('http://localhost:3001/api/ultravox/audio/welcome');

      // Iniciar la llamada de voz después de reproducir el audio de bienvenida
      if (!isCallActive) {
        startVoiceCall();
      }
    } catch (error) {
      console.error('Error al iniciar conversación con el Guru:', error);
      setError(`Error al iniciar conversación con el Guru: ${error}`);
    }
  };

  // Función para reproducir un audio específico
  const playSpecificAudio = async (audioId: string, audioName: string) => {
    try {
      console.log(`Reproduciendo audio de prueba: ${audioName}`);

      // Ocultar el menú de opciones
      setShowAudioOptions(false);

      // URL del audio seleccionado
      const audioUrl = `http://localhost:3001/api/ultravox/audio/${audioId}`;

      // Reproducir el audio
      await playAudio(audioUrl);
    } catch (error) {
      console.error('Error en la prueba de audio:', error);
      setError(`Error en la prueba de audio: ${error}`);
    }
  };

  return (
    <div className={`guru-voice-interface ${isPlayingAudio ? 'playing-audio' : ''}`}>
      <div className="voice-interface-header">
        <h3>Interfaz de Voz del Guru</h3>
        <div className="voice-status">
          {apiStatus === 'checking' && <span className="status checking">Verificando API...</span>}
          {apiStatus === 'available' && <span className="status available">API disponible</span>}
          {apiStatus === 'unavailable' && (
            <span className="status unavailable">
              API no disponible
              <button
                className="api-key-button"
                onClick={() => setShowApiKeyForm(!showApiKeyForm)}
                title="Actualizar API key"
              >
                <i className="fas fa-key"></i>
              </button>
            </span>
          )}
          <span className="credits">Créditos: {credits}</span>
          {audioMethod === 'webSpeech' && (
            <span className="audio-method webspeech">
              <i className="fas fa-volume-up"></i> Usando síntesis de voz del navegador
            </span>
          )}

          {/* Botón para mostrar/ocultar controles de audio */}
          <button
            className="audio-controls-toggle"
            onClick={() => setShowAudioControls(!showAudioControls)}
            title="Ajustes de audio"
          >
            <i className="fas fa-sliders-h"></i>
          </button>
        </div>
      </div>

      {/* Controles de audio (volumen y velocidad) */}
      {showAudioControls && (
        <div className="audio-controls-panel">
          <div className="audio-control">
            <label htmlFor="volume-control">
              <i className="fas fa-volume-up"></i> Volumen: {Math.round(volume * 100)}%
            </label>
            <input
              id="volume-control"
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
            />
          </div>

          <div className="audio-control">
            <label htmlFor="speed-control">
              <i className="fas fa-tachometer-alt"></i> Velocidad: {speechRate}x
            </label>
            <input
              id="speed-control"
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={speechRate}
              onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
            />
          </div>
        </div>
      )}

      {showApiKeyForm && (
        <div className="api-key-form">
          <h4>Actualizar API Key de Ultravox</h4>
          <div className="form-group">
            <input
              type="text"
              value={newApiKey}
              onChange={(e) => setNewApiKey(e.target.value)}
              placeholder="Ingresa tu API key de Ultravox"
              disabled={isUpdatingApiKey}
            />
            <button
              onClick={updateApiKey}
              disabled={!newApiKey.trim() || isUpdatingApiKey}
            >
              {isUpdatingApiKey ? 'Actualizando...' : 'Actualizar'}
            </button>
            <button
              onClick={() => {
                setShowApiKeyForm(false);
                setNewApiKey('');
              }}
              disabled={isUpdatingApiKey}
            >
              Cancelar
            </button>
          </div>
          <p className="api-key-help">
            Obtén tu API key en <a href="https://ultravox.ai" target="_blank" rel="noopener noreferrer">Ultravox.ai</a>
          </p>
        </div>
      )}

      {error && (
        <div className="voice-error">
          <p>{error}</p>
          <button onClick={() => setError(null)}>Cerrar</button>
        </div>
      )}

      {/* Indicador de estado del Guru */}
      <div className={`guru-status-indicator ${guruState}`}>
        {guruState === 'idle' && (
          <div className="guru-status idle">
            <i className="fas fa-circle"></i>
            <span>Guru en espera</span>
          </div>
        )}
        {guruState === 'listening' && (
          <div className="guru-status listening">
            <div className="listening-animation">
              <div className="circle"></div>
              <div className="circle"></div>
              <div className="circle"></div>
            </div>
            <span>Escuchando...</span>
          </div>
        )}
        {guruState === 'processing' && (
          <div className="guru-status processing">
            <div className="processing-animation">
              <i className="fas fa-cog fa-spin"></i>
            </div>
            <span>Procesando...</span>
          </div>
        )}
        {guruState === 'speaking' && (
          <div className="guru-status speaking">
            <div className="audio-animation">
              <div className="bar"></div>
              <div className="bar"></div>
              <div className="bar"></div>
              <div className="bar"></div>
              <div className="bar"></div>
            </div>
            <span>
              {audioMethod === 'native'
                ? 'Hablando...'
                : 'Hablando (síntesis)...'}
            </span>
          </div>
        )}
      </div>

      <div className="voice-controls">
        {!isCallActive ? (
          <button
            className="start-conversation-button"
            onClick={startGuruConversation}
            disabled={apiStatus !== 'available' || isProcessing || isPlayingAudio}
            title="Iniciar conversación con el Guru"
          >
            <i className="fas fa-comment-dots"></i>
            Hablar con el Guru
          </button>
        ) : (
          <button
            className={`voice-button ${isListening ? 'listening' : ''} ${isCallActive ? 'active' : ''}`}
            onClick={toggleListening}
            disabled={apiStatus !== 'available' || isProcessing || isPlayingAudio}
          >
            {isListening ? (
              <i className="fas fa-microphone-slash"></i>
            ) : (
              <i className="fas fa-microphone"></i>
            )}
            {isListening ? 'Detener' : 'Hablar'}
          </button>
        )}

        {isCallActive && (
          <button
            className="end-call-button"
            onClick={endVoiceCall}
            disabled={isProcessing || isPlayingAudio}
          >
            <i className="fas fa-phone-slash"></i>
            Finalizar Conversación
          </button>
        )}

        <div className="audio-test-container">
          <button
            className="test-audio-button"
            onClick={testAudio}
            disabled={isProcessing || isPlayingAudio}
            title="Probar reproducción de audio"
          >
            <i className="fas fa-volume-up"></i>
            Probar Voces
          </button>

          {showAudioOptions && (
            <div className="audio-options-menu">
              {audioOptions.map((option) => (
                <button
                  key={option.id}
                  className="audio-option-button"
                  onClick={() => playSpecificAudio(option.id, option.name)}
                  disabled={isProcessing || isPlayingAudio}
                >
                  {option.name}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {isListening && (
        <form className="voice-form" onSubmit={handleSubmit}>
          <div className="transcript-container">
            <p className="transcript">{transcript || 'Habla ahora...'}</p>
          </div>

          <button
            type="submit"
            className="send-voice-button"
            disabled={!transcript.trim() || isProcessing || isPlayingAudio}
          >
            <i className="fas fa-paper-plane"></i>
            Enviar
          </button>
        </form>
      )}
    </div>
  );
};

export default GuruVoiceInterface;
