const axios = require('axios');

/**
 * Realiza una búsqueda web utilizando el servidor Brave Search
 * @param {string} query - Consulta de búsqueda
 * @param {number} count - Número de resultados a devolver (1-10)
 * @param {number} offset - Desplazamiento para paginación (no utilizado actualmente)
 * @param {string} freshness - Filtro de tiempo ('pd'=último día, 'pw'=última semana, 'pm'=último mes, 'py'=último año)
 * @returns {Promise<Array>} - Resultados de la búsqueda
 */
async function searchNews(query, count = 5, offset = 0, freshness = 'pm') {
  try {
    console.log(`Realizando búsqueda para: "${query}" (count: ${count}, freshness: ${freshness})`);

    // URL del servidor Brave Search
    const braveSearchServerUrl = process.env.BRAVE_SEARCH_SERVER_URL || 'http://localhost:3102';

    // Utilizar el servidor Brave Search para realizar la búsqueda
    console.log('Utilizando el servidor Brave Search...');
    const response = await axios.post(`${braveSearchServerUrl}/search`, {
      query,
      count,
      freshness
    }, {
      timeout: 10000 // 10 segundos de timeout
    });

    if (response.data && response.data.results) {
      console.log(`Recibidos ${response.data.results.length} resultados del servidor Brave Search`);

      // Verificar si hay resultados
      if (response.data.results.length === 0) {
        console.log('No se encontraron resultados para la consulta');
      } else {
        // Mostrar un resumen de los resultados
        console.log('Resumen de resultados:');
        response.data.results.forEach((result, index) => {
          console.log(`${index + 1}. ${result.title} - ${result.url}`);
        });
      }

      return response.data.results;
    } else {
      console.log('No se recibieron resultados del servidor Brave Search');
      return [];
    }
  } catch (error) {
    console.error('Error al buscar con Brave Search:', error.message);

    // Verificar si el error es por falta de API key
    if (error.response && error.response.status === 401 && error.response.data && error.response.data.error === 'API key no configurada') {
      console.error('Se requiere una API key válida para Brave Search');
      throw new Error('Se requiere una API key válida para Brave Search. Por favor, configura la variable de entorno BRAVE_API_KEY.');
    }

    // Otros errores
    if (error.response) {
      console.error('Detalles del error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    }

    // Devolver un array vacío en caso de error
    return [];
  }
}

module.exports = {
  searchNews
};
