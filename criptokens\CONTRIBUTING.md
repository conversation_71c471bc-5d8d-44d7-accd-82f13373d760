# Guía de Contribución

¡Gracias por tu interés en contribuir al proyecto Criptokens! Esta guía te ayudará a configurar el entorno de desarrollo y a entender nuestro proceso de contribución.

## Configuración del Entorno de Desarrollo

1. **Clonar el repositorio**

```bash
git clone <url-del-repositorio>
cd criptokens
```

2. **Instalar dependencias**

```bash
# Para el backend
cd backend
npm install

# Para el frontend
cd ../frontend
npm install
```

3. **Configurar variables de entorno**

Crea un archivo `.env` en la carpeta `backend` basado en el archivo `.env.example`.

## Proceso de Contribución

1. **Crear una rama**

```bash
git checkout -b feature/nombre-de-la-caracteristica
```

Usa prefijos como `feature/`, `bugfix/`, `docs/`, etc., según corresponda.

2. **Real<PERSON>r camb<PERSON>**

Realiza tus cambios siguiendo las convenciones de código del proyecto.

3. **Ejecutar pruebas**

Asegúrate de que tus cambios no rompen ninguna funcionalidad existente.

```bash
# En el backend
cd backend
npm test

# En el frontend
cd frontend
npm test
```

4. **Enviar un Pull Request**

- Haz push de tu rama a tu fork del repositorio
- Crea un Pull Request desde tu rama a la rama principal del repositorio original
- Describe claramente los cambios realizados y cualquier información relevante

## Convenciones de Código

### Estilo de Código

- Usa 2 espacios para la indentación
- Usa punto y coma al final de cada declaración
- Usa comillas simples para strings
- Sigue las convenciones de ESLint configuradas en el proyecto

### Mensajes de Commit

Seguimos la convención de [Conventional Commits](https://www.conventionalcommits.org/):

```
<tipo>[alcance opcional]: <descripción>

[cuerpo opcional]

[pie opcional]
```

Ejemplos:
- `feat: añadir funcionalidad de streaming de respuestas`
- `fix: corregir error en la interfaz de chat`
- `docs: actualizar README con nuevas instrucciones`
- `style: formatear código según estándares`
- `refactor: reorganizar estructura de componentes`
- `test: añadir pruebas para el controlador de chat`
- `chore: actualizar dependencias`

## Reportar Problemas

Si encuentras un bug o tienes una sugerencia para una nueva característica, por favor crea un issue en el repositorio con la siguiente información:

- **Para bugs**: Descripción del problema, pasos para reproducirlo, comportamiento esperado vs. actual
- **Para características**: Descripción clara de la característica y por qué sería útil

## Preguntas

Si tienes alguna pregunta sobre cómo contribuir, no dudes en abrir un issue con la etiqueta "pregunta" o contactar a los mantenedores del proyecto.
