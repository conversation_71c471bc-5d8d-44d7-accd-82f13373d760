/**
 * Utilidad simple de caché en memoria
 */

// Almacenamiento de caché
const data = {};
const timestamps = {};

// Duración predeterminada de la caché en milisegundos (5 minutos)
const DEFAULT_DURATION = 5 * 60 * 1000;

/**
 * Obtiene un valor de la caché
 * @param {string} key - Clave del valor
 * @returns {any} - Valor almacenado o undefined si no existe o ha expirado
 */
function get(key) {
  const now = Date.now();
  const timestamp = timestamps[key];
  
  // Verificar si el valor existe y no ha expirado
  if (data[key] !== undefined && timestamp && now - timestamp < DEFAULT_DURATION) {
    return data[key];
  }
  
  // Si ha expirado, eliminar
  if (data[key] !== undefined) {
    delete data[key];
    delete timestamps[key];
  }
  
  return undefined;
}

/**
 * Almacena un valor en la caché
 * @param {string} key - Clave del valor
 * @param {any} value - Valor a almacenar
 * @param {number} duration - Duración en milisegundos (opcional)
 */
function set(key, value, duration = DEFAULT_DURATION) {
  data[key] = value;
  timestamps[key] = Date.now();
  
  // Programar eliminación automática
  setTimeout(() => {
    delete data[key];
    delete timestamps[key];
  }, duration);
}

/**
 * Elimina un valor de la caché
 * @param {string} key - Clave del valor
 */
function remove(key) {
  delete data[key];
  delete timestamps[key];
}

/**
 * Limpia toda la caché
 */
function clear() {
  Object.keys(data).forEach(key => {
    delete data[key];
    delete timestamps[key];
  });
}

module.exports = {
  get,
  set,
  remove,
  clear,
  data,
  timestamps,
  DEFAULT_DURATION
};
