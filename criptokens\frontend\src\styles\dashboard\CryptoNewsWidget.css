.crypto-news-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.crypto-news-widget:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.news-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.news-controls {
  display: flex;
  gap: 0.5rem;
}

.news-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--text-tertiary);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.5;
}

.news-dot.active {
  background-color: var(--color-primary);
  opacity: 1;
  transform: scale(1.2);
}

.news-carousel {
  position: relative;
  overflow: hidden;
  flex: 1;
}

.news-slider {
  display: flex;
  transition: transform 0.5s ease;
  height: 100%;
}

.news-item {
  min-width: 100%;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  box-sizing: border-box;
}

.news-content {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.news-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-xs);
}

.news-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.news-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.news-title {
  margin: 0;
  font-size: 0.95rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.news-source {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.news-date {
  color: var(--text-tertiary);
}

.news-sentiment {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.news-sentiment.positive {
  color: var(--color-positive);
  background-color: rgba(0, 255, 157, 0.1);
}

.news-sentiment.negative {
  color: var(--color-negative);
  background-color: rgba(255, 58, 110, 0.1);
}

.news-sentiment.neutral {
  color: var(--text-secondary);
  background-color: rgba(255, 255, 255, 0.1);
}

.news-link {
  align-self: flex-end;
  color: var(--color-primary);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
}

.news-link:hover {
  background-color: var(--color-primary-transparent);
  transform: translateX(2px);
}

.news-link i {
  font-size: 0.75rem;
}

/* Esqueleto de carga */
.crypto-news-widget.loading .skeleton-loading {
  width: 100%;
  height: 120px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estado vacío */
.empty-news {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-tertiary);
}

.empty-news i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-news p {
  margin: 0;
  font-size: 0.9rem;
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .news-content {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .news-thumbnail {
    width: 100%;
    height: 120px;
  }
  
  .news-title {
    font-size: 0.9rem;
  }
  
  .news-meta {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}
