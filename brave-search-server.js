const express = require('express');
const axios = require('axios');
const cors = require('cors');
require('dotenv').config();
const config = require('./config');

const app = express();
const PORT = process.env.PORT || config.ports.braveSearchMcp;
// API key para Brave Search
const BRAVE_API_KEY = process.env.BRAVE_API_KEY || config.apiKeys.braveSearch;

// Configurar middleware
app.use(express.json());
app.use(cors({
  origin: config.cors.origins,
  methods: config.cors.methods,
  allowedHeaders: config.cors.allowedHeaders,
  credentials: config.cors.credentials
}));

// Endpoint para búsquedas
app.post('/search', async (req, res) => {
  try {
    const { query, count = 5, freshness = 'pm' } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Se requiere un término de búsqueda' });
    }

    console.log(`Realizando búsqueda: "${query}" (${count} resultados, freshness: ${freshness})`);

    // Verificar si hay una API key válida
    if (!BRAVE_API_KEY) {
      return res.status(401).json({
        error: 'API key no configurada',
        message: 'Se requiere una API key válida para usar la API de Brave Search. Por favor, configura la variable de entorno BRAVE_API_KEY.',
        query
      });
    }

    // Construir los parámetros de búsqueda
    const params = {
      q: query,
      count: Math.min(count, 10), // Máximo 10 resultados por petición
      search_lang: 'es',
      country: 'ES',
      safesearch: 'moderate'
    };

    // Añadir filtro de tiempo si se especifica
    if (freshness) {
      params.freshness = freshness;
    }

    console.log('Parámetros de búsqueda:', params);

    const response = await axios.get('https://api.search.brave.com/res/v1/web/search', {
      params,
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': BRAVE_API_KEY
      },
      timeout: 10000 // 10 segundos de timeout
    });

    // Procesar y devolver los resultados
    const results = response.data.web?.results?.map(result => ({
      title: result.title,
      url: result.url,
      description: result.description,
      publishedDate: result.published_date || '',
      source: result.meta_url?.hostname || new URL(result.url).hostname
    })) || [];

    console.log(`Encontrados ${results.length} resultados para "${query}"`);

    res.json({
      results,
      totalCount: response.data.web?.total_count || results.length,
      queryTimeSec: response.data.web?.time_sec || 0
    });
  } catch (error) {
    console.error('Error en la búsqueda:', error.message);

    // Obtener más detalles del error si están disponibles
    let errorDetails = error.message;
    if (error.response) {
      console.error('Detalles de la respuesta de error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
      errorDetails = `${error.response.status} ${error.response.statusText}: ${JSON.stringify(error.response.data)}`;
    }

    // Devolver un error detallado
    res.status(500).json({
      error: 'Error al realizar la búsqueda con Brave Search',
      details: errorDetails,
      query: req.body.query,
      params: {
        count: req.body.count,
        freshness: req.body.freshness
      }
    });
  }
});

// Endpoint de estado
app.get('/status', (req, res) => {
  res.json({
    status: 'ok',
    service: 'Brave Search Server',
    version: '1.0.0'
  });
});



// Iniciar el servidor
app.listen(PORT, () => {
  console.log(`Servidor Brave Search iniciado en el puerto ${PORT}`);
  console.log(`Endpoint: http://localhost:${PORT}/search`);
  console.log(`Ejemplo de uso: curl -X POST http://localhost:${PORT}/search -H "Content-Type: application/json" -d '{"query": "bitcoin", "count": 5, "freshness": "pd"}'`);
});
