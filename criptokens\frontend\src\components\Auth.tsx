import { useState } from 'react';
import Login from './Login';
import Register from './Register';
import ResetPassword from './ResetPassword';
import AuthDebugHelper from './AuthDebugHelper';
import { useAuth } from '../context/NewAuthContext';
import '../styles/Auth.css';

enum AuthView {
  LOGIN = 'login',
  REGISTER = 'register',
  RESET_PASSWORD = 'reset_password'
}

const Auth = () => {
  const [currentView, setCurrentView] = useState<AuthView>(AuthView.LOGIN);
  const { currentUser } = useAuth();

  // Si el usuario ya está autenticado, no mostramos la pantalla de autenticación
  if (currentUser) {
    return null;
  }

  const switchToLogin = () => setCurrentView(AuthView.LOGIN);
  const switchToRegister = () => setCurrentView(AuthView.REGISTER);
  const switchToResetPassword = () => setCurrentView(AuthView.RESET_PASSWORD);

  return (
    <div className="auth-wrapper">
      {currentView === AuthView.LOGIN && (
        <Login
          onRegisterClick={switchToRegister}
          onResetPasswordClick={switchToResetPassword}
        />
      )}

      {currentView === AuthView.REGISTER && (
        <Register
          onLoginClick={switchToLogin}
        />
      )}

      {currentView === AuthView.RESET_PASSWORD && (
        <ResetPassword
          onLoginClick={switchToLogin}
        />
      )}

      {/* Herramienta de depuración para crear usuarios de prueba */}
      <AuthDebugHelper />
    </div>
  );
};

export default Auth;
