/**
 * Servicio para obtener datos del mercado de criptomonedas
 */
import axios from 'axios';
import { API_URLS } from '../config/api.config';

// Interfaz para los datos del mercado
export interface MarketData {
  fearGreedIndex: number;
  fearGreedClassification: string;
  btcDominance: number;
  totalMarketCap: number;
  totalMarketCapInTrillions: number;
  timestamp: string;
}

/**
 * Obtiene datos del mercado global de criptomonedas
 * @returns Datos del mercado
 */
export const getMarketData = async (): Promise<MarketData> => {
  try {
    console.log('Obteniendo datos del mercado desde el backend...');
    
    // Llamar al endpoint del backend
    const response = await axios.get(`${API_URLS.backend}/api/market/data`);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Formato de respuesta inesperado');
  } catch (error) {
    console.error('Error al obtener datos del mercado:', error);
    
    // Devolver datos predeterminados en caso de error
    return {
      fearGreedIndex: 50,
      fearGreedClassification: 'Neutral',
      btcDominance: 40,
      totalMarketCap: 1500000000000,
      totalMarketCapInTrillions: 1.5,
      timestamp: new Date().toISOString()
    };
  }
};
