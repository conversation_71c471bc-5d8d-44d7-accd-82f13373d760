import { useState, useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/NewAuthContext';
import { useTopCryptocurrencies } from '../hooks/useMcpClient';
import '../styles/Header.css';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { currentUser, userData, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { data: cryptos } = useTopCryptocurrencies();

  // Manejar la búsqueda
  const handleSearch = useCallback(() => {
    if (searchQuery.trim()) {
      // Buscar en criptomonedas
      const matchingCrypto = cryptos?.find(crypto =>
        crypto.name.toLowerCase() === searchQuery.toLowerCase() ||
        crypto.symbol.toLowerCase() === searchQuery.toLowerCase()
      );

      if (matchingCrypto) {
        navigate(`/coins/${matchingCrypto.id}`);
        return;
      }

      // Si no se encuentra, navegar a la página de búsqueda
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  }, [searchQuery, cryptos, navigate]);

  return (
    <header className="app-header">
      <div className="header-container">
        <div className="logo-container">
          <h1 className="logo">Criptokens</h1>
        </div>

        <nav className={`main-nav ${isMenuOpen ? 'active' : ''}`}>
          <ul className="nav-links">
            <li><a href="/" className={location.pathname === '/' || location.pathname === '/dashboard' ? 'active' : ''}>Dashboard</a></li>
            <li><a href="/portfolio" className={location.pathname === '/portfolio' ? 'active' : ''}>Mi Portafolio</a></li>
            <li><a href="/radar" className={location.pathname === '/radar' || location.pathname === '/watchlist' ? 'active' : ''}>Mi Radar Cripto</a></li>
            <li><a href="/technical" className={location.pathname === '/technical' ? 'active' : ''}>Análisis Técnico</a></li>
            <li><a href="/fundamental" className={location.pathname.includes('/fundamental') ? 'active' : ''}>Análisis Fundamental</a></li>
            <li><a href="/news" className={location.pathname === '/news' ? 'active' : ''}>Noticias</a></li>
            <li><a href="/guru" className={location.pathname === '/guru' ? 'active' : ''}>Gurú Cripto</a></li>
            <li><a href="/analysis" className={location.pathname === '/analysis' ? 'active' : ''}>Análisis</a></li>
            <li><a href="/academy" className={location.pathname === '/academy' ? 'active' : ''}>Academia</a></li>
            <li><a href="/defi" className={location.pathname === '/defi' ? 'active' : ''}>Centro DeFi</a></li>
          </ul>
        </nav>

        <div className="market-stats">
          <div className="market-stat-item">
            <span className="stat-label">Monedas:</span>
            <span className="stat-value">16,901</span>
          </div>
          <div className="market-stat-item">
            <span className="stat-label">Intercambios:</span>
            <span className="stat-value">1,290</span>
          </div>
          <div className="market-stat-item">
            <span className="stat-label">Cap. de mercado:</span>
            <span className="stat-value">3,028 B US$</span>
            <span className="stat-change positive">+1.7%</span>
          </div>
          <div className="market-stat-item">
            <span className="stat-label">Volumen en 24h:</span>
            <span className="stat-value">99,936 M US$</span>
          </div>
          <div className="market-stat-item">
            <span className="stat-label">Dominio:</span>
            <span className="stat-value">BTC 61.3%</span>
            <span className="stat-value">ETH 70.2%</span>
          </div>
          <div className="market-stat-item">
            <span className="stat-label">Gas:</span>
            <span className="stat-value">0.369 GWEI</span>
          </div>
        </div>

        <div className="header-actions">
          <div className="search-container">
            <input
              type="text"
              placeholder="Buscar criptomoneda..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button className="search-button" onClick={handleSearch}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.*************.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
              </svg>
            </button>
          </div>
          {currentUser ? (
            <div className="user-menu-container">
              <button
                className="user-button"
                onClick={() => setShowUserMenu(!showUserMenu)}
              >
                {userData?.photoURL ? (
                  <img src={userData.photoURL} alt={userData.displayName} className="user-avatar" />
                ) : (
                  <div className="user-avatar-placeholder">
                    {userData?.displayName?.charAt(0) || currentUser.email?.charAt(0)}
                  </div>
                )}
                <span className="user-name">{userData?.displayName || currentUser.email?.split('@')[0]}</span>
              </button>

              {showUserMenu && (
                <div className="user-dropdown">
                  <div className="user-info">
                    <div className="user-avatar-large">
                      {userData?.photoURL ? (
                        <img src={userData.photoURL} alt={userData.displayName} />
                      ) : (
                        <div className="user-avatar-placeholder large">
                          {userData?.displayName?.charAt(0) || currentUser.email?.charAt(0)}
                        </div>
                      )}
                    </div>
                    <div className="user-details">
                      <span className="user-display-name">{userData?.displayName || 'Usuario'}</span>
                      <span className="user-email">{currentUser.email}</span>
                    </div>
                  </div>
                  <div className="user-dropdown-divider"></div>
                  <a href="/profile" className="user-dropdown-item">Mi Perfil</a>
                  <a href="/settings" className="user-dropdown-item">Configuración</a>
                  <div className="user-dropdown-divider"></div>
                  <button
                    className="user-dropdown-item logout"
                    onClick={() => {
                      logout();
                      setShowUserMenu(false);
                    }}
                  >
                    Cerrar Sesión
                  </button>
                </div>
              )}
            </div>
          ) : (
            <a href="/login" className="login-button">Iniciar Sesión</a>
          )}
          <button className="mobile-menu-toggle" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
