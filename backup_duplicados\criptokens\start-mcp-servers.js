const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Leer la configuración de los servidores MCP
const configPath = path.join(__dirname, 'mcp-config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// Función para iniciar un servidor MCP
function startMcpServer(name, serverConfig) {
  console.log(`Iniciando servidor MCP: ${name}...`);

  const { command, args, env } = serverConfig;

  // Combinar variables de entorno
  const processEnv = { ...process.env, ...env };

  // Iniciar el proceso
  const serverProcess = spawn(command, args, {
    env: processEnv,
    stdio: 'inherit',
    shell: true
  });

  // Manejar eventos del proceso
  serverProcess.on('error', (error) => {
    console.error(`Error al iniciar el servidor MCP ${name}:`, error);
  });

  serverProcess.on('close', (code) => {
    if (code !== 0) {
      console.error(`El servidor MCP ${name} se cerró con código: ${code}`);
    } else {
      console.log(`El servidor MCP ${name} se cerró correctamente.`);
    }
  });

  return serverProcess;
}

// Iniciar el servidor MCP de crypto
console.log('Iniciando servidor MCP de crypto...');
const cryptoServerProcess = spawn('node', ['../crypto-mcp-server/http-server.js'], {
  stdio: 'inherit',
  shell: true
});

// Iniciar los servidores MCP configurados
const mcpServers = config.mcpServers || {};
const serverProcesses = {};

Object.entries(mcpServers).forEach(([name, serverConfig]) => {
  serverProcesses[name] = startMcpServer(name, serverConfig);
});

// Manejar la terminación del proceso principal
process.on('SIGINT', () => {
  console.log('Cerrando todos los servidores MCP...');

  // Cerrar el servidor de crypto
  if (cryptoServerProcess) {
    cryptoServerProcess.kill('SIGINT');
  }

  // Cerrar todos los servidores MCP
  Object.entries(serverProcesses).forEach(([name, process]) => {
    console.log(`Cerrando servidor MCP: ${name}...`);
    process.kill('SIGINT');
  });

  // Salir después de un breve retraso para permitir que los procesos se cierren correctamente
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});

console.log('Todos los servidores MCP están en ejecución. Presiona Ctrl+C para detenerlos.');
