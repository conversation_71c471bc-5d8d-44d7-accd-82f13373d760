import React from 'react';
import { usePortfolio } from '../hooks/usePortfolio';
import '../styles/PortfolioSummaryWidget.css';

interface PortfolioSummaryWidgetProps {
  onViewFullPortfolio: () => void;
}

const PortfolioSummaryWidget: React.FC<PortfolioSummaryWidgetProps> = ({ onViewFullPortfolio }) => {
  const { portfolioStats, isLoading, error } = usePortfolio();

  // Función para formatear números con separadores de miles
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('es-ES', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  // Determinar el color basado en el valor (positivo o negativo)
  const getValueColor = (value: number): string => {
    return value >= 0 ? 'positive' : 'negative';
  };

  if (isLoading) {
    return (
      <div className="portfolio-summary-widget loading">
        <div className="widget-header">
          <h3>Resumen de Mi Cartera</h3>
        </div>
        <div className="widget-content">
          <div className="loading-indicator">Cargando datos de tu cartera...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-summary-widget error">
        <div className="widget-header">
          <h3>Resumen de Mi Cartera</h3>
        </div>
        <div className="widget-content">
          <div className="error-message">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-summary-widget">
      <div className="widget-header">
        <h3>Resumen de Mi Cartera</h3>
        <span className="last-updated">
          Actualizado: {new Date(portfolioStats.lastUpdated).toLocaleTimeString()}
        </span>
      </div>
      <div className="widget-content">
        <div className="portfolio-value">
          <div className="value-label">Valor Total</div>
          <div className="value-amount">${formatNumber(portfolioStats.totalValue)}</div>
        </div>
        
        <div className="portfolio-change">
          <div className="change-label">Cambio 24h</div>
          <div className={`change-amount ${getValueColor(portfolioStats.totalProfitLoss)}`}>
            ${formatNumber(portfolioStats.totalProfitLoss)} 
            <span className="change-percentage">
              ({portfolioStats.totalProfitLossPercentage.toFixed(2)}%)
            </span>
          </div>
        </div>
        
        <div className="portfolio-assets-count">
          <div className="count-label">Activos</div>
          <div className="count-value">{portfolioStats.assetCount}</div>
        </div>
      </div>
      <div className="widget-footer">
        <button className="view-portfolio-button" onClick={onViewFullPortfolio}>
          Ver Cartera Completa
        </button>
      </div>
    </div>
  );
};

export default PortfolioSummaryWidget;
