import React from 'react';
import { Box, Card, CardContent, Typography, Divider, Chip, Grid } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import '../styles/TechnicalAnalysisCard.css';

interface TechnicalAnalysisCardProps {
  analysis: any;
}

const TechnicalAnalysisCard: React.FC<TechnicalAnalysisCardProps> = ({ analysis }) => {
  if (!analysis) return null;

  const { indicators, patterns, signals } = analysis;

  // Función para formatear números
  const formatNumber = (num: number) => {
    return num.toLocaleString('es-ES', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Función para obtener el color del RSI
  const getRSIColor = (rsi: number) => {
    if (rsi < 30) return '#4caf50'; // Verde para sobreventa
    if (rsi > 70) return '#f44336'; // Rojo para sobrecompra
    return '#2196f3'; // Azul para neutral
  };

  // Función para obtener el color del MACD
  const getMACDColor = (macd: number) => {
    if (macd > 0) return '#4caf50'; // Verde para positivo
    if (macd < 0) return '#f44336'; // Rojo para negativo
    return '#2196f3'; // Azul para neutral
  };

  // Función para obtener el color de la recomendación
  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'COMPRA':
        return '#4caf50';
      case 'VENTA':
        return '#f44336';
      default:
        return '#2196f3';
    }
  };

  // Función para obtener el icono de la recomendación
  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'COMPRA':
        return <TrendingUpIcon />;
      case 'VENTA':
        return <TrendingDownIcon />;
      default:
        return <TrendingFlatIcon />;
    }
  };

  // Contar patrones
  const countPatterns = () => {
    let bullishCount = 0;
    let bearishCount = 0;
    
    // Contar patrones alcistas
    Object.values(patterns.bullish).forEach((values: any) => {
      const lastValue = values[values.length - 1];
      if (lastValue) bullishCount++;
    });
    
    // Contar patrones bajistas
    Object.values(patterns.bearish).forEach((values: any) => {
      const lastValue = values[values.length - 1];
      if (lastValue) bearishCount++;
    });
    
    return { bullishCount, bearishCount };
  };

  const { bullishCount, bearishCount } = countPatterns();

  return (
    <Card className="technical-analysis-card">
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h3">
            Análisis Técnico
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {getRecommendationIcon(signals.recommendation)}
            <Typography 
              variant="subtitle1" 
              component="span" 
              sx={{ 
                ml: 1, 
                color: getRecommendationColor(signals.recommendation),
                fontWeight: 'bold'
              }}
            >
              {signals.recommendation}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Grid container spacing={2}>
          {/* Indicadores técnicos */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" gutterBottom>
              Indicadores
            </Typography>
            
            <Box sx={{ mb: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="body2">RSI (14)</Typography>
                <Chip 
                  label={formatNumber(indicators.rsi)} 
                  size="small" 
                  sx={{ 
                    bgcolor: getRSIColor(indicators.rsi), 
                    color: 'white',
                    fontWeight: 'bold'
                  }} 
                />
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="body2">MACD</Typography>
                <Chip 
                  label={formatNumber(indicators.macd.macd)} 
                  size="small" 
                  sx={{ 
                    bgcolor: getMACDColor(indicators.macd.macd), 
                    color: 'white',
                    fontWeight: 'bold'
                  }} 
                />
              </Box>
              
              <Typography variant="body2" gutterBottom>Bandas de Bollinger</Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="body2" color="textSecondary">Superior:</Typography>
                <Typography variant="body2">{formatNumber(indicators.bollingerBands.upper)}</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="body2" color="textSecondary">Media:</Typography>
                <Typography variant="body2">{formatNumber(indicators.bollingerBands.middle)}</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="body2" color="textSecondary">Inferior:</Typography>
                <Typography variant="body2">{formatNumber(indicators.bollingerBands.lower)}</Typography>
              </Box>
            </Box>
          </Grid>
          
          {/* Patrones y señales */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" gutterBottom>
              Patrones y Señales
            </Typography>
            
            <Box sx={{ mb: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Patrones detectados</Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip 
                    label={`${bullishCount} Alcistas`} 
                    size="small" 
                    color="success" 
                    variant={bullishCount > 0 ? "filled" : "outlined"} 
                  />
                  <Chip 
                    label={`${bearishCount} Bajistas`} 
                    size="small" 
                    color="error" 
                    variant={bearishCount > 0 ? "filled" : "outlined"} 
                  />
                </Box>
              </Box>
            </Box>
            
            <Typography variant="body2" gutterBottom>Señales</Typography>
            
            {signals.details.buy.length > 0 && (
              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" color="success.main" gutterBottom>Señales de compra:</Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                  {signals.details.buy.map((signal: string, index: number) => (
                    <li key={index}>
                      <Typography variant="body2">{signal}</Typography>
                    </li>
                  ))}
                </ul>
              </Box>
            )}
            
            {signals.details.sell.length > 0 && (
              <Box sx={{ mb: 1 }}>
                <Typography variant="body2" color="error.main" gutterBottom>Señales de venta:</Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                  {signals.details.sell.map((signal: string, index: number) => (
                    <li key={index}>
                      <Typography variant="body2">{signal}</Typography>
                    </li>
                  ))}
                </ul>
              </Box>
            )}
            
            {signals.details.neutral.length > 0 && (
              <Box>
                <Typography variant="body2" color="info.main" gutterBottom>Señales neutrales:</Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                  {signals.details.neutral.map((signal: string, index: number) => (
                    <li key={index}>
                      <Typography variant="body2">{signal}</Typography>
                    </li>
                  ))}
                </ul>
              </Box>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default TechnicalAnalysisCard;
