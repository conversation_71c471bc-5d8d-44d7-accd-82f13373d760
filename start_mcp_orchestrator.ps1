# PowerShell script to start MCP servers and Guru Cripto Orchestrator

Write-Host "Starting MCP servers and Guru Cripto Orchestrator..." -ForegroundColor Cyan
Write-Host ""

# Function to start a process
function Start-MCPProcess {
    param (
        [string]$Name,
        [string]$Command,
        [string]$Arguments
    )

    Write-Host "Starting $Name..." -ForegroundColor Yellow

    $process = Start-Process -FilePath $Command -ArgumentList $Arguments -PassThru -WindowStyle Normal

    return $process
}

# Start MCP servers
$cryptoProcess = Start-MCPProcess -Name "MCP Crypto Server" -Command "node" -Arguments "crypto-mcp-server.js"
Start-Sleep -Seconds 2

$braveProcess = Start-MCPProcess -Name "MCP Brave Server" -Command "node" -Arguments "brave-search-server.js"
Start-Sleep -Seconds 2

$playwrightProcess = Start-MCPProcess -Name "MCP Playwright Server" -Command "node" -Arguments "playwright-mcp-server.js"
Start-Sleep -Seconds 2

$context7Process = Start-MCPProcess -Name "Context7 MCP Server" -Command "npx" -Arguments "-y @upstash/context7-mcp@latest"
Start-Sleep -Seconds 2

# Change to onchain-mcp-server directory, install dependencies, and start the server
$onchainProcess = Start-MCPProcess -Name "OnChain MCP Server" -Command "powershell" -Arguments "-Command `"cd onchain-mcp-server; npm install; node index.js`""
Start-Sleep -Seconds 5

# Start Guru Cripto Orchestrator
Write-Host "Starting Guru Cripto Orchestrator..." -ForegroundColor Green
python start_guru_orchestrator.py

Write-Host ""
Write-Host "All servers and the Guru Cripto Orchestrator are running." -ForegroundColor Cyan
Write-Host "Press any key to stop all servers..." -ForegroundColor Red
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop all processes
Write-Host "Stopping all servers..." -ForegroundColor Yellow
$cryptoProcess | Stop-Process -Force
$braveProcess | Stop-Process -Force
$playwrightProcess | Stop-Process -Force
$context7Process | Stop-Process -Force
$onchainProcess | Stop-Process -Force

Write-Host "All servers stopped." -ForegroundColor Green
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
