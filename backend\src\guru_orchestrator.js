/**
 * Guru Cripto Orchestrator Integration for Backend
 * 
 * This module provides functions to interact with the Guru Cripto Orchestrator
 * through the ADK API.
 */

const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');
const logger = require('./utils/logger');

// ADK API URL
const ADK_API_URL = process.env.ADK_API_URL || 'http://localhost:8000';

// Process to store the Guru Cripto Orchestrator
let guruOrchestratorProcess = null;

/**
 * Start the Guru Cripto Orchestrator
 * @returns {Promise<void>}
 */
async function startGuruOrchestrator() {
  try {
    logger.info('Starting Guru Cripto Orchestrator...');
    
    // Check if the orchestrator is already running
    if (guruOrchestratorProcess) {
      logger.info('Guru Cripto Orchestrator is already running');
      return;
    }
    
    // Path to the Python script
    const scriptPath = path.join(__dirname, '..', '..', 'start_guru_orchestrator.py');
    
    // Start the Guru Cripto Orchestrator
    guruOrchestratorProcess = spawn('python', [scriptPath], {
      detached: true,
      stdio: 'pipe'
    });
    
    // Log stdout
    guruOrchestratorProcess.stdout.on('data', (data) => {
      logger.info(`Guru Orchestrator: ${data.toString().trim()}`);
    });
    
    // Log stderr
    guruOrchestratorProcess.stderr.on('data', (data) => {
      logger.error(`Guru Orchestrator Error: ${data.toString().trim()}`);
    });
    
    // Handle process exit
    guruOrchestratorProcess.on('close', (code) => {
      logger.info(`Guru Cripto Orchestrator exited with code ${code}`);
      guruOrchestratorProcess = null;
    });
    
    logger.info('Guru Cripto Orchestrator started successfully');
  } catch (error) {
    logger.error(`Error starting Guru Cripto Orchestrator: ${error.message}`);
    throw error;
  }
}

/**
 * Stop the Guru Cripto Orchestrator
 * @returns {Promise<void>}
 */
async function stopGuruOrchestrator() {
  try {
    logger.info('Stopping Guru Cripto Orchestrator...');
    
    if (guruOrchestratorProcess) {
      // Kill the process
      guruOrchestratorProcess.kill();
      guruOrchestratorProcess = null;
      logger.info('Guru Cripto Orchestrator stopped successfully');
    } else {
      logger.info('Guru Cripto Orchestrator is not running');
    }
  } catch (error) {
    logger.error(`Error stopping Guru Cripto Orchestrator: ${error.message}`);
    throw error;
  }
}

/**
 * Send a query to the Guru Cripto Orchestrator
 * @param {string} query - User query
 * @param {string} sessionId - Session ID
 * @returns {Promise<Object>} - Response from the Guru Cripto Orchestrator
 */
async function queryGuruOrchestrator(query, sessionId) {
  try {
    logger.info(`Sending query to Guru Cripto Orchestrator: ${query}`);
    
    // Make sure the orchestrator is running
    if (!guruOrchestratorProcess) {
      await startGuruOrchestrator();
    }
    
    // Send the query to the ADK API
    const response = await axios.post(`${ADK_API_URL}/agents/guru_cripto_orchestrator/query`, {
      query,
      session_id: sessionId
    });
    
    return response.data;
  } catch (error) {
    logger.error(`Error querying Guru Cripto Orchestrator: ${error.message}`);
    throw error;
  }
}

module.exports = {
  startGuruOrchestrator,
  stopGuruOrchestrator,
  queryGuruOrchestrator
};
