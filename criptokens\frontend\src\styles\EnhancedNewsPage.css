.enhanced-news-page {
  padding: 2rem 0;
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.search-container {
  padding: 1.5rem;
  margin-bottom: 2rem;
  background-color: #ffffff;
  border-radius: 8px;
}

.error-alert {
  margin-bottom: 2rem;
}

.popular-searches {
  margin: 1.5rem 0;
}

.news-content {
  margin-top: 2rem;
}

.sentiment-summary {
  padding: 1rem;
  margin-bottom: 2rem;
  background-color: #ffffff;
  border-radius: 8px;
}

.news-grid {
  margin-top: 1.5rem;
}

.news-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.news-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  line-height: 1.4;
}

.news-description {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.5rem;
}

.empty-state {
  padding: 3rem;
  text-align: center;
  background-color: #ffffff;
  border-radius: 8px;
}

/* Estilos para modo oscuro */
@media (prefers-color-scheme: dark) {
  .search-container,
  .sentiment-summary,
  .news-card,
  .empty-state {
    background-color: #1e1e1e;
  }
}
