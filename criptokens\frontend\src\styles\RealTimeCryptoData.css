.real-time-crypto-container {
  background-color: #1a1a2e;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  margin-bottom: 30px;
  color: #e6e6e6;
}

.crypto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #2a2a4a;
  padding-bottom: 15px;
}

.crypto-header h2 {
  font-size: 1.8rem;
  margin: 0;
  background: linear-gradient(45deg, #00f2ff, #4facfe);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.time-range-selector {
  display: flex;
  gap: 10px;
}

.time-range-selector button {
  background-color: #2a2a4a;
  border: none;
  color: #a0a0a0;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.time-range-selector button:hover {
  background-color: #3a3a5a;
  color: #ffffff;
}

.time-range-selector button.active {
  background: linear-gradient(45deg, #00f2ff, #4facfe);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 242, 255, 0.3);
}

.crypto-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
}

.crypto-list {
  background-color: #16162a;
  border-radius: 10px;
  padding: 15px;
  height: 600px;
  overflow-y: auto;
}

.crypto-list h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: #e6e6e6;
  text-align: center;
}

.crypto-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.crypto-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.crypto-list li {
  background-color: #1e1e3a;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.crypto-list li:hover {
  background-color: #2a2a4a;
  transform: translateY(-2px);
}

.crypto-list li.selected {
  background: linear-gradient(45deg, rgba(0, 242, 255, 0.1), rgba(79, 172, 254, 0.1));
  border-left: 3px solid #00f2ff;
}

.crypto-icon {
  width: 30px;
  height: 30px;
  margin-right: 12px;
}

.crypto-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.crypto-name {
  font-weight: 600;
  color: #e6e6e6;
}

.crypto-symbol {
  font-size: 0.8rem;
  color: #a0a0a0;
}

.crypto-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.crypto-price {
  font-weight: 600;
  color: #e6e6e6;
}

.crypto-change {
  font-size: 0.8rem;
}

.crypto-detail {
  background-color: #16162a;
  border-radius: 10px;
  padding: 20px;
  height: 600px;
  overflow-y: auto;
}

.crypto-detail h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
  color: #e6e6e6;
  text-align: center;
}

.chart-container {
  margin-bottom: 20px;
  background-color: #1a1a2e;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.crypto-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.stat-item {
  background-color: #1e1e3a;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 0.9rem;
  color: #a0a0a0;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #e6e6e6;
}

.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: #a0a0a0;
}

.error-container {
  color: #f44336;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
  .crypto-content {
    grid-template-columns: 1fr;
  }
  
  .crypto-list {
    height: 300px;
  }
  
  .crypto-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .time-range-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .time-range-selector button {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
  
  .crypto-stats {
    grid-template-columns: 1fr;
  }
}
