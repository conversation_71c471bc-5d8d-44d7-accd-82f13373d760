import React, { useEffect, useRef } from 'react';
import anime from '../utils/animeUtils';
import '../styles/BackgroundAnimation.css';

interface BackgroundAnimationProps {
  density?: number; // Densidad de partículas (1-100)
  speed?: number; // Velocidad de animación (1-10)
  color?: string; // Color principal
  interactive?: boolean; // Si las partículas reaccionan al mouse
}

const BackgroundAnimation: React.FC<BackgroundAnimationProps> = ({
  density = 50,
  speed = 5,
  color = '#00e0ff',
  interactive = true
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<any[]>([]);
  const dataLinesRef = useRef<any[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });
  const animationRef = useRef<number | null>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Inicializar la animación
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Ajustar el tamaño del canvas
    const resizeCanvas = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        // Reiniciar partículas cuando cambia el tamaño
        if (resizeTimeoutRef.current) {
          clearTimeout(resizeTimeoutRef.current);
        }
        
        resizeTimeoutRef.current = setTimeout(() => {
          initParticles();
          initDataLines();
        }, 200);
      }
    };

    // Inicializar partículas
    const initParticles = () => {
      if (!canvas) return;
      
      const particleCount = Math.floor((canvas.width * canvas.height) / (20000 / density));
      particlesRef.current = [];

      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          radius: Math.random() * 2 + 1,
          color: getRandomColor(0.1 + Math.random() * 0.3),
          vx: (Math.random() - 0.5) * (speed * 0.02),
          vy: (Math.random() - 0.5) * (speed * 0.02),
          depth: Math.random()
        });
      }
    };

    // Inicializar líneas de datos
    const initDataLines = () => {
      if (!canvas) return;
      
      const dataLineCount = 5;
      dataLinesRef.current = [];

      for (let i = 0; i < dataLineCount; i++) {
        const points = [];
        const pointCount = 100;
        const baseY = (canvas.height / (dataLineCount + 1)) * (i + 1);
        const amplitude = 50 * Math.random() + 20;
        const trend = Math.random() > 0.5 ? 1 : -1; // Tendencia alcista o bajista
        
        for (let j = 0; j < pointCount; j++) {
          const x = (canvas.width / pointCount) * j;
          // Crear un patrón que simule un gráfico de precios
          const y = baseY + Math.sin(j * 0.1) * amplitude + Math.random() * 10 * trend;
          points.push({ x, y });
        }
        
        dataLinesRef.current.push({
          points,
          color: trend > 0 ? 'rgba(0, 255, 157, 0.2)' : 'rgba(255, 58, 110, 0.2)',
          lineWidth: 2,
          opacity: 0,
          active: false,
          delay: i * 2000 // Activar líneas en secuencia
        });

        // Animar la aparición de la línea con anime.js
        setTimeout(() => {
          anime({
            targets: dataLinesRef.current[i],
            opacity: 0.8,
            duration: 2000,
            easing: 'easeInOutQuad'
          });
          dataLinesRef.current[i].active = true;
        }, i * 2000);
      }
    };

    // Función para obtener un color aleatorio basado en la paleta
    const getRandomColor = (opacity: number) => {
      const colors = [
        `rgba(0, 224, 255, ${opacity})`,   // Cian
        `rgba(123, 77, 255, ${opacity})`,  // Púrpura
        `rgba(0, 255, 157, ${opacity})`,   // Verde
        `rgba(255, 58, 110, ${opacity})`,  // Rosa
        `rgba(255, 204, 0, ${opacity})`    // Amarillo
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    };

    // Función de animación principal
    const animate = () => {
      if (!canvas || !ctx) return;
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Dibujar partículas y conexiones
      for (let i = 0; i < particlesRef.current.length; i++) {
        const p = particlesRef.current[i];
        
        // Actualizar posición con velocidad basada en profundidad
        p.x += p.vx * (p.depth + 0.5) * speed;
        p.y += p.vy * (p.depth + 0.5) * speed;
        
        // Rebote en los bordes
        if (p.x < 0 || p.x > canvas.width) p.vx *= -1;
        if (p.y < 0 || p.y > canvas.height) p.vy *= -1;
        
        // Interacción con el mouse si está habilitada
        if (interactive) {
          const dx = mouseRef.current.x - p.x;
          const dy = mouseRef.current.y - p.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            p.x -= dx * 0.01;
            p.y -= dy * 0.01;
          }
        }
        
        // Dibujar partícula
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
        ctx.fillStyle = p.color;
        ctx.fill();
        
        // Conectar partículas cercanas
        for (let j = i + 1; j < particlesRef.current.length; j++) {
          const p2 = particlesRef.current[j];
          const dx = p.x - p2.x;
          const dy = p.y - p2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(p.x, p.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.strokeStyle = `rgba(123, 77, 255, ${0.1 * (1 - distance / 100)})`;
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        }
      }
      
      // Dibujar líneas de datos financieros
      for (let i = 0; i < dataLinesRef.current.length; i++) {
        const line = dataLinesRef.current[i];
        
        if (line.active) {
          // Mover puntos de derecha a izquierda
          for (let j = 0; j < line.points.length; j++) {
            line.points[j].x -= 0.5 * speed;
            
            // Si el punto sale de la pantalla, reposicionarlo a la derecha
            if (line.points[j].x < 0) {
              line.points[j].x = canvas.width;
              // Ajustar Y para mantener la continuidad
              const prevPoint = line.points[(j + line.points.length - 1) % line.points.length];
              line.points[j].y = prevPoint.y + (Math.random() * 10 - 5);
            }
          }
          
          // Dibujar la línea
          ctx.beginPath();
          ctx.moveTo(line.points[0].x, line.points[0].y);
          
          for (let j = 1; j < line.points.length; j++) {
            ctx.lineTo(line.points[j].x, line.points[j].y);
          }
          
          ctx.strokeStyle = line.color.replace('0.2', line.opacity);
          ctx.lineWidth = line.lineWidth;
          ctx.stroke();
        }
      }
      
      animationRef.current = requestAnimationFrame(animate);
    };

    // Manejar eventos del mouse
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = {
        x: e.clientX,
        y: e.clientY
      };
    };

    // Manejar eventos de desplazamiento
    const handleScroll = () => {
      const scrollY = window.scrollY;
      
      // Mover partículas con efecto paralaje
      for (let i = 0; i < particlesRef.current.length; i++) {
        particlesRef.current[i].y += scrollY * 0.01 * particlesRef.current[i].depth;
        
        // Reposicionar partículas que salen de la pantalla
        if (particlesRef.current[i].y > canvas.height) {
          particlesRef.current[i].y = 0;
        } else if (particlesRef.current[i].y < 0) {
          particlesRef.current[i].y = canvas.height;
        }
      }
    };

    // Inicializar todo
    resizeCanvas();
    initParticles();
    initDataLines();
    animate();

    // Añadir event listeners
    window.addEventListener('resize', resizeCanvas);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);

    // Añadir efectos de brillo ocasionales
    const glowInterval = setInterval(() => {
      // Seleccionar una partícula aleatoria para hacerla brillar
      if (particlesRef.current.length > 0) {
        const randomIndex = Math.floor(Math.random() * particlesRef.current.length);
        const randomParticle = particlesRef.current[randomIndex];
        
        // Animar el brillo con anime.js
        const originalRadius = randomParticle.radius;
        anime({
          targets: randomParticle,
          radius: [originalRadius, originalRadius * 3, originalRadius],
          duration: 2000,
          easing: 'easeOutQuad'
        });
      }
    }, 1000);

    // Limpiar al desmontar
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
      clearInterval(glowInterval);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [density, speed, color, interactive]);

  return (
    <canvas 
      ref={canvasRef}
      className="background-animation"
    />
  );
};

export default BackgroundAnimation;
