import { db } from '../firebase-init';
import { collection, doc, setDoc, getDoc, getDocs, deleteDoc, updateDoc, query, where } from 'firebase/firestore';
import { User } from 'firebase/auth';

export type CryptoCategory = 'defi' | 'nft' | 'layer1' | 'layer2' | 'stablecoin' | 'meme' | 'exchange' | 'other';

export interface RadarItem {
  id: string;
  name: string;
  symbol: string;
  imageUrl: string;
  addedAt: Date;
  notes?: string;
  category?: CryptoCategory;
  isFavorite?: boolean;
  customAlertPrice?: number | null;
}

// Obtener la colección de radar para un usuario
const getRadarCollection = (userId: string) => {
  return collection(db, 'users', userId, 'radar');
};

// Añadir una criptomoneda al radar
export const addToRadar = async (
  user: User,
  crypto: {
    id: string;
    name: string;
    symbol: string;
    imageUrl: string;
  }
): Promise<void> => {
  if (!user) throw new Error('Usuario no autenticado');

  const radarCollection = getRadarCollection(user.uid);
  const cryptoDoc = doc(radarCollection, crypto.id);

  // Verificar si ya existe en el radar
  const docSnap = await getDoc(cryptoDoc);
  if (docSnap.exists()) {
    throw new Error('Esta criptomoneda ya está en tu radar');
  }

  // Añadir a la colección de radar
  await setDoc(cryptoDoc, {
    ...crypto,
    addedAt: new Date(),
    notes: '',
    category: 'other',
    isFavorite: false,
    customAlertPrice: null
  });
};

// Obtener todas las criptomonedas del radar
export const getRadarItems = async (userId: string): Promise<RadarItem[]> => {
  if (!userId) return [];

  const radarCollection = getRadarCollection(userId);
  const querySnapshot = await getDocs(radarCollection);

  return querySnapshot.docs.map(doc => {
    const data = doc.data();
    return {
      ...data,
      id: doc.id,
      addedAt: data.addedAt?.toDate() || new Date(),
    } as RadarItem;
  });
};

// Eliminar una criptomoneda del radar
export const removeFromRadar = async (userId: string, cryptoId: string): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const radarCollection = getRadarCollection(userId);
  const cryptoDoc = doc(radarCollection, cryptoId);

  await deleteDoc(cryptoDoc);
};

// Actualizar las notas de una criptomoneda
export const updateNotes = async (userId: string, cryptoId: string, notes: string): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const radarCollection = getRadarCollection(userId);
  const cryptoDoc = doc(radarCollection, cryptoId);

  await updateDoc(cryptoDoc, { notes });
};

// Actualizar la categoría de una criptomoneda
export const updateCategory = async (userId: string, cryptoId: string, category: CryptoCategory): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const radarCollection = getRadarCollection(userId);
  const cryptoDoc = doc(radarCollection, cryptoId);

  await updateDoc(cryptoDoc, { category });
};

// Marcar/desmarcar como favorito
export const toggleFavorite = async (userId: string, cryptoId: string, isFavorite: boolean): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const radarCollection = getRadarCollection(userId);
  const cryptoDoc = doc(radarCollection, cryptoId);

  await updateDoc(cryptoDoc, { isFavorite });
};

// Establecer precio de alerta personalizado
export const setAlertPrice = async (userId: string, cryptoId: string, price: number | null): Promise<void> => {
  if (!userId) throw new Error('Usuario no autenticado');

  const radarCollection = getRadarCollection(userId);
  const cryptoDoc = doc(radarCollection, cryptoId);

  await updateDoc(cryptoDoc, { customAlertPrice: price });
};

// Verificar si una criptomoneda está en el radar
export const isInRadar = async (userId: string, cryptoId: string): Promise<boolean> => {
  if (!userId) return false;

  const radarCollection = getRadarCollection(userId);
  const cryptoDoc = doc(radarCollection, cryptoId);
  const docSnap = await getDoc(cryptoDoc);

  return docSnap.exists();
};

// Obtener criptomonedas por categoría
export const getByCategory = async (userId: string, category: CryptoCategory): Promise<RadarItem[]> => {
  if (!userId) return [];

  const radarCollection = getRadarCollection(userId);
  const q = query(radarCollection, where('category', '==', category));
  const querySnapshot = await getDocs(q);

  return querySnapshot.docs.map(doc => {
    const data = doc.data();
    return {
      ...data,
      id: doc.id,
      addedAt: data.addedAt?.toDate() || new Date(),
    } as RadarItem;
  });
};

// Obtener criptomonedas favoritas
export const getFavorites = async (userId: string): Promise<RadarItem[]> => {
  if (!userId) return [];

  const radarCollection = getRadarCollection(userId);
  const q = query(radarCollection, where('isFavorite', '==', true));
  const querySnapshot = await getDocs(q);

  return querySnapshot.docs.map(doc => {
    const data = doc.data();
    return {
      ...data,
      id: doc.id,
      addedAt: data.addedAt?.toDate() || new Date(),
    } as RadarItem;
  });
};
