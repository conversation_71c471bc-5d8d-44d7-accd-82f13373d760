# Cryptokens

Plataforma de análisis y gestión de criptomonedas con inteligencia artificial.

## Estructura del Proyecto

El proyecto Criptokens está organizado en los siguientes componentes principales:

```
Criptokens/
├── adk_agents/               # Agentes ADK para análisis de criptomonedas (puerto 8002)
├── backend/                  # Servidor backend principal (puerto 3008)
├── criptokens/               # Directorio principal de la aplicación
│   ├── frontend/             # Aplicación frontend (puerto 5173)
│   └── README.md             # Documentación específica
├── crypto-mcp-server/        # Servidor MCP para datos de criptomonedas (puerto 3101)
├── playwright-mcp-server/    # Servidor MCP para análisis web (puerto 3103)
├── brave-search-server.js    # Servidor MCP para búsqueda web (puerto 3102)
├── config.js                 # Configuración centralizada
├── check-config.js           # Script para verificar la configuración
├── run_adk_api.py            # Script para iniciar el servidor ADK API
├── start-criptokens.js       # Script unificado para iniciar todos los servicios
└── README.md                 # Este archivo
```

## Requisitos

- Node.js 16.x o superior
- NPM 7.x o superior
- Python 3.10 o superior
- Google ADK (Agent Development Kit)
- Conexión a Internet para acceder a las APIs externas

## Configuración

Antes de ejecutar la aplicación, es recomendable verificar que todos los componentes estén correctamente configurados:

```bash
node check-config.js
```

Este script verificará:
- Que todos los directorios necesarios existan
- Que los archivos de configuración estén presentes
- Que las API keys estén configuradas
- Que los servidores estén accesibles (si están en ejecución)

## Ejecución

### Iniciar todos los componentes

Para iniciar todos los servidores necesarios (ADK API, MCP Crypto, MCP Brave, MCP Playwright, Context7, OnChain, Backend y Frontend) en el orden correcto:

```bash
# Usando el script original
node start-criptokens.js

# O usando los nuevos scripts de orquestación
# En Windows (CMD):
start_all.bat

# En Windows (PowerShell):
.\start_all.ps1

# En Linux/macOS:
./start_all.sh
```

El script `start_all.js` iniciará todos los componentes en el siguiente orden:
1. MCP Crypto Server (puerto 3101)
2. MCP Brave Server (puerto 3102)
3. MCP Playwright Server (puerto 3103)
4. Context7 MCP Server (puerto 7777)
5. OnChain MCP Server (puerto 3104)
6. Backend (puerto 3001/3008)
7. ADK Agents (puerto 8000/8002)
8. Guru Orchestrator
9. Frontend (puerto 5173)

### Detener todos los componentes

Para detener todos los procesos que estén utilizando los puertos necesarios:

```bash
# En Windows (CMD):
stop_all.bat

# En Windows (PowerShell):
.\stop_all.ps1

# En Linux/macOS:
./stop_all.sh
```

### Iniciar componentes manualmente

Alternativamente, puedes iniciar los componentes manualmente:

```bash
# Iniciar ADK API Server
python run_adk_api.py

# Iniciar Backend
cd backend
node src/server.js

# Iniciar Frontend
cd frontend
npm run dev
```

La aplicación estará disponible en `http://localhost:5173`

## Componentes Principales

### Frontend (puerto 5173)

El frontend es una aplicación React que proporciona la interfaz de usuario para interactuar con la plataforma Criptokens. Incluye:

- Dashboard con información de criptomonedas
- Portafolio para gestionar inversiones
- Radar Cripto (watchlist)
- Guru Cripto (asistente IA)
- Noticias y análisis

### Backend (puerto 3008)

El backend es un servidor Express que proporciona APIs para el frontend y se comunica con los servidores MCP y ADK. Incluye:

- API para el Guru Cripto
- Integración con servicios externos
- Gestión de portafolio
- Análisis de sentimiento
- Integración con agentes ADK

### ADK API Server (puerto 8002)

El servidor ADK API proporciona acceso a los agentes ADK (Agent Development Kit) de Google para análisis de criptomonedas. Incluye:

- Guru Cripto Agent: Agente experto en criptomonedas que proporciona análisis integral y predicciones
- Technical Analysis Agent: Agente especializado en análisis técnico de criptomonedas
- Sentiment Analysis Agent: Agente especializado en análisis de sentimiento de criptomonedas
- On-Chain Analysis Agent: Agente especializado en análisis on-chain de criptomonedas

### Servidores MCP

Los servidores MCP (Model Context Protocol) son microservicios especializados que proporcionan funcionalidades específicas:

- **Crypto MCP Server (puerto 3101)**: Proporciona datos de criptomonedas desde CoinMarketCap y CoinCap
- **Brave Search MCP Server (puerto 3102)**: Proporciona búsqueda web y noticias desde Brave Search
- **Playwright MCP Server (puerto 3103)**: Proporciona análisis de páginas web usando Playwright
- **Context7 MCP Server (puerto 7777)**: Proporciona acceso a documentación actualizada para bibliotecas y frameworks
- **OnChain MCP Server (puerto 3104)**: Proporciona análisis on-chain de criptomonedas mediante APIs como Etherscan

## Solución de Problemas

### Verificación de Configuración

Si encuentras problemas al iniciar la aplicación, ejecuta el script de verificación de configuración:

```bash
node check-config.js
```

### Error al crear conversaciones

Si encuentras un error al crear conversaciones en el Gurú Cripto, asegúrate de que:

1. El backend esté en ejecución en el puerto 3008
2. El servidor ADK API esté en ejecución en el puerto 8002
3. Estés autenticado en la aplicación
4. La ruta `/api/adk-agents/guru` esté correctamente configurada en el backend

### Error de conexión con los servidores MCP

Si encuentras errores de conexión con los servidores MCP, verifica que:

1. Todos los servidores MCP estén en ejecución en los puertos correctos
2. No haya conflictos de puertos con otras aplicaciones
3. Las rutas de los servidores MCP estén correctamente configuradas en el archivo `config.js`

### Problemas con las API keys

Si encuentras problemas relacionados con las API keys:

1. Verifica que todas las API keys estén correctamente configuradas en el archivo `config.js`
2. Asegúrate de que las API keys sean válidas y no hayan expirado
3. Verifica los límites de uso de las APIs (algunas tienen límites diarios o mensuales)

### Problemas con CORS

Si encuentras errores de CORS:

1. Verifica que la configuración CORS en el backend permita solicitudes desde el frontend
2. Asegúrate de que la configuración `credentials` sea consistente entre el frontend y el backend
3. Verifica que los orígenes permitidos incluyan `http://localhost:5173`

## Licencia

Este proyecto es propiedad de Creastillo QR Experience.
