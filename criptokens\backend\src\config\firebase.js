const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Ruta al archivo de credenciales de Firebase
const serviceAccountPath = path.join(__dirname, '../../firebase-credentials.json');

// Verificar si ya está inicializado para evitar inicializaciones múltiples
if (!admin.apps.length) {
  try {
    // Inicializar con configuración por defecto (para desarrollo)
    admin.initializeApp({
      projectId: 'criptoken-11c3b',
    });
    console.log('Firebase Admin inicializado en modo de emulación (desarrollo)');
    console.warn('ADVERTENCIA: Usando modo de emulación. Esto solo debe usarse en desarrollo.');
  } catch (error) {
    console.error('Error al inicializar Firebase Admin:', error);
    console.warn('Continuando sin Firebase Admin. Las funciones de conversación y portafolio no estarán disponibles.');
  }
}

// Exportar instancias de servicios
const db = admin.firestore();

module.exports = { admin, db };
