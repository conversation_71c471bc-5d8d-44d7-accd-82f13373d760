.crypto-predictor {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.predictor-header {
  text-align: center;
  margin-bottom: 1rem;
}

.predictor-header h2 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: var(--text-bright);
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.predictor-description {
  font-size: 1.1rem;
  color: var(--text-medium);
  max-width: 700px;
  margin: 0 auto 0.5rem auto;
}

.model-status {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  margin: 0.5rem auto;
  display: inline-block;
}

.model-status.available {
  background: rgba(var(--success-rgb), 0.1);
  color: var(--success);
  border: 1px solid rgba(var(--success-rgb), 0.3);
}

.model-status.unavailable {
  background: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
  border: 1px solid rgba(var(--warning-rgb), 0.3);
}

.predictor-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  align-items: flex-end;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.crypto-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 300px;
}

.crypto-selector label {
  font-size: 0.9rem;
  color: var(--text-medium);
}

.crypto-selector select {
  padding: 0.75rem 1rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-bright);
  font-size: 1rem;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.crypto-selector select:focus {
  outline: none;
  border-color: var(--primary);
}

.timeframe-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.timeframe-selector label {
  font-size: 0.9rem;
  color: var(--text-medium);
}

.timeframe-buttons {
  display: flex;
  gap: 0.5rem;
}

.timeframe-buttons button {
  padding: 0.75rem 1rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-medium);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeframe-buttons button:hover {
  background: rgba(0, 0, 0, 0.4);
  color: var(--text-bright);
}

.timeframe-buttons button.active {
  background: var(--primary-transparent);
  border-color: var(--primary);
  color: var(--text-bright);
}

.prediction-container {
  min-height: 500px;
}

.select-crypto-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.select-crypto-message p {
  color: var(--text-dim);
  font-size: 1.1rem;
}

.predictor-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.feature-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.feature-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.feature-card p {
  font-size: 0.9rem;
  color: var(--text-medium);
  margin: 0;
  line-height: 1.5;
}

.predictor-disclaimer {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 1rem;
}

.predictor-disclaimer h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-bright);
}

.predictor-disclaimer p {
  font-size: 0.9rem;
  color: var(--text-medium);
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

.predictor-disclaimer p:last-child {
  margin-bottom: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .crypto-predictor {
    padding: 1.5rem;
    gap: 1.5rem;
  }

  .predictor-header h2 {
    font-size: 1.75rem;
  }

  .predictor-description {
    font-size: 1rem;
  }

  .predictor-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .crypto-selector {
    min-width: auto;
  }

  .timeframe-buttons {
    justify-content: space-between;
  }

  .timeframe-buttons button {
    flex: 1;
    padding: 0.75rem 0.5rem;
    font-size: 0.85rem;
  }
}
