import React from 'react';
import '../../styles/dashboard/MarketTrendsWidget.css';

interface TrendItem {
  id: string;
  name: string;
  symbol: string;
  icon?: string;
  change: number;
  volume: number;
  category: string;
}

interface MarketTrendsWidgetProps {
  isLoading?: boolean;
}

const MarketTrendsWidget: React.FC<MarketTrendsWidgetProps> = ({ isLoading = false }) => {
  // Datos simulados de tendencias (en producción, esto vendría de una API)
  const trendingData: TrendItem[] = [
    {
      id: '1',
      name: 'Bitcoin',
      symbol: 'BTC',
      icon: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
      change: 5.2,
      volume: 28500000000,
      category: 'top_gainers'
    },
    {
      id: '2',
      name: 'Ethereum',
      symbol: 'ETH',
      icon: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
      change: 3.8,
      volume: 12300000000,
      category: 'top_gainers'
    },
    {
      id: '3',
      name: '<PERSON><PERSON>',
      symbol: 'SOL',
      icon: 'https://cryptologos.cc/logos/solana-sol-logo.png',
      change: 8.7,
      volume: 2800000000,
      category: 'top_gainers'
    },
    {
      id: '4',
      name: 'Cardano',
      symbol: 'ADA',
      icon: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
      change: -2.3,
      volume: 1200000000,
      category: 'top_losers'
    },
    {
      id: '5',
      name: 'Polkadot',
      symbol: 'DOT',
      icon: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png',
      change: -3.5,
      volume: 950000000,
      category: 'top_losers'
    },
    {
      id: '6',
      name: 'Chainlink',
      symbol: 'LINK',
      icon: 'https://cryptologos.cc/logos/chainlink-link-logo.png',
      change: -4.1,
      volume: 780000000,
      category: 'top_losers'
    },
    {
      id: '7',
      name: 'Uniswap',
      symbol: 'UNI',
      icon: 'https://cryptologos.cc/logos/uniswap-uni-logo.png',
      change: 12.4,
      volume: 650000000,
      category: 'trending'
    },
    {
      id: '8',
      name: 'Aave',
      symbol: 'AAVE',
      icon: 'https://cryptologos.cc/logos/aave-aave-logo.png',
      change: 9.8,
      volume: 420000000,
      category: 'trending'
    },
    {
      id: '9',
      name: 'Compound',
      symbol: 'COMP',
      icon: 'https://cryptologos.cc/logos/compound-comp-logo.png',
      change: 7.2,
      volume: 380000000,
      category: 'trending'
    }
  ];

  // Formatear números grandes
  const formatNumber = (num: number): string => {
    if (num >= 1_000_000_000) {
      return `$${(num / 1_000_000_000).toFixed(1)}B`;
    } else if (num >= 1_000_000) {
      return `$${(num / 1_000_000).toFixed(1)}M`;
    } else if (num >= 1_000) {
      return `$${(num / 1_000).toFixed(1)}K`;
    }
    return `$${num.toFixed(1)}`;
  };

  // Filtrar por categoría
  const getItemsByCategory = (category: string): TrendItem[] => {
    return trendingData.filter(item => item.category === category);
  };

  if (isLoading) {
    return (
      <div className="market-trends-widget loading" data-testid="market-trends-loading">
        <div className="trends-header">
          <h3>Tendencias del Mercado</h3>
        </div>
        <div className="trends-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="market-trends-widget" data-testid="market-trends-widget">
      <div className="trends-header">
        <h3>Tendencias del Mercado</h3>
      </div>
      
      <div className="trends-content">
        <div className="trends-section">
          <h4 className="section-title">
            <i className="fas fa-arrow-trend-up"></i> Top Ganadores
          </h4>
          <div className="trends-list">
            {getItemsByCategory('top_gainers').map(item => (
              <div key={item.id} className="trend-item">
                <div className="trend-icon">
                  {item.icon && <img src={item.icon} alt={item.name} />}
                </div>
                <div className="trend-info">
                  <div className="trend-name">
                    <span className="name">{item.name}</span>
                    <span className="symbol">{item.symbol}</span>
                  </div>
                  <div className="trend-stats">
                    <span className="change positive">+{item.change.toFixed(1)}%</span>
                    <span className="volume">{formatNumber(item.volume)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="trends-section">
          <h4 className="section-title">
            <i className="fas fa-arrow-trend-down"></i> Top Perdedores
          </h4>
          <div className="trends-list">
            {getItemsByCategory('top_losers').map(item => (
              <div key={item.id} className="trend-item">
                <div className="trend-icon">
                  {item.icon && <img src={item.icon} alt={item.name} />}
                </div>
                <div className="trend-info">
                  <div className="trend-name">
                    <span className="name">{item.name}</span>
                    <span className="symbol">{item.symbol}</span>
                  </div>
                  <div className="trend-stats">
                    <span className="change negative">{item.change.toFixed(1)}%</span>
                    <span className="volume">{formatNumber(item.volume)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="trends-section">
          <h4 className="section-title">
            <i className="fas fa-fire"></i> Tendencia
          </h4>
          <div className="trends-list">
            {getItemsByCategory('trending').map(item => (
              <div key={item.id} className="trend-item">
                <div className="trend-icon">
                  {item.icon && <img src={item.icon} alt={item.name} />}
                </div>
                <div className="trend-info">
                  <div className="trend-name">
                    <span className="name">{item.name}</span>
                    <span className="symbol">{item.symbol}</span>
                  </div>
                  <div className="trend-stats">
                    <span className={`change ${item.change >= 0 ? 'positive' : 'negative'}`}>
                      {item.change >= 0 ? '+' : ''}{item.change.toFixed(1)}%
                    </span>
                    <span className="volume">{formatNumber(item.volume)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketTrendsWidget;
