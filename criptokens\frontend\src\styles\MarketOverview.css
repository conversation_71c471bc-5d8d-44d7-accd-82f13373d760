/* Estilos para el resumen del mercado */

.market-overview {
  width: 100%;
}

.market-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  width: 100%;
}

.market-card {
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-md);
  padding: 1.25rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.market-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: white;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.market-cap-icon {
  background: linear-gradient(135deg, #3a7bd5, #00d2ff);
}

.volume-icon {
  background: linear-gradient(135deg, #ff9966, #ff5e62);
}

.dominance-icon {
  background: linear-gradient(135deg, #f7971e, #ffd200);
}

.sentiment-icon {
  background: linear-gradient(135deg, #11998e, #38ef7d);
}

.card-header h3 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.main-value {
  font-size: 1.6rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  letter-spacing: -0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.change-value {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

.change-value.positive {
  color: var(--color-positive);
}

.change-value.negative {
  color: var(--color-negative);
}

.change-value.neutral {
  color: var(--text-secondary);
}

.change-period {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  margin-left: 0.25rem;
}

.dominance-secondary {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.card-chart {
  margin-top: 0.5rem;
  height: 40px;
  width: 100%;
}

.mini-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.mini-chart::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border-color);
}

.market-cap-chart::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background: linear-gradient(to right,
    rgba(58, 123, 213, 0.1) 0%,
    rgba(0, 210, 255, 0.2) 100%);
  clip-path: polygon(
    0% 100%,
    10% 70%,
    20% 85%,
    30% 60%,
    40% 50%,
    50% 60%,
    60% 40%,
    70% 50%,
    80% 30%,
    90% 40%,
    100% 20%,
    100% 100%
  );
}

.volume-chart::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background: linear-gradient(to right,
    rgba(255, 153, 102, 0.1) 0%,
    rgba(255, 94, 98, 0.2) 100%);
  clip-path: polygon(
    0% 100%,
    10% 80%,
    20% 90%,
    30% 70%,
    40% 80%,
    50% 60%,
    60% 70%,
    70% 50%,
    80% 60%,
    90% 40%,
    100% 50%,
    100% 100%
  );
}

.dominance-chart::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background: linear-gradient(to right,
    rgba(247, 151, 30, 0.1) 0%,
    rgba(255, 210, 0, 0.2) 100%);
  clip-path: polygon(
    0% 100%,
    10% 60%,
    20% 70%,
    30% 50%,
    40% 60%,
    50% 40%,
    60% 50%,
    70% 30%,
    80% 40%,
    90% 30%,
    100% 20%,
    100% 100%
  );
}

/* Sentimiento del mercado */
.sentiment-meter {
  width: 100%;
  margin-top: 0.5rem;
}

.sentiment-scale {
  height: 10px;
  width: 100%;
  background: linear-gradient(to right,
    #e74c3c 0%,
    #e67e22 25%,
    #f1c40f 50%,
    #2ecc71 75%,
    #27ae60 100%);
  border-radius: 8px;
  position: relative;
  margin-bottom: 1.5rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.sentiment-marker {
  position: absolute;
  top: -5px;
  width: 20px;
  height: 20px;
  background-color: white;
  border: 3px solid var(--color-primary);
  border-radius: 50%;
  transform: translateX(-50%);
  transition: left 0.5s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.sentiment-label {
  position: absolute;
  bottom: -1.5rem;
  font-size: 0.8rem;
  transform: translateX(-50%);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.sentiment-label.fear {
  left: 15%;
}

.sentiment-label.greed {
  left: 85%;
}

.sentiment-value {
  text-align: center;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-top: 0.75rem;
  font-size: 1.1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Esqueleto de carga */
.skeleton-loading {
  width: 100%;
  height: 1.5rem;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* Estilos responsivos */
@media (max-width: 992px) {
  .market-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .market-cards {
    grid-template-columns: 1fr;
  }

  .market-card {
    padding: 0.75rem;
  }

  .card-icon {
    width: 28px;
    height: 28px;
    font-size: 0.875rem;
  }

  .main-value {
    font-size: 1.25rem;
  }

  .card-chart {
    height: 30px;
  }
}
