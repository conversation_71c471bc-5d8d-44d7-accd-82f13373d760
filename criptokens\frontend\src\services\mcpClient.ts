// Clase para interactuar con el servidor MCP
import { PLACEHOLDER_IMAGE } from '../utils/imageUtils';

class CryptoApiClient {
  private baseUrl: string;
  private mockData: boolean;

  constructor() {
    // Obtener la URL del servidor MCP desde las variables de entorno
    this.baseUrl = import.meta.env.VITE_MCP_SERVER_URL || 'http://localhost:3101';
    // Usar datos reales por defecto, con fallback a simulados si el servidor no está disponible
    this.mockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';

    // Permitir el uso de datos simulados como fallback
    this.mockData = true;

    console.log(`CryptoApiClient inicializado. URL del servidor MCP: ${this.baseUrl}, Usar datos simulados como fallback: ${this.mockData}`);
    console.log('IMPORTANTE: Se permitirá el uso de datos simulados como fallback si el servidor MCP no está disponible.');
  }

  // Método genérico para hacer peticiones al servidor MCP
  private async fetchFromApi(endpoint: string, params: Record<string, any> = {}) {
    try {
      console.log(`Iniciando solicitud al servidor MCP para endpoint: ${endpoint}`);

      // Verificar si el servidor MCP está disponible
      const isMcpAvailable = await this.checkMcpServerAvailability();

      if (!isMcpAvailable) {
        console.warn('Servidor MCP no disponible. Verificando si se permiten datos simulados...');
        if (this.mockData) {
          console.warn('Usando datos simulados como fallback.');
          return this.getMockData(endpoint, params);
        } else {
          console.error('Servidor MCP no disponible y no se permiten datos simulados.');
          throw new Error('Servidor MCP no disponible y no se permiten datos simulados.');
        }
      }

      console.log('Servidor MCP disponible. Continuando con la solicitud...');

      // Determinar la herramienta MCP a utilizar
      let toolName = '';
      let toolParams = {};

      if (endpoint.includes('/coins/markets')) {
        toolName = 'getTopCryptocurrencies';
        toolParams = {
          limit: params.per_page || 10,
          page: params.page || 1
        };
      } else if (endpoint.includes('/market_chart')) {
        toolName = 'getCryptoHistoricalData';
        toolParams = {
          cryptoId: endpoint.split('/')[2],
          days: params.days || 7
        };
      } else if (endpoint.includes('/coins/') && !endpoint.includes('/market_chart')) {
        toolName = 'getCryptoPrice';
        toolParams = {
          cryptoId: endpoint.split('/')[2]
        };
      } else if (endpoint.includes('/search')) {
        toolName = 'searchCryptocurrencies';
        toolParams = {
          query: params.query || ''
        };
      }

      if (!toolName) {
        throw new Error(`Endpoint no soportado: ${endpoint}`);
      }

      console.log(`Fetching data from MCP server: ${toolName}`, toolParams);

      // Realizar la petición al servidor MCP
      let response;
      try {
        // Intentar primero con modo normal
        response = await fetch(`${this.baseUrl}/tools/${toolName}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(toolParams)
        });
      } catch (fetchError) {
        console.warn(`Error en la petición normal al servidor MCP: ${fetchError.message}. Intentando con modo no-cors...`);

        // Si falla, intentar con modo no-cors como último recurso
        // Nota: Esto devolverá una respuesta opaca que no se puede leer directamente
        // pero al menos evita errores de CORS en el navegador
        response = await fetch(`${this.baseUrl}/tools/${toolName}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(toolParams),
          mode: 'no-cors'
        });

        // Como no podemos leer la respuesta en modo no-cors, usamos datos simulados
        console.warn('Usando datos simulados debido a restricciones de CORS');
        return this.getMockData(endpoint, params);
      }

      // Verificar si la respuesta es exitosa
      if (!response.ok) {
        console.warn(`Respuesta del servidor MCP no exitosa: ${response.status} ${response.statusText}. Intentando procesar la respuesta de todos modos.`);
      }

      // Parsear la respuesta como JSON
      const result = await response.json();

      console.log(`Respuesta completa del servidor MCP para ${toolName}:`, result);

      // El servidor MCP puede devolver los datos en diferentes formatos
      // Formato 1: { content: [{ type: 'text', text: JSON.stringify(data) }] }
      // Formato 2: { data: ... } - Datos directos
      // Formato 3: Datos directos sin envolver

      // Caso 1: Formato estándar MCP
      if (result && result.content && Array.isArray(result.content) && result.content.length > 0) {
        const contentItem = result.content[0];
        if (contentItem && contentItem.text) {
          try {
            return JSON.parse(contentItem.text);
          } catch (parseError) {
            console.warn('Error al parsear JSON de la respuesta MCP:', parseError);
            // Si no se puede parsear, devolver el texto tal cual
            return contentItem.text;
          }
        }
      }

      // Caso 2: Datos en propiedad data
      if (result && result.data) {
        return result.data;
      }

      // Caso 3: Datos directos
      if (result && (Array.isArray(result) || typeof result === 'object')) {
        return result;
      }

      console.warn('Formato de respuesta del servidor MCP no reconocido:', result);
      throw new Error('Formato de respuesta del servidor MCP no válido');
    } catch (error) {
      console.error(`Error al obtener datos del servidor MCP:`, error);

      if (this.mockData) {
        console.warn('Error al conectar con el servidor MCP. Usando datos simulados.');
        return this.getMockData(endpoint, params);
      }

      throw error;
    }
  }

  // Verificar si el servidor MCP está disponible
  private async checkMcpServerAvailability(): Promise<boolean> {
    try {
      // Intentar conectarse al servidor MCP con un timeout corto
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 segundos de timeout

      console.log(`Verificando disponibilidad del servidor MCP en ${this.baseUrl}...`);

      try {
        // Intentar primero con modo normal
        const response = await fetch(`${this.baseUrl}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // Verificar si la respuesta es exitosa
        if (response.ok) {
          console.log('Servidor MCP disponible y respondiendo correctamente');
          return true;
        } else {
          console.warn(`Servidor MCP respondió con estado: ${response.status} ${response.statusText}`);
          // Aún consideramos que está disponible si responde, aunque sea con error
          return true;
        }
      } catch (fetchError) {
        console.warn(`Error en la verificación normal del servidor MCP: ${fetchError.message}. Intentando con modo no-cors...`);

        // Si falla, intentar con modo no-cors como último recurso
        try {
          const noCorsResponse = await fetch(`${this.baseUrl}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            mode: 'no-cors',
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          // En modo no-cors, no podemos verificar si la respuesta es exitosa
          // pero al menos sabemos que el servidor está respondiendo
          console.log('Servidor MCP respondiendo en modo no-cors');
          return true;
        } catch (noCorsError) {
          clearTimeout(timeoutId);
          console.error('Error al verificar disponibilidad del servidor MCP en modo no-cors:', noCorsError);
          return false;
        }
      }
    } catch (error) {
      console.error('Error al verificar disponibilidad del servidor MCP:', error);
      return false;
    }
  }

  // Obtener datos simulados como fallback
  private getMockData(endpoint: string, params: Record<string, any> = {}): any {
    console.log('Generando datos simulados para:', endpoint, params);

    // Datos simulados para diferentes endpoints
    if (endpoint.includes('/coins/markets')) {
      return this.getMockTopCryptocurrencies(params.per_page || 10);
    } else if (endpoint.includes('/market_chart')) {
      return this.getMockHistoricalData(endpoint.split('/')[2], params.days || 7);
    } else if (endpoint.includes('/coins/') && !endpoint.includes('/market_chart')) {
      return this.getMockCryptoPrice(endpoint.split('/')[2]);
    } else if (endpoint.includes('/search')) {
      return this.getMockSearchResults(params.query || '');
    }

    return {};
  }

  // Datos simulados para precios de criptomonedas
  private getMockCryptoPrice(cryptoId: string) {
    const cryptoData: Record<string, any> = {
      'bitcoin': {
        id: 'bitcoin',
        name: 'Bitcoin',
        symbol: 'BTC',
        price: 61245.32,
        price_change_24h: 2.5,
        market_cap: 1200000000000,
        total_volume: 32000000000,
        high_24h: 62000,
        low_24h: 60000,
        image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        last_updated: new Date().toISOString(),
        market_data: {
          current_price: { usd: 61245.32 },
          price_change_percentage_24h: 2.5,
          market_cap: { usd: 1200000000000 },
          total_volume: { usd: 32000000000 },
          high_24h: { usd: 62000 },
          low_24h: { usd: 60000 }
        }
      },
      'ethereum': {
        id: 'ethereum',
        name: 'Ethereum',
        symbol: 'ETH',
        price: 3521.18,
        price_change_24h: -1.2,
        market_cap: 420000000000,
        total_volume: 15000000000,
        high_24h: 3600,
        low_24h: 3500,
        image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
        last_updated: new Date().toISOString(),
        market_data: {
          current_price: { usd: 3521.18 },
          price_change_percentage_24h: -1.2,
          market_cap: { usd: 420000000000 },
          total_volume: { usd: 15000000000 },
          high_24h: { usd: 3600 },
          low_24h: { usd: 3500 }
        }
      },
      'binancecoin': {
        id: 'binancecoin',
        name: 'BNB',
        symbol: 'BNB',
        price: 608.42,
        price_change_24h: -0.8,
        market_cap: 95000000000,
        total_volume: 2000000000,
        high_24h: 615,
        low_24h: 600,
        image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
        last_updated: new Date().toISOString(),
        market_data: {
          current_price: { usd: 608.42 },
          price_change_percentage_24h: -0.8,
          market_cap: { usd: 95000000000 },
          total_volume: { usd: 2000000000 },
          high_24h: { usd: 615 },
          low_24h: { usd: 600 }
        }
      }
    };

    // Si la criptomoneda solicitada existe en nuestros datos simulados, la devolvemos
    if (cryptoData[cryptoId]) {
      return cryptoData[cryptoId];
    }

    // Si no, devolvemos datos genéricos
    return {
      id: cryptoId,
      name: cryptoId.charAt(0).toUpperCase() + cryptoId.slice(1),
      symbol: cryptoId.substring(0, 3).toUpperCase(),
      price: Math.random() * 1000,
      price_change_24h: (Math.random() * 10) - 5,
      market_cap: Math.random() * 10000000000,
      total_volume: Math.random() * 1000000000,
      high_24h: Math.random() * 1000,
      low_24h: Math.random() * 900,
      image: PLACEHOLDER_IMAGE,
      last_updated: new Date().toISOString(),
      market_data: {
        current_price: { usd: Math.random() * 1000 },
        price_change_percentage_24h: (Math.random() * 10) - 5,
        market_cap: { usd: Math.random() * 10000000000 },
        total_volume: { usd: Math.random() * 1000000000 },
        high_24h: { usd: Math.random() * 1000 },
        low_24h: { usd: Math.random() * 900 }
      }
    };
  }

  // Datos simulados para las principales criptomonedas
  private getMockTopCryptocurrencies(limit: number): any[] {
    const topCryptos = [
      {
        id: 'bitcoin',
        name: 'Bitcoin',
        symbol: 'btc',
        current_price: 61245.32,
        price_change_percentage_24h: 2.5,
        market_cap: 1200000000000,
        total_volume: 32000000000,
        circulating_supply: 19000000,
        ath: 69000,
        image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'ethereum',
        name: 'Ethereum',
        symbol: 'eth',
        current_price: 3521.18,
        price_change_percentage_24h: -1.2,
        market_cap: 420000000000,
        total_volume: 15000000000,
        circulating_supply: 120000000,
        ath: 4800,
        image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'binancecoin',
        name: 'BNB',
        symbol: 'bnb',
        current_price: 608.42,
        price_change_percentage_24h: -0.8,
        market_cap: 95000000000,
        total_volume: 2000000000,
        circulating_supply: 155000000,
        ath: 690,
        image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'ripple',
        name: 'XRP',
        symbol: 'xrp',
        current_price: 0.58,
        price_change_percentage_24h: 1.5,
        market_cap: 31000000000,
        total_volume: 1200000000,
        circulating_supply: 53000000000,
        ath: 3.4,
        image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'cardano',
        name: 'Cardano',
        symbol: 'ada',
        current_price: 0.45,
        price_change_percentage_24h: 0.3,
        market_cap: 16000000000,
        total_volume: 500000000,
        circulating_supply: 35000000000,
        ath: 3.1,
        image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'solana',
        name: 'Solana',
        symbol: 'sol',
        current_price: 142.35,
        price_change_percentage_24h: 3.2,
        market_cap: 60000000000,
        total_volume: 2500000000,
        circulating_supply: 420000000,
        ath: 260,
        image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'polkadot',
        name: 'Polkadot',
        symbol: 'dot',
        current_price: 7.82,
        price_change_percentage_24h: -0.5,
        market_cap: 10000000000,
        total_volume: 400000000,
        circulating_supply: 1280000000,
        ath: 55,
        image: 'https://assets.coingecko.com/coins/images/12171/large/polkadot.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'dogecoin',
        name: 'Dogecoin',
        symbol: 'doge',
        current_price: 0.12,
        price_change_percentage_24h: 1.8,
        market_cap: 17000000000,
        total_volume: 800000000,
        circulating_supply: 140000000000,
        ath: 0.73,
        image: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'avalanche',
        name: 'Avalanche',
        symbol: 'avax',
        current_price: 35.42,
        price_change_percentage_24h: 2.1,
        market_cap: 12000000000,
        total_volume: 600000000,
        circulating_supply: 350000000,
        ath: 146,
        image: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png',
        last_updated: new Date().toISOString()
      },
      {
        id: 'chainlink',
        name: 'Chainlink',
        symbol: 'link',
        current_price: 15.78,
        price_change_percentage_24h: -0.3,
        market_cap: 9000000000,
        total_volume: 350000000,
        circulating_supply: 570000000,
        ath: 52.7,
        image: 'https://assets.coingecko.com/coins/images/877/large/chainlink-new-logo.png',
        last_updated: new Date().toISOString()
      }
    ];

    return topCryptos.slice(0, limit);
  }

  // Datos históricos simulados
  private getMockHistoricalData(cryptoId: string, days: number): any {
    const prices: [number, number][] = [];
    const market_caps: [number, number][] = [];
    const total_volumes: [number, number][] = [];

    // Generar datos para cada día
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;

    // Precio base según la criptomoneda
    let basePrice = 0;
    switch (cryptoId) {
      case 'bitcoin':
        basePrice = 60000;
        break;
      case 'ethereum':
        basePrice = 3500;
        break;
      case 'binancecoin':
        basePrice = 600;
        break;
      default:
        basePrice = 100;
    }

    // Generar datos para cada día
    for (let i = days; i >= 0; i--) {
      const timestamp = now - (i * dayInMs);

      // Precio con variación aleatoria
      const priceVariation = (Math.random() * 0.1) - 0.05; // -5% a +5%
      const price = basePrice * (1 + priceVariation);

      // Market cap basado en el precio
      const marketCap = price * 1000000;

      // Volumen basado en el precio
      const volume = price * 10000;

      prices.push([timestamp, price]);
      market_caps.push([timestamp, marketCap]);
      total_volumes.push([timestamp, volume]);
    }

    return {
      prices,
      market_caps,
      total_volumes
    };
  }

  // Resultados de búsqueda simulados
  private getMockSearchResults(query: string): any {
    const allCryptos = [
      { id: 'bitcoin', name: 'Bitcoin', symbol: 'btc', market_cap_rank: 1 },
      { id: 'ethereum', name: 'Ethereum', symbol: 'eth', market_cap_rank: 2 },
      { id: 'binancecoin', name: 'BNB', symbol: 'bnb', market_cap_rank: 3 },
      { id: 'ripple', name: 'XRP', symbol: 'xrp', market_cap_rank: 4 },
      { id: 'cardano', name: 'Cardano', symbol: 'ada', market_cap_rank: 5 },
      { id: 'solana', name: 'Solana', symbol: 'sol', market_cap_rank: 6 },
      { id: 'polkadot', name: 'Polkadot', symbol: 'dot', market_cap_rank: 7 },
      { id: 'dogecoin', name: 'Dogecoin', symbol: 'doge', market_cap_rank: 8 },
      { id: 'avalanche', name: 'Avalanche', symbol: 'avax', market_cap_rank: 9 },
      { id: 'chainlink', name: 'Chainlink', symbol: 'link', market_cap_rank: 10 }
    ];

    // Filtrar por la consulta
    const lowercaseQuery = query.toLowerCase();
    const filteredCoins = allCryptos.filter(coin =>
      coin.id.includes(lowercaseQuery) ||
      coin.name.toLowerCase().includes(lowercaseQuery) ||
      coin.symbol.includes(lowercaseQuery)
    );

    return {
      coins: filteredCoins,
      exchanges: [],
      icos: [],
      categories: [],
      nfts: []
    };
  }

  // Obtener el precio de una criptomoneda
  async getCryptoPrice(cryptoId: string) {
    try {
      // Usar el servidor MCP para obtener datos detallados
      const data = await this.fetchFromApi(`/coins/${cryptoId}`, {
        localization: false,
        tickers: false,
        market_data: true,
        community_data: false,
        developer_data: false
      });

      return {
        id: data.id,
        name: data.name,
        symbol: data.symbol.toUpperCase(),
        price: data.market_data.current_price.usd,
        price_change_24h: data.market_data.price_change_percentage_24h,
        market_cap: data.market_data.market_cap.usd,
        total_volume: data.market_data.total_volume.usd,
        high_24h: data.market_data.high_24h.usd,
        low_24h: data.market_data.low_24h.usd,
        image: data.image.large,
        last_updated: data.last_updated
      };
    } catch (error) {
      console.error(`Error al obtener el precio de ${cryptoId}:`, error);
      throw error;
    }
  }

  // Obtener las principales criptomonedas
  async getTopCryptocurrencies(limit: number = 10, page: number = 1) {
    try {
      const data = await this.fetchFromApi('/coins/markets', {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: limit,
        page: page,
        sparkline: true,
        price_change_percentage: '24h'
      });

      return data;
    } catch (error) {
      console.error('Error al obtener las principales criptomonedas:', error);
      throw error;
    }
  }

  // Obtener datos históricos de una criptomoneda
  async getCryptoHistoricalData(cryptoId: string, days: number = 7) {
    try {
      const data = await this.fetchFromApi(`/coins/${cryptoId}/market_chart`, {
        vs_currency: 'usd',
        days: days
      });

      return data;
    } catch (error) {
      console.error(`Error al obtener datos históricos para ${cryptoId}:`, error);
      throw error;
    }
  }

  // Buscar criptomonedas por nombre o símbolo
  async searchCryptocurrencies(query: string) {
    try {
      const data = await this.fetchFromApi('/search', { query });

      return data;
    } catch (error) {
      console.error('Error al buscar criptomonedas:', error);
      throw error;
    }
  }
}

// Exportar una instancia única del cliente de API de criptomonedas
export const cryptoApiClient = new CryptoApiClient();
