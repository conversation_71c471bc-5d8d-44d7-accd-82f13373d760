/**
 * Service for interacting with A2A agents
 */
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// A2A agent URLs
const GURU_AGENT_URL = process.env.GURU_AGENT_URL || 'http://localhost:3200';
const TECHNICAL_AGENT_URL = process.env.TECHNICAL_AGENT_URL || 'http://localhost:3201';
const SENTIMENT_AGENT_URL = process.env.SENTIMENT_AGENT_URL || 'http://localhost:3202';
const ONCHAIN_AGENT_URL = process.env.ONCHAIN_AGENT_URL || 'http://localhost:3203';

/**
 * Get a prediction from the Guru Cripto A2A agent
 * @param {string} query - The user's query
 * @param {string} sessionId - Session ID (optional)
 * @returns {Promise<object>} - Prediction response
 */
const getPrediction = async (query, sessionId = null) => {
  try {
    // Generate session ID if not provided
    const session = sessionId || uuidv4();
    
    // Create task ID
    const taskId = `guru_${session}_${Date.now()}`;
    
    // Create request payload
    const payload = {
      id: taskId,
      sessionId: session,
      message: {
        role: 'user',
        parts: [{ type: 'text', text: query }]
      }
    };
    
    // Send request to Guru agent
    const response = await axios.post(`${GURU_AGENT_URL}/`, {
      jsonrpc: '2.0',
      id: uuidv4(),
      method: 'tasks/send',
      params: payload
    });
    
    // Extract result
    if (response.data && response.data.result) {
      const result = response.data.result;
      
      // Check for artifacts
      if (result.artifacts && result.artifacts.length > 0) {
        for (const artifact of result.artifacts) {
          for (const part of artifact.parts) {
            if (part.type === 'data') {
              return part.data;
            } else if (part.type === 'text') {
              // Parse text as JSON if possible
              try {
                return JSON.parse(part.text);
              } catch (e) {
                // Return text as is
                return { text: part.text };
              }
            }
          }
        }
      }
      
      // Check for message
      if (result.status && result.status.message) {
        for (const part of result.status.message.parts) {
          if (part.type === 'data') {
            return part.data;
          } else if (part.type === 'text') {
            // Parse text as JSON if possible
            try {
              return JSON.parse(part.text);
            } catch (e) {
              // Return text as is
              return { text: part.text };
            }
          }
        }
      }
    }
    
    // If we couldn't extract data, return error
    throw new Error('Failed to extract prediction data from response');
  } catch (error) {
    console.error('Error getting prediction from Guru Cripto A2A agent:', error);
    throw error;
  }
};

/**
 * Stream a prediction from the Guru Cripto A2A agent
 * @param {string} query - The user's query
 * @param {string} sessionId - Session ID (optional)
 * @param {function} onUpdate - Callback for updates
 * @returns {Promise<void>}
 */
const streamPrediction = async (query, sessionId = null, onUpdate) => {
  try {
    // Generate session ID if not provided
    const session = sessionId || uuidv4();
    
    // Create task ID
    const taskId = `guru_${session}_${Date.now()}`;
    
    // Create request payload
    const payload = {
      id: taskId,
      sessionId: session,
      message: {
        role: 'user',
        parts: [{ type: 'text', text: query }]
      }
    };
    
    // Send streaming request to Guru agent
    const response = await axios.post(`${GURU_AGENT_URL}/`, {
      jsonrpc: '2.0',
      id: uuidv4(),
      method: 'tasks/sendSubscribe',
      params: payload
    }, {
      responseType: 'stream'
    });
    
    // Process streaming response
    response.data.on('data', (chunk) => {
      try {
        // Parse SSE data
        const data = chunk.toString();
        const lines = data.split('\\n\\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const eventData = JSON.parse(line.substring(6));
            
            if (eventData.result) {
              const result = eventData.result;
              
              // Check if it's a status update
              if (result.status) {
                const status = result.status;
                const isComplete = status.state === 'completed' || status.state === 'failed';
                
                // Check for message
                if (status.message) {
                  for (const part of status.message.parts) {
                    if (part.type === 'text') {
                      onUpdate({
                        is_task_complete: isComplete,
                        updates: part.text
                      });
                    }
                  }
                } else {
                  onUpdate({
                    is_task_complete: isComplete,
                    updates: `Task state: ${status.state}`
                  });
                }
              }
              
              // Check if it's an artifact update
              if (result.artifact) {
                const artifact = result.artifact;
                
                for (const part of artifact.parts) {
                  if (part.type === 'data') {
                    onUpdate({
                      is_task_complete: true,
                      content: part.data
                    });
                  } else if (part.type === 'text') {
                    // Parse text as JSON if possible
                    try {
                      onUpdate({
                        is_task_complete: true,
                        content: JSON.parse(part.text)
                      });
                    } catch (e) {
                      // Return text as is
                      onUpdate({
                        is_task_complete: true,
                        content: { text: part.text }
                      });
                    }
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error processing streaming data:', error);
        onUpdate({
          is_task_complete: true,
          error: 'Error processing streaming data'
        });
      }
    });
    
    // Handle errors
    response.data.on('error', (error) => {
      console.error('Error streaming prediction:', error);
      onUpdate({
        is_task_complete: true,
        error: 'Error streaming prediction'
      });
    });
  } catch (error) {
    console.error('Error streaming prediction from Guru Cripto A2A agent:', error);
    onUpdate({
      is_task_complete: true,
      error: 'Error streaming prediction'
    });
  }
};

/**
 * Get technical analysis from the Technical Analysis A2A agent
 * @param {string} crypto - Cryptocurrency name
 * @param {string} timeframe - Timeframe description
 * @param {string} sessionId - Session ID (optional)
 * @returns {Promise<object>} - Technical analysis response
 */
const getTechnicalAnalysis = async (crypto, timeframe = '1 week', sessionId = null) => {
  try {
    // Generate session ID if not provided
    const session = sessionId || uuidv4();
    
    // Create task ID
    const taskId = `technical_${session}_${Date.now()}`;
    
    // Create query
    const query = `Analyze ${crypto} technical indicators for ${timeframe}`;
    
    // Create request payload
    const payload = {
      id: taskId,
      sessionId: session,
      message: {
        role: 'user',
        parts: [{ type: 'text', text: query }]
      }
    };
    
    // Send request to Technical agent
    const response = await axios.post(`${TECHNICAL_AGENT_URL}/`, {
      jsonrpc: '2.0',
      id: uuidv4(),
      method: 'tasks/send',
      params: payload
    });
    
    // Extract result
    if (response.data && response.data.result) {
      const result = response.data.result;
      
      // Check for artifacts
      if (result.artifacts && result.artifacts.length > 0) {
        for (const artifact of result.artifacts) {
          for (const part of artifact.parts) {
            if (part.type === 'data') {
              return part.data;
            } else if (part.type === 'text') {
              // Parse text as JSON if possible
              try {
                return JSON.parse(part.text);
              } catch (e) {
                // Return text as is
                return { text: part.text };
              }
            }
          }
        }
      }
      
      // Check for message
      if (result.status && result.status.message) {
        for (const part of result.status.message.parts) {
          if (part.type === 'data') {
            return part.data;
          } else if (part.type === 'text') {
            // Parse text as JSON if possible
            try {
              return JSON.parse(part.text);
            } catch (e) {
              // Return text as is
              return { text: part.text };
            }
          }
        }
      }
    }
    
    // If we couldn't extract data, return error
    throw new Error('Failed to extract technical analysis data from response');
  } catch (error) {
    console.error('Error getting technical analysis:', error);
    throw error;
  }
};

/**
 * Get sentiment analysis from the Sentiment Analysis A2A agent
 * @param {string} crypto - Cryptocurrency name
 * @param {string} timeframe - Timeframe description
 * @param {string} sessionId - Session ID (optional)
 * @returns {Promise<object>} - Sentiment analysis response
 */
const getSentimentAnalysis = async (crypto, timeframe = '1 week', sessionId = null) => {
  try {
    // Generate session ID if not provided
    const session = sessionId || uuidv4();
    
    // Create task ID
    const taskId = `sentiment_${session}_${Date.now()}`;
    
    // Create query
    const query = `Analyze ${crypto} sentiment for ${timeframe}`;
    
    // Create request payload
    const payload = {
      id: taskId,
      sessionId: session,
      message: {
        role: 'user',
        parts: [{ type: 'text', text: query }]
      }
    };
    
    // Send request to Sentiment agent
    const response = await axios.post(`${SENTIMENT_AGENT_URL}/`, {
      jsonrpc: '2.0',
      id: uuidv4(),
      method: 'tasks/send',
      params: payload
    });
    
    // Extract result
    if (response.data && response.data.result) {
      const result = response.data.result;
      
      // Check for artifacts
      if (result.artifacts && result.artifacts.length > 0) {
        for (const artifact of result.artifacts) {
          for (const part of artifact.parts) {
            if (part.type === 'data') {
              return part.data;
            } else if (part.type === 'text') {
              // Parse text as JSON if possible
              try {
                return JSON.parse(part.text);
              } catch (e) {
                // Return text as is
                return { text: part.text };
              }
            }
          }
        }
      }
      
      // Check for message
      if (result.status && result.status.message) {
        for (const part of result.status.message.parts) {
          if (part.type === 'data') {
            return part.data;
          } else if (part.type === 'text') {
            // Parse text as JSON if possible
            try {
              return JSON.parse(part.text);
            } catch (e) {
              // Return text as is
              return { text: part.text };
            }
          }
        }
      }
    }
    
    // If we couldn't extract data, return error
    throw new Error('Failed to extract sentiment analysis data from response');
  } catch (error) {
    console.error('Error getting sentiment analysis:', error);
    throw error;
  }
};

/**
 * Get on-chain analysis from the On-Chain Analysis A2A agent
 * @param {string} crypto - Cryptocurrency name
 * @param {string} timeframe - Timeframe description
 * @param {string} sessionId - Session ID (optional)
 * @returns {Promise<object>} - On-chain analysis response
 */
const getOnChainAnalysis = async (crypto, timeframe = '1 week', sessionId = null) => {
  try {
    // Generate session ID if not provided
    const session = sessionId || uuidv4();
    
    // Create task ID
    const taskId = `onchain_${session}_${Date.now()}`;
    
    // Create query
    const query = `Analyze ${crypto} on-chain data for ${timeframe}`;
    
    // Create request payload
    const payload = {
      id: taskId,
      sessionId: session,
      message: {
        role: 'user',
        parts: [{ type: 'text', text: query }]
      }
    };
    
    // Send request to On-Chain agent
    const response = await axios.post(`${ONCHAIN_AGENT_URL}/`, {
      jsonrpc: '2.0',
      id: uuidv4(),
      method: 'tasks/send',
      params: payload
    });
    
    // Extract result
    if (response.data && response.data.result) {
      const result = response.data.result;
      
      // Check for artifacts
      if (result.artifacts && result.artifacts.length > 0) {
        for (const artifact of result.artifacts) {
          for (const part of artifact.parts) {
            if (part.type === 'data') {
              return part.data;
            } else if (part.type === 'text') {
              // Parse text as JSON if possible
              try {
                return JSON.parse(part.text);
              } catch (e) {
                // Return text as is
                return { text: part.text };
              }
            }
          }
        }
      }
      
      // Check for message
      if (result.status && result.status.message) {
        for (const part of result.status.message.parts) {
          if (part.type === 'data') {
            return part.data;
          } else if (part.type === 'text') {
            // Parse text as JSON if possible
            try {
              return JSON.parse(part.text);
            } catch (e) {
              // Return text as is
              return { text: part.text };
            }
          }
        }
      }
    }
    
    // If we couldn't extract data, return error
    throw new Error('Failed to extract on-chain analysis data from response');
  } catch (error) {
    console.error('Error getting on-chain analysis:', error);
    throw error;
  }
};

/**
 * Check if an agent is available
 * @param {string} url - Agent URL
 * @returns {Promise<boolean>} - True if agent is available
 */
const checkAgentAvailability = async (url) => {
  try {
    const response = await axios.get(`${url}/.well-known/agent.json`, { timeout: 2000 });
    return response.status === 200;
  } catch (error) {
    return false;
  }
};

/**
 * Get status of all A2A agents
 * @returns {Promise<object>} - Status of all agents
 */
const getAgentsStatus = async () => {
  const [guruStatus, technicalStatus, sentimentStatus, onchainStatus] = await Promise.all([
    checkAgentAvailability(GURU_AGENT_URL),
    checkAgentAvailability(TECHNICAL_AGENT_URL),
    checkAgentAvailability(SENTIMENT_AGENT_URL),
    checkAgentAvailability(ONCHAIN_AGENT_URL)
  ]);
  
  return {
    guru: guruStatus,
    technical: technicalStatus,
    sentiment: sentimentStatus,
    onchain: onchainStatus
  };
};

module.exports = {
  getPrediction,
  streamPrediction,
  getTechnicalAnalysis,
  getSentimentAnalysis,
  getOnChainAnalysis,
  getAgentsStatus
};
