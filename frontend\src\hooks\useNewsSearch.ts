import { useState, useEffect, useCallback } from 'react';
import { searchCryptoNews, searchCoinNews, NewsItem } from '../services/braveSearchService';

interface UseNewsSearchProps {
  initialQuery?: string;
  coinId?: string;
  coinName?: string;
  initialCount?: number;
  initialFreshness?: string;
}

interface UseNewsSearchResult {
  news: NewsItem[];
  isLoading: boolean;
  error: string | null;
  fetchNews: (query?: string, freshness?: string) => Promise<void>;
  fetchMoreNews: () => Promise<void>;
  resetNews: () => void;
}

/**
 * Hook para buscar noticias de criptomonedas
 * @param props Propiedades de configuración
 * @returns Estado y funciones para gestionar la búsqueda de noticias
 */
export const useNewsSearch = ({
  initialQuery = '',
  coinId = '',
  coinName = '',
  initialCount = 10,
  initialFreshness = 'pm'
}: UseNewsSearchProps = {}): UseNewsSearchResult => {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentQuery, setCurrentQuery] = useState<string>(initialQuery);
  const [currentFreshness, setCurrentFreshness] = useState<string>(initialFreshness);
  const [currentOffset, setCurrentOffset] = useState<number>(0);
  const [currentCount] = useState<number>(initialCount);

  /**
   * Busca noticias con los parámetros especificados
   */
  const fetchNews = useCallback(async (query?: string, freshness?: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Actualizar parámetros de búsqueda
      const newQuery = query !== undefined ? query : currentQuery;
      const newFreshness = freshness !== undefined ? freshness : currentFreshness;
      
      setCurrentQuery(newQuery);
      setCurrentFreshness(newFreshness);
      setCurrentOffset(0);

      // Realizar la búsqueda
      let results: NewsItem[];
      
      if (coinId && coinName) {
        // Búsqueda específica para una criptomoneda
        results = await searchCoinNews(
          coinId,
          coinName,
          currentCount,
          0,
          newFreshness
        );
      } else {
        // Búsqueda general
        results = await searchCryptoNews(
          newQuery,
          currentCount,
          0,
          newFreshness
        );
      }

      setNews(results);
    } catch (err: any) {
      console.error('Error al buscar noticias:', err);
      setError(err.message || 'Error al buscar noticias');
    } finally {
      setIsLoading(false);
    }
  }, [coinId, coinName, currentCount, currentFreshness, currentQuery]);

  /**
   * Carga más noticias (paginación)
   */
  const fetchMoreNews = useCallback(async () => {
    try {
      if (isLoading) return;
      
      setIsLoading(true);
      const newOffset = currentOffset + currentCount;
      
      // Realizar la búsqueda
      let results: NewsItem[];
      
      if (coinId && coinName) {
        // Búsqueda específica para una criptomoneda
        results = await searchCoinNews(
          coinId,
          coinName,
          currentCount,
          newOffset,
          currentFreshness
        );
      } else {
        // Búsqueda general
        results = await searchCryptoNews(
          currentQuery,
          currentCount,
          newOffset,
          currentFreshness
        );
      }

      // Añadir los nuevos resultados a los existentes
      setNews(prevNews => [...prevNews, ...results]);
      setCurrentOffset(newOffset);
    } catch (err: any) {
      console.error('Error al cargar más noticias:', err);
      setError(err.message || 'Error al cargar más noticias');
    } finally {
      setIsLoading(false);
    }
  }, [coinId, coinName, currentCount, currentFreshness, currentOffset, currentQuery, isLoading]);

  /**
   * Reinicia la búsqueda
   */
  const resetNews = useCallback(() => {
    setNews([]);
    setCurrentOffset(0);
    setError(null);
  }, []);

  // Cargar noticias al montar el componente
  useEffect(() => {
    fetchNews();
  }, [fetchNews]);

  return {
    news,
    isLoading,
    error,
    fetchNews,
    fetchMoreNews,
    resetNews
  };
};

export default useNewsSearch;
