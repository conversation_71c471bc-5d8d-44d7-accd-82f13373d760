.guru-insight-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  border: var(--border-width) solid var(--border-color);
  transition: var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.guru-insight-widget:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-secondary);
}

.guru-insight-widget .widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: var(--border-width) solid var(--border-color);
}

.guru-insight-widget .widget-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.guru-insight-widget .widget-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background-color: rgba(138, 43, 226, 0.05);
  border-radius: var(--border-radius-sm);
  border: var(--border-width) solid rgba(138, 43, 226, 0.2);
  margin-bottom: var(--spacing-md);
  flex: 1;
  position: relative;
  overflow: hidden;
}

.guru-insight-widget .widget-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, var(--color-secondary), var(--color-secondary-light));
}

.guru-insight-widget .guru-avatar-container {
  flex-shrink: 0;
  margin-right: var(--spacing-md);
}

.guru-insight-widget .guru-message-container {
  flex: 1;
}

.guru-insight-widget .guru-message {
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  margin: 0;
  font-style: italic;
  position: relative;
  padding-left: var(--spacing-sm);
}

.guru-insight-widget .guru-message::before {
  content: '"';
  position: absolute;
  left: 0;
  top: -2px;
  font-size: var(--font-size-lg);
  color: var(--color-secondary);
  opacity: 0.5;
}

.guru-insight-widget .loading-message {
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  font-style: italic;
}

.guru-insight-widget .widget-footer {
  margin-top: auto;
  text-align: center;
}

.guru-insight-widget .open-chat-button {
  background: linear-gradient(90deg, var(--color-secondary), var(--color-secondary-light));
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-fast);
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.guru-insight-widget .open-chat-button:hover {
  background: linear-gradient(90deg, var(--color-secondary-light), var(--color-secondary));
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(138, 43, 226, 0.3);
}

@media (max-width: 768px) {
  .guru-insight-widget .widget-content {
    flex-direction: column;
    text-align: center;
  }

  .guru-insight-widget .guru-avatar-container {
    margin-right: 0;
    margin-bottom: 0.75rem;
  }
}
