{"id": "module-2", "title": "Blockchain: La Tecnología Detrás de las Criptomonedas", "description": "Comprende los fundamentos de la tecnología blockchain y cómo funciona para hacer posibles las criptomonedas.", "order": 2, "lessons": [{"id": "lesson-2-1", "title": "¿Qué es Blockchain?", "duration": 20, "order": 1, "content": "# ¿Qué es Blockchain?\n\nLa blockchain (cadena de bloques) es una tecnología de registro distribuido que permite almacenar información de manera segura, transparente e inmutable.\n\n## Definición\n\nUna blockchain es esencialmente una base de datos distribuida que mantiene una lista creciente de registros (bloques) que están enlazados y asegurados mediante criptografía. Cada bloque contiene un hash criptográfico del bloque anterior, una marca de tiempo y datos de transacciones.\n\n## Características Clave\n\n### Descentralización\n\nLa información se almacena en múltiples nodos (computadoras) en lugar de en un servidor central. Esto elimina la necesidad de intermediarios y reduce los puntos únicos de fallo.\n\n### Inmutabilidad\n\nUna vez que los datos se registran en un bloque y se añaden a la cadena, no pueden ser alterados retroactivamente sin modificar todos los bloques subsiguientes, lo que requeriría el consenso de la mayoría de la red.\n\n### Transparencia\n\nTodas las transacciones son visibles para cualquiera que tenga acceso a la red, lo que crea un sistema transparente y auditable.\n\n### Seguridad\n\nLa combinación de criptografía, consenso distribuido y la estructura enlazada de los bloques hace que la blockchain sea extremadamente segura contra ataques y manipulaciones.\n\n## Estructura de una Blockchain\n\n### Bloques\n\nCada bloque contiene:\n- **Cabecera**: Incluye metadatos como el hash del bloque anterior, timestamp, nonce, etc.\n- **Cuerpo**: Contiene las transacciones u otros datos\n\n### Cadena\n\nLos bloques se conectan secuencialmente mediante hashes criptográficos, formando una cadena donde cada bloque referencia al anterior.\n\n### Nodos\n\nComputadoras que mantienen una copia de la blockchain y participan en la validación y propagación de transacciones y bloques.\n\n## Tipos de Blockchain\n\n### Públicas\n\nAbiertas a cualquier persona, completamente descentralizadas (ej. Bitcoin, Ethereum).\n\n### Privadas\n\nRestringidas a participantes específicos, controladas por una organización (ej. Hyperledger Fabric).\n\n### Híbridas/Consorcio\n\nCombinación de características públicas y privadas, controladas por un grupo de organizaciones (ej. R3 Corda)."}, {"id": "lesson-2-2", "title": "Cómo Funciona una Blockchain", "duration": 25, "order": 2, "content": "# Cómo Funciona una Blockchain\n\nEn esta lección, exploraremos el funcionamiento interno de una blockchain, desde la creación de transacciones hasta la formación de bloques y el mecanismo de consenso.\n\n## El Proceso de Transacción\n\n### 1. Creación de la Transacción\n\nCuando un usuario inicia una transacción (por ejemplo, enviar Bitcoin), crea un mensaje que incluye:\n- Dirección del remitente\n- Dirección del destinatario\n- Cantidad a transferir\n- Comisión de transacción (opcional)\n\n### 2. Firma Digital\n\nEl usuario firma la transacción con su clave privada, lo que:\n- Verifica que el remitente es el propietario de los fondos\n- Asegura que la transacción no pueda ser alterada\n\n### 3. Propagación\n\nLa transacción firmada se transmite a la red y es recogida por los nodos.\n\n### 4. Verificación\n\nLos nodos verifican la validez de la transacción comprobando:\n- La firma digital es válida\n- El remitente tiene fondos suficientes\n- La transacción cumple con las reglas de la red\n\n### 5. Inclusión en el Mempool\n\nLas transacciones verificadas se almacenan temporalmente en el \"mempool\" (pool de memoria) a la espera de ser incluidas en un bloque.\n\n## Creación de Bloques\n\n### 1. Selección de Transacciones\n\nLos mineros o validadores seleccionan transacciones del mempool, priorizando generalmente aquellas con mayores comisiones.\n\n### 2. Construcción del Bloque\n\nCrean un bloque candidato que incluye:\n- Cabecera con referencia al bloque anterior\n- Lista de transacciones seleccionadas\n- Timestamp\n- Otros metadatos específicos del protocolo\n\n### 3. Minería/Validación\n\nDependiendo del mecanismo de consenso:\n- **Proof of Work (PoW)**: Los mineros compiten para resolver un complejo problema matemático\n- **Proof of Stake (PoS)**: Los validadores son seleccionados en función de la cantidad de criptomonedas que tienen en stake\n\n### 4. Propagación del Bloque\n\nUna vez creado, el nuevo bloque se propaga a toda la red.\n\n### 5. Verificación y Consenso\n\nLos demás nodos verifican la validez del bloque y, si es válido, lo añaden a su copia de la blockchain.\n\n## Mecanismos de Consenso\n\n### Proof of Work (PoW)\n\n- Utilizado por Bitcoin y otras criptomonedas\n- Los mineros compiten para resolver un problema criptográfico\n- Requiere gran cantidad de energía\n- Seguro contra ataques pero ineficiente energéticamente\n\n### Proof of Stake (PoS)\n\n- Utilizado por Ethereum 2.0, Cardano, etc.\n- Los validadores son seleccionados en proporción a su participación económica\n- Más eficiente energéticamente que PoW\n- Potencialmente menos descentralizado\n\n### Delegated Proof of Stake (DPoS)\n\n- Utilizado por EOS, TRON, etc.\n- Los titulares de tokens votan por un número limitado de delegados\n- Muy eficiente pero más centralizado\n\n### Practical Byzantine Fault Tolerance (PBFT)\n\n- Utilizado en blockchains privadas/de consorcio\n- Basado en votación entre nodos conocidos\n- Eficiente pero requiere identidades conocidas"}, {"id": "lesson-2-3", "title": "Aplicaciones de Blockchain", "duration": 20, "order": 3, "content": "# Aplicaciones de Blockchain\n\nLa tecnología blockchain va mucho más allá de las criptomonedas. En esta lección, exploraremos las diversas aplicaciones y casos de uso de esta tecnología revolucionaria.\n\n## Finanzas y Banca\n\n### Pagos Transfronterizos\n\n- Transferencias internacionales más rápidas y económicas\n- Eliminación de intermediarios y reducción de costos\n- Ejemplos: Ripple (XRP), Stellar (XLM)\n\n### Finanzas Descentralizadas (DeFi)\n\n- Préstamos y créditos sin intermediarios\n- Exchanges descentralizados (DEX)\n- Derivados y seguros basados en blockchain\n- Ejemplos: Aave, Compound, Uniswap\n\n### Tokenización de Activos\n\n- Representación digital de activos físicos o financieros\n- Fraccionamiento de activos de alto valor\n- Mayor liquidez y accesibilidad\n- Ejemplos: Tokenización de bienes raíces, arte, acciones\n\n## Contratos Inteligentes\n\n### Automatización de Acuerdos\n\n- Contratos auto-ejecutables cuando se cumplen condiciones predefinidas\n- Reducción de disputas y necesidad de intermediarios\n- Ejemplos: Seguros paramétricos, acuerdos comerciales\n\n### Gobernanza Descentralizada\n\n- Organizaciones Autónomas Descentralizadas (DAOs)\n- Toma de decisiones transparente y democrática\n- Ejemplos: MakerDAO, Aragon\n\n## Cadena de Suministro\n\n### Trazabilidad\n\n- Seguimiento de productos desde el origen hasta el consumidor\n- Verificación de autenticidad y procedencia\n- Ejemplos: IBM Food Trust, VeChain\n\n### Gestión de Inventario\n\n- Visibilidad en tiempo real de inventarios\n- Reducción de fraudes y errores\n- Optimización de la cadena de suministro\n\n## Identidad Digital\n\n### Identidad Soberana\n\n- Control personal de datos de identidad\n- Verificación sin revelar información innecesaria\n- Ejemplos: Civic, uPort\n\n### Credenciales Verificables\n\n- Títulos académicos, certificaciones profesionales\n- Reducción de falsificaciones\n- Verificación instantánea\n\n## Salud\n\n### Registros Médicos\n\n- Historial médico seguro y accesible\n- Control de acceso granular\n- Interoperabilidad entre proveedores de salud\n\n### Trazabilidad de Medicamentos\n\n- Combate a medicamentos falsificados\n- Seguimiento de la cadena de frío\n- Gestión de recalls\n\n## Gobierno y Sector Público\n\n### Votación Electrónica\n\n- Sistemas de votación transparentes y verificables\n- Reducción de fraude electoral\n- Mayor participación ciudadana\n\n### Registros Públicos\n\n- Títulos de propiedad\n- Registros civiles (nacimientos, matrimonios, defunciones)\n- Transparencia en contratación pública\n\n## Entretenimiento y Medios\n\n### NFTs (Tokens No Fungibles)\n\n- Propiedad digital verificable de arte, coleccionables\n- Nuevos modelos de monetización para creadores\n- Ejemplos: CryptoPunks, NBA Top Shot\n\n### Derechos de Autor y Regalías\n\n- Gestión transparente de derechos\n- Distribución automática de regalías\n- Ejemplos: Audius, Mediachain"}], "quiz": {"id": "quiz-module-2", "title": "Evaluación: Blockchain", "description": "Comprueba tu comprensión de la tecnología blockchain", "passingScore": 70, "questions": [{"id": "q1-m2", "question": "¿Qué característica NO es propia de una blockchain?", "options": ["Inmutabilidad", "Centralización", "Transparencia", "Seguridad criptográfica"], "correctAnswer": 1, "explanation": "La centralización es lo opuesto a una de las características fundamentales de blockchain, que es la descentralización. Las blockchains están diseñadas para operar sin una autoridad central."}, {"id": "q2-m2", "question": "¿Qué contiene cada bloque en una blockchain?", "options": ["Solo transacciones financieras", "Un hash del bloque anterior, timestamp y datos de transacciones", "Únicamente información de los mineros", "Solamente claves privadas de los usuarios"], "correctAnswer": 1, "explanation": "Cada bloque en una blockchain contiene un hash criptográfico del bloque anterior (creando la 'cadena'), una marca de tiempo (timestamp) y datos de transacciones u otros datos según el tipo de blockchain."}, {"id": "q3-m2", "question": "¿Qué es el mecanismo de consenso Proof of Work (PoW)?", "options": ["Un sistema donde los validadores son elegidos según la cantidad de criptomonedas que poseen", "Un proceso donde los mineros compiten para resolver un problema matemático complejo", "Un método donde un grupo selecto de nodos verifica las transacciones", "Un sistema que requiere identificación KYC para validar bloques"], "correctAnswer": 1, "explanation": "Proof of Work (PoW) es un mecanismo de consenso donde los mineros compiten para resolver un problema matemático complejo (encontrar un hash con ciertas características). El primero en resolverlo gana el derecho a añadir el siguiente bloque y recibir la recompensa."}, {"id": "q4-m2", "question": "¿Cuál de las siguientes NO es una aplicación de la tecnología blockchain?", "options": ["Finanzas descentralizadas (DeFi)", "Trazabilidad en cadena de suministro", "Almacenamiento centralizado de datos personales", "Tokenización de activos físicos"], "correctAnswer": 2, "explanation": "El almacenamiento centralizado de datos personales va en contra de los principios de blockchain, que promueve la descentralización y el control personal de los datos. Las blockchains se utilizan para sistemas descentralizados, no centralizados."}, {"id": "q5-m2", "question": "¿Qué ventaja ofrece Proof of Stake (PoS) sobre Proof of Work (PoW)?", "options": ["Mayor seguridad contra ataques del 51%", "Mayor descentralización", "Menor consumo energético", "Mayor velocidad de transacción garantizada"], "correctAnswer": 2, "explanation": "Una de las principales ventajas de Proof of Stake (PoS) sobre Proof of Work (PoW) es su eficiencia energética. PoS no requiere resolver complejos problemas matemáticos que consumen mucha energía, sino que selecciona validadores basándose en la cantidad de criptomonedas que tienen en stake."}]}}