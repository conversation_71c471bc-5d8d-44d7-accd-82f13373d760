import { 
  RecommendedAsset, 
  RiskProfile, 
  SentimentAnalysis 
} from '../../types/dashboard';

/**
 * Genera recomendaciones de activos personalizadas basadas en el perfil del usuario y datos del mercado
 */
export const generateRecommendations = (
  availableAssets: any[],
  userPortfolio: any[],
  userPreferences: {
    favoriteAssets: string[];
    preferredCategories: string[];
    riskProfile: RiskProfile;
  },
  sentimentAnalysis: SentimentAnalysis | null
): RecommendedAsset[] => {
  if (!availableAssets || availableAssets.length === 0) {
    return [];
  }

  // Extraer datos relevantes
  const { favoriteAssets, preferredCategories, riskProfile } = userPortfolio;
  const marketSentiment = sentimentAnalysis?.overallSentiment || 'neutral';
  
  // Crear una copia de los activos para no modificar el original
  let assets = [...availableAssets];
  
  // 1. Filtrar por categorías preferidas si hay alguna
  if (preferredCategories && preferredCategories.length > 0) {
    const filteredByCategory = assets.filter(asset => 
      asset.category && preferredCategories.includes(asset.category)
    );
    
    // Solo usar el filtro si devuelve resultados
    if (filteredByCategory.length > 0) {
      assets = filteredByCategory;
    }
  }
  
  // 2. Aplicar filtros basados en el perfil de riesgo
  assets = filterByRiskProfile(assets, riskProfile, marketSentiment);
  
  // 3. Calcular puntuación de recomendación para cada activo
  assets = assets.map(asset => {
    const recommendationScore = calculateRecommendationScore(
      asset,
      userPortfolio,
      favoriteAssets,
      riskProfile,
      marketSentiment
    );
    
    // Determinar el nivel de riesgo
    let riskLevel: 'low' | 'medium' | 'high';
    if (asset.market_cap_rank <= 10) {
      riskLevel = 'low';
    } else if (asset.market_cap_rank <= 50) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'high';
    }
    
    // Generar razón de recomendación
    const recommendationReason = generateRecommendationReason(
      asset,
      recommendationScore,
      riskProfile,
      marketSentiment
    );
    
    // Extraer o generar tags
    const tags = generateAssetTags(asset);
    
    return {
      id: asset.id,
      name: asset.name,
      symbol: asset.symbol,
      currentPrice: asset.current_price,
      priceChangePercentage24h: asset.price_change_percentage_24h || 0,
      image: asset.image,
      marketCap: asset.market_cap || 0,
      volume24h: asset.total_volume || 0,
      recommendationReason,
      recommendationScore,
      riskLevel,
      tags
    };
  });
  
  // 4. Ordenar por puntuación de recomendación y limitar a 5
  return assets
    .sort((a, b) => b.recommendationScore - a.recommendationScore)
    .slice(0, 5);
};

/**
 * Filtra activos basados en el perfil de riesgo del usuario y el sentimiento del mercado
 */
const filterByRiskProfile = (
  assets: any[], 
  riskProfile: RiskProfile,
  marketSentiment: string
): any[] => {
  switch (riskProfile) {
    case 'conservative':
      // Para conservadores, priorizar activos de mayor capitalización
      // En mercados de miedo, ser aún más conservador
      if (marketSentiment === 'fear' || marketSentiment === 'extreme_fear') {
        return assets.filter(asset => asset.market_cap_rank <= 15);
      }
      return assets.filter(asset => asset.market_cap_rank <= 30);
      
    case 'moderate':
      // Para moderados, un rango más amplio
      return assets.filter(asset => asset.market_cap_rank <= 100);
      
    case 'aggressive':
      // Para agresivos, incluir activos de menor capitalización
      // En mercados de codicia, ser más selectivo
      if (marketSentiment === 'greed' || marketSentiment === 'extreme_greed') {
        return assets.filter(asset => asset.market_cap_rank <= 200);
      }
      return assets; // Sin filtro para agresivos en mercados normales o de miedo
      
    default:
      return assets;
  }
};

/**
 * Calcula una puntuación de recomendación para un activo
 * @returns Puntuación de 1-10, donde 10 es la recomendación más fuerte
 */
const calculateRecommendationScore = (
  asset: any,
  userPortfolio: any[],
  favoriteAssets: string[],
  riskProfile: RiskProfile,
  marketSentiment: string
): number => {
  let score = 5; // Puntuación base
  
  // Factores que aumentan la puntuación
  
  // 1. Si el activo está en favoritos
  if (favoriteAssets.includes(asset.id)) {
    score += 2;
  }
  
  // 2. Rendimiento reciente positivo
  if (asset.price_change_percentage_24h > 0) {
    score += Math.min(2, asset.price_change_percentage_24h / 5);
  }
  
  // 3. Volumen de trading saludable
  if (asset.total_volume > 100000000) { // $100M+
    score += 0.5;
  }
  
  // 4. Ajustes basados en el perfil de riesgo y sentimiento del mercado
  if (riskProfile === 'conservative') {
    // Para conservadores, priorizar activos de mayor capitalización
    score += (30 - Math.min(30, asset.market_cap_rank)) / 10;
    
    // En mercados de miedo, priorizar aún más los activos seguros
    if (marketSentiment === 'fear' || marketSentiment === 'extreme_fear') {
      score += (15 - Math.min(15, asset.market_cap_rank)) / 5;
    }
  } else if (riskProfile === 'aggressive') {
    // Para agresivos, dar bonus a activos de capitalización media
    if (asset.market_cap_rank > 20 && asset.market_cap_rank <= 100) {
      score += 1;
    }
    
    // En mercados de miedo, dar bonus a activos con mayor potencial
    if (marketSentiment === 'fear' || marketSentiment === 'extreme_fear') {
      if (asset.market_cap_rank > 20) {
        score += 1;
      }
    }
  }
  
  // 5. Diversificación - evitar recomendar activos que ya están en el portafolio
  const isInPortfolio = userPortfolio.some(item => item.id === asset.id);
  if (isInPortfolio) {
    score -= 1;
  }
  
  // Limitar la puntuación entre 1 y 10
  return Math.max(1, Math.min(10, score));
};

/**
 * Genera una razón de recomendación personalizada
 */
const generateRecommendationReason = (
  asset: any,
  score: number,
  riskProfile: RiskProfile,
  marketSentiment: string
): string => {
  // Razones basadas en el perfil de riesgo
  const riskReasons = {
    conservative: [
      'Activo de gran capitalización con volatilidad controlada',
      'Históricamente estable en diferentes condiciones de mercado',
      'Buena opción para inversores que priorizan la seguridad'
    ],
    moderate: [
      'Buen equilibrio entre potencial de crecimiento y riesgo',
      'Rendimiento consistente con volatilidad moderada',
      'Adecuado para diversificar tu portafolio'
    ],
    aggressive: [
      'Alto potencial de crecimiento a pesar de mayor volatilidad',
      'Oportunidad de crecimiento en un sector emergente',
      'Podría ofrecer rendimientos superiores a largo plazo'
    ]
  };
  
  // Razones basadas en el sentimiento del mercado
  const sentimentReasons = {
    fear: 'El sentimiento actual de miedo podría presentar una oportunidad de entrada',
    extreme_fear: 'La extrema aversión al riesgo actual podría ofrecer un buen punto de entrada',
    neutral: 'Las condiciones de mercado actuales son favorables para este activo',
    greed: 'Considerar entradas escalonadas debido al optimismo actual del mercado',
    extreme_greed: 'Evaluar cuidadosamente el punto de entrada debido a la euforia del mercado'
  };
  
  // Razones basadas en el rendimiento reciente
  let performanceReason = '';
  if (asset.price_change_percentage_24h > 5) {
    performanceReason = 'Muestra un fuerte impulso alcista';
  } else if (asset.price_change_percentage_24h > 0) {
    performanceReason = 'Tendencia positiva en el último día';
  } else if (asset.price_change_percentage_24h > -5) {
    performanceReason = 'Ligera corrección que podría presentar oportunidad';
  } else {
    performanceReason = 'Corrección significativa que podría ofrecer punto de entrada';
  }
  
  // Seleccionar una razón aleatoria basada en el perfil de riesgo
  const riskIndex = Math.floor(Math.random() * riskReasons[riskProfile].length);
  const riskReason = riskReasons[riskProfile][riskIndex];
  
  // Combinar razones
  return `${riskReason}. ${performanceReason}. ${sentimentReasons[marketSentiment as keyof typeof sentimentReasons]}.`;
};

/**
 * Genera tags para un activo basado en sus características
 */
const generateAssetTags = (asset: any): string[] => {
  const tags: string[] = [];
  
  // Añadir categoría si existe
  if (asset.category) {
    tags.push(asset.category.toLowerCase());
  }
  
  // Añadir tags basados en capitalización de mercado
  if (asset.market_cap_rank <= 10) {
    tags.push('top10');
  } else if (asset.market_cap_rank <= 50) {
    tags.push('top50');
  } else if (asset.market_cap_rank <= 100) {
    tags.push('top100');
  }
  
  // Añadir tags basados en rendimiento
  if (asset.price_change_percentage_24h > 10) {
    tags.push('trending');
  } else if (asset.price_change_percentage_24h < -10) {
    tags.push('dip');
  }
  
  // Añadir tags específicos si podemos identificarlos
  // Esto sería más sofisticado en una implementación real
  if (asset.symbol === 'btc') {
    tags.push('store-of-value');
  } else if (asset.symbol === 'eth') {
    tags.push('smart-contracts');
  } else if (['sol', 'avax', 'ada'].includes(asset.symbol)) {
    tags.push('layer1');
  } else if (['uni', 'aave', 'cake'].includes(asset.symbol)) {
    tags.push('defi');
  } else if (['link', 'grt', 'band'].includes(asset.symbol)) {
    tags.push('oracle');
  }
  
  return tags;
};
