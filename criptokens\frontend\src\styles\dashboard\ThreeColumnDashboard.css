/* Estilos para el dashboard de 3 columnas */
:root {
  --column-left-width: 25%;
  --column-center-width: 50%;
  --column-right-width: 25%;
  --column-min-width: 280px;
  --column-gap: 1.25rem;
  --widget-gap: 1.25rem;
  --widget-padding: 1.25rem;
  --widget-border-radius: var(--border-radius-lg, 12px);
  --widget-border: 1px solid var(--border-color, #2a2d3e);
  --widget-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
  --widget-hover-shadow: var(--shadow-lg, 0 10px 15px rgba(0, 0, 0, 0.1));
  --header-height: 70px;
}

/* Contenedor principal del dashboard */
.dashboard-container {
  width: 100%;
  max-width: 100%;
  padding: clamp(1.25rem, 2.5vw, 2rem);
  background-color: var(--color-background);
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: calc(var(--widget-gap) * 1.2);
  box-sizing: border-box;
}

/* Header del dashboard */
.dashboard-header {
  height: auto;
  display: flex;
  flex-direction: column;
  padding: calc(var(--widget-padding) * 1.2);
  background-color: var(--color-surface-dark);
  border-radius: var(--widget-border-radius);
  box-shadow: var(--widget-shadow);
  border: var(--widget-border);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: box-shadow 0.3s ease;
}



.dashboard-header:hover {
  box-shadow: var(--widget-hover-shadow);
}

/* Botones de navegación centrales */
.navigation-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  gap: 0.75rem;
  margin: 1.5rem 0;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  overflow-x: auto;
  padding: 0.5rem 0;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) var(--color-surface-dark);
  -webkit-overflow-scrolling: touch;
}

.navigation-buttons::-webkit-scrollbar {
  height: 6px;
}

.navigation-buttons::-webkit-scrollbar-track {
  background: var(--color-surface-dark);
  border-radius: 10px;
}

.navigation-buttons::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 10px;
}

.nav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 0.75rem;
  min-width: 100px;
  width: 100px;
  height: 90px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--widget-shadow);
  text-decoration: none;
  flex-shrink: 0;
}

.nav-button i {
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
  color: var(--color-primary);
}

.nav-button span {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-primary);
  text-align: center;
}

.nav-button:hover {
  transform: translateY(-5px);
  box-shadow: var(--widget-hover-shadow);
  border-color: var(--color-primary);
}

.nav-button.active {
  border-color: var(--color-primary);
  background-color: rgba(0, 132, 255, 0.1);
  box-shadow: 0 0 15px rgba(0, 132, 255, 0.3);
}

.nav-button.active i {
  color: var(--color-primary);
}

.nav-button.active:hover {
  transform: translateY(-5px);
  box-shadow: 0 0 20px rgba(0, 132, 255, 0.4);
}

@media (max-width: 768px) {
  .navigation-buttons {
    justify-content: flex-start;
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .nav-button {
    min-width: 90px;
    width: 90px;
    height: 80px;
    padding: 0.5rem;
  }

  .nav-button i {
    font-size: 1.5rem;
    margin-bottom: 0.4rem;
  }

  .nav-button span {
    font-size: 0.75rem;
  }
}

/* Contenido principal con estructura inspirada en CoinGecko */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: var(--widget-gap);
  width: 100%;
  max-width: 100%;
}

/* Sección Superior - Resumen Global */
.dashboard-global-summary {
  display: flex;
  flex-direction: column;
  gap: var(--widget-gap);
  width: 100%;
  background-color: var(--color-surface);
  border-radius: var(--widget-border-radius);
  border: var(--widget-border);
  box-shadow: var(--widget-shadow);
  padding: var(--widget-padding);
}

.global-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.metric-card {
  background-color: var(--color-surface-light);
  border-radius: 8px;
  padding: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.metric-title {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metric-value .change {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.metric-value .change.positive {
  color: var(--color-success, #00e676);
  background-color: var(--color-success-bg, rgba(0, 230, 118, 0.1));
}

.metric-value .change.negative {
  color: var(--color-error, #ff3a6e);
  background-color: var(--color-error-bg, rgba(255, 58, 110, 0.1));
}

.portfolio-summary-card {
  background-color: var(--color-surface-light);
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid var(--color-primary);
  margin-top: 1rem;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.card-title h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
}

.view-all-button {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.2s ease;
}

.view-all-button:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

.portfolio-value {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.value-label {
  font-size: 1rem;
  color: var(--text-secondary);
}

.value-amount {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.value-change {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.value-change.positive {
  color: var(--color-success, #00e676);
  background-color: var(--color-success-bg, rgba(0, 230, 118, 0.1));
}

.value-change.negative {
  color: var(--color-error, #ff3a6e);
  background-color: var(--color-error-bg, rgba(255, 58, 110, 0.1));
}

.empty-portfolio {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  color: var(--text-secondary);
}

.portfolio-actions {
  display: flex;
  gap: 0.5rem;
  width: 100%;
}

.empty-portfolio button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
}

.empty-portfolio button:first-child {
  background-color: var(--color-surface-light);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.empty-portfolio button:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
}

.empty-portfolio button:first-child:hover {
  background-color: var(--color-primary);
  color: white;
}

.sentiment-indicator-card {
  grid-column: 3 / 5;
  background-color: var(--color-surface-light);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sentiment-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.sentiment-value {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sentiment-value.extreme_fear {
  color: #e74c3c;
}

.sentiment-value.fear {
  color: #e67e22;
}

.sentiment-value.neutral {
  color: #f1c40f;
}

.sentiment-value.greed {
  color: #2ecc71;
}

.sentiment-value.extreme_greed {
  color: #27ae60;
}

.sentiment-meter {
  width: 100%;
  margin-top: 0.5rem;
}

.meter-bar {
  height: 8px;
  width: 100%;
  background: linear-gradient(90deg, #e74c3c, #f1c40f, #27ae60);
  border-radius: 4px;
  position: relative;
}

.meter-indicator {
  position: absolute;
  top: -4px;
  width: 16px;
  height: 16px;
  background-color: white;
  border: 2px solid var(--color-primary);
  border-radius: 50%;
  transform: translateX(-50%);
}

/* Contenido Principal - Grid de 2 columnas */
.dashboard-main-content {
  display: grid;
  grid-template-columns: 68% 32%;
  gap: calc(var(--widget-gap) * 1.2);
  width: 100%;
}

.main-column, .sidebar-column {
  display: flex;
  flex-direction: column;
  gap: calc(var(--widget-gap) * 1.2);
}

/* Personalización de scrollbar para navegadores webkit */
.dashboard-column::-webkit-scrollbar {
  width: 6px;
}

.dashboard-column::-webkit-scrollbar-track {
  background: var(--color-surface-dark);
  border-radius: 10px;
}

.dashboard-column::-webkit-scrollbar-thumb {
  background-color: var(--color-primary-transparent);
  border-radius: 10px;
}

/* Widgets del dashboard */
.dashboard-widget {
  background-color: var(--color-surface);
  border-radius: var(--widget-border-radius);
  border: var(--widget-border);
  box-shadow: var(--widget-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.dashboard-widget:hover {
  border-color: var(--border-color-hover);
  box-shadow: var(--widget-hover-shadow);
  transform: translateY(-2px);
}

/* Encabezado de widget */
.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.85rem var(--widget-padding);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-variant, var(--color-surface-dark));
  position: relative;
}

.widget-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--color-primary);
  transition: width 0.3s ease;
}

.dashboard-widget:hover .widget-header::after {
  width: 80px;
}

.widget-header h2 {
  margin: 0;
  font-size: 1.15rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.widget-header h3 {
  margin: 0;
  font-size: 1.05rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.widget-actions {
  display: flex;
  gap: 0.5rem;
}

.widget-action-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.widget-action-button:hover {
  color: var(--color-primary);
  background-color: var(--color-surface-light);
}

/* Contenido de widget */
.widget-content {
  padding: var(--widget-padding);
}

/* Variantes de widgets */
.widget-primary {
  border-color: var(--color-primary-border, rgba(0, 224, 255, 0.3));
}

.widget-primary .widget-header {
  background-color: var(--color-primary-transparent, rgba(0, 224, 255, 0.1));
}

.widget-secondary {
  border-color: var(--color-secondary-border, rgba(138, 86, 232, 0.3));
}

.widget-secondary .widget-header {
  background-color: var(--color-secondary-transparent, rgba(138, 86, 232, 0.1));
}

/* Estados de widgets */
.widget-empty-state,
.widget-loading-state,
.widget-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  text-align: center;
  min-height: 120px;
}

.widget-empty-state {
  color: var(--text-secondary);
  background-color: var(--color-surface-light);
  border-radius: 8px;
}

.widget-empty-state i {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  opacity: 0.5;
}

.widget-empty-state p {
  margin: 0 0 1rem;
}

.widget-empty-state button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.widget-empty-state button:hover {
  background-color: var(--color-primary-dark, #0288d1);
}

.widget-loading-state {
  color: var(--text-secondary);
}

.widget-loading-state i {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.widget-loading-state p {
  margin: 0;
}

.widget-error-state {
  color: var(--color-error, #ff3a6e);
  background-color: var(--color-error-bg, rgba(255, 58, 110, 0.1));
  border-radius: 8px;
}

.widget-error-state i {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.widget-error-state p {
  margin: 0 0 1rem;
}

.widget-error-state button {
  background: none;
  border: 1px solid var(--color-error, #ff3a6e);
  color: var(--color-error, #ff3a6e);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.widget-error-state button:hover {
  background-color: var(--color-error, #ff3a6e);
  color: white;
}

/* Tamaños de widgets */
.widget-xs {
  min-height: 100px;
}

.widget-sm {
  min-height: 150px;
}

.widget-md {
  min-height: 250px;
}

.widget-lg {
  min-height: 350px;
}

.widget-xl {
  min-height: 450px;
}

.widget-full {
  flex: 1;
}

/* Estilos específicos para widgets */

/* Widgets de Insights y Recomendaciones */
.insights-widget, .recommendations-widget {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.insights-widget::before, .recommendations-widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(var(--color-primary-rgb, 0, 224, 255), 0.03), transparent 70%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: var(--widget-border-radius);
}

.insights-widget:hover::before, .recommendations-widget:hover::before {
  opacity: 1;
}

.insights-widget .widget-header, .recommendations-widget .widget-header {
  background: linear-gradient(90deg, rgba(var(--color-primary-rgb, 0, 224, 255), 0.1), transparent);
}

.insights-widget .widget-content, .recommendations-widget .widget-content {
  padding: calc(var(--widget-padding) * 0.8);
  max-height: 350px;
  overflow-y: auto;
}

/* Columna Lateral y Widgets de IA */
.sidebar-column {
  background-color: var(--color-surface-dark);
  border-radius: var(--widget-border-radius);
  padding: calc(var(--widget-padding) * 1.2);
  box-shadow: var(--widget-shadow);
  border: var(--widget-border);
  position: relative;
}

.sidebar-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(var(--color-primary-rgb, 0, 224, 255), 0.03), transparent 70%);
  pointer-events: none;
  border-radius: var(--widget-border-radius);
}

.ai-widgets-group {
  display: flex;
  flex-direction: column;
  gap: calc(var(--widget-gap) * 1.2);
  margin-top: calc(var(--widget-gap) * 1.2);
  margin-bottom: calc(var(--widget-gap) * 1.2);
  position: relative;
  z-index: 1;
}

.ai-widgets-group .widget-header h3 {
  font-size: 1.05rem;
  font-weight: var(--font-weight-semibold);
}

/* Widget de Guru */
.guru-widget {
  background-color: var(--color-surface);
  border-radius: var(--widget-border-radius);
  border: var(--widget-border);
  box-shadow: var(--widget-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: var(--widget-gap);
  position: relative;
}

.guru-widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(var(--color-primary-rgb, 0, 224, 255), 0.05), transparent 70%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.guru-widget:hover::before {
  opacity: 1;
}

.guru-widget:hover {
  box-shadow: var(--widget-hover-shadow);
  border-color: rgba(138, 86, 232, 0.5);
}

.guru-header {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  padding: 0.875rem var(--widget-padding);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.guru-header h3 {
  margin: 0;
  color: white;
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.guru-content {
  padding: var(--widget-padding);
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.guru-avatar-container {
  flex-shrink: 0;
  position: relative;
  transition: transform 0.3s ease;
}

.guru-widget:hover .guru-avatar-container {
  transform: scale(1.05);
}

.guru-quick-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.guru-action-button {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.85rem 1.25rem;
  font-weight: var(--font-weight-semibold);
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(138, 86, 232, 0.3);
  position: relative;
  overflow: hidden;
}

.guru-action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.guru-action-button:hover {
  background: linear-gradient(90deg, #7e52da, #9a66f8);
  box-shadow: 0 4px 12px rgba(138, 86, 232, 0.5);
  transform: translateY(-3px);
}

.guru-action-button:hover::before {
  left: 100%;
}

.guru-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.25rem;
}

.suggestion-chip {
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 0.4rem 0.9rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-chip:hover {
  background-color: rgba(138, 86, 232, 0.1);
  border-color: rgba(138, 86, 232, 0.3);
  color: #8a56e8;
  transform: translateY(-2px);
}

/* Estilos para el chip de sugerencia activo */
.suggestion-chip.active {
  background-color: rgba(138, 86, 232, 0.2);
  color: #8a56e8;
  border-color: #8a56e8;
}

.suggestion-chip:hover {
  background-color: rgba(138, 86, 232, 0.2);
  border-color: rgba(138, 86, 232, 0.5);
}

/* Widget de Portafolio */
.portfolio-widget .portfolio-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: 0.5rem;
}

.portfolio-widget .portfolio-change {
  display: inline-block;
  margin-left: 0.5rem;
  font-size: 0.9rem;
}

.portfolio-widget .asset-allocation {
  margin-top: 1rem;
}

/* Widget de Resumen del Mercado */
.market-overview-widget .market-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.market-overview-widget .market-stat {
  background-color: var(--color-surface-light);
  border-radius: 8px;
  padding: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.market-overview-widget .market-stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.market-overview-widget .stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.market-overview-widget .stat-value {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.market-overview-widget .change {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.market-overview-widget .change.positive {
  color: var(--color-success, #00e676);
  background-color: var(--color-success-bg, rgba(0, 230, 118, 0.1));
}

.market-overview-widget .change.negative {
  color: var(--color-error, #ff3a6e);
  background-color: var(--color-error-bg, rgba(255, 58, 110, 0.1));
}

/* Widget de Tabla de Criptomonedas */
.crypto-table-widget {
  overflow: hidden;
}

.crypto-table-widget .widget-header.with-filters {
  flex-direction: column;
  align-items: flex-start;
}

.crypto-table-widget .header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.crypto-table-widget .filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.crypto-table-widget .time-filter,
.crypto-table-widget .category-filter {
  display: flex;
  gap: 0.25rem;
}

.crypto-table-widget .time-filter button,
.crypto-table-widget .category-filter button {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.crypto-table-widget .time-filter button:hover,
.crypto-table-widget .category-filter button:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.crypto-table-widget .time-filter button.active,
.crypto-table-widget .category-filter button.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.crypto-table-widget .table-container {
  max-height: 500px;
  overflow-y: auto;
}

.crypto-table-widget table {
  width: 100%;
  border-collapse: collapse;
}

.crypto-table-widget th {
  position: sticky;
  top: 0;
  background-color: var(--color-surface-dark);
  z-index: 10;
}

/* Sección de Noticias y Eventos */
.news-events-section {
  margin-top: var(--widget-gap);
}

.section-header {
  margin-bottom: 1rem;
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.news-events-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--widget-gap);
}

/* Widget de Noticias */
.news-widget .news-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.news-widget .news-item {
  display: flex;
  gap: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.news-widget .news-item:hover {
  background-color: var(--color-surface-light);
}

.news-widget .news-item:last-child {
  border-bottom: none;
}

.news-widget .news-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-widget .news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-widget .news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-widget .news-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--text-primary);
  transition: color 0.2s ease;
}

.news-widget .news-title:hover {
  color: var(--color-primary);
}

.news-widget .news-source {
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.news-widget .view-more-news {
  margin-top: 0.75rem;
  text-align: center;
}

.news-widget .view-more-button {
  background: none;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.news-widget .view-more-button:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Widget de Eventos */
.events-widget .events-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.events-widget .event-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.events-widget .event-item:last-child {
  border-bottom: none;
}

.events-widget .event-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--color-primary-transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  flex-shrink: 0;
}

.events-widget .event-content {
  flex: 1;
}

.events-widget .event-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.events-widget .event-details {
  display: flex;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.events-widget .event-project {
  font-weight: var(--font-weight-medium);
}

.events-widget .event-countdown {
  font-size: 0.8rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
  white-space: nowrap;
}

.events-widget .view-more-events {
  margin-top: 0.75rem;
  text-align: center;
}

.events-widget .view-more-button {
  background: none;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.events-widget .view-more-button:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Widget de Alertas */
.alerts-widget .alerts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alerts-widget .alert-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 6px;
  background-color: var(--color-surface-light);
  border-left: 3px solid var(--color-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.alerts-widget .alert-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.alerts-widget .alert-item.triggered {
  border-left-color: var(--color-success, #00e676);
  background-color: var(--color-success-bg, rgba(0, 230, 118, 0.1));
}

.alerts-widget .alert-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-primary-transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  flex-shrink: 0;
}

.alerts-widget .alert-item.triggered .alert-icon {
  background-color: var(--color-success-transparent, rgba(0, 230, 118, 0.2));
  color: var(--color-success, #00e676);
}

.alerts-widget .alert-content {
  flex: 1;
}

.alerts-widget .alert-crypto {
  font-weight: var(--font-weight-semibold);
  margin-bottom: 0.25rem;
}

.alerts-widget .alert-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.alerts-widget .alert-actions {
  display: flex;
  gap: 0.5rem;
}

.alerts-widget .alert-action-button {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.alerts-widget .alert-action-button:hover {
  color: var(--color-primary);
  background-color: var(--color-surface);
}

.alerts-widget .create-alert-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  transition: background-color 0.2s ease;
}

.alerts-widget .create-alert-button:hover {
  background-color: var(--color-primary-dark, #0288d1);
}

/* Media queries para responsividad */
@media (max-width: 1200px) {
  .global-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-main-content {
    grid-template-columns: 1fr;
  }

  .sidebar-column {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: calc(var(--widget-gap) * 1.2);
    padding: calc(var(--widget-padding) * 1.5);
  }

  .guru-widget {
    grid-column: 1 / -1;
  }

  .ai-widgets-group {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: calc(var(--widget-gap) * 1.2);
    margin: 0;
  }

  .insights-widget, .recommendations-widget {
    height: 100%;
  }
}

@media (max-width: 992px) {
  .news-events-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    height: auto;
    padding: 1.25rem;
    gap: 1.25rem;
  }

  .global-stats, .search-container {
    width: 100%;
  }

  .global-metrics {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .portfolio-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .sidebar-column {
    display: flex;
    flex-direction: column;
    padding: var(--widget-padding);
  }

  .ai-widgets-group {
    display: flex;
    flex-direction: column;
    gap: var(--widget-gap);
  }

  .guru-content {
    flex-direction: column;
    text-align: center;
    gap: 1.25rem;
    padding: 1.5rem;
  }

  .guru-avatar-container {
    margin: 0 auto;
  }

  .guru-suggestions {
    justify-content: center;
  }

  .crypto-table-widget .filter-controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .crypto-table-widget .time-filter,
  .crypto-table-widget .category-filter {
    width: 100%;
    justify-content: space-between;
  }

  .insights-widget .widget-content,
  .recommendations-widget .widget-content {
    max-height: 300px;
  }
}

@media (max-width: 576px) {
  .global-metrics {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .dashboard-global-summary {
    padding: 1rem;
  }

  .portfolio-summary-card {
    padding: 0.75rem;
  }

  .metric-card {
    padding: 0.85rem;
  }

  .metric-value {
    font-size: 1rem;
  }

  .crypto-table-widget .time-filter button,
  .crypto-table-widget .category-filter button {
    padding: 0.35rem;
    font-size: 0.75rem;
  }

  .guru-content {
    padding: 1.25rem 1rem;
  }

  .guru-action-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .suggestion-chip {
    padding: 0.35rem 0.75rem;
    font-size: 0.8rem;
  }

  .insights-widget .widget-content,
  .recommendations-widget .widget-content {
    padding: 0.75rem;
    max-height: 250px;
  }

  .widget-header {
    padding: 0.75rem;
  }

  .widget-header h3 {
    font-size: 0.95rem;
  }
}
