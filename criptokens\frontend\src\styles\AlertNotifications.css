.notifications-container {
  background-color: #1a1a2e;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  color: #e6e6e6;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  overflow: hidden;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(45deg, #16162a, #1e1e3a);
  border-bottom: 1px solid rgba(123, 77, 255, 0.2);
  position: relative;
}

.notifications-header h2 {
  margin: 0;
  font-size: 1.5rem;
  background: linear-gradient(45deg, #00f2ff, #4facfe);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.unread-badge {
  background: #f44336;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 10px;
}

.close-button {
  background: none;
  border: none;
  color: #a0a0a0;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close-button:hover {
  color: #ffffff;
}

.notifications-filter {
  display: flex;
  padding: 10px 20px;
  background: rgba(10, 10, 26, 0.5);
  border-bottom: 1px solid rgba(123, 77, 255, 0.2);
}

.notifications-filter button {
  background: none;
  border: none;
  color: #a0a0a0;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.notifications-filter button:hover {
  color: #e6e6e6;
}

.notifications-filter button.active {
  background: rgba(123, 77, 255, 0.2);
  color: #e6e6e6;
}

.notifications-content {
  max-height: 400px;
  overflow-y: auto;
}

.no-notifications {
  text-align: center;
  padding: 30px 0;
  color: #a0a0a0;
}

.notifications-list {
  padding: 10px;
}

.notification-item {
  display: flex;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 10px;
  background: rgba(10, 10, 26, 0.5);
  border: 1px solid rgba(123, 77, 255, 0.2);
  transition: all 0.3s ease;
}

.notification-item.unread {
  background: rgba(123, 77, 255, 0.1);
  border-color: rgba(123, 77, 255, 0.4);
  cursor: pointer;
}

.notification-item.unread:hover {
  background: rgba(123, 77, 255, 0.15);
  transform: translateY(-2px);
}

.notification-icon {
  margin-right: 16px;
  font-size: 1.5rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background-color: #4facfe;
  border-radius: 50%;
  display: inline-block;
  margin-left: 8px;
}

.notification-message {
  color: #a0a0a0;
  font-size: 0.9rem;
  margin-bottom: 8px;
  line-height: 1.4;
}

.notification-time {
  color: #777;
  font-size: 0.8rem;
}

.price-alert-icon {
  color: #ffc107;
}

.system-icon {
  color: #4facfe;
}

.notifications-loading {
  text-align: center;
  padding: 30px;
  color: #a0a0a0;
}

.notifications-error {
  text-align: center;
  padding: 30px;
  color: #f44336;
}
