# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o uv.lock
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.16
    # via
    #   auth0-python
    #   instructor
    #   langchain-community
    #   litellm
aiosignal==1.3.2
    # via aiohttp
alembic==1.15.2
    # via embedchain
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   google-genai
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
appdirs==1.4.4
    # via crewai
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
asttokens==3.0.0
    # via stack-data
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
auth0-python==4.9.0
    # via crewai
azure-common==1.1.28
    # via azure-search-documents
azure-core==1.32.0
    # via azure-search-documents
azure-search-documents==11.5.2
    # via mem0ai
backoff==2.2.1
    # via posthog
bcrypt==4.3.0
    # via chromadb
beautifulsoup4==4.13.3
    # via embedchain
blinker==1.9.0
    # via crewai
build==1.2.2.post1
    # via chromadb
cachetools==5.5.2
    # via
    #   google-auth
    #   gptcache
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   kubernetes
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.1
    # via
    #   pdfminer-six
    #   requests
chroma-hnswlib==0.7.6
    # via chromadb
chromadb==0.5.23
    # via
    #   crewai
    #   crewai-tools
    #   embedchain
click==8.1.8
    # via
    #   crewai
    #   crewai-tools
    #   litellm
    #   typer
    #   uvicorn
cohere==5.14.2
    # via langchain-cohere
coloredlogs==15.0.1
    # via onnxruntime
crewai==0.95.0
    # via
    #   a2a-samples-image-generation (pyproject.toml)
    #   crewai-tools
crewai-tools==0.33.0
    # via crewai
cryptography==44.0.2
    # via
    #   auth0-python
    #   pdfminer-six
dataclasses-json==0.6.7
    # via langchain-community
decorator==5.2.1
    # via ipython
deprecated==1.2.18
    # via
    #   opentelemetry-api
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
    #   opentelemetry-semantic-conventions
deprecation==2.1.0
    # via lancedb
distro==1.9.0
    # via
    #   openai
    #   posthog
docker==7.1.0
    # via crewai-tools
docstring-parser==0.16
    # via instructor
durationpy==0.9
    # via kubernetes
embedchain==0.1.128
    # via crewai-tools
et-xmlfile==2.0.0
    # via openpyxl
executing==2.2.0
    # via stack-data
fastapi==0.115.12
    # via chromadb
fastavro==1.10.0
    # via cohere
filelock==3.18.0
    # via huggingface-hub
flatbuffers==25.2.10
    # via onnxruntime
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via huggingface-hub
google-auth==2.38.0
    # via
    #   google-genai
    #   kubernetes
google-genai==1.9.0
    # via a2a-samples-image-generation (pyproject.toml)
googleapis-common-protos==1.69.2
    # via
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
gptcache==0.1.44
    # via embedchain
grpcio==1.71.0
    # via
    #   chromadb
    #   grpcio-tools
    #   opentelemetry-exporter-otlp-proto-grpc
    #   qdrant-client
grpcio-tools==1.71.0
    # via qdrant-client
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.7
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   chromadb
    #   cohere
    #   google-genai
    #   langsmith
    #   litellm
    #   openai
    #   qdrant-client
httpx-sse==0.4.0
    # via
    #   cohere
    #   langchain-community
huggingface-hub==0.30.1
    # via tokenizers
humanfriendly==10.0
    # via coloredlogs
hyperframe==6.1.0
    # via h2
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==8.6.1
    # via
    #   litellm
    #   opentelemetry-api
importlib-resources==6.5.2
    # via chromadb
instructor==1.7.8
    # via crewai
ipython==9.0.2
    # via pyvis
ipython-pygments-lexers==1.1.1
    # via ipython
isodate==0.7.2
    # via azure-search-documents
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via
    #   instructor
    #   litellm
    #   pyvis
jiter==0.8.2
    # via
    #   instructor
    #   openai
json-repair==0.40.0
    # via crewai
jsonpatch==1.33
    # via langchain-core
jsonpickle==4.0.5
    # via pyvis
jsonpointer==3.0.0
    # via jsonpatch
jsonref==1.1.0
    # via crewai
jsonschema==4.23.0
    # via litellm
jsonschema-specifications==2024.10.1
    # via jsonschema
kubernetes==32.0.1
    # via chromadb
lancedb==0.21.2
    # via crewai-tools
langchain==0.3.22
    # via
    #   embedchain
    #   langchain-community
langchain-cohere==0.3.5
    # via embedchain
langchain-community==0.3.20
    # via
    #   embedchain
    #   langchain-experimental
langchain-core==0.3.50
    # via
    #   langchain
    #   langchain-cohere
    #   langchain-community
    #   langchain-experimental
    #   langchain-openai
    #   langchain-text-splitters
langchain-experimental==0.3.4
    # via langchain-cohere
langchain-openai==0.2.14
    # via embedchain
langchain-text-splitters==0.3.7
    # via langchain
langsmith==0.3.23
    # via
    #   embedchain
    #   langchain
    #   langchain-community
    #   langchain-core
litellm==1.65.1
    # via crewai
mako==1.3.9
    # via alembic
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
marshmallow==3.26.1
    # via dataclasses-json
matplotlib-inline==0.1.7
    # via ipython
mdurl==0.1.2
    # via markdown-it-py
mem0ai==0.1.81
    # via embedchain
mmh3==5.1.0
    # via chromadb
monotonic==1.6
    # via posthog
mpmath==1.3.0
    # via sympy
multidict==6.2.0
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.0.0
    # via typing-inspect
networkx==3.4.2
    # via pyvis
nodeenv==1.9.1
    # via pyright
numpy==2.2.4
    # via
    #   chroma-hnswlib
    #   chromadb
    #   gptcache
    #   langchain-community
    #   onnxruntime
    #   pandas
    #   qdrant-client
oauthlib==3.2.2
    # via
    #   kubernetes
    #   requests-oauthlib
onnxruntime==1.21.0
    # via chromadb
openai==1.70.0
    # via
    #   crewai
    #   crewai-tools
    #   embedchain
    #   instructor
    #   langchain-openai
    #   litellm
    #   mem0ai
openpyxl==3.1.5
    # via crewai
opentelemetry-api==1.31.1
    # via
    #   chromadb
    #   crewai
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.31.1
    # via
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
opentelemetry-exporter-otlp-proto-grpc==1.31.1
    # via chromadb
opentelemetry-exporter-otlp-proto-http==1.31.1
    # via crewai
opentelemetry-instrumentation==0.52b1
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-asgi==0.52b1
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-fastapi==0.52b1
    # via chromadb
opentelemetry-proto==1.31.1
    # via
    #   opentelemetry-exporter-otlp-proto-common
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
opentelemetry-sdk==1.31.1
    # via
    #   chromadb
    #   crewai
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
opentelemetry-semantic-conventions==0.52b1
    # via
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
opentelemetry-util-http==0.52b1
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
orjson==3.10.16
    # via
    #   chromadb
    #   langsmith
overrides==7.7.0
    # via
    #   chromadb
    #   lancedb
packaging==24.2
    # via
    #   build
    #   deprecation
    #   huggingface-hub
    #   lancedb
    #   langchain-core
    #   langsmith
    #   marshmallow
    #   onnxruntime
    #   opentelemetry-instrumentation
pandas==2.2.3
    # via langchain-cohere
parso==0.8.4
    # via jedi
pdfminer-six==20250327
    # via pdfplumber
pdfplumber==0.11.6
    # via crewai
pexpect==4.9.0
    # via ipython
pillow==11.1.0
    # via pdfplumber
portalocker==2.10.1
    # via qdrant-client
posthog==3.23.0
    # via
    #   chromadb
    #   embedchain
    #   mem0ai
prompt-toolkit==3.0.50
    # via ipython
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
protobuf==5.29.4
    # via
    #   googleapis-common-protos
    #   grpcio-tools
    #   onnxruntime
    #   opentelemetry-proto
psycopg2-binary==2.9.10
    # via mem0ai
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.3
    # via stack-data
pyarrow==19.0.1
    # via lancedb
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.1
    # via
    #   chromadb
    #   cohere
    #   crewai
    #   crewai-tools
    #   fastapi
    #   google-genai
    #   instructor
    #   lancedb
    #   langchain
    #   langchain-cohere
    #   langchain-core
    #   langsmith
    #   litellm
    #   mem0ai
    #   openai
    #   pydantic-settings
    #   qdrant-client
pydantic-core==2.33.0
    # via
    #   cohere
    #   instructor
    #   pydantic
pydantic-settings==2.8.1
    # via langchain-community
pygments==2.19.1
    # via
    #   ipython
    #   ipython-pygments-lexers
    #   rich
pyjwt==2.10.1
    # via auth0-python
pypdf==5.4.0
    # via embedchain
pypdfium2==4.30.1
    # via pdfplumber
pypika==0.48.9
    # via chromadb
pyproject-hooks==1.2.0
    # via build
pyright==1.1.398
    # via crewai-tools
pysbd==0.3.4
    # via embedchain
python-dateutil==2.9.0.post0
    # via
    #   kubernetes
    #   pandas
    #   posthog
python-dotenv==1.1.0
    # via
    #   crewai
    #   embedchain
    #   litellm
    #   pydantic-settings
    #   uvicorn
pytube==15.0.0
    # via crewai-tools
pytz==2024.2
    # via
    #   mem0ai
    #   pandas
pyvis==0.3.2
    # via crewai
pyyaml==6.0.2
    # via
    #   chromadb
    #   huggingface-hub
    #   kubernetes
    #   langchain
    #   langchain-community
    #   langchain-core
    #   uvicorn
qdrant-client==1.13.3
    # via mem0ai
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   crewai
    #   tiktoken
requests==2.32.3
    # via
    #   auth0-python
    #   azure-core
    #   cohere
    #   crewai-tools
    #   docker
    #   google-genai
    #   gptcache
    #   huggingface-hub
    #   instructor
    #   kubernetes
    #   langchain
    #   langchain-community
    #   langsmith
    #   opentelemetry-exporter-otlp-proto-http
    #   posthog
    #   requests-oauthlib
    #   requests-toolbelt
    #   tiktoken
requests-oauthlib==2.0.0
    # via kubernetes
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.4
    # via
    #   chromadb
    #   embedchain
    #   instructor
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
schema==0.7.7
    # via embedchain
setuptools==78.1.0
    # via grpcio-tools
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   azure-core
    #   kubernetes
    #   posthog
    #   python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
soupsieve==2.6
    # via beautifulsoup4
sqlalchemy==2.0.40
    # via
    #   alembic
    #   embedchain
    #   langchain
    #   langchain-community
    #   mem0ai
stack-data==0.6.3
    # via ipython
starlette==0.46.1
    # via fastapi
sympy==1.13.3
    # via onnxruntime
tabulate==0.9.0
    # via langchain-cohere
tenacity==9.1.2
    # via
    #   chromadb
    #   instructor
    #   langchain-community
    #   langchain-core
tiktoken==0.9.0
    # via
    #   langchain-openai
    #   litellm
tokenizers==0.20.3
    # via
    #   chromadb
    #   cohere
    #   litellm
tomli==2.2.1
    # via crewai
tomli-w==1.2.0
    # via crewai
tqdm==4.67.1
    # via
    #   chromadb
    #   huggingface-hub
    #   lancedb
    #   openai
traitlets==5.14.3
    # via
    #   ipython
    #   matplotlib-inline
typer==0.15.2
    # via
    #   chromadb
    #   instructor
types-requests==2.32.0.20250328
    # via cohere
typing-extensions==4.13.0
    # via
    #   alembic
    #   azure-core
    #   azure-search-documents
    #   beautifulsoup4
    #   chromadb
    #   cohere
    #   fastapi
    #   google-genai
    #   huggingface-hub
    #   langchain-core
    #   openai
    #   opentelemetry-sdk
    #   pydantic
    #   pydantic-core
    #   pyright
    #   sqlalchemy
    #   typer
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via pandas
urllib3==2.3.0
    # via
    #   auth0-python
    #   docker
    #   kubernetes
    #   qdrant-client
    #   requests
    #   types-requests
uv==0.6.12
    # via crewai
uvicorn==0.34.0
    # via chromadb
uvloop==0.21.0
    # via uvicorn
watchfiles==1.0.4
    # via uvicorn
wcwidth==0.2.13
    # via prompt-toolkit
websocket-client==1.8.0
    # via kubernetes
websockets==15.0.1
    # via
    #   google-genai
    #   uvicorn
wrapt==1.17.2
    # via
    #   deprecated
    #   opentelemetry-instrumentation
yarl==1.18.3
    # via aiohttp
zipp==3.21.0
    # via importlib-metadata
zstandard==0.23.0
    # via langsmith
