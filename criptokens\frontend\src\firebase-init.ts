import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Configuración de Firebase
const firebaseConfig = {
  apiKey: "AIzaSyAFEBqXDfsAhIXrRopCLdgQOIq4WT9DsHE",
  authDomain: "criptoken-11c3b.firebaseapp.com",
  projectId: "criptoken-11c3b",
  storageBucket: "criptoken-11c3b.firebasestorage.app",
  messagingSenderId: "576538378262",
  appId: "1:576538378262:web:7456ae50448da561f63537",
  measurementId: "G-PMFJY76HWT"
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);

// Inicializar Auth con configuración explícita
const auth = getAuth(app);

// Inicializar Firestore
const db = getFirestore(app);

// Verificar inicialización
console.log('Firebase inicializado con proyecto:', firebaseConfig.projectId);
console.log('Auth inicializado:', auth ? 'Sí' : 'No');
console.log('Firestore inicializado:', db ? 'Sí' : 'No');

export { app, auth, db };
