.fundamental-analysis-card {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.fundamental-analysis-card .MuiCardContent-root {
  padding: 24px;
}

.fundamental-analysis-card .MuiAccordion-root {
  margin-bottom: 16px;
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
}

.fundamental-analysis-card .MuiAccordionSummary-root {
  background-color: rgba(0, 0, 0, 0.02);
}

.fundamental-analysis-card .MuiAccordionDetails-root {
  padding: 16px 24px;
}

.fundamental-analysis-card .data-item {
  margin-bottom: 8px;
}

.fundamental-analysis-card .link-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.fundamental-analysis-card .link-item a {
  color: #1976d2;
  text-decoration: none;
  transition: color 0.2s ease;
}

.fundamental-analysis-card .link-item a:hover {
  color: #0d47a1;
  text-decoration: underline;
}

.fundamental-analysis-card .MuiListItem-root {
  padding-top: 2px;
  padding-bottom: 2px;
}

.fundamental-analysis-card .MuiListItemIcon-root {
  min-width: 36px;
}

/* Estilos para modo oscuro */
@media (prefers-color-scheme: dark) {
  .fundamental-analysis-card {
    background-color: #1e1e1e;
    color: #ffffff;
  }
  
  .fundamental-analysis-card .MuiAccordion-root {
    background-color: #1e1e1e;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .fundamental-analysis-card .MuiAccordionSummary-root {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .fundamental-analysis-card .MuiTypography-colorTextSecondary {
    color: rgba(255, 255, 255, 0.7);
  }
  
  .fundamental-analysis-card .link-item a {
    color: #90caf9;
  }
  
  .fundamental-analysis-card .link-item a:hover {
    color: #bbdefb;
  }
}
