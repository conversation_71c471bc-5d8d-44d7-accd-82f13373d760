/**
 * Adaptador para el servidor MCP de Context7
 * 
 * Este adaptador se comunica con el servidor MCP de Context7 para
 * obtener documentación actualizada de bibliotecas y frameworks.
 */

const BaseMcpAdapter = require('./base-mcp-adapter');
const logger = require('../utils/logger');

class Context7McpAdapter extends BaseMcpAdapter {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(options = {}) {
    super('context7', options);
  }
  
  /**
   * Resuelve el ID de una biblioteca
   * @param {string} libraryName - Nombre de la biblioteca
   * @returns {Promise<string>} ID de la biblioteca
   */
  async resolveLibraryId(libraryName) {
    logger.debug('Context7McpAdapter', `Resolviendo ID para biblioteca ${libraryName}`);
    
    const result = await this.executeTool('resolve-library-id', {
      name: libraryName
    });
    
    return result;
  }
  
  /**
   * Obtiene documentación para una biblioteca
   * @param {string} libraryName - Nombre de la biblioteca
   * @param {Object} [options={}] - Opciones adicionales
   * @param {string} [options.topic] - Tema específico a buscar
   * @param {number} [options.maxTokens=4000] - Número máximo de tokens a devolver
   * @returns {Promise<Object>} Documentación de la biblioteca
   */
  async getLibraryDocs(libraryName, options = {}) {
    logger.debug('Context7McpAdapter', `Obteniendo documentación para biblioteca ${libraryName}`, options);
    
    // Primero resolver el ID de la biblioteca
    const libraryId = await this.resolveLibraryId(libraryName);
    
    // Parámetros para la herramienta get-library-docs
    const params = {
      libraryId: libraryId,
      maxTokens: options.maxTokens || 4000
    };
    
    // Añadir el tema si se proporciona
    if (options.topic) {
      params.topic = options.topic;
    }
    
    // Obtener la documentación
    const documentation = await this.executeTool('get-library-docs', params);
    
    return {
      libraryName,
      libraryId,
      topic: options.topic || null,
      documentation
    };
  }
  
  /**
   * Busca documentación para múltiples bibliotecas
   * @param {string[]} libraryNames - Lista de nombres de bibliotecas
   * @param {string} [topic] - Tema común a buscar en todas las bibliotecas
   * @returns {Promise<Object[]>} Documentación de las bibliotecas
   */
  async getMultipleLibraryDocs(libraryNames, topic = null) {
    logger.debug('Context7McpAdapter', `Obteniendo documentación para múltiples bibliotecas: ${libraryNames.join(', ')}`, {
      topic: topic
    });
    
    const results = [];
    
    for (const libraryName of libraryNames) {
      try {
        const docs = await this.getLibraryDocs(libraryName, { topic });
        results.push(docs);
      } catch (error) {
        logger.warn('Context7McpAdapter', `Error al obtener documentación para biblioteca ${libraryName}`, {
          error: error.message
        });
        
        // Añadir un resultado con error
        results.push({
          libraryName,
          error: error.message,
          documentation: `No se pudo obtener documentación para ${libraryName}. Error: ${error.message}`
        });
      }
    }
    
    return results;
  }
  
  /**
   * Detecta bibliotecas mencionadas en un texto
   * @param {string} text - Texto a analizar
   * @returns {Promise<string[]>} Lista de bibliotecas detectadas
   */
  async detectLibraries(text) {
    logger.debug('Context7McpAdapter', 'Detectando bibliotecas en texto');
    
    // Esta función es una simulación ya que Context7 no tiene esta capacidad directamente
    // En una implementación real, podríamos usar un LLM para detectar bibliotecas
    
    // Lista de bibliotecas comunes para detectar
    const commonLibraries = [
      'react', 'vue', 'angular', 'svelte', 'next.js', 'nuxt.js',
      'node.js', 'express', 'koa', 'fastify', 'nest.js',
      'tensorflow', 'pytorch', 'scikit-learn', 'pandas', 'numpy',
      'django', 'flask', 'fastapi', 'spring', 'laravel',
      'react-native', 'flutter', 'ionic', 'electron',
      'jquery', 'bootstrap', 'tailwind', 'material-ui', 'chakra-ui',
      'firebase', 'supabase', 'mongodb', 'postgresql', 'mysql',
      'graphql', 'apollo', 'redux', 'mobx', 'zustand',
      'webpack', 'vite', 'rollup', 'parcel',
      'jest', 'mocha', 'chai', 'cypress', 'playwright',
      'ethers.js', 'web3.js', 'solidity', 'hardhat', 'truffle',
      'chart.js', 'd3.js', 'three.js', 'p5.js',
      'lodash', 'ramda', 'date-fns', 'moment',
      'axios', 'fetch', 'swr', 'react-query',
      'typescript', 'javascript', 'python', 'java', 'c#', 'go', 'rust'
    ];
    
    // Detectar bibliotecas en el texto
    const detectedLibraries = [];
    
    for (const library of commonLibraries) {
      const regex = new RegExp(`\\b${library.replace('.', '\\.')}\\b`, 'i');
      if (regex.test(text)) {
        detectedLibraries.push(library);
      }
    }
    
    return detectedLibraries;
  }
}

module.exports = Context7McpAdapter;
