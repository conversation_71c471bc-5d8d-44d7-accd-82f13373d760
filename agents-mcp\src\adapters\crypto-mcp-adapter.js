/**
 * Adaptador para el servidor MCP de Crypto
 * 
 * Este adaptador se comunica con el servidor MCP de Crypto para
 * obtener información sobre criptomonedas.
 */

const BaseMcpAdapter = require('./base-mcp-adapter');
const logger = require('../utils/logger');

class CryptoMcpAdapter extends BaseMcpAdapter {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(options = {}) {
    super('crypto', options);
  }
  
  /**
   * Obtiene el precio actual de una criptomoneda
   * @param {string} symbol - Sí<PERSON>lo de la criptomoneda (ej: BTC, ETH)
   * @param {string} [currency='usd'] - Moneda en la que obtener el precio
   * @returns {Promise<Object>} Información de precio
   */
  async getPrice(symbol, currency = 'usd') {
    logger.debug('CryptoMcpAdapter', `Obteniendo precio de ${symbol} en ${currency}`);
    
    return await this.executeTool('getPrices', {
      symbols: [symbol],
      currency: currency
    });
  }
  
  /**
   * Obtiene los precios de múltiples criptomonedas
   * @param {string[]} symbols - Lista de símbolos de criptomonedas
   * @param {string} [currency='usd'] - Moneda en la que obtener los precios
   * @returns {Promise<Object>} Información de precios
   */
  async getPrices(symbols, currency = 'usd') {
    logger.debug('CryptoMcpAdapter', `Obteniendo precios de ${symbols.join(', ')} en ${currency}`);
    
    return await this.executeTool('getPrices', {
      symbols: symbols,
      currency: currency
    });
  }
  
  /**
   * Obtiene datos históricos de una criptomoneda
   * @param {string} symbol - Símbolo de la criptomoneda
   * @param {number} days - Número de días de datos históricos
   * @param {string} [interval='daily'] - Intervalo de los datos (daily, hourly, minutely)
   * @returns {Promise<Object>} Datos históricos
   */
  async getHistoricalData(symbol, days, interval = 'daily') {
    logger.debug('CryptoMcpAdapter', `Obteniendo datos históricos de ${symbol} para ${days} días con intervalo ${interval}`);
    
    return await this.executeTool('getHistoricalData', {
      symbol: symbol,
      days: days,
      interval: interval
    });
  }
  
  /**
   * Obtiene datos de mercado de una criptomoneda
   * @param {string} symbol - Símbolo de la criptomoneda
   * @returns {Promise<Object>} Datos de mercado
   */
  async getMarketData(symbol) {
    logger.debug('CryptoMcpAdapter', `Obteniendo datos de mercado de ${symbol}`);
    
    return await this.executeTool('getMarketData', {
      symbol: symbol
    });
  }
  
  /**
   * Obtiene las criptomonedas en tendencia
   * @param {number} [limit=10] - Número máximo de criptomonedas a obtener
   * @returns {Promise<Object>} Criptomonedas en tendencia
   */
  async getTrendingCoins(limit = 10) {
    logger.debug('CryptoMcpAdapter', `Obteniendo criptomonedas en tendencia (límite: ${limit})`);
    
    return await this.executeTool('getTrendingCoins', {
      limit: limit
    });
  }
  
  /**
   * Busca criptomonedas por nombre o símbolo
   * @param {string} query - Consulta de búsqueda
   * @param {number} [limit=10] - Número máximo de resultados
   * @returns {Promise<Object>} Resultados de la búsqueda
   */
  async searchCoins(query, limit = 10) {
    logger.debug('CryptoMcpAdapter', `Buscando criptomonedas con consulta "${query}" (límite: ${limit})`);
    
    return await this.executeTool('searchCoins', {
      query: query,
      limit: limit
    });
  }
  
  /**
   * Obtiene información detallada de una criptomoneda
   * @param {string} id - ID de la criptomoneda
   * @returns {Promise<Object>} Información detallada
   */
  async getCoinInfo(id) {
    logger.debug('CryptoMcpAdapter', `Obteniendo información detallada de ${id}`);
    
    return await this.executeTool('getCoinInfo', {
      id: id
    });
  }
}

module.exports = CryptoMcpAdapter;
