{"name": "onchain-mcp-server", "version": "1.0.0", "description": "MCP server for on-chain analysis of cryptocurrencies", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "blockchain", "ethereum", "onchain", "analysis"], "author": "<PERSON>rip<PERSON><PERSON>", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.18.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}