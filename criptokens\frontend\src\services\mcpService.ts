/**
 * Servicio para interactuar con el servidor MCP de Criptokens
 *
 * Este servicio proporciona métodos para llamar a las herramientas y recursos
 * del servidor MCP que ofrece información sobre criptomonedas y análisis de mercado.
 */

// URL del servidor MCP desde las variables de entorno
const MCP_SERVER_URL = import.meta.env.VITE_MCP_SERVER_URL || 'http://localhost:3101';

/**
 * Interfaz para los parámetros de la herramienta getCryptoPrice
 */
interface GetCryptoPriceParams {
  cryptoId: string;
  currency?: string;
}

/**
 * Interfaz para los parámetros de la herramienta getTopCryptos
 */
interface GetTopCryptosParams {
  limit?: number;
  currency?: string;
}

/**
 * Interfaz para los parámetros de la herramienta getCryptoInfo
 */
interface GetCryptoInfoParams {
  cryptoId: string;
}

/**
 * Interfaz para los parámetros de la herramienta convertCrypto
 */
interface ConvertCryptoParams {
  fromCrypto: string;
  toCrypto: string;
  amount: number;
}

/**
 * Clase que proporciona métodos para interactuar con el servidor MCP
 */
class McpService {
  /**
   * Método genérico para llamar a una herramienta del servidor MCP
   * @param toolName Nombre de la herramienta
   * @param params Parámetros para la herramienta
   * @returns Respuesta de la herramienta
   */
  private async callTool(toolName: string, params: any): Promise<string> {
    try {
      const response = await fetch(`${MCP_SERVER_URL}/tools/${toolName}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ params })
        // Se ha eliminado credentials: 'include' para evitar problemas de CORS
      });

      if (!response.ok) {
        throw new Error(`Error al llamar a la herramienta ${toolName}: ${response.statusText}`);
      }

      const data = await response.json();

      // Extraer el texto de la respuesta
      if (data.content && data.content.length > 0 && data.content[0].type === 'text') {
        return data.content[0].text;
      }

      return JSON.stringify(data);
    } catch (error) {
      console.error(`Error en McpService.callTool(${toolName}):`, error);
      throw error;
    }
  }

  /**
   * Obtiene el precio actual de una criptomoneda
   * @param params Parámetros para la herramienta
   * @returns Información sobre el precio de la criptomoneda
   */
  async getCryptoPrice(params: GetCryptoPriceParams): Promise<string> {
    return this.callTool('getCryptoPrice', params);
  }

  /**
   * Obtiene las principales criptomonedas por capitalización de mercado
   * @param params Parámetros para la herramienta
   * @returns Lista de las principales criptomonedas
   */
  async getTopCryptos(params: GetTopCryptosParams = {}): Promise<string> {
    return this.callTool('getTopCryptos', params);
  }

  /**
   * Analiza el sentimiento actual del mercado de criptomonedas
   * @returns Análisis del sentimiento del mercado
   */
  async getMarketSentiment(): Promise<string> {
    return this.callTool('getMarketSentiment', {});
  }

  /**
   * Obtiene información detallada sobre una criptomoneda
   * @param params Parámetros para la herramienta
   * @returns Información detallada sobre la criptomoneda
   */
  async getCryptoInfo(params: GetCryptoInfoParams): Promise<string> {
    return this.callTool('getCryptoInfo', params);
  }

  /**
   * Convierte una cantidad de una criptomoneda a otra
   * @param params Parámetros para la herramienta
   * @returns Resultado de la conversión
   */
  async convertCrypto(params: ConvertCryptoParams): Promise<string> {
    return this.callTool('convertCrypto', params);
  }

  /**
   * Obtiene un recurso de criptomoneda
   * @param cryptoId ID de la criptomoneda
   * @returns Información sobre la criptomoneda como recurso
   */
  async getCryptoResource(cryptoId: string): Promise<string> {
    try {
      const response = await fetch(`${MCP_SERVER_URL}/resources/crypto/${cryptoId}`, {
        // Se ha eliminado credentials: 'include' para evitar problemas de CORS
      });

      if (!response.ok) {
        throw new Error(`Error al obtener el recurso crypto://${cryptoId}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.contents && data.contents.length > 0) {
        return data.contents[0].text;
      }

      return JSON.stringify(data);
    } catch (error) {
      console.error(`Error en McpService.getCryptoResource(${cryptoId}):`, error);
      throw error;
    }
  }
}

// Exportar una instancia del servicio
export const mcpService = new McpService();

// Exportar interfaces para su uso en otros componentes
export type {
  GetCryptoPriceParams,
  GetTopCryptosParams,
  GetCryptoInfoParams,
  ConvertCryptoParams
};
