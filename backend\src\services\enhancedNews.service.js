/**
 * Servicio mejorado para noticias de criptomonedas
 */
const axios = require('axios');
const NodeCache = require('node-cache');
const { analyzeNewsSentiment } = require('./sentiment.service');

// Crear caché con tiempo de vida de 30 minutos
const cache = new NodeCache({ stdTTL: 1800 });

/**
 * Busca noticias sobre criptomonedas con análisis de sentimiento
 * @param {string} query - Consul<PERSON> de búsqueda
 * @param {number} count - Número de resultados a devolver
 * @param {string} freshness - Frescura de los resultados (pd = último día, pw = última semana, pm = último mes)
 * @returns {Promise<Object>} - Resultados de la búsqueda con análisis de sentimiento
 */
async function searchCryptoNewsWithSentiment(query, count = 10, freshness = 'pw') {
  try {
    // Clave de caché única para esta consulta
    const cacheKey = `news_${query}_${count}_${freshness}`;
    
    // Verificar si los resultados están en caché
    const cachedResults = cache.get(cacheKey);
    if (cachedResults) {
      console.log(`Usando resultados de noticias en caché para "${query}"`);
      return cachedResults;
    }
    
    console.log(`Buscando noticias para "${query}" (${count} resultados, frescura: ${freshness})...`);
    
    // Realizar búsqueda a través del MCP Brave Search
    const response = await axios.post('http://localhost:3102/search', {
      query: `${query} cryptocurrency crypto`,
      count,
      freshness
    });
    
    if (response.status !== 200) {
      throw new Error(`Error en la búsqueda de noticias: ${response.statusText}`);
    }
    
    const results = response.data.results || [];
    
    // Analizar el sentimiento de cada noticia
    const resultsWithSentiment = await Promise.all(
      results.map(async (result) => {
        // Combinar título y descripción para el análisis de sentimiento
        const content = `${result.title}. ${result.description || ''}`;
        const sentiment = await analyzeNewsSentiment(content);
        
        return {
          ...result,
          sentiment
        };
      })
    );
    
    // Calcular el sentimiento general
    const overallSentiment = calculateOverallSentiment(resultsWithSentiment);
    
    // Organizar los resultados por relevancia y sentimiento
    const organizedResults = organizeNewsByRelevance(resultsWithSentiment);
    
    const enhancedResults = {
      query,
      count,
      freshness,
      overallSentiment,
      results: organizedResults
    };
    
    // Guardar en caché
    cache.set(cacheKey, enhancedResults);
    
    return enhancedResults;
  } catch (error) {
    console.error(`Error al buscar noticias para "${query}":`, error.message);
    
    // Si hay un error, generar resultados simulados
    return generateSimulatedNewsResults(query, count);
  }
}

/**
 * Calcula el sentimiento general de un conjunto de noticias
 * @param {Array} results - Resultados de noticias con sentimiento
 * @returns {Object} - Sentimiento general
 */
function calculateOverallSentiment(results) {
  if (!results || results.length === 0) {
    return { score: 0, label: 'neutral' };
  }
  
  // Calcular la puntuación media de sentimiento
  const totalScore = results.reduce((sum, result) => {
    return sum + (result.sentiment ? result.sentiment.score : 0);
  }, 0);
  
  const averageScore = totalScore / results.length;
  
  // Determinar la etiqueta de sentimiento
  let label = 'neutral';
  if (averageScore > 0.2) {
    label = 'positive';
  } else if (averageScore < -0.2) {
    label = 'negative';
  }
  
  return {
    score: averageScore,
    label
  };
}

/**
 * Organiza las noticias por relevancia y sentimiento
 * @param {Array} results - Resultados de noticias con sentimiento
 * @returns {Array} - Resultados organizados
 */
function organizeNewsByRelevance(results) {
  if (!results || results.length === 0) {
    return [];
  }
  
  // Ordenar por una combinación de relevancia y sentimiento
  return results.sort((a, b) => {
    // Priorizar noticias con sentimiento extremo (muy positivo o muy negativo)
    const sentimentWeightA = Math.abs(a.sentiment ? a.sentiment.score : 0);
    const sentimentWeightB = Math.abs(b.sentiment ? b.sentiment.score : 0);
    
    // Combinar con la posición original (relevancia)
    const indexA = results.indexOf(a);
    const indexB = results.indexOf(b);
    
    // Fórmula que combina sentimiento y relevancia
    const scoreA = sentimentWeightA * 0.7 - indexA * 0.3;
    const scoreB = sentimentWeightB * 0.7 - indexB * 0.3;
    
    return scoreB - scoreA;
  });
}

/**
 * Genera resultados de noticias simulados
 * @param {string} query - Consulta de búsqueda
 * @param {number} count - Número de resultados a devolver
 * @returns {Object} - Resultados simulados
 */
function generateSimulatedNewsResults(query, count) {
  console.log(`Generando resultados de noticias simulados para "${query}"...`);
  
  const currentDate = new Date();
  
  // Determinar si la consulta es sobre una criptomoneda específica
  const isCryptoSpecific = /bitcoin|btc|ethereum|eth|binance|bnb|solana|sol|cardano|ada|ripple|xrp/i.test(query);
  
  // Generar títulos y descripciones basados en la consulta
  let simulatedResults = [];
  
  if (isCryptoSpecific) {
    // Noticias específicas para criptomonedas populares
    if (/bitcoin|btc/i.test(query)) {
      simulatedResults = generateBitcoinNews(count);
    } else if (/ethereum|eth/i.test(query)) {
      simulatedResults = generateEthereumNews(count);
    } else if (/binance|bnb/i.test(query)) {
      simulatedResults = generateBinanceNews(count);
    } else if (/solana|sol/i.test(query)) {
      simulatedResults = generateSolanaNews(count);
    } else {
      simulatedResults = generateGenericCryptoNews(query, count);
    }
  } else {
    // Noticias generales sobre criptomonedas
    simulatedResults = generateMarketNews(count);
  }
  
  // Añadir sentimiento a cada noticia
  simulatedResults.forEach(result => {
    // Determinar sentimiento basado en palabras clave en el título
    const positiveKeywords = ['surge', 'rally', 'gain', 'bullish', 'adoption', 'breakthrough', 'partnership'];
    const negativeKeywords = ['crash', 'drop', 'fall', 'bearish', 'ban', 'regulation', 'hack', 'scam'];
    
    let sentimentScore = 0;
    
    // Comprobar palabras clave positivas
    positiveKeywords.forEach(keyword => {
      if (result.title.toLowerCase().includes(keyword)) {
        sentimentScore += 0.2;
      }
    });
    
    // Comprobar palabras clave negativas
    negativeKeywords.forEach(keyword => {
      if (result.title.toLowerCase().includes(keyword)) {
        sentimentScore -= 0.2;
      }
    });
    
    // Limitar el rango de sentimiento
    sentimentScore = Math.max(-0.9, Math.min(0.9, sentimentScore));
    
    // Determinar la etiqueta de sentimiento
    let sentimentLabel = 'neutral';
    if (sentimentScore > 0.2) {
      sentimentLabel = 'positive';
    } else if (sentimentScore < -0.2) {
      sentimentLabel = 'negative';
    }
    
    result.sentiment = {
      score: sentimentScore,
      label: sentimentLabel
    };
  });
  
  // Calcular el sentimiento general
  const overallSentiment = calculateOverallSentiment(simulatedResults);
  
  return {
    query,
    count,
    freshness: 'pw',
    overallSentiment,
    results: simulatedResults
  };
}

/**
 * Genera noticias simuladas sobre Bitcoin
 * @param {number} count - Número de noticias a generar
 * @returns {Array} - Noticias simuladas
 */
function generateBitcoinNews(count) {
  const news = [
    {
      title: "Bitcoin Surges Past $60,000 as Institutional Adoption Grows",
      description: "Bitcoin has reached a new all-time high as major financial institutions continue to invest in the cryptocurrency.",
      url: "https://example.com/bitcoin-surge",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Bitcoin Mining Difficulty Reaches New Heights",
      description: "The Bitcoin network's mining difficulty has adjusted upward, reflecting increased competition among miners.",
      url: "https://example.com/bitcoin-mining",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Bitcoin ETF Approval Could Be Coming Soon, Analysts Say",
      description: "Regulatory experts suggest that the SEC may be warming up to the idea of approving a Bitcoin ETF in the near future.",
      url: "https://example.com/bitcoin-etf",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Bitcoin's Energy Consumption Remains a Concern for Environmentalists",
      description: "Critics continue to highlight the environmental impact of Bitcoin mining as the network grows.",
      url: "https://example.com/bitcoin-energy",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Bitcoin Adoption in El Salvador Shows Mixed Results One Year Later",
      description: "A year after making Bitcoin legal tender, El Salvador's experiment has shown both benefits and challenges.",
      url: "https://example.com/bitcoin-el-salvador",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Major Bank Launches Bitcoin Custody Services for Institutional Clients",
      description: "Another major financial institution has entered the cryptocurrency space with new Bitcoin services.",
      url: "https://example.com/bank-bitcoin",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Bitcoin Volatility Decreases as Market Matures",
      description: "Analysis shows that Bitcoin's price volatility has been decreasing over time, potentially indicating a maturing market.",
      url: "https://example.com/bitcoin-volatility",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Bitcoin Lightning Network Capacity Reaches All-Time High",
      description: "The Layer 2 scaling solution for Bitcoin continues to grow, enabling faster and cheaper transactions.",
      url: "https://example.com/lightning-network",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Bitcoin Faces Resistance at Key Technical Level",
      description: "Technical analysts point to significant resistance that Bitcoin needs to overcome for continued upward momentum.",
      url: "https://example.com/bitcoin-resistance",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "New Research Suggests Bitcoin Could Reach $100,000 by Year End",
      description: "A new report from a major investment firm projects significant upside potential for Bitcoin in the coming months.",
      url: "https://example.com/bitcoin-projection",
      publishedDate: getRandomRecentDate()
    }
  ];
  
  // Devolver el número solicitado de noticias
  return news.slice(0, count);
}

/**
 * Genera noticias simuladas sobre Ethereum
 * @param {number} count - Número de noticias a generar
 * @returns {Array} - Noticias simuladas
 */
function generateEthereumNews(count) {
  const news = [
    {
      title: "Ethereum Completes Major Network Upgrade",
      description: "The Ethereum network has successfully implemented its latest upgrade, improving scalability and reducing gas fees.",
      url: "https://example.com/ethereum-upgrade",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum DeFi Ecosystem Continues to Grow Despite Market Downturn",
      description: "The total value locked in Ethereum-based DeFi protocols has remained resilient despite broader market challenges.",
      url: "https://example.com/ethereum-defi",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum Layer 2 Solutions See Surge in Adoption",
      description: "Users are increasingly turning to Layer 2 scaling solutions to avoid high gas fees on the Ethereum mainnet.",
      url: "https://example.com/ethereum-layer2",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum NFT Market Shows Signs of Recovery",
      description: "After months of declining sales, the Ethereum NFT market is showing renewed activity and interest.",
      url: "https://example.com/ethereum-nft",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum Staking Yields Decline as More Validators Join Network",
      description: "The increasing number of validators on the Ethereum network has led to lower staking rewards for participants.",
      url: "https://example.com/ethereum-staking",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Major Ethereum Protocol Vulnerability Patched",
      description: "Developers have successfully addressed a potential security issue in the Ethereum protocol before it could be exploited.",
      url: "https://example.com/ethereum-security",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum Foundation Announces New Research Grants",
      description: "The Ethereum Foundation has allocated $30 million in grants to support research and development on the network.",
      url: "https://example.com/ethereum-grants",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum Gas Fees Reach Six-Month Low",
      description: "Transaction costs on the Ethereum network have decreased significantly, making the blockchain more accessible.",
      url: "https://example.com/ethereum-gas",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum Developer Activity Reaches All-Time High",
      description: "Data shows that more developers than ever are building on Ethereum, despite competition from alternative blockchains.",
      url: "https://example.com/ethereum-developers",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Ethereum's Transition to Proof-of-Stake Reduces Energy Consumption by 99.95%",
      description: "New analysis confirms the dramatic reduction in energy usage following Ethereum's shift from proof-of-work.",
      url: "https://example.com/ethereum-energy",
      publishedDate: getRandomRecentDate()
    }
  ];
  
  // Devolver el número solicitado de noticias
  return news.slice(0, count);
}

/**
 * Genera noticias simuladas sobre Binance
 * @param {number} count - Número de noticias a generar
 * @returns {Array} - Noticias simuladas
 */
function generateBinanceNews(count) {
  const news = [
    {
      title: "Binance Faces Regulatory Challenges in Multiple Jurisdictions",
      description: "The world's largest cryptocurrency exchange continues to navigate complex regulatory environments globally.",
      url: "https://example.com/binance-regulation",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance Coin (BNB) Sees Increased Utility Across BNB Chain Ecosystem",
      description: "BNB's use cases continue to expand beyond just trading fee discounts on the Binance exchange.",
      url: "https://example.com/bnb-utility",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance Launches $500 Million Fund to Support Web3 and Blockchain Startups",
      description: "The exchange's venture capital arm is increasing its investment in early-stage blockchain projects.",
      url: "https://example.com/binance-fund",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance Smart Chain Rebrands to BNB Chain, Focuses on Multi-Chain Strategy",
      description: "The blockchain formerly known as Binance Smart Chain has rebranded as part of a broader strategic shift.",
      url: "https://example.com/bnb-chain",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance Expands Compliance Team Amid Regulatory Scrutiny",
      description: "The exchange has hired several former regulators and compliance experts to strengthen its regulatory approach.",
      url: "https://example.com/binance-compliance",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance Reduces Maximum Leverage for Futures Trading",
      description: "In response to regulatory concerns, Binance has lowered the maximum leverage available to traders.",
      url: "https://example.com/binance-leverage",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance NFT Marketplace Sees Growing Competition",
      description: "While still a major player, Binance's NFT platform faces increasing competition from specialized marketplaces.",
      url: "https://example.com/binance-nft",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance CEO Addresses Security Concerns Following Attempted Hack",
      description: "After thwarting a sophisticated attack attempt, Binance's CEO has outlined enhanced security measures.",
      url: "https://example.com/binance-security",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Binance Launches Institutional Trading Platform",
      description: "The new platform aims to attract more institutional investors to cryptocurrency trading.",
      url: "https://example.com/binance-institutional",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "BNB Chain Transaction Volume Surpasses Ethereum for Third Consecutive Month",
      description: "The BNB Chain continues to see high adoption, particularly for DeFi and gaming applications.",
      url: "https://example.com/bnb-volume",
      publishedDate: getRandomRecentDate()
    }
  ];
  
  // Devolver el número solicitado de noticias
  return news.slice(0, count);
}

/**
 * Genera noticias simuladas sobre Solana
 * @param {number} count - Número de noticias a generar
 * @returns {Array} - Noticias simuladas
 */
function generateSolanaNews(count) {
  const news = [
    {
      title: "Solana Network Experiences Downtime, Developers Rush to Implement Fix",
      description: "The Solana blockchain faced another outage, raising questions about its reliability for critical applications.",
      url: "https://example.com/solana-outage",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Solana Mobile Announces Second-Generation Web3 Smartphone",
      description: "Following the success of the Saga phone, Solana Mobile is preparing to launch an improved crypto-focused device.",
      url: "https://example.com/solana-mobile",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Solana DeFi Ecosystem Reaches $10 Billion in Total Value Locked",
      description: "Despite market challenges, Solana's DeFi protocols continue to attract significant capital.",
      url: "https://example.com/solana-defi",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Solana Foundation Announces Developer Grants Program",
      description: "A new initiative aims to attract more developers to build applications on the Solana blockchain.",
      url: "https://example.com/solana-grants",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Major Upgrade Improves Solana's Transaction Processing Capacity",
      description: "The latest network upgrade has significantly increased Solana's ability to handle concurrent transactions.",
      url: "https://example.com/solana-upgrade",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Solana NFT Market Shows Resilience Despite Broader Crypto Downturn",
      description: "Trading volumes on Solana NFT marketplaces have remained relatively stable compared to other blockchains.",
      url: "https://example.com/solana-nft",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Institutional Investors Increase Exposure to Solana",
      description: "Several major investment funds have added Solana to their cryptocurrency portfolios in recent months.",
      url: "https://example.com/solana-institutional",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Solana-Based Gaming Projects Attract Millions of Users",
      description: "Web3 games built on Solana are seeing significant adoption due to low fees and fast transactions.",
      url: "https://example.com/solana-gaming",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Solana Pay Adoption Grows Among Merchants",
      description: "More retailers are implementing Solana's payment solution for cryptocurrency transactions.",
      url: "https://example.com/solana-pay",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Solana Validator Count Reaches New High, Improving Decentralization",
      description: "The increasing number of validators on the Solana network is addressing previous centralization concerns.",
      url: "https://example.com/solana-validators",
      publishedDate: getRandomRecentDate()
    }
  ];
  
  // Devolver el número solicitado de noticias
  return news.slice(0, count);
}

/**
 * Genera noticias simuladas sobre una criptomoneda genérica
 * @param {string} query - Consulta de búsqueda
 * @param {number} count - Número de noticias a generar
 * @returns {Array} - Noticias simuladas
 */
function generateGenericCryptoNews(query, count) {
  // Extraer el nombre de la criptomoneda de la consulta
  const cryptoName = query.replace(/\s+/g, ' ').trim();
  
  const news = [
    {
      title: `${cryptoName} Price Surges 15% Following Major Partnership Announcement`,
      description: `The value of ${cryptoName} has increased significantly after revealing a collaboration with a major tech company.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-partnership`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `${cryptoName} Network Upgrade Scheduled for Next Month`,
      description: `Developers have announced a significant update to the ${cryptoName} protocol aimed at improving scalability.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-upgrade`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `${cryptoName} Added to Major Cryptocurrency Exchange`,
      description: `Trading pairs for ${cryptoName} are now available on one of the world's largest cryptocurrency exchanges.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-listing`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `${cryptoName} Foundation Launches Developer Grant Program`,
      description: `A new initiative aims to attract more developers to build on the ${cryptoName} blockchain.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-grants`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `Analysts Predict Bright Future for ${cryptoName} Despite Market Volatility`,
      description: `Several cryptocurrency experts remain bullish on ${cryptoName}'s long-term prospects.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-analysis`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `${cryptoName} Community Votes on Governance Proposal`,
      description: `Token holders are currently voting on a significant change to the ${cryptoName} protocol.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-governance`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `${cryptoName} Faces Regulatory Scrutiny in Multiple Jurisdictions`,
      description: `Regulatory authorities are examining ${cryptoName}'s compliance with securities laws.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-regulation`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `New Use Cases Emerge for ${cryptoName} in DeFi Ecosystem`,
      description: `Decentralized finance protocols are finding innovative ways to utilize ${cryptoName}.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-defi`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `${cryptoName} Transaction Volume Reaches All-Time High`,
      description: `Network activity on the ${cryptoName} blockchain has surged to unprecedented levels.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-volume`,
      publishedDate: getRandomRecentDate()
    },
    {
      title: `${cryptoName} Team Addresses Security Concerns Following Audit`,
      description: `Developers have implemented fixes for potential vulnerabilities identified in a recent security audit.`,
      url: `https://example.com/${cryptoName.toLowerCase()}-security`,
      publishedDate: getRandomRecentDate()
    }
  ];
  
  // Devolver el número solicitado de noticias
  return news.slice(0, count);
}

/**
 * Genera noticias simuladas sobre el mercado de criptomonedas en general
 * @param {number} count - Número de noticias a generar
 * @returns {Array} - Noticias simuladas
 */
function generateMarketNews(count) {
  const news = [
    {
      title: "Cryptocurrency Market Recovers as Bitcoin Leads Rally",
      description: "The overall crypto market capitalization has increased by 10% in the past week, with Bitcoin leading the recovery.",
      url: "https://example.com/crypto-recovery",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Regulatory Clarity on the Horizon for Cryptocurrency Industry",
      description: "Lawmakers in several major economies are working on comprehensive cryptocurrency regulations.",
      url: "https://example.com/crypto-regulation",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "DeFi Sector Shows Resilience Despite Market Volatility",
      description: "Decentralized finance protocols continue to attract users and capital despite broader market challenges.",
      url: "https://example.com/defi-resilience",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "NFT Market Enters New Phase as Utility Projects Gain Traction",
      description: "The focus in the NFT space is shifting from pure collectibles to projects with practical utility.",
      url: "https://example.com/nft-utility",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Institutional Adoption of Cryptocurrencies Continues to Grow",
      description: "More traditional financial institutions are offering cryptocurrency services to their clients.",
      url: "https://example.com/institutional-adoption",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Layer 2 Solutions See Surge in Adoption as Ethereum Gas Fees Rise",
      description: "Users are increasingly turning to scaling solutions to avoid high transaction costs on mainnet blockchains.",
      url: "https://example.com/layer2-adoption",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Central Bank Digital Currencies Gain Momentum Globally",
      description: "More countries are advancing their CBDC projects, potentially reshaping the future of money.",
      url: "https://example.com/cbdc-momentum",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Web3 Gaming Attracts Mainstream Players Despite Crypto Winter",
      description: "Major gaming companies are exploring blockchain technology despite the broader market downturn.",
      url: "https://example.com/web3-gaming",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Cryptocurrency Mining Industry Adapts to Changing Economics",
      description: "Mining companies are implementing new strategies to remain profitable in the current market environment.",
      url: "https://example.com/mining-adaptation",
      publishedDate: getRandomRecentDate()
    },
    {
      title: "Cross-Chain Bridges Face Security Challenges as Hacks Continue",
      description: "The interoperability sector is working to improve security following several high-profile bridge exploits.",
      url: "https://example.com/bridge-security",
      publishedDate: getRandomRecentDate()
    }
  ];
  
  // Devolver el número solicitado de noticias
  return news.slice(0, count);
}

/**
 * Genera una fecha reciente aleatoria
 * @returns {string} - Fecha en formato ISO
 */
function getRandomRecentDate() {
  const now = new Date();
  const daysAgo = Math.floor(Math.random() * 7); // Entre 0 y 6 días atrás
  const hoursAgo = Math.floor(Math.random() * 24); // Entre 0 y 23 horas atrás
  
  const date = new Date(now);
  date.setDate(date.getDate() - daysAgo);
  date.setHours(date.getHours() - hoursAgo);
  
  return date.toISOString();
}

module.exports = {
  searchCryptoNewsWithSentiment
};
