/**
 * Definiciones de tipos para el sistema de agentes MCP
 * 
 * Este archivo contiene las definiciones de tipos utilizadas en todo el sistema.
 * Aunque JavaScript no tiene tipado estático, estas definiciones sirven como
 * documentación y pueden ser utilizadas por herramientas como JSDoc.
 */

/**
 * @typedef {Object} McpServerConfig
 * @property {string} url - URL del servidor MCP
 * @property {number} timeout - Tiempo máximo de espera para las solicitudes en ms
 * @property {string[]} capabilities - Lista de capacidades del servidor MCP
 */

/**
 * @typedef {Object} AgentConfig
 * @property {string} name - Nombre del agente
 * @property {string} description - Descripción del agente
 * @property {string} model - Modelo de LLM a utilizar
 * @property {string[]} allowedMcpServers - Lista de servidores MCP a los que el agente tiene acceso
 * @property {string} systemPrompt - Prompt de sistema para el agente
 */

/**
 * @typedef {Object} OrchestratorConfig
 * @property {string} name - Nombre del orquestador
 * @property {string} description - Descripción del orquestador
 * @property {string} model - Modelo de LLM a utilizar
 * @property {string} systemPrompt - Prompt de sistema para el orquestador
 */

/**
 * @typedef {Object} SystemConfig
 * @property {boolean} debug - Modo de depuración
 * @property {string} logLevel - Nivel de logging
 * @property {number} timeout - Tiempo máximo de espera global en ms
 * @property {number} maxRetries - Número máximo de reintentos
 * @property {string} defaultModel - Modelo de LLM por defecto
 */

/**
 * @typedef {Object} Config
 * @property {SystemConfig} system - Configuración general del sistema
 * @property {Object.<string, McpServerConfig>} mcpServers - Configuración de los servidores MCP
 * @property {Object.<string, AgentConfig>} agents - Configuración de los agentes
 * @property {OrchestratorConfig} orchestrator - Configuración del orquestador
 */

/**
 * @typedef {Object} Task
 * @property {string} id - Identificador único de la tarea
 * @property {string} type - Tipo de tarea
 * @property {string} description - Descripción de la tarea
 * @property {string} assignedAgent - Agente asignado a la tarea
 * @property {Object} parameters - Parámetros de la tarea
 * @property {string} status - Estado de la tarea (pending, in-progress, completed, failed)
 * @property {Object} [result] - Resultado de la tarea
 * @property {Error} [error] - Error si la tarea falló
 * @property {Date} createdAt - Fecha de creación de la tarea
 * @property {Date} [startedAt] - Fecha de inicio de la tarea
 * @property {Date} [completedAt] - Fecha de finalización de la tarea
 */

/**
 * @typedef {Object} Plan
 * @property {string} id - Identificador único del plan
 * @property {string} userRequest - Solicitud original del usuario
 * @property {Task[]} tasks - Lista de tareas que componen el plan
 * @property {string} status - Estado del plan (pending, in-progress, completed, failed)
 * @property {Object} [result] - Resultado final del plan
 * @property {Date} createdAt - Fecha de creación del plan
 * @property {Date} [completedAt] - Fecha de finalización del plan
 */

/**
 * @typedef {Object} Message
 * @property {string} role - Rol del mensaje (system, user, assistant, function)
 * @property {string} content - Contenido del mensaje
 * @property {string} [name] - Nombre de la función (solo para role=function)
 */

/**
 * @typedef {Object} McpToolCall
 * @property {string} server - Nombre del servidor MCP
 * @property {string} tool - Nombre de la herramienta
 * @property {Object} parameters - Parámetros de la llamada
 * @property {Object} [result] - Resultado de la llamada
 * @property {Error} [error] - Error si la llamada falló
 */

// No se exporta nada ya que este archivo solo define tipos
module.exports = {};
