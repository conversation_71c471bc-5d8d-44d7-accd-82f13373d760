/**
 * Middleware de autenticación para el backend de Criptokens
 */

const { admin } = require('../config/firebase');
const logger = require('../utils/logger');

/**
 * Middleware para verificar el token de autenticación de Firebase
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 * @param {Function} next - Función para continuar con el siguiente middleware
 */
const authenticateToken = async (req, res, next) => {
  // Para desarrollo, permitir solicitudes sin autenticación
  if (process.env.NODE_ENV === 'development' || true) {
    // Simular un usuario autenticado para desarrollo
    req.user = {
      uid: req.query.userId || req.body.userId || 'dev-user-123',
      email: '<EMAIL>',
      role: 'user'
    };
    return next();
  }

  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No se proporcionó un token de autenticación válido'
      });
    }

    const token = authHeader.split(' ')[1];
    
    // Verificar el token con Firebase Admin
    const decodedToken = await admin.auth().verifyIdToken(token);
    
    // Añadir el usuario decodificado a la solicitud
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      role: decodedToken.role || 'user'
    };
    
    next();
  } catch (error) {
    logger.error('Error de autenticación:', error.message);
    return res.status(403).json({
      success: false,
      message: 'Error de autenticación',
      error: error.message
    });
  }
};

module.exports = {
  authenticateToken
};
