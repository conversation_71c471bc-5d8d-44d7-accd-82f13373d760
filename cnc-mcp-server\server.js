import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import fetch from "node-fetch";

// Crear el servidor MCP
const server = new McpServer({
  name: "CNC MCP Server",
  version: "1.0.0",
  description: "Servidor MCP proxy para la API de Cripto Noticias y Cotizaciones (CNC)"
});

// URL del servidor backend
const BACKEND_URL = "http://localhost:3002"; // Asegúrate de que este sea el puerto correcto

// Función para obtener datos históricos desde el backend
async function getCncHistoricalData(symbol, days) {
  try {
    const response = await fetch(`${BACKEND_URL}/api/crypto/historical/${symbol}?days=${days}`);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Error al obtener datos históricos del backend: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${symbol} desde el backend:`, error);
    throw error;
  }
}

// Herramienta para obtener datos históricos de CNC
server.tool(
  "getCncHistoricalData",
  {
    symbol: z.string().describe("Símbolo de la criptomoneda (ej: BTC, ETH)"),
    days: z.number().min(1).max(365).default(30).describe("Número de días de datos históricos")
  },
  async ({ symbol, days }) => {
    try {
      const data = await getCncHistoricalData(symbol.toLowerCase(), days);

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2)
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error al obtener datos históricos para ${symbol}: ${error.message}`
          }
        ]
      };
    }
  }
);

// Iniciar el servidor MCP
console.log("Iniciando servidor MCP para CNC...");
const transport = new StdioServerTransport();
await server.connect(transport);
console.log("Servidor MCP de CNC conectado y listo para recibir solicitudes.");
