# Instrucciones para usar datos reales en lugar de datos simulados

Para hacer pruebas con datos reales, sigue estos pasos:

## 1. Asegúrate de que el servidor MCP esté en ejecución

El servidor MCP debe estar ejecutándose en el puerto 3100. Puedes verificarlo con:

```bash
curl http://localhost:3100/tools
```

Si no está en ejecución, puedes iniciarlo con:

```bash
node start-all.js
```

## 2. Modifica los siguientes archivos:

### frontend/src/services/mcpClient.ts

Busca:
```typescript
constructor() {
  // Siempre usar datos simulados por defecto para evitar errores de conexión
  this.mockData = true;
  console.log(`CryptoApiClient inicializado. Usando datos simulados por defecto.`);
}
```

Cambia a:
```typescript
constructor() {
  // Usar datos reales ya que el servidor MCP está disponible
  this.mockData = false;
  console.log(`CryptoApiClient inicializado. Usando datos reales a través del servidor MCP.`);
}
```

### frontend/src/services/api.ts

Busca:
```typescript
// Usar datos simulados o reales
const useMockData = true; // Cambiado a true para usar datos simulados por defecto
const useMcpServer = false; // Cambiado a false para evitar errores de conexión
```

Cambia a:
```typescript
// Usar datos simulados o reales
const useMockData = false; // Cambiado a false para usar datos reales
const useMcpServer = true; // Cambiado a true para usar el servidor MCP
```

### frontend/src/services/crypto.service.ts

Busca:
```typescript
// Función para verificar si el servidor MCP está disponible
const checkMcpServerAvailability = async (): Promise<boolean> => {
  // Siempre devolver false para usar datos simulados por defecto
  console.log('Usando datos simulados por defecto, sin intentar conectar al servidor MCP');
  return false;
};
```

Cambia a:
```typescript
// Función para verificar si el servidor MCP está disponible
const checkMcpServerAvailability = async (): Promise<boolean> => {
  try {
    // Intentar conectarse al servidor MCP
    const response = await fetch(`${MCP_SERVER_URL}/tools`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.ok;
  } catch (error) {
    console.error('Error al verificar disponibilidad del servidor MCP:', error);
    return false;
  }
};
```

## 3. Reinicia la aplicación

Después de hacer estos cambios, reinicia la aplicación para que cargue la nueva configuración.

## 4. Verifica que estás usando datos reales

Abre la consola del navegador y verifica que no hay mensajes de "Usando datos simulados". Deberías ver mensajes como "Usando datos reales a través del servidor MCP".
