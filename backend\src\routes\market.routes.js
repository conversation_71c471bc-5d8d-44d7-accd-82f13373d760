/**
 * Rutas para datos del mercado de criptomonedas
 */
const express = require('express');
const router = express.Router();
const marketDataService = require('../services/market-data.service');

/**
 * @route GET /api/market/data
 * @desc Obtiene datos generales del mercado de criptomonedas
 * @access Public
 */
router.get('/data', async (req, res) => {
  try {
    const marketData = await marketDataService.getMarketData();
    res.json(marketData);
  } catch (error) {
    console.error('Error al obtener datos del mercado:', error.message);
    res.status(500).json({ error: 'Error al obtener datos del mercado' });
  }
});

/**
 * @route GET /api/market/fear-greed
 * @desc Obtiene el índice de miedo y codicia
 * @access Public
 */
router.get('/fear-greed', async (req, res) => {
  try {
    const fearGreedIndex = await marketDataService.getFearGreedIndex();
    res.json(fearGreedIndex);
  } catch (error) {
    console.error('Error al obtener índice de miedo y codicia:', error.message);
    res.status(500).json({ error: 'Error al obtener índice de miedo y codicia' });
  }
});

/**
 * @route GET /api/market/btc-dominance
 * @desc Obtiene la dominancia de Bitcoin en el mercado
 * @access Public
 */
router.get('/btc-dominance', async (req, res) => {
  try {
    const btcDominance = await marketDataService.getBtcDominance();
    res.json({ btcDominance });
  } catch (error) {
    console.error('Error al obtener dominancia de Bitcoin:', error.message);
    res.status(500).json({ error: 'Error al obtener dominancia de Bitcoin' });
  }
});

/**
 * @route GET /api/market/total-cap
 * @desc Obtiene la capitalización total del mercado
 * @access Public
 */
router.get('/total-cap', async (req, res) => {
  try {
    const totalMarketCap = await marketDataService.getTotalMarketCap();
    res.json({ totalMarketCap, totalMarketCapInTrillions: totalMarketCap / 1000000000000 });
  } catch (error) {
    console.error('Error al obtener capitalización total del mercado:', error.message);
    res.status(500).json({ error: 'Error al obtener capitalización total del mercado' });
  }
});

module.exports = router;
