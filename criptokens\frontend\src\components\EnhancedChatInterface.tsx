import { useState, useEffect, useRef } from 'react';
import anime from '../utils/animeUtils';
import { getTopCryptocurrencies, getGlobalMarketData } from '../services/api';
import CriptoAgentAvatar from './CriptoAgentAvatar';
import '../styles/EnhancedChatInterface.css';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'ai';
  isStreaming?: boolean;
  cryptoData?: any; // Datos de criptomonedas para mostrar en el mensaje
  timestamp?: string;
}

const EnhancedChatInterface = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isThinking, setIsThinking] = useState(false);
  const [agentMood, setAgentMood] = useState<'neutral' | 'happy' | 'thinking' | 'excited' | 'concerned'>('neutral');
  const [useStreaming, setUseStreaming] = useState(true);
  const [cryptoData, setCryptoData] = useState<any>(null);
  const [marketData, setMarketData] = useState<any>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const agentRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);

  // Referencia al evento SSE
  const eventSourceRef = useRef<EventSource | null>(null);

  // Inicializar animaciones y cargar datos
  useEffect(() => {
    // Inicializar animación del agente
    if (agentRef.current) {
      anime({
        targets: agentRef.current,
        scale: [0.9, 1],
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 1200,
        easing: 'spring(1, 80, 10, 0)'
      });
    }

    // Crear partículas
    if (particlesRef.current) {
      createParticles();
    }

    // Cargar datos de criptomonedas
    const fetchCryptoData = async () => {
      try {
        // Obtener las principales criptomonedas
        const topCryptos = await getTopCryptocurrencies(10);
        setCryptoData(topCryptos);

        // Obtener datos del mercado global
        const globalData = await getGlobalMarketData();
        setMarketData(globalData.data);

        // Añadir mensaje de bienvenida
        setMessages([
          {
            id: Date.now(),
            text: "¡Hola! Soy Cripto, tu asistente de criptomonedas. ¿En qué puedo ayudarte hoy?",
            sender: 'ai',
            timestamp: new Date().toLocaleTimeString()
          }
        ]);
      } catch (error) {
        console.error('Error al cargar datos de criptomonedas:', error);
      }
    };

    fetchCryptoData();

    // Limpiar la conexión SSE cuando el componente se desmonte
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  // Scroll al final de los mensajes cuando se añaden nuevos
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });

      // Animar nuevo mensaje
      if (messages.length > 0) {
        anime({
          targets: '.message:last-child',
          translateY: [20, 0],
          opacity: [0, 1],
          duration: 800,
          easing: 'spring(1, 80, 10, 0)'
        });
      }
    }
  }, [messages]);

  // Crear partículas
  const createParticles = () => {
    if (!particlesRef.current) return;

    const particlesContainer = particlesRef.current;
    const particleCount = 40;

    // Limpiar partículas existentes
    particlesContainer.innerHTML = '';

    // Crear nuevas partículas
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.classList.add('particle');

      // Posición aleatoria
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;

      // Tamaño aleatorio
      const size = Math.random() * 6 + 2;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // Opacidad aleatoria
      particle.style.opacity = (Math.random() * 0.5 + 0.1).toString();

      particlesContainer.appendChild(particle);
    }

    // Animar partículas
    anime({
      targets: '.particle',
      translateX: () => anime.random(-50, 50),
      translateY: () => anime.random(-50, 50),
      opacity: () => Math.random() * 0.5 + 0.1,
      scale: () => anime.random(0.5, 2),
      easing: 'easeInOutQuad',
      duration: () => anime.random(1000, 3000),
      complete: (anim) => {
        anim.restart();
      },
      loop: true
    });
  };

  // Función para manejar respuestas en streaming
  const handleStreamingResponse = (messageText: string, commandData: any = null) => {
    // Crear un ID único para el mensaje de IA
    const aiMessageId = Date.now() + 1;
    const timestamp = new Date().toLocaleTimeString();

    // Inicializar el mensaje de IA con texto vacío
    setMessages(prevMessages => [
      ...prevMessages,
      {
        id: aiMessageId,
        text: '',
        sender: 'ai',
        isStreaming: true,
        cryptoData: commandData,
        timestamp
      }
    ]);

    // Animar agente "pensando"
    setIsThinking(true);

    // Animación de pulso para el agente mientras piensa
    const thinkingAnimation = anime({
      targets: agentRef.current,
      scale: [1, 1.05, 1],
      duration: 1200,
      easing: 'easeInOutQuad',
      loop: true
    });

    // Configurar la conexión SSE
    const eventSource = new EventSource(`http://localhost:3001/api/chat/stream?message=${encodeURIComponent(messageText)}`);
    eventSourceRef.current = eventSource;

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.content) {
        // Actualizar el mensaje existente con el nuevo contenido
        setMessages(prevMessages => {
          return prevMessages.map(msg => {
            if (msg.id === aiMessageId) {
              return {
                ...msg,
                text: msg.text + data.content
              };
            }
            return msg;
          });
        });
      }

      // Si es el final del stream, marcar como completado
      if (data.done) {
        setMessages(prevMessages => {
          return prevMessages.map(msg => {
            if (msg.id === aiMessageId) {
              return {
                ...msg,
                isStreaming: false
              };
            }
            return msg;
          });
        });
        eventSource.close();
        eventSourceRef.current = null;
        setIsLoading(false);
        setIsThinking(false);
        thinkingAnimation.pause();

        // Restaurar el agente a normal
        anime({
          targets: agentRef.current,
          scale: 1,
          duration: 400,
          easing: 'easeOutQuad'
        });
      }
    };

    eventSource.onerror = (error) => {
      console.error('Error en la conexión SSE:', error);
      eventSource.close();
      eventSourceRef.current = null;
      setIsLoading(false);
      setIsThinking(false);
      thinkingAnimation.pause();

      // Restaurar el agente a normal
      anime({
        targets: agentRef.current,
        scale: 1,
        duration: 400,
        easing: 'easeOutQuad'
      });

      // Actualizar el mensaje para indicar que hubo un error
      setMessages(prevMessages => {
        return prevMessages.map(msg => {
          if (msg.id === aiMessageId) {
            return {
              ...msg,
              text: msg.text + '\n[Error de conexión. Por favor, intenta de nuevo.]',
              isStreaming: false
            };
          }
          return msg;
        });
      });
    };
  };

  // Función para manejar respuestas estándar (no streaming)
  const handleStandardResponse = async (messageText: string, commandData: any = null) => {
    try {
      // Animar agente "pensando"
      setIsThinking(true);

      // Animación de pulso para el agente mientras piensa
      const thinkingAnimation = anime({
        targets: agentRef.current,
        scale: [1, 1.05, 1],
        duration: 1200,
        easing: 'easeInOutQuad',
        loop: true
      });

      // Llamada a la API del backend
      const response = await fetch('http://localhost:3001/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: messageText }),
      });

      const data = await response.json();

      // Añadir respuesta del AI a la conversación
      const aiMessage: Message = {
        id: Date.now() + 1,
        text: data.reply,
        sender: 'ai',
        cryptoData: commandData,
        timestamp: new Date().toLocaleTimeString()
      };

      setMessages((prevMessages) => [...prevMessages, aiMessage]);
    } catch (error) {
      console.error('Error al enviar mensaje:', error);
      // Mostrar mensaje de error en la UI
      setMessages(prevMessages => [
        ...prevMessages,
        {
          id: Date.now() + 1,
          text: 'Lo siento, ha ocurrido un error al procesar tu mensaje. Por favor, intenta de nuevo.',
          sender: 'ai',
          timestamp: new Date().toLocaleTimeString()
        }
      ]);
    } finally {
      setIsLoading(false);
      setIsThinking(false);

      // Restaurar el agente a normal
      anime({
        targets: agentRef.current,
        scale: 1,
        duration: 400,
        easing: 'easeOutQuad'
      });
    }
  };

  // Función para procesar comandos especiales
  const processSpecialCommands = (message: string): { processedMessage: string, cryptoData: any | null } => {
    // Comando para obtener precio de una criptomoneda
    const priceRegex = /precio\s+de\s+(bitcoin|ethereum|ripple|cardano|solana|\w+)/i;
    const priceMatch = message.match(priceRegex);

    if (priceMatch && cryptoData) {
      const cryptoName = priceMatch[1].toLowerCase();
      const crypto = cryptoData.find((c: any) =>
        c.id.toLowerCase() === cryptoName ||
        c.name.toLowerCase() === cryptoName ||
        c.symbol.toLowerCase() === cryptoName
      );

      if (crypto) {
        return {
          processedMessage: message,
          cryptoData: {
            type: 'price',
            data: crypto
          }
        };
      }
    }

    // Comando para obtener resumen del mercado
    const marketRegex = /(resumen|estado)\s+(del|de)\s+(mercado|criptomonedas)/i;
    const marketMatch = message.match(marketRegex);

    if (marketMatch && marketData) {
      return {
        processedMessage: message,
        cryptoData: {
          type: 'market',
          data: marketData
        }
      };
    }

    // Comando para listar principales criptomonedas
    const listRegex = /(listar|mostrar|cuáles son)\s+(las\s+)?(principales|top)\s+criptomonedas/i;
    const listMatch = message.match(listRegex);

    if (listMatch && cryptoData) {
      return {
        processedMessage: message,
        cryptoData: {
          type: 'list',
          data: cryptoData.slice(0, 5)
        }
      };
    }

    return { processedMessage: message, cryptoData: null };
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    // Procesar comandos especiales
    const { processedMessage, cryptoData: commandData } = processSpecialCommands(inputMessage);

    // Añadir mensaje del usuario a la conversación
    const userMessage: Message = {
      id: Date.now(),
      text: processedMessage,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // Cerrar cualquier conexión SSE existente
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    // Usar streaming o respuesta estándar según la configuración
    if (useStreaming) {
      handleStreamingResponse(processedMessage, commandData);
    } else {
      await handleStandardResponse(processedMessage, commandData);
    }
  };

  // Función para manejar la tecla Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="enhanced-chat-container">
      <div ref={particlesRef} className="particles-container"></div>

      <div className="agent-section" ref={agentRef}>
        <CriptoAgentAvatar
          mood={isThinking ? 'thinking' : agentMood}
          speaking={isThinking}
          size="large"
          pulseEffect={true}
        />
        {isThinking && <div className="thinking-indicator">Procesando...</div>}
      </div>

      <div className="conversation-section">
        <div className="messages-container">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`message ${message.sender === 'user' ? 'user-message' : 'ai-message'} ${message.isStreaming ? 'streaming' : ''}`}
            >
              <div className="message-content">
                <p>{message.text || (message.isStreaming ? '...' : '')}</p>
                {message.cryptoData && (
                  <div className="crypto-data-container">
                    {message.cryptoData.type === 'price' && (
                      <div className="crypto-price-card">
                        <div className="crypto-price-header">
                          <img src={message.cryptoData.data.image} alt={message.cryptoData.data.name} className="crypto-icon" />
                          <h4>{message.cryptoData.data.name} ({message.cryptoData.data.symbol.toUpperCase()})</h4>
                        </div>
                        <div className="crypto-price-details">
                          <div className="price-item">
                            <span className="price-label">Precio:</span>
                            <span className="price-value">${message.cryptoData.data.current_price.toLocaleString()}</span>
                          </div>
                          <div className="price-item">
                            <span className="price-label">Cambio 24h:</span>
                            <span className={`price-value ${message.cryptoData.data.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                              {message.cryptoData.data.price_change_percentage_24h >= 0 ? '▲' : '▼'}
                              {Math.abs(message.cryptoData.data.price_change_percentage_24h).toFixed(2)}%
                            </span>
                          </div>
                          <div className="price-item">
                            <span className="price-label">Cap. Mercado:</span>
                            <span className="price-value">${message.cryptoData.data.market_cap.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {message.cryptoData.type === 'market' && (
                      <div className="market-summary-card">
                        <h4>Resumen del Mercado</h4>
                        <div className="market-summary-details">
                          <div className="market-item">
                            <span className="market-label">Cap. Total:</span>
                            <span className="market-value">
                              ${(message.cryptoData.data.total_market_cap.usd / 1e12).toFixed(2)}T
                            </span>
                          </div>
                          <div className="market-item">
                            <span className="market-label">Cambio 24h:</span>
                            <span className={`market-value ${message.cryptoData.data.market_cap_change_percentage_24h_usd >= 0 ? 'positive' : 'negative'}`}>
                              {message.cryptoData.data.market_cap_change_percentage_24h_usd >= 0 ? '▲' : '▼'}
                              {Math.abs(message.cryptoData.data.market_cap_change_percentage_24h_usd).toFixed(2)}%
                            </span>
                          </div>
                          <div className="market-item">
                            <span className="market-label">Dominio BTC:</span>
                            <span className="market-value">
                              {message.cryptoData.data.market_cap_percentage.btc.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {message.cryptoData.type === 'list' && (
                      <div className="crypto-list-card">
                        <h4>Principales Criptomonedas</h4>
                        <div className="crypto-list">
                          {message.cryptoData.data.map((crypto: any, index: number) => (
                            <div key={crypto.id} className="crypto-list-item">
                              <div className="crypto-rank">{index + 1}</div>
                              <img src={crypto.image} alt={crypto.name} className="crypto-icon" />
                              <div className="crypto-name">
                                <span>{crypto.name}</span>
                                <span className="crypto-symbol">{crypto.symbol.toUpperCase()}</span>
                              </div>
                              <div className="crypto-price">${crypto.current_price.toLocaleString()}</div>
                              <div className={`crypto-change ${crypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                                {crypto.price_change_percentage_24h >= 0 ? '▲' : '▼'}
                                {Math.abs(crypto.price_change_percentage_24h).toFixed(2)}%
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="message-time">{message.timestamp}</div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        <div className="chat-options">
          <label className="streaming-toggle">
            <input
              type="checkbox"
              checked={useStreaming}
              onChange={() => setUseStreaming(!useStreaming)}
            />
            Modo streaming {useStreaming ? 'activado' : 'desactivado'}
          </label>
        </div>

        <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }} className="input-container">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Pregunta sobre criptomonedas, blockchain o cualquier duda..."
            className="message-input"
            disabled={isLoading}
          />
          <button
            type="submit"
            className="send-button"
            disabled={isLoading || !inputMessage.trim()}
          >
            <span className="send-icon">➤</span>
          </button>
        </form>
      </div>
    </div>
  );
};

export default EnhancedChatInterface;
