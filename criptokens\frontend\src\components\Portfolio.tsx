import { useState, useEffect } from 'react';
import { 
  getPortfolio, 
  updatePortfolioPrices, 
  addAssetToPortfolio, 
  removeAssetFromPortfolio,
  type Portfolio as PortfolioType,
  type PortfolioAsset
} from '../services/portfolio';
import { getTopCryptocurrencies } from '../services/api';
import '../styles/Portfolio.css';

const Portfolio = () => {
  const [portfolio, setPortfolio] = useState<PortfolioType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddAssetForm, setShowAddAssetForm] = useState(false);
  const [availableCryptos, setAvailableCryptos] = useState<any[]>([]);
  
  // Form state
  const [selectedCryptoId, setSelectedCryptoId] = useState('');
  const [amount, setAmount] = useState('');
  const [purchasePrice, setPurchasePrice] = useState('');
  const [formError, setFormError] = useState<string | null>(null);

  // Cargar la cartera al montar el componente
  useEffect(() => {
    const loadPortfolio = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Obtener la cartera guardada
        const savedPortfolio = getPortfolio();
        
        // Actualizar los precios si hay activos en la cartera
        if (savedPortfolio.assets.length > 0) {
          const updatedPortfolio = await updatePortfolioPrices();
          setPortfolio(updatedPortfolio);
        } else {
          setPortfolio(savedPortfolio);
        }
        
        // Cargar las criptomonedas disponibles para añadir
        const cryptos = await getTopCryptocurrencies(50);
        setAvailableCryptos(cryptos);
      } catch (err) {
        console.error('Error al cargar la cartera:', err);
        setError('No se pudo cargar la cartera. Por favor, intenta de nuevo más tarde.');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPortfolio();
  }, []);
  
  // Función para manejar la adición de un nuevo activo
  const handleAddAsset = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    
    // Validar el formulario
    if (!selectedCryptoId) {
      setFormError('Por favor, selecciona una criptomoneda.');
      return;
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      setFormError('Por favor, ingresa una cantidad válida.');
      return;
    }
    
    if (!purchasePrice || parseFloat(purchasePrice) <= 0) {
      setFormError('Por favor, ingresa un precio de compra válido.');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Añadir el activo a la cartera
      const updatedPortfolio = await addAssetToPortfolio(
        selectedCryptoId,
        parseFloat(amount),
        parseFloat(purchasePrice)
      );
      
      setPortfolio(updatedPortfolio);
      
      // Resetear el formulario
      setSelectedCryptoId('');
      setAmount('');
      setPurchasePrice('');
      setShowAddAssetForm(false);
    } catch (err) {
      console.error('Error al añadir activo:', err);
      setFormError('No se pudo añadir el activo. Por favor, intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Función para manejar la eliminación de un activo
  const handleRemoveAsset = (id: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar este activo de tu cartera?')) {
      try {
        const updatedPortfolio = removeAssetFromPortfolio(id);
        setPortfolio(updatedPortfolio);
      } catch (err) {
        console.error('Error al eliminar activo:', err);
        setError('No se pudo eliminar el activo. Por favor, intenta de nuevo.');
      }
    }
  };
  
  // Función para actualizar los precios de la cartera
  const handleRefreshPrices = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedPortfolio = await updatePortfolioPrices();
      setPortfolio(updatedPortfolio);
    } catch (err) {
      console.error('Error al actualizar precios:', err);
      setError('No se pudieron actualizar los precios. Por favor, intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Formatear números para mostrar
  const formatNumber = (num: number, decimals = 2) => {
    return num.toLocaleString(undefined, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  };
  
  // Formatear fecha para mostrar
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  if (isLoading && !portfolio) {
    return <div className="portfolio-loading">Cargando cartera...</div>;
  }
  
  if (error && !portfolio) {
    return <div className="portfolio-error">{error}</div>;
  }
  
  return (
    <div className="portfolio-container">
      <div className="portfolio-header">
        <h2>Mi Cartera</h2>
        <div className="portfolio-actions">
          <button 
            className="refresh-button" 
            onClick={handleRefreshPrices}
            disabled={isLoading || !portfolio?.assets.length}
          >
            Actualizar Precios
          </button>
          <button 
            className="add-asset-button" 
            onClick={() => setShowAddAssetForm(!showAddAssetForm)}
          >
            {showAddAssetForm ? 'Cancelar' : 'Añadir Activo'}
          </button>
        </div>
      </div>
      
      {showAddAssetForm && (
        <div className="add-asset-form-container">
          <form className="add-asset-form" onSubmit={handleAddAsset}>
            <h3>Añadir Nuevo Activo</h3>
            
            {formError && <p className="form-error">{formError}</p>}
            
            <div className="form-group">
              <label htmlFor="crypto-select">Criptomoneda</label>
              <select 
                id="crypto-select"
                value={selectedCryptoId}
                onChange={(e) => setSelectedCryptoId(e.target.value)}
                required
              >
                <option value="">Selecciona una criptomoneda</option>
                {availableCryptos.map((crypto) => (
                  <option key={crypto.id} value={crypto.id}>
                    {crypto.name} ({crypto.symbol.toUpperCase()})
                  </option>
                ))}
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="amount-input">Cantidad</label>
              <input 
                id="amount-input"
                type="number"
                min="0.00000001"
                step="0.00000001"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Ej: 0.5"
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="price-input">Precio de Compra (USD)</label>
              <input 
                id="price-input"
                type="number"
                min="0.00000001"
                step="0.00000001"
                value={purchasePrice}
                onChange={(e) => setPurchasePrice(e.target.value)}
                placeholder="Ej: 50000"
                required
              />
            </div>
            
            <div className="form-actions">
              <button 
                type="button" 
                className="cancel-button"
                onClick={() => setShowAddAssetForm(false)}
              >
                Cancelar
              </button>
              <button 
                type="submit" 
                className="submit-button"
                disabled={isLoading}
              >
                Añadir
              </button>
            </div>
          </form>
        </div>
      )}
      
      {portfolio && (
        <div className="portfolio-summary">
          <div className="summary-item">
            <span className="summary-label">Valor Total:</span>
            <span className="summary-value">${formatNumber(portfolio.totalValue)}</span>
          </div>
          
          <div className="summary-item">
            <span className="summary-label">Ganancia/Pérdida:</span>
            <span className={`summary-value ${portfolio.totalProfitLoss >= 0 ? 'positive' : 'negative'}`}>
              ${formatNumber(portfolio.totalProfitLoss)} 
              ({formatNumber(portfolio.totalProfitLossPercentage)}%)
            </span>
          </div>
          
          <div className="summary-item">
            <span className="summary-label">Última Actualización:</span>
            <span className="summary-value date">
              {formatDate(portfolio.lastUpdated)}
            </span>
          </div>
        </div>
      )}
      
      {portfolio && portfolio.assets.length > 0 ? (
        <div className="portfolio-assets">
          <table className="assets-table">
            <thead>
              <tr>
                <th>Activo</th>
                <th>Cantidad</th>
                <th>Precio de Compra</th>
                <th>Precio Actual</th>
                <th>Valor</th>
                <th>Ganancia/Pérdida</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              {portfolio.assets.map((asset) => (
                <tr key={asset.id}>
                  <td className="asset-info">
                    <img src={asset.image} alt={asset.name} className="asset-icon" />
                    <div>
                      <span className="asset-name">{asset.name}</span>
                      <span className="asset-symbol">{asset.symbol}</span>
                    </div>
                  </td>
                  <td>{formatNumber(asset.amount, 8)}</td>
                  <td>${formatNumber(asset.purchasePrice)}</td>
                  <td>${formatNumber(asset.currentPrice)}</td>
                  <td>${formatNumber(asset.value)}</td>
                  <td className={asset.profitLoss >= 0 ? 'positive' : 'negative'}>
                    ${formatNumber(asset.profitLoss)} 
                    ({formatNumber(asset.profitLossPercentage)}%)
                  </td>
                  <td>
                    <button 
                      className="remove-asset-button"
                      onClick={() => handleRemoveAsset(asset.id)}
                    >
                      Eliminar
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="empty-portfolio">
          <p>No tienes activos en tu cartera.</p>
          <p>Haz clic en "Añadir Activo" para comenzar a construir tu cartera.</p>
        </div>
      )}
    </div>
  );
};

export default Portfolio;
