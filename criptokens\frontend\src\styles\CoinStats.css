/* Estilos para las estadísticas de la criptomoneda */

.coin-stats-container {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.coin-stats-container h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.stat-item {
  padding: 1rem;
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background-color: var(--color-surface-light);
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.stat-label i {
  color: var(--color-primary);
  font-size: 0.875rem;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.stat-change.positive {
  color: var(--color-positive);
}

.stat-change.negative {
  color: var(--color-negative);
}

.stat-date {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.supply-progress {
  margin-top: 0.5rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: var(--color-surface-light);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  margin-top: 0.25rem;
}

.additional-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.additional-info h4 {
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--text-primary);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.info-value {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.info-link {
  color: var(--color-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.info-link:hover {
  text-decoration: underline;
}

.info-link i {
  font-size: 0.75rem;
}

.info-social {
  display: flex;
  gap: 0.75rem;
}

.social-link {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-surface-dark);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.social-link:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
  border-color: var(--border-color-hover);
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.category-tag {
  padding: 0.25rem 0.5rem;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .stats-grid, .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .coin-stats-container {
    padding: 0.75rem;
  }
  
  .stat-item {
    padding: 0.75rem;
  }
}
