"""
OnChain MCP Tool for ADK Agents

This module provides a tool for ADK agents to interact with the OnChain MCP server.
"""
import os
from typing import Dict, Any, List, Optional
from .base_mcp_tool import BaseMcpTool

class OnChainMcpTool(BaseMcpTool):
    """Tool for interacting with the OnChain MCP server."""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the OnChain MCP tool.
        
        Args:
            base_url: Base URL of the OnChain MCP server (optional)
        """
        super().__init__("onchain", base_url)
    
    async def get_wallet_balance(self, address: str, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get the balance of a wallet address.
        
        Args:
            address: The wallet address
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Wallet balance information
        """
        return await self.execute_tool("getWalletBalance", {
            "address": address,
            "chain": chain
        })
    
    async def get_token_balance(self, address: str, token_address: str, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get the token balance of a wallet address.
        
        Args:
            address: The wallet address
            token_address: The token contract address
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Token balance information
        """
        return await self.execute_tool("getTokenBalance", {
            "address": address,
            "tokenAddress": token_address,
            "chain": chain
        })
    
    async def get_crypto_balance(self, address: str, crypto_id: str, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get the balance of a cryptocurrency by name.
        
        Args:
            address: The wallet address
            crypto_id: The cryptocurrency ID (bitcoin, ethereum, etc.)
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Cryptocurrency balance information
        """
        return await self.execute_tool("getCryptoBalance", {
            "address": address,
            "cryptoId": crypto_id,
            "chain": chain
        })
    
    async def get_portfolio(self, address: str, crypto_ids: Optional[List[str]] = None, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get portfolio of cryptocurrencies for a wallet address.
        
        Args:
            address: The wallet address
            crypto_ids: List of cryptocurrency IDs (bitcoin, ethereum, etc.)
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Portfolio information
        """
        params = {
            "address": address,
            "chain": chain
        }
        
        if crypto_ids:
            params["cryptoIds"] = crypto_ids
        
        return await self.execute_tool("getPortfolio", params)
    
    async def get_transactions(self, address: str, page: int = 1, offset: int = 10, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get transactions for a wallet address.
        
        Args:
            address: The wallet address
            page: Page number
            offset: Number of transactions per page
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Transaction history
        """
        return await self.execute_tool("getTransactions", {
            "address": address,
            "page": page,
            "offset": offset,
            "chain": chain
        })
    
    async def get_token_transfers(self, address: str, token_address: Optional[str] = None, page: int = 1, offset: int = 10, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get token transfers for a wallet address.
        
        Args:
            address: The wallet address
            token_address: The token contract address (optional)
            page: Page number
            offset: Number of transfers per page
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Token transfer history
        """
        params = {
            "address": address,
            "page": page,
            "offset": offset,
            "chain": chain
        }
        
        if token_address:
            params["tokenAddress"] = token_address
        
        return await self.execute_tool("getTokenTransfers", params)
    
    async def get_crypto_transfers(self, address: str, crypto_id: str, page: int = 1, offset: int = 10, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get transfers of a specific cryptocurrency.
        
        Args:
            address: The wallet address
            crypto_id: The cryptocurrency ID (bitcoin, ethereum, etc.)
            page: Page number
            offset: Number of transfers per page
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Cryptocurrency transfer history
        """
        return await self.execute_tool("getCryptoTransfers", {
            "address": address,
            "cryptoId": crypto_id,
            "page": page,
            "offset": offset,
            "chain": chain
        })
    
    async def analyze_transactions(self, address: str, days: int = 30, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Analyze recent transactions of a wallet.
        
        Args:
            address: The wallet address
            days: Number of days to analyze
            chain: The blockchain (ethereum, binance, solana)
            
        Returns:
            Transaction analysis
        """
        return await self.execute_tool("analyzeTransactions", {
            "address": address,
            "days": days,
            "chain": chain
        })
    
    async def get_gas_price(self, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get current gas prices.
        
        Args:
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Gas price information
        """
        return await self.execute_tool("getGasPrice", {
            "chain": chain
        })
    
    async def get_token_whales(self, token_address: str, limit: int = 10, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get information about token whales (large holders).
        
        Args:
            token_address: The token contract address
            limit: Maximum number of whales to return
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Token whale information
        """
        return await self.execute_tool("getTokenWhales", {
            "tokenAddress": token_address,
            "limit": limit,
            "chain": chain
        })
    
    async def get_crypto_whales(self, crypto_id: str, limit: int = 10, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get information about cryptocurrency whales (large holders).
        
        Args:
            crypto_id: The cryptocurrency ID (bitcoin, ethereum, etc.)
            limit: Maximum number of whales to return
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Cryptocurrency whale information
        """
        return await self.execute_tool("getCryptoWhales", {
            "cryptoId": crypto_id,
            "limit": limit,
            "chain": chain
        })
    
    async def analyze_whale_activity(self, crypto_id: str, days: int = 7, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Analyze whale activity for a cryptocurrency.
        
        Args:
            crypto_id: The cryptocurrency ID (bitcoin, ethereum, etc.)
            days: Number of days to analyze
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Whale activity analysis
        """
        return await self.execute_tool("analyzeWhaleActivity", {
            "cryptoId": crypto_id,
            "days": days,
            "chain": chain
        })
    
    async def get_token_info(self, token_address: str, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get information about an ERC-20 token.
        
        Args:
            token_address: The token contract address
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Token information
        """
        return await self.execute_tool("getTokenInfo", {
            "tokenAddress": token_address,
            "chain": chain
        })
    
    async def get_crypto_token_info(self, crypto_id: str, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Get information about a cryptocurrency token by name.
        
        Args:
            crypto_id: The cryptocurrency ID (bitcoin, ethereum, etc.)
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Cryptocurrency token information
        """
        return await self.execute_tool("getCryptoTokenInfo", {
            "cryptoId": crypto_id,
            "chain": chain
        })
    
    async def analyze_token_activity(self, token_address: str, days: int = 7, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Analyze activity of a token.
        
        Args:
            token_address: The token contract address
            days: Number of days to analyze
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Token activity analysis
        """
        return await self.execute_tool("analyzeTokenActivity", {
            "tokenAddress": token_address,
            "days": days,
            "chain": chain
        })
    
    async def analyze_crypto_activity(self, crypto_id: str, days: int = 7, chain: str = "ethereum") -> Dict[str, Any]:
        """
        Analyze activity of a cryptocurrency token by name.
        
        Args:
            crypto_id: The cryptocurrency ID (bitcoin, ethereum, etc.)
            days: Number of days to analyze
            chain: The blockchain (ethereum, binance)
            
        Returns:
            Cryptocurrency activity analysis
        """
        return await self.execute_tool("analyzeCryptoActivity", {
            "cryptoId": crypto_id,
            "days": days,
            "chain": chain
        })
