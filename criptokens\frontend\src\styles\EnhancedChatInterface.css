.enhanced-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: rgba(15, 15, 45, 0.3);
  border-radius: 20px;
}

/* Particles */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(64, 220, 255, 0.5);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.8);
  pointer-events: none;
}

/* Agent Section */
.agent-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180px;
  position: relative;
  z-index: 2;
  padding: 20px;
}

.agent-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.agent-core {
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, #00f2ff 0%, #4657ce 100%);
  border-radius: 50%;
  box-shadow: 0 0 30px rgba(0, 242, 255, 0.8);
  position: relative;
  z-index: 3;
}

.agent-ring {
  position: absolute;
  border-radius: 50%;
  border: 2px solid rgba(64, 220, 255, 0.5);
  animation: pulse 3s infinite;
}

.ring1 {
  width: 60px;
  height: 60px;
  animation-delay: 0s;
}

.ring2 {
  width: 80px;
  height: 80px;
  animation-delay: 0.5s;
}

.ring3 {
  width: 100px;
  height: 100px;
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.thinking-indicator {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #00f2ff;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 14px;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Conversation Section */
.conversation-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(15, 15, 45, 0.7);
  border-radius: 20px 20px 0 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 -10px 20px rgba(0, 0, 0, 0.2);
  z-index: 2;
  overflow: hidden;
  border-top: 1px solid rgba(64, 220, 255, 0.3);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  scrollbar-width: thin;
  scrollbar-color: rgba(64, 220, 255, 0.5) rgba(15, 15, 45, 0.3);
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(15, 15, 45, 0.3);
}

.messages-container::-webkit-scrollbar-thumb {
  background-color: rgba(64, 220, 255, 0.5);
  border-radius: 6px;
}

.message {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
}

.user-message {
  align-self: flex-end;
  background: linear-gradient(135deg, #4657ce 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.ai-message {
  align-self: flex-start;
  background: rgba(30, 30, 60, 0.8);
  color: #e0e0ff;
  border-bottom-left-radius: 4px;
  border-left: 3px solid #00f2ff;
}

.message-content {
  font-size: 16px;
  line-height: 1.4;
}

.message-content p {
  margin: 0;
  white-space: pre-wrap;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 5px;
  text-align: right;
}

.streaming::after {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #00f2ff;
  border-radius: 50%;
  margin-left: 5px;
  animation: blink 1s infinite;
}

/* Crypto Data Cards */
.crypto-data-container {
  margin-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 15px;
}

.crypto-price-card, .market-summary-card, .crypto-list-card {
  background: rgba(20, 20, 50, 0.7);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(64, 220, 255, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.crypto-price-header, .market-summary-card h4, .crypto-list-card h4 {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.crypto-icon {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  margin-right: 10px;
  border-radius: 50%;
  vertical-align: middle !important;
  object-fit: contain !important;
}

.crypto-price-details, .market-summary-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-item, .market-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.price-label, .market-label {
  color: #a0a0d0;
}

.positive {
  color: #4ade80;
}

.negative {
  color: #f87171;
}

.crypto-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.crypto-list-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.crypto-rank {
  width: 24px;
  text-align: center;
  font-weight: 600;
  color: #a0a0d0;
}

.crypto-name {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.crypto-symbol {
  font-size: 12px;
  color: #a0a0d0;
}

.crypto-price {
  margin-right: 15px;
  font-weight: 600;
}

.crypto-change {
  width: 70px;
  text-align: right;
  font-weight: 600;
}

/* Chat Options */
.chat-options {
  padding: 10px 20px;
  border-top: 1px solid rgba(64, 220, 255, 0.2);
  background: rgba(20, 20, 50, 0.5);
}

.streaming-toggle {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #a0a0d0;
  cursor: pointer;
}

.streaming-toggle input {
  margin-right: 8px;
}

/* Input Container */
.input-container {
  display: flex;
  padding: 15px 20px;
  background: rgba(20, 20, 50, 0.9);
  border-top: 1px solid rgba(64, 220, 255, 0.2);
}

.message-input {
  flex: 1;
  background: rgba(30, 30, 60, 0.6);
  border: 1px solid rgba(64, 220, 255, 0.3);
  border-radius: 24px;
  padding: 12px 20px;
  color: white;
  font-size: 16px;
  outline: none;
  transition: all 0.3s;
}

.message-input:focus {
  border-color: rgba(64, 220, 255, 0.8);
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.3);
}

.message-input::placeholder {
  color: rgba(200, 200, 255, 0.5);
}

.send-button {
  width: 46px;
  height: 46px;
  margin-left: 10px;
  background: linear-gradient(135deg, #4657ce 0%, #00f2ff 100%);
  border: none;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.5);
}

.send-button:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(64, 220, 255, 0.8);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: scale(1);
  box-shadow: none;
}

.send-icon {
  color: white;
  font-size: 16px;
  transform: translateX(1px);
}

/* Animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .agent-section {
    height: 150px;
  }

  .agent-avatar {
    width: 100px;
    height: 100px;
  }

  .agent-core {
    width: 30px;
    height: 30px;
  }

  .ring1 {
    width: 50px;
    height: 50px;
  }

  .ring2 {
    width: 70px;
    height: 70px;
  }

  .ring3 {
    width: 90px;
    height: 90px;
  }

  .message {
    max-width: 90%;
  }
}
