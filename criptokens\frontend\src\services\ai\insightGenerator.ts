import { 
  AIInsight, 
  PortfolioStats, 
  GlobalMarketData, 
  SentimentAnalysis,
  RiskProfile
} from '../../types/dashboard';

/**
 * Genera insights personalizados basados en el portafolio, datos de mercado y preferencias del usuario
 */
export const generateInsights = (
  portfolioStats: PortfolioStats,
  marketData: GlobalMarketData | null,
  sentimentAnalysis: SentimentAnalysis | null,
  userRiskProfile: RiskProfile,
  userAssets: any[],
  watchlistAssets: any[]
): AIInsight[] => {
  const insights: AIInsight[] = [];
  const now = new Date();

  // Solo proceder si tenemos datos suficientes
  if (!portfolioStats || !marketData) {
    return insights;
  }

  // 1. Insight sobre rendimiento del portafolio vs mercado
  if (portfolioStats.totalValue > 0) {
    const marketPerformance = marketData.market_cap_change_percentage_24h_usd || 0;
    const portfolioPerformance = portfolioStats.totalProfitLossPercentage;
    const performanceDiff = portfolioPerformance - marketPerformance;

    if (Math.abs(performanceDiff) > 3) {
      insights.push({
        id: `portfolio-performance-${now.getTime()}`,
        type: performanceDiff > 0 ? 'success' : 'warning',
        title: performanceDiff > 0 
          ? 'Tu portafolio supera al mercado' 
          : 'Tu portafolio está por debajo del mercado',
        description: performanceDiff > 0
          ? `Tu portafolio está rindiendo un ${portfolioPerformance.toFixed(2)}%, superando al mercado general (${marketPerformance.toFixed(2)}%) por ${Math.abs(performanceDiff).toFixed(2)}%.`
          : `Tu portafolio está rindiendo un ${portfolioPerformance.toFixed(2)}%, por debajo del mercado general (${marketPerformance.toFixed(2)}%) por ${Math.abs(performanceDiff).toFixed(2)}%.`,
        action: performanceDiff > 0 ? 'Ver portafolio' : 'Analizar con Guru',
        actionPath: performanceDiff > 0 ? '/portfolio' : '/guru',
        relevanceScore: Math.min(10, 5 + Math.abs(performanceDiff) / 2),
        timestamp: now,
        source: 'portfolio'
      });
    }
  }

  // 2. Insight sobre diversificación del portafolio
  if (portfolioStats.assetCount > 0) {
    const diversificationScore = calculateDiversificationScore(portfolioStats);
    
    if (diversificationScore < 5 && portfolioStats.assetCount >= 3) {
      insights.push({
        id: `portfolio-diversification-${now.getTime()}`,
        type: 'warning',
        title: 'Portafolio poco diversificado',
        description: `Tu portafolio está concentrado en pocas categorías (${diversificationScore.toFixed(1)}/10). Considera diversificar para reducir el riesgo.`,
        action: 'Ver recomendaciones',
        actionPath: '/guru',
        relevanceScore: 8,
        timestamp: now,
        source: 'portfolio'
      });
    } else if (diversificationScore > 8) {
      insights.push({
        id: `portfolio-diversification-${now.getTime()}`,
        type: 'success',
        title: 'Portafolio bien diversificado',
        description: `Tu portafolio tiene una buena diversificación (${diversificationScore.toFixed(1)}/10), lo que puede ayudar a reducir el riesgo.`,
        action: 'Ver portafolio',
        actionPath: '/portfolio',
        relevanceScore: 6,
        timestamp: now,
        source: 'portfolio'
      });
    }
  }

  // 3. Insight basado en el sentimiento del mercado y perfil de riesgo
  if (sentimentAnalysis) {
    const { overallSentiment, fearGreedIndex } = sentimentAnalysis;
    
    // Para inversores conservadores
    if (userRiskProfile === 'conservative' && (overallSentiment === 'greed' || overallSentiment === 'extreme_greed')) {
      insights.push({
        id: `market-sentiment-conservative-${now.getTime()}`,
        type: 'caution',
        title: 'Considerar tomar ganancias',
        description: `El sentimiento del mercado es de codicia (${fearGreedIndex}/100). Con tu perfil conservador, podría ser un buen momento para asegurar algunas ganancias.`,
        action: 'Analizar portafolio',
        actionPath: '/portfolio',
        relevanceScore: 9,
        timestamp: now,
        source: 'market'
      });
    }
    
    // Para inversores agresivos
    if (userRiskProfile === 'aggressive' && (overallSentiment === 'fear' || overallSentiment === 'extreme_fear')) {
      insights.push({
        id: `market-sentiment-aggressive-${now.getTime()}`,
        type: 'opportunity',
        title: 'Posible oportunidad de compra',
        description: `El sentimiento del mercado es de miedo (${fearGreedIndex}/100). Con tu perfil agresivo, podrías considerar esta como una oportunidad de compra.`,
        action: 'Explorar oportunidades',
        actionPath: '/guru',
        relevanceScore: 9,
        timestamp: now,
        source: 'market'
      });
    }
  }

  // 4. Insight sobre activos en watchlist con buen rendimiento
  if (watchlistAssets && watchlistAssets.length > 0) {
    const topPerformers = watchlistAssets
      .filter(crypto => crypto.priceChangePercentage24h > 8)
      .sort((a, b) => b.priceChangePercentage24h - a.priceChangePercentage24h)
      .slice(0, 3);
    
    if (topPerformers.length > 0) {
      const assetIds = topPerformers.map(asset => asset.id);
      insights.push({
        id: `watchlist-performers-${now.getTime()}`,
        type: 'info',
        title: 'Monedas en tu radar con buen rendimiento',
        description: `${topPerformers.map(c => `${c.name} (${c.priceChangePercentage24h.toFixed(2)}%)`).join(', ')} están teniendo un buen rendimiento hoy.`,
        action: 'Ver radar',
        actionPath: '/radar',
        relevanceScore: 7,
        timestamp: now,
        source: 'market',
        assetIds
      });
    }
  }

  // 5. Insight sobre correlación entre activos
  if (userAssets && userAssets.length >= 3) {
    const correlationInsight = analyzeAssetCorrelation(userAssets);
    if (correlationInsight) {
      insights.push(correlationInsight);
    }
  }

  // 6. Insight sobre volatilidad del mercado
  if (sentimentAnalysis && sentimentAnalysis.sentimentFactors.volatility > 70) {
    insights.push({
      id: `market-volatility-${now.getTime()}`,
      type: 'caution',
      title: 'Alta volatilidad en el mercado',
      description: `La volatilidad del mercado está actualmente elevada (${sentimentAnalysis.sentimentFactors.volatility}/100). Considera ajustar tu estrategia para gestionar el riesgo.`,
      action: 'Consultar al Guru',
      actionPath: '/guru',
      relevanceScore: 8,
      timestamp: now,
      source: 'market'
    });
  }

  // Ordenar insights por relevancia
  return insights
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, 5); // Limitar a los 5 más relevantes
};

/**
 * Calcula un puntaje de diversificación para el portafolio
 * @returns Puntaje de 1-10, donde 10 es perfectamente diversificado
 */
const calculateDiversificationScore = (portfolioStats: PortfolioStats): number => {
  const { assetAllocation } = portfolioStats;
  
  // Si no hay datos de asignación, devolver un valor bajo
  if (!assetAllocation || assetAllocation.length === 0) {
    return 3;
  }
  
  // Calcular el índice de Herfindahl-Hirschman (HHI) - medida de concentración
  // Un portafolio perfectamente diversificado tendría un HHI bajo
  const hhi = assetAllocation.reduce((sum, allocation) => {
    return sum + Math.pow(allocation.percentage, 2);
  }, 0);
  
  // Convertir HHI a una escala de 1-10 donde 10 es mejor diversificación
  // HHI de 10000 significa concentración total (1 activo = 100%)
  // HHI de 1000 o menos se considera diversificado
  const score = 10 - (hhi / 1000);
  
  // Limitar el puntaje entre 1 y 10
  return Math.max(1, Math.min(10, score));
};

/**
 * Analiza la correlación entre activos del portafolio
 */
const analyzeAssetCorrelation = (assets: any[]): AIInsight | null => {
  // Simulación de análisis de correlación
  // En una implementación real, esto usaría datos históricos para calcular correlaciones
  
  // Simulamos que encontramos alta correlación si hay muchos activos de la misma categoría
  const categories = assets.map(asset => asset.category || 'other');
  const categoryCounts: Record<string, number> = {};
  
  categories.forEach(category => {
    categoryCounts[category] = (categoryCounts[category] || 0) + 1;
  });
  
  const highestCategory = Object.entries(categoryCounts)
    .sort(([, countA], [, countB]) => countB - countA)[0];
  
  if (highestCategory && highestCategory[1] >= 3 && highestCategory[0] !== 'other') {
    return {
      id: `asset-correlation-${new Date().getTime()}`,
      type: 'warning',
      title: 'Alta correlación detectada',
      description: `Tienes ${highestCategory[1]} activos en la categoría "${highestCategory[0]}". Estos activos tienden a moverse juntos, lo que podría aumentar tu riesgo.`,
      action: 'Ver recomendaciones',
      actionPath: '/guru',
      relevanceScore: 7,
      timestamp: new Date(),
      source: 'technical'
    };
  }
  
  return null;
};
