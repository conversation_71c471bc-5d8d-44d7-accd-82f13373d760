/**
 * Script para iniciar todos los componentes del sistema Criptokens
 */
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { loadConfig } = require('./config-loader');
const { waitForService, waitForDependencies } = require('./health-checker');

// Logs de depuración iniciales
console.log('=== Iniciando depuración de start_all.js ===');
console.log('Directorio actual:', process.cwd());
console.log('Versión de Node:', process.version);
console.log('Plataforma:', process.platform);
console.log('Arquitectura:', process.arch);

// Cargar la configuración
console.log('Cargando configuración desde config.json...');
const config = loadConfig('./config.json');
console.log('Configuración cargada correctamente.');

// Función para iniciar un proceso
const startProcess = (component) => {
  return new Promise(async (resolve) => {
    const { name, cmd, args, cwd, port, type } = component;

    console.log(`\n--- Iniciando ${name} (${type}) ---`);
    console.log(`Comando: ${cmd} ${args.join(' ')}`);
    console.log(`Directorio de trabajo: ${cwd}`);
    console.log(`Puerto: ${port || 'N/A'}`);

    // Verificar si el directorio existe
    if (!fs.existsSync(cwd)) {
      console.error(`¡ERROR! El directorio ${cwd} no existe.`);
      console.error(`Ruta absoluta: ${path.resolve(cwd)}`);
      console.error(`Contenido del directorio padre: ${fs.existsSync(path.dirname(cwd)) ?
        fs.readdirSync(path.dirname(cwd)).join(', ') : 'Directorio padre no existe'}`);
      resolve(null);
      return;
    }

    // Verificar si el comando existe (solo para comandos locales)
    if (cmd.includes('/') || cmd.includes('\\')) {
      if (!fs.existsSync(cmd)) {
        console.error(`¡ERROR! El comando ${cmd} no existe.`);
        resolve(null);
        return;
      }
    }

    // Opciones para el proceso
    const options = {
      cwd,
      shell: true,
      stdio: 'pipe',
      env: { ...process.env }
    };

    console.log(`Iniciando proceso con opciones:`, JSON.stringify(options));

    try {
      // Iniciar el proceso
      const proc = spawn(cmd, args, options);

      // Manejar la salida estándar
      proc.stdout.on('data', (data) => {
        const output = data.toString().trim();
        console.log(`[${name}] ${output}`);
      });

      // Manejar la salida de error
      proc.stderr.on('data', (data) => {
        const output = data.toString().trim();
        console.error(`[${name}] ERROR: ${output}`);
      });

      // Manejar el cierre del proceso
      proc.on('close', (code) => {
        if (code !== 0) {
          console.error(`[${name}] Proceso terminado con código ${code}`);
        } else {
          console.log(`[${name}] Proceso terminado correctamente`);
        }
      });

      // Manejar errores
      proc.on('error', (err) => {
        console.error(`[${name}] Error al iniciar el proceso: ${err.message}`);
        console.error(`[${name}] Detalles del error:`, err);
        resolve(null);
      });

      console.log(`[${name}] Proceso iniciado con PID: ${proc.pid}`);

      // Resolver con el proceso iniciado
      resolve(proc);
    } catch (err) {
      console.error(`[${name}] Error crítico al iniciar el proceso:`, err);
      resolve(null);
    }
  });
};

// Función para iniciar todos los componentes secuencialmente
const startAllComponents = async () => {
  console.log('=== Iniciando todos los componentes del sistema Criptokens ===\n');

  const processes = [];
  const { components, system } = config;
  const { maxRetries, retryInterval } = system;

  console.log(`Configuración del sistema:`);
  console.log(`- maxRetries: ${maxRetries}`);
  console.log(`- retryInterval: ${retryInterval}ms`);
  console.log(`- Componentes: ${components.length}`);
  console.log(`- Componentes a iniciar: ${components.map(c => c.name).join(', ')}`);

  // MODO PRUEBA: Descomentar para probar solo un componente específico
  const testComponentId = 'backend'; // Cambia esto al ID del componente que quieras probar
  const testComponent = components.find(c => c.id === testComponentId);

  if (testComponent) {
    console.log(`\n=== MODO PRUEBA: Iniciando solo ${testComponent.name} ===`);

    // Verificar dependencias del componente de prueba
    console.log(`\nVerificando dependencias para ${testComponent.name}:`, testComponent.dependencies);

    if (testComponent.dependencies && testComponent.dependencies.length > 0) {
      console.log(`ADVERTENCIA: ${testComponent.name} tiene dependencias que no se iniciarán en modo prueba.`);
      console.log(`Dependencias: ${testComponent.dependencies.join(', ')}`);
      console.log(`Esto puede causar que el componente no funcione correctamente.`);
    }

    // Iniciar el componente de prueba
    const proc = await startProcess(testComponent);
    if (proc) {
      processes.push({ name: testComponent.name, process: proc });

      // Esperar a que el servicio esté disponible
      console.log(`\nEsperando a que ${testComponent.name} esté disponible...`);
      const available = await waitForService(testComponent, maxRetries, retryInterval);

      if (available) {
        console.log(`\n=== ${testComponent.name} está disponible ===`);
      } else {
        console.error(`\n=== ERROR: ${testComponent.name} no está disponible después de ${maxRetries} intentos ===`);
      }
    } else {
      console.error(`\n=== ERROR: No se pudo iniciar ${testComponent.name} ===`);
    }
  } else {
    // MODO NORMAL: Iniciar todos los componentes
    console.log(`\n=== MODO NORMAL: Iniciando todos los componentes ===`);

    for (const component of components) {
      console.log(`\n--- Procesando ${component.name} ---`);

      // Esperar a que las dependencias estén disponibles
      console.log(`Verificando dependencias para ${component.name}:`, component.dependencies);

      const dependenciesReady = await waitForDependencies(component, components, {
        maxAttempts: maxRetries,
        interval: retryInterval
      });

      if (!dependenciesReady) {
        console.error(`Error: No se pueden iniciar las dependencias para ${component.name}. Abortando.`);
        process.exit(1);
      }

      // Iniciar el componente
      console.log(`Iniciando ${component.name}...`);
      const proc = await startProcess(component);

      if (proc) {
        processes.push({ name: component.name, process: proc });

        // Esperar a que el servicio esté disponible
        console.log(`Esperando a que ${component.name} esté disponible...`);
        const available = await waitForService(component, maxRetries, retryInterval);

        if (available) {
          console.log(`${component.name} está disponible.`);
        } else {
          console.error(`Error: ${component.name} no está disponible después de ${maxRetries} intentos. Abortando.`);
          process.exit(1);
        }
      } else {
        console.error(`Error: No se pudo iniciar ${component.name}. Abortando.`);
        process.exit(1);
      }
    }

    console.log('\n=== Todos los componentes han sido iniciados ===');
  }

  console.log('\nPresiona Ctrl+C para detener todos los procesos.');

  // Manejar la terminación del script
  process.on('SIGINT', () => {
    console.log('\n\n=== Deteniendo todos los procesos... ===');

    processes.forEach(({ name, process }) => {
      try {
        console.log(`Deteniendo ${name}...`);
        process.kill();
      } catch (err) {
        console.error(`Error al detener ${name}: ${err.message}`);
      }
    });

    console.log('=== Todos los procesos han sido detenidos ===');
    process.exit(0);
  });

  // Manejar otras señales
  ['SIGTERM', 'SIGHUP'].forEach(signal => {
    process.on(signal, () => {
      console.log(`\n\n=== Recibida señal ${signal}. Deteniendo todos los procesos... ===`);

      processes.forEach(({ name, process }) => {
        try {
          console.log(`Deteniendo ${name}...`);
          process.kill();
        } catch (err) {
          console.error(`Error al detener ${name}: ${err.message}`);
        }
      });

      console.log('=== Todos los procesos han sido detenidos ===');
      process.exit(0);
    });
  });
};

// Iniciar todos los componentes
startAllComponents().catch(err => {
  console.error('Error al iniciar los componentes:', err);
  process.exit(1);
});
