/**
 * Agente especializado en noticias sobre criptomonedas
 * 
 * Este agente se encarga de buscar y analizar noticias sobre criptomonedas.
 */

const BaseAgent = require('./base-agent');
const logger = require('../utils/logger');
const { AgentError } = require('../utils/error-handler');

class NewsAgent extends BaseAgent {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(options = {}) {
    super('newsAgent', options);
  }
  
  /**
   * Ejecuta una tarea especializada
   * @param {Object} task - Tarea a ejecutar
   * @returns {Promise<Object>} Resultado de la tarea
   * @private
   */
  async _executeSpecializedTask(task) {
    switch (task.type) {
      case 'SEARCH_CRYPTO_NEWS':
        return await this.searchCryptoNews(task.parameters);
      case 'GET_COIN_NEWS':
        return await this.getCoinNews(task.parameters);
      case 'ANALYZE_NEWS_SENTIMENT':
        return await this.analyzeNewsSentiment(task.parameters);
      case 'SUMMARIZE_NEWS':
        return await this.summarizeNews(task.parameters);
      case 'BROWSE_NEWS_ARTICLE':
        return await this.browseNewsArticle(task.parameters);
      default:
        return await super._executeSpecializedTask(task);
    }
  }
  
  /**
   * Busca noticias sobre criptomonedas
   * @param {Object} parameters - Parámetros para la búsqueda de noticias
   * @returns {Promise<Object>} Resultados de la búsqueda
   */
  async searchCryptoNews(parameters) {
    try {
      const { query, count = 5, freshness = 'pd' } = parameters;
      
      logger.debug('NewsAgent', `Buscando noticias sobre criptomonedas con consulta "${query}"`);
      
      // Obtener el adaptador MCP de Brave
      const braveAdapter = this.getMcpAdapter('brave');
      
      // Buscar noticias
      const newsResults = await braveAdapter.getNews(query, {
        count,
        freshness
      });
      
      return {
        query,
        count,
        freshness,
        results: newsResults
      };
    } catch (error) {
      throw new AgentError(
        `Error al buscar noticias sobre criptomonedas: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Obtiene noticias específicas sobre una criptomoneda
   * @param {Object} parameters - Parámetros para la obtención de noticias
   * @returns {Promise<Object>} Noticias sobre la criptomoneda
   */
  async getCoinNews(parameters) {
    try {
      const { symbol, count = 5, freshness = 'pd' } = parameters;
      
      logger.debug('NewsAgent', `Obteniendo noticias sobre ${symbol}`);
      
      // Obtener el adaptador MCP de Brave
      const braveAdapter = this.getMcpAdapter('brave');
      
      // Buscar noticias específicas sobre la criptomoneda
      const newsResults = await braveAdapter.getCoinNews(symbol, {
        count,
        freshness
      });
      
      return {
        symbol,
        count,
        freshness,
        results: newsResults
      };
    } catch (error) {
      throw new AgentError(
        `Error al obtener noticias sobre ${parameters.symbol}: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Analiza el sentimiento de las noticias
   * @param {Object} parameters - Parámetros para el análisis de sentimiento
   * @returns {Promise<Object>} Análisis de sentimiento
   */
  async analyzeNewsSentiment(parameters) {
    try {
      const { news, symbol } = parameters;
      
      logger.debug('NewsAgent', `Analizando sentimiento de noticias${symbol ? ` sobre ${symbol}` : ''}`);
      
      // Si no se proporcionan noticias, buscarlas
      let newsData = news;
      if (!newsData && symbol) {
        const coinNews = await this.getCoinNews({
          symbol,
          count: 5,
          freshness: 'pd'
        });
        
        newsData = coinNews.results;
      }
      
      // Verificar que haya noticias para analizar
      if (!newsData || newsData.length === 0) {
        throw new AgentError('No hay noticias para analizar');
      }
      
      // Preparar el texto para el análisis
      const newsText = newsData.map(item => {
        return `Título: ${item.title}\nDescripción: ${item.description || 'No disponible'}\nFuente: ${item.source || 'Desconocida'}\nFecha: ${item.date || 'No disponible'}`;
      }).join('\n\n');
      
      // Analizar el sentimiento utilizando el LLM
      const analysisResult = await this._analyzeText({
        text: newsText,
        instructions: `Analiza el sentimiento general de estas noticias${symbol ? ` sobre ${symbol}` : ''}. Clasifica cada noticia como positiva, negativa o neutral, y proporciona un resumen del sentimiento general. Identifica también los temas principales mencionados en las noticias.`,
        format: 'JSON'
      });
      
      return {
        symbol: symbol || null,
        newsCount: newsData.length,
        sentiment: analysisResult.analysis
      };
    } catch (error) {
      throw new AgentError(
        `Error al analizar sentimiento de noticias: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Resume una o varias noticias
   * @param {Object} parameters - Parámetros para el resumen de noticias
   * @returns {Promise<Object>} Resumen de noticias
   */
  async summarizeNews(parameters) {
    try {
      const { news, url, maxLength } = parameters;
      
      logger.debug('NewsAgent', `Resumiendo noticias`);
      
      let newsContent;
      
      // Si se proporciona una URL, navegar a la página y extraer el contenido
      if (url) {
        const playwrightAdapter = this.getMcpAdapter('playwright');
        const pageData = await playwrightAdapter.browseWebPage(url);
        newsContent = `Título: ${pageData.title}\n\nContenido:\n${pageData.content}`;
      } 
      // Si se proporcionan noticias directamente, usarlas
      else if (news) {
        if (Array.isArray(news)) {
          newsContent = news.map(item => {
            return `Título: ${item.title}\nDescripción: ${item.description || 'No disponible'}\nFuente: ${item.source || 'Desconocida'}\nFecha: ${item.date || 'No disponible'}`;
          }).join('\n\n');
        } else {
          newsContent = news;
        }
      } else {
        throw new AgentError('Se debe proporcionar una URL o noticias para resumir');
      }
      
      // Resumir las noticias utilizando el LLM
      const summaryResult = await this._generateText({
        prompt: `Por favor, resume la siguiente noticia o conjunto de noticias de manera concisa${maxLength ? ` en aproximadamente ${maxLength} palabras` : ''}:\n\n${newsContent}`,
        systemPrompt: `Eres un experto en resumir noticias sobre criptomonedas. Tu objetivo es extraer la información más importante y presentarla de manera clara y concisa. Mantén un tono objetivo y neutral.`
      });
      
      return {
        summary: summaryResult.text,
        source: url || 'Noticias proporcionadas directamente'
      };
    } catch (error) {
      throw new AgentError(
        `Error al resumir noticias: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Navega a un artículo de noticias y extrae su contenido
   * @param {Object} parameters - Parámetros para la navegación
   * @returns {Promise<Object>} Contenido del artículo
   */
  async browseNewsArticle(parameters) {
    try {
      const { url } = parameters;
      
      logger.debug('NewsAgent', `Navegando a artículo de noticias: ${url}`);
      
      // Obtener el adaptador MCP de Playwright
      const playwrightAdapter = this.getMcpAdapter('playwright');
      
      // Navegar a la página
      const pageData = await playwrightAdapter.browseWebPage(url);
      
      // Extraer el contenido relevante
      const analysisResult = await this._analyzeText({
        text: pageData.content,
        instructions: 'Extrae la información más relevante de este artículo de noticias sobre criptomonedas. Identifica el tema principal, los hechos clave, las opiniones expresadas y cualquier dato o estadística mencionada.',
        format: 'JSON'
      });
      
      return {
        url,
        title: pageData.title,
        content: pageData.content,
        analysis: analysisResult.analysis,
        screenshot: pageData.screenshot
      };
    } catch (error) {
      throw new AgentError(
        `Error al navegar a artículo de noticias: ${error.message}`,
        { parameters },
        error
      );
    }
  }
}

module.exports = NewsAgent;
