# Guía para iniciar todos los servicios de Criptokens

Esta guía explica cómo iniciar todos los servicios necesarios para que la aplicación Criptokens funcione correctamente.

## Servicios necesarios

La aplicación Criptokens consta de los siguientes servicios:

1. **Crypto MCP Server** (Puerto 3101): Proporciona datos de criptomonedas en tiempo real.
2. **Brave Search Server** (Puerto 3102): Permite realizar búsquedas web para obtener noticias y análisis.
3. **Playwright MCP Server** (Puerto 3103): Permite visualizar y analizar páginas web completas.
4. **Backend** (Puerto 3001): Servidor Express que gestiona la lógica de negocio y las API.
5. **Frontend** (Puerto 5173): Interfaz de usuario desarrollada con React y Vite.

## Opciones para iniciar los servicios

### Opción 1: Scripts unificados (recomendado)

Hemos creado varios scripts para iniciar los servicios de forma flexible:

#### Iniciar solo la aplicación (frontend + backend):

```bash
# Con Node.js (multiplataforma)
node start-app.js
```

#### Iniciar solo los servidores MCP:

##### En Windows:

```powershell
# Desde PowerShell
.\start-mcp-servers.ps1
```

##### En Linux/macOS:

```bash
# Dar permisos de ejecución al script
chmod +x start-mcp-servers.sh

# Ejecutar el script
./start-mcp-servers.sh
```

#### Iniciar todos los servicios:

Puedes iniciar todos los servicios ejecutando ambos scripts en terminales separadas:

```bash
# En una terminal: iniciar la aplicación
node start-app.js

# En otra terminal: iniciar los servidores MCP
node start-mcp-servers.js  # o el script correspondiente a tu sistema operativo
```

### Opción 2: Iniciar servicios por separado

Si prefieres iniciar los servicios por separado, sigue estos pasos:

1. **Iniciar el Crypto MCP Server**:
   ```bash
   cd ../crypto-mcp-server
   node http-server.js
   ```

2. **Iniciar el Brave Search Server**:
   ```bash
   cd ..
   node brave-search-server.js
   ```

3. **Iniciar el Playwright MCP Server**:
   ```bash
   cd ../playwright-mcp-server
   node dist/server.js
   ```

4. **Iniciar el Backend**:
   ```bash
   cd backend
   node src/server.js
   ```

5. **Iniciar el Frontend**:
   ```bash
   cd frontend
   npx vite
   ```

## Verificación de servicios

Una vez iniciados todos los servicios, puedes verificar que estén funcionando correctamente accediendo a las siguientes URLs:

- Frontend: [http://localhost:5173](http://localhost:5173)
- Backend: [http://localhost:3001](http://localhost:3001)
- Crypto MCP Server: [http://localhost:3101/tools](http://localhost:3101/tools)
- Brave Search Server: [http://localhost:3102/search](http://localhost:3102/search) (requiere petición POST)
- Playwright MCP Server: [http://localhost:3103/status](http://localhost:3103/status)

## Solución de problemas

Si alguno de los servicios no se inicia correctamente, verifica lo siguiente:

1. **Puertos ocupados**: Asegúrate de que los puertos 3001, 3101, 3102, 3103 y 5173 no estén siendo utilizados por otras aplicaciones.

2. **Dependencias**: Verifica que todas las dependencias estén instaladas ejecutando `npm install` en cada directorio de servicio.

3. **Archivos de configuración**: Comprueba que los archivos `.env` en los directorios `frontend` y `backend` contengan las configuraciones correctas.

4. **Logs de error**: Revisa los mensajes de error en la consola para identificar problemas específicos.

## Detener los servicios

Para detener todos los servicios:

- Si usaste el script unificado, presiona `Ctrl+C` en la terminal donde lo ejecutaste.
- Si iniciaste los servicios por separado, presiona `Ctrl+C` en cada terminal.

## Notas adicionales

- El Guru Cripto utiliza todos estos servicios para proporcionar análisis completos y actualizados.
- Si solo necesitas probar funcionalidades básicas, puedes ejecutar únicamente el frontend y el backend.
- Para un funcionamiento óptimo del Guru Cripto, se recomienda tener todos los servicios en ejecución.
