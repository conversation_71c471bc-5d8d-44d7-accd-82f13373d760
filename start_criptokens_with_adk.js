/**
 * Script para iniciar todos los componentes de Criptokens con ADK
 */
const childProcess = require('child_process');
const path = require('path');
const config = require('./config');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',

  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m'
};

// Función para imprimir mensajes con formato
function printMessage(message, color = colors.white) {
  const timestamp = new Date().toISOString().replace(/T/, ' ').replace(/\..+/, '');
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${color}${message}${colors.reset}`);
}

// Función para iniciar un proceso
function startProcess(name, command, args, cwd, color) {
  printMessage(`Iniciando ${name}...`, colors.yellow);

  const processEnv = Object.assign({}, process.env);
  processEnv.FORCE_COLOR = "true";

  const proc = childProcess.spawn(command, args, {
    cwd: cwd,
    shell: true,
    env: processEnv
  });

  proc.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${color}[${name}] ${line}${colors.reset}`);
      }
    });
  });

  proc.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${colors.red}[${name} ERROR] ${line}${colors.reset}`);
      }
    });
  });

  proc.on('close', (code) => {
    if (code !== 0) {
      printMessage(`${name} se ha cerrado con código ${code}`, colors.red);
    } else {
      printMessage(`${name} se ha cerrado correctamente`, colors.green);
    }
  });

  return proc;
}

// Almacenar los procesos para poder cerrarlos correctamente
const processes = {};

// Iniciar MCP Cripto Server
processes.mcpCripto = startProcess(
  'MCP Cripto Server',
  'node',
  ['http-server.js'],
  path.join(__dirname, 'crypto-mcp-server'),
  colors.cyan
);

// Esperar un poco antes de iniciar el siguiente servicio
setTimeout(() => {
  // Iniciar MCP Brave Server
  processes.mcpBrave = startProcess(
    'MCP Brave Server',
    'node',
    ['brave-search-server.js'],
    __dirname,
    colors.magenta
  );

  // Esperar un poco antes de iniciar el siguiente servicio
  setTimeout(() => {
    // Iniciar MCP Playwright Server
    processes.mcpPlaywright = startProcess(
      'MCP Playwright Server',
      'node',
      ['dist/server.js'],
      path.join(__dirname, 'playwright-mcp-server'),
      colors.blue
    );

    // Esperar un poco antes de iniciar el siguiente servicio
    setTimeout(() => {
      // Iniciar ADK API Server
      processes.adkApi = startProcess(
        'ADK API Server',
        'adk',
        ['api_server', '--port', '8001'],
        __dirname,
        colors.green
      );

      // Esperar un poco antes de iniciar el siguiente servicio
      setTimeout(() => {
        // Iniciar Backend
        processes.backend = startProcess(
          'Backend',
          'node',
          ['src/server.js'],
          path.join(__dirname, 'backend'),
          colors.yellow
        );

        // Esperar un poco antes de iniciar el siguiente servicio
        setTimeout(() => {
          // Iniciar Frontend
          processes.frontend = startProcess(
            'Frontend',
            'npm',
            ['run', 'dev'],
            path.join(__dirname, 'frontend'),
            colors.magenta
          );

          printMessage('Todos los servicios han sido iniciados correctamente', colors.green);
          printMessage('Presiona Ctrl+C para detener todos los servicios', colors.yellow);
        }, 2000);
      }, 2000);
    }, 2000);
  }, 2000);
}, 2000);

// Manejar el cierre de la aplicación
process.on('SIGINT', () => {
  printMessage('Deteniendo todos los servicios...', colors.yellow);

  // Cerrar todos los procesos
  Object.values(processes).forEach(proc => {
    if (proc && !proc.killed) {
      proc.kill();
    }
  });

  printMessage('Todos los servicios han sido detenidos correctamente', colors.green);
  process.exit(0);
});
