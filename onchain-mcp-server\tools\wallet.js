/**
 * Herramientas MCP relacionadas con carteras
 */
const etherscanService = require('../services/etherscan');
const config = require('../config');

module.exports = {
  /**
   * Obtiene el balance de una cartera
   */
  getWalletBalance: {
    description: 'Get the balance of a wallet address',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address']
    },
    handler: async ({ address, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getWalletBalance(address);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene el balance de un token para una cartera
   */
  getTokenBalance: {
    description: 'Get the token balance of a wallet address',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        tokenAddress: {
          type: 'string',
          description: 'The token contract address'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address', 'tokenAddress']
    },
    handler: async ({ address, tokenAddress, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getTokenBalance(address, tokenAddress);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene el balance de un token de criptomoneda por su nombre
   */
  getCryptoBalance: {
    description: 'Get the balance of a cryptocurrency by name',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        cryptoId: {
          type: 'string',
          description: 'The cryptocurrency ID (bitcoin, ethereum, etc.)'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address', 'cryptoId']
    },
    handler: async ({ address, cryptoId, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener la dirección del token para la criptomoneda
        const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
        
        if (!tokenAddress) {
          throw new Error(`Token address not found for ${cryptoId}`);
        }
        
        return await etherscanService.getTokenBalance(address, tokenAddress);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene los balances de múltiples tokens para una cartera
   */
  getMultipleTokenBalances: {
    description: 'Get multiple token balances for a wallet address',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        tokenAddresses: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: 'List of token contract addresses'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address', 'tokenAddresses']
    },
    handler: async ({ address, tokenAddresses, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        const balances = [];
        
        for (const tokenAddress of tokenAddresses) {
          try {
            const balance = await etherscanService.getTokenBalance(address, tokenAddress);
            balances.push(balance);
          } catch (error) {
            console.error(`Error fetching balance for token ${tokenAddress}:`, error);
            balances.push({
              address,
              tokenAddress,
              error: error.message
            });
          }
        }
        
        return {
          address,
          balances,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene los balances de múltiples criptomonedas para una cartera
   */
  getPortfolio: {
    description: 'Get portfolio of cryptocurrencies for a wallet address',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        cryptoIds: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: 'List of cryptocurrency IDs (bitcoin, ethereum, etc.)'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address']
    },
    handler: async ({ address, cryptoIds, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Si no se proporcionan cryptoIds, usar todas las criptomonedas configuradas
        const ids = cryptoIds || Object.keys(config.tokenAddresses);
        
        const tokenAddresses = ids
          .map(id => {
            const tokenAddress = config.tokenAddresses[id.toLowerCase()];
            return tokenAddress ? { id, tokenAddress } : null;
          })
          .filter(item => item !== null);
        
        const balances = [];
        
        // Obtener el balance de ETH
        try {
          const ethBalance = await etherscanService.getWalletBalance(address);
          balances.push({
            id: 'ethereum',
            symbol: 'ETH',
            name: 'Ethereum',
            balance: ethBalance.balance,
            tokenAddress: null
          });
        } catch (error) {
          console.error('Error fetching ETH balance:', error);
        }
        
        // Obtener los balances de tokens
        for (const { id, tokenAddress } of tokenAddresses) {
          try {
            const balance = await etherscanService.getTokenBalance(address, tokenAddress);
            balances.push({
              id,
              symbol: balance.tokenSymbol,
              name: balance.tokenName,
              balance: balance.balance,
              tokenAddress
            });
          } catch (error) {
            console.error(`Error fetching balance for ${id}:`, error);
          }
        }
        
        return {
          address,
          balances: balances.filter(b => b.balance > 0), // Solo incluir tokens con balance positivo
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  }
};
