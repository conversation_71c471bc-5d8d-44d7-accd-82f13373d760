/**
 * Cliente para comunicarse con el servidor MCP Orchestrator
 */
const axios = require('axios');

// URL base del servidor MCP Orchestrator
const MCP_ORCHESTRATOR_URL = process.env.MCP_ORCHESTRATOR_URL || 'http://localhost:3102/mcp';

/**
 * Llama a una herramienta específica en el servidor MCP Orchestrator
 * @param {string} toolName - Nombre de la herramienta a llamar
 * @param {Object} input - Parámetros de entrada para la herramienta
 * @returns {Promise<any>} - Resultado de la ejecución de la herramienta
 */
async function callMcpTool(toolName, input) {
  try {
    console.log(`Llamando a la herramienta ${toolName} en el servidor MCP Orchestrator con input:`, input);
    
    // Crear una sesión temporal
    const sessionResponse = await axios.post(MCP_ORCHESTRATOR_URL, {
      jsonrpc: '2.0',
      method: 'session.create',
      params: {},
      id: 1
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Mcp-Session-Id': 'temp-session-id'
      }
    });
    
    if (!sessionResponse.data || sessionResponse.data.error) {
      console.error('Error al crear sesión MCP:', sessionResponse.data?.error || 'Respuesta vacía');
      throw new Error('Error al crear sesión MCP');
    }
    
    const sessionId = sessionResponse.data.result.sessionId;
    console.log(`Sesión MCP creada con ID: ${sessionId}`);
    
    // Llamar a la herramienta
    const toolResponse = await axios.post(MCP_ORCHESTRATOR_URL, {
      jsonrpc: '2.0',
      method: 'execute',
      params: {
        tool: toolName,
        input: input
      },
      id: 2
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Mcp-Session-Id': sessionId
      }
    });
    
    if (!toolResponse.data || toolResponse.data.error) {
      console.error(`Error al llamar a la herramienta ${toolName}:`, toolResponse.data?.error || 'Respuesta vacía');
      throw new Error(`Error al llamar a la herramienta ${toolName}`);
    }
    
    console.log(`Respuesta de la herramienta ${toolName} recibida:`, toolResponse.data.result);
    return toolResponse.data.result;
  } catch (error) {
    console.error(`Error al comunicarse con el servidor MCP Orchestrator:`, error.message);
    throw error;
  }
}

/**
 * Llama a la herramienta brave_search en el servidor MCP Orchestrator
 * @param {string} query - Consulta de búsqueda
 * @param {number} count - Número de resultados a devolver
 * @param {string} freshness - Filtro de tiempo
 * @returns {Promise<Array>} - Resultados de la búsqueda
 */
async function braveSearch(query, count = 5, freshness = 'pm') {
  try {
    return await callMcpTool('brave_search', {
      query,
      count,
      freshness
    });
  } catch (error) {
    console.error('Error al realizar búsqueda con Brave Search:', error.message);
    // Devolver un array vacío en caso de error
    return [];
  }
}

module.exports = {
  callMcpTool,
  braveSearch
};
