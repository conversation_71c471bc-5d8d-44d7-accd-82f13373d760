"use strict";
const { MCPTool } = require("mcp-framework");
const { z } = require("zod");
const axios = require("axios");
const dotenv = require("dotenv");
// Cargar variables de entorno
dotenv.config();
class BraveSearchTool extends MCPTool {
    name = "brave_search";
    description = "Busca información actualizada en la web utilizando Brave Search";
    schema = {
        query: {
            type: z.string(),
            description: "La consulta de búsqueda",
        },
        count: {
            type: z.number().optional(),
            description: "Número de resultados a devolver (1-10)",
        },
        freshness: {
            type: z.enum(["pd", "pw", "pm", "py"]).optional(),
            description: "Filtro de tiempo: pd=último día, pw=última semana, pm=último mes, py=último año",
        },
    };
    async execute(input) {
        const { query, count = 5, freshness = "pm" } = input;
        console.log(`Realizando búsqueda en Brave para: "${query}" (count: ${count}, freshness: ${freshness})`);
        try {
            // Obtener la API key de las variables de entorno
            const braveApiKey = process.env.BRAVE_API_KEY;
            if (!braveApiKey) {
                console.error("API key de Brave no encontrada en las variables de entorno");
                return this.generateMockResults(query, count);
            }
            // Realizar la petición a la API de Brave Search
            const response = await axios.get('https://api.search.brave.com/res/v1/web/search', {
                params: {
                    q: query,
                    count: Math.min(count, 10), // Máximo 10 resultados por petición
                    freshness: freshness,
                    search_lang: 'es',
                    country: 'ES',
                    safesearch: 'moderate'
                },
                headers: {
                    'Accept': 'application/json',
                    'Accept-Encoding': 'gzip',
                    'X-Subscription-Token': braveApiKey
                },
                timeout: 5000 // 5 segundos de timeout
            });
            if (response.data && response.data.web && response.data.web.results) {
                console.log(`Recibidos ${response.data.web.results.length} resultados de Brave Search`);
                // Transformar los resultados al formato esperado
                return response.data.web.results.map((result) => ({
                    title: result.title,
                    url: result.url,
                    description: result.description || '',
                    publishedDate: result.age || '',
                    source: result.meta_url?.split('/')[2] || new URL(result.url).hostname
                }));
            }
            return this.generateMockResults(query, count);
        }
        catch (error) {
            console.error('Error al usar la API de Brave Search:', error);
            return this.generateMockResults(query, count);
        }
    }
    // Método para generar resultados simulados en caso de error
    generateMockResults(query, count) {
        console.log(`Generando resultados simulados para: "${query}"`);
        const results = [];
        const currentDate = new Date().toISOString().split('T')[0];
        for (let i = 0; i < count; i++) {
            results.push({
                title: `Resultado simulado ${i + 1} para "${query}"`,
                url: `https://ejemplo.com/resultados/${encodeURIComponent(query)}/${i + 1}`,
                description: `Este es un resultado simulado para la búsqueda "${query}". Se genera cuando hay problemas para conectar con la API de Brave Search.`,
                publishedDate: currentDate,
                source: 'Simulado'
            });
        }
        return results;
    }
}
module.exports = BraveSearchTool;
