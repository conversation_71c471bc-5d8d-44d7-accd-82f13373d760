import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { useCryptoGuru } from '../hooks/useCryptoGuru';
import { useTopCryptocurrencies } from '../hooks/useMcpClient';
import { AvatarStatusContext } from './Layout';
import { mockNewsData, getMockNewsForCoin } from '../services/mockNewsData';
import '../styles/GuruContextSidebar.css';

// Interfaces para los widgets
interface MarketSentimentData {
  fearGreedIndex: number;
  btcDominance: number;
  marketTrend: 'bullish' | 'bearish' | 'neutral';
}

interface GuruContextSidebarProps {
  currentTopic: string | null;
}

const GuruContextSidebar: React.FC<GuruContextSidebarProps> = ({ currentTopic }) => {
  const [marketSentiment, setMarketSentiment] = useState<MarketSentimentData>({
    fearGreedIndex: 55, // Valor por defecto
    btcDominance: 52.3, // Valor por defecto
    marketTrend: 'neutral' // Valor por defecto
  });

  const { avatarStatus } = useContext(AvatarStatusContext);
  const { data: topCryptos, loading: topCryptosLoading } = useTopCryptocurrencies(5);
  const {
    data: cryptoData,
    loading: cryptoLoading,
    getCryptoData,
    formatPrice,
    formatChange,
    formatVolume
  } = useCryptoGuru();

  // Efecto para cargar datos de la criptomoneda específica cuando cambia el tema
  useEffect(() => {
    if (currentTopic && !['general_market', 'news_summary', null].includes(currentTopic)) {
      getCryptoData(currentTopic);
    }

    // Registrar cambio de tema en la consola para depuración
    console.log('Tema actual cambiado a:', currentTopic);
  }, [currentTopic, getCryptoData]);

  // Función para obtener datos simulados de criptomonedas
  const getMockCryptoData = (cryptoId: string) => {
    const mockData: Record<string, any> = {
      'bitcoin': {
        id: 'bitcoin',
        name: 'Bitcoin',
        symbol: 'btc',
        price: 61245.32,
        changePercent24Hr: 2.5,
        volumeUsd24Hr: 32000000000,
        image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        last_updated: new Date().toISOString()
      },
      'ethereum': {
        id: 'ethereum',
        name: 'Ethereum',
        symbol: 'eth',
        price: 3521.18,
        changePercent24Hr: -0.8,
        volumeUsd24Hr: 18000000000,
        image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
        last_updated: new Date().toISOString()
      },
      'solana': {
        id: 'solana',
        name: 'Solana',
        symbol: 'sol',
        price: 142.67,
        changePercent24Hr: 5.2,
        volumeUsd24Hr: 3000000000,
        image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
        last_updated: new Date().toISOString()
      },
      'ripple': {
        id: 'ripple',
        name: 'XRP',
        symbol: 'xrp',
        price: 0.5423,
        changePercent24Hr: 1.8,
        volumeUsd24Hr: 1200000000,
        image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
        last_updated: new Date().toISOString()
      },
      'cardano': {
        id: 'cardano',
        name: 'Cardano',
        symbol: 'ada',
        price: 0.45,
        changePercent24Hr: 1.1,
        volumeUsd24Hr: 700000000,
        image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
        last_updated: new Date().toISOString()
      },
      'binancecoin': {
        id: 'binancecoin',
        name: 'BNB',
        symbol: 'bnb',
        price: 608.42,
        changePercent24Hr: 0.85,
        volumeUsd24Hr: 2000000000,
        image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
        last_updated: new Date().toISOString()
      }
    };

    // Si no encontramos el ID exacto, intentamos buscar por nombre parcial
    if (!mockData[cryptoId]) {
      const normalizedId = cryptoId.toLowerCase();
      const matchingKey = Object.keys(mockData).find(key =>
        key.includes(normalizedId) || normalizedId.includes(key) ||
        mockData[key].name.toLowerCase().includes(normalizedId) ||
        mockData[key].symbol.toLowerCase().includes(normalizedId)
      );

      if (matchingKey) {
        return mockData[matchingKey];
      }

      // Si no encontramos coincidencia, devolvemos Bitcoin como fallback
      return mockData['bitcoin'];
    }

    return mockData[cryptoId];
  };

  // Efecto para calcular el sentimiento del mercado basado en los datos de las principales criptomonedas
  useEffect(() => {
    if (topCryptos && topCryptos.length > 0) {
      // Calcular el índice de miedo/codicia basado en el cambio porcentual promedio
      const avgChange = topCryptos.reduce((sum, crypto) =>
        sum + (crypto.price_change_percentage_24h || 0), 0) / topCryptos.length;

      // Calcular un índice de miedo/codicia simple (0-100)
      // 0 = miedo extremo, 100 = codicia extrema
      const fearGreedIndex = Math.min(100, Math.max(0, 50 + avgChange * 2));

      // Calcular la dominancia de Bitcoin
      const totalMarketCap = topCryptos.reduce((sum, crypto) => sum + crypto.market_cap, 0);
      const btcMarketCap = topCryptos.find(c => c.id === 'bitcoin')?.market_cap || 0;
      const btcDominance = (btcMarketCap / totalMarketCap) * 100;

      // Determinar la tendencia del mercado
      let marketTrend: 'bullish' | 'bearish' | 'neutral' = 'neutral';
      if (avgChange > 3) marketTrend = 'bullish';
      else if (avgChange < -3) marketTrend = 'bearish';

      setMarketSentiment({
        fearGreedIndex,
        btcDominance,
        marketTrend
      });
    } else {
      // Si no hay datos reales, usar datos simulados
      setMarketSentiment({
        fearGreedIndex: 65, // Valor simulado ligeramente optimista
        btcDominance: 52.3,
        marketTrend: 'neutral'
      });
    }
  }, [topCryptos]);

  // Renderizar el widget de sentimiento del mercado
  const renderMarketSentimentWidget = () => {
    return (
      <div className="context-widget market-sentiment-widget">
        <h3>Sentimiento del Mercado</h3>

        {(!topCryptos || topCryptos.length === 0) && (
          <div className="mock-data-badge">Datos simulados</div>
        )}

        <div className="fear-greed-meter">
          <div className="fear-greed-label">Índice de Miedo/Codicia</div>
          <div className="fear-greed-value" data-sentiment={getSentimentCategory(marketSentiment.fearGreedIndex)}>
            {Math.round(marketSentiment.fearGreedIndex)}
          </div>
          <div className="fear-greed-scale">
            <span className="fear">Miedo</span>
            <div className="scale-bar">
              <div
                className="scale-indicator"
                style={{ left: `${marketSentiment.fearGreedIndex}%` }}
              ></div>
            </div>
            <span className="greed">Codicia</span>
          </div>
        </div>

        <div className="market-dominance">
          <div className="dominance-label">Dominancia BTC</div>
          <div className="dominance-value">{marketSentiment.btcDominance.toFixed(1)}%</div>
        </div>

        <div className="market-trend">
          <div className="trend-label">Tendencia</div>
          <div className={`trend-value ${marketSentiment.marketTrend}`}>
            {marketSentiment.marketTrend === 'bullish' ? '🚀 Alcista' :
             marketSentiment.marketTrend === 'bearish' ? '🐻 Bajista' : '⚖️ Neutral'}
          </div>
        </div>

        {topCryptos && !topCryptosLoading && (
          <div className="top-movers">
            <h4>Principales Movimientos</h4>
            <div className="movers-list">
              {topCryptos.slice(0, 3).map(crypto => (
                <div key={crypto.id} className="mover-item">
                  <img src={crypto.image} alt={crypto.name} className="crypto-icon" />
                  <span className="crypto-name">{crypto.symbol.toUpperCase()}</span>
                  <span className={`crypto-change ${crypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                    {crypto.price_change_percentage_24h >= 0 ? '+' : ''}
                    {crypto.price_change_percentage_24h.toFixed(2)}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Renderizar el widget de contexto de una criptomoneda específica
  const renderCoinContextWidget = () => {
    if (cryptoLoading || !cryptoData) {
      // Mostrar datos simulados en lugar de solo "Cargando..."
      const mockCryptoData = getMockCryptoData(currentTopic || 'bitcoin');

      return (
        <div className="context-widget coin-context-widget">
          <div className="coin-header">
            <img src={mockCryptoData.image} alt={mockCryptoData.name} className="coin-icon" />
            <h3>{mockCryptoData.name} ({mockCryptoData.symbol.toUpperCase()})</h3>
            <div className="mock-data-badge">Datos simulados</div>
          </div>

          <div className="coin-price-container">
            <div className="coin-price">{formatPrice(mockCryptoData.price)}</div>
            <div className={`coin-change ${mockCryptoData.changePercent24Hr >= 0 ? 'positive' : 'negative'}`}>
              {formatChange(mockCryptoData.changePercent24Hr)}
            </div>
          </div>

          <div className="coin-stats">
            <div className="stat-item">
              <div className="stat-label">Volumen 24h</div>
              <div className="stat-value">{formatVolume(mockCryptoData.volumeUsd24Hr)}</div>
            </div>
            <div className="stat-item">
              <div className="stat-label">Actualizado</div>
              <div className="stat-value">{new Date().toLocaleTimeString()}</div>
            </div>
          </div>

          <div className="coin-actions">
            <button className="action-button chart-button">
              <i className="fas fa-chart-line"></i>
              Ver Gráfico Avanzado
            </button>
            <button className="action-button news-button">
              <i className="fas fa-newspaper"></i>
              Buscar Noticias
            </button>
            <button className="action-button watchlist-button">
              <i className="fas fa-star"></i>
              Añadir a Mi Radar
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="context-widget coin-context-widget">
        <div className="coin-header">
          <img src={cryptoData.image} alt={cryptoData.name} className="coin-icon" />
          <h3>{cryptoData.name} ({cryptoData.symbol.toUpperCase()})</h3>
        </div>

        <div className="coin-price-container">
          <div className="coin-price">{formatPrice(cryptoData.price)}</div>
          <div className={`coin-change ${cryptoData.changePercent24Hr >= 0 ? 'positive' : 'negative'}`}>
            {formatChange(cryptoData.changePercent24Hr)}
          </div>
        </div>

        <div className="coin-stats">
          <div className="stat-item">
            <div className="stat-label">Volumen 24h</div>
            <div className="stat-value">{formatVolume(cryptoData.volumeUsd24Hr)}</div>
          </div>
          <div className="stat-item">
            <div className="stat-label">Actualizado</div>
            <div className="stat-value">{new Date(cryptoData.last_updated).toLocaleTimeString()}</div>
          </div>
        </div>

        <div className="coin-actions">
          <button className="action-button chart-button">
            <i className="fas fa-chart-line"></i>
            Ver Gráfico Avanzado
          </button>
          <button className="action-button news-button">
            <i className="fas fa-newspaper"></i>
            Buscar Noticias
          </button>
          <button className="action-button watchlist-button">
            <i className="fas fa-star"></i>
            Añadir a Mi Radar
          </button>
        </div>
      </div>
    );
  };

  // Renderizar el widget de resumen de noticias
  const renderNewsSummaryWidget = () => {
    // Obtener noticias simuladas para el tema actual
    const newsItems = currentTopic && !['general_market', 'news_summary', null].includes(currentTopic)
      ? getMockNewsForCoin(currentTopic, 3)
      : mockNewsData.slice(0, 3);

    // Determinar el sentimiento general basado en las noticias
    const sentiments = newsItems.map(item => item.sentiment || 'neutral');
    const positiveCount = sentiments.filter(s => s === 'positive').length;
    const negativeCount = sentiments.filter(s => s === 'negative').length;

    let overallSentiment = 'neutral';
    if (positiveCount > negativeCount && positiveCount > sentiments.length / 3) {
      overallSentiment = 'positive';
    } else if (negativeCount > positiveCount && negativeCount > sentiments.length / 3) {
      overallSentiment = 'negative';
    }

    return (
      <div className="context-widget news-summary-widget">
        <h3>Resumen de Noticias</h3>
        <div className="mock-data-badge">Datos simulados</div>

        <div className="news-sentiment">
          <div className="sentiment-label">Sentimiento General</div>
          <div className={`sentiment-value ${overallSentiment}`}>
            {overallSentiment === 'positive' ? 'Positivo' :
             overallSentiment === 'negative' ? 'Negativo' : 'Neutral'}
          </div>
        </div>

        <div className="news-items">
          {newsItems.map((item, index) => (
            <div key={index} className={`news-item ${item.sentiment || 'neutral'}`}>
              <div className="news-title">{item.title}</div>
              <div className="news-source">{item.source} · {new Date(item.publishedTime).toLocaleDateString()}</div>
            </div>
          ))}
        </div>

        <div className="news-topics">
          <h4>Temas Relacionados</h4>
          <div className="topics-tags">
            <span className="topic-tag">Regulación</span>
            <span className="topic-tag">DeFi</span>
            <span className="topic-tag">NFTs</span>
            <span className="topic-tag">Mercados</span>
          </div>
        </div>
      </div>
    );
  };

  // Función para determinar la categoría de sentimiento
  const getSentimentCategory = (value: number): 'extreme-fear' | 'fear' | 'neutral' | 'greed' | 'extreme-greed' => {
    if (value < 20) return 'extreme-fear';
    if (value < 40) return 'fear';
    if (value < 60) return 'neutral';
    if (value < 80) return 'greed';
    return 'extreme-greed';
  };

  // Renderizar el contenido según el tema actual
  const renderContent = () => {
    if (!currentTopic || currentTopic === 'general_market') {
      return renderMarketSentimentWidget();
    } else if (currentTopic === 'news_summary') {
      return renderNewsSummaryWidget();
    } else {
      return renderCoinContextWidget();
    }
  };

  return (
    <div className={`guru-context-sidebar ${avatarStatus}`}>
      <div className="sidebar-header">
        <h2>Contexto</h2>
        <div className="context-type">
          {!currentTopic || currentTopic === 'general_market' ? 'Mercado General' :
           currentTopic === 'news_summary' ? 'Resumen de Noticias' :
           cryptoData ? cryptoData.name : currentTopic}
        </div>
      </div>

      <div className="sidebar-content">
        {renderContent()}
      </div>

      <div className="sidebar-footer">
        <Link to="/dashboard" className="dashboard-link">
          <i className="fas fa-tachometer-alt"></i>
          Ver Dashboard Completo
        </Link>
      </div>
    </div>
  );
};

export default GuruContextSidebar;
