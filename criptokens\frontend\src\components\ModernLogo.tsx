import React, { useEffect, useRef } from 'react';
import anime from '../utils/simpleAnime';

interface ModernLogoProps {
  size?: 'small' | 'medium' | 'large';
}

const ModernLogo: React.FC<ModernLogoProps> = ({ size = 'medium' }) => {
  const logoRef = useRef<HTMLDivElement>(null);

  // Determinar dimensiones según el tamaño
  const getDimensions = () => {
    switch (size) {
      case 'small':
        return { width: '140px', height: '40px', fontSize: '16px' };
      case 'large':
        return { width: '280px', height: '80px', fontSize: '32px' };
      case 'medium':
      default:
        return { width: '180px', height: '60px', fontSize: '22px' };
    }
  };

  const dimensions = getDimensions();

  // Animar el logo al montar el componente
  useEffect(() => {
    if (logoRef.current) {
      // Animar las letras
      anime({
        targets: '.logo-letter',
        opacity: [0, 1],
        translateY: [20, 0],
        delay: anime.stagger(100),
        duration: 800,
        easing: 'easeOutQuad'
      });

      // Animar el brillo
      anime({
        targets: '.logo-glow',
        opacity: [0, 0.8, 0],
        translateX: [-50, 250],
        duration: 3000,
        delay: 1000,
        easing: 'easeInOutSine',
        loop: true
      });
    }
  }, []);

  return (
    <div
      ref={logoRef}
      className="modern-logo"
      style={{
        width: dimensions.width,
        height: dimensions.height,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Fondo del logo */}
      <div
        className="logo-background"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'linear-gradient(135deg, rgba(0, 224, 255, 0.1) 0%, rgba(0, 163, 255, 0.1) 100%)',
          borderRadius: '8px',
          zIndex: 0
        }}
      />

      {/* Efecto de brillo */}
      <div
        className="logo-glow"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '50px',
          height: '100%',
          background: 'linear-gradient(90deg, rgba(0, 224, 255, 0) 0%, rgba(0, 224, 255, 0.8) 50%, rgba(0, 224, 255, 0) 100%)',
          transform: 'skewX(-20deg)',
          zIndex: 1
        }}
      />

      {/* Texto del logo */}
      <div
        className="logo-text"
        style={{
          position: 'relative',
          zIndex: 2,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          height: '100%',
          fontFamily: "'Orbitron', sans-serif",
          fontSize: dimensions.fontSize,
          fontWeight: 700,
          letterSpacing: '1px',
          textTransform: 'uppercase'
        }}
      >
        <span
          className="logo-letter"
          style={{
            background: 'linear-gradient(45deg, #00e0ff, #00a3ff)',
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            color: 'transparent',
            textShadow: '0 0 10px rgba(0, 224, 255, 0.3)'
          }}
        >
          C
        </span>
        <span
          className="logo-letter"
          style={{
            background: 'linear-gradient(45deg, #00e0ff, #00a3ff)',
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            color: 'transparent'
          }}
        >
          ripto
        </span>
        <span
          className="logo-letter"
          style={{
            background: 'linear-gradient(45deg, #00a3ff, #00ff9d)',
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            color: 'transparent',
            textShadow: '0 0 10px rgba(0, 255, 157, 0.3)'
          }}
        >
          k
        </span>
        <span
          className="logo-letter"
          style={{
            background: 'linear-gradient(45deg, #00a3ff, #00ff9d)',
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            color: 'transparent'
          }}
        >
          ens
        </span>
      </div>

      {/* Elemento decorativo */}
      <div
        className="logo-decoration"
        style={{
          position: 'absolute',
          bottom: '5px',
          right: '5px',
          width: '10px',
          height: '10px',
          borderRadius: '50%',
          background: 'linear-gradient(45deg, #00e0ff, #00ff9d)',
          boxShadow: '0 0 10px rgba(0, 224, 255, 0.7)'
        }}
      />
    </div>
  );
};

export default ModernLogo;
