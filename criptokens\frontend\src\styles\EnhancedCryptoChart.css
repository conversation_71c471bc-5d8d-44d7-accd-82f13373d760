.enhanced-crypto-chart {
  position: relative;
  background: rgba(10, 10, 26, 0.5);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(64, 220, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.enhanced-crypto-chart:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
  border-color: rgba(64, 220, 255, 0.4);
}

.enhanced-crypto-chart canvas {
  display: block;
  margin: 0 auto;
}

.chart-tooltip {
  position: absolute;
  background: rgba(15, 15, 35, 0.9);
  border-radius: 6px;
  padding: 8px 12px;
  pointer-events: none;
  opacity: 0;
  transform: translate(-50%, -100%);
  transition: opacity 0.2s;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(64, 220, 255, 0.3);
  min-width: 100px;
  text-align: center;
}

.tooltip-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.tooltip-value {
  font-size: 14px;
  font-weight: bold;
  color: white;
  background: linear-gradient(90deg, #00f2ff, #4657ce);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Animación de brillo */
.enhanced-crypto-chart::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(64, 220, 255, 0.1) 0%, rgba(10, 10, 26, 0) 70%);
  opacity: 0;
  transition: opacity 0.5s;
  pointer-events: none;
}

.enhanced-crypto-chart:hover::after {
  opacity: 1;
  animation: pulse 3s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.1;
  }
}
