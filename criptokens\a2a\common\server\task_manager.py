import asyncio
from typing import Dict, List, AsyncIterable, Any
from ..types import (
    Task,
    TaskStatus,
    TaskState,
    Message,
    Artifact,
    SendTaskRequest,
    SendTaskResponse,
    GetTaskRequest,
    GetTaskResponse,
    CancelTaskRequest,
    CancelTaskResponse,
    TaskNotFoundError,
    TaskNotCancelableError,
    JSONRPCResponse,
    TaskSendParams,
    TaskQueryParams,
    TaskIdParams,
    SetTaskPushNotificationRequest,
    SetTaskPushNotificationResponse,
    GetTaskPushNotificationRequest,
    GetTaskPushNotificationResponse,
    PushNotificationNotSupportedError,
    SendTaskStreamingRequest,
    SendTaskStreamingResponse,
    TaskStatusUpdateEvent,
    TaskArtifactUpdateEvent,
    UnsupportedOperationError,
    TaskResubscriptionRequest,
)
import logging

logger = logging.getLogger(__name__)


class TaskManager:
    """Base class for task managers."""

    async def on_send_task(self, request: SendTaskRequest) -> SendTaskResponse:
        """Handle a send task request."""
        raise NotImplementedError()

    async def on_send_task_subscribe(
        self, request: SendTaskStreamingRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        """Handle a send task streaming request."""
        raise NotImplementedError()

    async def on_get_task(self, request: GetTaskRequest) -> GetTaskResponse:
        """Handle a get task request."""
        raise NotImplementedError()

    async def on_cancel_task(self, request: CancelTaskRequest) -> CancelTaskResponse:
        """Handle a cancel task request."""
        raise NotImplementedError()

    async def on_set_task_push_notification(
        self, request: SetTaskPushNotificationRequest
    ) -> SetTaskPushNotificationResponse:
        """Handle a set task push notification request."""
        raise NotImplementedError()

    async def on_get_task_push_notification(
        self, request: GetTaskPushNotificationRequest
    ) -> GetTaskPushNotificationResponse:
        """Handle a get task push notification request."""
        raise NotImplementedError()

    async def on_resubscribe_to_task(
        self, request: TaskResubscriptionRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        """Handle a resubscribe to task request."""
        raise NotImplementedError()


class InMemoryTaskManager(TaskManager):
    """A task manager that stores tasks in memory."""

    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.task_messages: Dict[str, List[Message]] = {}
        self.lock = asyncio.Lock()

    async def upsert_task(self, task_send_params: TaskSendParams) -> Task:
        """Create or update a task."""
        async with self.lock:
            task_id = task_send_params.id
            if task_id in self.tasks:
                # Update existing task
                task = self.tasks[task_id]
                # Add the new message to history
                if task.history is None:
                    task.history = []
                task.history.append(task_send_params.message)
                # Update status to submitted
                task.status = TaskStatus(state=TaskState.SUBMITTED)
            else:
                # Create new task
                task = Task(
                    id=task_id,
                    sessionId=task_send_params.sessionId,
                    status=TaskStatus(state=TaskState.SUBMITTED),
                    history=[task_send_params.message],
                    metadata=task_send_params.metadata,
                )
                self.tasks[task_id] = task
                self.task_messages[task_id] = [task_send_params.message]

            return task

    async def on_send_task(self, request: SendTaskRequest) -> SendTaskResponse:
        """Handle a send task request."""
        task_send_params = request.params
        await self.upsert_task(task_send_params)
        # This is a placeholder - subclasses should override this method
        return SendTaskResponse(
            id=request.id,
            result=Task(
                id=task_send_params.id,
                sessionId=task_send_params.sessionId,
                status=TaskStatus(state=TaskState.COMPLETED),
                history=[task_send_params.message],
                artifacts=[
                    Artifact(
                        parts=[
                            {
                                "type": "text",
                                "text": "This is a placeholder response. Override on_send_task in your TaskManager subclass.",
                            }
                        ]
                    )
                ],
            ),
        )

    async def on_send_task_subscribe(
        self, request: SendTaskStreamingRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        """Handle a send task streaming request."""
        return JSONRPCResponse(
            id=request.id,
            error=UnsupportedOperationError(
                message="Streaming is not supported by this task manager"
            ),
        )

    async def on_get_task(self, request: GetTaskRequest) -> GetTaskResponse:
        """Handle a get task request."""
        task_query_params: TaskQueryParams = request.params
        task_id = task_query_params.id

        async with self.lock:
            if task_id not in self.tasks:
                return GetTaskResponse(
                    id=request.id, error=TaskNotFoundError(data={"task_id": task_id})
                )

            task = self.tasks[task_id]
            if task_query_params.historyLength is not None and task.history:
                # Limit history length
                history_length = task_query_params.historyLength
                task.history = task.history[-history_length:]

            return GetTaskResponse(id=request.id, result=task)

    async def on_cancel_task(self, request: CancelTaskRequest) -> CancelTaskResponse:
        """Handle a cancel task request."""
        task_id_params: TaskIdParams = request.params
        task_id = task_id_params.id

        async with self.lock:
            if task_id not in self.tasks:
                return CancelTaskResponse(
                    id=request.id, error=TaskNotFoundError(data={"task_id": task_id})
                )

            task = self.tasks[task_id]
            if task.status.state in [
                TaskState.COMPLETED,
                TaskState.FAILED,
                TaskState.CANCELED,
            ]:
                return CancelTaskResponse(
                    id=request.id,
                    error=TaskNotCancelableError(
                        data={"task_id": task_id, "state": task.status.state}
                    ),
                )

            # Update task status to canceled
            task.status = TaskStatus(state=TaskState.CANCELED)
            return CancelTaskResponse(id=request.id, result=task)

    async def on_set_task_push_notification(
        self, request: SetTaskPushNotificationRequest
    ) -> SetTaskPushNotificationResponse:
        """Handle a set task push notification request."""
        return SetTaskPushNotificationResponse(
            id=request.id,
            error=PushNotificationNotSupportedError(),
        )

    async def on_get_task_push_notification(
        self, request: GetTaskPushNotificationRequest
    ) -> GetTaskPushNotificationResponse:
        """Handle a get task push notification request."""
        return GetTaskPushNotificationResponse(
            id=request.id,
            error=PushNotificationNotSupportedError(),
        )

    async def on_resubscribe_to_task(
        self, request: TaskResubscriptionRequest
    ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
        """Handle a resubscribe to task request."""
        return JSONRPCResponse(
            id=request.id,
            error=UnsupportedOperationError(
                message="Resubscription is not supported by this task manager"
            ),
        )
