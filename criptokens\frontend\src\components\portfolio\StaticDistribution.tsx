import React from 'react';

interface Asset {
  name: string;
  symbol: string;
  value: number;
  percentage: number;
  color: string;
}

interface StaticDistributionProps {
  assets: Asset[];
}

const StaticDistribution: React.FC<StaticDistributionProps> = ({ assets }) => {
  if (!assets || assets.length === 0) {
    return (
      <div className="static-distribution empty">
        <p>No hay datos disponibles</p>
      </div>
    );
  }

  return (
    <div className="static-distribution">
      {assets.map((asset, index) => (
        <div key={index} className="asset-bar">
          <div className="asset-info">
            <div className="asset-color" style={{ backgroundColor: asset.color }}></div>
            <span className="asset-name">{asset.symbol} ({asset.name})</span>
            <span className="asset-value">${asset.value.toLocaleString()} ({asset.percentage}%)</span>
          </div>
          <div className="asset-bar-container">
            <div 
              className="asset-bar-fill" 
              style={{ 
                width: `${asset.percentage}%`, 
                backgroundColor: asset.color 
              }}
            ></div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default StaticDistribution;
