import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useTopCryptocurrencies } from '../hooks/useMcpClient';
import '../styles/AnalysisPage.css';

const AnalysisPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('technical');
  const { data: cryptos, isLoading } = useTopCryptocurrencies();
  const [selectedCrypto, setSelectedCrypto] = useState<string>('bitcoin');

  return (
    <div className="analysis-page">
      <header className="analysis-header">
        <Link to="/" className="back-link">
          ← Volver al Dashboard
        </Link>
        <h1>Centro de Análisis</h1>
        <div className="analysis-actions">
          <select 
            className="crypto-selector"
            value={selectedCrypto}
            onChange={(e) => setSelectedCrypto(e.target.value)}
          >
            {isLoading ? (
              <option value="">Cargando...</option>
            ) : (
              cryptos?.map(crypto => (
                <option key={crypto.id} value={crypto.id}>
                  {crypto.name} ({crypto.symbol.toUpperCase()})
                </option>
              ))
            )}
          </select>
        </div>
      </header>

      <div className="analysis-tabs">
        <button 
          className={`tab-button ${activeTab === 'technical' ? 'active' : ''}`}
          onClick={() => setActiveTab('technical')}
        >
          Análisis Técnico
        </button>
        <button 
          className={`tab-button ${activeTab === 'fundamental' ? 'active' : ''}`}
          onClick={() => setActiveTab('fundamental')}
        >
          Análisis Fundamental
        </button>
        <button 
          className={`tab-button ${activeTab === 'calculators' ? 'active' : ''}`}
          onClick={() => setActiveTab('calculators')}
        >
          Calculadoras
        </button>
        <button 
          className={`tab-button ${activeTab === 'comparison' ? 'active' : ''}`}
          onClick={() => setActiveTab('comparison')}
        >
          Comparador
        </button>
      </div>

      <div className="analysis-content">
        {activeTab === 'technical' && (
          <div className="technical-analysis">
            <div className="chart-container">
              <div className="chart-placeholder">
                <h3>Gráfico Avanzado</h3>
                <p>Aquí se mostrará el gráfico de {selectedCrypto ? selectedCrypto : 'la criptomoneda seleccionada'}</p>
                <div className="placeholder-chart">
                  <div className="chart-line"></div>
                  <div className="chart-indicators">
                    <div className="indicator"></div>
                    <div className="indicator"></div>
                    <div className="indicator"></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="indicators-panel">
              <h3>Indicadores Técnicos</h3>
              <div className="indicators-grid">
                <div className="indicator-card">
                  <h4>RSI (14)</h4>
                  <div className="indicator-value">58.24</div>
                  <div className="indicator-status neutral">Neutral</div>
                </div>
                <div className="indicator-card">
                  <h4>MACD</h4>
                  <div className="indicator-value">+0.0012</div>
                  <div className="indicator-status positive">Compra</div>
                </div>
                <div className="indicator-card">
                  <h4>Media Móvil (50)</h4>
                  <div className="indicator-value">$42,350</div>
                  <div className="indicator-status positive">Por encima</div>
                </div>
                <div className="indicator-card">
                  <h4>Bandas de Bollinger</h4>
                  <div className="indicator-value">Medio</div>
                  <div className="indicator-status neutral">Neutral</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'fundamental' && (
          <div className="fundamental-analysis">
            <h3>Análisis Fundamental</h3>
            <p>Aquí encontrarás datos fundamentales sobre la criptomoneda seleccionada.</p>
            
            <div className="metrics-grid">
              <div className="metric-card">
                <h4>Capitalización de Mercado</h4>
                <div className="metric-value">$825.4B</div>
                <div className="metric-description">Ranking: #1</div>
              </div>
              <div className="metric-card">
                <h4>Volumen (24h)</h4>
                <div className="metric-value">$28.5B</div>
                <div className="metric-description">Ratio Vol/Cap: 3.45%</div>
              </div>
              <div className="metric-card">
                <h4>Suministro Circulante</h4>
                <div className="metric-value">19.4M BTC</div>
                <div className="metric-description">De 21M máximo</div>
              </div>
              <div className="metric-card">
                <h4>Dominancia</h4>
                <div className="metric-value">42.3%</div>
                <div className="metric-description">-0.8% (30d)</div>
              </div>
              <div className="metric-card">
                <h4>Desarrolladores Activos</h4>
                <div className="metric-value">52</div>
                <div className="metric-description">+5% (30d)</div>
              </div>
              <div className="metric-card">
                <h4>Transacciones (24h)</h4>
                <div className="metric-value">284,521</div>
                <div className="metric-description">+2.3% (7d)</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'calculators' && (
          <div className="calculators">
            <h3>Calculadoras</h3>
            <p>Herramientas para calcular inversiones, impuestos y más.</p>
            
            <div className="calculators-grid">
              <div className="calculator-card">
                <h4>Calculadora de ROI</h4>
                <p>Calcula el retorno de inversión de tus criptomonedas.</p>
                <button className="calculator-button">Abrir Calculadora</button>
              </div>
              <div className="calculator-card">
                <h4>Calculadora de Impuestos</h4>
                <p>Estima los impuestos sobre tus ganancias en criptomonedas.</p>
                <button className="calculator-button">Abrir Calculadora</button>
              </div>
              <div className="calculator-card">
                <h4>Calculadora de DCA</h4>
                <p>Planifica tu estrategia de inversión promediada.</p>
                <button className="calculator-button">Abrir Calculadora</button>
              </div>
              <div className="calculator-card">
                <h4>Calculadora de Staking</h4>
                <p>Calcula tus recompensas potenciales de staking.</p>
                <button className="calculator-button">Abrir Calculadora</button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'comparison' && (
          <div className="comparison-tool">
            <h3>Comparador de Criptomonedas</h3>
            <p>Compara diferentes criptomonedas lado a lado.</p>
            
            <div className="comparison-selectors">
              <div className="comparison-selector">
                <label>Criptomoneda 1</label>
                <select>
                  <option value="bitcoin">Bitcoin (BTC)</option>
                  <option value="ethereum">Ethereum (ETH)</option>
                  <option value="cardano">Cardano (ADA)</option>
                  <option value="solana">Solana (SOL)</option>
                </select>
              </div>
              <div className="comparison-vs">VS</div>
              <div className="comparison-selector">
                <label>Criptomoneda 2</label>
                <select>
                  <option value="ethereum">Ethereum (ETH)</option>
                  <option value="bitcoin">Bitcoin (BTC)</option>
                  <option value="cardano">Cardano (ADA)</option>
                  <option value="solana">Solana (SOL)</option>
                </select>
              </div>
            </div>
            
            <button className="compare-button">Comparar</button>
            
            <div className="comparison-placeholder">
              <p>Selecciona dos criptomonedas y haz clic en "Comparar" para ver un análisis detallado.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalysisPage;
