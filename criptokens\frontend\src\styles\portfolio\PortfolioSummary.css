/* Estilos para el componente de resumen del portafolio */

.portfolio-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

.summary-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: all var(--transition-normal);
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: rgba(255, 255, 255, 0.2);
}

.summary-card h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-dim);
}

.summary-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0.5rem 0;
  color: var(--text-bright);
}

.summary-subtitle {
  font-size: 0.875rem;
  color: var(--text-dim);
  margin: 0;
}

/* Estilos específicos para cada tarjeta */
.total-value .summary-value {
  color: var(--text-bright);
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.profit-loss .summary-value.positive {
  color: var(--success);
}

.profit-loss .summary-value.negative {
  color: var(--error);
}

.percentage {
  font-size: 1rem;
  margin-left: 0.5rem;
  opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
  .portfolio-summary {
    grid-template-columns: 1fr;
  }
  
  .summary-card {
    padding: 1.25rem;
  }
  
  .summary-value {
    font-size: 1.5rem;
  }
}
