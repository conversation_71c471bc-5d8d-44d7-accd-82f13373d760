import React, { useState } from 'react';
import { mcpService } from '../services/mcpService';
import '../styles/McpInterface.css';

/**
 * Componente que proporciona una interfaz para interactuar con el servidor MCP
 */
const McpInterface: React.FC = () => {
  // Estado para la herramienta seleccionada
  const [selectedTool, setSelectedTool] = useState<string>('getCryptoPrice');
  
  // Estados para los parámetros de las herramientas
  const [cryptoId, setCryptoId] = useState<string>('bitcoin');
  const [currency, setCurrency] = useState<string>('usd');
  const [limit, setLimit] = useState<number>(10);
  const [fromCrypto, setFromCrypto] = useState<string>('bitcoin');
  const [toCrypto, setToCrypto] = useState<string>('ethereum');
  const [amount, setAmount] = useState<number>(1);
  
  // Estado para la respuesta del servidor MCP
  const [response, setResponse] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Función para manejar el envío del formulario
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      let result: string;
      
      switch (selectedTool) {
        case 'getCryptoPrice':
          result = await mcpService.getCryptoPrice({ cryptoId, currency });
          break;
        case 'getTopCryptos':
          result = await mcpService.getTopCryptos({ limit, currency });
          break;
        case 'getMarketSentiment':
          result = await mcpService.getMarketSentiment();
          break;
        case 'getCryptoInfo':
          result = await mcpService.getCryptoInfo({ cryptoId });
          break;
        case 'convertCrypto':
          result = await mcpService.convertCrypto({ fromCrypto, toCrypto, amount });
          break;
        case 'getCryptoResource':
          result = await mcpService.getCryptoResource(cryptoId);
          break;
        default:
          throw new Error(`Herramienta no reconocida: ${selectedTool}`);
      }
      
      setResponse(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
      setResponse('');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Renderizar el formulario según la herramienta seleccionada
  const renderForm = () => {
    switch (selectedTool) {
      case 'getCryptoPrice':
        return (
          <>
            <div className="form-group">
              <label htmlFor="cryptoId">ID de la criptomoneda:</label>
              <input
                type="text"
                id="cryptoId"
                value={cryptoId}
                onChange={(e) => setCryptoId(e.target.value)}
                placeholder="bitcoin"
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="currency">Moneda:</label>
              <input
                type="text"
                id="currency"
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
                placeholder="usd"
              />
            </div>
          </>
        );
      
      case 'getTopCryptos':
        return (
          <>
            <div className="form-group">
              <label htmlFor="limit">Límite:</label>
              <input
                type="number"
                id="limit"
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value))}
                min="1"
                max="100"
              />
            </div>
            <div className="form-group">
              <label htmlFor="currency">Moneda:</label>
              <input
                type="text"
                id="currency"
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
                placeholder="usd"
              />
            </div>
          </>
        );
      
      case 'getMarketSentiment':
        return (
          <p className="form-info">
            Esta herramienta no requiere parámetros. Haz clic en "Ejecutar" para obtener el análisis de sentimiento del mercado.
          </p>
        );
      
      case 'getCryptoInfo':
        return (
          <div className="form-group">
            <label htmlFor="cryptoId">ID de la criptomoneda:</label>
            <input
              type="text"
              id="cryptoId"
              value={cryptoId}
              onChange={(e) => setCryptoId(e.target.value)}
              placeholder="bitcoin"
              required
            />
          </div>
        );
      
      case 'convertCrypto':
        return (
          <>
            <div className="form-group">
              <label htmlFor="fromCrypto">Desde criptomoneda:</label>
              <input
                type="text"
                id="fromCrypto"
                value={fromCrypto}
                onChange={(e) => setFromCrypto(e.target.value)}
                placeholder="bitcoin"
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="toCrypto">A criptomoneda:</label>
              <input
                type="text"
                id="toCrypto"
                value={toCrypto}
                onChange={(e) => setToCrypto(e.target.value)}
                placeholder="ethereum"
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="amount">Cantidad:</label>
              <input
                type="number"
                id="amount"
                value={amount}
                onChange={(e) => setAmount(parseFloat(e.target.value))}
                min="0.000001"
                step="0.000001"
                required
              />
            </div>
          </>
        );
      
      case 'getCryptoResource':
        return (
          <div className="form-group">
            <label htmlFor="cryptoId">ID de la criptomoneda:</label>
            <input
              type="text"
              id="cryptoId"
              value={cryptoId}
              onChange={(e) => setCryptoId(e.target.value)}
              placeholder="bitcoin"
              required
            />
          </div>
        );
      
      default:
        return <p>Herramienta no reconocida</p>;
    }
  };
  
  return (
    <div className="mcp-interface">
      <div className="mcp-header">
        <h2>Interfaz MCP para Criptokens</h2>
        <p>Utiliza el Protocolo de Contexto de Modelo para obtener información sobre criptomonedas</p>
      </div>
      
      <div className="mcp-content">
        <div className="mcp-tools">
          <h3>Herramientas disponibles</h3>
          
          <div className="tools-list">
            <button
              className={`tool-button ${selectedTool === 'getCryptoPrice' ? 'active' : ''}`}
              onClick={() => setSelectedTool('getCryptoPrice')}
            >
              Precio de criptomoneda
            </button>
            <button
              className={`tool-button ${selectedTool === 'getTopCryptos' ? 'active' : ''}`}
              onClick={() => setSelectedTool('getTopCryptos')}
            >
              Top criptomonedas
            </button>
            <button
              className={`tool-button ${selectedTool === 'getMarketSentiment' ? 'active' : ''}`}
              onClick={() => setSelectedTool('getMarketSentiment')}
            >
              Sentimiento del mercado
            </button>
            <button
              className={`tool-button ${selectedTool === 'getCryptoInfo' ? 'active' : ''}`}
              onClick={() => setSelectedTool('getCryptoInfo')}
            >
              Información detallada
            </button>
            <button
              className={`tool-button ${selectedTool === 'convertCrypto' ? 'active' : ''}`}
              onClick={() => setSelectedTool('convertCrypto')}
            >
              Convertir criptomonedas
            </button>
            <button
              className={`tool-button ${selectedTool === 'getCryptoResource' ? 'active' : ''}`}
              onClick={() => setSelectedTool('getCryptoResource')}
            >
              Recurso de criptomoneda
            </button>
          </div>
          
          <form onSubmit={handleSubmit} className="mcp-form">
            <div className="form-header">
              <h4>Parámetros para {selectedTool}</h4>
            </div>
            
            <div className="form-body">
              {renderForm()}
            </div>
            
            <div className="form-footer">
              <button type="submit" className="submit-button" disabled={isLoading}>
                {isLoading ? 'Ejecutando...' : 'Ejecutar'}
              </button>
            </div>
          </form>
        </div>
        
        <div className="mcp-response">
          <h3>Respuesta</h3>
          
          {error && (
            <div className="error-message">
              <p>Error: {error}</p>
            </div>
          )}
          
          {isLoading ? (
            <div className="loading-indicator">
              <div className="spinner"></div>
              <p>Consultando al servidor MCP...</p>
            </div>
          ) : response ? (
            <pre className="response-content">{response}</pre>
          ) : (
            <div className="empty-response">
              <p>Ejecuta una herramienta para ver la respuesta aquí</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default McpInterface;
