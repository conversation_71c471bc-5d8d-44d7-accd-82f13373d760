/* Estilos para el Dashboard Integrado */

.integrated-dashboard {
  display: flex;
  width: 100%;
  height: 100vh;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden;
}

/* Estilos para la barra lateral moderna */
.dashboard-sidebar {
  width: 220px;
  height: 100%;
  background-color: #0f1123;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

/* Efecto de gradiente para el fondo */
.dashboard-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(32, 41, 97, 0.2) 0%, rgba(11, 16, 35, 0.2) 100%);
  z-index: 0;
}

.sidebar-header {
  padding: 25px 20px;
  position: relative;
  z-index: 1;
}

.sidebar-header h1 {
  margin: 0;
  font-size: 28px;
  font-family: 'Orbitron', sans-serif;
  background: linear-gradient(45deg, #00e0ff, #00a3ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  text-shadow: 0 0 10px rgba(0, 224, 255, 0.3);
}

.sidebar-nav {
  flex: 1;
  padding: 15px 0;
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin: 5px 15px;
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.external-link {
  margin-left: auto;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.sidebar-nav li:hover .external-link {
  opacity: 1;
}

.sidebar-nav li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(0, 224, 255, 0.1) 0%, rgba(0, 163, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.sidebar-nav li:hover::before {
  opacity: 1;
}

.sidebar-nav li.active {
  background: linear-gradient(90deg, rgba(0, 224, 255, 0.2) 0%, rgba(0, 163, 255, 0.2) 100%);
  box-shadow: 0 4px 15px rgba(0, 224, 255, 0.15);
}

.sidebar-nav li.active::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: linear-gradient(to bottom, #00e0ff, #00a3ff);
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  margin-right: 12px;
  font-size: 20px;
  color: #00e0ff;
  transition: all 0.3s ease;
}

.sidebar-nav li:hover .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  font-weight: 500;
  letter-spacing: 0.5px;
}

.sidebar-footer {
  padding: 20px;
  position: relative;
  z-index: 1;
  margin-top: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.server-status {
  display: flex;
  align-items: center;
  background: rgba(15, 20, 40, 0.5);
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  width: 80%;
  justify-content: center;
  border: 1px solid rgba(0, 224, 255, 0.2);
  font-size: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.status-indicator.online .status-dot {
  background-color: #00ff9d;
  box-shadow: 0 0 10px rgba(0, 255, 157, 0.7);
}

.status-indicator.offline .status-dot {
  background-color: #ff3a6e;
  box-shadow: 0 0 10px rgba(255, 58, 110, 0.7);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  position: relative;
}

.status-dot::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: transparent;
  border: 2px solid currentColor;
  opacity: 0.5;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
    box-shadow: 0 0 0 0 rgba(0, 224, 255, 0.7);
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
    box-shadow: 0 0 0 10px rgba(0, 224, 255, 0);
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
    box-shadow: 0 0 0 0 rgba(0, 224, 255, 0);
  }
}



/* Estilos para el contenido principal */
.dashboard-main {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* Estilos para el botón de API */
.api-button {
  position: relative;
  z-index: 10;
  width: 80%;
  margin: 15px auto !important;
  overflow: hidden;
}

.api-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, rgba(0, 224, 255, 0.5), rgba(0, 163, 255, 0.5), rgba(0, 224, 255, 0.5));
  transform: translateX(-100%) translateY(-100%) rotate(45deg);
  animation: shine 3s infinite;
  z-index: -1;
}

.api-button:hover {
  transform: scale(1.05);
  background-color: rgba(0, 224, 255, 0.8) !important;
  box-shadow: 0 0 30px rgba(0, 224, 255, 1), inset 0 0 15px rgba(0, 224, 255, 0.6) !important;
}

.api-button:active {
  transform: scale(0.98);
  background-color: rgba(0, 224, 255, 0.9) !important;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  20%, 100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Estilos para la sección de API */
.api-content {
  width: 100%;
}

.api-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.api-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.api-section, .api-info {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.api-status {
  margin: 20px 0;
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(15, 20, 40, 0.5);
}

.api-status.online .status-indicator .status-dot {
  background-color: #00ff9d;
  box-shadow: 0 0 10px rgba(0, 255, 157, 0.7);
}

.api-status.offline .status-indicator .status-dot {
  background-color: #ff3a6e;
  box-shadow: 0 0 10px rgba(255, 58, 110, 0.7);
}

.api-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.api-actions button {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.connect-button {
  background-color: rgba(0, 224, 255, 0.2);
  color: #00e0ff;
  border: 1px solid rgba(0, 224, 255, 0.5) !important;
}

.connect-button:hover {
  background-color: rgba(0, 224, 255, 0.3);
}

.test-button {
  background-color: rgba(0, 255, 157, 0.2);
  color: #00ff9d;
  border: 1px solid rgba(0, 255, 157, 0.5) !important;
}

.test-button:hover {
  background-color: rgba(0, 255, 157, 0.3);
}

.api-details {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  margin-top: 20px;
}

.api-detail-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: rgba(15, 20, 40, 0.3);
  border-radius: 8px;
}

.detail-label {
  font-weight: 600;
  color: var(--text-secondary);
}

.detail-value {
  color: #00e0ff;
  font-weight: 500;
}

/* Estilos para el encabezado del dashboard */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--input-bg);
  border-radius: 20px;
  padding: 5px 15px;
  margin-right: 10px;
}

.search-bar input {
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-color);
  width: 200px;
  padding: 5px;
}

.search-button, .refresh-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: var(--text-color);
  transition: all 0.2s ease;
}

.search-button:hover, .refresh-button:hover {
  color: var(--primary-color);
}

.refresh-button {
  background-color: var(--input-bg);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Estilos para la cuadrícula del dashboard */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: auto auto;
  gap: 20px;
  grid-template-areas:
    "market-info selected-crypto selected-crypto"
    "crypto-list selected-crypto selected-crypto"
    "agent-panel agent-panel agent-panel";
}

.market-info-panel {
  grid-area: market-info;
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.selected-crypto-panel {
  grid-area: selected-crypto;
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.crypto-list-panel {
  grid-area: crypto-list;
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 500px;
  overflow-y: auto;
}

.agent-panel {
  grid-area: agent-panel;
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Estilos para la información del mercado */
.market-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
}

/* Estilos para la criptomoneda seleccionada */
.crypto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.crypto-title {
  display: flex;
  align-items: center;
}

.crypto-icon {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.crypto-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price-value {
  font-size: 24px;
  font-weight: 700;
}

.price-change {
  font-size: 14px;
  font-weight: 600;
}

.time-range-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.time-range-selector button {
  background-color: var(--button-bg);
  border: none;
  color: var(--text-color);
  padding: 5px 15px;
  margin: 0 5px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-range-selector button:hover {
  background-color: var(--button-hover);
}

.time-range-selector button.active {
  background-color: var(--primary-color);
  color: white;
}

.chart-container {
  width: 100%;
  height: 300px;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.crypto-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

/* Estilos para la lista de criptomonedas */
.crypto-list {
  display: flex;
  flex-direction: column;
}

.crypto-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.crypto-list-item:hover {
  background-color: var(--hover-color);
}

.crypto-list-item.selected {
  background-color: var(--active-bg);
}

.crypto-item-left {
  display: flex;
  align-items: center;
}

.crypto-icon-small {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.crypto-item-name {
  display: flex;
  flex-direction: column;
}

.crypto-name {
  font-weight: 600;
}

.crypto-symbol {
  font-size: 12px;
  color: var(--text-secondary);
}

.crypto-item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.view-all-button {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.view-all-button button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-button button:hover {
  background-color: var(--primary-dark);
}

/* Estilos para el agente virtual */
.agent-container {
  display: flex;
  align-items: center;
}

.agent-message {
  background-color: var(--input-bg);
  padding: 10px 15px;
  border-radius: 10px;
  margin-left: 15px;
  max-width: 70%;
}

.agent-actions {
  display: flex;
}

.agent-actions button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.agent-actions button:hover {
  background-color: var(--primary-dark);
}

/* Estilos para la sección de mercado */
.market-content {
  width: 100%;
}

.market-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  background-color: var(--button-bg);
  border: none;
  color: var(--text-color);
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: var(--button-hover);
}

.market-table {
  width: 100%;
  overflow-x: auto;
}

.market-table table {
  width: 100%;
  border-collapse: collapse;
}

.market-table th {
  text-align: left;
  padding: 12px 15px;
  background-color: var(--table-header-bg);
  color: var(--text-color);
  font-weight: 600;
}

.market-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
}

.market-table tr:hover {
  background-color: var(--hover-color);
  cursor: pointer;
}

/* Estilos para el botón de watchlist */
.watchlist-button {
  background-color: rgba(0, 224, 255, 0.08);
  border: 1px solid rgba(0, 224, 255, 0.15);
  border-radius: 50%;
  color: #00e0ff;
  width: 32px;
  height: 32px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.watchlist-button:hover {
  background-color: rgba(0, 224, 255, 0.12);
  border-color: rgba(0, 224, 255, 0.25);
  transform: scale(1.08);
  box-shadow: 0 2px 5px rgba(0, 224, 255, 0.2);
}

.watchlist-button.in-watchlist {
  background-color: rgba(0, 255, 157, 0.08);
  border: 1px solid rgba(0, 255, 157, 0.2);
  color: #00ff9d;
  box-shadow: 0 1px 3px rgba(0, 255, 157, 0.15);
}

.watchlist-button.in-watchlist:hover {
  background-color: rgba(0, 255, 157, 0.12);
  border-color: rgba(0, 255, 157, 0.25);
  transform: scale(1.08);
  box-shadow: 0 2px 5px rgba(0, 255, 157, 0.2);
}

.crypto-name-cell {
  display: flex;
  align-items: center;
}

/* Estilos para la sección de portafolio */
.portfolio-content {
  width: 100%;
}

.portfolio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* Estilos para la sección de watchlist */
.watchlist-content {
  width: 100%;
}

.watchlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* Estilos para la sección de alertas */
.alerts-content {
  width: 100%;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* Estilos para la sección del asistente */
.assistant-content {
  width: 100%;
}

.assistant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.assistant-container {
  display: flex;
  height: calc(100vh - 150px);
}

.assistant-avatar {
  width: 200px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 30px;
}

.chat-container {
  flex: 1;
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chat-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
}

/* Estilos para los estados de carga */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--text-secondary);
}

/* Estilos responsivos */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
      "market-info selected-crypto"
      "crypto-list selected-crypto"
      "agent-panel agent-panel";
  }
}

@media (max-width: 768px) {
  .integrated-dashboard {
    flex-direction: column;
  }

  .dashboard-sidebar {
    width: 100%;
    height: auto;
  }

  .sidebar-nav ul {
    display: flex;
    overflow-x: auto;
  }

  .sidebar-nav li {
    padding: 10px 15px;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .sidebar-nav li.active {
    border-left: none;
    border-bottom: 3px solid var(--primary-color);
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    grid-template-areas:
      "market-info"
      "selected-crypto"
      "crypto-list"
      "agent-panel";
  }

  .crypto-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
