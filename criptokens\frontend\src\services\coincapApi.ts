import axios from 'axios';
import { mockTopCryptos, mockGlobalData, mockHistoricalData, generateHistoricalData } from './mockData';

// API base URL para CoinCap
const COINCAP_API_URL = 'https://api.coincap.io/v2';

// Crear una instancia de axios con la URL base
const api = axios.create({
  baseURL: COINCAP_API_URL,
  timeout: 10000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  }
});

// Sistema de caché simple
const cache = {
  data: {} as Record<string, any>,
  timestamps: {} as Record<string, number>,
  // Tiempo de caché en milisegundos (60 segundos)
  cacheDuration: 60 * 1000
};

// Control de tasa de solicitudes
const rateLimiter = {
  lastRequestTime: 0,
  // Tiempo mínimo entre solicitudes en milisegundos (1 segundo)
  minRequestInterval: 1000
};

// Función para obtener datos de la API de CoinCap con caché y control de tasa
const fetchFromCoinCap = async (endpoint: string, params: Record<string, any> = {}) => {
  try {
    // Construir la URL con los parámetros
    const url = new URL(`${COINCAP_API_URL}${endpoint}`);
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key].toString());
    });

    const cacheKey = url.toString();

    // Verificar si tenemos datos en caché y si son válidos
    const now = Date.now();
    if (
      cache.data[cacheKey] &&
      cache.timestamps[cacheKey] &&
      (now - cache.timestamps[cacheKey]) < cache.cacheDuration
    ) {
      console.log(`Usando datos en caché para: ${cacheKey}`);
      return cache.data[cacheKey];
    }

    // Controlar la tasa de solicitudes
    const timeSinceLastRequest = now - rateLimiter.lastRequestTime;
    if (timeSinceLastRequest < rateLimiter.minRequestInterval) {
      const waitTime = rateLimiter.minRequestInterval - timeSinceLastRequest;
      console.log(`Esperando ${waitTime}ms antes de la siguiente solicitud...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    console.log(`Fetching data from CoinCap: ${url.toString()}`);

    // Actualizar el tiempo de la última solicitud
    rateLimiter.lastRequestTime = Date.now();

    // Realizar la petición
    const response = await axios.get(url.toString(), {
      timeout: 5000, // Timeout de 5 segundos
      headers: {
        'User-Agent': 'Criptokens/1.0'
      }
    });

    // Guardar en caché
    cache.data[cacheKey] = response.data;
    cache.timestamps[cacheKey] = Date.now();

    return response.data;
  } catch (error) {
    console.error(`Error al obtener datos de CoinCap:`, error);

    // Si hay un error, intentar usar datos en caché aunque estén caducados
    const cacheKey = new URL(`${COINCAP_API_URL}${endpoint}`).toString();
    if (cache.data[cacheKey]) {
      console.log(`Usando datos en caché caducados como fallback para: ${cacheKey}`);
      return cache.data[cacheKey];
    }

    throw error;
  }
};

// Función para obtener las principales criptomonedas
export const getTopCryptocurrencies = async (limit = 10, page = 1) => {
  try {
    // CoinCap no soporta paginación como CoinGecko, así que obtenemos todos y filtramos
    const data = await fetchFromCoinCap('/assets');

    // Calcular índices para la paginación manual
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    // Filtrar y formatear los datos para que sean compatibles con el frontend
    const formattedData = data.data
      .slice(startIndex, endIndex)
      .map((asset: any) => {
        const changePercent24Hr = parseFloat(asset.changePercent24Hr);
        const price = parseFloat(asset.priceUsd);

        return {
          id: asset.id,
          name: asset.name,
          symbol: asset.symbol.toLowerCase(),
          current_price: price,
          price_change_percentage_24h: changePercent24Hr,
          market_cap: parseFloat(asset.marketCapUsd),
          total_volume: parseFloat(asset.volumeUsd24Hr),
          circulating_supply: parseFloat(asset.supply),
          image: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
          last_updated: new Date().toISOString(),
          // Datos adicionales estimados
          ath: price * 1.5, // Estimación del ATH
          sparkline_in_7d: { price: Array(7).fill(0).map(() => price * (0.9 + Math.random() * 0.2)) }
        };
      });

    return formattedData;
  } catch (error) {
    console.error('Error al obtener las principales criptomonedas:', error);
    // Fallback a datos simulados
    return mockTopCryptos.slice(0, limit);
  }
};

// Función para obtener datos históricos de una criptomoneda
export const getCryptoHistoricalData = async (cryptoId: string, days: number | string = 7) => {
  try {
    // CoinCap tiene un endpoint diferente para datos históricos
    // Calculamos el intervalo de tiempo en milisegundos
    const now = Date.now();
    const daysNum = typeof days === 'number' ? days : days === 'max' ? 365 : parseInt(days as string);
    const start = now - (daysNum * 24 * 60 * 60 * 1000);

    // Obtener datos históricos de CoinCap
    const data = await fetchFromCoinCap(`/assets/${cryptoId}/history`, {
      interval: 'd1', // Intervalo diario
      start: start,
      end: now
    });

    // Formatear los datos para que sean compatibles con el formato de CoinGecko
    const prices: [number, number][] = [];
    const market_caps: [number, number][] = [];
    const total_volumes: [number, number][] = [];

    // CoinCap devuelve los datos en un formato diferente
    if (data.data && Array.isArray(data.data)) {
      data.data.forEach((item: any) => {
        const timestamp = new Date(item.time).getTime();
        const price = parseFloat(item.priceUsd);
        const marketCap = parseFloat(item.marketCapUsd || '0');
        const volume = parseFloat(item.volumeUsd || '0');

        prices.push([timestamp, price]);
        market_caps.push([timestamp, marketCap]);
        total_volumes.push([timestamp, volume]);
      });
    }

    return {
      prices,
      market_caps,
      total_volumes
    };
  } catch (error) {
    console.error(`Error al obtener datos históricos para ${cryptoId}:`, error);
    // Fallback a datos simulados
    if (mockHistoricalData[cryptoId as keyof typeof mockHistoricalData]) {
      return mockHistoricalData[cryptoId as keyof typeof mockHistoricalData];
    } else {
      const basePrice = cryptoId === 'bitcoin' ? 60000 :
                       cryptoId === 'ethereum' ? 3500 :
                       cryptoId === 'binancecoin' ? 600 :
                       cryptoId === 'solana' ? 140 :
                       cryptoId === 'ripple' ? 0.54 :
                       cryptoId === 'cardano' ? 0.45 : 100;
      
      const volatility = basePrice * 0.1; // 10% de volatilidad
      return generateHistoricalData(typeof days === 'number' ? days : 30, basePrice, volatility);
    }
  }
};

// Función para obtener los detalles de una criptomoneda
export const getCryptoDetails = async (cryptoId: string) => {
  try {
    // Usar la API de CoinCap para obtener datos detallados
    const data = await fetchFromCoinCap(`/assets/${cryptoId}`);

    // CoinCap devuelve los datos en un formato diferente
    const asset = data.data;

    // Calcular el cambio porcentual en 24h
    const changePercent24Hr = parseFloat(asset.changePercent24Hr);
    const price = parseFloat(asset.priceUsd);

    return {
      id: asset.id,
      name: asset.name,
      symbol: asset.symbol.toUpperCase(),
      current_price: price,
      price_change_percentage_24h: changePercent24Hr,
      market_cap: parseFloat(asset.marketCapUsd),
      total_volume: parseFloat(asset.volumeUsd24Hr),
      circulating_supply: parseFloat(asset.supply),
      image: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
      last_updated: new Date().toISOString(),
      // Datos adicionales para compatibilidad con el frontend
      market_data: {
        current_price: { usd: price },
        price_change_percentage_24h: changePercent24Hr,
        market_cap: { usd: parseFloat(asset.marketCapUsd) },
        total_volume: { usd: parseFloat(asset.volumeUsd24Hr) },
        high_24h: { usd: price * (1 + Math.abs(changePercent24Hr) / 100) }, // Estimación
        low_24h: { usd: price * (1 - Math.abs(changePercent24Hr) / 100) } // Estimación
      }
    };
  } catch (error) {
    console.error(`Error al obtener detalles para ${cryptoId}:`, error);
    // Fallback a datos simulados
    const crypto = mockTopCryptos.find(c => c.id === cryptoId);
    return crypto || mockTopCryptos[0];
  }
};

// Función para obtener los datos del mercado global
export const getGlobalMarketData = async () => {
  try {
    // CoinCap no tiene un endpoint específico para datos globales,
    // así que calculamos algunos datos a partir de las principales criptomonedas
    const topCryptos = await getTopCryptocurrencies(20);
    
    // Calcular la capitalización total del mercado sumando las principales criptomonedas
    let totalMarketCap = 0;
    let totalVolume = 0;
    const marketCapPercentage: Record<string, number> = {};
    
    topCryptos.forEach(crypto => {
      totalMarketCap += crypto.market_cap || 0;
      totalVolume += crypto.total_volume || 0;
      
      // Calcular el porcentaje de dominancia
      if (crypto.market_cap) {
        marketCapPercentage[crypto.symbol] = 0; // Lo calcularemos después
      }
    });
    
    // Calcular los porcentajes de dominancia
    Object.keys(marketCapPercentage).forEach(symbol => {
      const crypto = topCryptos.find(c => c.symbol === symbol);
      if (crypto && crypto.market_cap) {
        marketCapPercentage[symbol] = (crypto.market_cap / totalMarketCap) * 100;
      }
    });
    
    // Crear un objeto con el formato esperado por el frontend
    return {
      data: {
        active_cryptocurrencies: 10000, // Valor estimado
        markets: 800, // Valor estimado
        total_market_cap: {
          usd: totalMarketCap
        },
        total_volume: {
          usd: totalVolume
        },
        market_cap_percentage: marketCapPercentage,
        market_cap_change_percentage_24h_usd: 0, // No tenemos este dato
        updated_at: Date.now() / 1000
      }
    };
  } catch (error) {
    console.error('Error al obtener datos del mercado global:', error);
    // Fallback a datos simulados
    return mockGlobalData;
  }
};

// Función para buscar criptomonedas
export const searchCryptos = async (query: string) => {
  try {
    // CoinCap no tiene un endpoint de búsqueda, así que obtenemos todas las criptomonedas y filtramos
    const data = await fetchFromCoinCap('/assets');

    // Filtrar por nombre o símbolo
    const searchTerm = query.toLowerCase();
    const filteredAssets = data.data.filter((asset: any) =>
      asset.id.toLowerCase().includes(searchTerm) ||
      asset.name.toLowerCase().includes(searchTerm) ||
      asset.symbol.toLowerCase().includes(searchTerm)
    );

    // Formatear los resultados para que sean compatibles con el formato de CoinGecko
    return {
      coins: filteredAssets.map((asset: any) => ({
        id: asset.id,
        name: asset.name,
        symbol: asset.symbol.toLowerCase(),
        market_cap_rank: parseInt(asset.rank),
        thumb: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`,
        large: `https://assets.coincap.io/assets/icons/${asset.symbol.toLowerCase()}@2x.png`
      })),
      exchanges: [],
      icos: [],
      categories: [],
      nfts: []
    };
  } catch (error) {
    console.error('Error al buscar criptomonedas:', error);
    // Fallback a datos simulados
    return {
      coins: mockTopCryptos.filter(crypto =>
        crypto.name.toLowerCase().includes(query.toLowerCase()) ||
        crypto.symbol.toLowerCase().includes(query.toLowerCase())
      )
    };
  }
};

export default {
  getTopCryptocurrencies,
  getCryptoHistoricalData,
  getCryptoDetails,
  getGlobalMarketData,
  searchCryptos
};
