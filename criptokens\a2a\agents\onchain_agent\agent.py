import json
from typing import Dict, Any, AsyncIterable, List, Optional
import logging
import aiohttp
import os
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)

class OnChainAnalysisAgent:
    """Agent for on-chain analysis of cryptocurrency data."""
    
    SUPPORTED_CONTENT_TYPES = ["text", "data"]
    
    def __init__(self):
        self.etherscan_api_key = os.getenv("ETHERSCAN_API_KEY", "**********************************")
    
    async def fetch_whale_transactions(self, crypto: str, days: int = 7) -> List[Dict[str, Any]]:
        """Fetch large transactions (whale movements).
        
        Args:
            crypto: The cryptocurrency to analyze
            days: Number of days of data to fetch
            
        Returns:
            List of whale transactions
        """
        try:
            # For Ethereum, we can use Etherscan API
            if crypto.lower() in ["ethereum", "eth"]:
                return await self._fetch_eth_whale_transactions(days)
            
            # For other cryptocurrencies, use simulated data
            return self._generate_simulated_whale_transactions(crypto, days)
        except Exception as e:
            logger.error(f"Error fetching whale transactions: {e}")
            return self._generate_simulated_whale_transactions(crypto, days)
    
    async def _fetch_eth_whale_transactions(self, days: int) -> List[Dict[str, Any]]:
        """Fetch Ethereum whale transactions from Etherscan.
        
        Args:
            days: Number of days of data to fetch
            
        Returns:
            List of Ethereum whale transactions
        """
        try:
            # Define whale threshold (in ETH)
            whale_threshold = 100  # Transactions > 100 ETH
            
            # Calculate start timestamp
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            start_timestamp = int(start_date.timestamp())
            end_timestamp = int(end_date.timestamp())
            
            # Etherscan API endpoint for transactions
            url = "https://api.etherscan.io/api"
            
            # We'll need to make multiple requests to get a comprehensive view
            # This is a simplified implementation
            params = {
                "module": "account",
                "action": "txlist",
                "address": "******************************************",  # Binance hot wallet as example
                "startblock": 0,
                "endblock": ********,
                "sort": "desc",
                "apikey": self.etherscan_api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("status") == "1" and "result" in result:
                            transactions = result["result"]
                            
                            # Filter for whale transactions
                            whale_txs = []
                            for tx in transactions:
                                # Convert value from wei to ETH
                                value_eth = float(tx.get("value", "0")) / 1e18
                                if value_eth > whale_threshold:
                                    timestamp = int(tx.get("timeStamp", "0"))
                                    if start_timestamp <= timestamp <= end_timestamp:
                                        whale_txs.append({
                                            "hash": tx.get("hash"),
                                            "from": tx.get("from"),
                                            "to": tx.get("to"),
                                            "value": value_eth,
                                            "timestamp": timestamp,
                                            "date": datetime.fromtimestamp(timestamp).isoformat()
                                        })
                            
                            return whale_txs[:20]  # Limit to 20 transactions
            
            # If we reach here, something went wrong
            logger.warning("Failed to fetch Ethereum whale transactions from Etherscan")
            return self._generate_simulated_whale_transactions("ethereum", days)
        except Exception as e:
            logger.error(f"Error fetching Ethereum whale transactions: {e}")
            return self._generate_simulated_whale_transactions("ethereum", days)
    
    def _generate_simulated_whale_transactions(self, crypto: str, days: int) -> List[Dict[str, Any]]:
        """Generate simulated whale transactions.
        
        Args:
            crypto: The cryptocurrency
            days: Number of days of data to generate
            
        Returns:
            List of simulated whale transactions
        """
        import random
        
        # Define crypto-specific parameters
        crypto_params = {
            "bitcoin": {"symbol": "BTC", "min_value": 10, "max_value": 1000},
            "ethereum": {"symbol": "ETH", "min_value": 100, "max_value": 5000},
            "binance coin": {"symbol": "BNB", "min_value": 1000, "max_value": 10000},
            "cardano": {"symbol": "ADA", "min_value": 100000, "max_value": 1000000},
            "solana": {"symbol": "SOL", "min_value": 1000, "max_value": 50000},
            "xrp": {"symbol": "XRP", "min_value": 100000, "max_value": 1000000},
            "dogecoin": {"symbol": "DOGE", "min_value": 1000000, "max_value": 10000000},
        }
        
        # Default parameters if crypto not found
        params = crypto_params.get(crypto.lower(), {"symbol": crypto.upper(), "min_value": 100, "max_value": 1000})
        
        # Generate random transactions
        transactions = []
        end_date = datetime.now()
        
        # Generate 20 transactions
        for i in range(20):
            # Random date within the specified days
            days_ago = random.uniform(0, days)
            date = end_date - timedelta(days=days_ago)
            timestamp = int(date.timestamp())
            
            # Random value
            value = random.uniform(params["min_value"], params["max_value"])
            
            # Random addresses
            from_address = "0x" + "".join(random.choices("0123456789abcdef", k=40))
            to_address = "0x" + "".join(random.choices("0123456789abcdef", k=40))
            
            # Transaction hash
            tx_hash = "0x" + "".join(random.choices("0123456789abcdef", k=64))
            
            transactions.append({
                "hash": tx_hash,
                "from": from_address,
                "to": to_address,
                "value": value,
                "symbol": params["symbol"],
                "timestamp": timestamp,
                "date": date.isoformat(),
                "_simulated": True
            })
        
        # Sort by timestamp (newest first)
        transactions.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return transactions
    
    async def analyze_whale_activity(self, transactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze whale activity from transactions.
        
        Args:
            transactions: List of whale transactions
            
        Returns:
            Analysis of whale activity
        """
        if not transactions:
            return {
                "error": "No whale transactions found"
            }
        
        # Calculate total inflows and outflows
        # For simplicity, we'll consider transactions to exchanges as outflows
        # and transactions from exchanges as inflows
        exchange_addresses = [
            "******************************************",  # Binance
            "******************************************",  # Binance 2
            "******************************************",  # Tether
            "******************************************",  # FTX
            "******************************************"   # Coinbase
        ]
        
        inflows = 0
        outflows = 0
        
        for tx in transactions:
            value = tx.get("value", 0)
            from_address = tx.get("from", "").lower()
            to_address = tx.get("to", "").lower()
            
            # Check if transaction is to/from an exchange
            is_to_exchange = any(to_address == addr.lower() for addr in exchange_addresses)
            is_from_exchange = any(from_address == addr.lower() for addr in exchange_addresses)
            
            if is_to_exchange:
                outflows += value
            elif is_from_exchange:
                inflows += value
            else:
                # For non-exchange transactions, use a heuristic
                # Transactions between whales are considered neutral
                if random.random() < 0.5:
                    inflows += value
                else:
                    outflows += value
        
        # Calculate net flow
        net_flow = inflows - outflows
        
        # Calculate on-chain sentiment (-100 to +100)
        max_flow = max(inflows, outflows)
        if max_flow > 0:
            sentiment = (net_flow / max_flow) * 100
        else:
            sentiment = 0
        
        return {
            "whale_inflows": round(inflows, 2),
            "whale_outflows": round(outflows, 2),
            "net_whale_flow": round(net_flow, 2),
            "on_chain_sentiment": round(sentiment, 2),
            "transaction_count": len(transactions)
        }
    
    async def fetch_active_addresses(self, crypto: str, days: int = 7) -> Dict[str, Any]:
        """Fetch active addresses data.
        
        Args:
            crypto: The cryptocurrency
            days: Number of days of data
            
        Returns:
            Active addresses data
        """
        # This would normally call an API, but we'll simulate for now
        return self._generate_simulated_active_addresses(crypto, days)
    
    def _generate_simulated_active_addresses(self, crypto: str, days: int) -> Dict[str, Any]:
        """Generate simulated active addresses data.
        
        Args:
            crypto: The cryptocurrency
            days: Number of days of data
            
        Returns:
            Simulated active addresses data
        """
        import random
        import numpy as np
        
        # Base number of active addresses by crypto
        base_addresses = {
            "bitcoin": 1000000,
            "ethereum": 500000,
            "binance coin": 200000,
            "cardano": 300000,
            "solana": 250000,
            "xrp": 150000,
            "dogecoin": 400000
        }
        
        # Get base number for this crypto
        base = base_addresses.get(crypto.lower(), 100000)
        
        # Generate daily active addresses
        daily_data = []
        end_date = datetime.now()
        
        # Create a smooth trend with some randomness
        trend = np.linspace(-0.2, 0.2, days)  # Trend from -20% to +20%
        
        for i in range(days):
            date = end_date - timedelta(days=days-i-1)
            
            # Calculate active addresses with trend and randomness
            active = int(base * (1 + trend[i] + random.uniform(-0.1, 0.1)))
            
            daily_data.append({
                "date": date.strftime("%Y-%m-%d"),
                "active_addresses": active
            })
        
        # Calculate average and change
        current = daily_data[-1]["active_addresses"]
        previous = daily_data[0]["active_addresses"] if days > 1 else current
        average = sum(day["active_addresses"] for day in daily_data) / len(daily_data)
        change_pct = ((current - previous) / previous) * 100 if previous > 0 else 0
        
        return {
            "current_active_addresses": current,
            "average_active_addresses": int(average),
            "change_percentage": round(change_pct, 2),
            "daily_data": daily_data
        }
    
    async def fetch_network_data(self, crypto: str) -> Dict[str, Any]:
        """Fetch network data (fees, hashrate, etc.).
        
        Args:
            crypto: The cryptocurrency
            
        Returns:
            Network data
        """
        # This would normally call an API, but we'll simulate for now
        return self._generate_simulated_network_data(crypto)
    
    def _generate_simulated_network_data(self, crypto: str) -> Dict[str, Any]:
        """Generate simulated network data.
        
        Args:
            crypto: The cryptocurrency
            
        Returns:
            Simulated network data
        """
        import random
        
        # Base parameters by crypto
        network_params = {
            "bitcoin": {
                "avg_fee": random.uniform(1, 5),
                "fee_unit": "USD",
                "hashrate": random.uniform(100, 200),
                "hashrate_unit": "EH/s",
                "difficulty": random.uniform(30, 40),
                "difficulty_unit": "T"
            },
            "ethereum": {
                "avg_fee": random.uniform(5, 20),
                "fee_unit": "USD",
                "hashrate": random.uniform(500, 1000),
                "hashrate_unit": "TH/s",
                "difficulty": random.uniform(10, 15),
                "difficulty_unit": "P"
            }
        }
        
        # Get parameters for this crypto or use defaults
        params = network_params.get(crypto.lower(), {
            "avg_fee": random.uniform(0.1, 1),
            "fee_unit": "USD",
            "hashrate": random.uniform(10, 50),
            "hashrate_unit": "TH/s",
            "difficulty": random.uniform(1, 5),
            "difficulty_unit": "P"
        })
        
        return {
            "average_transaction_fee": round(params["avg_fee"], 2),
            "fee_unit": params["fee_unit"],
            "network_hashrate": round(params["hashrate"], 2),
            "hashrate_unit": params["hashrate_unit"],
            "network_difficulty": round(params["difficulty"], 2),
            "difficulty_unit": params["difficulty_unit"],
            "timestamp": datetime.now().isoformat()
        }
    
    async def process_query(self, query: str, session_id: str) -> Dict[str, Any]:
        """Process a query and return on-chain analysis.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            On-chain analysis results
        """
        # Extract crypto name from query
        crypto = self._extract_crypto_name(query)
        if not crypto:
            return {
                "error": "Could not determine which cryptocurrency to analyze. Please specify a cryptocurrency."
            }
        
        # Extract timeframe from query
        days = self._extract_timeframe(query)
        
        # Fetch whale transactions
        whale_transactions = await self.fetch_whale_transactions(crypto, days)
        
        # Analyze whale activity
        whale_analysis = await self.analyze_whale_activity(whale_transactions)
        
        # Fetch active addresses data
        active_addresses = await self.fetch_active_addresses(crypto, days)
        
        # Fetch network data
        network_data = await self.fetch_network_data(crypto)
        
        # Generate summary
        summary = self._generate_onchain_summary(
            crypto, whale_analysis, active_addresses, network_data
        )
        
        return {
            "crypto": crypto,
            "timeframe_days": days,
            "whale_analysis": whale_analysis,
            "active_addresses": active_addresses,
            "network_data": network_data,
            "summary": summary,
            "recent_whale_transactions": whale_transactions[:5]  # Include top 5 transactions
        }
    
    def _extract_crypto_name(self, query: str) -> Optional[str]:
        """Extract cryptocurrency name from query.
        
        Args:
            query: The user's query
            
        Returns:
            Cryptocurrency name or None if not found
        """
        # Common cryptocurrencies
        crypto_names = {
            "bitcoin": "Bitcoin",
            "btc": "Bitcoin",
            "ethereum": "Ethereum",
            "eth": "Ethereum",
            "binance coin": "Binance Coin",
            "bnb": "Binance Coin",
            "cardano": "Cardano",
            "ada": "Cardano",
            "solana": "Solana",
            "sol": "Solana",
            "xrp": "XRP",
            "dogecoin": "Dogecoin",
            "doge": "Dogecoin",
            "polkadot": "Polkadot",
            "dot": "Polkadot"
        }
        
        query_lower = query.lower()
        
        # Check for exact matches
        for crypto_name_lower, crypto_name in crypto_names.items():
            if crypto_name_lower in query_lower:
                return crypto_name
        
        # Default to Bitcoin if no match found
        return "Bitcoin"
    
    def _extract_timeframe(self, query: str) -> int:
        """Extract timeframe from query.
        
        Args:
            query: The user's query
            
        Returns:
            Number of days for analysis
        """
        query_lower = query.lower()
        
        if "year" in query_lower or "365" in query_lower:
            return 365
        elif "6 month" in query_lower or "180" in query_lower:
            return 180
        elif "3 month" in query_lower or "90" in query_lower:
            return 90
        elif "month" in query_lower or "30" in query_lower:
            return 30
        elif "week" in query_lower or "7" in query_lower:
            return 7
        elif "day" in query_lower or "24" in query_lower:
            return 1
        
        # Default to 7 days
        return 7
    
    def _generate_onchain_summary(
        self, 
        crypto: str, 
        whale_analysis: Dict[str, Any],
        active_addresses: Dict[str, Any],
        network_data: Dict[str, Any]
    ) -> str:
        """Generate a summary of on-chain analysis.
        
        Args:
            crypto: Cryptocurrency name
            whale_analysis: Whale activity analysis
            active_addresses: Active addresses data
            network_data: Network data
            
        Returns:
            Summary text
        """
        if "error" in whale_analysis:
            return f"Error in on-chain analysis: {whale_analysis['error']}"
        
        # Extract key metrics
        whale_inflows = whale_analysis.get("whale_inflows", 0)
        whale_outflows = whale_analysis.get("whale_outflows", 0)
        net_whale_flow = whale_analysis.get("net_whale_flow", 0)
        on_chain_sentiment = whale_analysis.get("on_chain_sentiment", 0)
        
        active_now = active_addresses.get("current_active_addresses", 0)
        active_change = active_addresses.get("change_percentage", 0)
        
        avg_fee = network_data.get("average_transaction_fee", 0)
        fee_unit = network_data.get("fee_unit", "USD")
        
        # Create summary
        summary = f"On-Chain Analysis for {crypto}:\n\n"
        
        # Add whale activity
        summary += "Whale Activity:\n"
        summary += f"• Inflows: {whale_inflows:,.2f} {crypto}\n"
        summary += f"• Outflows: {whale_outflows:,.2f} {crypto}\n"
        summary += f"• Net Flow: {net_whale_flow:,.2f} {crypto}"
        
        # Add flow direction indicator
        if net_whale_flow > 0:
            summary += " (positive) 📈\n"
        elif net_whale_flow < 0:
            summary += " (negative) 📉\n"
        else:
            summary += " (neutral) ↔️\n"
        
        # Add on-chain sentiment
        sentiment_description = "neutral"
        if on_chain_sentiment > 30:
            sentiment_description = "very positive"
        elif on_chain_sentiment > 10:
            sentiment_description = "positive"
        elif on_chain_sentiment < -30:
            sentiment_description = "very negative"
        elif on_chain_sentiment < -10:
            sentiment_description = "negative"
        
        summary += f"• On-Chain Sentiment: {on_chain_sentiment:.2f} (-100 to 100, {sentiment_description})\n"
        
        # Add active addresses
        summary += f"\nActive Addresses: {active_now:,}"
        if active_change > 0:
            summary += f" (+{active_change:.2f}% change) 📈\n"
        elif active_change < 0:
            summary += f" ({active_change:.2f}% change) 📉\n"
        else:
            summary += " (no change) ↔️\n"
        
        # Add network data
        summary += f"\nNetwork Data:\n"
        summary += f"• Average Transaction Fee: {avg_fee} {fee_unit}\n"
        
        if "network_hashrate" in network_data and "hashrate_unit" in network_data:
            summary += f"• Network Hashrate: {network_data['network_hashrate']} {network_data['hashrate_unit']}\n"
        
        if "network_difficulty" in network_data and "difficulty_unit" in network_data:
            summary += f"• Network Difficulty: {network_data['network_difficulty']} {network_data['difficulty_unit']}\n"
        
        return summary
    
    async def invoke(self, query: str, session_id: str) -> str:
        """Process a query and return a text response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            Text response
        """
        result = await self.process_query(query, session_id)
        
        if "error" in result:
            return f"Error: {result['error']}"
        
        return result["summary"]
    
    async def stream(self, query: str, session_id: str) -> AsyncIterable[Dict[str, Any]]:
        """Process a query and stream the response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Yields:
            Response updates
        """
        # Extract crypto name from query
        crypto = self._extract_crypto_name(query)
        if not crypto:
            yield {
                "is_task_complete": True,
                "content": "Could not determine which cryptocurrency to analyze. Please specify a cryptocurrency."
            }
            return
        
        # Extract timeframe from query
        days = self._extract_timeframe(query)
        
        # Update on fetching whale transactions
        yield {
            "is_task_complete": False,
            "updates": f"Fetching whale transactions for {crypto}..."
        }
        
        # Fetch whale transactions
        whale_transactions = await self.fetch_whale_transactions(crypto, days)
        
        # Update on analyzing whale activity
        yield {
            "is_task_complete": False,
            "updates": "Analyzing whale activity..."
        }
        
        # Analyze whale activity
        whale_analysis = await self.analyze_whale_activity(whale_transactions)
        
        # Update on fetching active addresses
        yield {
            "is_task_complete": False,
            "updates": "Fetching active addresses data..."
        }
        
        # Fetch active addresses data
        active_addresses = await self.fetch_active_addresses(crypto, days)
        
        # Update on fetching network data
        yield {
            "is_task_complete": False,
            "updates": "Fetching network data..."
        }
        
        # Fetch network data
        network_data = await self.fetch_network_data(crypto)
        
        # Generate summary
        summary = self._generate_onchain_summary(
            crypto, whale_analysis, active_addresses, network_data
        )
        
        # Final response with full analysis
        yield {
            "is_task_complete": True,
            "content": {
                "crypto": crypto,
                "timeframe_days": days,
                "whale_analysis": whale_analysis,
                "active_addresses": active_addresses,
                "network_data": network_data,
                "summary": summary,
                "recent_whale_transactions": whale_transactions[:5]  # Include top 5 transactions
            }
        }
