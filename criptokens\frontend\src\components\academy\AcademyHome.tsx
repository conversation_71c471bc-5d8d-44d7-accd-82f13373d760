import React from 'react';
import { Link } from 'react-router-dom';
import '../../styles/academy/AcademyHome.css';

const AcademyHome: React.FC = () => {
  // Categorías de cursos
  const categories = [
    {
      id: 'fundamentals',
      title: 'Fundamentos',
      description: 'Aprende los conceptos básicos de las criptomonedas y blockchain',
      icon: 'fas fa-book',
      color: '#3498db'
    },
    {
      id: 'trading',
      title: 'Trading',
      description: 'Estrategias y técnicas para operar en el mercado de criptomonedas',
      icon: 'fas fa-chart-line',
      color: '#2ecc71'
    },
    {
      id: 'defi',
      title: 'DeFi',
      description: 'Explora el mundo de las finanzas descentralizadas',
      icon: 'fas fa-university',
      color: '#9b59b6'
    },
    {
      id: 'security',
      title: 'Seguridad',
      description: 'Protege tus activos digitales con las mejores prácticas',
      icon: 'fas fa-shield-alt',
      color: '#e74c3c'
    }
  ];

  // Cursos destacados
  const featuredCourses = [
    {
      id: 'crypto-fundamentals',
      title: 'Fundamentos de Criptomonedas',
      description: 'Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.',
      level: 'beginner',
      duration: '4 horas',
      thumbnail: '/images/courses/crypto-fundamentals.svg',
      instructor: {
        name: '<PERSON> Rodríguez',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
      }
    },
    {
      id: 'crypto-trading',
      title: 'Trading de Criptomonedas',
      description: 'Aprende estrategias de trading, análisis técnico y gestión de riesgos para operar en el mercado de criptomonedas.',
      level: 'intermediate',
      duration: '6 horas',
      thumbnail: '/images/courses/crypto-trading.svg',
      instructor: {
        name: 'Carlos Vega',
        avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
      }
    },
    {
      id: 'defi-essentials',
      title: 'Finanzas Descentralizadas (DeFi)',
      description: 'Descubre el mundo de las finanzas descentralizadas, protocolos, oportunidades y riesgos en este ecosistema emergente.',
      level: 'intermediate',
      duration: '5 horas',
      thumbnail: '/images/courses/defi-essentials.svg',
      instructor: {
        name: 'Laura Martínez',
        avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
      }
    }
  ];

  return (
    <div className="academy-home">
      <section className="academy-hero">
        <div className="hero-content">
          <h1>Academia Cripto</h1>
          <p>Aprende todo sobre criptomonedas, blockchain y finanzas descentralizadas con nuestros cursos interactivos.</p>
          <div className="hero-actions">
            <Link to="../dashboard" className="btn-primary">
              <i className="fas fa-graduation-cap"></i> Mi Aprendizaje
            </Link>
            <Link to="../courses" className="btn-secondary">
              <i className="fas fa-book"></i> Explorar Cursos
            </Link>
          </div>
        </div>
        <div className="hero-image">
          <img src="/images/academy-hero.svg" alt="Academia Cripto" />
        </div>
      </section>

      <section className="academy-stats">
        <div className="stat-item">
          <div className="stat-icon">
            <i className="fas fa-users"></i>
          </div>
          <div className="stat-content">
            <h3>10,000+</h3>
            <p>Estudiantes</p>
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-icon">
            <i className="fas fa-book-open"></i>
          </div>
          <div className="stat-content">
            <h3>50+</h3>
            <p>Cursos</p>
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-icon">
            <i className="fas fa-certificate"></i>
          </div>
          <div className="stat-content">
            <h3>5,000+</h3>
            <p>Certificados Emitidos</p>
          </div>
        </div>
        <div className="stat-item">
          <div className="stat-icon">
            <i className="fas fa-clock"></i>
          </div>
          <div className="stat-content">
            <h3>200+</h3>
            <p>Horas de Contenido</p>
          </div>
        </div>
      </section>

      <section className="featured-courses">
        <div className="section-header">
          <h2>Cursos Destacados</h2>
          <Link to="../courses" className="view-all">
            Ver todos los cursos <i className="fas fa-arrow-right"></i>
          </Link>
        </div>
        <div className="courses-grid">
          {featuredCourses.map(course => (
            <div key={course.id} className="course-card">
              <div className="course-image">
                {course.thumbnail ? (
                  <img src={course.thumbnail} alt={course.title} />
                ) : (
                  <div className="placeholder-image"></div>
                )}
                <div className={`course-level ${course.level}`}>
                  {course.level === 'beginner' && 'Principiante'}
                  {course.level === 'intermediate' && 'Intermedio'}
                  {course.level === 'advanced' && 'Avanzado'}
                </div>
              </div>
              <div className="course-content">
                <h3>{course.title}</h3>
                <p className="course-description">{course.description}</p>
                <div className="course-meta">
                  <span className="course-duration">
                    <i className="fas fa-clock"></i> {course.duration}
                  </span>
                </div>
                <div className="course-instructor">
                  <img src={course.instructor.avatar} alt={course.instructor.name} />
                  <span>{course.instructor.name}</span>
                </div>
                <Link to={`../courses/${course.id}`} className="view-course-button">
                  Ver Curso
                </Link>
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className="course-categories">
        <div className="section-header">
          <h2>Categorías de Cursos</h2>
        </div>
        <div className="categories-grid">
          {categories.map(category => (
            <div key={category.id} className="category-card" style={{ borderColor: category.color }}>
              <div className="category-icon" style={{ backgroundColor: category.color }}>
                <i className={category.icon}></i>
              </div>
              <div className="category-content">
                <h3>{category.title}</h3>
                <p>{category.description}</p>
                <Link to={`../categories/${category.id}`} className="category-link">
                  Explorar cursos <i className="fas fa-arrow-right"></i>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className="learning-path">
        <div className="section-header">
          <h2>Ruta de Aprendizaje Recomendada</h2>
        </div>
        <div className="path-container">
          <div className="path-step">
            <div className="step-number">1</div>
            <div className="step-content">
              <h3>Fundamentos de Criptomonedas</h3>
              <p>Aprende los conceptos básicos de las criptomonedas y blockchain.</p>
              <Link to="../courses/crypto-fundamentals" className="step-link">
                Comenzar <i className="fas fa-arrow-right"></i>
              </Link>
            </div>
          </div>
          <div className="path-connector"></div>
          <div className="path-step">
            <div className="step-number">2</div>
            <div className="step-content">
              <h3>Wallets y Seguridad</h3>
              <p>Aprende a almacenar y proteger tus criptomonedas de manera segura.</p>
              <Link to="../courses/crypto-security" className="step-link">
                Comenzar <i className="fas fa-arrow-right"></i>
              </Link>
            </div>
          </div>
          <div className="path-connector"></div>
          <div className="path-step">
            <div className="step-number">3</div>
            <div className="step-content">
              <h3>Trading de Criptomonedas</h3>
              <p>Aprende estrategias de trading y análisis técnico.</p>
              <Link to="../courses/crypto-trading" className="step-link">
                Comenzar <i className="fas fa-arrow-right"></i>
              </Link>
            </div>
          </div>
          <div className="path-connector"></div>
          <div className="path-step">
            <div className="step-number">4</div>
            <div className="step-content">
              <h3>Finanzas Descentralizadas (DeFi)</h3>
              <p>Explora el mundo de las finanzas descentralizadas.</p>
              <Link to="../courses/defi-essentials" className="step-link">
                Comenzar <i className="fas fa-arrow-right"></i>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <section className="academy-cta">
        <div className="cta-content">
          <h2>¿Listo para comenzar tu viaje en el mundo cripto?</h2>
          <p>Regístrate hoy y accede a todos nuestros cursos y recursos educativos.</p>
          <Link to="../dashboard" className="btn-primary">
            Comenzar Ahora
          </Link>
        </div>
      </section>
    </div>
  );
};

export default AcademyHome;
