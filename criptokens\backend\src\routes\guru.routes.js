const express = require('express');
const axios = require('axios');
const { getChatResponse, generateImage } = require('../services/openrouter.service');
const { searchNews } = require('../services/brave.service');
const { getUserPortfolio, calculatePortfolioStats, updateAssetPrices } = require('../services/portfolio.service');
const { findRelevantDocs } = require('../services/knowledge.service');
const { visualizeWebPage, navigateBack, navigateForward, clickElement } = require('../services/playwright-mcp.service');
const guruEtherscanService = require('../services/guru-etherscan.service');

const router = express.Router();

// Función para obtener datos de criptomonedas desde el servidor MCP
async function getCryptoData(cryptoId) {
  try {
    // URL del servidor MCP
    const mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:3101';

    // Realizar la petición al servidor MCP
    const response = await axios.post(`${mcpServerUrl}/tools/getCryptoPrice`, {
      cryptoId
    });

    // Extraer y parsear los datos
    if (response.data && response.data.content && response.data.content[0]) {
      const dataText = response.data.content[0].text;
      return JSON.parse(dataText);
    }

    throw new Error('Formato de respuesta inesperado del servidor MCP');
  } catch (error) {
    console.error('Error al obtener datos de criptomoneda:', error);
    return null;
  }
}

// Función para detectar si la pregunta es sobre una criptomoneda específica
function detectCryptoInQuestion(question) {
  // Lista de criptomonedas comunes y sus identificadores
  const cryptoMap = {
    'bitcoin': 'bitcoin',
    'btc': 'bitcoin',
    'ethereum': 'ethereum',
    'eth': 'ethereum',
    'solana': 'solana',
    'sol': 'solana',
    'cardano': 'cardano',
    'ada': 'cardano',
    'ripple': 'ripple',
    'xrp': 'ripple',
    'binance coin': 'binancecoin',
    'bnb': 'binancecoin',
    'dogecoin': 'dogecoin',
    'doge': 'dogecoin',
    'polkadot': 'polkadot',
    'dot': 'polkadot',
    'litecoin': 'litecoin',
    'ltc': 'litecoin'
  };

  // Convertir la pregunta a minúsculas para facilitar la comparación
  const lowerQuestion = question.toLowerCase();

  // Buscar menciones de criptomonedas en la pregunta
  for (const [keyword, id] of Object.entries(cryptoMap)) {
    if (lowerQuestion.includes(keyword)) {
      return id;
    }
  }

  return null;
}

// Ruta para consultar al Gurú Cripto
router.post('/ask', async (req, res) => {
  try {
    const { question, userId, history } = req.body;

    if (!question) {
      return res.status(400).json({
        error: 'La pregunta es obligatoria'
      });
    }

    console.log(`Pregunta recibida: ${question}`);
    if (userId) {
      console.log(`Usuario autenticado: ${userId}`);
    }

    // Verificar si se recibió el historial de conversación
    if (history && Array.isArray(history)) {
      console.log(`Historial de conversación recibido con ${history.length} mensajes`);
    } else {
      console.log('No se recibió historial de conversación');
    }

    // Detectar si la pregunta es sobre una criptomoneda específica
    const cryptoId = detectCryptoInQuestion(question);
    let cryptoData = null;

    // Si se detectó una criptomoneda, obtener datos actualizados
    if (cryptoId) {
      console.log(`Criptomoneda detectada: ${cryptoId}`);
      cryptoData = await getCryptoData(cryptoId);
    }

    // Obtener datos del portafolio si el usuario está autenticado
    let portfolioData = null;
    if (userId) {
      try {
        // Obtener datos del portafolio
        portfolioData = await getUserPortfolio(userId);

        if (portfolioData && portfolioData.assets && portfolioData.assets.length > 0) {
          console.log(`Portafolio encontrado para el usuario ${userId} con ${portfolioData.assets.length} activos`);

          // Obtener precios actuales para los activos del portafolio
          const currentPrices = {};

          // Si tenemos datos de criptomonedas, podemos usar esos precios
          if (cryptoData) {
            currentPrices[cryptoData.id] = cryptoData.price;
          }

          // Intentar obtener precios actuales para todos los activos del portafolio
          try {
            // Obtener IDs de todos los activos en el portafolio
            const assetIds = portfolioData.assets.map(asset => asset.id);
            console.log(`Obteniendo precios actuales para ${assetIds.length} activos`);

            // Para cada activo, intentar obtener su precio actual
            for (const assetId of assetIds) {
              // Si ya tenemos el precio (de cryptoData), omitir
              if (currentPrices[assetId]) continue;

              try {
                const assetData = await getCryptoData(assetId);
                if (assetData && assetData.price) {
                  currentPrices[assetId] = assetData.price;
                  console.log(`Precio obtenido para ${assetId}: ${assetData.price}`);
                }
              } catch (priceError) {
                console.warn(`No se pudo obtener precio para ${assetId}:`, priceError.message);
              }
            }
          } catch (pricesError) {
            console.error('Error al obtener precios actuales:', pricesError);
          }

          // Actualizar estadísticas del portafolio con los precios actuales
          if (Object.keys(currentPrices).length > 0) {
            console.log(`Actualizando portafolio con ${Object.keys(currentPrices).length} precios actuales`);
            portfolioData = calculatePortfolioStats(portfolioData, currentPrices);

            // Actualizar los precios en Firestore (en segundo plano)
            updateAssetPrices(userId, currentPrices).then(updated => {
              if (updated) {
                console.log(`Precios actualizados en Firestore para el usuario ${userId}`);
              }
            }).catch(updateError => {
              console.error('Error al actualizar precios en Firestore:', updateError);
            });
          }
        } else {
          console.log(`No se encontraron activos en el portafolio del usuario ${userId}`);
        }
      } catch (portfolioError) {
        console.error('Error al obtener datos del portafolio:', portfolioError);
      }
    }

    // Generar una clave de caché basada en la pregunta, los datos y el usuario
    const cacheKey = `${question}_${cryptoData ? cryptoId : 'no-crypto'}_${userId || 'no-user'}`;

    // Función para generar una respuesta simulada (para desarrollo)
    const generateMockResponse = (message) => {
      // Respuestas simuladas basadas en palabras clave en el mensaje
      if (message.toLowerCase().includes('hola') || message.toLowerCase().includes('saludos')) {
        return '¡Hola! Soy el Gurú Cripto. ¿En qué puedo ayudarte hoy con respecto al mundo de las criptomonedas?';
      } else if (message.toLowerCase().includes('bitcoin') || message.toLowerCase().includes('btc')) {
        return 'Bitcoin (BTC) es la primera y más conocida criptomoneda, creada en 2009 por una persona o grupo bajo el seudónimo de Satoshi Nakamoto. Funciona en una red descentralizada sin un banco central o administrador único.';
      } else if (message.toLowerCase().includes('ethereum') || message.toLowerCase().includes('eth')) {
        return 'Ethereum (ETH) es una plataforma blockchain descentralizada que permite la creación de contratos inteligentes y aplicaciones descentralizadas (dApps). Fue propuesta por Vitalik Buterin en 2013.';
      } else if (message.toLowerCase().includes('blockchain')) {
        return 'Blockchain es una tecnología de registro distribuido que mantiene un registro continuo de transacciones en bloques enlazados y asegurados mediante criptografía. Es la tecnología subyacente de las criptomonedas.';
      } else if (message.toLowerCase().includes('portafolio') || message.toLowerCase().includes('cartera')) {
        return `He analizado tu portafolio y parece que tienes una buena diversificación de activos. Para un análisis más detallado, necesitaría acceso a datos actualizados del mercado.`;
      } else {
        return `He recibido tu consulta sobre "${message}". Como estamos en modo de desarrollo, estoy generando una respuesta simulada. En producción, esto se conectaría a un modelo de lenguaje real.`;
      }
    };

    // Detectar si es una solicitud de generación de imagen
    const isImageGenerationRequest = question.toLowerCase().includes('genera una imagen') ||
                                    question.toLowerCase().includes('crea una imagen') ||
                                    question.toLowerCase().includes('dibuja') ||
                                    question.toLowerCase().includes('muestra una imagen');

    let imageUrl = null;

    // Si es una solicitud de imagen, intentar generar la imagen
    if (isImageGenerationRequest) {
      try {
        // Extraer el prompt para la imagen
        const promptRegex = /(genera|crea|dibuja|muestra) una imagen (de|sobre|con) (.+)/i;
        const match = question.match(promptRegex);

        if (match && match[3]) {
          const imagePrompt = match[3];
          console.log(`Detectada solicitud de imagen. Prompt: ${imagePrompt}`);

          // Generar la imagen
          const result = await generateImage(imagePrompt);
          imageUrl = result.imageUrl;

          // Añadir la descripción de la imagen a la historia de mensajes
          if (result.imageDescription) {
            history.push({
              role: 'assistant',
              content: `He generado una imagen de "${imagePrompt}". Aquí está mi descripción de la imagen:\n\n${result.imageDescription}`
            });
          }
        }
      } catch (imageError) {
        console.error('Error al generar imagen durante la consulta:', imageError);
        // Continuar con la respuesta de texto aunque falle la generación de imagen
      }
    }

    // Buscar documentos relevantes en la base de conocimiento
    console.log('Buscando documentos relevantes en la base de conocimiento...');
    const relevantDocs = await findRelevantDocs(question);
    console.log(`Se encontraron ${relevantDocs.length} documentos relevantes`);

    // Obtener respuesta de texto de OpenRouter
    try {
      const response = await getChatResponse(question, {
        cryptoData,
        portfolioData,
        cacheKey,
        history,
        relevantDocs
      });

      // Verificar si la respuesta es un objeto o una cadena de texto
      let reply, webPageData = null;

      if (typeof response === 'object' && response !== null) {
        // Si es un objeto, extraer las propiedades
        reply = response.reply;
        webPageData = response.webPageData;
        console.log('Respuesta recibida como objeto:', { reply: typeof reply, webPageData: !!webPageData });
      } else {
        // Si es una cadena de texto, usarla directamente
        reply = response;
        console.log('Respuesta recibida como cadena de texto');
      }

      // Verificar que reply sea una cadena de texto
      if (typeof reply !== 'string') {
        console.error('Error: La respuesta no es una cadena de texto:', reply);
        reply = 'Lo siento, no pude generar una respuesta adecuada.';
      }

      // Enviar respuesta al cliente (incluyendo URL de imagen si se generó)
      res.json({
        reply,
        cryptoData,
        hasPortfolioData: !!portfolioData,
        imageUrl, // Será null si no se generó ninguna imagen
        imageDescription: imageUrl ? 'Ver descripción en el mensaje' : null, // Referencia a la descripción en el mensaje
        webPageData // Incluir datos de página web si existen
      });
    } catch (apiError) {
      console.error('Error al conectar con OpenRouter:', apiError);
      console.error('Detalles del error en la ruta:', JSON.stringify(apiError, Object.getOwnPropertyNames(apiError), 2));

      // Mostrar información adicional sobre el error
      console.error('Tipo de error:', apiError.constructor.name);
      console.error('Código de estado:', apiError.status || 'N/A');
      console.error('Mensaje:', apiError.message || 'Sin mensaje');

      // Fallback a respuesta simulada en caso de error
      console.log('Usando respuesta simulada como fallback');
      let fallbackReply = generateMockResponse(question);

      // Verificar que fallbackReply sea una cadena de texto
      if (typeof fallbackReply !== 'string') {
        console.error('Error: La respuesta de fallback no es una cadena de texto:', fallbackReply);
        fallbackReply = 'Lo siento, no pude generar una respuesta adecuada debido a un error.';
      }

      res.json({
        reply: fallbackReply,
        cryptoData,
        hasPortfolioData: !!portfolioData,
        fallback: true,
        error: {
          message: apiError.message,
          type: apiError.constructor.name
        }
      });
    }
  } catch (error) {
    console.error('Error al procesar la consulta:', error);
    res.status(500).json({
      error: 'Error al procesar la consulta',
      details: error.message
    });
  }
});

// Ruta para buscar noticias sobre criptomonedas
router.post('/news', async (req, res) => {
  try {
    const { topic, query, count = 5, freshness = 'pm' } = req.body;

    // Usar query si está disponible, de lo contrario usar topic
    const searchQuery = query || topic;

    if (!searchQuery) {
      return res.status(400).json({
        error: 'Se requiere un término de búsqueda (topic o query)'
      });
    }

    console.log(`Realizando búsqueda para: "${searchQuery}" (count: ${count}, freshness: ${freshness})`);

    try {
      // Buscar utilizando el servicio de Brave Search
      const searchResults = await searchNews(searchQuery, count, 0, freshness);

      // Enviar resultados al cliente
      res.json({
        results: searchResults,
        query: searchQuery,
        count: searchResults.length,
        freshness
      });
    } catch (searchError) {
      console.error('Error al realizar búsqueda:', searchError);

      // En caso de error, el servicio ya devuelve datos simulados
      res.status(500).json({
        error: 'Error al realizar búsqueda',
        details: searchError.message
      });
    }
  } catch (error) {
    console.error('Error al procesar la búsqueda de noticias:', error);
    res.status(500).json({
      error: 'Error al procesar la búsqueda de noticias',
      details: error.message
    });
  }
});

// Ruta para generar imágenes a partir de texto
router.post('/generate-image', async (req, res) => {
  try {
    const { prompt } = req.body;

    if (!prompt) {
      return res.status(400).json({
        error: 'El prompt de texto es obligatorio'
      });
    }

    console.log(`Generando imagen para prompt: ${prompt}`);

    try {
      // Generar imagen utilizando el servicio de OpenRouter
      const result = await generateImage(prompt);

      // Enviar URL de la imagen y descripción al cliente
      res.json({
        imageUrl: result.imageUrl,
        imageDescription: result.imageDescription,
        prompt,
        success: true
      });
    } catch (imageError) {
      console.error('Error al generar imagen:', imageError);

      // En caso de error, usar una imagen de placeholder y notificar al cliente
      // que estamos usando una imagen de respaldo
      const fallbackImageUrl = 'https://images.unsplash.com/photo-1518546305927-5a555bb7020d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80';

      res.json({
        imageUrl: fallbackImageUrl,
        prompt,
        success: false,
        fallback: true,
        error: 'Error al generar imagen con DALL-E. Usando imagen de respaldo.'
      });
    }
  } catch (error) {
    console.error('Error al procesar la generación de imagen:', error);

    // En caso de error general, usar una imagen de placeholder
    const fallbackImageUrl = 'https://images.unsplash.com/photo-1518546305927-5a555bb7020d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80';

    res.json({
      imageUrl: fallbackImageUrl,
      prompt: req.body.prompt || 'Imagen de Bitcoin',
      success: false,
      fallback: true,
      error: 'Error al procesar la solicitud. Usando imagen de respaldo.'
    });
  }
});

// Ruta para obtener el portafolio del usuario
router.get('/portfolio/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        error: 'El ID de usuario es obligatorio'
      });
    }

    console.log(`Obteniendo portafolio para el usuario: ${userId}`);

    // Obtener el portafolio del usuario
    const portfolioData = await getUserPortfolio(userId);

    if (!portfolioData) {
      return res.status(404).json({
        error: 'No se encontró el portafolio del usuario',
        userId
      });
    }

    // Si hay activos en el portafolio, intentar obtener precios actuales
    if (portfolioData.assets && portfolioData.assets.length > 0) {
      const currentPrices = {};
      const assetIds = portfolioData.assets.map(asset => asset.id);

      // Intentar obtener precios actuales para cada activo
      for (const assetId of assetIds) {
        try {
          const assetData = await getCryptoData(assetId);
          if (assetData && assetData.price) {
            currentPrices[assetId] = assetData.price;
          }
        } catch (priceError) {
          console.warn(`No se pudo obtener precio para ${assetId}:`, priceError.message);
        }
      }

      // Actualizar estadísticas del portafolio con los precios actuales
      if (Object.keys(currentPrices).length > 0) {
        const updatedPortfolio = calculatePortfolioStats(portfolioData, currentPrices);

        // Actualizar los precios en Firestore (en segundo plano)
        updateAssetPrices(userId, currentPrices).then(updated => {
          if (updated) {
            console.log(`Precios actualizados en Firestore para el usuario ${userId}`);
          }
        }).catch(updateError => {
          console.error('Error al actualizar precios en Firestore:', updateError);
        });

        // Devolver el portafolio actualizado
        return res.json({
          portfolio: updatedPortfolio,
          pricesUpdated: true
        });
      }
    }

    // Si no se pudieron obtener precios actuales, devolver el portafolio tal cual
    res.json({
      portfolio: portfolioData,
      pricesUpdated: false
    });
  } catch (error) {
    console.error('Error al obtener portafolio:', error);
    res.status(500).json({
      error: 'Error al obtener portafolio',
      details: error.message
    });
  }
});

// Ruta para realizar búsquedas web generales (para deep research)
router.post('/search', async (req, res) => {
  try {
    const { query, count = 5, freshness = 'pm' } = req.body;

    if (!query) {
      return res.status(400).json({
        error: 'La consulta de búsqueda es obligatoria'
      });
    }

    console.log(`Realizando búsqueda web para deep research: "${query}" (count: ${count}, freshness: ${freshness})`);

    try {
      // Realizar búsqueda utilizando el servicio de Brave Search
      const searchResults = await searchNews(query, count, 0, freshness);

      // Enviar resultados al cliente
      res.json({
        results: searchResults,
        query,
        count: searchResults.length,
        freshness
      });
    } catch (searchError) {
      console.error('Error al realizar búsqueda web:', searchError);

      // En caso de error, el servicio ya devuelve datos simulados
      res.status(500).json({
        error: 'Error al realizar búsqueda web',
        details: searchError.message
      });
    }
  } catch (error) {
    console.error('Error al procesar la búsqueda web:', error);
    res.status(500).json({
      error: 'Error al procesar la búsqueda web',
      details: error.message
    });
  }
});

// Endpoint para visualizar una página web
router.post('/visualize-webpage', async (req, res) => {
  try {
    const { url, sessionId } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'Se requiere una URL' });
    }

    console.log(`Visualizando página web: ${url}`);

    // Navegar a la página y obtener snapshot
    const pageData = await visualizeWebPage(url, sessionId);

    // Devolver los datos
    res.json(pageData);
  } catch (error) {
    console.error('Error al visualizar página web:', error);
    res.status(500).json({ error: 'Error al visualizar página web', details: error.message });
  }
});

// Endpoint para navegar hacia atrás en el historial
router.post('/navigate-back', async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: 'Se requiere un ID de sesión' });
    }

    console.log(`Navegando hacia atrás en sesión: ${sessionId}`);

    // Navegar hacia atrás y obtener datos de la página
    const pageData = await navigateBack(sessionId);

    // Devolver los datos
    res.json(pageData);
  } catch (error) {
    console.error('Error al navegar hacia atrás:', error);
    res.status(500).json({ error: 'Error al navegar hacia atrás', details: error.message });
  }
});

// Endpoint para navegar hacia adelante en el historial
router.post('/navigate-forward', async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: 'Se requiere un ID de sesión' });
    }

    console.log(`Navegando hacia adelante en sesión: ${sessionId}`);

    // Navegar hacia adelante y obtener datos de la página
    const pageData = await navigateForward(sessionId);

    // Devolver los datos
    res.json(pageData);
  } catch (error) {
    console.error('Error al navegar hacia adelante:', error);
    res.status(500).json({ error: 'Error al navegar hacia adelante', details: error.message });
  }
});

// Endpoint para hacer clic en un elemento
router.post('/click-element', async (req, res) => {
  try {
    const { sessionId, selector, text } = req.body;

    if (!sessionId) {
      return res.status(400).json({ error: 'Se requiere un ID de sesión' });
    }

    if (!selector && !text) {
      return res.status(400).json({ error: 'Se requiere un selector o texto para hacer clic' });
    }

    console.log(`Haciendo clic en elemento: ${selector || text} en sesión: ${sessionId}`);

    // Hacer clic en el elemento y obtener datos de la página
    const pageData = await clickElement(selector, text, sessionId);

    // Devolver los datos
    res.json(pageData);
  } catch (error) {
    console.error('Error al hacer clic en elemento:', error);
    res.status(500).json({ error: 'Error al hacer clic en elemento', details: error.message });
  }
});

// Endpoint para analizar el impacto de The Merge en Ethereum
router.get('/ethereum/the-merge', async (req, res) => {
  try {
    console.log('Analizando el impacto de The Merge en Ethereum...');

    // Obtener análisis detallado
    const analysis = await guruEtherscanService.analyzeTheMergeImpact();

    // Devolver los datos
    res.json(analysis);
  } catch (error) {
    console.error('Error al analizar el impacto de The Merge:', error);
    res.status(500).json({ error: 'Error al analizar el impacto de The Merge', details: error.message });
  }
});

// Endpoint para analizar el estado actual de Ethereum y DeFi
router.get('/ethereum/defi-state', async (req, res) => {
  try {
    console.log('Analizando el estado actual de Ethereum y DeFi...');

    // Obtener análisis detallado
    const analysis = await guruEtherscanService.analyzeEthereumDefiState();

    // Devolver los datos
    res.json(analysis);
  } catch (error) {
    console.error('Error al analizar el estado de Ethereum y DeFi:', error);
    res.status(500).json({ error: 'Error al analizar el estado de Ethereum y DeFi', details: error.message });
  }
});

// Endpoint para analizar una dirección de Ethereum
router.get('/ethereum/address/:address', async (req, res) => {
  try {
    const { address } = req.params;

    if (!address) {
      return res.status(400).json({ error: 'Se requiere una dirección de Ethereum' });
    }

    console.log(`Analizando dirección de Ethereum: ${address}`);

    // Obtener análisis detallado
    const analysis = await guruEtherscanService.analyzeEthereumAddress(address);

    // Devolver los datos
    res.json(analysis);
  } catch (error) {
    console.error(`Error al analizar la dirección de Ethereum:`, error);
    res.status(500).json({ error: 'Error al analizar la dirección de Ethereum', details: error.message });
  }
});

// Endpoint para analizar un contrato inteligente
router.get('/ethereum/contract/:address', async (req, res) => {
  try {
    const { address } = req.params;

    if (!address) {
      return res.status(400).json({ error: 'Se requiere una dirección de contrato' });
    }

    console.log(`Analizando contrato inteligente: ${address}`);

    // Obtener análisis detallado
    const analysis = await guruEtherscanService.analyzeSmartContract(address);

    // Devolver los datos
    res.json(analysis);
  } catch (error) {
    console.error(`Error al analizar el contrato inteligente:`, error);
    res.status(500).json({ error: 'Error al analizar el contrato inteligente', details: error.message });
  }
});

// Endpoint para generar proyección de precio para ETH
router.get('/ethereum/price-projection', async (req, res) => {
  try {
    const { months } = req.query;
    const timeframe = months ? parseInt(months) : 6;

    console.log(`Generando proyección de precio para ETH a ${timeframe} meses...`);

    // Obtener proyección
    const projection = await guruEtherscanService.generateEthPriceProjection(timeframe);

    // Devolver los datos
    res.json(projection);
  } catch (error) {
    console.error('Error al generar proyección de precio para ETH:', error);
    res.status(500).json({ error: 'Error al generar proyección de precio para ETH', details: error.message });
  }
});

module.exports = router;
