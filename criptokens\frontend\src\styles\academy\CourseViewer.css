.course-viewer {
  display: grid;
  grid-template-columns: 300px 1fr 280px;
  gap: 1.5rem;
  height: calc(100vh - 150px);
  overflow: hidden;
  color: var(--text-primary);
}

/* Sidebar izquierdo */
.course-sidebar {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.course-info {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.course-info h2 {
  margin: 0 0 0.75rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.course-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.course-level, .course-duration {
  font-size: 0.85rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-level::before {
  content: '\f19d';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.course-duration::before {
  content: '\f017';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.course-progress {
  margin-top: 1rem;
}

.progress-bar {
  height: 8px;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.course-modules {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.module-item {
  margin-bottom: 0.5rem;
}

.module-header {
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.module-header:hover {
  background-color: var(--color-surface-light);
}

.module-item.active .module-header {
  background-color: var(--color-primary-transparent);
}

.module-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.module-progress {
  margin-top: 0.5rem;
}

.module-lessons {
  padding: 0.5rem 0;
}

.lesson-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem 0.75rem 2.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
  color: var(--text-secondary);
  position: relative;
}

.lesson-item::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--text-secondary);
}

.lesson-item.completed::before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  background: none;
  color: #2ecc71;
  font-size: 0.8rem;
}

.lesson-item:hover {
  background-color: var(--color-surface-light);
}

.lesson-item.active {
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
}

.lesson-item.active::before {
  background-color: var(--color-primary);
}

.lesson-item.active.completed::before {
  background: none;
  color: var(--color-primary);
}

.lesson-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.lesson-duration, .lesson-type {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.lesson-item.quiz::before {
  content: '\f059';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  background: none;
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.lesson-item.quiz.completed::before {
  content: '\f058';
  color: #2ecc71;
}

/* Contenido principal */
.course-content {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--color-primary);
  font-weight: 500;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--color-primary);
}

.lesson-content, .resources-content, .discussion-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.lesson-content h2, .resources-content h2, .discussion-content h2 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.lesson-video {
  margin-bottom: 2rem;
  border-radius: 8px;
  overflow: hidden;
  background-color: #000;
  aspect-ratio: 16 / 9;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #1a1a2e;
}

.video-placeholder i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.lesson-text {
  line-height: 1.6;
  color: var(--text-primary);
}

.lesson-text p {
  margin-bottom: 1.5rem;
}

.lesson-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.nav-button {
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-button.prev {
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.nav-button.prev:hover:not(:disabled) {
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
}

.nav-button.next {
  background-color: var(--color-primary);
  border: 1px solid var(--color-primary);
  color: white;
}

.nav-button.next:hover {
  background-color: var(--color-primary-dark);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.no-lesson-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* Quiz */
.quiz-container {
  padding: 1rem 0;
}

.quiz-questions {
  margin-bottom: 2rem;
}

.quiz-question {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.quiz-question h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quiz-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.quiz-option:hover {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
}

.quiz-option input {
  margin: 0;
}

.quiz-submit {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.quiz-submit:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.quiz-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quiz-results {
  text-align: center;
  padding: 2rem;
}

.quiz-score {
  margin-bottom: 2rem;
  padding: 2rem;
  border-radius: 12px;
}

.quiz-score.pass {
  background-color: rgba(46, 204, 113, 0.1);
  border: 1px solid #2ecc71;
}

.quiz-score.fail {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid #e74c3c;
}

.quiz-score h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.quiz-score.pass h3 {
  color: #2ecc71;
}

.quiz-score.fail h3 {
  color: #e74c3c;
}

.quiz-button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 0.5rem;
}

.quiz-button.retry {
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.quiz-button.retry:hover {
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
}

.quiz-button.continue {
  background-color: var(--color-primary);
  border: 1px solid var(--color-primary);
  color: white;
}

.quiz-button.continue:hover {
  background-color: var(--color-primary-dark);
}

/* Recursos */
.resources-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border-radius: 8px;
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.resource-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-sm);
}

.resource-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.resource-info {
  flex: 1;
}

.resource-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.resource-type {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.resource-link {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  background-color: var(--color-primary);
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.resource-link:hover {
  background-color: var(--color-primary-dark);
}

/* Discusión */
.discussion-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  color: var(--text-secondary);
}

.discussion-placeholder i {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

/* Sidebar derecho */
.course-info-sidebar {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  overflow-y: auto;
}

.instructor-info h3, .course-reviews h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.instructor-profile {
  display: flex;
  gap: 1rem;
}

.instructor-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
}

.instructor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.instructor-details h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.instructor-details p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.review-item {
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.review-user {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
}

.review-rating {
  display: flex;
  gap: 0.25rem;
  color: #ccc;
}

.review-rating .filled {
  color: #f1c40f;
}

.review-comment {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 1200px) {
  .course-viewer {
    grid-template-columns: 250px 1fr 250px;
  }
}

@media (max-width: 992px) {
  .course-viewer {
    grid-template-columns: 220px 1fr;
  }

  .course-info-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .course-viewer {
    grid-template-columns: 1fr;
    height: auto;
  }

  .course-sidebar {
    height: 300px;
  }

  .course-content {
    height: 600px;
  }
}

/* Certificate Tab */
.certificate-placeholder {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-surface-light);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  max-width: 800px;
  margin: 2rem auto;
}

.certificate-placeholder h2 {
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.certificate-placeholder p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
}
