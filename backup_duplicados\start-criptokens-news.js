const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  reset: '\x1b[0m'
};

console.log(`${colors.green}Iniciando servicios de Criptokens News...${colors.reset}`);

// Verificar que los directorios existan
if (!fs.existsSync(path.join(__dirname, 'backend'))) {
  fs.mkdirSync(path.join(__dirname, 'backend'), { recursive: true });
  console.log(`${colors.yellow}Directorio backend creado${colors.reset}`);
}

// Función para ejecutar comandos
function runCommand(command, args, cwd, name) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.yellow}Ejecutando: ${command} ${args.join(' ')} en ${cwd}${colors.reset}`);
    
    const proc = spawn(command, args, {
      cwd,
      shell: true,
      stdio: 'inherit'
    });
    
    console.log(`${colors.green}Proceso ${name} iniciado (PID: ${proc.pid})${colors.reset}`);
    
    proc.on('error', (error) => {
      console.error(`${colors.red}Error al iniciar ${name}: ${error.message}${colors.reset}`);
      reject(error);
    });
    
    proc.on('close', (code) => {
      if (code !== 0) {
        console.log(`${colors.red}${name} se cerró con código: ${code}${colors.reset}`);
        reject(new Error(`${name} se cerró con código: ${code}`));
      } else {
        console.log(`${colors.green}${name} finalizado correctamente${colors.reset}`);
        resolve();
      }
    });
    
    // Devolver el proceso para poder terminarlo más tarde
    resolve(proc);
  });
}

// Función principal
async function main() {
  try {
    // Iniciar el backend
    console.log(`${colors.yellow}Iniciando el servidor backend...${colors.reset}`);
    const backendPath = path.join(__dirname, 'backend');
    
    // Instalar dependencias del backend si es necesario
    if (!fs.existsSync(path.join(backendPath, 'node_modules'))) {
      console.log(`${colors.yellow}Instalando dependencias del backend...${colors.reset}`);
      await runCommand('npm', ['install'], backendPath, 'Backend npm install');
    }
    
    // Iniciar el servidor backend
    const backendProc = await runCommand('npm', ['run', 'dev'], backendPath, 'Backend server');
    
    // Esperar a que el backend esté listo
    console.log(`${colors.yellow}Esperando a que el backend esté listo...${colors.reset}`);
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log(`${colors.green}¡Servicios iniciados correctamente!${colors.reset}`);
    console.log(`${colors.green}Backend ejecutándose en: ${colors.yellow}http://localhost:3001${colors.reset}`);
    console.log(`${colors.yellow}Presiona Ctrl+C para detener todos los servicios${colors.reset}`);
    
    // Manejar la terminación
    process.on('SIGINT', () => {
      console.log(`${colors.red}Deteniendo servicios...${colors.reset}`);
      if (backendProc && backendProc.kill) {
        backendProc.kill();
      }
      process.exit(0);
    });
    
  } catch (error) {
    console.error(`${colors.red}Error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Ejecutar la función principal
main();
