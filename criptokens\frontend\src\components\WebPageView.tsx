import React, { useState, useRef, useEffect } from 'react';
import { WebPageData } from '../types/WebPageData';
import axios from 'axios';
import './WebPageView.css';

interface WebPageViewProps {
  data: WebPageData;
  onNavigate?: (url: string, sessionId?: string) => Promise<void>;
  onBack?: (sessionId?: string) => Promise<void>;
  onForward?: (sessionId?: string) => Promise<void>;
}

/**
 * Componente para mostrar los datos de una página web visualizada
 */
const WebPageView: React.FC<WebPageViewProps> = ({ data, onNavigate, onBack, onForward }) => {
  const [expanded, setExpanded] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [activeTab, setActiveTab] = useState('summary'); // 'summary', 'content', 'screenshot'
  const screenshotRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Formatear la URL para mostrar
  const displayUrl = data.url.length > 60
    ? `${data.url.substring(0, 57)}...`
    : data.url;

  // Manejar navegación
  const handleNavigate = async (url: string) => {
    if (onNavigate && data.sessionId) {
      setIsLoading(true);
      try {
        await onNavigate(url, data.sessionId);
      } catch (error) {
        console.error('Error al navegar:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Manejar navegación hacia atrás
  const handleBack = async () => {
    if (onBack && data.sessionId) {
      setIsLoading(true);
      try {
        await onBack(data.sessionId);
      } catch (error) {
        console.error('Error al navegar hacia atrás:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Manejar navegación hacia adelante
  const handleForward = async () => {
    if (onForward && data.sessionId) {
      setIsLoading(true);
      try {
        await onForward(data.sessionId);
      } catch (error) {
        console.error('Error al navegar hacia adelante:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Manejar zoom
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.25, 0.5));
  };

  const handleZoomReset = () => {
    setZoomLevel(1);
  };

  // Manejar modo pantalla completa
  const toggleFullscreen = () => {
    setFullscreen(!fullscreen);
  };

  // Efecto para manejar el modo pantalla completa
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && fullscreen) {
        setFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [fullscreen]);

  // Determinar la clase del contenedor
  const containerClass = `webpage-view-container ${fullscreen ? 'fullscreen' : ''}`;

  return (
    <div className={containerClass} ref={containerRef}>
      <div className="webpage-view-header">
        <div className="webpage-view-title">
          <h3>{data.title || 'Página Web Visualizada'}</h3>
          <a
            href={data.url}
            target="_blank"
            rel="noopener noreferrer"
            className="webpage-view-url"
          >
            {displayUrl}
          </a>
        </div>
        <div className="webpage-view-actions">
          <button
            className="webpage-view-toggle-btn"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? 'Contraer' : 'Expandir'}
          </button>
          <button
            className="webpage-view-fullscreen-btn"
            onClick={toggleFullscreen}
          >
            {fullscreen ? 'Salir' : 'Pantalla Completa'}
          </button>
          <a
            href={data.url}
            target="_blank"
            rel="noopener noreferrer"
            className="webpage-view-open-btn"
          >
            Abrir
          </a>
        </div>
      </div>

      {/* Barra de navegación */}
      {onNavigate && (
        <div className="webpage-view-navbar">
          <button
            className="webpage-nav-btn"
            onClick={handleBack}
            disabled={isLoading}
          >
            &larr;
          </button>
          <button
            className="webpage-nav-btn"
            onClick={handleForward}
            disabled={isLoading}
          >
            &rarr;
          </button>
          <input
            type="text"
            className="webpage-nav-url"
            defaultValue={data.url}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleNavigate((e.target as HTMLInputElement).value);
              }
            }}
          />
          <button
            className="webpage-nav-go-btn"
            onClick={() => {
              const input = containerRef.current?.querySelector('.webpage-nav-url') as HTMLInputElement;
              if (input) {
                handleNavigate(input.value);
              }
            }}
            disabled={isLoading}
          >
            Ir
          </button>
        </div>
      )}

      {/* Pestañas */}
      {expanded && (
        <div className="webpage-view-tabs">
          <button
            className={`webpage-tab ${activeTab === 'summary' ? 'active' : ''}`}
            onClick={() => setActiveTab('summary')}
          >
            Resumen
          </button>
          <button
            className={`webpage-tab ${activeTab === 'content' ? 'active' : ''}`}
            onClick={() => setActiveTab('content')}
          >
            Contenido
          </button>
          <button
            className={`webpage-tab ${activeTab === 'screenshot' ? 'active' : ''}`}
            onClick={() => setActiveTab('screenshot')}
          >
            Captura
          </button>
        </div>
      )}

      {/* Captura de pantalla */}
      {data.screenshot && (activeTab === 'screenshot' || !expanded) && (
        <div className="webpage-view-screenshot">
          <div className="webpage-screenshot-controls">
            <button onClick={handleZoomOut}>-</button>
            <span>{Math.round(zoomLevel * 100)}%</span>
            <button onClick={handleZoomIn}>+</button>
            <button onClick={handleZoomReset}>Reset</button>
          </div>
          <div className="webpage-screenshot-container" style={{ overflow: 'auto' }}>
            <img
              ref={screenshotRef}
              src={`data:image/jpeg;base64,${data.screenshot}`}
              alt="Captura de la página web"
              className="webpage-screenshot-img"
              style={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}
            />
          </div>
        </div>
      )}

      {/* Contenido expandido */}
      {expanded && (
        <div className="webpage-view-content">
          {/* Resumen */}
          {activeTab === 'summary' && data.summary && (
            <div className="webpage-view-summary">
              <h4>Resumen</h4>
              <p>{data.summary}</p>

              <div className="webpage-view-metadata">
                <p className="webpage-view-timestamp">
                  Visualizado: {data.timestamp || new Date().toLocaleString()}
                </p>
              </div>
            </div>
          )}

          {/* Contenido extraído */}
          {activeTab === 'content' && data.extractedText && (
            <div className="webpage-view-text">
              <h4>Contenido Extraído</h4>
              <div className="webpage-extracted-text">
                {data.extractedText}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Indicador de carga */}
      {isLoading && (
        <div className="webpage-view-loading">
          <div className="webpage-view-loading-spinner"></div>
          <p>Cargando...</p>
        </div>
      )}
    </div>
  );
};

export default WebPageView;
