# Fundamentos de Criptomonedas

## Módulo 1: ¿Qué son las Criptomonedas?

### Lección 1: Definición y Características

Las criptomonedas son activos digitales diseñados para funcionar como medio de intercambio que utiliza criptografía para asegurar las transacciones, controlar la creación de unidades adicionales y verificar la transferencia de activos.

#### Características principales

- **Descentralización**: No están controladas por ningún banco central o gobierno
- **Seguridad**: Utilizan criptografía avanzada para proteger las transacciones
- **Transparencia**: Todas las transacciones son públicas y verificables
- **Limitación**: Muchas tienen un suministro limitado (como Bitcoin con 21 millones)
- **Pseudoanonimato**: Las transacciones no están vinculadas directamente a identidades reales

#### ¿Por qué son importantes?

Las criptomonedas representan una innovación significativa en el sistema financiero global por varias razones:

1. **Inclusión financiera**: Permiten el acceso a servicios financieros a personas sin acceso a la banca tradicional
2. **Reducción de intermediarios**: Eliminan la necesidad de terceros en las transacciones
3. **Control personal**: Dan a los usuarios control total sobre sus fondos
4. **Innovación tecnológica**: Han impulsado avances en criptografía, consenso distribuido y otras tecnologías
5. **Alternativa a sistemas monetarios tradicionales**: Ofrecen una alternativa a monedas fiduciarias controladas por gobiernos

#### Tipos de criptomonedas

Existen miles de criptomonedas, pero podemos clasificarlas en varias categorías:

- **Monedas**: Diseñadas principalmente como medio de pago (Bitcoin, Litecoin)
- **Plataformas**: Permiten la creación de aplicaciones descentralizadas (Ethereum, Solana)
- **Tokens de utilidad**: Proporcionan acceso a servicios específicos dentro de un ecosistema
- **Stablecoins**: Diseñadas para mantener un valor estable (USDT, USDC)
- **Tokens de privacidad**: Enfocadas en transacciones anónimas (Monero, Zcash)
- **Tokens de gobernanza**: Otorgan derechos de voto en protocolos descentralizados

### Lección 2: Historia de las Criptomonedas

#### Orígenes y Desarrollo

La historia de las criptomonedas comienza mucho antes de Bitcoin, con los primeros conceptos de dinero digital y criptografía aplicada a las finanzas.

#### Precursores (1980s-1990s)

- **DigiCash (1989)**: Creada por David Chaum, fue uno de los primeros intentos de crear dinero electrónico usando criptografía
- **B-Money (1998)**: Propuesta por Wei Dai, introdujo la idea de crear dinero mediante la resolución de problemas computacionales
- **Bit Gold (1998)**: Diseñado por Nick Szabo, considerado el precursor directo de Bitcoin

#### El nacimiento de Bitcoin (2008-2009)

En octubre de 2008, en medio de la crisis financiera global, una persona o grupo bajo el seudónimo de Satoshi Nakamoto publicó el whitepaper "Bitcoin: Un Sistema de Efectivo Electrónico Peer-to-Peer". Este documento describía un sistema de pagos electrónicos basado en pruebas criptográficas en lugar de confianza.

El 3 de enero de 2009, Satoshi minó el primer bloque de Bitcoin (el bloque génesis), marcando el inicio oficial de la red Bitcoin. Los primeros años de Bitcoin fueron de desarrollo técnico y adopción limitada principalmente entre entusiastas de la tecnología y la criptografía.

#### La era de los altcoins (2011-2013)

A partir de 2011, comenzaron a surgir criptomonedas alternativas (altcoins) que buscaban mejorar o modificar aspectos de Bitcoin:

- **Litecoin (2011)**: Creada por Charlie Lee, ofrecía tiempos de confirmación más rápidos
- **Ripple (2012)**: Enfocada en facilitar transferencias entre instituciones financieras
- **Peercoin (2012)**: Introdujo el mecanismo de consenso Proof of Stake

#### El surgimiento de Ethereum y los contratos inteligentes (2015)

En 2015, Ethereum fue lanzado por Vitalik Buterin, introduciendo el concepto de contratos inteligentes a las criptomonedas. Esto permitió la creación de aplicaciones descentralizadas (dApps) y marcó el inicio de una nueva era para la tecnología blockchain.

#### El boom de las ICOs (2017-2018)

En 2017, las Ofertas Iniciales de Monedas (ICOs) se volvieron extremadamente populares como método para financiar nuevos proyectos de criptomonedas. Esto llevó a una explosión en el número de tokens y proyectos, así como a una burbuja especulativa que alcanzó su punto máximo a finales de 2017.

#### La era DeFi y NFTs (2020-presente)

A partir de 2020, las Finanzas Descentralizadas (DeFi) y los Tokens No Fungibles (NFTs) emergieron como casos de uso prominentes para la tecnología blockchain, ampliando significativamente el ecosistema de criptomonedas más allá de simples transferencias de valor.

## Módulo 2: Blockchain: La Tecnología Detrás

### Lección 1: ¿Qué es Blockchain?

La blockchain (cadena de bloques) es una tecnología de registro distribuido que permite almacenar información de manera segura, transparente e inmutable.

#### Definición

Una blockchain es esencialmente una base de datos distribuida que mantiene una lista creciente de registros (bloques) que están enlazados y asegurados mediante criptografía. Cada bloque contiene un hash criptográfico del bloque anterior, una marca de tiempo y datos de transacciones.

#### Componentes clave de una blockchain

1. **Bloques**: Contenedores de datos que registran transacciones
2. **Hash criptográfico**: Función matemática que convierte datos en una cadena de caracteres única
3. **Nodos**: Computadoras que mantienen copias de la blockchain y verifican transacciones
4. **Consenso**: Mecanismo por el cual los nodos acuerdan el estado de la blockchain
5. **Mineros/Validadores**: Participantes que crean nuevos bloques y reciben recompensas

#### Tipos de blockchain

- **Públicas**: Abiertas a cualquier persona (Bitcoin, Ethereum)
- **Privadas**: Acceso restringido a participantes específicos
- **Permisionadas**: Combinan elementos de públicas y privadas
- **Consorcios**: Controladas por un grupo de organizaciones

#### ¿Cómo funciona una blockchain?

1. **Transacción iniciada**: Un usuario inicia una transacción
2. **Verificación**: La transacción es verificada por los nodos de la red
3. **Agrupación**: Las transacciones verificadas se agrupan en un bloque
4. **Minería/Validación**: Los mineros o validadores compiten para añadir el bloque a la cadena
5. **Adición a la cadena**: El bloque se añade a la cadena existente
6. **Confirmación**: La transacción se considera confirmada

#### Ventajas de la tecnología blockchain

- **Inmutabilidad**: Una vez registrados, los datos no pueden ser alterados
- **Transparencia**: Todas las transacciones son visibles para los participantes
- **Seguridad**: La criptografía y la distribución hacen muy difícil atacar la red
- **Eficiencia**: Reduce la necesidad de intermediarios y procesos manuales
- **Trazabilidad**: Permite seguir el historial completo de transacciones

### Lección 2: Mecanismos de Consenso

Los mecanismos de consenso son protocolos que aseguran que todos los nodos en una red blockchain estén sincronizados y acuerden el estado legítimo de la red.

#### Proof of Work (PoW)

- **Funcionamiento**: Los mineros compiten para resolver problemas matemáticos complejos
- **Ventajas**: Alta seguridad, probado a lo largo del tiempo
- **Desventajas**: Alto consumo energético, velocidad limitada
- **Ejemplos**: Bitcoin, Litecoin, Dogecoin

#### Proof of Stake (PoS)

- **Funcionamiento**: Los validadores son seleccionados para crear bloques basándose en la cantidad de criptomonedas que tienen en stake
- **Ventajas**: Eficiencia energética, mayor escalabilidad
- **Desventajas**: Tendencia a la centralización, "nothing at stake problem"
- **Ejemplos**: Ethereum 2.0, Cardano, Solana

#### Delegated Proof of Stake (DPoS)

- **Funcionamiento**: Los poseedores de tokens votan por delegados que validan transacciones
- **Ventajas**: Alta eficiencia, gobernanza democrática
- **Desventajas**: Menor descentralización
- **Ejemplos**: EOS, Tron

#### Proof of Authority (PoA)

- **Funcionamiento**: Los bloques son validados por nodos aprobados con identidades conocidas
- **Ventajas**: Alta eficiencia, ideal para blockchains privadas
- **Desventajas**: Centralización, confianza requerida
- **Ejemplos**: VeChain, POA Network

## Módulo 3: Bitcoin: La Primera Criptomoneda

### Lección 1: Orígenes y Fundamentos de Bitcoin

Bitcoin es la primera y más conocida criptomoneda, creada como respuesta a la crisis financiera de 2008 y con el objetivo de crear un sistema de dinero electrónico descentralizado.

#### El whitepaper de Bitcoin

El 31 de octubre de 2008, Satoshi Nakamoto publicó el documento "Bitcoin: Un Sistema de Efectivo Electrónico Peer-to-Peer", que describía:

- Un sistema de pagos electrónicos basado en pruebas criptográficas
- Una solución al problema del doble gasto sin necesidad de una autoridad central
- Un mecanismo de consenso basado en prueba de trabajo (Proof of Work)
- Un sistema de incentivos para los mineros

#### Características clave de Bitcoin

- **Suministro limitado**: Solo existirán 21 millones de bitcoins
- **Emisión programada**: Nuevos bitcoins se crean a través de la minería
- **Halving**: Cada aproximadamente 4 años, la recompensa por minería se reduce a la mitad
- **Descentralización**: No hay una entidad central que controle la red
- **Pseudoanonimato**: Las transacciones son públicas pero no están vinculadas directamente a identidades reales

#### La red Bitcoin

- **Nodos completos**: Almacenan una copia completa de la blockchain
- **Mineros**: Validan transacciones y crean nuevos bloques
- **Carteras (wallets)**: Almacenan las claves privadas que permiten acceder a los bitcoins

#### Transacciones en Bitcoin

1. **Creación**: Un usuario crea una transacción especificando destinatario y cantidad
2. **Firma**: La transacción es firmada con la clave privada del remitente
3. **Transmisión**: La transacción se transmite a la red
4. **Verificación**: Los nodos verifican la validez de la transacción
5. **Inclusión en un bloque**: Los mineros incluyen la transacción en un bloque
6. **Confirmación**: Cada nuevo bloque añadido después del que contiene la transacción cuenta como una confirmación adicional

### Lección 2: Minería de Bitcoin

La minería es el proceso por el cual se verifican las transacciones y se añaden nuevos bloques a la blockchain de Bitcoin, a la vez que se crean nuevos bitcoins.

#### El proceso de minería

1. **Recopilación de transacciones**: Los mineros seleccionan transacciones del mempool
2. **Creación del bloque candidato**: Se crea un bloque con las transacciones seleccionadas
3. **Cálculo del hash**: Los mineros intentan encontrar un hash que cumpla con la dificultad actual
4. **Ajuste de nonce**: Se modifica el valor "nonce" para generar diferentes hashes
5. **Solución encontrada**: Cuando se encuentra un hash válido, el bloque se transmite a la red
6. **Verificación y adición**: Los demás nodos verifican el bloque y lo añaden a su copia de la blockchain

#### Dificultad de minería

- Se ajusta automáticamente cada 2016 bloques (aproximadamente 2 semanas)
- El objetivo es mantener un tiempo promedio de 10 minutos entre bloques
- A medida que más mineros se unen a la red, la dificultad aumenta

#### Equipos de minería

- **CPU**: Usado en los primeros días, ahora obsoleto
- **GPU**: Tarjetas gráficas, más eficientes que las CPU pero también obsoletas para Bitcoin
- **FPGA**: Circuitos integrados programables, fase intermedia
- **ASIC**: Circuitos integrados específicos para minería, actualmente dominantes

#### Pools de minería

- Grupos de mineros que combinan su poder de cómputo
- Comparten recompensas proporcionalmente al poder de cómputo aportado
- Permiten a mineros pequeños recibir recompensas más consistentes

## Módulo 4: Wallets y Seguridad

### Lección 1: Tipos de Wallets

Las wallets (carteras) de criptomonedas son herramientas que permiten a los usuarios almacenar, enviar y recibir criptomonedas. En realidad, lo que almacenan son las claves criptográficas necesarias para acceder a los fondos en la blockchain.

#### Conceptos clave

- **Clave privada**: Código secreto que permite firmar transacciones
- **Clave pública**: Derivada de la clave privada, se usa para generar direcciones
- **Dirección**: Identificador público al que se pueden enviar fondos
- **Frase semilla (seed phrase)**: Conjunto de palabras que permite recuperar todas las claves privadas

#### Tipos de wallets según almacenamiento

1. **Wallets calientes (hot wallets)**
   - Conectadas a internet
   - Más convenientes pero menos seguras
   - Ejemplos: wallets de escritorio, móviles, web

2. **Wallets frías (cold wallets)**
   - No conectadas a internet
   - Más seguras pero menos convenientes
   - Ejemplos: hardware wallets, paper wallets

#### Tipos específicos de wallets

- **Wallets de escritorio**: Software instalado en un ordenador
  - Ejemplos: Electrum, Bitcoin Core, Exodus

- **Wallets móviles**: Aplicaciones para smartphones
  - Ejemplos: Trust Wallet, Coinbase Wallet, MetaMask Mobile

- **Wallets web**: Accesibles a través de navegadores web
  - Ejemplos: MetaMask (extensión), MyEtherWallet

- **Hardware wallets**: Dispositivos físicos especializados
  - Ejemplos: Ledger, Trezor, KeepKey

- **Paper wallets**: Claves impresas en papel
  - Menos comunes actualmente debido a riesgos de uso

- **Wallets custodiales vs no-custodiales**
  - Custodiales: Un tercero (como un exchange) controla tus claves privadas
  - No-custodiales: Tú controlas tus claves privadas ("not your keys, not your coins")

### Lección 2: Mejores Prácticas de Seguridad

La seguridad es fundamental en el mundo de las criptomonedas, ya que las transacciones son irreversibles y los fondos perdidos generalmente no pueden recuperarse.

#### Protección de claves privadas

- **Nunca compartir**: Las claves privadas o frases semilla nunca deben compartirse con nadie
- **Almacenamiento seguro**: Considerar soluciones como placas de metal resistentes al fuego para frases semilla
- **Copias de seguridad**: Mantener múltiples copias en diferentes ubicaciones físicas
- **Evitar medios digitales**: No almacenar en correos electrónicos, fotos o documentos en la nube

#### Autenticación de dos factores (2FA)

- Implementar 2FA en todas las cuentas relacionadas con criptomonedas
- Preferir aplicaciones de autenticación (como Google Authenticator, Authy) sobre SMS
- Considerar dispositivos de seguridad físicos como YubiKey

#### Protección contra phishing

- Verificar siempre las URLs de los sitios web
- No hacer clic en enlaces sospechosos en correos electrónicos o mensajes
- Usar marcadores para acceder a sitios de exchanges y wallets
- Verificar las direcciones de destino antes de enviar fondos

#### Estrategias avanzadas de seguridad

- **Multisig (múltiples firmas)**: Requiere múltiples claves para autorizar transacciones
- **Almacenamiento en frío**: Mantener la mayoría de los fondos en wallets frías
- **Diversificación**: No mantener todos los fondos en un solo lugar
- **Testamento digital**: Plan para que los seres queridos puedan acceder a los fondos en caso de fallecimiento

#### Seguridad operacional (OpSec)

- Evitar discutir públicamente las cantidades de criptomonedas que posees
- Usar VPN al acceder a servicios de criptomonedas
- Mantener el software de wallets y sistemas operativos actualizados
- Considerar el uso de un dispositivo dedicado solo para operaciones con criptomonedas

### Lección 3: Recuperación y Herencia

#### Planes de recuperación

- Documentar claramente los pasos para recuperar acceso a tus fondos
- Probar periódicamente el proceso de recuperación
- Considerar soluciones como Shamir's Secret Sharing para dividir claves entre múltiples personas de confianza

#### Planificación de herencia

- Crear instrucciones claras para herederos
- Considerar servicios de custodia especializados
- Equilibrar seguridad con accesibilidad para herederos
- Consultar con profesionales legales familiarizados con activos digitales
