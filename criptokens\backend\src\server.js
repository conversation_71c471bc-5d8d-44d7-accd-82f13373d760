const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const axios = require('axios');

// Importar rutas
const guruRoutes = require('./routes/guru.routes.js');
const conversationRoutes = require('./routes/conversation.routes.js');
const etherscanRoutes = require('./routes/etherscan.routes.js');
const sentimentRoutes = require('./routes/sentiment.routes.js');
const recommendationsRoutes = require('./routes/recommendations.routes.js');
const ultravoxRoutes = require('./routes/ultravox.routes.js');

// Cargar variables de entorno
dotenv.config();

// Inicializar Express
const app = express();

// Middleware
// Configurar CORS para permitir solicitudes desde orígenes específicos
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:3001'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  credentials: false // Cambiado a false para evitar problemas con '*' en Access-Control-Allow-Origin
}));

// Middleware para manejar preflight OPTIONS y configurar cabeceras CORS adicionales
app.use((req, res, next) => {
  // Configurar cabeceras CORS específicas
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');

  // Manejar las solicitudes OPTIONS (preflight)
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});
app.use(express.json());

// Definir puerto
const PORT = process.env.PORT || 3001;

// Ruta raíz
app.get('/', (req, res) => {
  res.json({ message: "Backend Criptokens funcionando!" });
});

// Usar rutas del Gurú Cripto
app.use('/api/guru', guruRoutes);

// Usar rutas de conversaciones
app.use('/api/conversations', conversationRoutes);

// Usar rutas de Etherscan
app.use('/api/etherscan', etherscanRoutes);

// Usar rutas de análisis de sentimiento
app.use('/api/sentiment', sentimentRoutes);

// Usar rutas de recomendaciones
app.use('/api/recommendations', recommendationsRoutes);

// Usar rutas de Ultravox
app.use('/api/ultravox', ultravoxRoutes);

// Función para verificar la configuración de la API del LLM
const verifyLLMConfig = () => {
  const apiKey = process.env.LLM_API_KEY;
  const apiUrl = process.env.LLM_API_URL;

  if (!apiKey || apiKey === 'your_api_key_here') {
    return { valid: false, message: 'API key no configurada. Por favor, configura LLM_API_KEY en el archivo .env' };
  }

  if (!apiUrl) {
    return { valid: false, message: 'URL de API no configurada. Por favor, configura LLM_API_URL en el archivo .env' };
  }

  return { valid: true };
};

// Función para generar una respuesta simulada (para desarrollo)
const generateMockResponse = (message) => {
  // Respuestas simuladas basadas en palabras clave en el mensaje
  if (message.toLowerCase().includes('hola') || message.toLowerCase().includes('saludos')) {
    return '¡Hola! Soy el asistente de Criptokens. ¿En qué puedo ayudarte hoy?';
  } else if (message.toLowerCase().includes('bitcoin') || message.toLowerCase().includes('btc')) {
    return 'Bitcoin (BTC) es la primera y más conocida criptomoneda, creada en 2009 por una persona o grupo bajo el seudónimo de Satoshi Nakamoto. Funciona en una red descentralizada sin un banco central o administrador único.';
  } else if (message.toLowerCase().includes('ethereum') || message.toLowerCase().includes('eth')) {
    return 'Ethereum (ETH) es una plataforma blockchain descentralizada que permite la creación de contratos inteligentes y aplicaciones descentralizadas (dApps). Fue propuesta por Vitalik Buterin en 2013.';
  } else if (message.toLowerCase().includes('blockchain')) {
    return 'Blockchain es una tecnología de registro distribuido que mantiene un registro continuo de transacciones en bloques enlazados y asegurados mediante criptografía. Es la tecnología subyacente de las criptomonedas.';
  } else {
    return `He recibido tu mensaje: "${message}". Como estamos en modo de desarrollo, estoy generando una respuesta simulada. En producción, esto se conectaría a un modelo de lenguaje real.`;
  }
};

// Ruta para chat (respuesta estándar)
app.post('/api/chat', async (req, res) => {
  const { message } = req.body;
  console.log(`Mensaje recibido: ${message}`);

  // Verificar configuración de LLM
  const configCheck = verifyLLMConfig();

  if (!configCheck.valid) {
    console.warn(configCheck.message);
    // Usar respuesta simulada en desarrollo
    const mockResponse = generateMockResponse(message);
    return res.json({ reply: mockResponse });
  }

  try {
    // En una implementación real, aquí se conectaría con la API del LLM
    // Por ejemplo, para OpenAI:
    /*
    const response = await axios.post(process.env.LLM_API_URL, {
      model: process.env.LLM_MODEL || 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: message }],
      temperature: 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.LLM_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const reply = response.data.choices[0].message.content;
    res.json({ reply });
    */

    // Por ahora, usamos una respuesta simulada
    const mockResponse = generateMockResponse(message);
    res.json({ reply: mockResponse });
  } catch (error) {
    console.error('Error al conectar con la API del LLM:', error);
    res.status(500).json({
      error: 'Error al procesar la solicitud',
      details: error.message
    });
  }
});

// Función para simular streaming de respuestas (para desarrollo)
const simulateStreamingResponse = (message, res) => {
  // Generar una respuesta simulada
  const fullResponse = generateMockResponse(message);

  // Dividir la respuesta en fragmentos para simular streaming
  const chunks = [];
  const words = fullResponse.split(' ');

  // Agrupar palabras en fragmentos de 1-3 palabras
  let currentChunk = '';
  let wordCount = 0;

  for (const word of words) {
    currentChunk += (currentChunk ? ' ' : '') + word;
    wordCount++;

    // Enviar el fragmento después de 1-3 palabras (aleatorio)
    if (wordCount >= Math.floor(Math.random() * 3) + 1 || word === words[words.length - 1]) {
      chunks.push(currentChunk);
      currentChunk = '';
      wordCount = 0;
    }
  }

  // Enviar los fragmentos con un retraso para simular la generación de texto
  let chunkIndex = 0;

  const sendNextChunk = () => {
    if (chunkIndex < chunks.length) {
      const chunk = chunks[chunkIndex];
      res.write(`data: ${JSON.stringify({ content: chunk + ' ' })}\n\n`);
      chunkIndex++;

      // Simular diferentes velocidades de generación
      const delay = Math.floor(Math.random() * 300) + 100; // 100-400ms
      setTimeout(sendNextChunk, delay);
    } else {
      // Enviar señal de finalización
      res.write(`data: ${JSON.stringify({ done: true })}\n\n`);
    }
  };

  // Iniciar el envío de fragmentos
  setTimeout(sendNextChunk, 500); // Pequeño retraso inicial para simular procesamiento
};

// Ruta para streaming de chat (SSE)
app.get('/api/chat/stream', (req, res) => {
  const { message } = req.query;
  console.log(`Mensaje recibido para streaming: ${message}`);

  // Configurar cabeceras para SSE
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  // Permitir CORS para SSE desde orígenes específicos
  res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');

  // Enviar un evento de conexión inicial
  res.write(`data: ${JSON.stringify({ event: 'connected' })}\n\n`);

  // Verificar configuración de LLM
  const configCheck = verifyLLMConfig();

  if (!configCheck.valid) {
    console.warn(configCheck.message);
    // Usar respuesta simulada en desarrollo
    simulateStreamingResponse(message, res);
    return;
  }

  try {
    // En una implementación real, aquí se conectaría con la API del LLM en modo streaming
    // Por ejemplo, para OpenAI:
    /*
    const response = await fetch(process.env.LLM_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.LLM_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: process.env.LLM_MODEL || 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: message }],
        temperature: 0.7,
        stream: true
      })
    });

    // Procesar la respuesta streaming
    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      // Procesar el chunk según el formato de la API
      // y enviar al cliente
      res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`);
    }

    res.write(`data: ${JSON.stringify({ done: true })}\n\n`);
    */

    // Por ahora, usamos una respuesta simulada con streaming
    simulateStreamingResponse(message, res);
  } catch (error) {
    console.error('Error al conectar con la API del LLM para streaming:', error);
    res.write(`data: ${JSON.stringify({ error: 'Error al procesar la solicitud', details: error.message })}\n\n`);
    res.write(`data: ${JSON.stringify({ done: true })}\n\n`);
  }

  // Manejar desconexión del cliente
  req.on('close', () => {
    console.log('Cliente desconectado');
    // Limpiar recursos si es necesario
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`Servidor ejecutándose en http://localhost:${PORT}`);
});
