.enhanced-portfolio {
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 20px;
  background: rgba(15, 15, 45, 0.3);
  border-radius: 20px;
}

/* Portfolio Header */
.portfolio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.portfolio-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0;
  background: linear-gradient(90deg, #ffffff, #a0a0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.portfolio-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #00f2ff, #4657ce);
  border-radius: 3px;
}

.portfolio-actions {
  display: flex;
  gap: 15px;
}

.refresh-button, .add-asset-button, .transactions-button {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.refresh-button {
  background: rgba(30, 30, 60, 0.5);
  color: #a0a0d0;
  border: 1px solid rgba(64, 220, 255, 0.2);
}

.refresh-button:hover:not(:disabled) {
  background: rgba(30, 30, 60, 0.8);
  color: white;
  border-color: rgba(64, 220, 255, 0.4);
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-asset-button {
  background: linear-gradient(90deg, rgba(70, 87, 206, 0.8), rgba(0, 242, 255, 0.8));
  color: white;
}

.transactions-button {
  background: rgba(30, 30, 60, 0.5);
  color: #a0a0d0;
  border: 1px solid rgba(64, 220, 255, 0.2);
}

.transactions-button:hover {
  background: rgba(30, 30, 60, 0.8);
  color: white;
  border-color: rgba(64, 220, 255, 0.4);
}

.add-asset-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.refresh-icon, .add-icon, .transactions-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* Add Asset Form */
.add-asset-form-container {
  background: rgba(20, 20, 50, 0.5);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(64, 220, 255, 0.2);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease-out;
}

.add-asset-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.add-asset-form h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0 0 10px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  color: #a0a0d0;
}

.form-group select, .form-group input {
  padding: 12px;
  background: rgba(30, 30, 60, 0.6);
  border: 1px solid rgba(64, 220, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 16px;
  outline: none;
  transition: all 0.3s;
}

.form-group select:focus, .form-group input:focus {
  border-color: rgba(64, 220, 255, 0.8);
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.3);
}

.form-error {
  color: #f87171;
  font-size: 14px;
  padding: 10px;
  background: rgba(248, 113, 113, 0.1);
  border-radius: 8px;
  border-left: 3px solid #f87171;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 10px;
}

.cancel-button, .submit-button {
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-button {
  background: rgba(30, 30, 60, 0.5);
  color: #a0a0d0;
  border: 1px solid rgba(64, 220, 255, 0.2);
}

.cancel-button:hover {
  background: rgba(30, 30, 60, 0.8);
  color: white;
}

.submit-button {
  background: linear-gradient(90deg, #4657ce, #00f2ff);
  color: white;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Portfolio Overview */
.portfolio-overview {
  display: flex;
  gap: 30px;
}

.portfolio-summary {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.summary-card {
  display: flex;
  align-items: center;
  background: rgba(20, 20, 50, 0.5);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(64, 220, 255, 0.2);
  transition: all 0.3s;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-color: rgba(64, 220, 255, 0.4);
}

.summary-icon {
  font-size: 24px;
  margin-right: 15px;
}

.summary-details {
  display: flex;
  flex-direction: column;
}

.summary-label {
  font-size: 14px;
  color: #a0a0d0;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: white;
}

.positive {
  color: #4ade80;
}

.negative {
  color: #f87171;
}

.date {
  font-size: 16px;
}

.portfolio-chart-container {
  flex: 0 0 350px;
}

/* Portfolio Assets */
.portfolio-assets {
  overflow-x: auto;
}

.assets-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.assets-table thead {
  background: rgba(20, 20, 50, 0.7);
}

.assets-table th {
  padding: 15px;
  text-align: left;
  color: white;
  font-weight: 600;
  border-bottom: 1px solid rgba(64, 220, 255, 0.2);
}

.assets-table tbody tr {
  background: rgba(20, 20, 50, 0.5);
  transition: all 0.3s;
}

.assets-table tbody tr:hover {
  background: rgba(20, 20, 50, 0.8);
}

.assets-table td {
  padding: 15px;
  border-bottom: 1px solid rgba(64, 220, 255, 0.1);
  color: #e0e0ff;
}

.asset-info {
  display: flex;
  align-items: center;
}

.asset-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border-radius: 50%;
}

.asset-name {
  display: block;
  font-weight: 600;
  color: white;
}

.asset-symbol {
  display: block;
  font-size: 12px;
  color: #a0a0d0;
}

.current-price, .asset-value, .profit-loss-value {
  font-weight: 600;
}

.remove-asset-button {
  width: 30px;
  height: 30px;
  background: rgba(248, 113, 113, 0.2);
  border: none;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #f87171;
}

.remove-asset-button:hover {
  background: rgba(248, 113, 113, 0.4);
  transform: scale(1.1);
}

/* Empty Portfolio */
.empty-portfolio {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
  background: rgba(20, 20, 50, 0.5);
  border-radius: 16px;
  border: 1px solid rgba(64, 220, 255, 0.2);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.empty-portfolio h3 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0 0 10px;
}

.empty-portfolio p {
  color: #a0a0d0;
  margin-bottom: 20px;
  max-width: 400px;
}

.add-first-asset-button {
  padding: 12px 24px;
  background: linear-gradient(90deg, #4657ce, #00f2ff);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.add-first-asset-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Transaction History Section */
.transaction-history-section {
  margin-top: 30px;
  animation: fadeIn 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .portfolio-overview {
    flex-direction: column;
  }

  .portfolio-chart-container {
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .portfolio-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .portfolio-actions {
    width: 100%;
  }

  .refresh-button, .add-asset-button, .transactions-button {
    flex: 1;
    justify-content: center;
  }

  .button-text {
    display: none;
  }

  .refresh-icon, .add-icon, .transactions-icon {
    margin-right: 0;
    font-size: 18px;
  }

  .assets-table {
    display: block;
    overflow-x: auto;
  }
}
