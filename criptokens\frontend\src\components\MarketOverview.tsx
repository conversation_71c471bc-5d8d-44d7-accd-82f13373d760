import React from 'react';
import '../styles/MarketOverview.css';

interface MarketOverviewProps {
  globalMarketData: any;
  isLoading: boolean;
}

const MarketOverview: React.FC<MarketOverviewProps> = ({ globalMarketData, isLoading }) => {
  // Función para formatear números grandes
  const formatNumber = (num: number, digits: number = 2): string => {
    if (num >= 1_000_000_000_000) {
      return `${(num / 1_000_000_000_000).toFixed(digits)}T`;
    } else if (num >= 1_000_000_000) {
      return `${(num / 1_000_000_000).toFixed(digits)}B`;
    } else if (num >= 1_000_000) {
      return `${(num / 1_000_000).toFixed(digits)}M`;
    } else if (num >= 1_000) {
      return `${(num / 1_000).toFixed(digits)}K`;
    }
    return num.toFixed(digits);
  };

  return (
    <div className="market-overview">
      <div className="section-header">
        <h2>Resumen del Mercado</h2>
      </div>

      <div className="market-cards">
        {/* Capitalización de Mercado */}
        <div className="market-card">
          <div className="card-header">
            <div className="card-icon market-cap-icon">
              <i className="fas fa-chart-pie"></i>
            </div>
            <h3>Capitalización de Mercado</h3>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="skeleton-loading"></div>
            ) : (
              <>
                <div className="main-value">
                  ${globalMarketData?.total_market_cap?.usd
                    ? formatNumber(globalMarketData.total_market_cap.usd)
                    : '0'}
                </div>
                <div className={`change-value ${globalMarketData?.market_cap_change_percentage_24h_usd >= 0 ? 'positive' : 'negative'}`}>
                  <i className={`fas ${globalMarketData?.market_cap_change_percentage_24h_usd >= 0 ? 'fa-caret-up' : 'fa-caret-down'}`}></i>
                  {globalMarketData?.market_cap_change_percentage_24h_usd
                    ? globalMarketData.market_cap_change_percentage_24h_usd.toFixed(2) + '%'
                    : '0%'}
                  <span className="change-period">24h</span>
                </div>
              </>
            )}
          </div>
          <div className="card-chart">
            {/* Aquí iría un mini gráfico de la capitalización de mercado */}
            <div className="mini-chart market-cap-chart"></div>
          </div>
        </div>

        {/* Volumen de Comercio */}
        <div className="market-card">
          <div className="card-header">
            <div className="card-icon volume-icon">
              <i className="fas fa-exchange-alt"></i>
            </div>
            <h3>Volumen de Comercio 24h</h3>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="skeleton-loading"></div>
            ) : (
              <>
                <div className="main-value">
                  ${globalMarketData?.total_volume?.usd
                    ? formatNumber(globalMarketData.total_volume.usd)
                    : '0'}
                </div>
                <div className="change-value neutral">
                  <span className="change-period">24h</span>
                </div>
              </>
            )}
          </div>
          <div className="card-chart">
            {/* Aquí iría un mini gráfico del volumen de comercio */}
            <div className="mini-chart volume-chart"></div>
          </div>
        </div>

        {/* Dominancia de BTC */}
        <div className="market-card">
          <div className="card-header">
            <div className="card-icon dominance-icon">
              <i className="fab fa-bitcoin"></i>
            </div>
            <h3>Dominancia de BTC</h3>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="skeleton-loading"></div>
            ) : (
              <>
                <div className="main-value">
                  {globalMarketData?.market_cap_percentage?.btc
                    ? globalMarketData.market_cap_percentage.btc.toFixed(1) + '%'
                    : '0%'}
                </div>
                <div className="dominance-secondary">
                  ETH: {globalMarketData?.market_cap_percentage?.eth
                    ? globalMarketData.market_cap_percentage.eth.toFixed(1) + '%'
                    : '0%'}
                </div>
              </>
            )}
          </div>
          <div className="card-chart">
            {/* Aquí iría un mini gráfico de la dominancia */}
            <div className="mini-chart dominance-chart"></div>
          </div>
        </div>

        {/* Sentimiento del Mercado */}
        <div className="market-card">
          <div className="card-header">
            <div className="card-icon sentiment-icon">
              <i className="fas fa-smile"></i>
            </div>
            <h3>Sentimiento del Mercado</h3>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="skeleton-loading"></div>
            ) : (
              <>
                <div className="sentiment-meter">
                  <div className="sentiment-scale">
                    <div className="sentiment-marker" style={{
                      left: `${globalMarketData?.market_cap_change_percentage_24h_usd >= 0 ?
                        Math.min(65 + globalMarketData.market_cap_change_percentage_24h_usd, 95) :
                        Math.max(35 + globalMarketData.market_cap_change_percentage_24h_usd, 5)}%`
                    }}></div>
                    <div className="sentiment-label fear">Miedo</div>
                    <div className="sentiment-label greed">Codicia</div>
                  </div>
                </div>
                <div className="sentiment-value">
                  {globalMarketData?.market_cap_change_percentage_24h_usd >= 3 ? 'Codicia Extrema' :
                   globalMarketData?.market_cap_change_percentage_24h_usd >= 0 ? 'Codicia' :
                   globalMarketData?.market_cap_change_percentage_24h_usd >= -3 ? 'Miedo' : 'Miedo Extremo'}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketOverview;
