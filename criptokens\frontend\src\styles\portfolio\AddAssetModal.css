/* Estilos para el modal de añadir activo */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.add-asset-modal {
  background: var(--bg-dark);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  border: var(--border-light);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-bright);
}

.close-button {
  background: transparent;
  border: none;
  color: var(--text-dim);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.close-button:hover {
  color: var(--text-bright);
}

.modal-content {
  padding: 1.5rem;
}

.funds-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.funds-label {
  color: var(--text-dim);
  font-size: 0.9rem;
}

.funds-value {
  font-weight: 600;
  color: var(--text-bright);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-medium);
}

.crypto-selector {
  margin-bottom: 1rem;
}

.selected-crypto {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.crypto-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
}

.crypto-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.crypto-name {
  font-weight: 600;
  color: var(--text-bright);
}

.crypto-symbol {
  font-size: 0.8125rem;
  color: var(--text-dim);
}

.crypto-price {
  font-weight: 600;
  color: var(--text-medium);
}

.change-crypto-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: var(--text-medium);
  border-radius: var(--radius-sm);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.change-crypto-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.crypto-search {
  position: relative;
}

.search-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  color: var(--text-bright);
  font-size: 0.9375rem;
}

.search-input::placeholder {
  color: var(--text-dim);
}

.crypto-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-dark);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-md);
  max-height: 300px;
  overflow-y: auto;
  z-index: 10;
  margin-top: 0.5rem;
}

.crypto-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.crypto-option:hover {
  background: rgba(255, 255, 255, 0.05);
}

.no-results {
  padding: 1rem;
  text-align: center;
  color: var(--text-dim);
}

.amount-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  color: var(--text-bright);
  font-size: 0.9375rem;
}

.amount-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.total-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.total-label {
  font-weight: 600;
  color: var(--text-medium);
}

.total-amount {
  font-weight: 700;
  color: var(--text-bright);
  font-size: 1.1rem;
}

.notes-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  color: var(--text-bright);
  font-size: 0.9375rem;
  min-height: 100px;
  resize: vertical;
}

.error-message {
  background-color: rgba(255, 0, 0, 0.1);
  color: var(--error);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 0, 0, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.cancel-button,
.add-button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.cancel-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.add-button {
  background: var(--gradient-primary);
  color: var(--text-bright);
  border: none;
  box-shadow: var(--shadow-sm);
}

.add-button:hover:not(:disabled) {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-2px);
}

.add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .add-asset-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-content {
    padding: 1.25rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .cancel-button,
  .add-button {
    width: 100%;
  }
}
