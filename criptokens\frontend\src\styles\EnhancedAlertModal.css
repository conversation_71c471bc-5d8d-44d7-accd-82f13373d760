.enhanced-alert-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.enhanced-alert-modal-content {
  background-color: #1a1a2e;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  color: #e6e6e6;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhanced-alert-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #2a2a4a;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #16213e;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.enhanced-alert-modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-price {
  font-size: 0.9rem;
  color: #8a8aaa;
  margin-left: 15px;
}

.enhanced-alert-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #8a8aaa;
  cursor: pointer;
  transition: color 0.2s;
}

.enhanced-alert-modal-close:hover {
  color: #ff6b6b;
}

.enhanced-alert-modal-body {
  padding: 20px;
  flex: 1;
}

.alert-mode-selector {
  display: flex;
  margin-bottom: 20px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #2a2a4a;
}

.mode-button {
  flex: 1;
  padding: 12px;
  background-color: #252547;
  border: none;
  color: #8a8aaa;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.mode-button:hover {
  background-color: #2a2a4a;
  color: #e6e6e6;
}

.mode-button.active {
  background-color: #0f3460;
  color: #ffffff;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #b8b8d4;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #2a2a4a;
  border-radius: 4px;
  background-color: #252547;
  color: #e6e6e6;
  font-size: 0.9rem;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #4361ee;
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.3);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 5px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-label input {
  margin-right: 10px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-label input {
  margin-right: 10px;
}

.notification-options {
  margin-top: 20px;
  background-color: #252547;
  border-radius: 6px;
  padding: 15px;
}

.notification-options h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #b8b8d4;
  font-size: 1rem;
}

.target-price-display {
  background-color: #1e1e30;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #b8b8d4;
}

.alert-info {
  margin-top: 20px;
  background-color: rgba(67, 97, 238, 0.1);
  border-left: 3px solid #4361ee;
  padding: 10px 15px;
  border-radius: 4px;
}

.alert-info p {
  margin: 0;
  font-size: 0.85rem;
  color: #b8b8d4;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.alert-info i {
  color: #4361ee;
  font-size: 1rem;
  margin-top: 2px;
}

.enhanced-alert-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #2a2a4a;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #1e1e30;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #4361ee;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #3a56d4;
}

.btn-primary:disabled {
  background-color: #2a2a4a;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: transparent;
  color: #b8b8d4;
  border: 1px solid #2a2a4a;
}

.btn-secondary:hover {
  background-color: #2a2a4a;
  color: #e6e6e6;
}

/* Responsive */
@media (max-width: 768px) {
  .enhanced-alert-modal-content {
    width: 95%;
  }
  
  .enhanced-alert-modal-header h3 {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .current-price {
    margin-left: 0;
    margin-top: 5px;
  }
}
