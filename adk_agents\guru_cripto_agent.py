"""
<PERSON>ripto Agent for Criptokens
"""
import os
import asyncio
from google.adk.agents.llm_agent import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai.types import Content, Part

# Set the Google API key
os.environ["GOOGLE_API_KEY"] = "AIzaSyCashyNX3fNv-bj5xOCtTDMfL_-vGm9Yag"

# Create the Guru Cripto agent
guru_cripto_agent = LlmAgent(
    name="guru_cripto_agent",
    model="gemini-1.5-pro",
    description="<PERSON> is a cryptocurrency expert with deep knowledge of technical analysis, market sentiment, and on-chain metrics.",
    instruction="""
    You are <PERSON>, a cryptocurrency expert with deep knowledge of technical analysis, market sentiment, and on-chain metrics.
    
    Your capabilities include:
    1. Providing technical analysis of cryptocurrency price movements
    2. Analyzing market sentiment from news and social media
    3. Interpreting on-chain data and blockchain metrics
    4. Making informed price predictions based on comprehensive analysis
    5. Explaining complex cryptocurrency concepts in simple terms
    
    When responding to queries:
    - Be clear, concise, and informative
    - Support your analysis with specific data points
    - Explain technical terms when necessary
    - Provide balanced perspectives, acknowledging both bullish and bearish factors
    - For price predictions, emphasize that they are estimates based on current data, not guarantees
    
    Remember that you are an educational resource, not a financial advisor. Always remind users to do their own research and not to make investment decisions solely based on your analysis.
    """
)

# Create a session service
session_service = InMemorySessionService()

# Create a runner
guru_cripto_runner = Runner(
    agent=guru_cripto_agent,
    app_name="criptokens",
    session_service=session_service
)

async def run_guru_cripto(query, user_id="user"):
    """
    Run the Guru Cripto agent with a query
    
    Args:
        query (str): The query to send to the agent
        user_id (str): The user ID for the session
        
    Returns:
        str: The response from the agent
    """
    # Create a session
    session = session_service.create_session(
        user_id=user_id,
        app_name="criptokens"
    )
    
    # Create a message
    message = Content(
        role="user",
        parts=[Part(text=query)]
    )
    
    # Run the agent with the session
    response_text = ""
    async for event in guru_cripto_runner.run_async(
        user_id=user_id,
        session_id=session.id,
        new_message=message
    ):
        if hasattr(event, 'content') and event.content and event.content.parts:
            for part in event.content.parts:
                if hasattr(part, 'text') and part.text:
                    response_text += part.text
    
    return response_text

# Example usage
if __name__ == "__main__":
    query = "What's your prediction for Bitcoin in the next week?"
    response = asyncio.run(run_guru_cripto(query))
    print(f"Query: {query}")
    print(f"Response: {response}")
