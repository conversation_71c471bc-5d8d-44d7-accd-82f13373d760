.course-search-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  padding: 1rem;
}

/* Filtros */
.search-filters {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  height: fit-content;
  position: sticky;
  top: 1rem;
}

.search-input-container {
  position: relative;
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--color-surface-light);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-transparent);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1rem;
}

.filter-section {
  margin-bottom: 1.5rem;
}

.filter-section h3 {
  font-size: 1rem;
  margin: 0 0 0.75rem 0;
  color: var(--text-primary);
}

.level-filters, .tag-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.level-filter, .tag-filter {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.level-filter:hover, .tag-filter:hover {
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
}

.level-filter.active, .tag-filter.active {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.clear-filters {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.clear-filters:hover {
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
}

/* Resultados */
.search-results {
  flex: 1;
}

.results-header {
  margin-bottom: 1.5rem;
}

.results-header h2 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.no-results {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.course-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.course-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
  transform: scale(1.05);
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background-color: var(--color-surface-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.placeholder-image::before {
  content: '\f19d';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  font-size: 3rem;
}

.course-level {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.35rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

.course-level.beginner {
  background-color: #2ecc71;
}

.course-level.intermediate {
  background-color: #3498db;
}

.course-level.advanced {
  background-color: #9b59b6;
}

.course-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.course-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.course-description {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.course-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.course-duration {
  font-size: 0.85rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.course-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  font-size: 0.8rem;
}

.course-instructor {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.course-instructor img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.course-instructor span {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.view-course-button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: var(--color-primary);
  color: white;
  text-align: center;
  text-decoration: none;
  font-size: 0.95rem;
  transition: background-color 0.2s ease;
  margin-top: auto;
}

.view-course-button:hover {
  background-color: var(--color-primary-dark);
}

/* Responsive */
@media (max-width: 992px) {
  .course-search-container {
    grid-template-columns: 1fr;
  }
  
  .search-filters {
    position: static;
    margin-bottom: 1.5rem;
  }
  
  .level-filters, .tag-filters {
    flex-wrap: wrap;
  }
}
