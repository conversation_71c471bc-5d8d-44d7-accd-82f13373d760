.certificate-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.certificate {
  position: relative;
  width: 100%;
  aspect-ratio: 1.414 / 1; /* A4 paper ratio */
  background-color: #fff;
  color: #333;
  padding: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.certificate-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.certificate-border {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 2px solid rgba(123, 97, 255, 0.2);
  border-radius: 8px;
}

.certificate-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  background-image: url('/images/academy-logo.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.05;
}

.certificate-header,
.certificate-content,
.certificate-footer {
  position: relative;
  z-index: 1;
}

.certificate-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
}

.certificate-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 1.5rem;
}

.certificate-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.certificate-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #7b61ff;
  text-align: center;
  margin: 0;
  letter-spacing: 1px;
}

.certificate-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 3rem;
}

.certificate-text {
  font-size: 1.2rem;
  margin: 0.5rem 0;
  text-align: center;
  color: #555;
}

.certificate-name {
  font-size: 2.2rem;
  font-weight: 700;
  color: #333;
  margin: 1rem 0;
  text-align: center;
  font-family: 'Playfair Display', serif;
}

.certificate-course {
  font-size: 1.8rem;
  font-weight: 600;
  color: #7b61ff;
  margin: 1rem 0;
  text-align: center;
  max-width: 80%;
}

.certificate-date {
  font-size: 1.1rem;
  margin: 1rem 0;
  text-align: center;
  color: #555;
}

.certificate-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
}

.certificate-signature {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.signature-line {
  width: 200px;
  height: 2px;
  background-color: #333;
  margin-bottom: 0.5rem;
}

.signature-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.signature-title {
  font-size: 1rem;
  color: #555;
  margin: 0.25rem 0 0 0;
}

.certificate-verification {
  text-align: right;
}

.verification-id {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #7b61ff;
}

.verification-text {
  font-size: 0.9rem;
  color: #555;
  margin: 0.25rem 0 0 0;
}

.certificate-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: var(--color-primary-transparent);
}

/* Responsive styles */
@media (max-width: 768px) {
  .certificate {
    padding: 2rem;
  }
  
  .certificate-title {
    font-size: 2rem;
  }
  
  .certificate-name {
    font-size: 1.8rem;
  }
  
  .certificate-course {
    font-size: 1.5rem;
  }
  
  .certificate-footer {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }
  
  .certificate-verification {
    text-align: center;
  }
  
  .certificate-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .certificate-actions button {
    width: 100%;
  }
}
