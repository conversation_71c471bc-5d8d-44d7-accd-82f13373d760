import axios from 'axios';
import { mockTopCryptos, mockGlobalData, mockHistoricalData, generateHistoricalData } from './mockData';
import { cryptoApiClient } from './mcpClient';

// API base URL para CoinGecko (como fallback)
const COINGECKO_API_URL = 'https://api.coingecko.com/api/v3';

// Crear una instancia de axios con la URL base
const api = axios.create({
  baseURL: COINGECKO_API_URL,
  timeout: 10000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  }
});

// Usar datos simulados o reales
const useMockData = false; // Intentar usar datos reales por defecto
const useMcpServer = true; // Intentar usar el servidor MCP por defecto

// Añadir un log para verificar la configuración
console.log('API configurada para usar datos reales (useMockData:', useMockData, ', useMcpServer:', useMcpServer, ')');
console.log('IMPORTANTE: Se ha forzado el uso de datos reales para esta sesión.');

// Verificar la conexión con el servidor MCP
const checkMcpServerAvailability = async () => {
  try {
    const response = await fetch('http://localhost:3101/tools');
    if (response.ok) {
      console.log('Servidor MCP disponible y respondiendo correctamente');
      return true;
    } else {
      console.error(`Servidor MCP respondió con estado ${response.status}: ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.error('Error al verificar disponibilidad del servidor MCP:', error);
    return false;
  }
};

// Verificar la disponibilidad del servidor MCP al cargar la página
checkMcpServerAvailability().then(isAvailable => {
  if (isAvailable) {
    console.log('Servidor MCP disponible. Se usarán datos reales.');
  } else {
    console.warn('Servidor MCP no disponible. Se usarán datos simulados como fallback.');
  }
});

// Comentario para mantener la estructura del archivo
// Los datos simulados ahora se importan desde mockData.ts

// Función para obtener los datos del mercado global
export const getGlobalMarketData = async () => {
  try {
    if (useMockData) {
      console.log('Usando datos simulados para getGlobalMarketData');
      return mockGlobalData;
    }

    // Usar el servidor MCP
    if (useMcpServer) {
      console.log('Obteniendo datos del mercado global a través del servidor MCP...');
      try {
        // El servidor MCP no tiene un endpoint específico para datos globales,
        // así que calculamos algunos datos a partir de las principales criptomonedas
        const topCryptos = await cryptoApiClient.getTopCryptocurrencies(20);
        console.log('Datos de top criptomonedas recibidos para calcular datos globales:', topCryptos);

        // Calcular la capitalización total del mercado sumando las principales criptomonedas
        let totalMarketCap = 0;
        let totalVolume = 0;
        const marketCapPercentage: Record<string, number> = {};

        topCryptos.forEach(crypto => {
          totalMarketCap += crypto.market_cap || 0;
          totalVolume += crypto.total_volume || 0;

          // Calcular el porcentaje de dominancia
          if (crypto.market_cap) {
            marketCapPercentage[crypto.symbol] = 0; // Lo calcularemos después
          }
        });

        // Calcular los porcentajes de dominancia
        Object.keys(marketCapPercentage).forEach(symbol => {
          const crypto = topCryptos.find(c => c.symbol === symbol);
          if (crypto && crypto.market_cap) {
            marketCapPercentage[symbol] = (crypto.market_cap / totalMarketCap) * 100;
          }
        });

        // Crear un objeto con el formato esperado por el frontend
        const globalData = {
          data: {
            active_cryptocurrencies: 10000, // Valor estimado
            markets: 800, // Valor estimado
            total_market_cap: {
              usd: totalMarketCap
            },
            total_volume: {
              usd: totalVolume
            },
            market_cap_percentage: marketCapPercentage,
            market_cap_change_percentage_24h_usd: 0, // No tenemos este dato
            updated_at: Date.now() / 1000
          }
        };

        console.log('Datos globales calculados:', globalData);
        return globalData;
      } catch (mcpError) {
        console.error('Error al obtener datos del mercado global desde MCP:', mcpError);
        throw mcpError; // Propagar el error para que se maneje en el catch principal
      }
    }

    // Si no se usa el servidor MCP, intentar con CoinGecko
    console.log('Obteniendo datos del mercado global desde CoinGecko...');
    const response = await api.get('/global');
    return response.data;
  } catch (error) {
    console.error('Error fetching global market data:', error);
    console.log('Fallback a datos simulados para getGlobalMarketData');
    return mockGlobalData;
  }
};

// Función para obtener las principales criptomonedas
export const getTopCryptocurrencies = async (limit = 10, page = 1) => {
  try {
    if (useMockData) {
      console.log('Usando datos simulados para getTopCryptocurrencies');
      return mockTopCryptos.slice(0, limit);
    }

    // Usar el servidor MCP
    if (useMcpServer) {
      console.log('Obteniendo top criptomonedas desde el servidor MCP...');
      const data = await cryptoApiClient.getTopCryptocurrencies(limit, page);
      console.log('Datos recibidos del servidor MCP:', data);
      return data;
    }

    // Si no se usa el servidor MCP, intentar con CoinGecko
    console.log('Obteniendo top criptomonedas desde CoinGecko...');
    const response = await api.get('/coins/markets', {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: limit,
        page: page,
        sparkline: true,
        price_change_percentage: '24h'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching top cryptocurrencies:', error);
    console.log('Fallback a datos simulados para getTopCryptocurrencies');
    return mockTopCryptos.slice(0, limit);
  }
};

// Función para obtener los datos históricos de una criptomoneda
export const getCryptoHistoricalData = async (id: string, days = 7) => {
  try {
    if (useMockData) {
      console.log('Usando datos simulados para getCryptoHistoricalData para', id);
      // Usar datos específicos para cada criptomoneda si están disponibles
      if (mockHistoricalData[id as keyof typeof mockHistoricalData]) {
        console.log('Usando datos históricos específicos para', id);
        return mockHistoricalData[id as keyof typeof mockHistoricalData];
      } else {
        // Si no hay datos específicos, generar datos nuevos
        console.log('Generando datos históricos para', id);
        const basePrice = id === 'bitcoin' ? 60000 :
                         id === 'ethereum' ? 3500 :
                         id === 'binancecoin' ? 600 :
                         id === 'solana' ? 140 :
                         id === 'ripple' ? 0.54 :
                         id === 'cardano' ? 0.45 : 100;

        const volatility = basePrice * 0.1; // 10% de volatilidad
        return generateHistoricalData(typeof days === 'number' ? days : 30, basePrice, volatility);
      }
    }

    // Usar el servidor MCP
    if (useMcpServer) {
      console.log(`Obteniendo datos históricos para ${id} desde el servidor MCP...`);
      try {
        const data = await cryptoApiClient.getCryptoHistoricalData(id, typeof days === 'number' ? days : 30);
        console.log(`Datos históricos recibidos del servidor MCP para ${id}:`, data);
        return data;
      } catch (mcpError) {
        console.error(`Error al obtener datos históricos para ${id} desde MCP:`, mcpError);
        throw mcpError; // Propagar el error para que se maneje en el catch principal
      }
    }

    // Si no se usa el servidor MCP, intentar con CoinGecko
    console.log(`Obteniendo datos históricos para ${id} desde CoinGecko...`);
    const response = await api.get(`/coins/${id}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days: days
      }
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching historical data for ${id}:`, error);
    console.log('Fallback a datos simulados para getCryptoHistoricalData');
    // Usar datos de Bitcoin como fallback
    return mockHistoricalData.bitcoin || generateHistoricalData(30, 60000, 5000);
  }
};

// Función para obtener los detalles de una criptomoneda
export const getCryptoDetails = async (id: string) => {
  try {
    if (useMockData) {
      console.log('Usando datos simulados para getCryptoDetails');
      const crypto = mockTopCryptos.find(c => c.id === id);
      return crypto || mockTopCryptos[0];
    }

    // Usar el servidor MCP
    if (useMcpServer) {
      console.log(`Obteniendo detalles para ${id} desde el servidor MCP...`);
      try {
        const data = await cryptoApiClient.getCryptoPrice(id);
        console.log(`Detalles recibidos del servidor MCP para ${id}:`, data);
        return data;
      } catch (mcpError) {
        console.error(`Error al obtener detalles para ${id} desde MCP:`, mcpError);
        throw mcpError; // Propagar el error para que se maneje en el catch principal
      }
    }

    // Si no se usa el servidor MCP, intentar con CoinGecko
    console.log(`Obteniendo detalles para ${id} desde CoinGecko...`);
    const response = await api.get(`/coins/${id}`, {
      params: {
        localization: false,
        tickers: false,
        market_data: true,
        community_data: false,
        developer_data: false,
        sparkline: false
      }
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching details for ${id}:`, error);
    console.log('Fallback a datos simulados para getCryptoDetails');
    const crypto = mockTopCryptos.find(c => c.id === id);
    return crypto || mockTopCryptos[0];
  }
};

// Función para buscar criptomonedas
export const searchCryptos = async (query: string) => {
  try {
    if (useMockData) {
      console.log('Usando datos simulados para searchCryptos');
      return {
        coins: mockTopCryptos.filter(crypto =>
          crypto.name.toLowerCase().includes(query.toLowerCase()) ||
          crypto.symbol.toLowerCase().includes(query.toLowerCase())
        )
      };
    }

    // Usar el servidor MCP
    if (useMcpServer) {
      console.log(`Buscando criptomonedas con término "${query}" desde el servidor MCP...`);
      try {
        const data = await cryptoApiClient.searchCryptocurrencies(query);
        console.log(`Resultados de búsqueda recibidos del servidor MCP para "${query}":`, data);
        return data;
      } catch (mcpError) {
        console.error(`Error al buscar criptomonedas con término "${query}" desde MCP:`, mcpError);
        throw mcpError; // Propagar el error para que se maneje en el catch principal
      }
    }

    // Si no se usa el servidor MCP, intentar con CoinGecko
    console.log(`Buscando criptomonedas con término "${query}" desde CoinGecko...`);
    const response = await api.get('/search', {
      params: {
        query: query
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error searching cryptocurrencies:', error);
    console.log('Fallback a datos simulados para searchCryptos');
    return {
      coins: mockTopCryptos.filter(crypto =>
        crypto.name.toLowerCase().includes(query.toLowerCase()) ||
        crypto.symbol.toLowerCase().includes(query.toLowerCase())
      )
    };
  }
};

// Función para obtener los detalles completos de una criptomoneda
export const getCoinDetails = async (id: string) => {
  try {
    if (useMockData) {
      console.log('Usando datos simulados para getCoinDetails');
      // Crear un objeto con más detalles basado en los datos simulados
      const crypto = mockTopCryptos.find(c => c.id === id);
      if (crypto) {
        return {
          id: crypto.id,
          name: crypto.name,
          symbol: crypto.symbol,
          image: {
            large: crypto.image,
            small: crypto.image,
            thumb: crypto.image
          },
          market_cap_rank: crypto.market_cap_rank,
          description: {
            en: `${crypto.name} is a cryptocurrency that aims to revolutionize the digital economy.`,
            es: `${crypto.name} es una criptomoneda que busca revolucionar la economía digital.`
          },
          links: {
            homepage: [`https://${crypto.id}.org`],
            blockchain_site: [`https://explorer.${crypto.id}.org`],
            official_forum_url: [`https://forum.${crypto.id}.org`],
            subreddit_url: `https://reddit.com/r/${crypto.id}`,
            twitter_screen_name: crypto.id,
            facebook_username: crypto.id,
            telegram_channel_identifier: crypto.id,
            repos_url: {
              github: [`https://github.com/${crypto.id}`]
            }
          },
          market_data: {
            current_price: {
              usd: crypto.current_price
            },
            market_cap: {
              usd: crypto.market_cap
            },
            total_volume: {
              usd: crypto.total_volume
            },
            fully_diluted_valuation: {
              usd: crypto.market_cap * 1.2
            },
            circulating_supply: crypto.circulating_supply || crypto.market_cap / crypto.current_price,
            total_supply: (crypto.circulating_supply || crypto.market_cap / crypto.current_price) * 1.2,
            max_supply: (crypto.circulating_supply || crypto.market_cap / crypto.current_price) * 1.5,
            price_change_percentage_24h: crypto.price_change_percentage_24h,
            price_change_percentage_7d: crypto.price_change_percentage_24h * 2,
            price_change_percentage_14d: crypto.price_change_percentage_24h * 3,
            price_change_percentage_30d: crypto.price_change_percentage_24h * 4,
            price_change_percentage_60d: crypto.price_change_percentage_24h * 5,
            price_change_percentage_200d: crypto.price_change_percentage_24h * 6,
            price_change_percentage_1y: crypto.price_change_percentage_24h * 7,
            market_cap_change_percentage_24h: crypto.price_change_percentage_24h * 0.8,
            ath: {
              usd: crypto.current_price * 2
            },
            ath_change_percentage: {
              usd: -50
            },
            ath_date: {
              usd: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()
            },
            atl: {
              usd: crypto.current_price * 0.2
            },
            atl_change_percentage: {
              usd: 400
            },
            atl_date: {
              usd: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString()
            }
          },
          categories: ['Cryptocurrency', crypto.id === 'bitcoin' ? 'Store of Value' : 'Smart Contract Platform']
        };
      }
      return mockTopCryptos[0];
    }

    // Usar el servidor MCP o CoinGecko
    console.log(`Obteniendo detalles completos para ${id}...`);
    const response = await api.get(`/coins/${id}`, {
      params: {
        localization: true,
        tickers: false,
        market_data: true,
        community_data: true,
        developer_data: true,
        sparkline: false
      }
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching details for ${id}:`, error);
    console.log('Fallback a datos simulados para getCoinDetails');
    // Crear un objeto con más detalles basado en los datos simulados
    const crypto = mockTopCryptos.find(c => c.id === id) || mockTopCryptos[0];
    return {
      id: crypto.id,
      name: crypto.name,
      symbol: crypto.symbol,
      image: {
        large: crypto.image,
        small: crypto.image,
        thumb: crypto.image
      },
      market_cap_rank: crypto.market_cap_rank,
      description: {
        en: `${crypto.name} is a cryptocurrency that aims to revolutionize the digital economy.`,
        es: `${crypto.name} es una criptomoneda que busca revolucionar la economía digital.`
      },
      links: {
        homepage: [`https://${crypto.id}.org`],
        blockchain_site: [`https://explorer.${crypto.id}.org`],
        official_forum_url: [`https://forum.${crypto.id}.org`],
        subreddit_url: `https://reddit.com/r/${crypto.id}`,
        twitter_screen_name: crypto.id,
        facebook_username: crypto.id,
        telegram_channel_identifier: crypto.id,
        repos_url: {
          github: [`https://github.com/${crypto.id}`]
        }
      },
      market_data: {
        current_price: {
          usd: crypto.current_price
        },
        market_cap: {
          usd: crypto.market_cap
        },
        total_volume: {
          usd: crypto.total_volume
        },
        fully_diluted_valuation: {
          usd: crypto.market_cap * 1.2
        },
        circulating_supply: crypto.circulating_supply || crypto.market_cap / crypto.current_price,
        total_supply: (crypto.circulating_supply || crypto.market_cap / crypto.current_price) * 1.2,
        max_supply: (crypto.circulating_supply || crypto.market_cap / crypto.current_price) * 1.5,
        price_change_percentage_24h: crypto.price_change_percentage_24h,
        price_change_percentage_7d: crypto.price_change_percentage_24h * 2,
        price_change_percentage_14d: crypto.price_change_percentage_24h * 3,
        price_change_percentage_30d: crypto.price_change_percentage_24h * 4,
        price_change_percentage_60d: crypto.price_change_percentage_24h * 5,
        price_change_percentage_200d: crypto.price_change_percentage_24h * 6,
        price_change_percentage_1y: crypto.price_change_percentage_24h * 7,
        market_cap_change_percentage_24h: crypto.price_change_percentage_24h * 0.8,
        ath: {
          usd: crypto.current_price * 2
        },
        ath_change_percentage: {
          usd: -50
        },
        ath_date: {
          usd: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()
        },
        atl: {
          usd: crypto.current_price * 0.2
        },
        atl_change_percentage: {
          usd: 400
        },
        atl_date: {
          usd: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString()
        }
      },
      categories: ['Cryptocurrency', crypto.id === 'bitcoin' ? 'Store of Value' : 'Smart Contract Platform']
    };
  }
};

// Función para obtener los mercados donde se comercia una criptomoneda
export const getCoinMarkets = async (id: string) => {
  try {
    if (useMockData) {
      console.log('Usando datos simulados para getCoinMarkets');
      // Crear mercados simulados
      const exchanges = ['Binance', 'Coinbase', 'Kraken', 'Huobi', 'KuCoin', 'Bitfinex', 'Bitstamp', 'OKX', 'Bybit', 'Gate.io'];
      const pairs = ['USDT', 'USD', 'BTC', 'EUR', 'BUSD', 'USDC'];

      return exchanges.flatMap((exchange, i) => {
        return pairs.slice(0, Math.floor(Math.random() * 4) + 2).map((pair, j) => {
          const basePrice = mockTopCryptos.find(c => c.id === id)?.current_price || 100;
          const price = basePrice * (0.98 + Math.random() * 0.04); // Variación de ±2%
          const volume = Math.random() * 10000000 + 1000000;

          return {
            exchange,
            exchange_logo: `https://example.com/${exchange.toLowerCase()}.png`,
            pair: `${id.toUpperCase()}/${pair}`,
            price,
            volume,
            trust_score: Math.floor(Math.random() * 5) + 1,
            trade_url: `https://${exchange.toLowerCase()}.com/trade/${id.toLowerCase()}_${pair.toLowerCase()}`
          };
        });
      });
    }

    // Usar CoinGecko
    console.log(`Obteniendo mercados para ${id}...`);
    const response = await api.get(`/coins/${id}/tickers`, {
      params: {
        include_exchange_logo: true,
        order: 'volume_desc'
      }
    });

    // Transformar los datos al formato esperado
    return response.data.tickers.map((ticker: any) => ({
      exchange: ticker.market.name,
      exchange_logo: ticker.market.logo,
      pair: `${ticker.base}/${ticker.target}`,
      price: ticker.last,
      volume: ticker.volume,
      trust_score: ticker.trust_score,
      trade_url: ticker.trade_url
    }));
  } catch (error) {
    console.error(`Error fetching markets for ${id}:`, error);
    console.log('Fallback a datos simulados para getCoinMarkets');
    // Crear mercados simulados
    const exchanges = ['Binance', 'Coinbase', 'Kraken', 'Huobi', 'KuCoin', 'Bitfinex', 'Bitstamp', 'OKX', 'Bybit', 'Gate.io'];
    const pairs = ['USDT', 'USD', 'BTC', 'EUR', 'BUSD', 'USDC'];

    return exchanges.flatMap((exchange, i) => {
      return pairs.slice(0, Math.floor(Math.random() * 4) + 2).map((pair, j) => {
        const basePrice = mockTopCryptos.find(c => c.id === id)?.current_price || 100;
        const price = basePrice * (0.98 + Math.random() * 0.04); // Variación de ±2%
        const volume = Math.random() * 10000000 + 1000000;

        return {
          exchange,
          exchange_logo: `https://example.com/${exchange.toLowerCase()}.png`,
          pair: `${id.toUpperCase()}/${pair}`,
          price,
          volume,
          trust_score: Math.floor(Math.random() * 5) + 1,
          trade_url: `https://${exchange.toLowerCase()}.com/trade/${id.toLowerCase()}_${pair.toLowerCase()}`
        };
      });
    });
  }
};

// Función para obtener datos históricos con formato específico
export const getHistoricalData = async (id: string, timeRange: string) => {
  try {
    // Convertir el rango de tiempo a días
    let days;
    switch (timeRange) {
      case '24h':
        days = 1;
        break;
      case '7d':
        days = 7;
        break;
      case '14d':
        days = 14;
        break;
      case '30d':
        days = 30;
        break;
      case '90d':
        days = 90;
        break;
      case '1y':
        days = 365;
        break;
      case 'max':
        days = 'max';
        break;
      default:
        days = 7;
    }

    // Usar la función existente para obtener los datos históricos
    return await getCryptoHistoricalData(id, days);
  } catch (error) {
    console.error(`Error fetching historical data for ${id} with timeRange ${timeRange}:`, error);
    // Generar datos históricos simulados
    const basePrice = mockTopCryptos.find(c => c.id === id)?.current_price || 100;
    const volatility = basePrice * 0.1; // 10% de volatilidad
    const daysNumber = timeRange === 'max' ? 1000 :
                      timeRange === '1y' ? 365 :
                      timeRange === '90d' ? 90 :
                      timeRange === '30d' ? 30 :
                      timeRange === '14d' ? 14 :
                      timeRange === '7d' ? 7 : 1;

    return generateHistoricalData(daysNumber, basePrice, volatility);
  }
};

export default api;
