/* Estilos para la página de detalle de criptomoneda */

.coin-detail-container {
  width: 100%;
  max-width: 100%;
  padding: clamp(0.5rem, 2vw, 2rem);
  background-color: var(--color-background);
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 2rem);
  box-sizing: border-box;
}

/* Cabecera con información básica */
.coin-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(1rem, 1.5vw, 1.5rem);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  flex-wrap: wrap;
  gap: 1.5rem;
  border: 1px solid var(--border-color);
}

.coin-basic-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.coin-logo {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: contain;
  background-color: white;
  padding: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coin-name-container {
  display: flex;
  flex-direction: column;
}

.coin-name {
  font-size: clamp(1.25rem, 2vw, 1.5rem);
  font-weight: var(--font-weight-bold);
  margin: 0;
  color: var(--text-primary);
}

.coin-symbol {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.coin-rank {
  font-size: 0.75rem;
  color: var(--color-primary);
  background-color: var(--color-primary-transparent);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-top: 0.5rem;
  display: inline-block;
}

.coin-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.coin-current-price {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.coin-price-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.coin-price-change.positive {
  color: var(--color-positive);
  background-color: rgba(0, 255, 157, 0.1);
}

.coin-price-change.negative {
  color: var(--color-negative);
  background-color: rgba(255, 58, 110, 0.1);
}

.coin-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.watchlist-button, .guru-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.watchlist-button {
  background-color: var(--color-surface);
  color: var(--text-secondary);
}

.watchlist-button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.watchlist-button.active {
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.guru-button {
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.guru-button:hover {
  background-color: var(--color-primary);
  color: white;
}

/* Pestañas de navegación */
.coin-detail-tabs {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--color-primary);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
}

/* Controles del gráfico */
.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.time-range-buttons {
  display: flex;
  gap: 0.25rem;
  overflow-x: auto;
  padding-bottom: 0.25rem;
}

.time-button {
  padding: 0.375rem 0.75rem;
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.time-button.active {
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.chart-type-buttons {
  display: flex;
  gap: 0.25rem;
}

.chart-type-button {
  padding: 0.375rem 0.75rem;
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-type-button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.chart-type-button.active {
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

/* Contenedor del gráfico */
.chart-container {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: clamp(0.75rem, 1.5vw, 1.5rem);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  height: 400px;
  margin-bottom: clamp(1rem, 2vw, 2rem);
}

/* Contenido principal */
.coin-detail-content {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 2rem);
}

/* Visión general */
.overview-container {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 2rem);
}

.stats-and-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: clamp(1rem, 2vw, 2rem);
}

.coin-description {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.coin-description h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.description-content {
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.description-content a {
  color: var(--color-primary);
  text-decoration: none;
}

.description-content a:hover {
  text-decoration: underline;
}

/* Noticias */
.coin-news {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  margin-top: clamp(1rem, 2vw, 2rem);
}

.coin-news .featured-news-container {
  background-color: transparent;
  box-shadow: none;
  padding: 0;
  margin-bottom: 0;
}

.coin-news .featured-news-header h2 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

/* Enlaces */
.coin-links {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-md);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.coin-links h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.links-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.coin-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--color-surface-dark);
  border-radius: 4px;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.coin-link:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
  border-color: var(--border-color-hover);
}

/* Modal del Guru */
.guru-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.guru-modal {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.guru-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.guru-modal-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: var(--text-primary);
}

.guru-modal-content {
  padding: 1.5rem;
  display: flex;
  gap: 1.5rem;
  overflow-y: auto;
}

.guru-avatar-container {
  flex-shrink: 0;
}

.guru-message {
  flex: 1;
}

.guru-message p {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-primary);
}

.guru-suggestions {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.guru-suggestions li {
  padding: 0.75rem 1rem;
  background-color: var(--color-surface-dark);
  border-radius: 8px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.guru-suggestions li:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
  border-color: var(--border-color-hover);
}

.guru-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 0.75rem;
}

.guru-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
  font-size: 0.875rem;
  outline: none;
  transition: all 0.2s ease;
}

.guru-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-transparent);
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: none;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-button:hover {
  background-color: var(--color-primary-light);
}

/* Esqueletos de carga */
.coin-detail-header.skeleton {
  min-height: 100px;
}

.skeleton-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-surface-light);
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-text-block {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-text {
  height: 1rem;
  width: 150px;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-text.small {
  width: 80px;
  height: 0.75rem;
}

.skeleton-text.medium {
  width: 120px;
}

.skeleton-text.large {
  width: 180px;
  height: 1.5rem;
}

.coin-detail-tabs.skeleton {
  height: 40px;
  display: flex;
  gap: 1rem;
}

.skeleton-tab {
  width: 100px;
  height: 100%;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

.chart-container.skeleton {
  height: 400px;
}

.skeleton-chart {
  width: 100%;
  height: 100%;
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
  animation: pulse 1.5s infinite ease-in-out;
}

.stats-container.skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-stats-row {
  height: 80px;
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* Mensaje de error */
.coin-detail-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  padding: 3rem;
  text-align: center;
}

.coin-detail-error h2 {
  color: var(--text-primary);
  font-size: 1.5rem;
  margin: 0;
}

.coin-detail-error button {
  padding: 0.75rem 1.5rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.coin-detail-error button:hover {
  background-color: var(--color-primary-light);
}

/* Estilos responsivos */
@media (max-width: 992px) {
  .stats-and-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .coin-detail-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .coin-price-info {
    align-items: flex-start;
    width: 100%;
  }

  .chart-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .time-range-buttons, .chart-type-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .guru-modal-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .coin-detail-container {
    padding: 0.5rem;
  }

  .coin-actions {
    flex-direction: column;
    width: 100%;
  }

  .watchlist-button, .guru-button {
    width: 100%;
    justify-content: center;
  }

  .chart-container {
    height: 300px;
  }

  .time-button, .chart-type-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}
