"""
Brave MCP Tool for ADK Agents

This module provides a tool for ADK agents to interact with the Brave MCP server.
"""
import os
from typing import Dict, Any, List, Optional
from .base_mcp_tool import BaseMcpTool

class BraveMcpTool(BaseMcpTool):
    """Tool for interacting with the Brave MCP server."""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the Brave MCP tool.
        
        Args:
            base_url: Base URL of the Brave MCP server (optional)
        """
        super().__init__("brave", base_url)
    
    async def search(self, query: str, count: int = 5, freshness: str = "pm", country: str = "ES") -> List[Dict[str, Any]]:
        """
        Perform a web search.
        
        Args:
            query: Search query
            count: Number of results to return (default: 5)
            freshness: Time filter (pd: day, pw: week, pm: month, py: year)
            country: Country code for the search
            
        Returns:
            Search results
        """
        return await self.execute_tool("brave_search", {
            "query": query,
            "count": count,
            "freshness": freshness,
            "country": country
        })
    
    async def get_news(self, query: str, count: int = 5, freshness: str = "pd", country: str = "ES") -> List[Dict[str, Any]]:
        """
        Search for news.
        
        Args:
            query: Search query
            count: Number of results to return (default: 5)
            freshness: Time filter (pd: day, pw: week, pm: month, py: year)
            country: Country code for the search
            
        Returns:
            News search results
        """
        return await self.execute_tool("getNews", {
            "query": f"{query} cryptocurrency crypto news",
            "count": count,
            "freshness": freshness,
            "country": country,
            "type": "news"
        })
    
    async def get_coin_news(self, symbol: str, count: int = 5, freshness: str = "pd", country: str = "ES") -> List[Dict[str, Any]]:
        """
        Search for news about a specific cryptocurrency.
        
        Args:
            symbol: Symbol of the cryptocurrency
            count: Number of results to return (default: 5)
            freshness: Time filter (pd: day, pw: week, pm: month, py: year)
            country: Country code for the search
            
        Returns:
            News search results
        """
        return await self.execute_tool("getNews", {
            "query": f"{symbol} cryptocurrency price news",
            "count": count,
            "freshness": freshness,
            "country": country,
            "type": "news"
        })
    
    async def search_crypto_topic(self, topic: str, count: int = 5, freshness: str = "pm", country: str = "ES") -> List[Dict[str, Any]]:
        """
        Search for information about a specific cryptocurrency topic.
        
        Args:
            topic: Topic to search for
            count: Number of results to return (default: 5)
            freshness: Time filter (pd: day, pw: week, pm: month, py: year)
            country: Country code for the search
            
        Returns:
            Search results
        """
        return await self.execute_tool("brave_search", {
            "query": f"{topic} cryptocurrency blockchain",
            "count": count,
            "freshness": freshness,
            "country": country
        })
