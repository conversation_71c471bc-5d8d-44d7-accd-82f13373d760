import React, { useEffect, useRef } from 'react';
import anime from '../utils/animeUtils';
import '../styles/ParticlesBackground.css';

interface ParticlesBackgroundProps {
  density?: number; // Densidad de partículas (1-100)
  speed?: number; // Velocidad de animación (1-10)
}

const ParticlesBackground: React.FC<ParticlesBackgroundProps> = ({
  density = 50,
  speed = 3
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<any[]>([]);
  const animationRef = useRef<number | null>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Inicializar la animación
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Ajustar el tamaño del canvas
    const resizeCanvas = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        // Reiniciar partículas cuando cambia el tamaño
        if (resizeTimeoutRef.current) {
          clearTimeout(resizeTimeoutRef.current);
        }

        resizeTimeoutRef.current = setTimeout(() => {
          initParticles();
        }, 200);
      }
    };

    // Inicializar partículas
    const initParticles = () => {
      if (!canvas) return;

      // Calcular número de partículas basado en la densidad y el tamaño de la pantalla
      const maxParticles = 150;
      const baseCount = Math.min(
        Math.floor((canvas.width * canvas.height) / (20000 / density)),
        maxParticles
      );

      particlesRef.current = [];

      // Crear partículas
      for (let i = 0; i < baseCount; i++) {
        // Determinar tipo de partícula (moneda, símbolo, punto)
        const particleType = Math.random() < 0.2 ?
                            (Math.random() < 0.6 ? 'coin' : 'symbol') :
                            'dot';

        // Determinar qué símbolo mostrar (BTC, ETH, etc.)
        let symbol = '';
        if (particleType === 'symbol') {
          const symbols = ['₿', 'Ξ', '₮', 'Ł', '₳', '◎', 'Ð'];
          symbol = symbols[Math.floor(Math.random() * symbols.length)];
        }

        // Crear partícula con propiedades aleatorias
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: particleType === 'dot' ?
                Math.random() * 3 + 1 :
                Math.random() * 10 + 12,
          color: getRandomColor(particleType === 'dot' ?
                               (0.2 + Math.random() * 0.3) :
                               (0.6 + Math.random() * 0.4)),
          vx: (Math.random() - 0.5) * (speed * 0.015),
          vy: (Math.random() - 0.5) * (speed * 0.015),
          type: particleType,
          symbol: symbol,
          rotation: Math.random() * 360,
          rotationSpeed: (Math.random() - 0.5) * 0.5,
          pulseSpeed: 0.5 + Math.random() * 2,
          pulseAmount: 0.1 + Math.random() * 0.3,
          pulseOffset: Math.random() * Math.PI * 2
        });
      }

      // Animar algunas partículas con anime.js para efectos especiales
      particlesRef.current.forEach((particle, index) => {
        if (particle.type !== 'dot' && Math.random() < 0.3) {
          // Animar brillo ocasional
          setTimeout(() => {
            anime({
              targets: particle,
              size: [particle.size, particle.size * 1.5, particle.size],
              opacity: [1, 1.5, 1],
              duration: 2000 + Math.random() * 3000,
              easing: 'easeInOutSine',
              complete: function() {
                // Repetir aleatoriamente
                if (Math.random() < 0.3) {
                  setTimeout(() => {
                    anime({
                      targets: particle,
                      size: [particle.size, particle.size * 1.5, particle.size],
                      opacity: [1, 1.5, 1],
                      duration: 2000 + Math.random() * 3000,
                      easing: 'easeInOutSine'
                    });
                  }, Math.random() * 10000);
                }
              }
            });
          }, Math.random() * 5000);
        }
      });
    };

    // Función para obtener un color aleatorio basado en la paleta de Criptokens
    const getRandomColor = (opacity: number) => {
      const colors = [
        `rgba(0, 224, 255, ${opacity})`,   // Cian
        `rgba(123, 77, 255, ${opacity})`,  // Púrpura
        `rgba(0, 255, 157, ${opacity})`,   // Verde
        `rgba(255, 204, 0, ${opacity})`,   // Amarillo
        `rgba(255, 58, 110, ${opacity})`,  // Rosa
        `rgba(70, 87, 206, ${opacity})`    // Azul
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    };

    // Función de animación principal
    const animate = () => {
      if (!canvas || !ctx) return;

      // Limpiar canvas con un fondo semi-transparente para crear efecto de estela
      ctx.fillStyle = 'rgba(15, 17, 35, 0.15)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Actualizar y dibujar partículas
      for (let i = 0; i < particlesRef.current.length; i++) {
        const p = particlesRef.current[i];

        // Actualizar posición
        p.x += p.vx * speed;
        p.y += p.vy * speed;

        // Actualizar rotación para monedas y símbolos
        if (p.type !== 'dot') {
          p.rotation += p.rotationSpeed;
        }

        // Efecto de pulso para todas las partículas
        const pulseScale = 1 + Math.sin(Date.now() * 0.001 * p.pulseSpeed + p.pulseOffset) * p.pulseAmount;

        // Rebote en los bordes
        if (p.x < 0 || p.x > canvas.width) {
          p.vx *= -1;
          // Pequeño efecto de "rebote"
          if (p.type !== 'dot') {
            p.rotationSpeed = -p.rotationSpeed * 1.2;
          }
        }
        if (p.y < 0 || p.y > canvas.height) {
          p.vy *= -1;
          // Pequeño efecto de "rebote"
          if (p.type !== 'dot') {
            p.rotationSpeed = -p.rotationSpeed * 1.2;
          }
        }

        // Dibujar según el tipo de partícula
        if (p.type === 'dot') {
          // Dibujar punto simple
          ctx.beginPath();
          ctx.arc(p.x, p.y, p.size * pulseScale, 0, Math.PI * 2);
          ctx.fillStyle = p.color;
          ctx.fill();
        }
        else if (p.type === 'coin') {
          // Dibujar moneda
          ctx.save();
          ctx.translate(p.x, p.y);
          ctx.rotate(p.rotation * Math.PI / 180);

          // Círculo exterior
          ctx.beginPath();
          ctx.arc(0, 0, p.size * pulseScale, 0, Math.PI * 2);
          ctx.fillStyle = p.color;
          ctx.fill();

          // Borde
          ctx.beginPath();
          ctx.arc(0, 0, p.size * pulseScale, 0, Math.PI * 2);
          ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
          ctx.lineWidth = 1;
          ctx.stroke();

          // Símbolo $ en el centro
          ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
          ctx.font = `${p.size * 0.8}px Arial`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('$', 0, 0);

          ctx.restore();
        }
        else if (p.type === 'symbol') {
          // Dibujar símbolo de criptomoneda
          ctx.save();
          ctx.translate(p.x, p.y);
          ctx.rotate(p.rotation * Math.PI / 180);

          // Fondo del símbolo
          ctx.beginPath();
          ctx.arc(0, 0, p.size * 0.8 * pulseScale, 0, Math.PI * 2);
          ctx.fillStyle = p.color;
          ctx.fill();

          // Símbolo de criptomoneda
          ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
          ctx.font = `${p.size * 0.8}px Arial`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(p.symbol, 0, 0);

          ctx.restore();
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    // Inicializar todo
    resizeCanvas();
    initParticles();
    animate();

    // Añadir event listeners
    window.addEventListener('resize', resizeCanvas);

    // Limpiar al desmontar
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [density, speed]);

  return (
    <canvas
      ref={canvasRef}
      className="particles-background"
    />
  );
};

export default ParticlesBackground;
