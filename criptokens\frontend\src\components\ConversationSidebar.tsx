import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/NewAuthContext';
import '../styles/ConversationSidebar.css';

interface Conversation {
  id: string;
  title: string;
  createdAt: any; // Timestamp de Firestore
  updatedAt: any; // Timestamp de Firestore
}

interface ConversationSidebarProps {
  onSelectConversation: (conversationId: string) => void;
  onNewConversation: () => void;
  activeConversationId: string | null;
}

const ConversationSidebar: React.FC<ConversationSidebarProps> = ({
  onSelectConversation,
  onNewConversation,
  activeConversationId
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { currentUser } = useAuth();

  // Cargar conversaciones del usuario
  useEffect(() => {
    const fetchConversations = async () => {
      if (!currentUser) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`http://localhost:3001/api/conversations?userId=${currentUser.uid}`);

        if (!response.ok) {
          throw new Error(`Error al cargar conversaciones: ${response.statusText}`);
        }

        const data = await response.json();

        // Verificar que data.conversations sea un array
        if (data && data.conversations && Array.isArray(data.conversations)) {
          setConversations(data.conversations);
        } else if (data && Array.isArray(data)) {
          setConversations(data);
        } else {
          console.error('Formato de respuesta inesperado:', data);
          setConversations([]);
          setError('Formato de respuesta inesperado');
        }
      } catch (err: any) {
        console.error('Error al cargar conversaciones:', err);
        setError(err.message || 'Error al cargar conversaciones');
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversations();
  }, [currentUser]);

  // Formatear fecha para mostrar
  const formatDate = (timestamp: any): string => {
    if (!timestamp) return '';

    // Si es un timestamp de Firestore
    if (timestamp.seconds) {
      const date = new Date(timestamp.seconds * 1000);
      return date.toLocaleDateString();
    }

    // Si es una cadena ISO
    try {
      const date = new Date(timestamp);
      return date.toLocaleDateString();
    } catch (e) {
      return 'Fecha desconocida';
    }
  };

  // Eliminar una conversación
  const handleDeleteConversation = async (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation(); // Evitar que se seleccione la conversación

    if (!currentUser) return;

    if (window.confirm('¿Estás seguro de que deseas eliminar esta conversación?')) {
      try {
        const response = await fetch(`http://localhost:3001/api/conversations/${conversationId}?userId=${currentUser.uid}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error(`Error al eliminar conversación: ${response.statusText}`);
        }

        // Actualizar la lista de conversaciones
        setConversations(prevConversations =>
          prevConversations.filter(conv => conv.id !== conversationId)
        );

        // Si la conversación eliminada era la activa, crear una nueva
        if (conversationId === activeConversationId) {
          onNewConversation();
        }
      } catch (err: any) {
        console.error('Error al eliminar conversación:', err);
        alert(`Error al eliminar conversación: ${err.message}`);
      }
    }
  };

  return (
    <div className="conversation-sidebar">
      <div className="sidebar-header">
        <h2>Conversaciones</h2>
        <button
          className="new-chat-button"
          onClick={onNewConversation}
          title="Nueva conversación"
        >
          <i className="fas fa-plus"></i> Nueva
        </button>
      </div>

      {isLoading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <span>Cargando conversaciones...</span>
        </div>
      ) : error ? (
        <div className="error-container">
          <i className="fas fa-exclamation-circle"></i>
          <span>{error}</span>
          <button onClick={() => window.location.reload()}>Reintentar</button>
        </div>
      ) : conversations.length === 0 ? (
        <div className="empty-state">
          <i className="fas fa-comments"></i>
          <p>No hay conversaciones</p>
          <p>Inicia una nueva conversación con el Gurú Cripto</p>
        </div>
      ) : (
        <ul className="conversation-list">
          {conversations.map(conversation => (
            <li
              key={conversation.id}
              className={`conversation-item ${activeConversationId === conversation.id ? 'active' : ''}`}
              onClick={() => onSelectConversation(conversation.id)}
            >
              <div className="conversation-icon">
                <i className="fas fa-comments"></i>
              </div>
              <div className="conversation-details">
                <div className="conversation-title">{conversation.title}</div>
                <div className="conversation-date">{formatDate(conversation.updatedAt)}</div>
              </div>
              <button
                className="delete-button"
                onClick={(e) => handleDeleteConversation(e, conversation.id)}
                title="Eliminar conversación"
              >
                <i className="fas fa-trash"></i>
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ConversationSidebar;
