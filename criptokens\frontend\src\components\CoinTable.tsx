import React, { useState } from 'react';
import { useWatchlist } from '../hooks/useWatchlist';
import '../styles/CoinTable.css';

interface CoinTableProps {
  cryptos: any[];
  isLoading: boolean;
  timeRange: string;
  onSelectCrypto: (id: string) => void;
}

const CoinTable: React.FC<CoinTableProps> = ({
  cryptos,
  isLoading,
  timeRange,
  onSelectCrypto
}) => {
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useWatchlist();
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'ascending' | 'descending';
  }>({
    key: 'market_cap',
    direction: 'descending'
  });

  // Función para formatear números con separadores de miles
  const formatNumber = (num: number, maximumFractionDigits: number = 2): string => {
    return new Intl.NumberFormat('es-ES', {
      minimumFractionDigits: 2,
      maximumFractionDigits
    }).format(num);
  };

  // Función para formatear la capitalización de mercado
  const formatMarketCap = (marketCap: number): string => {
    if (marketCap >= 1_000_000_000_000) {
      return `$${(marketCap / 1_000_000_000_000).toFixed(2)}T`;
    } else if (marketCap >= 1_000_000_000) {
      return `$${(marketCap / 1_000_000_000).toFixed(2)}B`;
    } else if (marketCap >= 1_000_000) {
      return `$${(marketCap / 1_000_000).toFixed(2)}M`;
    } else {
      return `$${formatNumber(marketCap)}`;
    }
  };

  // Función para manejar el cambio de ordenación
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Función para obtener la clase de la columna de ordenación
  const getSortDirectionClass = (key: string) => {
    if (sortConfig.key !== key) return '';
    return sortConfig.direction === 'ascending' ? 'ascending' : 'descending';
  };

  // Función para obtener el campo de cambio de precio según el rango de tiempo
  const getPriceChangeField = (timeRange: string) => {
    switch (timeRange) {
      case '1h':
        return 'price_change_percentage_1h_in_currency';
      case '7d':
        return 'price_change_percentage_7d_in_currency';
      case '30d':
        return 'price_change_percentage_30d_in_currency';
      case '24h':
      default:
        return 'price_change_percentage_24h';
    }
  };

  // Función para ordenar los datos
  const sortedData = React.useMemo(() => {
    if (!cryptos || cryptos.length === 0) return [];

    const sortableItems = [...cryptos];
    const priceChangeField = getPriceChangeField(timeRange);

    sortableItems.sort((a, b) => {
      // Manejar ordenación por cambio de precio según el rango de tiempo
      if (sortConfig.key === 'price_change') {
        const aValue = a[priceChangeField] || 0;
        const bValue = b[priceChangeField] || 0;

        if (aValue < bValue) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      }

      // Ordenación normal para otros campos
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? 1 : -1;
      }
      return 0;
    });

    return sortableItems;
  }, [cryptos, sortConfig, timeRange]);

  // Renderizar un esqueleto de carga
  const renderSkeleton = () => {
    return Array(10).fill(0).map((_, index) => (
      <tr key={`skeleton-${index}`} className="skeleton-row">
        <td><div className="skeleton-cell rank"></div></td>
        <td><div className="skeleton-cell name"></div></td>
        <td><div className="skeleton-cell price"></div></td>
        <td><div className="skeleton-cell change"></div></td>
        <td><div className="skeleton-cell market-cap"></div></td>
        <td><div className="skeleton-cell volume"></div></td>
        <td><div className="skeleton-cell sparkline"></div></td>
        <td><div className="skeleton-cell actions"></div></td>
      </tr>
    ));
  };

  // Función para alternar una criptomoneda en la lista de seguimiento
  const toggleWatchlist = (e: React.MouseEvent, cryptoId: string) => {
    e.stopPropagation(); // Evitar que se propague al tr y active la selección

    if (isInWatchlist(cryptoId)) {
      removeFromWatchlist(cryptoId);
    } else {
      addToWatchlist(cryptoId);
    }
  };

  // Función para renderizar el mini gráfico (sparkline)
  const renderSparkline = (sparklineData: number[] | undefined) => {
    if (!sparklineData || sparklineData.length === 0) {
      return <div className="no-sparkline">No hay datos</div>;
    }

    const min = Math.min(...sparklineData);
    const max = Math.max(...sparklineData);
    const range = max - min;

    // Determinar si la tendencia es positiva o negativa
    const isPositive = sparklineData[sparklineData.length - 1] >= sparklineData[0];

    return (
      <svg className="sparkline" width="100" height="30" viewBox="0 0 100 30">
        <path
          d={sparklineData.map((value, index) => {
            const x = (index / (sparklineData.length - 1)) * 100;
            const y = 30 - ((value - min) / range) * 30;
            return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
          }).join(' ')}
          fill="none"
          stroke={isPositive ? 'var(--color-positive)' : 'var(--color-negative)'}
          strokeWidth="1.5"
        />
      </svg>
    );
  };

  return (
    <div className="coin-table-container">
      <div className="table-wrapper">
        <table className="coin-table">
          <thead>
            <tr>
              <th
                onClick={() => requestSort('market_cap_rank')}
                className={getSortDirectionClass('market_cap_rank')}
              >
                #
              </th>
              <th
                onClick={() => requestSort('name')}
                className={getSortDirectionClass('name')}
              >
                Nombre
              </th>
              <th
                onClick={() => requestSort('current_price')}
                className={getSortDirectionClass('current_price')}
              >
                Precio
              </th>
              <th
                onClick={() => requestSort('price_change')}
                className={getSortDirectionClass('price_change')}
              >
                {timeRange} %
              </th>
              <th
                onClick={() => requestSort('market_cap')}
                className={getSortDirectionClass('market_cap')}
              >
                Cap. Mercado
              </th>
              <th
                onClick={() => requestSort('total_volume')}
                className={getSortDirectionClass('total_volume')}
              >
                Volumen (24h)
              </th>
              <th>
                Últimos 7 días
              </th>
              <th>
                Acciones
              </th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              renderSkeleton()
            ) : (
              sortedData.map((crypto) => {
                const priceChangeField = getPriceChangeField(timeRange);
                const priceChange = crypto[priceChangeField] || 0;

                return (
                  <tr
                    key={crypto.id}
                    onClick={() => onSelectCrypto(crypto.id)}
                    className="coin-row"
                  >
                    <td className="rank-cell">{crypto.market_cap_rank || '-'}</td>
                    <td className="name-cell">
                      <div className="coin-info">
                        <img
                          src={crypto.image}
                          alt={crypto.name}
                          className="coin-icon"
                          style={{
                            width: '16px',
                            height: '16px',
                            maxWidth: '16px',
                            maxHeight: '16px',
                            borderRadius: '50%',
                            verticalAlign: 'middle',
                            objectFit: 'contain'
                          }}
                        />
                        <div className="coin-name-container">
                          <span className="coin-name">{crypto.name}</span>
                          <span className="coin-symbol">{crypto.symbol.toUpperCase()}</span>
                        </div>
                      </div>
                    </td>
                    <td className="price-cell">${formatNumber(crypto.current_price)}</td>
                    <td className={`change-cell ${priceChange >= 0 ? 'positive' : 'negative'}`}>
                      {priceChange?.toFixed(2)}%
                    </td>
                    <td className="market-cap-cell">{formatMarketCap(crypto.market_cap)}</td>
                    <td className="volume-cell">${formatMarketCap(crypto.total_volume)}</td>
                    <td className="sparkline-cell">
                      {renderSparkline(crypto.sparkline_in_7d?.price)}
                    </td>
                    <td className="actions-cell">
                      <button
                        className={`watchlist-button ${isInWatchlist(crypto.id) ? 'active' : ''}`}
                        onClick={(e) => toggleWatchlist(e, crypto.id)}
                        title={isInWatchlist(crypto.id) ? "Quitar de Watchlist" : "Añadir a Watchlist"}
                      >
                        <i className={`fas ${isInWatchlist(crypto.id) ? 'fa-star' : 'fa-star'}`}></i>
                      </button>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CoinTable;
