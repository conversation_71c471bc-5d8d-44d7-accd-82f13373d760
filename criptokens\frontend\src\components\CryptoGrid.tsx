import React, { useState, useEffect } from 'react';
import CryptoCard from './CryptoCard';
import '../styles/CryptoGrid.css';

interface CryptoGridProps {
  cryptos: any[];
  onSelectCrypto: (id: string) => void;
}

const CryptoGrid: React.FC<CryptoGridProps> = ({ cryptos, onSelectCrypto }) => {
  const [visibleCryptos, setVisibleCryptos] = useState<any[]>([]);
  const [filter, setFilter] = useState('');
  const [sortBy, setSortBy] = useState('market_cap');
  const [sortDirection, setSortDirection] = useState('desc');

  // Aplicar filtros y ordenación
  useEffect(() => {
    let filtered = [...cryptos];
    
    // Filtrar por nombre o símbolo
    if (filter) {
      const searchTerm = filter.toLowerCase();
      filtered = filtered.filter(crypto => 
        crypto.name.toLowerCase().includes(searchTerm) || 
        crypto.symbol.toLowerCase().includes(searchTerm)
      );
    }
    
    // Ordenar
    filtered.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortBy) {
        case 'name':
          valueA = a.name.toLowerCase();
          valueB = b.name.toLowerCase();
          return sortDirection === 'asc' 
            ? valueA.localeCompare(valueB) 
            : valueB.localeCompare(valueA);
        
        case 'price':
          valueA = a.current_price;
          valueB = b.current_price;
          break;
          
        case 'change':
          valueA = a.price_change_percentage_24h;
          valueB = b.price_change_percentage_24h;
          break;
          
        case 'market_cap':
        default:
          valueA = a.market_cap;
          valueB = b.market_cap;
          break;
      }
      
      return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
    });
    
    setVisibleCryptos(filtered);
  }, [cryptos, filter, sortBy, sortDirection]);

  // Manejar cambio de ordenación
  const handleSortChange = (field: string) => {
    if (sortBy === field) {
      // Si ya estamos ordenando por este campo, cambiar la dirección
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Si es un nuevo campo, establecer como predeterminado descendente
      setSortBy(field);
      setSortDirection('desc');
    }
  };

  return (
    <div className="crypto-grid-container">
      <div className="crypto-grid-controls">
        <div className="search-container">
          <input
            type="text"
            placeholder="Buscar criptomoneda..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="search-input"
          />
          <span className="search-icon">🔍</span>
        </div>
        
        <div className="sort-controls">
          <span className="sort-label">Ordenar por:</span>
          <div className="sort-buttons">
            <button 
              className={`sort-button ${sortBy === 'market_cap' ? 'active' : ''}`}
              onClick={() => handleSortChange('market_cap')}
            >
              Cap. Mercado {sortBy === 'market_cap' && (sortDirection === 'asc' ? '↑' : '↓')}
            </button>
            <button 
              className={`sort-button ${sortBy === 'price' ? 'active' : ''}`}
              onClick={() => handleSortChange('price')}
            >
              Precio {sortBy === 'price' && (sortDirection === 'asc' ? '↑' : '↓')}
            </button>
            <button 
              className={`sort-button ${sortBy === 'change' ? 'active' : ''}`}
              onClick={() => handleSortChange('change')}
            >
              Cambio 24h {sortBy === 'change' && (sortDirection === 'asc' ? '↑' : '↓')}
            </button>
            <button 
              className={`sort-button ${sortBy === 'name' ? 'active' : ''}`}
              onClick={() => handleSortChange('name')}
            >
              Nombre {sortBy === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
            </button>
          </div>
        </div>
      </div>
      
      {visibleCryptos.length === 0 ? (
        <div className="no-results">
          <div className="no-results-icon">🔍</div>
          <h3>No se encontraron resultados</h3>
          <p>Intenta con otro término de búsqueda</p>
        </div>
      ) : (
        <div className="crypto-grid">
          {visibleCryptos.map((crypto, index) => (
            <div 
              className="crypto-grid-item" 
              key={crypto.id}
              style={{ '--index': index } as React.CSSProperties}
            >
              <CryptoCard 
                crypto={crypto} 
                onClick={() => onSelectCrypto(crypto.id)}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CryptoGrid;
