"""
On-Chain Analysis Agent for Criptokens
"""
import os
import asyncio
from google.adk.agents.llm_agent import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai.types import Content, Part

# Set the Google API key
os.environ["GOOGLE_API_KEY"] = "AIzaSyCashyNX3fNv-bj5xOCtTDMfL_-vGm9Yag"

# Create the On-Chain Analysis agent
onchain_analysis_agent = LlmAgent(
    name="onchain_analysis_agent",
    model="gemini-1.5-pro",
    description="On-Chain Analysis Agent is a blockchain and on-chain data analysis expert.",
    instruction="""
    You are a blockchain and on-chain data analysis expert. Your task is to:
    
    1. Analyze on-chain data for the cryptocurrency mentioned in the query
    2. Explain what the whale activity indicates about large holder behavior
    3. Interpret the gas prices and their implications for network activity
    4. Identify significant patterns in token transfers
    5. Provide a clear, concise on-chain analysis summary
    
    When responding:
    - Be specific about what the on-chain metrics mean
    - Explain the significance of whale accumulation or distribution
    - Highlight any unusual patterns in the data
    - Provide a conclusion about what the on-chain data suggests for the token
    
    Remember that you are an educational resource, not a financial advisor. Always remind users to do their own research and not to make investment decisions solely based on your analysis.
    """
)

# Create a session service
session_service = InMemorySessionService()

# Create a runner
onchain_analysis_runner = Runner(
    agent=onchain_analysis_agent,
    app_name="criptokens",
    session_service=session_service
)

async def run_onchain_analysis(query, user_id="user"):
    """
    Run the On-Chain Analysis agent with a query
    
    Args:
        query (str): The query to send to the agent
        user_id (str): The user ID for the session
        
    Returns:
        str: The response from the agent
    """
    # Create a session
    session = session_service.create_session(
        user_id=user_id,
        app_name="criptokens"
    )
    
    # Create a message
    message = Content(
        role="user",
        parts=[Part(text=query)]
    )
    
    # Run the agent with the session
    response_text = ""
    async for event in onchain_analysis_runner.run_async(
        user_id=user_id,
        session_id=session.id,
        new_message=message
    ):
        if hasattr(event, 'content') and event.content and event.content.parts:
            for part in event.content.parts:
                if hasattr(part, 'text') and part.text:
                    response_text += part.text
    
    return response_text

# Example usage
if __name__ == "__main__":
    query = "What's the on-chain analysis for Ethereum right now?"
    response = asyncio.run(run_onchain_analysis(query))
    print(f"Query: {query}")
    print(f"Response: {response}")
