# Script para probar start_all.js y capturar la salida en un archivo de log

# Detener procesos existentes
Write-Host "Deteniendo procesos existentes..." -ForegroundColor Cyan
node stop_all.js

# Verificar puertos en uso
Write-Host "`nVerificando puertos en uso..." -ForegroundColor Cyan
Get-NetTCPConnection -LocalPort 3101,3102,3103,3104,3001,7777,8000,5173 -ErrorAction SilentlyContinue | 
  Format-Table LocalPort,State,OwningProcess,@{Name="ProcessName";Expression={(Get-Process -Id $_.OwningProcess -ErrorAction SilentlyContinue).ProcessName}}

# Ejecutar start_all.js y capturar la salida en un archivo de log
Write-Host "`nEjecutando start_all.js..." -ForegroundColor Cyan
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = "start_all_$timestamp.log"

Write-Host "La salida se guardará en $logFile" -ForegroundColor Yellow
Write-Host "Presiona Ctrl+C para detener la ejecución" -ForegroundColor Yellow

# Ejecutar start_all.js y capturar la salida en un archivo de log
node start_all.js *> $logFile

# Verificar puertos en uso después de la ejecución
Write-Host "`nVerificando puertos en uso después de la ejecución..." -ForegroundColor Cyan
Get-NetTCPConnection -LocalPort 3101,3102,3103,3104,3001,7777,8000,5173 -ErrorAction SilentlyContinue | 
  Format-Table LocalPort,State,OwningProcess,@{Name="ProcessName";Expression={(Get-Process -Id $_.OwningProcess -ErrorAction SilentlyContinue).ProcessName}}

# Mostrar los procesos de Node.js y Python en ejecución
Write-Host "`nProcesos de Node.js y Python en ejecución:" -ForegroundColor Cyan
Get-Process -Name node,python -ErrorAction SilentlyContinue | Format-Table Id,ProcessName,Path,StartTime

# Mostrar las últimas 20 líneas del archivo de log
Write-Host "`nÚltimas 20 líneas del archivo de log:" -ForegroundColor Cyan
Get-Content -Tail 20 $logFile

Write-Host "`nPrueba completada. Presiona cualquier tecla para salir." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
