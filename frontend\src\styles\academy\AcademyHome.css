.academy-home {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  color: var(--text-primary);
}

/* Hero Section */
.academy-hero {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.hero-content {
  flex: 1;
  z-index: 1;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: var(--text-secondary);
  max-width: 600px;
}

.hero-actions {
  display: flex;
  gap: 1rem;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-surface);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-primary);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  max-height: 300px;
}

/* Stats Section */
.academy-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2rem;
  color: var(--color-primary);
  margin-right: 1rem;
}

.stat-content h3 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--text-primary);
}

.stat-content p {
  margin: 0;
  color: var(--text-secondary);
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  font-size: 1.8rem;
  margin: 0;
  color: var(--text-primary);
}

.view-all {
  color: var(--color-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: color 0.2s ease;
}

.view-all:hover {
  color: var(--color-primary-dark);
}

/* Featured Courses */
.featured-courses {
  margin-bottom: 3rem;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.course-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.course-image {
  height: 180px;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
  position: relative;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background-image: linear-gradient(135deg, #7b61ff, #2b5876);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.course-level {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.course-level.beginner {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.course-level.intermediate {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.course-level.advanced {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.course-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.course-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.course-description {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  flex-grow: 1;
}

.course-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.course-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-instructor {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.course-instructor img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.view-course-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  align-self: flex-start;
}

.view-course-button:hover {
  background-color: var(--color-primary-dark);
}

/* Course Categories */
.course-categories {
  margin-bottom: 3rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border-top: 4px solid;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem auto 1rem;
  color: white;
  font-size: 1.5rem;
}

.category-content {
  padding: 0 1.5rem 1.5rem;
  text-align: center;
}

.category-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.category-content p {
  margin: 0 0 1.5rem 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

.category-link {
  color: var(--color-primary);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: color 0.2s ease;
}

.category-link:hover {
  color: var(--color-primary-dark);
}

/* Learning Path */
.learning-path {
  margin-bottom: 3rem;
}

.path-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.path-step {
  display: flex;
  background-color: var(--color-surface);
  border-radius: 12px;
  padding: 1.5rem;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
}

.path-step:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 1.5rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.step-content p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
}

.step-link {
  color: var(--color-primary);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: color 0.2s ease;
}

.step-link:hover {
  color: var(--color-primary-dark);
}

.path-connector {
  width: 2px;
  height: 30px;
  background-color: var(--color-primary);
  margin-left: 20px;
  position: relative;
  z-index: 0;
}

/* CTA Section */
.academy-cta {
  background-color: var(--color-surface);
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
  margin-bottom: 3rem;
  background-image: linear-gradient(135deg, rgba(123, 97, 255, 0.1), rgba(43, 88, 118, 0.1));
}

.cta-content h2 {
  font-size: 2rem;
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.cta-content p {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  color: var(--text-secondary);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive */
@media (max-width: 768px) {
  .academy-hero {
    flex-direction: column;
    text-align: center;
  }
  
  .hero-content {
    margin-bottom: 2rem;
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .academy-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .academy-cta {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .academy-stats {
    grid-template-columns: 1fr;
  }
  
  .hero-actions {
    flex-direction: column;
  }
}
