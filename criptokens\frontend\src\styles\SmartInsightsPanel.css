.smart-insights-panel {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.ai-badge {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.insights-list {
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.insight-card {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--border-radius-md);
  background-color: var(--color-surface-light);
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.insight-card.success {
  border-left-color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.05);
}

.insight-card.warning {
  border-left-color: #f39c12;
  background-color: rgba(243, 156, 18, 0.05);
}

.insight-card.info {
  border-left-color: #3498db;
  background-color: rgba(52, 152, 219, 0.05);
}

.insight-card.opportunity {
  border-left-color: #9b59b6;
  background-color: rgba(155, 89, 182, 0.05);
}

.insight-card.caution {
  border-left-color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.05);
}

.insight-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  flex-shrink: 0;
  font-size: 1rem;
}

.insight-card.success .insight-icon {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
}

.insight-card.warning .insight-icon {
  color: #f39c12;
  background-color: rgba(243, 156, 18, 0.1);
}

.insight-card.info .insight-icon {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.insight-card.opportunity .insight-icon {
  color: #9b59b6;
  background-color: rgba(155, 89, 182, 0.1);
}

.insight-card.caution .insight-icon {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.insight-content p {
  margin: 0 0 0.75rem 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.insight-action-button {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: 0.8rem;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.insight-action-button:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

.empty-insights {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-tertiary);
}

.empty-insights i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-insights p {
  margin: 0;
  font-size: 0.9rem;
  max-width: 80%;
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .insights-list {
    max-height: 300px;
  }
}

@media (max-width: 576px) {
  .panel-header {
    padding: 0.75rem;
  }
  
  .insights-list {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .insight-card {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .insight-icon {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.8rem;
  }
  
  .insight-content h4 {
    font-size: 0.8rem;
  }
  
  .insight-content p {
    font-size: 0.75rem;
  }
}
