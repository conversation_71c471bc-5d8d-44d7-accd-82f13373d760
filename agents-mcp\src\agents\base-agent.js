/**
 * Agente base para el sistema de agentes MCP
 * 
 * Esta clase proporciona la funcionalidad común para todos los agentes.
 */

const { OpenAI } = require('openai');
const { createMcpAdapter } = require('../adapters');
const logger = require('../utils/logger');
const { AgentError, ValidationError } = require('../utils/error-handler');
const { generateId, sanitizeForLogging } = require('../utils/helpers');
const config = require('../config/config');

class BaseAgent {
  /**
   * @param {string} agentId - Identificador del agente
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(agentId, options = {}) {
    this.agentId = agentId;
    this.agentConfig = config.agents[agentId] || {};
    this.name = options.name || this.agentConfig.name || agentId;
    this.description = options.description || this.agentConfig.description || '';
    this.model = options.model || this.agentConfig.model || config.system.defaultModel;
    this.systemPrompt = options.systemPrompt || this.agentConfig.systemPrompt || '';
    this.allowedMcpServers = options.allowedMcpServers || this.agentConfig.allowedMcpServers || [];
    this.mcpAdapters = {};
    
    // Verificar que el agente esté configurado
    if (!this.agentConfig) {
      throw new ValidationError(`No se ha encontrado configuración para el agente ${agentId}`);
    }
    
    // Inicializar el cliente de OpenAI
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || process.env.OPENROUTER_API_KEY,
      baseURL: process.env.OPENAI_API_BASE || process.env.OPENROUTER_API_BASE || 'https://api.openai.com/v1',
      defaultHeaders: {
        'HTTP-Referer': 'https://criptokens.app',
        'X-Title': `Criptokens - ${this.name}`
      }
    });
    
    logger.debug('BaseAgent', `Inicializado agente ${this.name} (${this.agentId})`, {
      model: this.model,
      allowedMcpServers: this.allowedMcpServers
    });
  }
  
  /**
   * Inicializa los adaptadores MCP para los servidores permitidos
   * @returns {Promise<void>}
   */
  async initializeMcpAdapters() {
    logger.debug('BaseAgent', `Inicializando adaptadores MCP para el agente ${this.name}`);
    
    for (const serverName of this.allowedMcpServers) {
      try {
        this.mcpAdapters[serverName] = createMcpAdapter(serverName);
        await this.mcpAdapters[serverName].createSession();
        
        logger.debug('BaseAgent', `Adaptador MCP para ${serverName} inicializado correctamente`);
      } catch (error) {
        logger.warn('BaseAgent', `Error al inicializar adaptador MCP para ${serverName}`, {
          error: error.message
        });
      }
    }
  }
  
  /**
   * Verifica si el agente tiene acceso a un servidor MCP
   * @param {string} serverName - Nombre del servidor MCP
   * @returns {boolean} true si tiene acceso, false en caso contrario
   */
  hasAccessToMcpServer(serverName) {
    return this.allowedMcpServers.includes(serverName);
  }
  
  /**
   * Obtiene un adaptador MCP
   * @param {string} serverName - Nombre del servidor MCP
   * @returns {Object} Adaptador MCP
   */
  getMcpAdapter(serverName) {
    // Verificar que el agente tenga acceso al servidor MCP
    if (!this.hasAccessToMcpServer(serverName)) {
      throw new ValidationError(
        `El agente ${this.name} no tiene acceso al servidor MCP ${serverName}`,
        { allowedServers: this.allowedMcpServers }
      );
    }
    
    // Verificar que el adaptador esté inicializado
    if (!this.mcpAdapters[serverName]) {
      throw new ValidationError(
        `El adaptador MCP para ${serverName} no está inicializado`
      );
    }
    
    return this.mcpAdapters[serverName];
  }
  
  /**
   * Ejecuta una tarea
   * @param {Object} task - Tarea a ejecutar
   * @returns {Promise<Object>} Resultado de la tarea
   */
  async executeTask(task) {
    logger.info('BaseAgent', `Ejecutando tarea ${task.id} (${task.type}) con el agente ${this.name}`);
    
    try {
      // Verificar que la tarea sea válida
      this._validateTask(task);
      
      // Actualizar el estado de la tarea
      task.status = 'in-progress';
      task.startedAt = new Date();
      
      // Ejecutar la tarea según su tipo
      let result;
      
      switch (task.type) {
        case 'GENERATE_TEXT':
          result = await this._generateText(task.parameters);
          break;
        case 'ANALYZE_TEXT':
          result = await this._analyzeText(task.parameters);
          break;
        case 'EXECUTE_MCP_TOOL':
          result = await this._executeMcpTool(task.parameters);
          break;
        default:
          // Las clases derivadas deben implementar sus propios tipos de tareas
          result = await this._executeSpecializedTask(task);
      }
      
      // Actualizar el estado de la tarea
      task.status = 'completed';
      task.completedAt = new Date();
      task.result = result;
      
      logger.info('BaseAgent', `Tarea ${task.id} completada correctamente`);
      
      return result;
    } catch (error) {
      // Actualizar el estado de la tarea
      task.status = 'failed';
      task.completedAt = new Date();
      task.error = {
        message: error.message,
        code: error.code,
        details: error.details
      };
      
      logger.error('BaseAgent', `Error al ejecutar tarea ${task.id}`, {
        error: error.message,
        taskType: task.type
      });
      
      throw new AgentError(
        `Error al ejecutar tarea ${task.id} (${task.type}) con el agente ${this.name}: ${error.message}`,
        { taskId: task.id, taskType: task.type },
        error
      );
    }
  }
  
  /**
   * Valida una tarea
   * @param {Object} task - Tarea a validar
   * @private
   */
  _validateTask(task) {
    // Verificar que la tarea tenga los campos requeridos
    if (!task.id) {
      throw new ValidationError('La tarea no tiene ID');
    }
    
    if (!task.type) {
      throw new ValidationError('La tarea no tiene tipo');
    }
    
    if (!task.parameters) {
      throw new ValidationError('La tarea no tiene parámetros');
    }
  }
  
  /**
   * Genera texto utilizando el modelo de lenguaje
   * @param {Object} parameters - Parámetros para la generación de texto
   * @returns {Promise<Object>} Texto generado
   * @private
   */
  async _generateText(parameters) {
    try {
      logger.debug('BaseAgent', `Generando texto con el modelo ${this.model}`, 
        sanitizeForLogging(parameters));
      
      const { prompt, systemPrompt, temperature, maxTokens } = parameters;
      
      // Crear los mensajes para la API
      const messages = [
        { role: 'system', content: systemPrompt || this.systemPrompt },
        { role: 'user', content: prompt }
      ];
      
      // Crear el payload para la solicitud
      const payload = {
        model: this.model,
        messages: messages,
        temperature: temperature || 0.7,
        max_tokens: maxTokens || 1000
      };
      
      // Realizar la llamada a la API
      const response = await this.openai.chat.completions.create(payload);
      
      // Extraer la respuesta
      const reply = response.choices[0].message.content;
      
      return {
        text: reply,
        model: this.model,
        usage: response.usage
      };
    } catch (error) {
      throw new AgentError(
        `Error al generar texto: ${error.message}`,
        { parameters: sanitizeForLogging(parameters) },
        error
      );
    }
  }
  
  /**
   * Analiza texto utilizando el modelo de lenguaje
   * @param {Object} parameters - Parámetros para el análisis de texto
   * @returns {Promise<Object>} Análisis del texto
   * @private
   */
  async _analyzeText(parameters) {
    try {
      logger.debug('BaseAgent', `Analizando texto con el modelo ${this.model}`, 
        sanitizeForLogging(parameters));
      
      const { text, instructions, format } = parameters;
      
      // Crear los mensajes para la API
      const messages = [
        { role: 'system', content: `${this.systemPrompt}\n\nInstrucciones: ${instructions}\n\nFormato de salida: ${format || 'JSON'}` },
        { role: 'user', content: text }
      ];
      
      // Crear el payload para la solicitud
      const payload = {
        model: this.model,
        messages: messages,
        temperature: 0.3, // Temperatura baja para análisis más determinista
        max_tokens: 1000,
        response_format: format === 'JSON' ? { type: 'json_object' } : undefined
      };
      
      // Realizar la llamada a la API
      const response = await this.openai.chat.completions.create(payload);
      
      // Extraer la respuesta
      const reply = response.choices[0].message.content;
      
      // Si el formato es JSON, intentar parsearlo
      let parsedReply = reply;
      if (format === 'JSON') {
        try {
          parsedReply = JSON.parse(reply);
        } catch (parseError) {
          logger.warn('BaseAgent', `Error al parsear respuesta JSON: ${parseError.message}`);
        }
      }
      
      return {
        analysis: parsedReply,
        model: this.model,
        usage: response.usage
      };
    } catch (error) {
      throw new AgentError(
        `Error al analizar texto: ${error.message}`,
        { parameters: sanitizeForLogging(parameters) },
        error
      );
    }
  }
  
  /**
   * Ejecuta una herramienta MCP
   * @param {Object} parameters - Parámetros para la ejecución de la herramienta
   * @returns {Promise<Object>} Resultado de la ejecución
   * @private
   */
  async _executeMcpTool(parameters) {
    try {
      const { serverName, toolName, input } = parameters;
      
      logger.debug('BaseAgent', `Ejecutando herramienta MCP ${toolName} en servidor ${serverName}`, 
        sanitizeForLogging(input));
      
      // Obtener el adaptador MCP
      const adapter = this.getMcpAdapter(serverName);
      
      // Ejecutar la herramienta
      const result = await adapter.executeTool(toolName, input);
      
      return {
        serverName,
        toolName,
        result
      };
    } catch (error) {
      throw new AgentError(
        `Error al ejecutar herramienta MCP: ${error.message}`,
        { parameters: sanitizeForLogging(parameters) },
        error
      );
    }
  }
  
  /**
   * Ejecuta una tarea especializada (debe ser implementada por las clases derivadas)
   * @param {Object} task - Tarea a ejecutar
   * @returns {Promise<Object>} Resultado de la tarea
   * @private
   */
  async _executeSpecializedTask(task) {
    throw new AgentError(
      `El tipo de tarea ${task.type} no está implementado por el agente base`,
      { taskId: task.id, taskType: task.type }
    );
  }
  
  /**
   * Cierra todas las sesiones MCP
   * @returns {Promise<void>}
   */
  async close() {
    logger.debug('BaseAgent', `Cerrando sesiones MCP para el agente ${this.name}`);
    
    for (const serverName in this.mcpAdapters) {
      try {
        await this.mcpAdapters[serverName].closeSession();
        logger.debug('BaseAgent', `Sesión MCP para ${serverName} cerrada correctamente`);
      } catch (error) {
        logger.warn('BaseAgent', `Error al cerrar sesión MCP para ${serverName}`, {
          error: error.message
        });
      }
    }
  }
}

module.exports = BaseAgent;
