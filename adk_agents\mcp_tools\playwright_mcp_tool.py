"""
Playwright MCP Tool for ADK Agents

This module provides a tool for ADK agents to interact with the Playwright MCP server.
"""
import os
from typing import Dict, Any, Optional
from .base_mcp_tool import BaseMcpTool

class PlaywrightMcpTool(BaseMcpTool):
    """Tool for interacting with the Playwright MCP server."""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the Playwright MCP tool.
        
        Args:
            base_url: Base URL of the Playwright MCP server (optional)
        """
        super().__init__("playwright", base_url)
        self.browser_session = None
    
    async def browse_web_page(self, url: str, take_screenshot: bool = True, full_page: bool = False) -> Dict[str, Any]:
        """
        Navigate to a URL and analyze its content.
        
        Args:
            url: URL to visit
            take_screenshot: Whether to take a screenshot
            full_page: Whether the screenshot should be of the full page
            
        Returns:
            Page information
        """
        result = await self.execute_tool("browseWebPage", {
            "url": url,
            "takeScreenshot": take_screenshot,
            "fullPage": full_page
        })
        
        # Save the browser session ID if available
        if result and "sessionId" in result:
            self.browser_session = result["sessionId"]
        
        return result
    
    async def take_screenshot(self, full_page: bool = False) -> Dict[str, Any]:
        """
        Take a screenshot of the current page.
        
        Args:
            full_page: Whether the screenshot should be of the full page
            
        Returns:
            Screenshot information
        """
        if not self.browser_session:
            raise Exception("No active browser session. Call browse_web_page() first.")
        
        return await self.execute_tool("takeScreenshot", {
            "sessionId": self.browser_session,
            "fullPage": full_page
        })
    
    async def extract_content(self) -> Dict[str, Any]:
        """
        Extract content from the current page.
        
        Returns:
            Page content
        """
        if not self.browser_session:
            raise Exception("No active browser session. Call browse_web_page() first.")
        
        return await self.execute_tool("extractContent", {
            "sessionId": self.browser_session
        })
    
    async def click_element(self, selector: str) -> Dict[str, Any]:
        """
        Click an element on the page.
        
        Args:
            selector: CSS selector of the element
            
        Returns:
            Operation result
        """
        if not self.browser_session:
            raise Exception("No active browser session. Call browse_web_page() first.")
        
        return await self.execute_tool("clickElement", {
            "sessionId": self.browser_session,
            "selector": selector
        })
    
    async def type_text(self, selector: str, text: str) -> Dict[str, Any]:
        """
        Type text into an element on the page.
        
        Args:
            selector: CSS selector of the element
            text: Text to type
            
        Returns:
            Operation result
        """
        if not self.browser_session:
            raise Exception("No active browser session. Call browse_web_page() first.")
        
        return await self.execute_tool("typeText", {
            "sessionId": self.browser_session,
            "selector": selector,
            "text": text
        })
    
    async def go_back(self) -> Dict[str, Any]:
        """
        Navigate back in the browser history.
        
        Returns:
            Page information
        """
        if not self.browser_session:
            raise Exception("No active browser session. Call browse_web_page() first.")
        
        return await self.execute_tool("goBack", {
            "sessionId": self.browser_session
        })
    
    async def go_forward(self) -> Dict[str, Any]:
        """
        Navigate forward in the browser history.
        
        Returns:
            Page information
        """
        if not self.browser_session:
            raise Exception("No active browser session. Call browse_web_page() first.")
        
        return await self.execute_tool("goForward", {
            "sessionId": self.browser_session
        })
    
    async def close_browser(self) -> None:
        """Close the browser session."""
        if not self.browser_session:
            return
        
        try:
            await self.execute_tool("closeBrowser", {
                "sessionId": self.browser_session
            })
        except Exception as e:
            print(f"Error closing browser: {e}")
        finally:
            self.browser_session = None
    
    async def close(self) -> None:
        """Close the browser session and the MCP session."""
        await self.close_browser()
        await self.close_session()
