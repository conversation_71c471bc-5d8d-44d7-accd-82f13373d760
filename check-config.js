/**
 * Script para verificar la configuración de Criptokens
 * 
 * Este script verifica que todos los componentes estén correctamente configurados
 * y que los servidores estén accesibles.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const config = require('./config');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para verificar si un directorio existe
function checkDirectory(dir, name) {
  const fullPath = path.join(__dirname, dir);
  if (!fs.existsSync(fullPath)) {
    log(`❌ El directorio ${name} no existe: ${fullPath}`, colors.red);
    return false;
  }
  log(`✅ El directorio ${name} existe: ${fullPath}`, colors.green);
  return true;
}

// Función para verificar si un servidor está accesible
async function checkServer(url, name) {
  try {
    const response = await axios.get(url, { timeout: 5000 });
    log(`✅ El servidor ${name} está accesible: ${url}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ El servidor ${name} no está accesible: ${url}`, colors.red);
    if (error.response) {
      log(`   Código de estado: ${error.response.status}`, colors.yellow);
    } else if (error.request) {
      log(`   Error de conexión: ${error.message}`, colors.yellow);
    } else {
      log(`   Error: ${error.message}`, colors.yellow);
    }
    return false;
  }
}

// Función para verificar si una API key está configurada
function checkApiKey(key, name) {
  if (!key || key === 'undefined') {
    log(`❌ La API key ${name} no está configurada`, colors.red);
    return false;
  }
  // Mostrar solo los primeros y últimos 4 caracteres de la API key
  const maskedKey = key.length > 8 
    ? `${key.substring(0, 4)}...${key.substring(key.length - 4)}`
    : '****';
  log(`✅ La API key ${name} está configurada: ${maskedKey}`, colors.green);
  return true;
}

// Función para verificar si un archivo existe
function checkFile(filePath, name) {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) {
    log(`❌ El archivo ${name} no existe: ${fullPath}`, colors.red);
    return false;
  }
  log(`✅ El archivo ${name} existe: ${fullPath}`, colors.green);
  return true;
}

// Función principal
async function main() {
  log('=== Verificando configuración de Criptokens ===', colors.bright + colors.blue);
  log('');

  // Verificar directorios
  log('--- Verificando directorios ---', colors.bright);
  const frontendDir = checkDirectory(config.directories.frontend, 'Frontend');
  const backendDir = checkDirectory(config.directories.backend, 'Backend');
  const cryptoMcpDir = checkDirectory(config.directories.cryptoMcp, 'Crypto MCP Server');
  const playwrightMcpDir = checkDirectory(config.directories.playwrightMcp, 'Playwright MCP Server');
  log('');

  // Verificar archivos de configuración
  log('--- Verificando archivos de configuración ---', colors.bright);
  const configFile = checkFile('config.js', 'Configuración principal');
  const mcpConfigFile = checkFile('mcp-config.json', 'Configuración MCP');
  const envFile = checkFile('.env', 'Variables de entorno');
  const frontendEnvFile = checkFile(path.join(config.directories.frontend, '.env.local'), 'Variables de entorno del frontend');
  const backendEnvFile = checkFile(path.join(config.directories.backend, '.env'), 'Variables de entorno del backend');
  log('');

  // Verificar API keys
  log('--- Verificando API keys ---', colors.bright);
  const braveApiKey = checkApiKey(config.apiKeys.braveSearch, 'Brave Search');
  const coinMarketCapApiKey = checkApiKey(config.apiKeys.coinMarketCap, 'CoinMarketCap');
  const openRouterApiKey = checkApiKey(config.apiKeys.openRouter, 'OpenRouter');
  const etherscanApiKey = checkApiKey(config.apiKeys.etherscan, 'Etherscan');
  log('');

  // Verificar servidores (si están en ejecución)
  log('--- Verificando servidores (si están en ejecución) ---', colors.bright);
  log('Nota: Si los servidores no están en ejecución, esta verificación fallará.', colors.yellow);
  log('Puedes iniciar los servidores con: node start-criptokens.js', colors.yellow);
  log('');
  
  try {
    const backendServer = await checkServer(config.urls.backend, 'Backend');
    const cryptoMcpServer = await checkServer(config.urls.cryptoMcp, 'Crypto MCP Server');
    const braveSearchMcpServer = await checkServer(config.urls.braveSearchMcp, 'Brave Search MCP Server');
    const playwrightMcpServer = await checkServer(config.urls.playwrightMcp, 'Playwright MCP Server');
  } catch (error) {
    log(`Error al verificar servidores: ${error.message}`, colors.red);
  }
  log('');

  // Verificar dependencias
  log('--- Verificando dependencias ---', colors.bright);
  try {
    log('Verificando dependencias del backend...', colors.dim);
    execSync('npm list --depth=0', { cwd: path.join(__dirname, config.directories.backend), stdio: 'ignore' });
    log('✅ Dependencias del backend instaladas correctamente', colors.green);
  } catch (error) {
    log('❌ Error en las dependencias del backend', colors.red);
    log(`   ${error.message}`, colors.yellow);
  }

  try {
    log('Verificando dependencias del frontend...', colors.dim);
    execSync('npm list --depth=0', { cwd: path.join(__dirname, config.directories.frontend), stdio: 'ignore' });
    log('✅ Dependencias del frontend instaladas correctamente', colors.green);
  } catch (error) {
    log('❌ Error en las dependencias del frontend', colors.red);
    log(`   ${error.message}`, colors.yellow);
  }
  log('');

  // Resumen
  log('=== Resumen de la verificación ===', colors.bright + colors.blue);
  const allDirectoriesOk = frontendDir && backendDir && cryptoMcpDir && playwrightMcpDir;
  const allConfigFilesOk = configFile && mcpConfigFile && envFile && frontendEnvFile && backendEnvFile;
  const allApiKeysOk = braveApiKey && coinMarketCapApiKey && openRouterApiKey && etherscanApiKey;

  if (allDirectoriesOk && allConfigFilesOk && allApiKeysOk) {
    log('✅ Todos los componentes están correctamente configurados', colors.green + colors.bright);
    log('Puedes iniciar la aplicación con: node start-criptokens.js', colors.green);
  } else {
    log('❌ Hay problemas en la configuración que deben ser corregidos', colors.red + colors.bright);
    
    if (!allDirectoriesOk) {
      log('- Verifica que todos los directorios existan y estén en las rutas correctas', colors.yellow);
      log('  Puedes modificar las rutas en el archivo config.js', colors.yellow);
    }
    
    if (!allConfigFilesOk) {
      log('- Verifica que todos los archivos de configuración existan', colors.yellow);
      log('  Puedes crear los archivos faltantes siguiendo los ejemplos', colors.yellow);
    }
    
    if (!allApiKeysOk) {
      log('- Verifica que todas las API keys estén configuradas', colors.yellow);
      log('  Puedes obtener las API keys en los sitios web correspondientes', colors.yellow);
      log('  y configurarlas en el archivo config.js o en las variables de entorno', colors.yellow);
    }
  }
}

// Ejecutar la función principal
main().catch(error => {
  log(`Error inesperado: ${error.message}`, colors.red);
  process.exit(1);
});
