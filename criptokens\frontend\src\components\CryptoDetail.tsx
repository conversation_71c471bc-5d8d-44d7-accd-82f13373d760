import { useState, useEffect } from 'react';
import { getCryptoDetails } from '../services/api';
import PriceChart from './PriceChart';
import '../styles/CryptoDetail.css';

interface CryptoDetailProps {
  cryptoId: string;
  onBack: () => void;
}

interface CryptoData {
  id: string;
  name: string;
  symbol: string;
  image: {
    large: string;
  };
  market_data: {
    current_price: {
      usd: number;
    };
    price_change_percentage_24h: number;
    market_cap: {
      usd: number;
    };
    total_volume: {
      usd: number;
    };
    circulating_supply: number;
    total_supply: number;
    max_supply: number | null;
    ath: {
      usd: number;
    };
    ath_change_percentage: {
      usd: number;
    };
    ath_date: {
      usd: string;
    };
  };
  description: {
    en: string;
  };
}

const CryptoDetail = ({ cryptoId, onBack }: CryptoDetailProps) => {
  const [cryptoData, setCryptoData] = useState<CryptoData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCryptoDetails = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const data = await getCryptoDetails(cryptoId);
        setCryptoData(data);
      } catch (err) {
        console.error('Error fetching crypto details:', err);
        setError('Failed to load cryptocurrency details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCryptoDetails();
  }, [cryptoId]);

  if (isLoading) {
    return <div className="crypto-detail-loading">Loading cryptocurrency details...</div>;
  }

  if (error || !cryptoData) {
    return (
      <div className="crypto-detail-error">
        <p>{error || 'Failed to load data'}</p>
        <button className="back-button" onClick={onBack}>Back to List</button>
      </div>
    );
  }

  // Formatear números grandes
  const formatNumber = (num: number) => {
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };

  // Formatear porcentajes
  const formatPercentage = (percentage: number) => {
    return percentage.toFixed(2) + '%';
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  return (
    <div className="crypto-detail">
      <div className="crypto-detail-header">
        <button className="back-button" onClick={onBack}>
          ← Back to List
        </button>
        <div className="crypto-basic-info">
          <img src={cryptoData.image.large} alt={cryptoData.name} className="crypto-logo" />
          <div>
            <h2>{cryptoData.name} ({cryptoData.symbol.toUpperCase()})</h2>
            <div className="crypto-price-container">
              <span className="crypto-price">${formatNumber(cryptoData.market_data.current_price.usd)}</span>
              <span className={`crypto-price-change ${cryptoData.market_data.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                {cryptoData.market_data.price_change_percentage_24h >= 0 ? '▲' : '▼'} 
                {formatPercentage(Math.abs(cryptoData.market_data.price_change_percentage_24h))}
              </span>
            </div>
          </div>
        </div>
      </div>

      <PriceChart 
        cryptoId={cryptoData.id} 
        name={cryptoData.name} 
        color={cryptoData.market_data.price_change_percentage_24h >= 0 ? '#00C853' : '#FF3D00'} 
      />

      <div className="crypto-stats-container">
        <h3>Market Stats</h3>
        <div className="crypto-stats-grid">
          <div className="stat-item">
            <span className="stat-label">Market Cap</span>
            <span className="stat-value">${formatNumber(cryptoData.market_data.market_cap.usd)}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">24h Trading Volume</span>
            <span className="stat-value">${formatNumber(cryptoData.market_data.total_volume.usd)}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Circulating Supply</span>
            <span className="stat-value">{formatNumber(cryptoData.market_data.circulating_supply)} {cryptoData.symbol.toUpperCase()}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Total Supply</span>
            <span className="stat-value">
              {cryptoData.market_data.total_supply 
                ? formatNumber(cryptoData.market_data.total_supply) + ' ' + cryptoData.symbol.toUpperCase()
                : 'N/A'}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Max Supply</span>
            <span className="stat-value">
              {cryptoData.market_data.max_supply 
                ? formatNumber(cryptoData.market_data.max_supply) + ' ' + cryptoData.symbol.toUpperCase()
                : 'Unlimited'}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">All Time High</span>
            <span className="stat-value">${formatNumber(cryptoData.market_data.ath.usd)}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">ATH Change</span>
            <span className={`stat-value ${cryptoData.market_data.ath_change_percentage.usd >= 0 ? 'positive' : 'negative'}`}>
              {formatPercentage(cryptoData.market_data.ath_change_percentage.usd)}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">ATH Date</span>
            <span className="stat-value">{formatDate(cryptoData.market_data.ath_date.usd)}</span>
          </div>
        </div>
      </div>

      {cryptoData.description.en && (
        <div className="crypto-description">
          <h3>About {cryptoData.name}</h3>
          <div 
            className="description-content" 
            dangerouslySetInnerHTML={{ __html: cryptoData.description.en }}
          />
        </div>
      )}
    </div>
  );
};

export default CryptoDetail;
