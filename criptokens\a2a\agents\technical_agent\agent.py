import json
from typing import Dict, Any, AsyncIterable, Optional
import logging
import aiohttp
import os
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TechnicalAnalysisAgent:
    """Agent for technical analysis of cryptocurrency price data."""
    
    SUPPORTED_CONTENT_TYPES = ["text", "data"]
    
    def __init__(self):
        self.api_key = os.getenv("COINMARKETCAP_API_KEY", "37f9968e-6ab7-431f-80d7-0ac6686319f3")
        self.mcp_url = os.getenv("MCP_SERVER_URL", "http://localhost:3101")
    
    async def fetch_historical_data(self, crypto_id: str, days: int = 30) -> Dict[str, Any]:
        """Fetch historical price data for a cryptocurrency.
        
        Args:
            crypto_id: The ID of the cryptocurrency
            days: Number of days of historical data to fetch
            
        Returns:
            Historical price data
        """
        try:
            # Try to get data from MCP service first
            async with aiohttp.ClientSession() as session:
                payload = {
                    "cryptoId": crypto_id,
                    "days": days
                }
                async with session.post(f"{self.mcp_url}/historical", json=payload) as response:
                    if response.status == 200:
                        return await response.json()
            
            # Fallback to direct API call if MCP fails
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            url = f"https://pro-api.coinmarketcap.com/v2/cryptocurrency/ohlcv/historical"
            parameters = {
                'id': crypto_id,
                'time_start': start_date.strftime('%Y-%m-%dT%H:%M:%S'),
                'time_end': end_date.strftime('%Y-%m-%dT%H:%M:%S'),
                'interval': 'daily'
            }
            
            headers = {
                'X-CMC_PRO_API_KEY': self.api_key,
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=parameters, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"Error fetching historical data: {await response.text()}")
                        return self._generate_simulated_data(crypto_id, days)
        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return self._generate_simulated_data(crypto_id, days)
    
    def _generate_simulated_data(self, crypto_id: str, days: int) -> Dict[str, Any]:
        """Generate simulated historical data.
        
        Args:
            crypto_id: The ID of the cryptocurrency
            days: Number of days of historical data to generate
            
        Returns:
            Simulated historical data
        """
        import random
        
        data = {
            "status": {
                "timestamp": datetime.now().isoformat(),
                "error_code": 0,
                "error_message": None,
                "elapsed": 10,
                "credit_count": 1
            },
            "data": {
                "id": crypto_id,
                "name": f"Crypto {crypto_id}",
                "symbol": "CRYPTO",
                "quotes": []
            }
        }
        
        base_price = 1000 + random.random() * 9000
        volatility = 0.02
        
        end_date = datetime.now()
        for i in range(days):
            date = end_date - timedelta(days=i)
            change = (random.random() - 0.5) * 2 * volatility
            base_price = base_price * (1 + change)
            
            open_price = base_price * (1 + (random.random() - 0.5) * 0.01)
            close_price = base_price * (1 + (random.random() - 0.5) * 0.01)
            high_price = max(open_price, close_price) * (1 + random.random() * 0.01)
            low_price = min(open_price, close_price) * (1 - random.random() * 0.01)
            volume = base_price * 1000 * (0.5 + random.random())
            
            data["data"]["quotes"].append({
                "timestamp": date.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
                "quote": {
                    "USD": {
                        "open": open_price,
                        "high": high_price,
                        "low": low_price,
                        "close": close_price,
                        "volume": volume,
                        "market_cap": close_price * 1000000,
                        "timestamp": date.strftime('%Y-%m-%dT%H:%M:%S.000Z')
                    }
                }
            })
        
        return data
    
    async def analyze_technical_indicators(self, historical_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate technical indicators from historical data.
        
        Args:
            historical_data: Historical price data
            
        Returns:
            Technical indicators
        """
        try:
            quotes = historical_data.get("data", {}).get("quotes", [])
            if not quotes:
                return {"error": "No historical data available"}
            
            # Sort quotes by timestamp (newest first)
            quotes.sort(key=lambda x: x.get("timestamp"), reverse=True)
            
            # Extract closing prices
            closing_prices = [quote.get("quote", {}).get("USD", {}).get("close", 0) for quote in quotes]
            
            # Calculate simple moving averages
            sma_7 = sum(closing_prices[:7]) / 7 if len(closing_prices) >= 7 else None
            sma_14 = sum(closing_prices[:14]) / 14 if len(closing_prices) >= 14 else None
            sma_30 = sum(closing_prices[:30]) / 30 if len(closing_prices) >= 30 else None
            
            # Calculate price change percentages
            price_change_24h = ((closing_prices[0] / closing_prices[1]) - 1) * 100 if len(closing_prices) >= 2 else None
            price_change_7d = ((closing_prices[0] / closing_prices[6]) - 1) * 100 if len(closing_prices) >= 7 else None
            price_change_30d = ((closing_prices[0] / closing_prices[29]) - 1) * 100 if len(closing_prices) >= 30 else None
            
            # Calculate volatility (standard deviation of daily returns)
            daily_returns = []
            for i in range(1, len(closing_prices)):
                daily_return = (closing_prices[i-1] / closing_prices[i]) - 1
                daily_returns.append(daily_return)
            
            import numpy as np
            volatility = np.std(daily_returns) * 100 if daily_returns else None
            
            # Calculate RSI (Relative Strength Index)
            rsi = self._calculate_rsi(closing_prices, 14) if len(closing_prices) >= 14 else None
            
            # Determine trend
            trend = "neutral"
            if sma_7 and sma_30:
                if sma_7 > sma_30:
                    trend = "bullish"
                elif sma_7 < sma_30:
                    trend = "bearish"
            
            # Determine support and resistance levels
            support, resistance = self._calculate_support_resistance(quotes)
            
            return {
                "current_price": closing_prices[0],
                "sma_7": sma_7,
                "sma_14": sma_14,
                "sma_30": sma_30,
                "price_change_24h": price_change_24h,
                "price_change_7d": price_change_7d,
                "price_change_30d": price_change_30d,
                "volatility": volatility,
                "rsi": rsi,
                "trend": trend,
                "support": support,
                "resistance": resistance
            }
        except Exception as e:
            logger.error(f"Error analyzing technical indicators: {e}")
            return {"error": f"Error analyzing technical indicators: {str(e)}"}
    
    def _calculate_rsi(self, prices: list, period: int = 14) -> float:
        """Calculate the Relative Strength Index.
        
        Args:
            prices: List of prices (newest first)
            period: RSI period
            
        Returns:
            RSI value
        """
        if len(prices) <= period:
            return 50  # Default neutral value
        
        # Calculate price changes
        deltas = [prices[i-1] - prices[i] for i in range(1, len(prices))]
        
        # Get gains and losses
        gains = [delta if delta > 0 else 0 for delta in deltas]
        losses = [-delta if delta < 0 else 0 for delta in deltas]
        
        # Calculate average gains and losses
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        if avg_loss == 0:
            return 100  # No losses, RSI is 100
        
        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_support_resistance(self, quotes: list) -> tuple:
        """Calculate support and resistance levels.
        
        Args:
            quotes: List of price quotes
            
        Returns:
            Tuple of (support, resistance) prices
        """
        # Extract high and low prices
        highs = [quote.get("quote", {}).get("USD", {}).get("high", 0) for quote in quotes]
        lows = [quote.get("quote", {}).get("USD", {}).get("low", 0) for quote in quotes]
        
        # Simple approach: support is recent low, resistance is recent high
        support = min(lows[:7]) if len(lows) >= 7 else min(lows)
        resistance = max(highs[:7]) if len(highs) >= 7 else max(highs)
        
        return support, resistance
    
    async def process_query(self, query: str, session_id: str) -> Dict[str, Any]:
        """Process a query and return technical analysis.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            Technical analysis results
        """
        # Extract crypto ID from query
        crypto_id = self._extract_crypto_id(query)
        if not crypto_id:
            return {
                "error": "Could not determine which cryptocurrency to analyze. Please specify a cryptocurrency."
            }
        
        # Extract timeframe from query
        days = self._extract_timeframe(query)
        
        # Fetch historical data
        historical_data = await self.fetch_historical_data(crypto_id, days)
        
        # Analyze technical indicators
        indicators = await self.analyze_technical_indicators(historical_data)
        
        # Generate analysis summary
        summary = self._generate_analysis_summary(indicators, crypto_id)
        
        return {
            "crypto_id": crypto_id,
            "timeframe_days": days,
            "indicators": indicators,
            "summary": summary
        }
    
    def _extract_crypto_id(self, query: str) -> Optional[str]:
        """Extract cryptocurrency ID from query.
        
        Args:
            query: The user's query
            
        Returns:
            Cryptocurrency ID or None if not found
        """
        # Common cryptocurrencies and their IDs
        crypto_mapping = {
            "bitcoin": "1",
            "btc": "1",
            "ethereum": "1027",
            "eth": "1027",
            "binance coin": "1839",
            "bnb": "1839",
            "cardano": "2010",
            "ada": "2010",
            "solana": "5426",
            "sol": "5426",
            "xrp": "52",
            "dogecoin": "74",
            "doge": "74",
            "polkadot": "6636",
            "dot": "6636",
            "tether": "825",
            "usdt": "825",
            "usd coin": "3408",
            "usdc": "3408"
        }
        
        query_lower = query.lower()
        
        # Check for exact matches
        for crypto_name, crypto_id in crypto_mapping.items():
            if crypto_name in query_lower:
                return crypto_id
        
        # Default to Bitcoin if no match found
        return "1"
    
    def _extract_timeframe(self, query: str) -> int:
        """Extract timeframe from query.
        
        Args:
            query: The user's query
            
        Returns:
            Number of days for analysis
        """
        query_lower = query.lower()
        
        if "year" in query_lower or "365" in query_lower:
            return 365
        elif "6 month" in query_lower or "180" in query_lower:
            return 180
        elif "3 month" in query_lower or "90" in query_lower:
            return 90
        elif "month" in query_lower or "30" in query_lower:
            return 30
        elif "week" in query_lower or "7" in query_lower:
            return 7
        elif "day" in query_lower or "24" in query_lower:
            return 1
        
        # Default to 30 days
        return 30
    
    def _generate_analysis_summary(self, indicators: Dict[str, Any], crypto_id: str) -> str:
        """Generate a summary of the technical analysis.
        
        Args:
            indicators: Technical indicators
            crypto_id: Cryptocurrency ID
            
        Returns:
            Summary text
        """
        if "error" in indicators:
            return f"Error in technical analysis: {indicators['error']}"
        
        crypto_names = {
            "1": "Bitcoin",
            "1027": "Ethereum",
            "1839": "Binance Coin",
            "2010": "Cardano",
            "5426": "Solana",
            "52": "XRP",
            "74": "Dogecoin",
            "6636": "Polkadot",
            "825": "Tether",
            "3408": "USD Coin"
        }
        
        crypto_name = crypto_names.get(crypto_id, f"Cryptocurrency {crypto_id}")
        
        # Format price with commas for thousands
        current_price = f"${indicators['current_price']:,.2f}"
        
        # Create summary based on indicators
        summary = f"Technical Analysis for {crypto_name} (Current Price: {current_price}):\n\n"
        
        # Add price changes
        summary += "Price Changes:\n"
        if indicators.get('price_change_24h') is not None:
            summary += f"• 24h: {indicators['price_change_24h']:.2f}%\n"
        if indicators.get('price_change_7d') is not None:
            summary += f"• 7d: {indicators['price_change_7d']:.2f}%\n"
        if indicators.get('price_change_30d') is not None:
            summary += f"• 30d: {indicators['price_change_30d']:.2f}%\n"
        
        # Add moving averages
        summary += "\nMoving Averages:\n"
        if indicators.get('sma_7') is not None:
            summary += f"• 7-day SMA: ${indicators['sma_7']:,.2f}\n"
        if indicators.get('sma_14') is not None:
            summary += f"• 14-day SMA: ${indicators['sma_14']:,.2f}\n"
        if indicators.get('sma_30') is not None:
            summary += f"• 30-day SMA: ${indicators['sma_30']:,.2f}\n"
        
        # Add other indicators
        summary += "\nOther Indicators:\n"
        if indicators.get('rsi') is not None:
            rsi = indicators['rsi']
            rsi_interpretation = "neutral"
            if rsi > 70:
                rsi_interpretation = "overbought"
            elif rsi < 30:
                rsi_interpretation = "oversold"
            summary += f"• RSI (14): {rsi:.2f} ({rsi_interpretation})\n"
        
        if indicators.get('volatility') is not None:
            summary += f"• Volatility: {indicators['volatility']:.2f}%\n"
        
        if indicators.get('support') is not None and indicators.get('resistance') is not None:
            summary += f"• Support: ${indicators['support']:,.2f}\n"
            summary += f"• Resistance: ${indicators['resistance']:,.2f}\n"
        
        # Add trend analysis
        if indicators.get('trend'):
            trend = indicators['trend']
            trend_emoji = "↔️"
            if trend == "bullish":
                trend_emoji = "🔼"
            elif trend == "bearish":
                trend_emoji = "🔽"
            
            summary += f"\nOverall Trend: {trend_emoji} {trend.capitalize()}\n"
        
        return summary
    
    async def invoke(self, query: str, session_id: str) -> str:
        """Process a query and return a text response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            Text response
        """
        result = await self.process_query(query, session_id)
        
        if "error" in result:
            return f"Error: {result['error']}"
        
        return result["summary"]
    
    async def stream(self, query: str, session_id: str) -> AsyncIterable[Dict[str, Any]]:
        """Process a query and stream the response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Yields:
            Response updates
        """
        # Extract crypto ID from query
        crypto_id = self._extract_crypto_id(query)
        if not crypto_id:
            yield {
                "is_task_complete": True,
                "content": "Could not determine which cryptocurrency to analyze. Please specify a cryptocurrency."
            }
            return
        
        # Extract timeframe from query
        days = self._extract_timeframe(query)
        
        # Update on fetching data
        yield {
            "is_task_complete": False,
            "updates": f"Fetching historical data for cryptocurrency ID {crypto_id}..."
        }
        
        # Fetch historical data
        historical_data = await self.fetch_historical_data(crypto_id, days)
        
        # Update on analyzing data
        yield {
            "is_task_complete": False,
            "updates": "Analyzing technical indicators..."
        }
        
        # Analyze technical indicators
        indicators = await self.analyze_technical_indicators(historical_data)
        
        # Generate analysis summary
        summary = self._generate_analysis_summary(indicators, crypto_id)
        
        # Final response with full analysis
        yield {
            "is_task_complete": True,
            "content": {
                "crypto_id": crypto_id,
                "timeframe_days": days,
                "indicators": indicators,
                "summary": summary
            }
        }
