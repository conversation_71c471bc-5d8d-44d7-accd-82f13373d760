/**
 * CriptoAgentAvatar - Componente 2D del avatar del Guru Cripto
 *
 * NOTA: Este componente será reemplazado por una versión 3D en futuras actualizaciones.
 * La versión actual implementa un avatar 2D con reacciones básicas a los datos de criptomonedas.
 */

import React, { useEffect, useRef, useState, useContext } from 'react';
import { AvatarStatusContext } from './Layout';
import anime from '../utils/animeUtils';
import '../styles/CriptoAgentAvatar.css';

// Interfaz para los datos de criptomonedas
interface CryptoData {
  id?: string;
  name?: string;
  symbol?: string;
  price?: number;
  priceUsd?: number;
  changePercent24Hr?: number;
  volumeUsd24Hr?: number;
}

interface CriptoAgentAvatarProps {
  mood?: 'neutral' | 'happy' | 'thinking' | 'excited' | 'concerned' | 'analyzing' | 'predicting' | 'bullish' | 'bearish' | 'volatile';
  speaking?: boolean;
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  pulseEffect?: boolean;
  interactive?: boolean;
  powerMode?: 'normal' | 'enhanced' | 'predictive' | 'analytical';
  onCommand?: (command: string) => void;
  cryptoData?: CryptoData;
  marketSentiment?: 'bullish' | 'bearish' | 'neutral' | 'volatile';
  priceChangeIntensity?: 'low' | 'medium' | 'high' | 'extreme';
}

export const CriptoAgentAvatar: React.FC<CriptoAgentAvatarProps> = ({
  mood = 'neutral',
  speaking = false,
  size = 'medium',
  pulseEffect = false,
  interactive = false,
  powerMode = 'normal',
  onCommand,
  cryptoData,
  marketSentiment = 'neutral',
  priceChangeIntensity = 'low'
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isActivated, setIsActivated] = useState(false);
  const [voiceCommand, setVoiceCommand] = useState('');
  const [showCommandInterface, setShowCommandInterface] = useState(false);
  const avatarRef = useRef<HTMLDivElement>(null);
  const eyesRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);

  // Efecto para seguir el cursor con los ojos
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!avatarRef.current || !eyesRef.current) return;

      const avatar = avatarRef.current.getBoundingClientRect();
      const avatarCenterX = avatar.left + avatar.width / 2;
      const avatarCenterY = avatar.top + avatar.height / 2;

      const angleX = (e.clientX - avatarCenterX) / (window.innerWidth / 2);
      const angleY = (e.clientY - avatarCenterY) / (window.innerHeight / 2);

      // Limitar el movimiento de los ojos
      const maxMove = 3;
      const moveX = Math.min(Math.max(angleX * maxMove, -maxMove), maxMove);
      const moveY = Math.min(Math.max(angleY * maxMove, -maxMove), maxMove);

      eyesRef.current.style.transform = `translate(${moveX}px, ${moveY}px)`;
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Obtener el contexto global del estado del avatar
  const { setAvatarStatus } = useContext(AvatarStatusContext);

  // Efecto para animaciones cuando cambia el modo de poder o los datos de criptomonedas
  useEffect(() => {
    if (!avatarRef.current) return;

    // Detener todas las animaciones previas para evitar conflictos
    anime.remove(avatarRef.current);
    anime.remove(avatarRef.current.querySelector('.avatar-core'));
    anime.remove(avatarRef.current.querySelectorAll('.avatar-ring'));
    anime.remove(eyesRef.current);

    // Determinar el color y la animación basados en los datos de criptomonedas
    let ringColor = '#00f2ff';
    let coreAnimation = {};
    let coreColor = 'radial-gradient(circle, #4657ce 0%, #00f2ff 100%)';
    let newAvatarStatus = 'neutral';
    let avatarAnimation = {};

    // Si hay datos de criptomonedas, ajustar la animación según el cambio porcentual
    if (cryptoData && cryptoData.changePercent24Hr !== undefined) {
      const changePercent = cryptoData.changePercent24Hr;

      if (changePercent > 5) {
        // Muy positivo - verde brillante
        ringColor = '#00ff9d';
        coreColor = 'radial-gradient(circle, #00cc7a 0%, #00ff9d 100%)';
        coreAnimation = {
          scale: [1, 1.3, 1],
          opacity: [1, 0.8, 1],
          duration: 800,
          easing: 'easeInOutQuad'
        };
        // Animación de salto vertical para estado positivo
        avatarAnimation = {
          translateY: [0, -10, 0],
          duration: 1200,
          easing: 'easeInOutQuad',
          loop: true
        };
        newAvatarStatus = 'positive';
      } else if (changePercent > 2) {
        // Positivo - verde azulado
        ringColor = '#00f2ff';
        coreColor = 'radial-gradient(circle, #00c2cc 0%, #00f2ff 100%)';
        coreAnimation = {
          scale: [1, 1.2, 1],
          opacity: [1, 0.9, 1],
          duration: 1200,
          easing: 'easeInOutQuad'
        };
        // Animación de balanceo suave para estado positivo moderado
        avatarAnimation = {
          rotate: [-3, 3, -3],
          duration: 2000,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = 'positive';
      } else if (changePercent < -5) {
        // Muy negativo - rojo
        ringColor = '#ff3a6e';
        coreColor = 'radial-gradient(circle, #cc2357 0%, #ff3a6e 100%)';
        coreAnimation = {
          scale: [1, 0.9, 1],
          opacity: [1, 0.7, 1],
          duration: 1000,
          easing: 'easeInOutQuad'
        };
        // Animación de vibración horizontal para estado muy negativo
        avatarAnimation = {
          translateX: [-3, 3, -3, 3, -3, 3, -3],
          duration: 500,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = 'negative';
      } else if (changePercent < -2) {
        // Negativo - naranja rojizo
        ringColor = '#ff6b3d';
        coreColor = 'radial-gradient(circle, #cc5430 0%, #ff6b3d 100%)';
        coreAnimation = {
          scale: [1, 0.95, 1],
          opacity: [1, 0.8, 1],
          duration: 1500,
          easing: 'easeInOutQuad'
        };
        // Animación de parpadeo para estado de preocupación
        avatarAnimation = {
          backgroundColor: ['rgba(255, 107, 61, 0)', 'rgba(255, 107, 61, 0.1)', 'rgba(255, 107, 61, 0)'],
          duration: 1500,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = 'concerned';
      } else {
        // Neutral - azul púrpura
        ringColor = '#7b4dff';
        coreColor = 'radial-gradient(circle, #5e3dcc 0%, #7b4dff 100%)';
        coreAnimation = {
          scale: [1, 1.1, 1],
          opacity: [1, 0.9, 1],
          duration: 2000,
          easing: 'easeInOutQuad'
        };
        // Animación de respiración suave para estado neutral
        avatarAnimation = {
          scale: [1, 1.03, 1],
          duration: 3000,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = 'neutral';
      }

      // Actualizar el estado global del avatar
      setAvatarStatus(newAvatarStatus as any);

      // Aplicar color al núcleo
      if (avatarRef.current.querySelector('.avatar-core')) {
        (avatarRef.current.querySelector('.avatar-core') as HTMLElement).style.background = coreColor;
      }
    } else if (powerMode !== 'normal') {
      // Si no hay datos de criptomonedas pero hay un modo de poder, usar ese color
      ringColor =
        powerMode === 'enhanced' ? '#00f2ff' :
        powerMode === 'predictive' ? '#7b4dff' :
        powerMode === 'analytical' ? '#ff2a6d' : '#00f2ff';

      coreAnimation = {
        scale: [1, 1.2, 1],
        opacity: [1, 0.8, 1],
        duration: 1000,
        easing: 'easeInOutQuad'
      };

      // Definir animaciones según el modo de poder
      if (powerMode === 'enhanced') {
        avatarAnimation = {
          scale: [1, 1.05, 1],
          duration: 2000,
          easing: 'easeInOutSine',
          loop: true
        };
        setAvatarStatus('positive' as any);
      }
      else if (powerMode === 'predictive') {
        // Animación de pulso lento para modo predictivo/thinking
        avatarAnimation = {
          scale: [1, 1.08, 1],
          opacity: [1, 0.9, 1],
          duration: 3000,
          easing: 'easeInOutQuad',
          loop: true
        };
        newAvatarStatus = 'thinking';
        setAvatarStatus('thinking' as any);
      }
      else if (powerMode === 'analytical') {
        // Animación de enfoque para modo analítico
        avatarAnimation = {
          boxShadow: ['0 0 10px rgba(255, 42, 109, 0.3)', '0 0 20px rgba(255, 42, 109, 0.6)', '0 0 10px rgba(255, 42, 109, 0.3)'],
          duration: 2000,
          easing: 'easeInOutSine',
          loop: true
        };
        setAvatarStatus('concerned' as any);
      }
    }

    // Animar el núcleo con efectos más dinámicos
    if (Object.keys(coreAnimation).length > 0) {
      anime({
        targets: avatarRef.current.querySelector('.avatar-core'),
        ...coreAnimation,
        loop: true
      });
    }

    // Animar anillos con animaciones más dinámicas y colores según el estado
    anime({
      targets: avatarRef.current.querySelectorAll('.avatar-ring'),
      borderColor: [{ value: ringColor }],
      opacity: [0.7, 0.9, 0.7],
      scale: [1, 1.05, 1],
      easing: 'easeInOutSine',
      duration: 2000,
      loop: true
    });

    // Animar el avatar completo si hay una animación definida
    if (Object.keys(avatarAnimation).length > 0) {
      anime({
        targets: avatarRef.current,
        ...avatarAnimation
      });
    }

    // Aplicar efectos adicionales según el estado
    if (newAvatarStatus === 'positive') {
      // Efecto de brillo pulsante para estado positivo
      anime({
        targets: avatarRef.current,
        boxShadow: [
          '0 0 5px rgba(0, 255, 157, 0.3)',
          '0 0 15px rgba(0, 255, 157, 0.5)',
          '0 0 5px rgba(0, 255, 157, 0.3)'
        ],
        duration: 2000,
        easing: 'easeInOutSine',
        loop: true
      });
    } else if (newAvatarStatus === 'negative') {
      // Efecto de vibración sutil para estado negativo
      anime({
        targets: avatarRef.current,
        translateX: [-2, 2, -2, 2, -2, 0],
        duration: 500,
        easing: 'easeInOutSine',
        loop: true
      });
    }

    // Animar los ojos según el estado
    if (eyesRef.current) {
      switch (newAvatarStatus) {
        case 'thinking':
          // Efecto de parpadeo para modo pensativo
          anime({
            targets: eyesRef.current.querySelectorAll('.eye'),
            opacity: [1, 0.5, 1],
            duration: 2000,
            easing: 'easeInOutSine',
            loop: true
          });
          break;
        case 'positive':
          // Efecto de brillo para ojos felices
          anime({
            targets: eyesRef.current.querySelectorAll('.eye'),
            boxShadow: ['0 0 2px rgba(0, 255, 157, 0.3)', '0 0 8px rgba(0, 255, 157, 0.7)', '0 0 2px rgba(0, 255, 157, 0.3)'],
            duration: 2000,
            easing: 'easeInOutQuad',
            loop: true
          });
          break;
        case 'negative':
          // Efecto de vibración para ojos negativos
          anime({
            targets: eyesRef.current.querySelectorAll('.eye'),
            translateX: [0, 1, -1, 1, 0],
            duration: 500,
            easing: 'easeInOutSine',
            loop: true
          });
          break;
        case 'concerned':
          // Efecto de parpadeo amarillo para preocupación
          anime({
            targets: eyesRef.current.querySelectorAll('.eye'),
            backgroundColor: ['rgba(255, 204, 0, 0.9)', 'rgba(255, 107, 61, 0.9)', 'rgba(255, 204, 0, 0.9)'],
            duration: 2000,
            easing: 'easeInOutSine',
            loop: true
          });
          break;
      }
    }

    // Las animaciones de partículas ahora se manejan en AnimatedBackground.tsx
  }, [powerMode, cryptoData, marketSentiment, priceChangeIntensity, setAvatarStatus]);

  // Efecto para animaciones cuando cambia directamente el estado de ánimo (mood)
  useEffect(() => {
    if (!avatarRef.current || !eyesRef.current) return;

    // Detener todas las animaciones previas para evitar conflictos
    anime.remove(avatarRef.current);
    anime.remove(avatarRef.current.querySelector('.avatar-core'));
    anime.remove(avatarRef.current.querySelectorAll('.avatar-ring'));
    anime.remove(eyesRef.current);
    anime.remove(eyesRef.current.querySelectorAll('.eye'));

    // Definir animaciones según el estado de ánimo
    let avatarAnimation = {};
    let coreAnimation = {};
    let eyesAnimation = {};
    let newAvatarStatus = 'neutral';

    switch (mood) {
      case 'bullish':
      case 'happy':
      case 'excited':
        // Animación de salto vertical para estado positivo
        avatarAnimation = {
          translateY: [0, -10, 0],
          duration: 1200,
          easing: 'easeInOutQuad',
          loop: true
        };
        coreAnimation = {
          scale: [1, 1.3, 1],
          opacity: [1, 0.8, 1],
          duration: 800,
          easing: 'easeInOutQuad',
          loop: true
        };
        eyesAnimation = {
          scale: [1, 1.2, 1],
          boxShadow: ['0 0 2px rgba(0, 255, 157, 0.3)', '0 0 8px rgba(0, 255, 157, 0.7)', '0 0 2px rgba(0, 255, 157, 0.3)'],
          duration: 1500,
          easing: 'easeInOutQuad',
          loop: true
        };
        newAvatarStatus = 'positive';
        break;

      case 'bearish':
      case 'concerned':
        // Animación de vibración horizontal para estado negativo/preocupado
        avatarAnimation = {
          translateX: [-3, 3, -3, 3, -3, 3, -3],
          duration: 500,
          easing: 'easeInOutSine',
          loop: true
        };
        coreAnimation = {
          scale: [1, 0.9, 1],
          opacity: [1, 0.7, 1],
          duration: 1000,
          easing: 'easeInOutQuad',
          loop: true
        };
        eyesAnimation = {
          translateX: [0, 1, -1, 1, 0],
          backgroundColor: ['rgba(255, 107, 61, 0.9)', 'rgba(255, 58, 110, 0.9)', 'rgba(255, 107, 61, 0.9)'],
          duration: 800,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = mood === 'bearish' ? 'negative' : 'concerned';
        break;

      case 'thinking':
      case 'predicting':
      case 'analyzing':
        // Animación de pulso de brillo para modo pensativo
        avatarAnimation = {
          scale: [1, 1.05, 1],
          duration: 3000,
          easing: 'easeInOutSine',
          loop: true
        };
        coreAnimation = {
          opacity: [0.7, 1, 0.7],
          filter: ['brightness(0.9)', 'brightness(1.2)', 'brightness(0.9)'],
          duration: 2000,
          easing: 'easeInOutSine',
          loop: true
        };
        eyesAnimation = {
          opacity: [1, 0.5, 1],
          duration: 2000,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = 'thinking';
        break;

      case 'volatile':
        // Animación de movimiento aleatorio para volatilidad
        avatarAnimation = {
          rotate: [-2, 2, -2],
          translateX: [-2, 2, -2],
          translateY: [1, -1, 1],
          duration: 1000,
          easing: 'easeInOutSine',
          loop: true
        };
        coreAnimation = {
          scale: [1, 1.1, 0.95, 1.1, 1],
          opacity: [0.9, 1, 0.8, 1, 0.9],
          duration: 1500,
          easing: 'easeInOutQuad',
          loop: true
        };
        eyesAnimation = {
          translateX: [-2, 2, -2],
          translateY: [1, -1, 1],
          duration: 800,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = 'concerned';
        break;

      default: // neutral
        // Animación de respiración suave para estado neutral/idle
        avatarAnimation = {
          scale: [1, 1.03, 1],
          duration: 3000,
          easing: 'easeInOutSine',
          loop: true
        };
        coreAnimation = {
          scale: [1, 1.1, 1],
          opacity: [0.9, 1, 0.9],
          duration: 3000,
          easing: 'easeInOutSine',
          loop: true
        };
        newAvatarStatus = 'idle';
        break;
    }

    // Actualizar el estado global del avatar
    setAvatarStatus(newAvatarStatus as any);

    // Animar el núcleo con animaciones más expresivas
    if (Object.keys(coreAnimation).length > 0) {
      anime({
        targets: avatarRef.current.querySelector('.avatar-core'),
        ...coreAnimation
      });
    }

    // Animar el avatar completo con animaciones más dinámicas
    if (Object.keys(avatarAnimation).length > 0) {
      anime({
        targets: avatarRef.current,
        ...avatarAnimation
      });
    }

    // Animar los ojos con expresividad
    if (Object.keys(eyesAnimation).length > 0 && eyesRef.current) {
      anime({
        targets: eyesRef.current.querySelectorAll('.eye'),
        ...eyesAnimation
      });
    }

    // Animar los anillos según el estado
    const rings = avatarRef.current.querySelectorAll('.avatar-ring');
    let ringColor = '';

    switch (newAvatarStatus) {
      case 'positive':
        ringColor = 'rgba(0, 255, 157, 0.8)';
        break;
      case 'negative':
        ringColor = 'rgba(255, 58, 110, 0.8)';
        break;
      case 'concerned':
        ringColor = 'rgba(255, 204, 0, 0.8)';
        break;
      case 'thinking':
        ringColor = 'rgba(123, 77, 255, 0.8)';
        break;
      case 'idle':
      default:
        ringColor = 'rgba(0, 242, 255, 0.8)';
        break;
    }

    anime({
      targets: rings,
      borderColor: [{ value: ringColor }],
      opacity: [0.7, 0.9, 0.7],
      scale: [1, 1.05, 1],
      easing: 'easeInOutSine',
      duration: 2000,
      loop: true
    });
  }, [mood, setAvatarStatus]);

  // Efecto para inicializar y animar las partículas del avatar
  useEffect(() => {
    if (!particlesRef.current) return;

    // Detectar rendimiento del dispositivo
    const checkPerformance = () => {
      // Comprobar si es un dispositivo móvil o de baja potencia
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isLowPower = window.navigator.hardwareConcurrency ? window.navigator.hardwareConcurrency < 4 : false;

      return isMobile || isLowPower;
    };

    // Ajustar la calidad de las animaciones según el rendimiento
    const isLowPerformance = checkPerformance();

    // Limpiar partículas existentes
    particlesRef.current.innerHTML = '';

    // Crear círculos concéntricos (orbitas) - menos en dispositivos de bajo rendimiento
    const numOrbits = isLowPerformance ? 2 : 3;
    for (let i = 1; i <= numOrbits; i++) {
      const orbitElement = document.createElement('div');
      orbitElement.className = `avatar-orbit avatar-orbit-${i}`;
      particlesRef.current.appendChild(orbitElement);

      // Animar rotación de las orbitas (más lenta en dispositivos de bajo rendimiento)
      anime({
        targets: orbitElement,
        rotate: 360,
        duration: isLowPerformance ? 30000 + (i * 5000) : 20000 + (i * 5000),
        easing: 'linear',
        loop: true
      });
    }

    // Número de partículas (reducido para dispositivos de bajo rendimiento)
    let numParticles;
    if (isLowPerformance) {
      numParticles = size === 'small' ? 4 :
                    size === 'medium' ? 6 :
                    size === 'large' ? 8 : 10;
    } else {
      numParticles = size === 'small' ? 8 :
                    size === 'medium' ? 12 :
                    size === 'large' ? 16 : 20;
    }

    // Crear partículas
    for (let i = 0; i < numParticles; i++) {
      // Crear elemento de partícula
      const particleElement = document.createElement('div');
      particleElement.className = 'avatar-particle';

      // Determinar en qué órbita estará la partícula (distribuir entre las órbitas disponibles)
      const orbitIndex = i % numOrbits;
      const orbitRadius = [30, 45, 60][orbitIndex]; // Radio de cada órbita (ajustado según el tamaño del avatar)

      // Posición inicial en la órbita
      const angle = (i * (360 / (numParticles / numOrbits))) * (Math.PI / 180); // Distribuir partículas uniformemente
      const x = 50 + Math.cos(angle) * orbitRadius / 2; // 50% es el centro
      const y = 50 + Math.sin(angle) * orbitRadius / 2; // 50% es el centro

      particleElement.style.left = `${x}%`;
      particleElement.style.top = `${y}%`;

      // Añadir al contenedor
      particlesRef.current.appendChild(particleElement);

      // Animar la partícula en órbita (más lenta en dispositivos de bajo rendimiento)
      anime({
        targets: particleElement,
        loop: true,
        duration: isLowPerformance ? 15000 + (orbitIndex * 2000) : 10000 + (orbitIndex * 2000),
        easing: 'linear',
        keyframes: [
          {
            translateX: Math.cos(angle) * orbitRadius / 2,
            translateY: Math.sin(angle) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI/2) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI/2) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI*3/2) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI*3/2) * orbitRadius / 2
          },
          {
            translateX: Math.cos(angle + Math.PI*2) * orbitRadius / 2,
            translateY: Math.sin(angle + Math.PI*2) * orbitRadius / 2
          }
        ]
      });
    }

    // Limpiar al desmontar
    return () => {
      if (particlesRef.current) {
        anime.remove(particlesRef.current.childNodes);
        particlesRef.current.innerHTML = '';
      }
    };
  }, [size]); // Solo se ejecuta cuando cambia el tamaño del avatar

  // Efecto para actualizar las partículas según el estado de ánimo
  useEffect(() => {
    if (!particlesRef.current || particlesRef.current.childNodes.length === 0) return;

    // Determinar el estado del avatar basado en el estado de ánimo
    let avatarStatus = 'neutral';
    if (mood === 'bullish' || mood === 'happy' || mood === 'excited') avatarStatus = 'positive';
    if (mood === 'bearish' || mood === 'concerned') avatarStatus = 'negative';
    if (mood === 'analyzing' || mood === 'predicting' || mood === 'thinking') avatarStatus = 'thinking';
    if (mood === 'volatile') avatarStatus = 'concerned';

    // Obtener todas las órbitas y partículas
    const elements = Array.from(particlesRef.current.childNodes);
    const orbits = elements.filter(node => (node as HTMLElement).className.includes('avatar-orbit'));
    const particles = elements.filter(node => (node as HTMLElement).className.includes('avatar-particle'));

    // Actualizar color de las órbitas con animación
    orbits.forEach((orbit: any, index) => {
      let orbitColor = '';
      switch (avatarStatus) {
        case 'positive':
          orbitColor = index % 2 === 0 ? 'rgba(0, 255, 157, 0.4)' : 'rgba(0, 242, 255, 0.4)';
          break;
        case 'negative':
          orbitColor = index % 2 === 0 ? 'rgba(255, 58, 110, 0.4)' : 'rgba(123, 77, 255, 0.4)';
          break;
        case 'concerned':
          orbitColor = index % 2 === 0 ? 'rgba(255, 204, 0, 0.4)' : 'rgba(255, 107, 61, 0.4)';
          break;
        case 'thinking':
          orbitColor = index % 2 === 0 ? 'rgba(123, 77, 255, 0.4)' : 'rgba(0, 242, 255, 0.4)';
          break;
        default: // neutral/idle
          orbitColor = index % 2 === 0 ? 'rgba(123, 77, 255, 0.4)' : 'rgba(0, 242, 255, 0.4)';
          break;
      }

      // Animar la transición de color de las órbitas
      anime({
        targets: orbit,
        borderColor: orbitColor,
        duration: 300,
        easing: 'easeOutSine'
      });
    });

    // Actualizar las partículas con animación
    particles.forEach((particle: any) => {
      // Actualizar clase
      particle.className = `avatar-particle ${avatarStatus}`;

      // Animar la transición de las partículas
      anime({
        targets: particle,
        opacity: [0.5, 1],
        scale: [0.8, 1],
        duration: 300,
        easing: 'easeOutSine'
      });
    });
  }, [mood]);

  // Función para manejar la activación del agente
  const handleActivation = () => {
    if (!interactive) return;

    setIsActivated(!isActivated);
    setShowCommandInterface(!showCommandInterface);

    // Animación de activación más sutil
    anime({
      targets: avatarRef.current,
      scale: [1, 1.03, 1],
      opacity: [1, 0.9, 1],
      duration: 800,
      easing: 'easeInOutSine'
    });
  };

  // Función para enviar comandos
  const handleCommandSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (voiceCommand.trim() && onCommand) {
      onCommand(voiceCommand);
      setVoiceCommand('');

      // Animación de procesamiento de comando simplificada
      anime({
        targets: avatarRef.current?.querySelector('.avatar-core'),
        scale: [1, 1.05, 1],
        opacity: [1, 0.9, 1],
        duration: 800,
        easing: 'easeInOutSine'
      });
    }
  };

  // Determinar expresiones según el estado de ánimo o los datos de criptomonedas
  const getMoodStyles = () => {
    // Si hay datos de criptomonedas, ajustar el estado de ánimo según el cambio porcentual
    let currentMood = mood;

    if (cryptoData && cryptoData.changePercent24Hr !== undefined) {
      const changePercent = cryptoData.changePercent24Hr;

      // Determinar el estado de ánimo basado en el cambio porcentual
      if (changePercent > 5) {
        currentMood = 'bullish'; // Muy positivo
      } else if (changePercent > 2) {
        currentMood = 'happy'; // Positivo
      } else if (changePercent < -5) {
        currentMood = 'bearish'; // Muy negativo
      } else if (changePercent < -2) {
        currentMood = 'concerned'; // Negativo
      } else if (Math.abs(changePercent) > 1) {
        currentMood = 'analyzing'; // Cambio moderado
      } else {
        currentMood = 'neutral'; // Cambio mínimo
      }
    }

    // Si hay un sentimiento de mercado explícito, usarlo
    if (marketSentiment !== 'neutral') {
      switch (marketSentiment) {
        case 'bullish':
          currentMood = 'bullish';
          break;
        case 'bearish':
          currentMood = 'bearish';
          break;
        case 'volatile':
          currentMood = 'volatile';
          break;
      }
    }

    // Determinar expresiones según el estado de ánimo
    switch (currentMood) {
      case 'happy':
        return {
          eyeShape: 'happy-eyes',
          mouthShape: 'happy-mouth'
        };
      case 'thinking':
        return {
          eyeShape: 'thinking-eyes',
          mouthShape: 'thinking-mouth'
        };
      case 'excited':
        return {
          eyeShape: 'excited-eyes',
          mouthShape: 'excited-mouth'
        };
      case 'concerned':
        return {
          eyeShape: 'concerned-eyes',
          mouthShape: 'concerned-mouth'
        };
      case 'analyzing':
        return {
          eyeShape: 'analyzing-eyes',
          mouthShape: 'neutral-mouth'
        };
      case 'predicting':
        return {
          eyeShape: 'predicting-eyes',
          mouthShape: 'thinking-mouth'
        };
      case 'bullish':
        return {
          eyeShape: 'excited-eyes',
          mouthShape: 'happy-mouth'
        };
      case 'bearish':
        return {
          eyeShape: 'concerned-eyes',
          mouthShape: 'concerned-mouth'
        };
      case 'volatile':
        return {
          eyeShape: 'analyzing-eyes',
          mouthShape: 'excited-mouth'
        };
      default:
        return {
          eyeShape: 'neutral-eyes',
          mouthShape: 'neutral-mouth'
        };
    }
  };

  const moodStyles = getMoodStyles();

  // Determinar clases adicionales basadas en los datos de criptomonedas
  const getCryptoClasses = () => {
    if (!cryptoData || cryptoData.changePercent24Hr === undefined) return '';

    const changePercent = cryptoData.changePercent24Hr;

    if (changePercent > 5) return 'crypto-very-positive';
    if (changePercent > 2) return 'crypto-positive';
    if (changePercent < -5) return 'crypto-very-negative';
    if (changePercent < -2) return 'crypto-negative';
    return 'crypto-neutral';
  };

  const cryptoClasses = getCryptoClasses();
  const marketClasses = marketSentiment !== 'neutral' ? `market-${marketSentiment}` : '';
  const intensityClasses = priceChangeIntensity !== 'low' ? `intensity-${priceChangeIntensity}` : '';

  // Determinar la clase de estado del avatar basada en el estado de ánimo
  const getAvatarStatusClass = () => {
    if (mood === 'bullish' || mood === 'happy' || mood === 'excited') return 'avatar--positive';
    if (mood === 'bearish' || mood === 'concerned') return 'avatar--negative';
    if (mood === 'analyzing' || mood === 'predicting' || mood === 'thinking') return 'avatar--neutral';
    if (mood === 'volatile') return 'avatar--concerned';
    return 'avatar--neutral';
  };

  const avatarStatusClass = getAvatarStatusClass();

  return (
    <div
      className={`cripto-agent-avatar ${size} ${speaking ? 'speaking' : ''} ${pulseEffect ? 'pulse' : ''} ${powerMode !== 'normal' ? `power-${powerMode}` : ''} ${isActivated ? 'activated' : ''} ${cryptoClasses} ${marketClasses} ${intensityClasses} ${avatarStatusClass}`}
      onMouseEnter={() => interactive && setIsHovered(true)}
      onMouseLeave={() => interactive && setIsHovered(false)}
      onClick={handleActivation}
    >
      <div className="avatar-particles" ref={particlesRef}></div>

      {powerMode !== 'normal' && (
        <div className="power-aura"></div>
      )}

      <div className="avatar-container" ref={avatarRef}>
        <div className="avatar-core"></div>
        <div className="avatar-ring ring1"></div>
        <div className="avatar-ring ring2"></div>
        <div className="avatar-ring ring3"></div>

        <div className="avatar-face">
          <div className={`avatar-eyes ${moodStyles.eyeShape}`} ref={eyesRef}>
            <div className="eye left"></div>
            <div className="eye right"></div>
          </div>
          <div className={`avatar-mouth ${moodStyles.mouthShape} ${speaking ? 'speaking' : ''}`}></div>
        </div>
      </div>

      {speaking && (
        <div className="speech-indicator">
          <div className="speech-bar"></div>
          <div className="speech-bar"></div>
          <div className="speech-bar"></div>
          <div className="speech-bar"></div>
        </div>
      )}

      {interactive && isHovered && !showCommandInterface && (
        <div className="agent-tooltip">
          <span>Haz clic para activar CriptoAgente</span>
        </div>
      )}

      {showCommandInterface && (
        <div className="command-interface">
          <form onSubmit={handleCommandSubmit}>
            <input
              type="text"
              value={voiceCommand}
              onChange={(e) => setVoiceCommand(e.target.value)}
              placeholder="Escribe un comando..."
              autoFocus
            />
            <button type="submit">
              <span className="command-icon">⚡</span>
            </button>
          </form>
          <div className="command-suggestions">
            <span onClick={() => setVoiceCommand('Analizar Bitcoin')}>Analizar Bitcoin</span>
            <span onClick={() => setVoiceCommand('Predecir tendencias')}>Predecir tendencias</span>
            <span onClick={() => setVoiceCommand('Mostrar alertas')}>Mostrar alertas</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CriptoAgentAvatar;
