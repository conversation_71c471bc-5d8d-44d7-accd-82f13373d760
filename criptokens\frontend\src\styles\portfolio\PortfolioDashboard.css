/* Estilos para el dashboard de portafolio */

.portfolio-dashboard {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  color: var(--text-medium);
}

.portfolio-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: var(--border-light);
}

.portfolio-dashboard-title {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.portfolio-dashboard-title h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-bright);
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.portfolio-funds-display {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.funds-label {
  color: var(--text-dim);
  font-size: 0.9rem;
}

.funds-value {
  font-weight: 600;
  color: var(--text-bright);
}

.portfolio-dashboard-actions {
  display: flex;
  gap: 1rem;
}

.refresh-button,
.add-asset-button,
.add-funds-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  transition: all var(--transition-normal);
  border: none;
  cursor: pointer;
}

.refresh-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.refresh-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-asset-button {
  background: var(--gradient-primary);
  color: var(--text-bright);
  box-shadow: var(--shadow-sm);
}

.add-asset-button:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-2px);
}

.add-funds-button {
  background: var(--gradient-secondary);
  color: var(--text-bright);
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

.add-funds-button:hover {
  box-shadow: 0 0 10px var(--secondary-glow);
}

.reset-portfolio-button {
  background: linear-gradient(135deg, #ff5252, #b33939);
  color: var(--text-bright);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.reset-portfolio-button:hover {
  box-shadow: 0 0 15px rgba(255, 82, 82, 0.5);
  transform: translateY(-2px);
}

/* Tabs de navegación */
.portfolio-dashboard-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.tab-button {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  color: var(--text-dim);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.tab-button:hover {
  color: var(--text-bright);
  background-color: rgba(255, 255, 255, 0.05);
}

.tab-button.active {
  color: var(--text-bright);
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom: 3px solid var(--primary);
}

/* Contenido del dashboard */
.portfolio-dashboard-content {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: var(--border-light);
}

/* Pestaña de resumen */
.portfolio-overview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.portfolio-charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.portfolio-top-assets {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.portfolio-top-assets h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.view-all-button {
  align-self: center;
  padding: 0.75rem 1.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-top: 1rem;
}

.view-all-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

/* Pestaña de activos */
.portfolio-assets-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.portfolio-assets-tab h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

/* Pestaña de transacciones */
.portfolio-transactions-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.portfolio-transactions-tab h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

/* Pestaña de análisis */
.portfolio-analysis-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.portfolio-analysis-tab h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.portfolio-analysis-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.portfolio-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metric-card h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text-dim);
}

.metric-card p {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.metric-card p.positive {
  color: var(--success);
}

.metric-card p.negative {
  color: var(--error);
}

.metric-card .percentage {
  font-size: 0.875rem;
  margin-left: 0.5rem;
  opacity: 0.8;
}

.portfolio-guru-analysis {
  background: var(--gradient-card-alt);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: var(--border-light);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  text-align: center;
}

.portfolio-guru-analysis h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-bright);
}

.portfolio-guru-analysis p {
  color: var(--text-medium);
  margin: 0;
}

.guru-analysis-button {
  padding: 0.75rem 1.5rem;
  background: var(--gradient-primary);
  color: var(--text-bright);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.guru-analysis-button:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-2px);
}

/* Mensaje de portafolio vacío */
.empty-portfolio-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.empty-portfolio-message p {
  margin: 0.5rem 0;
  color: var(--text-medium);
}

/* Mensaje de error */
.portfolio-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.portfolio-error h3 {
  color: var(--error);
  margin: 0 0 1rem 0;
}

.portfolio-error p {
  margin: 0 0 1.5rem 0;
  color: var(--text-medium);
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.retry-button,
.reload-button {
  padding: 0.75rem 1.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.retry-button:hover,
.reload-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.retry-button {
  background-color: rgba(0, 200, 83, 0.1);
  border-color: rgba(0, 200, 83, 0.2);
}

.retry-button:hover {
  background-color: rgba(0, 200, 83, 0.2);
}

.reload-button {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .portfolio-dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.25rem;
  }

  .portfolio-dashboard-title {
    width: 100%;
  }

  .portfolio-funds-display {
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .portfolio-dashboard-actions {
    width: 100%;
    justify-content: space-between;
  }

  .portfolio-dashboard-tabs {
    overflow-x: auto;
    padding-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }

  .portfolio-dashboard-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .tab-button {
    white-space: nowrap;
    padding: 0.75rem 1rem;
  }

  .chart-container {
    min-height: 250px;
  }

  .error-actions {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
}
