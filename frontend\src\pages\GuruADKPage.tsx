import React from 'react';
import { <PERSON> } from 'react-router-dom';
import Guru<PERSON><PERSON><PERSON>nterface from '../components/GuruADK/GuruADKInterface';
import '../components/GuruADK/GuruADKInterface.css';

const GuruADKPage: React.FC = () => {
  return (
    <div className="standalone-page">
      <header className="standalone-header">
        <Link to="/" className="back-link">
          ← Volver al Dashboard
        </Link>
        <h1><PERSON>to ADK</h1>
        <div className="header-subtitle">
          Powered by Google's Agent Development Kit
        </div>
      </header>
      <main className="standalone-content">
        <GuruADKInterface />
      </main>
    </div>
  );
};

export default GuruADKPage;
