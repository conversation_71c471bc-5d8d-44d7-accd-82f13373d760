/**
 * Cliente para comunicarse con el servidor Context7 MCP
 */
const axios = require('axios');
require('dotenv').config();

// URL base del servidor Context7 MCP (puerto dinámico, se detectará automáticamente)
const CONTEXT7_MCP_URL = process.env.CONTEXT7_MCP_URL || 'http://localhost:7777';

/**
 * Llama a una herramienta específica en el servidor Context7 MCP
 * @param {string} toolName - Nombre de la herramienta a llamar
 * @param {Object} params - Parámetros de entrada para la herramienta
 * @returns {Promise<any>} - Resultado de la ejecución de la herramienta
 */
async function callContext7Tool(toolName, params) {
  try {
    console.log(`Llamando a la herramienta ${toolName} en el servidor Context7 MCP con params:`, params);
    
    // Crear una sesión temporal
    const sessionResponse = await axios.post(`${CONTEXT7_MCP_URL}/mcp`, {
      jsonrpc: '2.0',
      method: 'session.create',
      params: {},
      id: 1
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Mcp-Session-Id': 'temp-session-id'
      }
    });
    
    if (!sessionResponse.data || sessionResponse.data.error) {
      console.error('Error al crear sesión Context7 MCP:', sessionResponse.data?.error || 'Respuesta vacía');
      throw new Error('Error al crear sesión Context7 MCP');
    }
    
    const sessionId = sessionResponse.data.result.sessionId;
    console.log(`Sesión Context7 MCP creada con ID: ${sessionId}`);
    
    // Llamar a la herramienta
    const toolResponse = await axios.post(`${CONTEXT7_MCP_URL}/mcp`, {
      jsonrpc: '2.0',
      method: 'execute',
      params: {
        tool: toolName,
        input: params
      },
      id: 2
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Mcp-Session-Id': sessionId
      }
    });
    
    if (!toolResponse.data || toolResponse.data.error) {
      console.error('Error al ejecutar herramienta Context7 MCP:', toolResponse.data?.error || 'Respuesta vacía');
      throw new Error('Error al ejecutar herramienta Context7 MCP');
    }
    
    return toolResponse.data.result;
  } catch (error) {
    console.error(`Error al llamar a la herramienta ${toolName}:`, error);
    throw error;
  }
}

/**
 * Resuelve el ID de una biblioteca para Context7
 * @param {string} libraryName - Nombre de la biblioteca
 * @returns {Promise<string>} - ID de la biblioteca
 */
async function resolveLibraryId(libraryName) {
  try {
    return await callContext7Tool('resolve-library-id', { name: libraryName });
  } catch (error) {
    console.error('Error al resolver ID de biblioteca:', error.message);
    throw error;
  }
}

/**
 * Obtiene documentación actualizada para una biblioteca específica
 * @param {string} libraryName - Nombre de la biblioteca
 * @param {string} query - Consulta específica (opcional)
 * @returns {Promise<Object>} - Documentación de la biblioteca
 */
async function getContext7Documentation(libraryName, query = null) {
  try {
    console.log(`Obteniendo documentación para ${libraryName}${query ? ` con consulta: ${query}` : ''}`);
    
    // Primero, resolver el ID de la biblioteca
    const libraryId = await resolveLibraryId(libraryName);
    console.log(`ID de biblioteca resuelto: ${libraryId}`);
    
    // Parámetros para la herramienta get-library-docs
    const params = {
      libraryId: libraryId,
      maxTokens: 4000 // Limitar el tamaño de la respuesta
    };
    
    // Añadir la consulta si se proporciona
    if (query) {
      params.topic = query;
    }
    
    // Obtener la documentación
    const documentation = await callContext7Tool('get-library-docs', params);
    
    return {
      libraryName,
      libraryId,
      query: query || null,
      documentation
    };
  } catch (error) {
    console.error('Error al obtener documentación de Context7:', error.message);
    // Devolver un objeto con información de error
    return {
      libraryName,
      error: error.message,
      documentation: `No se pudo obtener documentación para ${libraryName}. Error: ${error.message}`
    };
  }
}

module.exports = {
  callContext7Tool,
  resolveLibraryId,
  getContext7Documentation
};
