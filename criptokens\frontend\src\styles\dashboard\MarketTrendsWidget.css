.market-trends-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.market-trends-widget:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.trends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.trends-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.trends-content {
  padding: 0.75rem;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.trends-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-title {
  margin: 0;
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed var(--border-color);
}

.section-title i {
  font-size: 0.85rem;
}

.section-title i.fa-arrow-trend-up {
  color: var(--color-positive);
}

.section-title i.fa-arrow-trend-down {
  color: var(--color-negative);
}

.section-title i.fa-fire {
  color: #ff9800;
}

.trends-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: var(--border-radius-md);
  transition: all 0.2s ease;
  background-color: var(--color-surface-light);
  border: 1px solid transparent;
}

.trend-item:hover {
  background-color: var(--color-surface-dark);
  border-color: var(--border-color-hover);
  transform: translateY(-2px);
}

.trend-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-xs);
}

.trend-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.trend-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-name {
  display: flex;
  flex-direction: column;
}

.trend-name .name {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.trend-name .symbol {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.trend-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.trend-stats .change {
  font-size: 0.85rem;
  font-weight: var(--font-weight-semibold);
}

.trend-stats .change.positive {
  color: var(--color-positive);
}

.trend-stats .change.negative {
  color: var(--color-negative);
}

.trend-stats .volume {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

/* Esqueleto de carga */
.market-trends-widget.loading .skeleton-loading {
  width: 100%;
  height: 200px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .trends-content {
    padding: 0.5rem;
  }
  
  .trend-item {
    padding: 0.375rem;
  }
  
  .trend-icon {
    width: 28px;
    height: 28px;
  }
  
  .trend-name .name {
    font-size: 0.85rem;
  }
  
  .trend-stats .change {
    font-size: 0.8rem;
  }
}
