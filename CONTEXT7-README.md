# Context7 MCP para Criptokens

Context7 MCP es un servidor que proporciona documentación actualizada para bibliotecas y frameworks, mejorando las capacidades del Gurú Cripto al generar código más preciso y actualizado.

## ¿Qué es Context7?

Context7 es un servicio que proporciona documentación actualizada y específica de versión para bibliotecas y frameworks populares. Cuando se utiliza con el Gurú Cripto, permite:

- Generar código más preciso y actualizado
- Obtener ejemplos de código basados en la última versión de las bibliotecas
- Evitar errores comunes debido a documentación obsoleta
- Mejorar la calidad de las respuestas relacionadas con desarrollo

## Instalación y Uso

### Opción 1: Usar los scripts proporcionados

Dependiendo de tu sistema operativo, puedes usar uno de los siguientes scripts:

- **Windows (CMD)**: Ejecuta `start-context7-mcp.bat`
- **Windows (PowerShell)**: Ejecuta `start-context7-mcp.ps1`
- **Unix/Linux/Mac**: Ejecuta `./start-context7-mcp.sh` (asegúrate de darle permisos de ejecución con `chmod +x start-context7-mcp.sh`)
- **Node.js**: Ejecuta `node start-context7-mcp.js`

### Opción 2: Usar NPX directamente

```bash
npx -y @upstash/context7-mcp@latest
```

## Cómo usar Context7 con el Gurú Cripto

Para utilizar Context7 en tus consultas al Gurú, simplemente añade `use context7` al final de tu prompt:

```
Crea un componente React que muestre un gráfico de precios de Bitcoin usando Chart.js. use context7
```

Context7 detectará automáticamente las bibliotecas mencionadas en tu consulta y proporcionará documentación actualizada al LLM, lo que resultará en código más preciso y actualizado.

## Solución de problemas

Si Context7 no funciona correctamente, prueba las siguientes soluciones:

1. **Usar Bun en lugar de NPX**:
   ```bash
   bunx -y @upstash/context7-mcp@latest
   ```

2. **Usar Deno en lugar de NPX**:
   ```bash
   deno run --allow-net npm:@upstash/context7-mcp
   ```

3. **Verificar la versión de Node.js**: Context7 requiere Node.js v18.0.0 o superior.

## Integración con el Orquestador de MCP

Si estás utilizando el orquestador de MCP para iniciar todos los servidores, Context7 ya está configurado en `mcp-config.json` y se iniciará automáticamente junto con los demás servidores MCP.

## Notas adicionales

- Context7 MCP utiliza un puerto dinámico, por lo que no es necesario configurar un puerto específico.
- El servicio `context7.service.js` en el backend ya está configurado para comunicarse con Context7 MCP.
- El Gurú Cripto ya tiene integrada la herramienta `getContext7Docs` para utilizar Context7 en sus respuestas.

## Recursos adicionales

- [Repositorio de Context7 en GitHub](https://github.com/upstash/context7)
- [Documentación oficial de Context7](https://upstash.com/blog/context7-mcp)
