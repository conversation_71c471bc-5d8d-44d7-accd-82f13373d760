/**
 * Servicio para obtener datos de mercado de criptomonedas
 * Utiliza fetch-mcp para obtener datos reales de diferentes fuentes
 */
const { fetchJson } = require('./fetch-mcp.service');
const cache = require('../utils/cache');

// Tiempo de caché en milisegundos (5 minutos)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * Obtiene el índice de miedo y codicia (Fear & Greed Index)
 * @returns {Promise<Object>} - Datos del índice de miedo y codicia
 */
async function getFearGreedIndex() {
  try {
    // Verificar caché
    const cacheKey = 'fear-greed-index';
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Usando datos en caché para el índice de miedo y codicia');
      return cachedData;
    }

    console.log('Obteniendo índice de miedo y codicia...');
    const data = await fetchJson('https://api.alternative.me/fng/');
    
    if (!data || !data.data || !data.data[0]) {
      throw new Error('Formato de respuesta inesperado');
    }
    
    const result = {
      value: parseInt(data.data[0].value),
      valueClassification: data.data[0].value_classification,
      timestamp: data.data[0].timestamp,
      timeUntilUpdate: data.data[0].time_until_update
    };
    
    // Guardar en caché
    cache.set(cacheKey, result, CACHE_DURATION);
    
    return result;
  } catch (error) {
    console.error('Error al obtener índice de miedo y codicia:', error.message);
    throw error;
  }
}

/**
 * Obtiene la dominancia de Bitcoin en el mercado
 * @returns {Promise<number>} - Porcentaje de dominancia de Bitcoin
 */
async function getBtcDominance() {
  try {
    // Verificar caché
    const cacheKey = 'btc-dominance';
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Usando datos en caché para la dominancia de Bitcoin');
      return cachedData;
    }

    console.log('Obteniendo dominancia de Bitcoin...');
    const data = await fetchJson('https://api.coingecko.com/api/v3/global');
    
    if (!data || !data.data || !data.data.market_cap_percentage || !data.data.market_cap_percentage.btc) {
      throw new Error('Formato de respuesta inesperado');
    }
    
    const btcDominance = data.data.market_cap_percentage.btc;
    
    // Guardar en caché
    cache.set(cacheKey, btcDominance, CACHE_DURATION);
    
    return btcDominance;
  } catch (error) {
    console.error('Error al obtener dominancia de Bitcoin:', error.message);
    throw error;
  }
}

/**
 * Obtiene la capitalización total del mercado de criptomonedas
 * @returns {Promise<number>} - Capitalización total del mercado en USD
 */
async function getTotalMarketCap() {
  try {
    // Verificar caché
    const cacheKey = 'total-market-cap';
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Usando datos en caché para la capitalización total del mercado');
      return cachedData;
    }

    console.log('Obteniendo capitalización total del mercado...');
    const data = await fetchJson('https://api.coingecko.com/api/v3/global');
    
    if (!data || !data.data || !data.data.total_market_cap || !data.data.total_market_cap.usd) {
      throw new Error('Formato de respuesta inesperado');
    }
    
    const totalMarketCap = data.data.total_market_cap.usd;
    
    // Guardar en caché
    cache.set(cacheKey, totalMarketCap, CACHE_DURATION);
    
    return totalMarketCap;
  } catch (error) {
    console.error('Error al obtener capitalización total del mercado:', error.message);
    throw error;
  }
}

/**
 * Obtiene datos completos del mercado
 * @returns {Promise<Object>} - Datos del mercado
 */
async function getMarketData() {
  try {
    // Verificar caché
    const cacheKey = 'market-data';
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Usando datos en caché para los datos del mercado');
      return cachedData;
    }

    console.log('Obteniendo datos del mercado...');
    
    // Obtener datos en paralelo
    const [fearGreedIndex, btcDominance, totalMarketCap] = await Promise.all([
      getFearGreedIndex().catch(error => {
        console.error('Error al obtener índice de miedo y codicia:', error.message);
        return { value: 50, valueClassification: 'Neutral' };
      }),
      getBtcDominance().catch(error => {
        console.error('Error al obtener dominancia de Bitcoin:', error.message);
        return 40;
      }),
      getTotalMarketCap().catch(error => {
        console.error('Error al obtener capitalización total del mercado:', error.message);
        return 1500000000000;
      })
    ]);
    
    const result = {
      fearGreedIndex: typeof fearGreedIndex === 'object' ? fearGreedIndex.value : fearGreedIndex,
      fearGreedClassification: typeof fearGreedIndex === 'object' ? fearGreedIndex.valueClassification : 'Neutral',
      btcDominance,
      totalMarketCap,
      totalMarketCapInTrillions: totalMarketCap / 1000000000000,
      timestamp: new Date().toISOString()
    };
    
    // Guardar en caché
    cache.set(cacheKey, result, CACHE_DURATION);
    
    return result;
  } catch (error) {
    console.error('Error al obtener datos del mercado:', error.message);
    throw error;
  }
}

module.exports = {
  getFearGreedIndex,
  getBtcDominance,
  getTotalMarketCap,
  getMarketData
};
