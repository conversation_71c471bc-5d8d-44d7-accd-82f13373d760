{"name": "mcp-orchestrator", "version": "0.0.1", "description": "MCP Orchestrator for Criptokens", "type": "commonjs", "bin": {"mcp-orchestrator": "./dist/server.js"}, "files": ["dist"], "scripts": {"build": "tsc", "watch": "tsc --watch", "start": "node dist/server.js"}, "dependencies": {"axios": "^1.8.4", "dotenv": "^16.5.0", "mcp-framework": "^0.2.11", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}, "engines": {"node": ">=18.19.0"}}