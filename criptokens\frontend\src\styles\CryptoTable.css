.crypto-table-container {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.crypto-table-container h2 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.crypto-table-wrapper {
  overflow-x: auto;
}

.crypto-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9375rem;
}

.crypto-table th {
  text-align: left;
  padding: 0.75rem 1rem;
  border-bottom: 2px solid #f0f0f0;
  color: #666;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.crypto-table th:hover {
  background-color: #f8f9fa;
}

.crypto-table td {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}

.crypto-table tbody tr:hover {
  background-color: #f8f9fa;
}

.crypto-name-cell {
  min-width: 180px;
}

.crypto-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.crypto-name > div {
  display: flex;
  flex-direction: column;
}

.crypto-icon {
  width: 16px !important;
  height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  border-radius: 50%;
  vertical-align: middle !important;
}

.crypto-fullname {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.crypto-symbol {
  color: #666;
  font-size: 0.8125rem;
}

.positive {
  color: #00C853;
}

.negative {
  color: #FF3D00;
}

.sparkline-cell {
  width: 100px;
}

.sparkline {
  width: 100%;
  height: 30px;
}

.crypto-table.loading,
.crypto-table-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.crypto-table-error {
  color: #d32f2f;
  background-color: #ffebee;
  border-radius: 8px;
}

.crypto-table-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.crypto-table-row:hover {
  background-color: #f0f7ff;
}

@media (max-width: 768px) {
  .crypto-table th,
  .crypto-table td {
    padding: 0.75rem 0.5rem;
  }

  .crypto-table th:nth-child(5),
  .crypto-table td:nth-child(5),
  .crypto-table th:nth-child(7),
  .crypto-table td:nth-child(7) {
    display: none;
  }
}
