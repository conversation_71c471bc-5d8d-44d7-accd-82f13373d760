import React, { useState, useEffect } from 'react';
import { getTopCryptocurrencies, getGlobalMarketData } from '../services/api';
import '../styles/SimpleDashboard.css';

const SimpleDashboard: React.FC = () => {
  const [cryptos, setCryptos] = useState<any[]>([]);
  const [marketData, setMarketData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // Obtener las principales criptomonedas
        const cryptoData = await getTopCryptocurrencies(10);
        setCryptos(cryptoData);

        // Obtener datos del mercado global
        const globalData = await getGlobalMarketData();
        setMarketData(globalData.data);
      } catch (error) {
        console.error('Error al cargar datos:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div style={{
      padding: '20px',
      background: '#0a0a1a',
      color: 'white',
      minHeight: '100vh',
      fontFamily: 'Inter, system-ui, sans-serif'
    }}>
      <h1 style={{
        color: '#00f2ff',
        fontSize: '28px',
        marginBottom: '20px',
        background: 'linear-gradient(90deg, #00f2ff, #4657ce)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent'
      }}>Criptokens - Dashboard</h1>

      {isLoading ? (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '40px',
          background: 'rgba(20, 20, 50, 0.5)',
          borderRadius: '12px',
          border: '1px solid rgba(64, 220, 255, 0.2)'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '3px solid rgba(64, 220, 255, 0.3)',
            borderRadius: '50%',
            borderTopColor: '#00f2ff',
            animation: 'spin 1s linear infinite',
            marginBottom: '15px'
          }}></div>
          <p>Cargando datos del mercado...</p>
        </div>
      ) : (
        <>
          {/* Resumen del mercado */}
          {marketData && (
            <div style={{
              marginBottom: '20px',
              padding: '20px',
              background: 'rgba(20, 20, 50, 0.5)',
              borderRadius: '12px',
              border: '1px solid rgba(64, 220, 255, 0.2)',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
              gap: '15px'
            }}>
              <div style={{
                background: 'rgba(10, 10, 26, 0.5)',
                borderRadius: '8px',
                padding: '15px',
                border: '1px solid rgba(64, 220, 255, 0.1)'
              }}>
                <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)', marginBottom: '8px' }}>Capitalización Total</div>
                <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                  ${(marketData.total_market_cap.usd / 1e12).toFixed(2)}T
                </div>
                <div style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  padding: '3px 8px',
                  borderRadius: '4px',
                  display: 'inline-block',
                  background: marketData.market_cap_change_percentage_24h_usd >= 0 ? 'rgba(0, 200, 83, 0.2)' : 'rgba(255, 82, 82, 0.2)',
                  color: marketData.market_cap_change_percentage_24h_usd >= 0 ? '#00c853' : '#ff5252'
                }}>
                  {marketData.market_cap_change_percentage_24h_usd >= 0 ? '▲' : '▼'} {Math.abs(marketData.market_cap_change_percentage_24h_usd).toFixed(2)}%
                </div>
              </div>

              <div style={{
                background: 'rgba(10, 10, 26, 0.5)',
                borderRadius: '8px',
                padding: '15px',
                border: '1px solid rgba(64, 220, 255, 0.1)'
              }}>
                <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)', marginBottom: '8px' }}>Dominancia BTC</div>
                <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                  {marketData.market_cap_percentage.btc.toFixed(1)}%
                </div>
                <div style={{ height: '6px', background: 'rgba(255, 255, 255, 0.1)', borderRadius: '3px', marginTop: '10px' }}>
                  <div style={{
                    height: '100%',
                    width: `${marketData.market_cap_percentage.btc}%`,
                    background: 'linear-gradient(90deg, #f7931a, #ffc107)',
                    borderRadius: '3px'
                  }}></div>
                </div>
              </div>

              <div style={{
                background: 'rgba(10, 10, 26, 0.5)',
                borderRadius: '8px',
                padding: '15px',
                border: '1px solid rgba(64, 220, 255, 0.1)'
              }}>
                <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)', marginBottom: '8px' }}>Dominancia ETH</div>
                <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                  {marketData.market_cap_percentage.eth.toFixed(1)}%
                </div>
                <div style={{ height: '6px', background: 'rgba(255, 255, 255, 0.1)', borderRadius: '3px', marginTop: '10px' }}>
                  <div style={{
                    height: '100%',
                    width: `${marketData.market_cap_percentage.eth}%`,
                    background: 'linear-gradient(90deg, #627eea, #3c5be0)',
                    borderRadius: '3px'
                  }}></div>
                </div>
              </div>
            </div>
          )}

          {/* Tabla de criptomonedas */}
          <div style={{
            marginTop: '20px',
            padding: '20px',
            background: 'rgba(20, 20, 50, 0.5)',
            borderRadius: '12px',
            border: '1px solid rgba(64, 220, 255, 0.2)'
          }}>
            <h2 style={{
              fontSize: '20px',
              marginTop: 0,
              marginBottom: '15px',
              color: 'white'
            }}>Principales Criptomonedas</h2>

            <div style={{ overflowX: 'auto' }}>
              <table style={{
                width: '100%',
                borderCollapse: 'collapse',
                color: 'white'
              }}>
                <thead>
                  <tr>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 15px',
                      borderBottom: '1px solid rgba(64, 220, 255, 0.1)',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: '14px'
                    }}>#</th>
                    <th style={{
                      textAlign: 'left',
                      padding: '12px 15px',
                      borderBottom: '1px solid rgba(64, 220, 255, 0.1)',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: '14px'
                    }}>Nombre</th>
                    <th style={{
                      textAlign: 'right',
                      padding: '12px 15px',
                      borderBottom: '1px solid rgba(64, 220, 255, 0.1)',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: '14px'
                    }}>Precio</th>
                    <th style={{
                      textAlign: 'right',
                      padding: '12px 15px',
                      borderBottom: '1px solid rgba(64, 220, 255, 0.1)',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: '14px'
                    }}>Cambio 24h</th>
                    <th style={{
                      textAlign: 'right',
                      padding: '12px 15px',
                      borderBottom: '1px solid rgba(64, 220, 255, 0.1)',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: '14px'
                    }}>Cap. Mercado</th>
                  </tr>
                </thead>
                <tbody>
                  {cryptos.map((crypto, index) => (
                    <tr key={crypto.id} style={{
                      transition: 'all 0.2s',
                      ':hover': { background: 'rgba(64, 220, 255, 0.05)' }
                    }}>
                      <td style={{
                        padding: '12px 15px',
                        borderBottom: '1px solid rgba(64, 220, 255, 0.05)',
                        fontSize: '14px'
                      }}>{index + 1}</td>
                      <td style={{
                        padding: '12px 15px',
                        borderBottom: '1px solid rgba(64, 220, 255, 0.05)',
                        fontSize: '14px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '10px'
                      }}>
                        <img
                          src={crypto.image}
                          alt={crypto.name}
                          style={{ width: '24px', height: '24px', borderRadius: '50%' }}
                        />
                        <div style={{ display: 'flex', flexDirection: 'column' }}>
                          <span style={{ fontWeight: '500' }}>{crypto.name}</span>
                          <span style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.6)' }}>{crypto.symbol.toUpperCase()}</span>
                        </div>
                      </td>
                      <td style={{
                        padding: '12px 15px',
                        borderBottom: '1px solid rgba(64, 220, 255, 0.05)',
                        fontSize: '14px',
                        textAlign: 'right'
                      }}>${crypto.current_price.toLocaleString()}</td>
                      <td style={{
                        padding: '12px 15px',
                        borderBottom: '1px solid rgba(64, 220, 255, 0.05)',
                        fontSize: '14px',
                        textAlign: 'right',
                        color: crypto.price_change_percentage_24h >= 0 ? '#00c853' : '#ff5252',
                        fontWeight: '500'
                      }}>
                        {crypto.price_change_percentage_24h >= 0 ? '▲' : '▼'} {Math.abs(crypto.price_change_percentage_24h).toFixed(2)}%
                      </td>
                      <td style={{
                        padding: '12px 15px',
                        borderBottom: '1px solid rgba(64, 220, 255, 0.05)',
                        fontSize: '14px',
                        textAlign: 'right'
                      }}>${(crypto.market_cap / 1e9).toFixed(2)}B</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SimpleDashboard;
