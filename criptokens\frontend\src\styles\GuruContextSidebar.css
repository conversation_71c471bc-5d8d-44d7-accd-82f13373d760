/* Estilos para la barra lateral contextual del Gurú */
.guru-context-sidebar {
  width: 320px;
  height: 100%;
  background-color: rgba(15, 17, 35, 0.7);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(123, 77, 255, 0.2);
  display: flex;
  flex-direction: column;
  color: #fff;
  transition: all 0.3s ease;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(123, 77, 255, 0.5) rgba(15, 17, 35, 0.3);
}

/* Estilos para la barra de desplazamiento */
.guru-context-sidebar::-webkit-scrollbar {
  width: 6px;
}

.guru-context-sidebar::-webkit-scrollbar-track {
  background: rgba(15, 17, 35, 0.3);
}

.guru-context-sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(123, 77, 255, 0.5);
  border-radius: 6px;
}

/* Cambiar el color del borde según el estado del avatar */
.guru-context-sidebar.positive {
  border-left-color: rgba(0, 255, 157, 0.3);
}

.guru-context-sidebar.negative {
  border-left-color: rgba(255, 58, 110, 0.3);
}

.guru-context-sidebar.concerned {
  border-left-color: rgba(255, 204, 0, 0.3);
}

.guru-context-sidebar.thinking {
  border-left-color: rgba(123, 77, 255, 0.3);
}

/* Encabezado de la barra lateral */
.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(123, 77, 255, 0.2);
}

.sidebar-header h2 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #fff;
}

.context-type {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Contenido de la barra lateral */
.sidebar-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Pie de la barra lateral */
.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(123, 77, 255, 0.2);
  text-align: center;
}

.dashboard-link {
  display: inline-flex;
  align-items: center;
  color: rgba(0, 242, 255, 0.9);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.dashboard-link i {
  margin-right: 8px;
}

.dashboard-link:hover {
  color: #fff;
  text-decoration: underline;
}

/* Estilos para los widgets contextuales */
.context-widget {
  background-color: rgba(21, 23, 42, 0.5);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.context-widget h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
}

.context-widget h4 {
  margin: 15px 0 10px;
  font-size: 0.95rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

/* Widget de sentimiento del mercado */
.market-sentiment-widget .fear-greed-meter {
  margin-bottom: 20px;
}

.fear-greed-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 5px;
}

.fear-greed-value {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin: 10px 0;
}

/* Colores para el índice de miedo/codicia */
.fear-greed-value[data-sentiment="extreme-fear"] {
  color: #ff3a6e;
}

.fear-greed-value[data-sentiment="fear"] {
  color: #ff6b3d;
}

.fear-greed-value[data-sentiment="neutral"] {
  color: #ffcc00;
}

.fear-greed-value[data-sentiment="greed"] {
  color: #00f2ff;
}

.fear-greed-value[data-sentiment="extreme-greed"] {
  color: #00ff9d;
}

.fear-greed-scale {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
}

.fear-greed-scale .fear {
  color: #ff3a6e;
  font-size: 0.8rem;
}

.fear-greed-scale .greed {
  color: #00ff9d;
  font-size: 0.8rem;
}

.scale-bar {
  flex: 1;
  height: 6px;
  background: linear-gradient(to right, #ff3a6e, #ffcc00, #00ff9d);
  border-radius: 3px;
  margin: 0 10px;
  position: relative;
}

.scale-indicator {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: #fff;
  border-radius: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.market-dominance, .market-trend {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background-color: rgba(15, 17, 35, 0.3);
  border-radius: 8px;
}

.dominance-label, .trend-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.dominance-value {
  font-weight: 600;
  color: #00f2ff;
}

.trend-value {
  font-weight: 600;
}

.trend-value.bullish {
  color: #00ff9d;
}

.trend-value.bearish {
  color: #ff3a6e;
}

.trend-value.neutral {
  color: #ffcc00;
}

.top-movers {
  margin-top: 20px;
}

.movers-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mover-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(15, 17, 35, 0.3);
  border-radius: 8px;
}

.crypto-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.crypto-name {
  flex: 1;
  font-weight: 500;
}

.crypto-change {
  font-weight: 600;
}

.crypto-change.positive {
  color: #00ff9d;
}

.crypto-change.negative {
  color: #ff3a6e;
}

/* Widget de contexto de criptomoneda */
.coin-context-widget.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* Indicador de datos simulados */
.mock-data-badge {
  background-color: rgba(255, 165, 0, 0.2);
  color: #ffa500;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: inline-block;
  border: 1px solid rgba(255, 165, 0, 0.3);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(123, 77, 255, 0.3);
  border-radius: 50%;
  border-top-color: #7b4dff;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.coin-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.coin-header .coin-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.coin-header h3 {
  margin: 0;
}

.coin-price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
}

.coin-price {
  font-size: 1.8rem;
  font-weight: 700;
  margin-right: 10px;
}

.coin-change {
  font-size: 1rem;
  font-weight: 600;
}

.coin-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  flex: 1;
  background-color: rgba(15, 17, 35, 0.3);
  padding: 10px;
  border-radius: 8px;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 0.95rem;
  font-weight: 600;
  color: #fff;
}

.coin-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 8px;
  border: none;
  background-color: rgba(123, 77, 255, 0.2);
  color: #fff;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button i {
  margin-right: 8px;
}

.action-button:hover {
  background-color: rgba(123, 77, 255, 0.4);
}

.chart-button {
  background-color: rgba(0, 242, 255, 0.2);
}

.chart-button:hover {
  background-color: rgba(0, 242, 255, 0.4);
}

.news-button {
  background-color: rgba(255, 204, 0, 0.2);
}

.news-button:hover {
  background-color: rgba(255, 204, 0, 0.4);
}

.watchlist-button {
  background-color: rgba(0, 255, 157, 0.2);
}

.watchlist-button:hover {
  background-color: rgba(0, 255, 157, 0.4);
}

/* Widget de resumen de noticias */
.news-summary-widget {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.news-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 15px;
}

.news-item {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
  border-left: 3px solid #7b4dff;
  transition: all 0.3s ease;
}

.news-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.news-item.positive {
  border-left-color: #00ff9d;
}

.news-item.negative {
  border-left-color: #ff3a6e;
}

.news-title {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 14px;
}

.news-source {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.news-sentiment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px;
  background-color: rgba(15, 17, 35, 0.3);
  border-radius: 8px;
}

.sentiment-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.sentiment-value {
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
}

.sentiment-value.positive {
  background-color: rgba(0, 255, 157, 0.2);
  color: #00ff9d;
}

.sentiment-value.negative {
  background-color: rgba(255, 58, 110, 0.2);
  color: #ff3a6e;
}

.sentiment-value.neutral {
  background-color: rgba(255, 204, 0, 0.2);
  color: #ffcc00;
}

.sources-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sources-list li {
  margin-bottom: 8px;
}

.sources-list a {
  color: #00f2ff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.sources-list a:hover {
  color: #fff;
  text-decoration: underline;
}

.topics-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.topic-tag {
  background-color: rgba(123, 77, 255, 0.2);
  color: #fff;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.topic-tag:hover {
  background-color: rgba(123, 77, 255, 0.4);
  cursor: pointer;
}

/* Responsive */
@media (max-width: 1200px) {
  .guru-context-sidebar {
    width: 280px;
  }
}

@media (max-width: 992px) {
  .guru-context-sidebar {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .guru-context-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    height: 100%;
    transform: translateX(100%);
    z-index: 1000;
    width: 300px;
  }

  .guru-context-sidebar.visible {
    transform: translateX(0);
  }
}
