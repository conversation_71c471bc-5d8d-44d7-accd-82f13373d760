const { db, admin } = require('../config/firebase');

// Almacenamiento en memoria para desarrollo
const inMemoryDb = {
  conversations: {}
};

// Inicializar la base de datos en memoria con algunos datos de ejemplo
function initializeInMemoryDb() {
  // Verificar si ya hay datos
  if (Object.keys(inMemoryDb.conversations).length > 0) {
    return;
  }

  console.log('Inicializando base de datos en memoria...');

  // Crear algunos usuarios de ejemplo
  const exampleUsers = [
    'GnIWOtEAF3RdjYjGmH2T8keB2fJ3', // ID de usuario de ejemplo
    'user123',
    'user456'
  ];

  // Crear conversaciones para cada usuario
  exampleUsers.forEach(userId => {
    inMemoryDb.conversations[userId] = [];

    // Crear una conversación de ejemplo
    const now = new Date().toISOString();
    const conversationId = `conv-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    inMemoryDb.conversations[userId].push({
      id: conversationId,
      title: 'Conversación de ejemplo',
      createdAt: now,
      updatedAt: now,
      messages: []
    });

    console.log(`Creada conversación de ejemplo para usuario ${userId}: ${conversationId}`);
  });

  // Agregar una función auxiliar para asegurar que un usuario siempre exista en la base de datos
  inMemoryDb.ensureUserExists = (userId) => {
    if (!userId) return;

    if (!inMemoryDb.conversations[userId]) {
      inMemoryDb.conversations[userId] = [];
      console.log(`Inicializado nuevo usuario en la base de datos: ${userId}`);

      // Crear una conversación inicial para el nuevo usuario
      const now = new Date().toISOString();
      const conversationId = `conv-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      inMemoryDb.conversations[userId].push({
        id: conversationId,
        title: 'Nueva conversación',
        createdAt: now,
        updatedAt: now,
        messages: []
      });
    }

    return inMemoryDb.conversations[userId];
  };
}

/**
 * Obtiene todas las conversaciones de un usuario
 * @param {string} userId - ID del usuario
 * @returns {Promise<Array>} - Lista de conversaciones
 */
async function getUserConversations(userId) {
  try {
    if (!userId) {
      throw new Error('Se requiere el ID del usuario');
    }

    // Asegurar que el usuario exista en la base de datos
    inMemoryDb.ensureUserExists(userId);

    return inMemoryDb.conversations[userId];
  } catch (error) {
    console.error('Error al obtener conversaciones del usuario:', error);
    throw error;
  }
}

/**
 * Obtiene una conversación específica con sus mensajes
 * @param {string} userId - ID del usuario
 * @param {string} conversationId - ID de la conversación
 * @returns {Promise<Object>} - Conversación con sus mensajes
 */
async function getConversation(userId, conversationId) {
  try {
    if (!userId || !conversationId) {
      throw new Error('Se requieren los IDs de usuario y conversación');
    }

    // Asegurar que el usuario exista en la base de datos
    inMemoryDb.ensureUserExists(userId);

    const conversation = inMemoryDb.conversations[userId].find(conv => conv.id === conversationId);

    if (!conversation) {
      // Si la conversación no existe, crear una nueva
      console.log(`Conversación ${conversationId} no encontrada para usuario ${userId}, creando una nueva`);
      const now = new Date().toISOString();
      const newConversation = {
        id: conversationId,
        title: 'Nueva conversación',
        createdAt: now,
        updatedAt: now,
        messages: []
      };

      inMemoryDb.conversations[userId].push(newConversation);
      return newConversation;
    }

    return conversation;
  } catch (error) {
    console.error('Error al obtener conversación:', error);
    throw error;
  }
}

/**
 * Crea una nueva conversación
 * @param {string} userId - ID del usuario
 * @param {string} title - Título de la conversación
 * @returns {Promise<Object>} - Nueva conversación creada
 */
async function createConversation(userId, title = 'Nueva conversación') {
  try {
    if (!userId) {
      throw new Error('Se requiere el ID del usuario');
    }

    // Asegurar que el usuario exista en la base de datos
    inMemoryDb.ensureUserExists(userId);

    const now = new Date().toISOString();
    const newConversation = {
      id: `conv-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      title,
      createdAt: now,
      updatedAt: now,
      messages: []
    };

    inMemoryDb.conversations[userId].unshift(newConversation);
    console.log(`Nueva conversación creada para usuario ${userId}: ${newConversation.id}`);

    return newConversation;
  } catch (error) {
    console.error('Error al crear conversación:', error);
    throw error;
  }
}

/**
 * Actualiza el título de una conversación
 * @param {string} userId - ID del usuario
 * @param {string} conversationId - ID de la conversación
 * @param {string} title - Nuevo título
 * @returns {Promise<void>}
 */
async function updateConversationTitle(userId, conversationId, title) {
  try {
    if (!userId || !conversationId || !title) {
      throw new Error('Se requieren los IDs de usuario y conversación, y el nuevo título');
    }

    // Usar almacenamiento en memoria para desarrollo
    if (!inMemoryDb.conversations[userId]) {
      throw new Error('Usuario no encontrado');
    }

    const conversationIndex = inMemoryDb.conversations[userId].findIndex(conv => conv.id === conversationId);

    if (conversationIndex === -1) {
      throw new Error('Conversación no encontrada');
    }

    inMemoryDb.conversations[userId][conversationIndex].title = title;
    inMemoryDb.conversations[userId][conversationIndex].updatedAt = new Date().toISOString();
  } catch (error) {
    console.error('Error al actualizar título de conversación:', error);
    throw error;
  }
}

/**
 * Elimina una conversación
 * @param {string} userId - ID del usuario
 * @param {string} conversationId - ID de la conversación
 * @returns {Promise<void>}
 */
async function deleteConversation(userId, conversationId) {
  try {
    if (!userId || !conversationId) {
      throw new Error('Se requieren los IDs de usuario y conversación');
    }

    // Usar almacenamiento en memoria para desarrollo
    if (!inMemoryDb.conversations[userId]) {
      throw new Error('Usuario no encontrado');
    }

    const conversationIndex = inMemoryDb.conversations[userId].findIndex(conv => conv.id === conversationId);

    if (conversationIndex === -1) {
      throw new Error('Conversación no encontrada');
    }

    // Eliminar la conversación
    inMemoryDb.conversations[userId].splice(conversationIndex, 1);
  } catch (error) {
    console.error('Error al eliminar conversación:', error);
    throw error;
  }
}

/**
 * Añade un mensaje a una conversación
 * @param {string} userId - ID del usuario
 * @param {string} conversationId - ID de la conversación
 * @param {Object} message - Mensaje a añadir
 * @param {string} message.role - Rol del mensaje ('user' o 'assistant')
 * @param {string} message.content - Contenido del mensaje
 * @returns {Promise<Object>} - Mensaje añadido
 */
async function addMessage(userId, conversationId, message) {
  try {
    if (!userId || !conversationId || !message || !message.role || !message.content) {
      throw new Error('Faltan parámetros requeridos');
    }

    // Usar almacenamiento en memoria para desarrollo
    if (!inMemoryDb.conversations[userId]) {
      // Si el usuario no existe, crear una entrada para él
      inMemoryDb.conversations[userId] = [];

      // Crear una nueva conversación con el ID proporcionado
      const now = new Date().toISOString();
      const newConversation = {
        id: conversationId,
        title: 'Nueva conversación',
        createdAt: now,
        updatedAt: now,
        messages: []
      };

      inMemoryDb.conversations[userId].push(newConversation);
      console.log(`Creada nueva conversación para usuario ${userId}: ${conversationId}`);
    }

    const conversationIndex = inMemoryDb.conversations[userId].findIndex(conv => conv.id === conversationId);

    if (conversationIndex === -1) {
      // Si la conversación no existe, crearla
      const now = new Date().toISOString();
      const newConversation = {
        id: conversationId,
        title: 'Nueva conversación',
        createdAt: now,
        updatedAt: now,
        messages: []
      };

      inMemoryDb.conversations[userId].push(newConversation);
      console.log(`Creada nueva conversación para usuario ${userId}: ${conversationId}`);

      // Actualizar el índice para usar la conversación recién creada
      const newIndex = inMemoryDb.conversations[userId].length - 1;

      // Crear el mensaje
      const messageData = {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        role: message.role,
        content: message.content,
        timestamp: now
      };

      // Añadir el mensaje a la conversación
      inMemoryDb.conversations[userId][newIndex].messages.push(messageData);

      // Actualizar la fecha de actualización de la conversación
      inMemoryDb.conversations[userId][newIndex].updatedAt = now;

      // Si es un mensaje del usuario, actualizar el título de la conversación
      if (message.role === 'user') {
        const title = message.content.length > 30
          ? `${message.content.substring(0, 30)}...`
          : message.content;

        inMemoryDb.conversations[userId][newIndex].title = title;
      }

      return messageData;
    } else {
      // La conversación existe, añadir el mensaje
      const now = new Date().toISOString();
      const messageData = {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        role: message.role,
        content: message.content,
        timestamp: now
      };

      // Añadir el mensaje a la conversación
      if (!inMemoryDb.conversations[userId][conversationIndex].messages) {
        inMemoryDb.conversations[userId][conversationIndex].messages = [];
      }

      inMemoryDb.conversations[userId][conversationIndex].messages.push(messageData);

      // Actualizar la fecha de actualización de la conversación
      inMemoryDb.conversations[userId][conversationIndex].updatedAt = now;

      // Si es el primer mensaje y es del usuario, actualizar el título de la conversación
      if (message.role === 'user' && inMemoryDb.conversations[userId][conversationIndex].messages.length === 1) {
        // Es el primer mensaje, usar como título
        const title = message.content.length > 30
          ? `${message.content.substring(0, 30)}...`
          : message.content;

        inMemoryDb.conversations[userId][conversationIndex].title = title;
      }

      return messageData;
    }
  } catch (error) {
    console.error('Error al añadir mensaje:', error);
    throw error;
  }
}

// Inicializar la base de datos en memoria
initializeInMemoryDb();

module.exports = {
  getUserConversations,
  getConversation,
  createConversation,
  updateConversationTitle,
  deleteConversation,
  addMessage
};
