import React, { useEffect, useRef } from 'react';
import anime from '../utils/animeUtils';
import '../styles/AnimatedPortfolioChart.css';

interface PortfolioAsset {
  id: string;
  symbol: string;
  name: string;
  amount: number;
  value: number;
  color?: string;
}

interface AnimatedPortfolioChartProps {
  assets: PortfolioAsset[];
  totalValue: number;
  width?: number;
  height?: number;
  title?: string;
  totalLabel?: string;
}

const AnimatedPortfolioChart: React.FC<AnimatedPortfolioChartProps> = ({
  assets,
  totalValue,
  width = 350,
  height = 350,
  title = 'Distribución del Portafolio',
  totalLabel = 'Valor Total'
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const totalValueRef = useRef<HTMLSpanElement>(null);

  // Colores predefinidos para criptomonedas comunes
  const cryptoColors: Record<string, string> = {
    btc: '#F7931A', // Bitcoin
    eth: '#627EEA', // Ethereum
    sol: '#00FFA3', // Solana
    ada: '#0033AD', // Cardano
    dot: '#E6007A', // Polkadot
    doge: '#C2A633', // Dogecoin
    xrp: '#23292F', // XRP
    ltc: '#345D9D', // Litecoin
    bnb: '#F3BA2F', // Binance Coin
    link: '#2A5ADA', // Chainlink
  };

  useEffect(() => {
    if (!chartRef.current || assets.length === 0) return;

    // Limpiar el contenedor
    chartRef.current.innerHTML = '';

    // Actualizar el valor total con animación
    if (totalValueRef.current) {
      const countObj = { value: 0 };
      anime({
        targets: countObj,
        value: totalValue,
        round: 1,
        duration: 2000,
        easing: 'easeInOutQuart',
        update: () => {
          if (totalValueRef.current) {
            totalValueRef.current.textContent = countObj.value.toLocaleString('es-ES', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            });
          }
        }
      });
    }

    let startAngle = 0;

    // Crear segmentos de la gráfica
    assets.forEach((asset, index) => {
      const percentage = asset.value / totalValue;
      const angle = percentage * 360;

      // Crear el segmento
      const segment = document.createElement('div');
      segment.classList.add('chart-segment');
      segment.style.transform = `rotate(${startAngle}deg)`;

      // Crear el interior del segmento
      const segmentInner = document.createElement('div');
      segmentInner.classList.add('segment-inner');
      segmentInner.style.transform = `rotate(${angle}deg)`;

      // Asignar color
      let segmentColor;
      if (asset.color) {
        segmentColor = asset.color;
      } else if (cryptoColors[asset.id.toLowerCase()]) {
        segmentColor = cryptoColors[asset.id.toLowerCase()];
      } else {
        // Generar color basado en el índice
        const hue = index * (360 / assets.length);
        segmentColor = `hsl(${hue}, 70%, 60%)`;
      }
      segmentInner.style.backgroundColor = segmentColor;

      // Añadir tooltip con información
      segment.setAttribute('data-tooltip', `${asset.name}: ${(percentage * 100).toFixed(1)}%`);

      // Añadir al DOM
      segment.appendChild(segmentInner);
      chartRef.current.appendChild(segment);

      // Crear etiqueta
      const label = document.createElement('div');
      label.classList.add('chart-label');

      // Posicionar la etiqueta
      const labelAngle = startAngle + (angle / 2);
      const labelRadius = width * 0.35; // Ajustar según el tamaño del gráfico
      const labelX = Math.cos((labelAngle - 90) * Math.PI / 180) * labelRadius;
      const labelY = Math.sin((labelAngle - 90) * Math.PI / 180) * labelRadius;

      label.style.transform = `translate(${labelX}px, ${labelY}px)`;
      label.textContent = asset.symbol;

      // Añadir al DOM
      chartRef.current.appendChild(label);

      // Actualizar ángulo de inicio para el siguiente segmento
      startAngle += angle;
    });

    // Animar los segmentos
    anime({
      targets: '.chart-segment',
      scale: [0, 1],
      opacity: [0, 1],
      delay: anime.stagger(100),
      easing: 'spring(1, 80, 10, 0)'
    });

    // Animar las etiquetas
    anime({
      targets: '.chart-label',
      opacity: [0, 1],
      translateX: (el) => {
        const transform = el.style.transform;
        const match = transform.match(/translate\(([^,]+)px/);
        return [0, match ? parseFloat(match[1]) : 0];
      },
      translateY: (el) => {
        const transform = el.style.transform;
        const match = transform.match(/translate\([^,]+px,\s*([^)]+)px/);
        return [0, match ? parseFloat(match[1]) : 0];
      },
      delay: anime.stagger(100, {start: 500}),
      easing: 'easeOutElastic(1, .5)'
    });

  }, [assets, totalValue, width, height]);

  return (
    <div className="animated-portfolio-chart" style={{ width: `${width}px`, height: `${height}px` }}>
      {title && <h3 className="chart-title">{title}</h3>}

      <div className="chart-wrapper">
        <div ref={chartRef} className="pie-chart" style={{ width: `${width * 0.7}px`, height: `${width * 0.7}px` }}></div>

        <div className="total-value-container">
          <span ref={totalValueRef} className="total-value">$0</span>
          <span className="total-label">{totalLabel}</span>
        </div>
      </div>

      <div className="legend-container">
        {assets.map((asset, index) => {
          // Determinar color
          let itemColor;
          if (asset.color) {
            itemColor = asset.color;
          } else if (cryptoColors[asset.id.toLowerCase()]) {
            itemColor = cryptoColors[asset.id.toLowerCase()];
          } else {
            const hue = index * (360 / assets.length);
            itemColor = `hsl(${hue}, 70%, 60%)`;
          }

          return (
            <div key={asset.id} className="legend-item">
              <div className="legend-color" style={{ backgroundColor: itemColor }}></div>
              <div className="legend-label">{asset.symbol}</div>
              <div className="legend-value">${asset.value.toLocaleString()}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AnimatedPortfolioChart;
