import React, { useMemo, useRef } from 'react';
// Importar de forma explícita para evitar problemas de resolución
import * as THREE from 'three';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Sphere } from '@react-three/drei';

// Definir la interfaz de Props
interface Avatar3DProps {
  status?: 'idle' | 'thinking' | 'positive' | 'negative' | 'neutral' | 'concerned';
}

const Avatar3D: React.FC<Avatar3DProps> = ({ status = 'neutral' }) => {
  // Calcular el color basado en el estado
  const avatarColor = useMemo(() => {
    switch (status) {
      case 'idle':
        return '#4657CE'; // Azul
      case 'positive':
        return '#00FF9D'; // Verde
      case 'negative':
        return '#FF3A6E'; // Rojo
      case 'concerned':
        return '#FFCC00'; // Amarillo
      case 'thinking':
        return '#7B4DFF'; // Morado
      default:
        return '#4657CE'; // Azul por defecto (neutral)
    }
  }, [status]);

  // Calcular el color emisivo (más suave para el efecto de brillo)
  const emissiveColor = useMemo(() => {
    switch (status) {
      case 'idle':
        return '#2A3580'; // Azul más oscuro
      case 'positive':
        return '#00CC7A'; // Verde más oscuro
      case 'negative':
        return '#CC2357'; // Rojo más oscuro
      case 'concerned':
        return '#CC9900'; // Amarillo más oscuro
      case 'thinking':
        return '#5E3DCC'; // Morado más oscuro
      default:
        return '#2A3580'; // Azul más oscuro por defecto (neutral)
    }
  }, [status]);

  // Usar THREE.Color para definir colores
  const color = useMemo(() => new THREE.Color(avatarColor), [avatarColor]);
  const emissive = useMemo(() => new THREE.Color(emissiveColor), [emissiveColor]);

  // Referencia para la esfera
  const sphereRef = useRef<THREE.Mesh>(null);

  return (
    <div className="avatar3d-container">
      <Canvas dpr={[1, 2]} performance={{ min: 0.5 }}>
        {/* Cámara */}
        <PerspectiveCamera makeDefault position={[0, 0, 5]} />

        {/* Luces */}
        <ambientLight intensity={0.5} />
        <directionalLight position={[3, 2, 3]} intensity={1.5} />

        {/* Modelo 3D (esfera) */}
        <Sphere ref={sphereRef} args={[1, 32, 32]}>
          <meshStandardMaterial
            color={color}
            emissive={emissive}
            emissiveIntensity={0.5}
            roughness={0.3}
            metalness={0.7}
          />
        </Sphere>

        {/* Controles para rotar el avatar (opcional para pruebas) */}
        <OrbitControls enableZoom={false} enablePan={false} />
      </Canvas>
    </div>
  );
};

export default Avatar3D;
