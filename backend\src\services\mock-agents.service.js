/**
 * Mock Agents Service
 * 
 * Este servicio proporciona respuestas simuladas para los agentes ADK
 */

/**
 * Simula el agente de análisis técnico
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function technicalAnalysisAgent(query) {
  const cryptoName = extractCryptoName(query) || 'Bitcoin';
  
  return {
    choices: [
      {
        message: {
          role: 'assistant',
          content: `
# Análisis Técnico para ${cryptoName}

## Tendencia Actual
Basado en los indicadores técnicos actuales, ${cryptoName} muestra una tendencia **moderadamente alcista** en el corto plazo.

## Indicadores Clave
- **Media Móvil Simple (SMA)**: Las SMA de 50 y 200 días muestran un patrón de cruce dorado, lo que generalmente indica un impulso alcista.
- **RSI (Índice de Fuerza Relativa)**: Actualmente en 58, lo que sugiere que hay espacio para más crecimiento antes de entrar en territorio de sobrecompra.
- **MACD**: El MACD está por encima de la línea de señal, confirmando el impulso alcista.
- **Bandas de Bollinger**: El precio está cerca del límite superior, lo que sugiere una posible resistencia.

## Niveles de Soporte y Resistencia
- **Soporte principal**: $${Math.floor(Math.random() * 5000) + 25000}
- **Resistencia principal**: $${Math.floor(Math.random() * 5000) + 30000}

## Conclusión
Los indicadores técnicos sugieren un panorama moderadamente alcista para ${cryptoName} en el corto plazo, con potencial para pruebas de resistencia. Sin embargo, los inversores deben estar atentos a posibles correcciones si el RSI entra en territorio de sobrecompra.

*Nota: Este análisis es puramente educativo y no constituye asesoramiento financiero.*
          `
        }
      }
    ]
  };
}

/**
 * Simula el agente de análisis de sentimiento
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function sentimentAnalysisAgent(query) {
  const cryptoName = extractCryptoName(query) || 'Bitcoin';
  const sentiment = Math.random() > 0.5 ? 'positivo' : 'mixto';
  const fearGreedValue = Math.floor(Math.random() * 100);
  
  return {
    choices: [
      {
        message: {
          role: 'assistant',
          content: `
# Análisis de Sentimiento para ${cryptoName}

## Sentimiento General del Mercado
El sentimiento actual del mercado para ${cryptoName} es **${sentiment}**.

## Análisis de Noticias
Las noticias recientes muestran un tono ${sentiment === 'positivo' ? 'mayormente positivo' : 'mixto'}, con varios desarrollos notables:
- Aumento de la adopción institucional
- Mejoras en la infraestructura de la red
- Desarrollos regulatorios en mercados clave

## Sentimiento en Redes Sociales
El análisis de redes sociales muestra:
- Twitter: Sentimiento ${sentiment === 'positivo' ? 'positivo (65%)' : 'mixto (52%)'}
- Reddit: Discusiones ${sentiment === 'positivo' ? 'optimistas' : 'divididas'} sobre el futuro precio
- Foros especializados: Interés creciente en desarrollos técnicos

## Índice de Miedo y Codicia
El Índice de Miedo y Codicia actual está en **${fearGreedValue}** (${fearGreedValue > 50 ? 'Codicia' : 'Miedo'}), lo que indica que el mercado está en un estado de ${fearGreedValue > 50 ? 'optimismo' : 'cautela'}.

## Conclusión
El sentimiento general para ${cryptoName} es ${sentiment}, con un Índice de Miedo y Codicia que sugiere ${fearGreedValue > 50 ? 'optimismo en el mercado' : 'cierta cautela entre los inversores'}. Este sentimiento ${sentiment === 'positivo' ? 'positivo podría respaldar movimientos alcistas de precios' : 'mixto podría resultar en volatilidad a corto plazo'}.

*Nota: Este análisis es puramente educativo y no constituye asesoramiento financiero.*
          `
        }
      }
    ]
  };
}

/**
 * Simula el agente de análisis on-chain
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function onchainAnalysisAgent(query) {
  const cryptoName = extractCryptoName(query) || 'Bitcoin';
  const whaleActivity = Math.random() > 0.5 ? 'acumulación' : 'distribución';
  
  return {
    choices: [
      {
        message: {
          role: 'assistant',
          content: `
# Análisis On-Chain para ${cryptoName}

## Actividad de Ballenas
Las direcciones con grandes tenencias (ballenas) muestran un patrón de **${whaleActivity}** en los últimos 7 días:
- ${whaleActivity === 'acumulación' ? 'Aumento' : 'Disminución'} del ${Math.floor(Math.random() * 10) + 1}% en tenencias totales
- ${Math.floor(Math.random() * 10) + 1} transacciones significativas de más de $1M

## Métricas de Red
- **Transacciones diarias**: ${Math.floor(Math.random() * 500000) + 200000}, un ${Math.floor(Math.random() * 20) + 1}% ${Math.random() > 0.5 ? 'más' : 'menos'} que la semana anterior
- **Tarifas de gas/transacción**: ${Math.random() > 0.5 ? 'Aumentando' : 'Disminuyendo'}, lo que indica ${Math.random() > 0.5 ? 'mayor' : 'menor'} congestión de la red
- **Nuevas direcciones**: ${Math.floor(Math.random() * 50000) + 10000} diarias, un indicador de ${Math.random() > 0.5 ? 'creciente' : 'estable'} adopción

## Patrones de Transferencia
- **Intercambios**: ${Math.random() > 0.5 ? 'Salidas netas' : 'Entradas netas'} de ${cryptoName}, sugiriendo ${Math.random() > 0.5 ? 'acumulación' : 'presión de venta'}
- **Staking/Bloqueo**: ${Math.floor(Math.random() * 10) + 60}% del suministro está actualmente bloqueado o en staking

## Conclusión
Los datos on-chain para ${cryptoName} muestran ${whaleActivity === 'acumulación' ? 'señales positivas con acumulación de ballenas y salidas de intercambios' : 'señales mixtas con alguna distribución de ballenas'}, lo que podría indicar ${whaleActivity === 'acumulación' ? 'presión alcista a medio plazo' : 'posible volatilidad a corto plazo'}.

*Nota: Este análisis es puramente educativo y no constituye asesoramiento financiero.*
          `
        }
      }
    ]
  };
}

/**
 * Simula el agente Guru Cripto
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function guruCriptoAgent(query) {
  const cryptoName = extractCryptoName(query) || 'Bitcoin';
  const sentiment = Math.random() > 0.5 ? 'positivo' : 'mixto';
  const priceChange = Math.floor(Math.random() * 20) - 5; // -5% a +15%
  
  return {
    choices: [
      {
        message: {
          role: 'assistant',
          content: `
# Análisis Completo de ${cryptoName}

## Resumen Ejecutivo
Basado en mi análisis integral, ${cryptoName} muestra un panorama **${priceChange > 0 ? 'moderadamente alcista' : 'mixto'}** para el corto plazo, con factores técnicos, de sentimiento y on-chain que sugieren ${priceChange > 5 ? 'un potencial movimiento alcista significativo' : priceChange > 0 ? 'un movimiento alcista moderado' : 'posible consolidación o corrección menor'}.

## Análisis Técnico
- **Tendencia**: ${priceChange > 0 ? 'Alcista' : 'Bajista/Neutral'} en el corto plazo
- **Indicadores clave**: RSI en ${Math.floor(Math.random() * 30) + 40}, MACD ${priceChange > 0 ? 'positivo' : 'negativo'}
- **Soportes y resistencias**: Soporte principal en $${Math.floor(Math.random() * 5000) + 25000}, resistencia en $${Math.floor(Math.random() * 5000) + 30000}

## Análisis de Sentimiento
- **Sentimiento general**: ${sentiment}
- **Índice de Miedo y Codicia**: ${Math.floor(Math.random() * 100)} (${Math.random() > 0.5 ? 'Codicia' : 'Miedo'})
- **Cobertura mediática**: ${sentiment === 'positivo' ? 'Mayormente positiva' : 'Mixta'}, con énfasis en ${Math.random() > 0.5 ? 'adopción institucional' : 'desarrollos tecnológicos'}

## Análisis On-Chain
- **Actividad de ballenas**: ${Math.random() > 0.5 ? 'Acumulación' : 'Distribución'} moderada
- **Flujos de intercambios**: ${Math.random() > 0.5 ? 'Salidas netas' : 'Entradas netas'}
- **Actividad de red**: ${Math.random() > 0.5 ? 'Creciente' : 'Estable'}, indicando ${Math.random() > 0.5 ? 'mayor' : 'consistente'} uso y adopción

## Predicción de Precio
Para la próxima semana, preveo que ${cryptoName} podría experimentar un movimiento de aproximadamente **${priceChange > 0 ? '+' : ''}${priceChange}%** desde los niveles actuales, con la posibilidad de ${priceChange > 0 ? 'pruebas de resistencia en niveles superiores' : 'consolidación en niveles de soporte'}.

## Factores a Vigilar
- Desarrollos macroeconómicos, especialmente decisiones de la Reserva Federal
- Noticias regulatorias en mercados clave
- Movimientos de grandes tenedores (ballenas)

*Recuerda que este análisis es educativo y no constituye asesoramiento financiero. Siempre realiza tu propia investigación antes de tomar decisiones de inversión.*
          `
        }
      }
    ]
  };
}

/**
 * Genera un análisis completo para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para el análisis
 * @returns {Promise<Object>} - El análisis completo
 */
async function generateComprehensiveAnalysis(cryptoName, timeframe) {
  const response = await guruCriptoAgent(`Análisis completo de ${cryptoName} para ${timeframe}`);
  
  return {
    cryptoName,
    timeframe,
    analysis: response,
    timestamp: new Date().toISOString()
  };
}

/**
 * Genera una predicción de precio para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para la predicción
 * @returns {Promise<Object>} - La predicción de precio
 */
async function generatePricePrediction(cryptoName, timeframe) {
  const priceChange = Math.floor(Math.random() * 20) - 5; // -5% a +15%
  
  const response = {
    choices: [
      {
        message: {
          role: 'assistant',
          content: `
# Predicción de Precio para ${cryptoName} (${timeframe})

## Resumen de la Predicción
Basado en mi análisis integral de factores técnicos, de sentimiento y on-chain, preveo que ${cryptoName} podría experimentar un movimiento de aproximadamente **${priceChange > 0 ? '+' : ''}${priceChange}%** en los próximos ${timeframe}.

## Factores Clave para esta Predicción
- **Análisis Técnico**: Patrones de precio ${priceChange > 0 ? 'alcistas' : 'bajistas/neutrales'}, con ${priceChange > 0 ? 'soporte sólido' : 'resistencia significativa'} en niveles actuales
- **Sentimiento del Mercado**: ${Math.random() > 0.5 ? 'Positivo' : 'Mixto'}, con un Índice de Miedo y Codicia en ${Math.floor(Math.random() * 100)}
- **Datos On-Chain**: ${Math.random() > 0.5 ? 'Acumulación' : 'Distribución'} moderada por parte de grandes tenedores

## Escenarios Posibles
- **Escenario Alcista**: Hasta +${priceChange + 10}% si ${Math.random() > 0.5 ? 'mejora el sentimiento general del mercado' : 'hay noticias positivas significativas'}
- **Escenario Base**: ${priceChange > 0 ? '+' : ''}${priceChange}% siguiendo la tendencia actual
- **Escenario Bajista**: Hasta ${priceChange - 10}% si ${Math.random() > 0.5 ? 'empeora el sentimiento del mercado' : 'hay noticias negativas inesperadas'}

## Niveles a Vigilar
- **Soporte clave**: $${Math.floor(Math.random() * 5000) + 25000}
- **Resistencia clave**: $${Math.floor(Math.random() * 5000) + 30000}

*Importante: Esta predicción es puramente educativa y no constituye asesoramiento financiero. Los mercados de criptomonedas son altamente volátiles e impredecibles. Siempre realiza tu propia investigación antes de tomar decisiones de inversión.*
          `
        }
      }
    ]
  };
  
  return {
    cryptoName,
    timeframe,
    prediction: response,
    timestamp: new Date().toISOString()
  };
}

/**
 * Extrae el nombre de la criptomoneda de una consulta
 * @param {string} query - La consulta del usuario
 * @returns {string|null} - El nombre de la criptomoneda o null si no se encuentra
 */
function extractCryptoName(query) {
  const cryptoNames = [
    'Bitcoin', 'Ethereum', 'Binance Coin', 'BNB', 'Cardano', 'ADA',
    'Solana', 'SOL', 'XRP', 'Dogecoin', 'DOGE', 'Polkadot', 'DOT',
    'Tether', 'USDT', 'USD Coin', 'USDC', 'BTC', 'ETH'
  ];
  
  const queryLower = query.toLowerCase();
  
  for (const name of cryptoNames) {
    if (queryLower.includes(name.toLowerCase())) {
      return name;
    }
  }
  
  return null;
}

module.exports = {
  technicalAnalysisAgent,
  sentimentAnalysisAgent,
  onchainAnalysisAgent,
  guruCriptoAgent,
  generateComprehensiveAnalysis,
  generatePricePrediction
};
