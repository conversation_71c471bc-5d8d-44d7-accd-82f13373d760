/* Estilos para el avatar simple */
.simple-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animación de rotación */
@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Animación de pulso */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 224, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(0, 224, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 224, 255, 0);
  }
}

/* Animación de flotación */
@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%) translateY(0);
  }
  50% {
    transform: translate(-50%, -50%) translateY(-10px);
  }
}

/* Animación para los ojos */
@keyframes blink {
  0%, 90%, 100% {
    transform: scaleY(1);
  }
  95% {
    transform: scaleY(0.1);
  }
}

/* Estilos para el mensaje */
.avatar-message {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
