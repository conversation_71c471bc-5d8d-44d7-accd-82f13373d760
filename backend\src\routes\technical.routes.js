/**
 * Rutas de Análisis Técnico
 * 
 * Estas rutas proporcionan acceso a las funcionalidades de análisis técnico
 * a través del MCP Technical Analysis.
 */

const express = require('express');
const router = express.Router();
const technicalService = require('../services/technical-analysis.service');

/**
 * @route   GET /api/technical/analysis/:symbol
 * @desc    Obtiene un análisis técnico completo para una criptomoneda
 * @access  Public
 */
router.get('/analysis/:symbol', async (req, res) => {
  try {
    const { symbol } = req.params;
    const { interval = '1d', limit = 30 } = req.query;
    
    const analysis = await technicalService.performTechnicalAnalysis(
      symbol,
      interval,
      parseInt(limit)
    );
    
    res.json(analysis);
  } catch (error) {
    console.error('Error en ruta de análisis técnico:', error);
    res.status(500).json({
      error: 'Error al obtener análisis técnico',
      message: error.message
    });
  }
});

/**
 * @route   POST /api/technical/indicators/:indicator
 * @desc    Calcula un indicador técnico específico
 * @access  Public
 */
router.post('/indicators/:indicator', async (req, res) => {
  try {
    const { indicator } = req.params;
    const { prices, ...params } = req.body;
    
    if (!prices || !Array.isArray(prices)) {
      return res.status(400).json({
        error: 'Datos inválidos',
        message: 'Se requiere un array de precios'
      });
    }
    
    const result = await technicalService.calculateIndicator(
      indicator,
      prices,
      params
    );
    
    res.json(result);
  } catch (error) {
    console.error(`Error en ruta de indicador ${req.params.indicator}:`, error);
    res.status(500).json({
      error: `Error al calcular indicador ${req.params.indicator}`,
      message: error.message
    });
  }
});

/**
 * @route   POST /api/technical/patterns
 * @desc    Detecta patrones de velas
 * @access  Public
 */
router.post('/patterns', async (req, res) => {
  try {
    const { candles } = req.body;
    
    if (!candles || !Array.isArray(candles)) {
      return res.status(400).json({
        error: 'Datos inválidos',
        message: 'Se requiere un array de velas'
      });
    }
    
    const patterns = await technicalService.detectCandlePatterns(candles);
    
    res.json(patterns);
  } catch (error) {
    console.error('Error en ruta de patrones:', error);
    res.status(500).json({
      error: 'Error al detectar patrones',
      message: error.message
    });
  }
});

module.exports = router;
