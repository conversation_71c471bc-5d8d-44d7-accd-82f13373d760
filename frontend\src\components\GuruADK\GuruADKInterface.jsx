import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaMicrophone, FaStop } from 'react-icons/fa';
import { MdSend } from 'react-icons/md';
import './GuruADKInterface.css';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002';

const GuruADKInterface = () => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [selectedCrypto, setSelectedCrypto] = useState('Bitcoin');
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  
  // Popular cryptocurrencies
  const cryptoOptions = [
    'Bitcoin', 'Ethereum', 'Binance Coin', 'Cardano', 'Solana', 
    'XRP', 'Dogecoin', 'Polkadot', 'Tether', 'USD Coin'
  ];
  
  // Timeframe options
  const timeframeOptions = [
    { value: '1d', label: '1 Day' },
    { value: '7d', label: '1 Week' },
    { value: '30d', label: '1 Month' },
    { value: '90d', label: '3 Months' },
    { value: '365d', label: '1 Year' }
  ];
  
  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  // Focus input on load
  useEffect(() => {
    inputRef.current?.focus();
  }, []);
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!input.trim() && !selectedCrypto) return;
    
    // Use input if provided, otherwise create a query based on selections
    const query = input.trim() || `What's your analysis and prediction for ${selectedCrypto} for the next ${selectedTimeframe}?`;
    
    // Add user message
    setMessages(prev => [...prev, { role: 'user', content: query }]);
    setInput('');
    setIsLoading(true);
    setError(null);
    
    try {
      // Call the ADK API
      const response = await axios.post(`${API_URL}/api/adk/guru`, { question: query });
      
      // Add response to messages
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: response.data.choices[0].message.content || 'No response from Guru Cripto'
      }]);
    } catch (err) {
      console.error('Error querying Guru ADK:', err);
      setError('Failed to get a response from Guru Cripto. Please try again.');
      
      // Add error message
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'Sorry, I encountered an error while processing your request. Please try again later.',
        isError: true
      }]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle quick analysis button
  const handleQuickAnalysis = async () => {
    if (!selectedCrypto || !selectedTimeframe) return;
    
    const query = `Analyze ${selectedCrypto} for the ${selectedTimeframe} timeframe`;
    
    // Add user message
    setMessages(prev => [...prev, { role: 'user', content: query }]);
    setIsLoading(true);
    setError(null);
    
    try {
      // Call the ADK API
      const response = await axios.post(`${API_URL}/api/adk/analysis`, { 
        cryptoName: selectedCrypto,
        timeframe: selectedTimeframe
      });
      
      // Add response to messages
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: response.data.analysis.choices[0].message.content || 'No analysis available'
      }]);
    } catch (err) {
      console.error('Error getting analysis:', err);
      setError('Failed to get analysis. Please try again.');
      
      // Add error message
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'Sorry, I encountered an error while generating the analysis. Please try again later.',
        isError: true
      }]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle prediction button
  const handlePrediction = async () => {
    if (!selectedCrypto || !selectedTimeframe) return;
    
    const query = `What's your prediction for ${selectedCrypto} price in the next ${selectedTimeframe}?`;
    
    // Add user message
    setMessages(prev => [...prev, { role: 'user', content: query }]);
    setIsLoading(true);
    setError(null);
    
    try {
      // Call the ADK API
      const response = await axios.post(`${API_URL}/api/adk/prediction`, { 
        cryptoName: selectedCrypto,
        timeframe: selectedTimeframe
      });
      
      // Add response to messages
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: response.data.prediction.choices[0].message.content || 'No prediction available'
      }]);
    } catch (err) {
      console.error('Error getting prediction:', err);
      setError('Failed to get prediction. Please try again.');
      
      // Add error message
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'Sorry, I encountered an error while generating the prediction. Please try again later.',
        isError: true
      }]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Toggle voice recording
  const toggleRecording = () => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      // Here you would normally process the recording
      // For now, we'll just simulate it
      setInput(input + " [Voice recording processed]");
    } else {
      // Start recording
      setIsRecording(true);
      // Simulate recording for demo purposes
      setTimeout(() => {
        setIsRecording(false);
        setInput(input + " How is the market looking today?");
      }, 3000);
    }
  };
  
  // Format message content with markdown-like syntax
  const formatMessage = (content) => {
    // This is a simple implementation - you might want to use a proper markdown parser
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br />');
  };
  
  return (
    <div className="guru-adk-container">
      <div className="guru-adk-header">
        <h2>Guru Cripto ADK</h2>
        <p>Powered by Google's Agent Development Kit</p>
      </div>
      
      <div className="guru-adk-controls">
        <div className="control-group">
          <label htmlFor="crypto-select">Cryptocurrency:</label>
          <select 
            id="crypto-select"
            value={selectedCrypto}
            onChange={(e) => setSelectedCrypto(e.target.value)}
            disabled={isLoading}
          >
            {cryptoOptions.map(crypto => (
              <option key={crypto} value={crypto}>{crypto}</option>
            ))}
          </select>
        </div>
        
        <div className="control-group">
          <label htmlFor="timeframe-select">Timeframe:</label>
          <select 
            id="timeframe-select"
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            disabled={isLoading}
          >
            {timeframeOptions.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
        
        <div className="action-buttons">
          <button 
            className="analysis-button"
            onClick={handleQuickAnalysis}
            disabled={isLoading}
          >
            Quick Analysis
          </button>
          
          <button 
            className="prediction-button"
            onClick={handlePrediction}
            disabled={isLoading}
          >
            Get Prediction
          </button>
        </div>
      </div>
      
      <div className="guru-adk-messages">
        {messages.length === 0 ? (
          <div className="empty-state">
            <FaRobot className="robot-icon" />
            <p>Ask Guru Cripto about cryptocurrency analysis, market trends, or price predictions.</p>
          </div>
        ) : (
          messages.map((message, index) => (
            <div 
              key={index} 
              className={`message ${message.role === 'user' ? 'user-message' : 'assistant-message'} ${message.isError ? 'error-message' : ''}`}
            >
              <div className="message-icon">
                {message.role === 'user' ? <FaUser /> : <FaRobot />}
              </div>
              <div 
                className="message-content"
                dangerouslySetInnerHTML={{ __html: formatMessage(message.content) }}
              />
            </div>
          ))
        )}
        
        {isLoading && (
          <div className="message assistant-message loading-message">
            <div className="message-icon">
              <FaRobot />
            </div>
            <div className="message-content">
              <FaSpinner className="spinner" />
              <span>Analyzing...</span>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      <form className="guru-adk-input" onSubmit={handleSubmit}>
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask about cryptocurrency analysis or predictions..."
          disabled={isLoading}
          ref={inputRef}
        />
        
        <button
          type="button"
          className={`voice-button ${isRecording ? 'recording' : ''}`}
          onClick={toggleRecording}
          disabled={isLoading}
        >
          {isRecording ? <FaStop /> : <FaMicrophone />}
        </button>
        
        <button
          type="submit"
          className="send-button"
          disabled={isLoading || (!input.trim() && !selectedCrypto)}
        >
          <MdSend />
        </button>
      </form>
      
      {error && <div className="error-notification">{error}</div>}
    </div>
  );
};

export default GuruADKInterface;
