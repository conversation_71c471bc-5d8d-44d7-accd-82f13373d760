/* Estilos para el componente de análisis de portafolio */

.portfolio-analysis {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.portfolio-analysis h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1.25rem 0;
  color: var(--text-bright);
}

.analysis-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.analysis-tabs button {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  color: var(--text-dim);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.analysis-tabs button:hover {
  color: var(--text-bright);
  background-color: rgba(255, 255, 255, 0.05);
}

.analysis-tabs button.active {
  color: var(--text-bright);
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom: 3px solid var(--primary);
}

.analysis-content {
  flex: 1;
  overflow-y: auto;
}

/* Métricas de riesgo */
.risk-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metric-card h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text-dim);
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text-bright);
}

.metric-value.positive {
  color: var(--success);
}

.metric-value.negative {
  color: var(--error);
}

.metric-description {
  font-size: 0.85rem;
  color: var(--text-medium);
  line-height: 1.4;
}

/* Matriz de correlación */
.correlation-matrix {
  overflow-x: auto;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1rem;
}

.correlation-matrix table {
  width: 100%;
  border-collapse: collapse;
}

.correlation-matrix th,
.correlation-matrix td {
  padding: 0.75rem;
  text-align: center;
  font-size: 0.9rem;
}

.correlation-matrix th {
  color: var(--text-medium);
  font-weight: 600;
  background: rgba(0, 0, 0, 0.2);
}

.correlation-matrix td {
  color: var(--text-bright);
}

.correlation-matrix td.perfect-correlation {
  background: rgba(var(--primary-rgb), 0.3);
}

.correlation-matrix td.high-correlation {
  background: rgba(var(--primary-rgb), 0.2);
}

.correlation-matrix td.medium-correlation {
  background: rgba(255, 255, 255, 0.1);
}

.correlation-matrix td.low-correlation {
  background: rgba(0, 0, 0, 0.1);
}

/* Análisis de rendimiento */
.performance-analysis {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.performance-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.asset-performance {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.asset-performance h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.asset-returns-table {
  width: 100%;
  border-collapse: collapse;
}

.asset-returns-table th,
.asset-returns-table td {
  padding: 0.75rem;
  text-align: left;
  font-size: 0.9rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.asset-returns-table th {
  color: var(--text-medium);
  font-weight: 600;
}

.asset-returns-table td {
  color: var(--text-bright);
}

.asset-returns-table td.positive {
  color: var(--success);
}

.asset-returns-table td.negative {
  color: var(--error);
}

/* Mensajes */
.loading-message,
.no-data-message,
.empty-analysis-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-dim);
  text-align: center;
  padding: 1rem;
}

/* Estilos para el análisis vacío */
.empty-analysis {
  display: flex;
  flex-direction: column;
}

/* Responsive */
@media (max-width: 768px) {
  .analysis-tabs {
    overflow-x: auto;
    padding-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }

  .analysis-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .analysis-tabs button {
    white-space: nowrap;
    padding: 0.75rem 1rem;
  }

  .risk-metrics {
    grid-template-columns: 1fr;
  }

  .performance-summary {
    grid-template-columns: 1fr;
  }
}
