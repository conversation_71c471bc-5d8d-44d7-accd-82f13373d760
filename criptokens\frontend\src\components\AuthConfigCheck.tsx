import { useState, useEffect } from 'react';
import { fetchSignInMethodsForEmail } from 'firebase/auth';
import { auth } from '../firebase-init';

const AuthConfigCheck = () => {
  const [status, setStatus] = useState<string>('Verificando configuración...');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuthConfig = async () => {
      try {
        // Usar un correo de prueba para verificar los métodos de inicio de sesión disponibles
        const testEmail = '<EMAIL>';
        console.log('Verificando métodos de inicio de sesión para:', testEmail);
        
        const methods = await fetchSignInMethodsForEmail(auth, testEmail);
        console.log('Métodos disponibles:', methods);
        
        // Si llegamos aquí, la configuración de autenticación está funcionando
        setStatus('Configuración de autenticación correcta');
        setError(null);
      } catch (err: any) {
        console.error('Error al verificar la configuración de autenticación:', err);
        setStatus('Error en la configuración de autenticación');
        setError(err.message || 'Error desconocido');
      } finally {
        setLoading(false);
      }
    };

    checkAuthConfig();
  }, []);

  return (
    <div style={{ padding: '15px', border: '1px solid #ddd', borderRadius: '5px', margin: '15px 0', backgroundColor: '#f9f9f9' }}>
      <h3>Verificación de Configuración de Autenticación</h3>
      
      {loading ? (
        <p>Verificando configuración...</p>
      ) : (
        <>
          <p><strong>Estado:</strong> {status}</p>
          {error && (
            <div style={{ color: 'red', marginTop: '10px' }}>
              <p><strong>Error:</strong> {error}</p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AuthConfigCheck;
