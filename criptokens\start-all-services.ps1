# Script para iniciar todos los servicios de Criptokens en Windows

# Función para mostrar mensajes con colores
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Mostrar mensaje de inicio
Write-ColorOutput Green "=== INICIANDO TODOS LOS SERVICIOS DE CRIPTOKENS ==="
Write-Output ""

# Iniciar el servidor MCP de crypto
Write-ColorOutput Magenta "Iniciando Crypto MCP Server..."
Start-Process -NoNewWindow -FilePath "node" -ArgumentList "../crypto-mcp-server/http-server.js" -WorkingDirectory (Get-Location)

# Esperar un momento para que el servidor MCP se inicie
Write-Output "Esperando a que el servidor MCP se inicie..."
Start-Sleep -Seconds 2

# Iniciar el servidor Brave Search
Write-ColorOutput Blue "Iniciando Brave Search Server..."
Start-Process -NoNewWindow -FilePath "node" -ArgumentList "../brave-search-server.js" -WorkingDirectory (Get-Location)

# Iniciar el servidor Playwright MCP
Write-ColorOutput Cyan "Iniciando Playwright MCP Server..."
Start-Process -NoNewWindow -FilePath "node" -ArgumentList "../playwright-mcp-server/dist/server.js" -WorkingDirectory (Get-Location)

# Esperar un momento para que los servidores MCP se inicien
Write-Output "Esperando a que los servidores MCP se inicien..."
Start-Sleep -Seconds 2

# Iniciar el backend
Write-ColorOutput Green "Iniciando Backend..."
Start-Process -NoNewWindow -FilePath "node" -ArgumentList "src/server.js" -WorkingDirectory (Join-Path -Path (Get-Location) -ChildPath "backend")

# Esperar un momento para que el backend se inicie
Write-Output "Esperando a que el backend se inicie..."
Start-Sleep -Seconds 2

# Iniciar el frontend
Write-ColorOutput Yellow "Iniciando Frontend..."
Start-Process -NoNewWindow -FilePath "npx" -ArgumentList "vite" -WorkingDirectory (Join-Path -Path (Get-Location) -ChildPath "frontend")

# Mostrar mensaje de éxito
Write-Output ""
Write-ColorOutput Green "=== TODOS LOS SERVICIOS INICIADOS ==="
Write-Output "Servicios disponibles:"
Write-ColorOutput Magenta "- Crypto MCP Server: http://localhost:3101"
Write-ColorOutput Blue "- Brave Search Server: http://localhost:3102"
Write-ColorOutput Cyan "- Playwright MCP Server: http://localhost:3103"
Write-ColorOutput Green "- Backend: http://localhost:3001"
Write-ColorOutput Yellow "- Frontend: http://localhost:5173"
Write-Output ""
Write-Output "Para detener los servicios, cierra las ventanas de comandos o presiona Ctrl+C en cada una de ellas."
Write-Output ""

# Mantener el script en ejecución
Write-Output "Presiona Ctrl+C para salir de este script (los servicios seguirán ejecutándose)."
try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Write-Output "Script finalizado. Los servicios siguen ejecutándose en segundo plano."
}
