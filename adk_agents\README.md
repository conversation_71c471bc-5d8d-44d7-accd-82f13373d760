# Criptokens ADK Agents

Este directorio contiene la implementación de agentes de IA utilizando el Agent Development Kit (ADK) de Google para el proyecto Criptokens.

## Estructura del Proyecto

```
adk_agents/
├── .env                    # Configuración de claves API
├── __init__.py             # Inicialización del paquete
├── technical_agent/        # Agente de análisis técnico
│   ├── __init__.py
│   └── agent.py
├── sentiment_agent/        # Agente de análisis de sentimiento
│   ├── __init__.py
│   └── agent.py
├── onchain_agent/          # Agente de análisis on-chain
│   ├── __init__.py
│   └── agent.py
└── guru_agent/             # Agente coordinador (Guru Cripto)
    ├── __init__.py
    └── agent.py
```

## Agentes Implementados

### 1. Agente de Análisis Técnico

El agente de análisis técnico proporciona análisis basados en indicadores técnicos como medias móviles, RSI, y patrones de precio. Utiliza datos históricos de precios para identificar tendencias, niveles de soporte y resistencia, y generar perspectivas técnicas.

### 2. Agente de Análisis de Sentimiento

El agente de análisis de sentimiento evalúa la percepción del mercado analizando noticias y actividad en redes sociales. Proporciona puntuaciones de sentimiento, índice de miedo y codicia, y análisis de la cobertura mediática.

### 3. Agente de Análisis On-Chain

El agente de análisis on-chain examina datos de la blockchain como transferencias de tokens, actividad de ballenas, y métricas de red. Proporciona información sobre el comportamiento de los grandes tenedores y la actividad en la red.

### 4. Agente Guru Cripto (Coordinador)

El agente Guru Cripto actúa como coordinador, delegando consultas a los agentes especializados o ejecutándolos en paralelo para generar análisis completos. Proporciona predicciones basadas en la combinación de análisis técnico, de sentimiento y on-chain.

## Configuración

1. Asegúrate de tener instalado el ADK de Google:
   ```
   pip install google-adk
   ```

2. Configura las claves API en el archivo `.env`:
   - `COINMARKETCAP_API_KEY`: Para datos de precios de criptomonedas
   - `BRAVE_API_KEY`: Para búsqueda de noticias
   - `ETHERSCAN_API_KEY`: Para datos on-chain
   - `OPENROUTER_API_KEY`: Para acceso a modelos de IA
   - `GOOGLE_API_KEY`: Para acceso a modelos Gemini (Google AI Studio)

3. Alternativamente, configura Vertex AI:
   - `GOOGLE_GENAI_USE_VERTEXAI=TRUE`
   - `GOOGLE_CLOUD_PROJECT=your-project-id`
   - `GOOGLE_CLOUD_LOCATION=us-central1`

## Uso

### Iniciar la Interfaz Web de ADK

Para iniciar la interfaz web de ADK y probar los agentes:

```bash
cd C:/Users/<USER>/OneDrive/Escritorio/Criptokens
python start_adk_agents.py
```

Luego, abre http://localhost:8000 en tu navegador.

### Uso Programático

Para usar los agentes programáticamente:

```python
from google.adk.runtime import Runtime
from adk_agents import guru_agent

async def main():
    runtime = Runtime()
    session = runtime.new_session()
    
    response = await guru_agent.run_async(
        session=session,
        query="¿Cuál es tu predicción para Bitcoin en la próxima semana?"
    )
    
    print(response)

import asyncio
asyncio.run(main())
```

## Integración con Criptokens

Estos agentes pueden integrarse con el frontend de Criptokens a través de la API de ADK. El script `start_adk_agents.py` inicia un servidor API que puede ser consumido por el frontend.

## Dependencias

- google-adk
- aiohttp
- numpy
- python-dotenv

## Notas

- Los agentes utilizan servicios MCP (Model Context Protocol) cuando están disponibles, con fallback a llamadas API directas o datos simulados.
- Para un rendimiento óptimo, asegúrate de que los servidores MCP estén en funcionamiento.
