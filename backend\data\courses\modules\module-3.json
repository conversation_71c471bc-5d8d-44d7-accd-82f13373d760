{"id": "module-3", "title": "Bitcoin: La Primera Criptomoneda", "description": "Explora en profundidad Bitcoin, la primera y más importante criptomoneda que revolucionó el sistema financiero global.", "order": 3, "lessons": [{"id": "lesson-3-1", "title": "Fundamentos de Bitcoin", "duration": 25, "order": 1, "content": "# Fundamentos de Bitcoin\n\nBitcoin es la primera implementación exitosa de una criptomoneda descentralizada, creada por <PERSON><PERSON> en 2009. En esta lección, exploraremos sus características fundamentales y su funcionamiento.\n\n## ¿Qué es Bitcoin?\n\nBitcoin es una moneda digital descentralizada que permite realizar pagos peer-to-peer sin necesidad de intermediarios como bancos o procesadores de pago. Funciona en una red blockchain que mantiene un registro público de todas las transacciones.\n\n## Características Clave\n\n### Suministro Limitado\n\n- El suministro máximo de Bitcoin está limitado a 21 millones\n- Esta escasez programada lo convierte en un activo deflacionario\n- Aproximadamente el 90% de todos los bitcoins ya han sido minados\n\n### Descentralización\n\n- No hay una entidad central que controle Bitcoin\n- La red es mantenida por miles de nodos distribuidos globalmente\n- Las decisiones sobre cambios en el protocolo se toman por consenso\n\n### Seguridad\n\n- Utiliza criptografía de curva elíptica para firmas digitales\n- La red Bitcoin nunca ha sido hackeada a nivel de protocolo\n- La seguridad aumenta con el tiempo debido al crecimiento de la potencia de minería\n\n### Pseudoanonimato\n\n- Las transacciones no están vinculadas directamente a identidades reales\n- Todas las transacciones son públicas y rastreables en la blockchain\n- No es completamente anónimo, sino pseudónimo\n\n## Componentes del Ecosistema Bitcoin\n\n### Blockchain de Bitcoin\n\n- Registro público e inmutable de todas las transacciones\n- Organizada en bloques que se añaden aproximadamente cada 10 minutos\n- Cada bloque contiene múltiples transacciones\n\n### Mineros\n\n- Validan transacciones y las agrupan en bloques\n- Compiten para resolver un problema matemático (Proof of Work)\n- Reciben recompensas en forma de nuevos bitcoins y comisiones de transacción\n\n### Nodos\n\n- Mantienen una copia completa de la blockchain\n- Verifican la validez de las transacciones y bloques\n- Propagan transacciones y bloques a otros nodos\n\n### Wallets (Monederos)\n\n- Almacenan las claves privadas que controlan los bitcoins\n- Permiten enviar y recibir bitcoins\n- Tipos: hardware, software, papel, cerebral\n\n## Unidades de Bitcoin\n\n- **1 Bitcoin (BTC)** = Unidad principal\n- **1 Millibitcoin (mBTC)** = 0.001 BTC\n- **1 Microbitcoin (μBTC)** = 0.000001 BTC\n- **1 Satoshi** = 0.00000001 BTC (la unidad más pequeña)"}, {"id": "lesson-3-2", "title": "Transacciones y Direcciones Bitcoin", "duration": 20, "order": 2, "content": "# Transacciones y Direcciones Bitcoin\n\nEn esta lección, aprenderemos cómo funcionan las transacciones de Bitcoin y cómo se generan y utilizan las direcciones.\n\n## Direcciones Bitcoin\n\n### ¿Qué es una dirección Bitcoin?\n\nUna dirección Bitcoin es una cadena alfanumérica que funciona como un \"número de cuenta\" al que otros pueden enviar bitcoins. Por ejemplo:\n\n`**********************************` (la primera dirección Bitcoin creada)\n\n### Tipos de Direcciones\n\n- **P2PKH** (Pay to Public Key Hash): Comienzan con '1' (formato legacy)\n- **P2SH** (Pay to Script Hash): Comienzan con '3' (permite scripts más complejos)\n- **Bech32** (SegWit nativo): Comienzan con 'bc1' (más eficientes en comisiones)\n\n### Generación de Direcciones\n\n1. Se genera un par de claves criptográficas (pública y privada)\n2. La clave pública se procesa a través de funciones hash (SHA-256 y RIPEMD-160)\n3. Se añaden bytes de versión y checksum\n4. Se codifica en Base58Check para crear la dirección\n\n### Seguridad de Direcciones\n\n- Cada dirección debe usarse idealmente solo una vez para maximizar la privacidad\n- La probabilidad de generar una dirección duplicada es prácticamente nula (2^160 posibilidades)\n\n## Transacciones Bitcoin\n\n### Estructura de una Transacción\n\nUna transacción de Bitcoin consiste en:\n\n- **Inputs**: Referencias a transacciones anteriores (UTXOs) que se están gastando\n- **Outputs**: Nuevas salidas que especifican quién recibe los bitcoins\n- **Cantidad**: Valor en bitcoins que se transfiere\n- **Comisión**: Diferencia entre inputs y outputs que se paga a los mineros\n\n### Modelo UTXO\n\nBitcoin utiliza el modelo UTXO (Unspent Transaction Output):\n\n- Los bitcoins existen como \"salidas no gastadas\" de transacciones anteriores\n- Cuando envías bitcoins, estás gastando UTXOs existentes y creando nuevos\n- No existe el concepto de \"saldo\" en la blockchain, solo UTXOs\n\n### Ejemplo de Transacción\n\nAlice quiere enviar 0.5 BTC a Bob:\n\n1. La wallet de Alice busca UTXOs suficientes (p.ej., tiene uno de 0.7 BTC)\n2. Crea una transacción con:\n   - Input: UTXO de 0.7 BTC\n   - Output 1: 0.5 BTC a la dirección de Bob\n   - Output 2: 0.19 BTC de vuelta a una nueva dirección de Alice (cambio)\n   - Comisión: 0.01 BTC (diferencia entre input y outputs)\n3. Firma la transacción con su clave privada\n4. Transmite la transacción a la red Bitcoin\n\n### Confirmaciones\n\n- Una transacción se considera \"confirmada\" cuando se incluye en un bloque\n- Cada bloque adicional añade una confirmación más\n- Generalmente se recomiendan 6 confirmaciones para transacciones de alto valor\n- El tiempo promedio para la primera confirmación es de 10 minutos\n\n### Comisiones de Transacción\n\n- Las comisiones son un incentivo para que los mineros incluyan tu transacción\n- Se calculan típicamente en satoshis por byte (sat/byte)\n- En momentos de alta congestión, las comisiones aumentan\n- Las transacciones con comisiones más altas tienen prioridad"}, {"id": "lesson-3-3", "title": "Minería de Bitcoin", "duration": 20, "order": 3, "content": "# Minería de Bitcoin\n\nLa minería es el proceso por el cual se verifican las transacciones y se añaden nuevos bloques a la blockchain de Bitcoin. En esta lección, exploraremos cómo funciona este proceso fundamental.\n\n## ¿Qué es la Minería de Bitcoin?\n\nLa minería de Bitcoin es el proceso mediante el cual:\n\n1. Se verifican y confirman las transacciones en la red\n2. Se crean nuevos bitcoins según un calendario predefinido\n3. Se asegura la red contra ataques y manipulaciones\n\n## El Proceso de Minería\n\n### 1. Recopilación de Transacciones\n\n- Los mineros seleccionan transacciones del mempool (pool de memoria)\n- Priorizan las transacciones con mayores comisiones\n- Agrupan las transacciones en un bloque candidato\n\n### 2. Creación del Bloque Candidato\n\n- Incluyen un coinbase transaction (que crea nuevos bitcoins como recompensa)\n- Añaden el hash del bloque anterior para mantener la cadena\n- Crean un merkle root de todas las transacciones incluidas\n\n### 3. Proof of Work (PoW)\n\n- Los mineros intentan encontrar un nonce (número arbitrario)\n- Este nonce, combinado con los datos del bloque, debe producir un hash que cumpla con cierta dificultad\n- La dificultad se ajusta cada 2016 bloques (~2 semanas) para mantener un tiempo promedio de 10 minutos por bloque\n\n### 4. Verificación y Propagación\n\n- Cuando un minero encuentra una solución válida, transmite el bloque a la red\n- Otros nodos verifican que el bloque cumple con todas las reglas\n- Si es válido, lo añaden a su copia de la blockchain y comienzan a trabajar en el siguiente bloque\n\n## Recompensas de Minería\n\n### Recompensa de Bloque\n\n- Inicialmente 50 BTC por bloque en 2009\n- Se reduce a la mitad cada 210,000 bloques (~4 años) en un evento llamado \"halving\"\n- Actualmente es de 6.25 BTC por bloque (desde mayo 2020)\n- Próximo halving estimado para 2024, reduciendo la recompensa a 3.125 BTC\n\n### Comisiones de Transacción\n\n- Además de la recompensa de bloque, los mineros reciben las comisiones de todas las transacciones incluidas\n- A medida que la recompensa de bloque disminuye, las comisiones se vuelven más importantes\n\n## Hardware de Minería\n\n### Evolución del Hardware\n\n1. **CPU** (2009-2010): Minería con procesadores de computadoras normales\n2. **GPU** (2010-2013): Tarjetas gráficas más eficientes para minería\n3. **FPGA** (2011-2013): Circuitos integrados programables\n4. **ASIC** (2013-presente): Circuitos integrados diseñados específicamente para minar Bitcoin\n\n### ASICs Modernos\n\n- Extremadamente eficientes para la tarea específica de minería\n- Medidos en TH/s (Terahashes por segundo)\n- Consumen gran cantidad de energía\n\n## Pools de Minería\n\n- Grupos de mineros que combinan su poder de cómputo\n- Comparten recompensas proporcionalmente a la contribución de cada minero\n- Permiten ingresos más estables aunque menores que la minería individual exitosa\n- Ejemplos: F2Pool, Antpool, Foundry USA\n\n## Impacto Ambiental\n\n- La minería de Bitcoin consume cantidades significativas de electricidad\n- Estimaciones actuales: ~110-170 TWh anuales (comparable a países como Suecia o Argentina)\n- Debate sobre fuentes de energía: algunos mineros utilizan energías renovables o excedentes energéticos\n- Iniciativas como el Bitcoin Mining Council promueven la transparencia y sostenibilidad"}, {"id": "lesson-3-4", "title": "El Papel de Bitcoin en el Sistema Financiero", "duration": 15, "order": 4, "content": "# El Papel de Bitcoin en el Sistema Financiero\n\nBitcoin ha evolucionado desde un experimento tecnológico a un activo reconocido globalmente. En esta lección, analizaremos su papel en el sistema financiero actual y su potencial futuro.\n\n## Bitcoin como Reserva de Valor\n\n### Características de Reserva de Valor\n\n- **Escasez**: Suministro limitado a 21 millones\n- **Durabilidad**: Existe digitalmente sin degradación\n- **Fungibilidad**: Cada bitcoin es equivalente a otro\n- **Divisibilidad**: Puede dividirse hasta 8 decimales (satoshis)\n- **Portabilidad**: Puede transferirse globalmente sin restricciones físicas\n- **Verificabilidad**: Fácilmente verificable en la blockchain\n\n### Comparación con el Oro\n\n- A menudo llamado \"oro digital\" o \"oro 2.0\"\n- Ventajas sobre el oro: más divisible, portable y verificable\n- Desventajas: men<PERSON> historial, mayor volatilid<PERSON>, dependencia tecnológica\n\n## Bitcoin como Medio de Pago\n\n### Ventajas como Sistema de Pago\n\n- **Global**: Funciona a través de fronteras sin restricciones\n- **Sin Permisos**: No requiere aprobación de terceros\n- **Irreversible**: Protección contra chargebacks para comerciantes\n- **Bajo Costo**: Especialmente para grandes transferencias internacionales\n\n### Limitaciones como Medio de Pago\n\n- **Escalabilidad**: Capacidad limitada de la capa base (7 TPS)\n- **Volatilidad**: Fluctuaciones de precio dificultan su uso cotidiano\n- **Tiempo de Confirmación**: ~10 minutos para primera confirmación\n- **Experiencia de Usuario**: Más compleja que sistemas tradicionales\n\n### Soluciones de Segunda Capa\n\n- **Lightning Network**: Permite transacciones instantáneas y de bajo costo\n- **Sidechains**: Cadenas paralelas con diferentes características\n\n## Adopción Institucional\n\n### Empresas\n\n- **Tesorería Corporativa**: Empresas como MicroStrategy, Tesla, Square han añadido Bitcoin a sus balances\n- **Servicios Financieros**: PayPal, Visa, Mastercard ofrecen servicios relacionados con Bitcoin\n\n### Inversores Institucionales\n\n- **Fondos de Inversión**: Grayscale Bitcoin Trust, ETFs de Bitcoin\n- **Family Offices y Fondos de Cobertura**: Diversificación de carteras\n\n### Bancos\n\n- **Custodia**: JPMorgan, Goldman Sachs, Morgan Stanley ofrecen servicios de custodia\n- **Trading**: Mesas de operaciones para clientes institucionales\n\n## Adopción por Países\n\n### El Salvador\n\n- Primer país en adoptar Bitcoin como moneda de curso legal (2021)\n- Implementación de wallet nacional (Chivo)\n- Resultados mixtos en adopción ciudadana\n\n### Otros Países\n\n- **Regulación Favorable**: Suiza, Singapur, Portugal\n- **Restricciones**: China, India, Rusia (situación cambiante)\n\n## Desafíos y Críticas\n\n### Regulatorios\n\n- **Cumplimiento KYC/AML**: Tensión con la naturaleza pseudónima\n- **Clasificación Legal**: ¿Commodity, activo, moneda?\n- **Impuestos**: Tratamiento fiscal complejo y variable por jurisdicción\n\n### Técnicos\n\n- **Escalabilidad**: Limitaciones de la capa base\n- **Consumo Energético**: Preocupaciones ambientales\n- **Seguridad**: Responsabilidad del usuario en la custodia\n\n### Económicos\n\n- **Volatilidad**: Fluctuaciones significativas de precio\n- **Distribución**: Concentración en early adopters y grandes tenedores\n\n## El Futuro de Bitcoin\n\n### Posibles Evoluciones\n\n- **Reserva Global Digital**: Activo de reserva para individuos, empresas y eventualmente países\n- **Sistema de Liquidación**: Capa base para un nuevo sistema financiero\n- **Dinero Programable**: Base para aplicaciones financieras avanzadas\n\n### Desarrollos Técnicos\n\n- **Taproot**: Mejora de privacidad y eficiencia (activado en 2021)\n- **Schnorr Signatures**: Firmas más eficientes y privadas\n- **Lightning Network**: Continuo desarrollo de la capa de pagos"}], "quiz": {"id": "quiz-module-3", "title": "Evaluación: Bitcoin", "description": "Comprueba tu comprensión de Bitcoin y su funcionamiento", "passingScore": 70, "questions": [{"id": "q1-m3", "question": "¿Cuál es el suministro máximo de Bitcoin?", "options": ["21 millones", "100 millones", "1 billón", "<PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 0, "explanation": "Bitcoin tiene un suministro máximo fijo de 21 millones. Esta limitación está codificada en el protocolo y es una de las características fundamentales que le da su valor como activo escaso."}, {"id": "q2-m3", "question": "¿Qué es un UTXO en Bitcoin?", "options": ["Una dirección especial para mineros", "Un tipo de wallet hardware", "Una salida de transacción no gastada", "Un nodo de validación"], "correctAnswer": 2, "explanation": "UTXO significa 'Unspent Transaction Output' (Salida de Transacción No Gastada). Es el modelo que utiliza Bitcoin para rastrear la propiedad de los fondos. Cuando recibes bitcoins, lo que realmente recibes son UTXOs que puedes gastar en el futuro."}, {"id": "q3-m3", "question": "¿Qué ocurre aproximadamente cada cuatro años en la red Bitcoin?", "options": ["Actualización completa del protocolo", "Halving (reducción a la mitad de la recompensa de bloque)", "Reinicio de la blockchain", "Cambio en el algoritmo de consenso"], "correctAnswer": 1, "explanation": "Aproximadamente cada cuatro años (o más precisamente, cada 210,000 bloques), ocurre un evento llamado 'halving' donde la recompensa que reciben los mineros por cada bloque se reduce a la mitad. Esto es parte del calendario de emisión predefinido de Bitcoin que eventualmente llevará al límite de 21 millones."}, {"id": "q4-m3", "question": "¿Qué es la Lightning Network?", "options": ["Un nuevo tipo de criptomoneda basada en Bitcoin", "Una red de minería más rápida", "Una solución de segunda capa para pagos rápidos y económicos", "Un tipo de ataque a la red Bitcoin"], "correctAnswer": 2, "explanation": "Lightning Network es una solución de 'segunda capa' construida sobre Bitcoin que permite transacciones casi instantáneas y con comisiones mínimas. Funciona creando canales de pago entre usuarios que pueden realizar múltiples transacciones sin necesidad de registrar cada una en la blockchain principal."}, {"id": "q5-m3", "question": "¿Qué significa que El Salvador adoptó Bitcoin como moneda de curso legal?", "options": ["Que solo se puede usar Bitcoin en El Salvador", "Que los comercios están obligados a aceptar Bitcoin como forma de pago", "Que el gobierno de El Salvador posee la mayoría de los bitcoins", "Que los ciudadanos de El Salvador reciben pagos gubernamentales solo en Bitcoin"], "correctAnswer": 1, "explanation": "Cuando El Salvador adoptó Bitcoin como moneda de curso legal en 2021, significó que los comercios están legalmente obligados a aceptar Bitcoin como forma de pago si tienen la capacidad técnica para hacerlo. Esto no elimina el dólar estadounidense, que sigue siendo la otra moneda oficial del país."}]}}