# Configuración de Firebase Admin para Criptokens

Este documento explica cómo configurar Firebase Admin en el backend de Criptokens para acceder a los datos reales del portafolio de los usuarios.

## Requisitos previos

- Tener acceso al proyecto Firebase de Criptokens
- Tener permisos para crear cuentas de servicio en Firebase

## Pasos para configurar Firebase Admin

### 1. Obtener credenciales de Firebase Admin

1. Ve a la [Consola de Firebase](https://console.firebase.google.com/) y selecciona el proyecto `criptoken-11c3b`.
2. Haz clic en el icono de engranaje (⚙️) junto a "Descripción general del proyecto" y selecciona "Configuración del proyecto".
3. Ve a la pestaña "Cuentas de servicio".
4. Haz clic en "Generar nueva clave privada" para descargar un archivo JSON con las credenciales.

### 2. Configurar las credenciales en el backend

1. Renombra el archivo descargado a `firebase-credentials.json`.
2. Coloca el archivo en la carpeta raíz del backend (`criptokens/backend/`).
3. Asegúrate de que el archivo esté incluido en `.gitignore` para no subirlo al repositorio.

### 3. Verificar la configuración

El backend está configurado para detectar automáticamente el archivo de credenciales y utilizarlo para conectarse a Firestore. Si el archivo no está presente, se utilizará el modo de emulación para desarrollo.

Para verificar que la configuración es correcta:

1. Inicia el servidor backend:
   ```bash
   cd criptokens/backend
   npm run dev
   ```

2. Deberías ver un mensaje en la consola que diga:
   ```
   Firebase Admin inicializado con credenciales de servicio
   ```

3. Prueba la conexión haciendo una petición a la API para obtener el portafolio de un usuario:
   ```
   GET http://localhost:3001/api/guru/portfolio/{userId}
   ```

## Estructura de datos en Firestore

El backend espera que los datos del portafolio estén estructurados de la siguiente manera en Firestore:

```
Portafolio/{userId}
  - assets: Array<{
      id: string,          // ID de la criptomoneda (ej: 'bitcoin')
      symbol: string,      // Símbolo de la criptomoneda (ej: 'BTC')
      name: string,        // Nombre de la criptomoneda (ej: 'Bitcoin')
      amount: number,      // Cantidad de la criptomoneda
      purchasePrice: number, // Precio de compra en USD
      purchaseDate: Timestamp // Fecha de compra
    }>
  - lastUpdated: Timestamp
```

## Modo de desarrollo sin credenciales

Si no tienes acceso a las credenciales de Firebase, el backend utilizará datos simulados para el portafolio. Esto es útil para desarrollo, pero no reflejará los datos reales de los usuarios.

## Solución de problemas

### Error: "Firebase Admin inicializado en modo de emulación"

Esto indica que el archivo de credenciales no se encontró o no es válido. Verifica:

1. Que el archivo `firebase-credentials.json` esté en la carpeta raíz del backend.
2. Que el archivo tenga el formato correcto y contenga todas las credenciales necesarias.

### Error: "Error al inicializar Firebase Admin"

Esto puede indicar un problema con las credenciales o con la conexión a Firebase. Verifica:

1. Que las credenciales sean válidas y correspondan al proyecto correcto.
2. Que tengas conexión a internet.
3. Que el proyecto Firebase esté activo y configurado correctamente.

## Notas adicionales

- El backend está configurado para actualizar automáticamente los precios de las criptomonedas en el portafolio cuando se consulta el Gurú Cripto.
- Los precios actualizados se guardan en Firestore para mantener el portafolio al día.
- Si hay problemas con la conexión a Firestore, el backend utilizará datos simulados como fallback.
