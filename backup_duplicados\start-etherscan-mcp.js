/**
 * Script para iniciar el servidor MCP de Etherscan
 */

const { spawn } = require('child_process');
const path = require('path');
require('dotenv').config();

// Configuración
const PORT = process.env.ETHERSCAN_MCP_PORT || 3104;
const ETHERSCAN_API_KEY = process.env.ETHERSCAN_API_KEY || '**********************************';

// Iniciar el servidor MCP de Etherscan
console.log(`Iniciando servidor MCP de Etherscan en puerto ${PORT}...`);

const etherscanMcp = spawn('node', ['etherscan-mcp-server.js'], {
  env: {
    ...process.env,
    PORT: PORT.toString(),
    ETHERSCAN_API_KEY
  },
  stdio: 'inherit'
});

etherscanMcp.on('error', (error) => {
  console.error(`Error al iniciar el servidor MCP de Etherscan: ${error.message}`);
});

etherscanMcp.on('close', (code) => {
  if (code !== 0) {
    console.error(`El servidor MCP de Etherscan se cerró con código ${code}`);
  } else {
    console.log('Servidor MCP de Etherscan detenido correctamente');
  }
});

// Manejar señales de terminación
process.on('SIGINT', () => {
  console.log('Deteniendo servidor MCP de Etherscan...');
  etherscanMcp.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Deteniendo servidor MCP de Etherscan...');
  etherscanMcp.kill('SIGTERM');
  process.exit(0);
});
