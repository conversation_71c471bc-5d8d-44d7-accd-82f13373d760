"""
OnChain Analysis Agent using Google ADK with MCP Integration
"""
import os
import json
import asyncio
from typing import Dict, Any, List, Optional

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from google.adk.runtime import InvocationContext

# Import MCP tools
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
from mcp_tools.onchain_mcp_tool import OnChainMcpTool
from mcp_tools.utils import extract_crypto_id, extract_timeframe, store_in_session, get_from_session

# Function tools for the agent
async def analyze_onchain_with_mcp(query: str, ctx: InvocationContext) -> str:
    """
    Analyze on-chain data based on user query using MCP tools.

    Args:
        query: User query about cryptocurrency
        ctx: Invocation context

    Returns:
        Analysis result
    """
    try:
        # Extract crypto ID and name from query
        crypto_id = extract_crypto_id(query)
        crypto_name = crypto_id.capitalize()
        
        # Extract timeframe from query
        days, _ = extract_timeframe(query)
        
        # Store in session state
        store_in_session(ctx, "crypto_id", crypto_id)
        store_in_session(ctx, "crypto_name", crypto_name)
        store_in_session(ctx, "days", days)
        
        # Create OnChain MCP tool
        onchain_tool = OnChainMcpTool()
        
        # Determine what kind of analysis to perform based on the query
        analysis_type = determine_analysis_type(query)
        
        # Perform the appropriate analysis
        result = {}
        
        if analysis_type == "whale":
            # Analyze whale activity
            whale_activity = await onchain_tool.analyze_whale_activity(crypto_id, days)
            result["whale_activity"] = whale_activity
            
        elif analysis_type == "token":
            # Analyze token activity
            token_activity = await onchain_tool.analyze_crypto_activity(crypto_id, days)
            result["token_activity"] = token_activity
            
        elif analysis_type == "wallet":
            # Extract wallet address from query or use a popular wallet
            wallet_address = extract_wallet_address(query)
            if not wallet_address:
                # Use a popular wallet for demonstration
                popular_wallets = {
                    "bitcoin": "******************************************",  # Binance
                    "ethereum": "******************************************",  # Binance
                    "binancecoin": "******************************************",  # Binance
                    "cardano": "******************************************",  # Binance
                    "solana": "******************************************"  # Binance
                }
                wallet_address = popular_wallets.get(crypto_id.lower(), "******************************************")
            
            # Analyze wallet transactions
            wallet_analysis = await onchain_tool.analyze_transactions(wallet_address, days)
            result["wallet_analysis"] = wallet_analysis
            
        else:
            # Comprehensive analysis
            # 1. Get token info
            token_info = await onchain_tool.get_crypto_token_info(crypto_id)
            result["token_info"] = token_info
            
            # 2. Analyze token activity
            token_activity = await onchain_tool.analyze_crypto_activity(crypto_id, days)
            result["token_activity"] = token_activity
            
            # 3. Get whale information
            whale_info = await onchain_tool.get_crypto_whales(crypto_id, 5)
            result["whale_info"] = whale_info
            
            # 4. Get gas price
            gas_price = await onchain_tool.get_gas_price()
            result["gas_price"] = gas_price
        
        # Store results in session state
        store_in_session(ctx, "onchain_analysis", result)
        
        # Close the MCP session
        await onchain_tool.close_session()
        
        # Return structured data for the LLM to format
        return json.dumps(result)
    except Exception as e:
        print(f"Error in analyze_onchain_with_mcp: {e}")
        # Return error message
        return json.dumps({
            "error": f"Error analyzing on-chain data: {str(e)}",
            "crypto_id": crypto_id,
            "crypto_name": crypto_name,
            "days": days
        })

def determine_analysis_type(query: str) -> str:
    """
    Determine the type of analysis to perform based on the query.
    
    Args:
        query: User query
        
    Returns:
        Analysis type (whale, token, wallet, comprehensive)
    """
    query_lower = query.lower()
    
    if any(keyword in query_lower for keyword in ["whale", "ballena", "holder", "tenedor", "grande"]):
        return "whale"
    
    if any(keyword in query_lower for keyword in ["token", "contrato", "erc20", "erc-20"]):
        return "token"
    
    if any(keyword in query_lower for keyword in ["wallet", "cartera", "dirección", "address"]):
        return "wallet"
    
    return "comprehensive"

def extract_wallet_address(query: str) -> Optional[str]:
    """
    Extract wallet address from query.
    
    Args:
        query: User query
        
    Returns:
        Wallet address if found, None otherwise
    """
    # Simple regex to find Ethereum addresses
    import re
    matches = re.findall(r'0x[a-fA-F0-9]{40}', query)
    
    if matches:
        return matches[0]
    
    return None

# Create the on-chain analysis agent with MCP integration
onchain_agent_mcp = LlmAgent(
    name="onchain_analysis_agent",
    model="gemini-1.5-pro",
    description="Analyzes on-chain data for cryptocurrencies with MCP integration",
    instruction="""
    You are an on-chain analysis expert for cryptocurrencies. Your task is to:

    1. Analyze the on-chain data provided to you
    2. Identify key metrics and trends from the blockchain
    3. Explain what the on-chain data indicates about the cryptocurrency
    4. Highlight important wallet activities, token movements, and whale behaviors
    5. Provide a clear, concise on-chain analysis summary

    When responding:
    - Be specific about what the on-chain metrics mean
    - Explain the significance of whale movements and token transfers
    - Highlight any unusual or noteworthy on-chain activity
    - Provide a conclusion about the overall on-chain health and activity

    The on-chain data will be provided to you as JSON data.
    """,
    tools=[FunctionTool(func=analyze_onchain_with_mcp)],
    output_key="onchain_analysis"
)

# For testing the agent directly
if __name__ == "__main__":
    from google.adk.runtime import Runtime
    
    async def main():
        runtime = Runtime()
        session = runtime.new_session()
        
        # Test the agent with a query
        response = await onchain_agent_mcp.run_async(
            session=session,
            query="Analyze on-chain data for Bitcoin for the last week"
        )
        
        print(response)
    
    asyncio.run(main())
