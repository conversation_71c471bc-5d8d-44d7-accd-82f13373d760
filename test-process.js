/**
 * Script para probar la gestión de procesos
 */
const { spawn } = require('child_process');

// Iniciar un servidor HTTP simple
console.log('Iniciando un servidor HTTP simple en el puerto 12345...');

const server = spawn('node', ['simple-server.js'], {
  stdio: 'pipe',
  shell: true
});

server.stdout.on('data', (data) => {
  console.log(`[Servidor] ${data.toString().trim()}`);
});

server.stderr.on('data', (data) => {
  console.error(`[Servidor ERROR] ${data.toString().trim()}`);
});

// Esperar 10 segundos y luego detener el servidor
setTimeout(() => {
  console.log('Deteniendo el servidor...');
  server.kill();
  console.log('Servidor detenido.');
}, 10000);

// Manejar la terminación del script
process.on('SIGINT', () => {
  console.log('Deteniendo el servidor debido a SIGINT...');
  server.kill();
  process.exit(0);
});
