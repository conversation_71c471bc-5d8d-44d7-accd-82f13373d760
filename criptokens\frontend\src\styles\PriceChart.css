.price-chart-container {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.price-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}

.price-chart-header h3 {
  font-size: 1.25rem;
  margin: 0;
  color: #333;
}

.time-range-selector {
  display: flex;
  gap: 0.5rem;
}

.time-range-selector button {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-range-selector button:hover {
  background-color: #f5f5f5;
}

.time-range-selector button.active {
  background-color: #0084ff;
  color: white;
  border-color: #0084ff;
}

.price-chart {
  height: 300px;
  position: relative;
}

.price-chart-loading,
.price-chart-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.price-chart-error {
  color: #d32f2f;
}

@media (max-width: 768px) {
  .price-chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .time-range-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .time-range-selector button {
    flex: 1;
    text-align: center;
    padding: 0.25rem 0;
  }
}
