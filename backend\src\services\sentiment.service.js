/**
 * Servicio para análisis de sentimiento
 */

/**
 * <PERSON>cula el índice de sentimiento del mercado
 * @returns {Promise<number>} - Índice de sentimiento del mercado (0-100)
 */
async function calculateMarketSentiment() {
  try {
    // Simulación de índice de sentimiento
    // En una implementación real, esto analizaría datos de redes sociales, noticias, etc.
    const sentiment = Math.floor(Math.random() * 100);
    return sentiment;
  } catch (error) {
    console.error('Error al calcular el sentimiento del mercado:', error);
    return 50; // Valor neutral por defecto
  }
}

/**
 * Analiza el sentimiento de un texto de noticia
 * @param {string} text - Texto a analizar
 * @returns {Promise<Object>} - Resultado del análisis de sentimiento
 */
async function analyzeNewsSentiment(text) {
  try {
    // En una implementación real, esto utilizaría un servicio de NLP como OpenAI
    // Para esta simulación, analizaremos palabras clave
    
    const positiveWords = [
      'bullish', 'surge', 'rally', 'gain', 'rise', 'soar', 'jump', 'positive', 
      'growth', 'adoption', 'partnership', 'breakthrough', 'success', 'innovation',
      'opportunity', 'progress', 'support', 'upgrade', 'improve', 'launch'
    ];
    
    const negativeWords = [
      'bearish', 'crash', 'drop', 'fall', 'plunge', 'decline', 'negative', 
      'loss', 'risk', 'concern', 'warning', 'hack', 'scam', 'fraud', 'ban',
      'regulation', 'restriction', 'investigation', 'problem', 'issue'
    ];
    
    // Convertir a minúsculas para comparación
    const lowerText = text.toLowerCase();
    
    // Contar palabras positivas y negativas
    let positiveCount = 0;
    let negativeCount = 0;
    
    positiveWords.forEach(word => {
      const regex = new RegExp('\\b' + word + '\\b', 'gi');
      const matches = lowerText.match(regex);
      if (matches) {
        positiveCount += matches.length;
      }
    });
    
    negativeWords.forEach(word => {
      const regex = new RegExp('\\b' + word + '\\b', 'gi');
      const matches = lowerText.match(regex);
      if (matches) {
        negativeCount += matches.length;
      }
    });
    
    // Calcular puntuación de sentimiento (-1 a 1)
    let score = 0;
    
    if (positiveCount > 0 || negativeCount > 0) {
      score = (positiveCount - negativeCount) / (positiveCount + negativeCount);
    }
    
    // Limitar el rango
    score = Math.max(-1, Math.min(1, score));
    
    // Determinar la etiqueta de sentimiento
    let label = 'neutral';
    if (score > 0.2) {
      label = 'positive';
    } else if (score < -0.2) {
      label = 'negative';
    }
    
    return {
      score,
      label,
      positiveCount,
      negativeCount
    };
  } catch (error) {
    console.error('Error al analizar el sentimiento del texto:', error);
    return {
      score: 0,
      label: 'neutral',
      positiveCount: 0,
      negativeCount: 0
    };
  }
}

module.exports = {
  calculateMarketSentiment,
  analyzeNewsSentiment
};
