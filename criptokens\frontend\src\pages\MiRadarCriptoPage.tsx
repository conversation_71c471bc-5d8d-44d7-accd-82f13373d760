import React from 'react';
import { Link } from 'react-router-dom';
import MiRadarCriptoComponent from '../components/MiRadarCripto';
import { AuthProvider } from '../context/NewAuthContext';
import '../styles/MiRadarCripto.css';

const MiRadarCriptoPage: React.FC = () => {
  return (
    <AuthProvider>
      <div className="standalone-page">
        <header className="standalone-header">
          <Link to="/" className="back-link">
            ← Volver al Dashboard
          </Link>
          <h1>Mi Radar Cripto</h1>
        </header>
        <main className="standalone-content">
          <MiRadarCriptoComponent />
        </main>
      </div>
    </AuthProvider>
  );
};

export default MiRadarCriptoPage;
