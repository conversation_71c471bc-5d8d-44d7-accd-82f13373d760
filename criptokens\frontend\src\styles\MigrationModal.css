.migration-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.migration-modal-content {
  background-color: #1a1a2e;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  border: 1px solid #2a2a4a;
  overflow: hidden;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.migration-modal-header {
  background-color: #16213e;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #2a2a4a;
}

.migration-modal-header h3 {
  margin: 0;
  color: #fff;
  font-size: 1.2rem;
}

.migration-modal-close {
  background: none;
  border: none;
  color: #aaa;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s;
}

.migration-modal-close:hover {
  color: #fff;
}

.migration-modal-body {
  padding: 20px;
  color: #ddd;
}

.migration-modal-body p {
  margin-bottom: 15px;
  line-height: 1.5;
}

.migration-modal-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  border-top: 1px solid #2a2a4a;
}

.migration-btn-primary,
.migration-btn-secondary {
  padding: 8px 16px;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.migration-btn-primary {
  background-color: #0f3460;
  color: white;
  border: none;
}

.migration-btn-primary:hover:not(:disabled) {
  background-color: #1a4b8c;
}

.migration-btn-secondary {
  background-color: transparent;
  color: #ddd;
  border: 1px solid #444;
}

.migration-btn-secondary:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.migration-btn-primary:disabled,
.migration-btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.migration-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.migration-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #0f3460;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.migration-error {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 4px solid #e74c3c;
  padding: 10px 15px;
  margin: 15px 0;
  border-radius: 4px;
}

.migration-success {
  background-color: rgba(0, 255, 0, 0.1);
  border-left: 4px solid #2ecc71;
  padding: 10px 15px;
  margin: 15px 0;
  border-radius: 4px;
}
