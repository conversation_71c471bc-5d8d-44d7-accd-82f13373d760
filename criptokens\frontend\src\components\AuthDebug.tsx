import { useState } from 'react';
import { auth, db } from '../firebase-init';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';

const AuthDebug = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testAuth = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      // Verificar el estado actual de autenticación
      const currentUser = auth.currentUser;
      console.log('Estado actual de autenticación:', currentUser);
      
      // Intentar iniciar sesión
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      console.log('Inicio de sesión exitoso:', user);
      
      // Intentar obtener datos del usuario de Firestore
      try {
        const userDoc = await getDoc(doc(db, 'Users', user.uid));
        if (userDoc.exists()) {
          console.log('Datos del usuario encontrados:', userDoc.data());
          setResult({
            success: true,
            user: user,
            userData: userDoc.data()
          });
        } else {
          console.log('No se encontraron datos del usuario en Firestore');
          setResult({
            success: true,
            user: user,
            userData: null,
            message: 'No se encontraron datos del usuario en Firestore'
          });
        }
      } catch (firestoreError) {
        console.error('Error al obtener datos de Firestore:', firestoreError);
        setResult({
          success: true,
          user: user,
          firestoreError: firestoreError
        });
      }
    } catch (error: any) {
      console.error('Error en la prueba de autenticación:', error);
      setResult({
        success: false,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '5px', margin: '20px 0' }}>
      <h3>Depuración de Autenticación</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <label style={{ display: 'block', marginBottom: '5px' }}>
          Email:
          <input 
            type="email" 
            value={email} 
            onChange={(e) => setEmail(e.target.value)} 
            style={{ marginLeft: '10px', padding: '5px' }}
          />
        </label>
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <label style={{ display: 'block', marginBottom: '5px' }}>
          Contraseña:
          <input 
            type="password" 
            value={password} 
            onChange={(e) => setPassword(e.target.value)} 
            style={{ marginLeft: '10px', padding: '5px' }}
          />
        </label>
      </div>
      
      <button 
        onClick={testAuth} 
        disabled={loading}
        style={{ 
          padding: '8px 15px', 
          backgroundColor: '#4CAF50', 
          color: 'white', 
          border: 'none', 
          borderRadius: '4px', 
          cursor: loading ? 'not-allowed' : 'pointer' 
        }}
      >
        {loading ? 'Probando...' : 'Probar Autenticación'}
      </button>
      
      {result && (
        <div style={{ marginTop: '20px' }}>
          <h4>Resultado:</h4>
          <pre style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '300px'
          }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default AuthDebug;
