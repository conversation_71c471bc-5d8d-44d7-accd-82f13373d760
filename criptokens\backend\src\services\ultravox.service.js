const axios = require('axios');
const config = require('../config');
const logger = require('../utils/logger');

// Configuración de la API de Ultravox
const ULTRAVOX_API_URL = config.ultravox.apiUrl;
const DEFAULT_VOICE_ID = config.ultravox.defaultVoiceId;
const SPANISH_VOICE_ID = config.ultravox.spanishVoiceId;

/**
 * Servicio para interactuar con la API de Ultravox
 */
class UltravoxService {
  constructor() {
    this.apiKey = process.env.ULTRAVOX_API_KEY || '';
    this.credits = 0;
    this.usageHistory = [];

    console.log('Ultravox API Key configurada:', this.apiKey ? 'Sí (longitud: ' + this.apiKey.length + ')' : 'No');

    // Configuración por defecto para las llamadas a la API
    this.axiosConfig = {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      }
    };
  }

  /**
   * Actualiza la clave de API
   * @param {string} apiKey - Nueva clave de API
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey;
    this.axiosConfig.headers['X-API-Key'] = apiKey;
    return this.checkApiKey();
  }

  /**
   * Verifica si la clave de API es válida
   * @returns {Promise<boolean>} - True si la clave es válida
   */
  async checkApiKey() {
    try {
      console.log('Verificando clave de API de Ultravox...');
      console.log('URL:', `${ULTRAVOX_API_URL}/voices`);
      console.log('Headers:', JSON.stringify(this.axiosConfig.headers));

      // Si no hay API key configurada, retornar false inmediatamente
      if (!this.apiKey || this.apiKey.trim() === '') {
        console.log('No hay API key configurada para Ultravox');
        return false;
      }

      const response = await axios.get(`${ULTRAVOX_API_URL}/voices`, this.axiosConfig);
      console.log('Respuesta de Ultravox:', response.status, response.statusText);

      return response.status === 200;
    } catch (error) {
      console.error('Error completo verificando la clave de API de Ultravox:', error);

      // Registrar detalles adicionales del error para diagnóstico
      if (error.response) {
        // La solicitud fue realizada y el servidor respondió con un código de estado
        console.error('Datos de respuesta de error:', error.response.data);
        console.error('Estado de respuesta de error:', error.response.status);
        console.error('Cabeceras de respuesta de error:', error.response.headers);
      } else if (error.request) {
        // La solicitud fue realizada pero no se recibió respuesta
        console.error('Solicitud sin respuesta:', error.request);
      } else {
        // Algo ocurrió al configurar la solicitud que desencadenó un error
        console.error('Error de configuración:', error.message);
      }

      logger.error('Error verificando la clave de API de Ultravox:', error.message);
      return false;
    }
  }

  /**
   * Obtiene el saldo de créditos disponibles
   * @returns {Promise<number>} - Saldo de créditos
   */
  async getCredits() {
    try {
      // Nota: Esta es una implementación simulada ya que Ultravox no tiene un endpoint específico para créditos
      // En una implementación real, se consultaría el endpoint correspondiente
      const response = await axios.get(`${ULTRAVOX_API_URL}/account`, this.axiosConfig);
      this.credits = response.data.credits || 0;
      return this.credits;
    } catch (error) {
      logger.error('Error obteniendo créditos de Ultravox:', error.message);
      return this.credits;
    }
  }

  /**
   * Crea una nueva llamada de voz
   * @param {string} systemPrompt - Prompt del sistema para el agente
   * @param {string} language - Idioma de la llamada (es, en, etc.)
   * @param {object} options - Opciones adicionales
   * @returns {Promise<object>} - Datos de la llamada creada
   */
  async createCall(systemPrompt, language = 'es', options = {}) {
    try {
      // Seleccionar la voz según el idioma
      const voiceId = language.startsWith('es') ? SPANISH_VOICE_ID : DEFAULT_VOICE_ID;

      const callData = {
        systemPrompt,
        model: 'fixie-ai/ultravox-70B',
        voice: voiceId,
        ...options
      };

      const response = await axios.post(`${ULTRAVOX_API_URL}/calls`, callData, this.axiosConfig);

      // Registrar el uso para seguimiento de créditos
      this._trackUsage('create_call', 1);

      return response.data;
    } catch (error) {
      logger.error('Error creando llamada en Ultravox:', error.message);
      throw new Error(`Error al crear llamada de voz: ${error.message}`);
    }
  }

  /**
   * Envía un mensaje a una llamada existente
   * @param {string} callId - ID de la llamada
   * @param {string} message - Mensaje a enviar
   * @returns {Promise<object>} - Respuesta del servidor
   */
  async sendMessage(callId, message) {
    try {
      const messageData = {
        role: 'user',
        content: message
      };

      const response = await axios.post(
        `${ULTRAVOX_API_URL}/calls/${callId}/messages`,
        messageData,
        this.axiosConfig
      );

      // Registrar el uso para seguimiento de créditos
      this._trackUsage('send_message', 1);

      return response.data;
    } catch (error) {
      logger.error(`Error enviando mensaje a la llamada ${callId}:`, error.message);
      throw new Error(`Error al enviar mensaje: ${error.message}`);
    }
  }

  /**
   * Obtiene los mensajes de una llamada
   * @param {string} callId - ID de la llamada
   * @returns {Promise<Array>} - Lista de mensajes
   */
  async getMessages(callId) {
    try {
      const response = await axios.get(
        `${ULTRAVOX_API_URL}/calls/${callId}/messages`,
        this.axiosConfig
      );
      return response.data.messages || [];
    } catch (error) {
      logger.error(`Error obteniendo mensajes de la llamada ${callId}:`, error.message);
      throw new Error(`Error al obtener mensajes: ${error.message}`);
    }
  }

  /**
   * Finaliza una llamada
   * @param {string} callId - ID de la llamada
   * @returns {Promise<object>} - Respuesta del servidor
   */
  async endCall(callId) {
    try {
      const response = await axios.post(
        `${ULTRAVOX_API_URL}/calls/${callId}/hangup`,
        {},
        this.axiosConfig
      );
      return response.data;
    } catch (error) {
      logger.error(`Error finalizando la llamada ${callId}:`, error.message);
      throw new Error(`Error al finalizar llamada: ${error.message}`);
    }
  }

  /**
   * Crea un corpus de conocimiento
   * @param {string} name - Nombre del corpus
   * @param {string} description - Descripción del corpus
   * @returns {Promise<object>} - Datos del corpus creado
   */
  async createCorpus(name, description = '') {
    try {
      const corpusData = {
        name,
        description
      };

      const response = await axios.post(
        `${ULTRAVOX_API_URL}/corpora`,
        corpusData,
        this.axiosConfig
      );

      return response.data;
    } catch (error) {
      logger.error('Error creando corpus en Ultravox:', error.message);
      throw new Error(`Error al crear corpus: ${error.message}`);
    }
  }

  /**
   * Añade una fuente web a un corpus
   * @param {string} corpusId - ID del corpus
   * @param {string} url - URL a rastrear
   * @returns {Promise<object>} - Datos de la fuente creada
   */
  async addWebSource(corpusId, url) {
    try {
      const sourceData = {
        url
      };

      const response = await axios.post(
        `${ULTRAVOX_API_URL}/corpora/${corpusId}/sources`,
        sourceData,
        this.axiosConfig
      );

      return response.data;
    } catch (error) {
      logger.error(`Error añadiendo fuente web al corpus ${corpusId}:`, error.message);
      throw new Error(`Error al añadir fuente web: ${error.message}`);
    }
  }

  /**
   * Consulta un corpus de conocimiento
   * @param {string} corpusId - ID del corpus
   * @param {string} query - Consulta a realizar
   * @param {number} maxResults - Número máximo de resultados
   * @returns {Promise<Array>} - Resultados de la consulta
   */
  async queryCorpus(corpusId, query, maxResults = 5) {
    try {
      const queryData = {
        query,
        max_results: maxResults
      };

      const response = await axios.post(
        `${ULTRAVOX_API_URL}/corpora/${corpusId}/query`,
        queryData,
        this.axiosConfig
      );

      // Registrar el uso para seguimiento de créditos
      this._trackUsage('query_corpus', 1);

      return response.data.results || [];
    } catch (error) {
      logger.error(`Error consultando el corpus ${corpusId}:`, error.message);
      throw new Error(`Error al consultar corpus: ${error.message}`);
    }
  }

  /**
   * Registra el uso de la API para seguimiento de créditos
   * @param {string} operation - Operación realizada
   * @param {number} credits - Créditos consumidos
   * @private
   */
  _trackUsage(operation, credits) {
    const usage = {
      operation,
      credits,
      timestamp: new Date().toISOString()
    };

    this.usageHistory.push(usage);
    this.credits -= credits;

    // Asegurar que los créditos no sean negativos
    if (this.credits < 0) this.credits = 0;
  }

  /**
   * Obtiene el historial de uso
   * @returns {Array} - Historial de uso
   */
  getUsageHistory() {
    return this.usageHistory;
  }

  /**
   * Añade créditos a la cuenta
   * @param {number} amount - Cantidad de créditos a añadir
   */
  addCredits(amount) {
    this.credits += amount;
    logger.info(`Se añadieron ${amount} créditos. Nuevo saldo: ${this.credits}`);
    return this.credits;
  }
}

module.exports = new UltravoxService();
