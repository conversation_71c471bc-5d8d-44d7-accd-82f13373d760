/**
 * Servidor Playwright-MCP para Criptokens
 * 
 * Este servidor proporciona capacidades de automatización de navegador utilizando Playwright
 * a través del Protocolo de Contexto de Modelo (MCP).
 */

const express = require('express');
const { chromium } = require('playwright');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Configuración
const PORT = process.env.PORT || 3103;
const SCREENSHOT_DIR = path.join(__dirname, '../screenshots');
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutos

// Crear directorio para capturas de pantalla si no existe
if (!fs.existsSync(SCREENSHOT_DIR)) {
  fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
}

// Inicializar Express
const app = express();
app.use(express.json());
app.use(cors());
app.use('/screenshots', express.static(SCREENSHOT_DIR));

// Almacenamiento de sesiones
const sessions = new Map();

// Función para limpiar sesiones antiguas
function cleanupSessions() {
  const now = Date.now();
  for (const [sessionId, session] of sessions.entries()) {
    if (now - session.lastAccessed > SESSION_TIMEOUT) {
      console.log(`Cerrando sesión inactiva: ${sessionId}`);
      if (session.browser) {
        try {
          session.browser.close();
        } catch (error) {
          console.error(`Error al cerrar el navegador para la sesión ${sessionId}:`, error);
        }
      }
      sessions.delete(sessionId);
    }
  }
}

// Programar limpieza de sesiones cada 5 minutos
setInterval(cleanupSessions, 5 * 60 * 1000);

/**
 * Obtiene o crea una sesión de navegador
 * @param {string} sessionId - ID de sesión
 * @returns {Promise<Object>} - Objeto de sesión con navegador y página
 */
async function getOrCreateSession(sessionId = null) {
  // Si no se proporciona un ID de sesión, crear uno nuevo
  if (!sessionId) {
    sessionId = uuidv4();
  }

  // Verificar si la sesión ya existe
  if (sessions.has(sessionId)) {
    const session = sessions.get(sessionId);
    session.lastAccessed = Date.now();
    return { sessionId, ...session };
  }

  // Crear una nueva sesión
  console.log(`Creando nueva sesión: ${sessionId}`);
  try {
    const browser = await chromium.launch({
      headless: true,
      args: ['--disable-web-security', '--disable-features=IsolateOrigins,site-per-process']
    });
    const context = await browser.newContext({
      viewport: { width: 1280, height: 800 },
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    });
    const page = await context.newPage();

    // Crear la sesión
    const session = {
      browser,
      context,
      page,
      history: [],
      currentIndex: -1,
      lastAccessed: Date.now()
    };

    // Almacenar la sesión
    sessions.set(sessionId, session);

    return { sessionId, ...session };
  } catch (error) {
    console.error(`Error al crear sesión ${sessionId}:`, error);
    throw error;
  }
}

/**
 * Extrae texto relevante de una página web
 * @param {Page} page - Instancia de página de Playwright
 * @returns {Promise<Object>} - Objeto con texto extraído y metadatos
 */
async function extractPageContent(page) {
  try {
    // Extraer metadatos básicos
    const title = await page.title();
    const url = page.url();

    // Extraer texto principal utilizando selectores inteligentes
    const mainContent = await page.evaluate(() => {
      // Función para limpiar el texto
      const cleanText = (text) => {
        return text
          .replace(/\\s+/g, ' ')
          .replace(/\\n+/g, '\\n')
          .trim();
      };

      // Selectores para contenido principal (en orden de prioridad)
      const mainSelectors = [
        'main',
        'article',
        '#content',
        '.content',
        '.main-content',
        '.article',
        '.post',
        '.entry-content'
      ];

      // Selectores para contenido a excluir
      const excludeSelectors = [
        'nav',
        'header',
        'footer',
        '.nav',
        '.menu',
        '.header',
        '.footer',
        '.sidebar',
        '.ad',
        '.advertisement',
        '.cookie-banner',
        '.popup',
        '.modal'
      ];

      // Intentar encontrar el contenido principal
      let mainElement = null;
      for (const selector of mainSelectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent.trim().length > 100) {
          mainElement = element;
          break;
        }
      }

      // Si no se encuentra un contenedor principal, usar el body
      if (!mainElement) {
        mainElement = document.body;
      }

      // Clonar el elemento para no modificar el DOM original
      const contentElement = mainElement.cloneNode(true);

      // Eliminar elementos no deseados
      excludeSelectors.forEach(selector => {
        const elements = contentElement.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      });

      // Extraer encabezados
      const headings = Array.from(contentElement.querySelectorAll('h1, h2, h3, h4, h5, h6'))
        .map(h => ({
          level: parseInt(h.tagName.substring(1)),
          text: cleanText(h.textContent)
        }))
        .filter(h => h.text.length > 0);

      // Extraer párrafos
      const paragraphs = Array.from(contentElement.querySelectorAll('p'))
        .map(p => cleanText(p.textContent))
        .filter(p => p.length > 0);

      // Extraer listas
      const lists = Array.from(contentElement.querySelectorAll('ul, ol'))
        .map(list => {
          const items = Array.from(list.querySelectorAll('li'))
            .map(li => cleanText(li.textContent))
            .filter(li => li.length > 0);
          return {
            type: list.tagName.toLowerCase(),
            items
          };
        })
        .filter(list => list.items.length > 0);

      // Extraer tablas
      const tables = Array.from(contentElement.querySelectorAll('table'))
        .map(table => {
          const headers = Array.from(table.querySelectorAll('th'))
            .map(th => cleanText(th.textContent));
          
          const rows = Array.from(table.querySelectorAll('tr'))
            .map(tr => {
              const cells = Array.from(tr.querySelectorAll('td'))
                .map(td => cleanText(td.textContent));
              return cells.length > 0 ? cells : null;
            })
            .filter(row => row !== null);

          return {
            headers,
            rows
          };
        })
        .filter(table => table.rows.length > 0);

      // Extraer enlaces importantes
      const links = Array.from(contentElement.querySelectorAll('a[href]'))
        .map(a => ({
          text: cleanText(a.textContent),
          href: a.href
        }))
        .filter(link => link.text.length > 0 && link.href.startsWith('http'));

      // Extraer texto plano (como fallback)
      const plainText = cleanText(contentElement.textContent);

      // Detectar si es una página de criptomonedas y extraer datos específicos
      const isCryptoPage = /crypto|bitcoin|ethereum|coin|token|blockchain|defi/i.test(plainText);
      let cryptoData = null;

      if (isCryptoPage) {
        // Buscar patrones de precio
        const priceMatch = plainText.match(/\\$[\\d,]+(\\.[\\d]+)?/);
        const price = priceMatch ? priceMatch[0] : null;

        // Buscar patrones de cambio de precio
        const changeMatch = plainText.match(/([+-]\\d+(\\.\\d+)?%)/);
        const priceChange = changeMatch ? changeMatch[0] : null;

        // Buscar patrones de capitalización de mercado
        const marketCapMatch = plainText.match(/market\\s*cap\\s*[:\\s]*\\$[\\d,]+(\\.[\\d]+)?\\s*(billion|million|trillion)?/i);
        const marketCap = marketCapMatch ? marketCapMatch[0] : null;

        if (price || priceChange || marketCap) {
          cryptoData = { price, priceChange, marketCap };
        }
      }

      return {
        headings,
        paragraphs,
        lists,
        tables,
        links,
        plainText,
        cryptoData
      };
    });

    // Generar un resumen del contenido
    const summary = generateSummary(mainContent);

    return {
      title,
      url,
      content: mainContent,
      summary
    };
  } catch (error) {
    console.error('Error al extraer contenido de la página:', error);
    return {
      title: 'Error al extraer contenido',
      url: page.url(),
      content: {
        plainText: 'No se pudo extraer el contenido de la página.'
      },
      summary: 'No se pudo generar un resumen del contenido.'
    };
  }
}

/**
 * Genera un resumen del contenido extraído
 * @param {Object} content - Contenido extraído de la página
 * @returns {string} - Resumen generado
 */
function generateSummary(content) {
  try {
    // Extraer los primeros encabezados
    const headings = content.headings.slice(0, 3).map(h => h.text).join('. ');
    
    // Extraer los primeros párrafos
    const paragraphs = content.paragraphs.slice(0, 2).join(' ');
    
    // Combinar para crear un resumen
    let summary = '';
    
    if (headings) {
      summary += headings + '. ';
    }
    
    if (paragraphs) {
      summary += paragraphs;
    }
    
    // Si hay datos de criptomonedas, añadirlos al resumen
    if (content.cryptoData) {
      const { price, priceChange, marketCap } = content.cryptoData;
      let cryptoSummary = 'Datos de criptomoneda detectados: ';
      
      if (price) cryptoSummary += `Precio: ${price}. `;
      if (priceChange) cryptoSummary += `Cambio: ${priceChange}. `;
      if (marketCap) cryptoSummary += `Capitalización de mercado: ${marketCap}.`;
      
      summary += ' ' + cryptoSummary;
    }
    
    // Si el resumen es demasiado corto, usar el texto plano
    if (summary.length < 100 && content.plainText) {
      summary = content.plainText.substring(0, 300) + '...';
    }
    
    return summary;
  } catch (error) {
    console.error('Error al generar resumen:', error);
    return 'No se pudo generar un resumen del contenido.';
  }
}

// Endpoint para verificar el estado del servidor
app.get('/status', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Playwright MCP Server running'
  });
});

// Endpoint para crear una nueva sesión
app.post('/session', async (req, res) => {
  try {
    const { sessionId } = await getOrCreateSession();
    res.json({
      sessionId,
      message: 'Sesión creada correctamente'
    });
  } catch (error) {
    console.error('Error al crear sesión:', error);
    res.status(500).json({
      error: 'Error al crear sesión',
      details: error.message
    });
  }
});

// Endpoint para navegar a una URL
app.post('/browse', async (req, res) => {
  const { url, sessionId } = req.body;
  
  if (!url) {
    return res.status(400).json({
      error: 'Se requiere una URL'
    });
  }
  
  try {
    // Obtener o crear sesión
    const session = await getOrCreateSession(sessionId);
    const { page, history } = session;
    
    // Navegar a la URL
    console.log(`Navegando a: ${url}`);
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
    
    // Esperar a que la página cargue completamente
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Timeout esperando networkidle, continuando de todos modos');
    });
    
    // Extraer contenido de la página
    const pageContent = await extractPageContent(page);
    
    // Tomar captura de pantalla
    const screenshotPath = path.join(SCREENSHOT_DIR, `${session.sessionId}_${Date.now()}.png`);
    await page.screenshot({ path: screenshotPath, fullPage: false });
    
    // Convertir la captura a base64
    const screenshotBuffer = fs.readFileSync(screenshotPath);
    const screenshot = screenshotBuffer.toString('base64');
    
    // Actualizar historial
    if (session.currentIndex < history.length - 1) {
      // Si estamos en medio del historial, eliminar entradas futuras
      history.splice(session.currentIndex + 1);
    }
    history.push(url);
    session.currentIndex = history.length - 1;
    
    // Actualizar timestamp de último acceso
    session.lastAccessed = Date.now();
    
    // Devolver respuesta
    res.json({
      url: page.url(),
      title: pageContent.title,
      content: pageContent.content,
      summary: pageContent.summary,
      screenshot,
      sessionId: session.sessionId,
      historyIndex: session.currentIndex,
      historyLength: history.length
    });
  } catch (error) {
    console.error('Error al navegar a la URL:', error);
    res.status(500).json({
      error: 'Error al navegar a la URL',
      details: error.message
    });
  }
});

// Endpoint para tomar una captura de pantalla
app.post('/screenshot', async (req, res) => {
  const { sessionId, fullPage = false } = req.body;
  
  try {
    // Obtener sesión
    const session = await getOrCreateSession(sessionId);
    const { page } = session;
    
    // Tomar captura de pantalla
    const screenshotPath = path.join(SCREENSHOT_DIR, `${session.sessionId}_${Date.now()}.png`);
    await page.screenshot({ path: screenshotPath, fullPage });
    
    // Convertir la captura a base64
    const screenshotBuffer = fs.readFileSync(screenshotPath);
    const screenshot = screenshotBuffer.toString('base64');
    
    // Actualizar timestamp de último acceso
    session.lastAccessed = Date.now();
    
    // Devolver respuesta
    res.json({
      screenshot,
      url: page.url(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error al tomar captura de pantalla:', error);
    res.status(500).json({
      error: 'Error al tomar captura de pantalla',
      details: error.message
    });
  }
});

// Endpoint para hacer clic en un elemento
app.post('/click', async (req, res) => {
  const { sessionId, selector, text, index = 0 } = req.body;
  
  if (!sessionId) {
    return res.status(400).json({
      error: 'Se requiere un ID de sesión'
    });
  }
  
  if (!selector && !text) {
    return res.status(400).json({
      error: 'Se requiere un selector o texto para hacer clic'
    });
  }
  
  try {
    // Obtener sesión
    const session = await getOrCreateSession(sessionId);
    const { page, history } = session;
    
    // Hacer clic en el elemento
    if (selector) {
      await page.click(selector, { timeout: 5000 });
    } else if (text) {
      await page.click(`text="${text}"`, { timeout: 5000 });
    }
    
    // Esperar a que la página cargue
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Timeout esperando networkidle, continuando de todos modos');
    });
    
    // Extraer contenido de la página
    const pageContent = await extractPageContent(page);
    
    // Tomar captura de pantalla
    const screenshotPath = path.join(SCREENSHOT_DIR, `${session.sessionId}_${Date.now()}.png`);
    await page.screenshot({ path: screenshotPath, fullPage: false });
    
    // Convertir la captura a base64
    const screenshotBuffer = fs.readFileSync(screenshotPath);
    const screenshot = screenshotBuffer.toString('base64');
    
    // Actualizar historial si la URL cambió
    const currentUrl = page.url();
    if (history.length === 0 || history[history.length - 1] !== currentUrl) {
      if (session.currentIndex < history.length - 1) {
        // Si estamos en medio del historial, eliminar entradas futuras
        history.splice(session.currentIndex + 1);
      }
      history.push(currentUrl);
      session.currentIndex = history.length - 1;
    }
    
    // Actualizar timestamp de último acceso
    session.lastAccessed = Date.now();
    
    // Devolver respuesta
    res.json({
      url: currentUrl,
      title: pageContent.title,
      content: pageContent.content,
      summary: pageContent.summary,
      screenshot,
      sessionId: session.sessionId,
      historyIndex: session.currentIndex,
      historyLength: history.length
    });
  } catch (error) {
    console.error('Error al hacer clic en el elemento:', error);
    res.status(500).json({
      error: 'Error al hacer clic en el elemento',
      details: error.message
    });
  }
});

// Endpoint para navegar hacia atrás en el historial
app.post('/back', async (req, res) => {
  const { sessionId } = req.body;
  
  if (!sessionId) {
    return res.status(400).json({
      error: 'Se requiere un ID de sesión'
    });
  }
  
  try {
    // Obtener sesión
    const session = await getOrCreateSession(sessionId);
    const { page, history, currentIndex } = session;
    
    // Verificar si podemos navegar hacia atrás
    if (currentIndex <= 0) {
      return res.status(400).json({
        error: 'No hay páginas anteriores en el historial'
      });
    }
    
    // Actualizar índice
    session.currentIndex = currentIndex - 1;
    
    // Navegar a la URL anterior
    const previousUrl = history[session.currentIndex];
    await page.goto(previousUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
    
    // Esperar a que la página cargue
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Timeout esperando networkidle, continuando de todos modos');
    });
    
    // Extraer contenido de la página
    const pageContent = await extractPageContent(page);
    
    // Tomar captura de pantalla
    const screenshotPath = path.join(SCREENSHOT_DIR, `${session.sessionId}_${Date.now()}.png`);
    await page.screenshot({ path: screenshotPath, fullPage: false });
    
    // Convertir la captura a base64
    const screenshotBuffer = fs.readFileSync(screenshotPath);
    const screenshot = screenshotBuffer.toString('base64');
    
    // Actualizar timestamp de último acceso
    session.lastAccessed = Date.now();
    
    // Devolver respuesta
    res.json({
      url: page.url(),
      title: pageContent.title,
      content: pageContent.content,
      summary: pageContent.summary,
      screenshot,
      sessionId: session.sessionId,
      historyIndex: session.currentIndex,
      historyLength: history.length
    });
  } catch (error) {
    console.error('Error al navegar hacia atrás:', error);
    res.status(500).json({
      error: 'Error al navegar hacia atrás',
      details: error.message
    });
  }
});

// Endpoint para navegar hacia adelante en el historial
app.post('/forward', async (req, res) => {
  const { sessionId } = req.body;
  
  if (!sessionId) {
    return res.status(400).json({
      error: 'Se requiere un ID de sesión'
    });
  }
  
  try {
    // Obtener sesión
    const session = await getOrCreateSession(sessionId);
    const { page, history, currentIndex } = session;
    
    // Verificar si podemos navegar hacia adelante
    if (currentIndex >= history.length - 1) {
      return res.status(400).json({
        error: 'No hay páginas siguientes en el historial'
      });
    }
    
    // Actualizar índice
    session.currentIndex = currentIndex + 1;
    
    // Navegar a la URL siguiente
    const nextUrl = history[session.currentIndex];
    await page.goto(nextUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
    
    // Esperar a que la página cargue
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Timeout esperando networkidle, continuando de todos modos');
    });
    
    // Extraer contenido de la página
    const pageContent = await extractPageContent(page);
    
    // Tomar captura de pantalla
    const screenshotPath = path.join(SCREENSHOT_DIR, `${session.sessionId}_${Date.now()}.png`);
    await page.screenshot({ path: screenshotPath, fullPage: false });
    
    // Convertir la captura a base64
    const screenshotBuffer = fs.readFileSync(screenshotPath);
    const screenshot = screenshotBuffer.toString('base64');
    
    // Actualizar timestamp de último acceso
    session.lastAccessed = Date.now();
    
    // Devolver respuesta
    res.json({
      url: page.url(),
      title: pageContent.title,
      content: pageContent.content,
      summary: pageContent.summary,
      screenshot,
      sessionId: session.sessionId,
      historyIndex: session.currentIndex,
      historyLength: history.length
    });
  } catch (error) {
    console.error('Error al navegar hacia adelante:', error);
    res.status(500).json({
      error: 'Error al navegar hacia adelante',
      details: error.message
    });
  }
});

// Endpoint para rellenar un formulario
app.post('/fill', async (req, res) => {
  const { sessionId, selector, value } = req.body;
  
  if (!sessionId) {
    return res.status(400).json({
      error: 'Se requiere un ID de sesión'
    });
  }
  
  if (!selector || value === undefined) {
    return res.status(400).json({
      error: 'Se requieren selector y valor para rellenar el formulario'
    });
  }
  
  try {
    // Obtener sesión
    const session = await getOrCreateSession(sessionId);
    const { page } = session;
    
    // Rellenar el campo
    await page.fill(selector, value, { timeout: 5000 });
    
    // Actualizar timestamp de último acceso
    session.lastAccessed = Date.now();
    
    // Devolver respuesta
    res.json({
      success: true,
      message: `Campo ${selector} rellenado con éxito`,
      sessionId: session.sessionId
    });
  } catch (error) {
    console.error('Error al rellenar el formulario:', error);
    res.status(500).json({
      error: 'Error al rellenar el formulario',
      details: error.message
    });
  }
});

// Endpoint para obtener el historial de navegación
app.get('/history/:sessionId', async (req, res) => {
  const { sessionId } = req.params;
  
  if (!sessionId) {
    return res.status(400).json({
      error: 'Se requiere un ID de sesión'
    });
  }
  
  try {
    // Verificar si la sesión existe
    if (!sessions.has(sessionId)) {
      return res.status(404).json({
        error: 'Sesión no encontrada'
      });
    }
    
    // Obtener sesión
    const session = sessions.get(sessionId);
    
    // Actualizar timestamp de último acceso
    session.lastAccessed = Date.now();
    
    // Devolver historial
    res.json({
      history: session.history,
      currentIndex: session.currentIndex
    });
  } catch (error) {
    console.error('Error al obtener historial:', error);
    res.status(500).json({
      error: 'Error al obtener historial',
      details: error.message
    });
  }
});

// Endpoint para cerrar una sesión
app.post('/close', async (req, res) => {
  const { sessionId } = req.body;
  
  if (!sessionId) {
    return res.status(400).json({
      error: 'Se requiere un ID de sesión'
    });
  }
  
  try {
    // Verificar si la sesión existe
    if (!sessions.has(sessionId)) {
      return res.status(404).json({
        error: 'Sesión no encontrada'
      });
    }
    
    // Obtener sesión
    const session = sessions.get(sessionId);
    
    // Cerrar navegador
    if (session.browser) {
      await session.browser.close();
    }
    
    // Eliminar sesión
    sessions.delete(sessionId);
    
    // Devolver respuesta
    res.json({
      success: true,
      message: `Sesión ${sessionId} cerrada correctamente`
    });
  } catch (error) {
    console.error('Error al cerrar sesión:', error);
    res.status(500).json({
      error: 'Error al cerrar sesión',
      details: error.message
    });
  }
});

// Endpoint para SSE (Server-Sent Events)
app.get('/sse', (req, res) => {
  // Configurar cabeceras para SSE
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  
  // Enviar un evento inicial
  res.write(`data: ${JSON.stringify({ type: 'connected', message: 'Conexión SSE establecida' })}\n\n`);
  
  // Mantener la conexión abierta
  const interval = setInterval(() => {
    res.write(`data: ${JSON.stringify({ type: 'ping', timestamp: Date.now() })}\n\n`);
  }, 30000);
  
  // Limpiar cuando el cliente se desconecta
  req.on('close', () => {
    clearInterval(interval);
  });
});

// Iniciar el servidor
app.listen(PORT, () => {
  console.log(`Servidor Playwright-MCP iniciado en http://localhost:${PORT}`);
});
