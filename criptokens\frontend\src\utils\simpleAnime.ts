/**
 * Implementación simplificada de animaciones para reemplazar anime.js
 * 
 * Este archivo proporciona una implementación básica de animaciones
 * para evitar problemas con anime.js en TypeScript.
 */

// Función para animar elementos
const anime = (params: any) => {
  const {
    targets,
    translateY,
    translateX,
    opacity,
    scale,
    duration = 1000,
    delay = 0,
    easing = 'linear',
    complete,
    loop = false
  } = params;

  // Seleccionar elementos
  let elements: HTMLElement[] = [];
  if (typeof targets === 'string') {
    elements = Array.from(document.querySelectorAll(targets)) as HTMLElement[];
  } else if (targets instanceof HTMLElement) {
    elements = [targets];
  } else if (Array.isArray(targets)) {
    elements = targets.filter(el => el instanceof HTMLElement);
  } else if (targets && targets.nodeType) {
    elements = [targets as HTMLElement];
  }

  // Función para aplicar animaciones con CSS
  const applyAnimation = (element: HTMLElement, index: number) => {
    // Calcular delay basado en el índice si es un stagger
    const elementDelay = typeof delay === 'function' ? delay(element, index) : 
                         Array.isArray(delay) ? delay[0] : delay;
    
    // Configurar transición
    element.style.transition = `transform ${duration}ms ${easing}, opacity ${duration}ms ${easing}`;
    element.style.transitionDelay = `${elementDelay}ms`;
    
    // Aplicar transformaciones después de un pequeño delay para que la transición funcione
    setTimeout(() => {
      // Aplicar transformaciones
      let transform = '';
      
      // Manejar translateY
      if (translateY) {
        const endValue = Array.isArray(translateY) ? translateY[1] : translateY;
        transform += `translateY(${endValue}px) `;
      }
      
      // Manejar translateX
      if (translateX) {
        const endValue = Array.isArray(translateX) ? translateX[1] : translateX;
        transform += `translateX(${endValue}px) `;
      }
      
      // Manejar scale
      if (scale) {
        const endValue = Array.isArray(scale) ? scale[1] : scale;
        transform += `scale(${endValue}) `;
      }
      
      // Aplicar transform
      if (transform) {
        element.style.transform = transform;
      }
      
      // Manejar opacity
      if (opacity !== undefined) {
        const endValue = Array.isArray(opacity) ? opacity[1] : opacity;
        element.style.opacity = endValue.toString();
      }
      
      // Llamar a complete después de que termine la animación
      if (complete && index === elements.length - 1) {
        setTimeout(() => {
          complete({ restart: () => applyAnimation(element, index) });
          
          // Manejar loop
          if (loop) {
            applyAnimation(element, index);
          }
        }, duration + elementDelay);
      }
    }, 10);
    
    // Configurar valores iniciales
    if (translateY && Array.isArray(translateY)) {
      element.style.transform = `translateY(${translateY[0]}px)`;
    }
    
    if (translateX && Array.isArray(translateX)) {
      element.style.transform = `translateX(${translateX[0]}px)`;
    }
    
    if (scale && Array.isArray(scale)) {
      element.style.transform = `scale(${scale[0]})`;
    }
    
    if (opacity !== undefined && Array.isArray(opacity)) {
      element.style.opacity = opacity[0].toString();
    }
  };

  // Aplicar animación a cada elemento
  elements.forEach(applyAnimation);

  // Devolver un objeto con métodos útiles
  return {
    pause: () => {
      elements.forEach(el => {
        el.style.animationPlayState = 'paused';
      });
    },
    play: () => {
      elements.forEach(el => {
        el.style.animationPlayState = 'running';
      });
    },
    restart: () => {
      elements.forEach((el, index) => {
        applyAnimation(el, index);
      });
    }
  };
};

// Función stagger para delays
anime.stagger = (value: number, options: any = {}) => {
  return (el: any, i: number) => {
    const { start = 0 } = options;
    return start + (i * value);
  };
};

// Función random para valores aleatorios
anime.random = (min: number, max: number) => {
  return Math.random() * (max - min) + min;
};

// Exportar la función anime
export default anime;
