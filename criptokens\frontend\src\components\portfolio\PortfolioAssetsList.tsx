import React, { useState } from 'react';
import { usePortfolio } from '../../hooks/usePortfolio';
import { PortfolioAsset } from '../../services/portfolio.service';
import { formatNumber, formatPercentage, formatDate } from '../../utils/formatters';
import '../../styles/portfolio/PortfolioAssetsList.css';

interface PortfolioAssetsListProps {
  portfolio: PortfolioAsset[];
  availableCryptos: any[];
  onRemoveAsset: (assetId: string) => void;
  compact?: boolean;
}

const PortfolioAssetsList: React.FC<PortfolioAssetsListProps> = ({ 
  portfolio, 
  availableCryptos, 
  onRemoveAsset,
  compact = false 
}) => {
  const { calculateAssetStats } = usePortfolio();
  const [sortField, setSortField] = useState<string>('value');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filter, setFilter] = useState<string>('');

  // Ordenar y filtrar los activos
  const sortedAssets = [...portfolio]
    .filter(asset => {
      if (!filter) return true;
      return (
        asset.name.toLowerCase().includes(filter.toLowerCase()) ||
        asset.symbol.toLowerCase().includes(filter.toLowerCase())
      );
    })
    .sort((a, b) => {
      const statsA = calculateAssetStats(a);
      const statsB = calculateAssetStats(b);

      let comparison = 0;
      switch (sortField) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'amount':
          comparison = a.amount - b.amount;
          break;
        case 'purchasePrice':
          comparison = a.purchasePrice - b.purchasePrice;
          break;
        case 'currentPrice':
          comparison = statsA.currentPrice - statsB.currentPrice;
          break;
        case 'value':
          comparison = statsA.value - statsB.value;
          break;
        case 'profitLoss':
          comparison = statsA.profitLoss - statsB.profitLoss;
          break;
        case 'profitLossPercentage':
          comparison = statsA.profitLossPercentage - statsB.profitLossPercentage;
          break;
        default:
          comparison = statsA.value - statsB.value;
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });

  // Manejar el cambio de ordenación
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Renderizar el encabezado de la tabla
  const renderTableHeader = () => {
    const renderSortIcon = (field: string) => {
      if (sortField !== field) return null;
      return sortDirection === 'asc' ? ' ↑' : ' ↓';
    };

    return (
      <thead>
        <tr>
          <th onClick={() => handleSort('name')} className="sortable">
            Activo {renderSortIcon('name')}
          </th>
          <th onClick={() => handleSort('amount')} className="sortable">
            Cantidad {renderSortIcon('amount')}
          </th>
          <th onClick={() => handleSort('purchasePrice')} className="sortable">
            Precio de Compra {renderSortIcon('purchasePrice')}
          </th>
          <th onClick={() => handleSort('currentPrice')} className="sortable">
            Precio Actual {renderSortIcon('currentPrice')}
          </th>
          <th onClick={() => handleSort('value')} className="sortable">
            Valor {renderSortIcon('value')}
          </th>
          <th onClick={() => handleSort('profitLoss')} className="sortable">
            Ganancia/Pérdida {renderSortIcon('profitLoss')}
          </th>
          {!compact && (
            <>
              <th>Fecha de Compra</th>
              <th>Acciones</th>
            </>
          )}
        </tr>
      </thead>
    );
  };

  // Renderizar el cuerpo de la tabla
  const renderTableBody = () => {
    return (
      <tbody>
        {sortedAssets.map((asset) => {
          const stats = calculateAssetStats(asset);
          const crypto = availableCryptos.find(c => c.id === asset.id);
          
          return (
            <tr key={asset.id}>
              <td className="asset-info">
                {crypto?.image && (
                  <img
                    src={crypto.image}
                    alt={asset.name}
                    className="asset-icon"
                  />
                )}
                <div className="asset-name-container">
                  <span className="asset-name">{asset.name}</span>
                  <span className="asset-symbol">{asset.symbol.toUpperCase()}</span>
                </div>
              </td>
              <td>{formatNumber(asset.amount)}</td>
              <td>${formatNumber(asset.purchasePrice)}</td>
              <td>${formatNumber(stats.currentPrice)}</td>
              <td>${formatNumber(stats.value)}</td>
              <td className={stats.profitLoss >= 0 ? 'positive' : 'negative'}>
                ${formatNumber(stats.profitLoss)}
                <span className="percentage">
                  ({formatPercentage(stats.profitLossPercentage)})
                </span>
              </td>
              {!compact && (
                <>
                  <td>{formatDate(asset.purchaseDate)}</td>
                  <td>
                    <button
                      className="remove-asset-button"
                      onClick={() => onRemoveAsset(asset.id)}
                      title="Eliminar activo"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </td>
                </>
              )}
            </tr>
          );
        })}
      </tbody>
    );
  };

  // Si no hay activos, mostrar mensaje
  if (portfolio.length === 0) {
    return (
      <div className="empty-assets-message">
        <p>No tienes activos en tu portafolio.</p>
      </div>
    );
  }

  return (
    <div className={`portfolio-assets-list ${compact ? 'compact' : ''}`}>
      {!compact && (
        <div className="assets-list-controls">
          <div className="search-container">
            <input
              type="text"
              placeholder="Buscar activo..."
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="search-input"
            />
          </div>
        </div>
      )}

      <div className="assets-table-container">
        <table className="assets-table">
          {renderTableHeader()}
          {renderTableBody()}
        </table>
      </div>
    </div>
  );
};

export default PortfolioAssetsList;
