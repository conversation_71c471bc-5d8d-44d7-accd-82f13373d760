.crypto-card {
  position: relative;
  background: rgba(15, 15, 35, 0.7);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(64, 220, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  transform-style: preserve-3d;
  transform: perspective(1000px);
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.crypto-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(64, 220, 255, 0.4);
}

.crypto-card-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.crypto-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
}

.crypto-info {
  display: flex;
  align-items: center;
}

.crypto-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  border-radius: 50%;
  background: white;
  padding: 2px;
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.3);
}

.crypto-name-container {
  display: flex;
  flex-direction: column;
}

.crypto-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.crypto-symbol {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.crypto-price-container {
  text-align: right;
}

.crypto-price {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.crypto-change {
  font-size: 14px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
}

.crypto-change.positive {
  background: rgba(0, 200, 83, 0.2);
  color: #00c853;
}

.crypto-change.negative {
  background: rgba(255, 82, 82, 0.2);
  color: #ff5252;
}

.crypto-chart-container {
  margin: 15px 0;
  position: relative;
  z-index: 2;
}

.crypto-stats {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
  position: relative;
  z-index: 2;
}

.stat-item {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.crypto-card-actions {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  position: relative;
  z-index: 2;
}

.action-button {
  flex: 1;
  padding: 10px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.details-button {
  background: rgba(64, 220, 255, 0.1);
  color: #40dcff;
  border: 1px solid rgba(64, 220, 255, 0.3);
}

.details-button:hover {
  background: rgba(64, 220, 255, 0.2);
  transform: translateY(-2px);
}

.add-button {
  background: linear-gradient(90deg, #4657ce, #00f2ff);
  color: white;
}

.add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 242, 255, 0.3);
}

/* Animación de aparición */
@keyframes cardAppear {
  from {
    opacity: 0;
    transform: perspective(1000px) translateY(20px) rotateX(-5deg);
  }
  to {
    opacity: 1;
    transform: perspective(1000px) translateY(0) rotateX(0);
  }
}

.crypto-card {
  animation: cardAppear 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  animation-delay: calc(var(--index, 0) * 0.1s);
}

/* Efecto de partículas en el fondo */
.crypto-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(64, 220, 255, 0.05) 0%, rgba(10, 10, 26, 0) 70%);
  opacity: 0;
  transition: opacity 0.5s;
  pointer-events: none;
  z-index: 0;
}

.crypto-card:hover::before {
  opacity: 1;
}
