import { useState, useEffect } from 'react';
import { cryptoApiClient } from '../services/mcpClient';

// Hook para obtener el precio de una criptomoneda
export const useCryptoPrice = (cryptoId: string) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await cryptoApiClient.getCryptoPrice(cryptoId);
        setData(result);
      } catch (err: any) {
        console.error(`Error al obtener el precio de ${cryptoId}:`, err);
        setError(err.message || 'Error desconocido');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [cryptoId]);

  return { data, loading, error };
};

// Hook para obtener las principales criptomonedas
export const useTopCryptocurrencies = (limit: number = 10, page: number = 1) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await cryptoApiClient.getTopCryptocurrencies(limit, page);
        setData(result);
      } catch (err: any) {
        console.error('Error al obtener las principales criptomonedas:', err);
        setError(err.message || 'Error desconocido');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [limit, page]);

  return { data, loading, error };
};

// Hook para obtener datos históricos de una criptomoneda
export const useCryptoHistoricalData = (cryptoId: string, days: number = 7) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await cryptoApiClient.getCryptoHistoricalData(cryptoId, days);
        setData(result);
      } catch (err: any) {
        console.error(`Error al obtener datos históricos para ${cryptoId}:`, err);
        setError(err.message || 'Error desconocido');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [cryptoId, days]);

  return { data, loading, error };
};

// Hook para buscar criptomonedas
export const useSearchCryptocurrencies = (query: string) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const search = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setData(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const result = await cryptoApiClient.searchCryptocurrencies(searchQuery);
      setData(result);
    } catch (err: any) {
      console.error('Error al buscar criptomonedas:', err);
      setError(err.message || 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (query.trim()) {
      search(query);
    }
  }, [query]);

  return { data, loading, error, search };
};
