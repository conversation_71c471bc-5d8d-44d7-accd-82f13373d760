#!/bin/bash

# Colores para la salida
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Función para manejar la terminación
cleanup() {
    echo -e "${RED}Deteniendo servicios...${NC}"
    kill $CRYPTO_MCP_PID 2>/dev/null
    kill $BRAVE_SEARCH_PID 2>/dev/null
    kill $PLAYWRIGHT_MCP_PID 2>/dev/null
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Registrar la función de limpieza para señales de terminación
trap cleanup SIGINT SIGTERM

echo -e "${GREEN}=== INICIANDO TODOS LOS SERVICIOS DE CRIPTOKENS ===${NC}"
echo ""

# Iniciar el servidor MCP de crypto
echo -e "${MAGENTA}Iniciando Crypto MCP Server...${NC}"
cd ../crypto-mcp-server
node http-server.js > /dev/null 2>&1 &
CRYPTO_MCP_PID=$!
cd - > /dev/null

# Esperar a que el servidor MCP se inicie
echo "Esperando a que el servidor MCP se inicie..."
sleep 2

# Iniciar el servidor Brave Search
echo -e "${BLUE}Iniciando Brave Search Server...${NC}"
cd ..
node brave-search-server.js > /dev/null 2>&1 &
BRAVE_SEARCH_PID=$!
cd - > /dev/null

# Iniciar el servidor Playwright MCP
echo -e "${CYAN}Iniciando Playwright MCP Server...${NC}"
cd ../playwright-mcp-server
node dist/server.js > /dev/null 2>&1 &
PLAYWRIGHT_MCP_PID=$!
cd - > /dev/null

# Esperar a que los servidores MCP se inicien
echo "Esperando a que los servidores MCP se inicien..."
sleep 2

# Iniciar el backend
echo -e "${GREEN}Iniciando Backend...${NC}"
cd backend
node src/server.js > /dev/null 2>&1 &
BACKEND_PID=$!
cd ..

# Esperar a que el backend se inicie
echo "Esperando a que el backend se inicie..."
sleep 2

# Iniciar el frontend
echo -e "${YELLOW}Iniciando Frontend...${NC}"
cd frontend
npx vite > /dev/null 2>&1 &
FRONTEND_PID=$!
cd ..

# Mostrar mensaje de éxito
echo ""
echo -e "${GREEN}=== TODOS LOS SERVICIOS INICIADOS ===${NC}"
echo "Servicios disponibles:"
echo -e "${MAGENTA}- Crypto MCP Server: http://localhost:3101${NC}"
echo -e "${BLUE}- Brave Search Server: http://localhost:3102${NC}"
echo -e "${CYAN}- Playwright MCP Server: http://localhost:3103${NC}"
echo -e "${GREEN}- Backend: http://localhost:3001${NC}"
echo -e "${YELLOW}- Frontend: http://localhost:5173${NC}"
echo ""
echo -e "${YELLOW}Presiona Ctrl+C para detener todos los servicios${NC}"

# Mantener el script en ejecución
wait
