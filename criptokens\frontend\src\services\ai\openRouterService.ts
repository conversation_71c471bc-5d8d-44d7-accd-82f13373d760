import axios from 'axios';

const OPENROUTER_API_KEY = '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';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1';

/**
 * Llama a la API de OpenRouter para generar una respuesta de chat
 * @param prompt El mensaje del usuario
 * @param options Opciones adicionales para la llamada
 * @returns La respuesta generada
 */
export const callOpenRouter = async (prompt: string, options: any = {}) => {
  try {
    console.log(`Llamando a OpenRouter con modelo: ${options.model || 'anthropic/claude-3-opus-20240229'}`);
    
    const response = await axios.post(
      `${OPENROUTER_API_URL}/chat/completions`,
      {
        model: options.model || 'anthropic/claude-3-opus-20240229',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: options.max_tokens || 1000,
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 0.9,
        stream: false
      },
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://criptokens.com',
          'X-Title': 'Criptokens'
        }
      }
    );

    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('Respuesta de OpenRouter inválida');
    }

    return {
      content: response.data.choices[0].message.content,
      model: response.data.model,
      usage: response.data.usage
    };
  } catch (error: any) {
    console.error('Error al llamar a OpenRouter:', error);
    
    // Proporcionar información más detallada sobre el error
    if (error.response) {
      console.error('Detalles del error:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    
    throw new Error(`Error al generar respuesta con OpenRouter: ${error.message}`);
  }
};

/**
 * Genera una imagen utilizando DALL-E a través de OpenRouter
 * @param prompt Descripción de la imagen a generar
 * @returns URL de la imagen generada
 */
export const generateImage = async (prompt: string) => {
  try {
    console.log('Generando imagen con DALL-E a través de OpenRouter');
    
    const response = await axios.post(
      `${OPENROUTER_API_URL}/images/generations`,
      {
        model: 'openai/dall-e-3',
        prompt: prompt,
        n: 1,
        size: '1024x1024',
        style: 'vivid'
      },
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://criptokens.com',
          'X-Title': 'Criptokens'
        }
      }
    );

    if (!response.data || !response.data.data || !response.data.data[0]) {
      throw new Error('Respuesta de OpenRouter inválida para generación de imagen');
    }

    return {
      imageUrl: response.data.data[0].url,
      imageDescription: response.data.data[0].revised_prompt || prompt
    };
  } catch (error: any) {
    console.error('Error al generar imagen con OpenRouter:', error);
    
    if (error.response) {
      console.error('Detalles del error:', {
        status: error.response.status,
        data: error.response.data
      });
    }
    
    throw new Error(`Error al generar imagen: ${error.message}`);
  }
};
