const { MCPServer } = require("mcp-framework");
const BraveSearchToolClass = require("./tools/BraveSearchTool");

// Crear instancias de las herramientas
const braveSearchTool = new BraveSearchToolClass();

// Crear el servidor MCP
const server = new MCPServer({
  transport: {
    type: "http-stream",
    options: {
      port: 3102,
      cors: {
        allowOrigin: "http://localhost:5173"
      }
    }
  }
});

// Iniciar el servidor
server.start();

console.log("Servidor MCP Orchestrator iniciado en el puerto 3102");
console.log("Herramientas disponibles:", braveSearchTool.name);
