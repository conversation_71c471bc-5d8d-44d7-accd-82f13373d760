import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import fetch from "node-fetch";

// Crear un servidor MCP para Criptokens
const server = new McpServer({
  name: "CriptokensServer",
  version: "1.0.0",
  description: "Servidor MCP para proporcionar información sobre criptomonedas y análisis de mercado"
});

// API Key para CoinGecko Pro (reemplazar con tu propia API key si tienes una)
// Para la versión gratuita, limitaremos las solicitudes
const COINGECKO_API_URL = "https://api.coingecko.com/api/v3";

// Función auxiliar para obtener datos de CoinGecko
async function fetchCoinGeckoData(endpoint, params = {}) {
  const queryParams = new URLSearchParams(params).toString();
  const url = `${COINGECKO_API_URL}/${endpoint}${queryParams ? `?${queryParams}` : ''}`;
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Error en la API de CoinGecko: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error al obtener datos de CoinGecko: ${error.message}`);
    throw error;
  }
}

// Herramienta: Obtener precio actual de una criptomoneda
server.tool(
  "getCryptoPrice",
  { 
    cryptoId: z.string().describe("ID de la criptomoneda (ej: bitcoin, ethereum)"),
    currency: z.string().default("usd").describe("Moneda para mostrar el precio (ej: usd, eur)")
  },
  async ({ cryptoId, currency }) => {
    try {
      const data = await fetchCoinGeckoData(`coins/${cryptoId.toLowerCase()}`);
      
      const price = data.market_data.current_price[currency.toLowerCase()];
      const priceChange24h = data.market_data.price_change_percentage_24h;
      const marketCap = data.market_data.market_cap[currency.toLowerCase()];
      
      return {
        content: [
          { 
            type: "text", 
            text: `Precio actual de ${data.name} (${data.symbol.toUpperCase()}): ${price} ${currency.toUpperCase()}\n` +
                  `Cambio en 24h: ${priceChange24h.toFixed(2)}%\n` +
                  `Capitalización de mercado: ${marketCap.toLocaleString()} ${currency.toUpperCase()}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error al obtener el precio: ${error.message}` }]
      };
    }
  }
);

// Herramienta: Obtener las principales criptomonedas por capitalización de mercado
server.tool(
  "getTopCryptos",
  { 
    limit: z.number().default(10).describe("Número de criptomonedas a mostrar"),
    currency: z.string().default("usd").describe("Moneda para mostrar los precios")
  },
  async ({ limit, currency }) => {
    try {
      const data = await fetchCoinGeckoData("coins/markets", {
        vs_currency: currency.toLowerCase(),
        order: "market_cap_desc",
        per_page: limit,
        page: 1,
        sparkline: false
      });
      
      let resultText = `Top ${limit} criptomonedas por capitalización de mercado:\n\n`;
      
      data.forEach((coin, index) => {
        resultText += `${index + 1}. ${coin.name} (${coin.symbol.toUpperCase()})\n` +
                      `   Precio: ${coin.current_price} ${currency.toUpperCase()}\n` +
                      `   Cambio 24h: ${coin.price_change_percentage_24h?.toFixed(2) || 'N/A'}%\n` +
                      `   Cap. Mercado: ${coin.market_cap.toLocaleString()} ${currency.toUpperCase()}\n\n`;
      });
      
      return {
        content: [{ type: "text", text: resultText }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error al obtener las principales criptomonedas: ${error.message}` }]
      };
    }
  }
);

// Herramienta: Analizar sentimiento del mercado
server.tool(
  "getMarketSentiment",
  {},
  async () => {
    try {
      const globalData = await fetchCoinGeckoData("global");
      const marketData = globalData.data;
      
      const totalMarketCap = marketData.total_market_cap.usd;
      const totalVolume = marketData.total_volume.usd;
      const marketCapChange = marketData.market_cap_change_percentage_24h_usd;
      const btcDominance = marketData.market_cap_percentage.btc;
      const ethDominance = marketData.market_cap_percentage.eth;
      
      // Calcular sentimiento basado en varios factores
      let sentiment = "neutral";
      let sentimentScore = 0;
      
      // Factor 1: Cambio en la capitalización de mercado
      if (marketCapChange > 5) {
        sentiment = "muy positivo";
        sentimentScore += 2;
      } else if (marketCapChange > 2) {
        sentiment = "positivo";
        sentimentScore += 1;
      } else if (marketCapChange < -5) {
        sentiment = "muy negativo";
        sentimentScore -= 2;
      } else if (marketCapChange < -2) {
        sentiment = "negativo";
        sentimentScore -= 1;
      }
      
      // Factor 2: Volumen de mercado (alto volumen suele indicar más actividad)
      const volumeToMarketCapRatio = (totalVolume / totalMarketCap) * 100;
      if (volumeToMarketCapRatio > 8) {
        sentimentScore += 1;
      } else if (volumeToMarketCapRatio < 3) {
        sentimentScore -= 1;
      }
      
      // Ajustar sentimiento final basado en la puntuación
      if (sentimentScore >= 2) {
        sentiment = "muy positivo";
      } else if (sentimentScore === 1) {
        sentiment = "positivo";
      } else if (sentimentScore === 0) {
        sentiment = "neutral";
      } else if (sentimentScore === -1) {
        sentiment = "negativo";
      } else {
        sentiment = "muy negativo";
      }
      
      return {
        content: [
          { 
            type: "text", 
            text: `Análisis de Sentimiento del Mercado Cripto:\n\n` +
                  `Sentimiento general: ${sentiment.toUpperCase()}\n\n` +
                  `Capitalización total del mercado: $${(totalMarketCap / 1e12).toFixed(2)} billones\n` +
                  `Cambio en 24h: ${marketCapChange.toFixed(2)}%\n` +
                  `Volumen total en 24h: $${(totalVolume / 1e9).toFixed(2)} mil millones\n` +
                  `Dominancia de Bitcoin: ${btcDominance.toFixed(2)}%\n` +
                  `Dominancia de Ethereum: ${ethDominance.toFixed(2)}%\n\n` +
                  `Ratio Volumen/Cap. Mercado: ${volumeToMarketCapRatio.toFixed(2)}%\n`
          }
        ]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error al analizar el sentimiento del mercado: ${error.message}` }]
      };
    }
  }
);

// Herramienta: Obtener información detallada sobre una criptomoneda
server.tool(
  "getCryptoInfo",
  { 
    cryptoId: z.string().describe("ID de la criptomoneda (ej: bitcoin, ethereum)")
  },
  async ({ cryptoId }) => {
    try {
      const data = await fetchCoinGeckoData(`coins/${cryptoId.toLowerCase()}`, {
        localization: false,
        tickers: false,
        market_data: true,
        community_data: true,
        developer_data: true
      });
      
      // Extraer datos relevantes
      const {
        name,
        symbol,
        market_data,
        description,
        links,
        genesis_date,
        developer_data
      } = data;
      
      // Crear respuesta formateada
      let resultText = `Información sobre ${name} (${symbol.toUpperCase()}):\n\n`;
      
      // Datos de mercado
      resultText += `📊 DATOS DE MERCADO:\n`;
      resultText += `Precio actual: $${market_data.current_price.usd}\n`;
      resultText += `Cambio 24h: ${market_data.price_change_percentage_24h?.toFixed(2) || 'N/A'}%\n`;
      resultText += `Capitalización: $${market_data.market_cap.usd.toLocaleString()}\n`;
      resultText += `Volumen 24h: $${market_data.total_volume.usd.toLocaleString()}\n`;
      resultText += `Rango por capitalización: #${market_data.market_cap_rank}\n`;
      resultText += `ATH: $${market_data.ath.usd} (${new Date(market_data.ath_date.usd).toLocaleDateString()})\n\n`;
      
      // Descripción (primeros 300 caracteres)
      if (description.en) {
        const shortDesc = description.en.replace(/<\/?[^>]+(>|$)/g, "").substring(0, 300);
        resultText += `📝 DESCRIPCIÓN:\n${shortDesc}...\n\n`;
      }
      
      // Enlaces importantes
      resultText += `🔗 ENLACES:\n`;
      if (links.homepage && links.homepage[0]) resultText += `Web oficial: ${links.homepage[0]}\n`;
      if (links.blockchain_site && links.blockchain_site[0]) resultText += `Explorador: ${links.blockchain_site[0]}\n`;
      if (links.official_forum_url && links.official_forum_url[0]) resultText += `Foro oficial: ${links.official_forum_url[0]}\n`;
      if (links.subreddit_url) resultText += `Reddit: ${links.subreddit_url}\n`;
      if (links.repos_url && links.repos_url.github && links.repos_url.github[0]) resultText += `GitHub: ${links.repos_url.github[0]}\n\n`;
      
      // Datos adicionales
      resultText += `📅 DATOS ADICIONALES:\n`;
      if (genesis_date) resultText += `Fecha de génesis: ${genesis_date}\n`;
      if (developer_data.forks) resultText += `Forks en GitHub: ${developer_data.forks}\n`;
      if (developer_data.stars) resultText += `Estrellas en GitHub: ${developer_data.stars}\n`;
      if (developer_data.subscribers) resultText += `Suscriptores en GitHub: ${developer_data.subscribers}\n`;
      if (developer_data.total_issues) resultText += `Issues totales: ${developer_data.total_issues}\n`;
      if (developer_data.closed_issues) resultText += `Issues cerrados: ${developer_data.closed_issues}\n`;
      
      return {
        content: [{ type: "text", text: resultText }]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error al obtener información de la criptomoneda: ${error.message}` }]
      };
    }
  }
);

// Herramienta: Convertir entre criptomonedas
server.tool(
  "convertCrypto",
  { 
    fromCrypto: z.string().describe("ID de la criptomoneda origen (ej: bitcoin)"),
    toCrypto: z.string().describe("ID de la criptomoneda destino (ej: ethereum)"),
    amount: z.number().describe("Cantidad a convertir")
  },
  async ({ fromCrypto, toCrypto, amount }) => {
    try {
      // Obtener precios en USD de ambas criptomonedas
      const fromData = await fetchCoinGeckoData(`coins/${fromCrypto.toLowerCase()}`);
      const toData = await fetchCoinGeckoData(`coins/${toCrypto.toLowerCase()}`);
      
      const fromPriceUsd = fromData.market_data.current_price.usd;
      const toPriceUsd = toData.market_data.current_price.usd;
      
      // Calcular la conversión
      const valueInUsd = amount * fromPriceUsd;
      const convertedAmount = valueInUsd / toPriceUsd;
      
      return {
        content: [
          { 
            type: "text", 
            text: `Conversión de ${amount} ${fromData.name} (${fromData.symbol.toUpperCase()}) a ${toData.name} (${toData.symbol.toUpperCase()}):\n\n` +
                  `${amount} ${fromData.symbol.toUpperCase()} = ${convertedAmount.toFixed(8)} ${toData.symbol.toUpperCase()}\n\n` +
                  `Tasa de cambio: 1 ${fromData.symbol.toUpperCase()} = ${(fromPriceUsd / toPriceUsd).toFixed(8)} ${toData.symbol.toUpperCase()}\n` +
                  `Valor en USD: $${valueInUsd.toFixed(2)}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error al realizar la conversión: ${error.message}` }]
      };
    }
  }
);

// Recurso: Información sobre criptomonedas
server.resource(
  "crypto",
  new ResourceTemplate("crypto://{id}", { list: undefined }),
  async (uri, { id }) => {
    try {
      const data = await fetchCoinGeckoData(`coins/${id.toLowerCase()}`);
      
      return {
        contents: [{
          uri: uri.href,
          text: `# ${data.name} (${data.symbol.toUpperCase()})\n\n` +
                `**Precio actual:** $${data.market_data.current_price.usd}\n` +
                `**Cambio 24h:** ${data.market_data.price_change_percentage_24h?.toFixed(2) || 'N/A'}%\n` +
                `**Capitalización de mercado:** $${data.market_data.market_cap.usd.toLocaleString()}\n` +
                `**Volumen 24h:** $${data.market_data.total_volume.usd.toLocaleString()}\n\n` +
                `${data.description.en ? data.description.en.replace(/<\/?[^>]+(>|$)/g, "") : 'No hay descripción disponible.'}`
        }]
      };
    } catch (error) {
      return {
        contents: [{
          uri: uri.href,
          text: `Error al obtener información sobre ${id}: ${error.message}`
        }]
      };
    }
  }
);

// Iniciar el servidor MCP
console.log("Iniciando servidor MCP para Criptokens...");
const transport = new StdioServerTransport();
await server.connect(transport);
console.log("Servidor MCP conectado y listo para recibir solicitudes.");
