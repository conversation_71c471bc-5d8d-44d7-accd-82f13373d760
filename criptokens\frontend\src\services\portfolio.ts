import { getTopCryptocurrencies, getCryptoDetails } from './api';

// Interfaz para los activos en la cartera
export interface PortfolioAsset {
  id: string;
  symbol: string;
  name: string;
  image: string;
  amount: number;
  purchasePrice: number;
  currentPrice: number;
  value: number;
  profitLoss: number;
  profitLossPercentage: number;
  lastUpdated: Date;
}

// Interfaz para la cartera completa
export interface Portfolio {
  assets: PortfolioAsset[];
  totalValue: number;
  totalProfitLoss: number;
  totalProfitLossPercentage: number;
  lastUpdated: Date;
}

// Clave para almacenar la cartera en localStorage
const PORTFOLIO_STORAGE_KEY = 'criptokens_portfolio';

// Función para obtener la cartera desde localStorage
export const getPortfolio = (): Portfolio => {
  const storedPortfolio = localStorage.getItem(PORTFOLIO_STORAGE_KEY);
  
  if (storedPortfolio) {
    const portfolio = JSON.parse(storedPortfolio);
    // Convertir las fechas de string a Date
    portfolio.lastUpdated = new Date(portfolio.lastUpdated);
    portfolio.assets.forEach((asset: PortfolioAsset) => {
      asset.lastUpdated = new Date(asset.lastUpdated);
    });
    return portfolio;
  }
  
  // Si no hay cartera guardada, devolver una vacía
  return {
    assets: [],
    totalValue: 0,
    totalProfitLoss: 0,
    totalProfitLossPercentage: 0,
    lastUpdated: new Date()
  };
};

// Función para guardar la cartera en localStorage
export const savePortfolio = (portfolio: Portfolio): void => {
  localStorage.setItem(PORTFOLIO_STORAGE_KEY, JSON.stringify(portfolio));
};

// Función para añadir un activo a la cartera
export const addAssetToPortfolio = async (
  id: string,
  amount: number,
  purchasePrice: number
): Promise<Portfolio> => {
  try {
    // Obtener los detalles de la criptomoneda
    const cryptoDetails = await getCryptoDetails(id);
    
    // Obtener la cartera actual
    const portfolio = getPortfolio();
    
    // Comprobar si el activo ya existe en la cartera
    const existingAssetIndex = portfolio.assets.findIndex(asset => asset.id === id);
    
    const currentPrice = cryptoDetails.market_data.current_price.usd;
    const value = amount * currentPrice;
    const profitLoss = value - (amount * purchasePrice);
    const profitLossPercentage = ((currentPrice - purchasePrice) / purchasePrice) * 100;
    
    const newAsset: PortfolioAsset = {
      id,
      symbol: cryptoDetails.symbol.toUpperCase(),
      name: cryptoDetails.name,
      image: cryptoDetails.image.small,
      amount,
      purchasePrice,
      currentPrice,
      value,
      profitLoss,
      profitLossPercentage,
      lastUpdated: new Date()
    };
    
    if (existingAssetIndex !== -1) {
      // Si el activo ya existe, actualizar la cantidad y recalcular valores
      const existingAsset = portfolio.assets[existingAssetIndex];
      const totalAmount = existingAsset.amount + amount;
      const avgPurchasePrice = ((existingAsset.amount * existingAsset.purchasePrice) + (amount * purchasePrice)) / totalAmount;
      
      newAsset.amount = totalAmount;
      newAsset.purchasePrice = avgPurchasePrice;
      newAsset.value = totalAmount * currentPrice;
      newAsset.profitLoss = newAsset.value - (totalAmount * avgPurchasePrice);
      newAsset.profitLossPercentage = ((currentPrice - avgPurchasePrice) / avgPurchasePrice) * 100;
      
      portfolio.assets[existingAssetIndex] = newAsset;
    } else {
      // Si es un nuevo activo, añadirlo a la cartera
      portfolio.assets.push(newAsset);
    }
    
    // Recalcular totales de la cartera
    updatePortfolioTotals(portfolio);
    
    // Guardar la cartera actualizada
    savePortfolio(portfolio);
    
    return portfolio;
  } catch (error) {
    console.error('Error al añadir activo a la cartera:', error);
    throw error;
  }
};

// Función para eliminar un activo de la cartera
export const removeAssetFromPortfolio = (id: string): Portfolio => {
  // Obtener la cartera actual
  const portfolio = getPortfolio();
  
  // Filtrar el activo a eliminar
  portfolio.assets = portfolio.assets.filter(asset => asset.id !== id);
  
  // Recalcular totales de la cartera
  updatePortfolioTotals(portfolio);
  
  // Guardar la cartera actualizada
  savePortfolio(portfolio);
  
  return portfolio;
};

// Función para actualizar los precios actuales de los activos en la cartera
export const updatePortfolioPrices = async (): Promise<Portfolio> => {
  try {
    // Obtener la cartera actual
    const portfolio = getPortfolio();
    
    if (portfolio.assets.length === 0) {
      return portfolio;
    }
    
    // Obtener los IDs de los activos en la cartera
    const assetIds = portfolio.assets.map(asset => asset.id);
    
    // Obtener los datos actualizados de las criptomonedas
    // Nota: En una implementación real, deberíamos hacer una llamada específica para obtener
    // solo los datos de las criptomonedas en la cartera. Por simplicidad, obtenemos las principales.
    const cryptoData = await getTopCryptocurrencies(50);
    
    // Actualizar los precios de los activos
    for (const asset of portfolio.assets) {
      const cryptoInfo = cryptoData.find(crypto => crypto.id === asset.id);
      
      if (cryptoInfo) {
        asset.currentPrice = cryptoInfo.current_price;
        asset.value = asset.amount * asset.currentPrice;
        asset.profitLoss = asset.value - (asset.amount * asset.purchasePrice);
        asset.profitLossPercentage = ((asset.currentPrice - asset.purchasePrice) / asset.purchasePrice) * 100;
        asset.lastUpdated = new Date();
      }
    }
    
    // Recalcular totales de la cartera
    updatePortfolioTotals(portfolio);
    
    // Guardar la cartera actualizada
    savePortfolio(portfolio);
    
    return portfolio;
  } catch (error) {
    console.error('Error al actualizar precios de la cartera:', error);
    throw error;
  }
};

// Función auxiliar para actualizar los totales de la cartera
const updatePortfolioTotals = (portfolio: Portfolio): void => {
  portfolio.totalValue = portfolio.assets.reduce((total, asset) => total + asset.value, 0);
  portfolio.totalProfitLoss = portfolio.assets.reduce((total, asset) => total + asset.profitLoss, 0);
  
  // Calcular el porcentaje de ganancia/pérdida total
  const totalInvestment = portfolio.assets.reduce(
    (total, asset) => total + (asset.amount * asset.purchasePrice), 
    0
  );
  
  portfolio.totalProfitLossPercentage = totalInvestment > 0 
    ? (portfolio.totalProfitLoss / totalInvestment) * 100 
    : 0;
    
  portfolio.lastUpdated = new Date();
};
