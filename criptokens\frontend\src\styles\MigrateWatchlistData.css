.migration-banner {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  background: rgba(20, 20, 30, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.migration-content {
  padding: 20px;
}

.migration-content h3 {
  margin: 0 0 10px 0;
  color: #fff;
  font-size: 18px;
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.migration-content p {
  margin: 0 0 15px 0;
  color: #ccc;
  font-size: 14px;
  line-height: 1.5;
}

.migrate-button {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  border: none;
  border-radius: 4px;
  color: white;
  padding: 8px 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.migrate-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(110, 66, 202, 0.4);
}

.migrate-button:disabled {
  background: #555;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.dismiss-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: #ccc;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.dismiss-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.error-actions {
  display: flex;
  gap: 10px;
}

.error-actions button {
  flex: 1;
}

.retry-button {
  background: rgba(255, 58, 110, 0.2);
  border: 1px solid #ff3a6e;
  border-radius: 4px;
  color: #ff3a6e;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.retry-button:hover {
  background: rgba(255, 58, 110, 0.4);
}

.close-banner {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-banner:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}
