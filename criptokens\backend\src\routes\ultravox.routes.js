const express = require('express');
const ultravoxService = require('../services/ultravox.service');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route GET /api/ultravox/status
 * @desc Verifica el estado de la API de Ultravox
 * @access Public (en desarrollo) / Private (en producción)
 */
router.get('/status', async (req, res) => {
  try {
    console.log('Verificando estado de Ultravox API con clave:', ultravoxService.apiKey ? 'Configurada' : 'No configurada');
    const isValid = await ultravoxService.checkApiKey();
    console.log('Resultado de verificación de Ultravox API:', isValid ? 'Válida' : 'Inválida');

    return res.status(200).json({
      success: true,
      isValid,
      credits: ultravoxService.credits
    });
  } catch (error) {
    console.error('Error verificando estado de Ultravox:', error);
    logger.error('Error verificando estado de Ultravox:', error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al verificar el estado de la API de Ultravox',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/apikey
 * @desc Actualiza la clave de API de Ultravox
 * @access Private
 */
router.post('/apikey', authenticateToken, async (req, res) => {
  try {
    const { apiKey } = req.body;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        message: 'La clave de API es requerida'
      });
    }

    const isValid = await ultravoxService.setApiKey(apiKey);

    return res.status(200).json({
      success: true,
      isValid,
      message: isValid ? 'Clave de API actualizada correctamente' : 'La clave de API no es válida'
    });
  } catch (error) {
    logger.error('Error actualizando clave de API de Ultravox:', error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al actualizar la clave de API',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/calls
 * @desc Crea una nueva llamada de voz
 * @access Private
 */
router.post('/calls', authenticateToken, async (req, res) => {
  try {
    const { systemPrompt, language, options } = req.body;

    if (!systemPrompt) {
      return res.status(400).json({
        success: false,
        message: 'El prompt del sistema es requerido'
      });
    }

    // Verificar si hay suficientes créditos
    if (ultravoxService.credits <= 0) {
      return res.status(402).json({
        success: false,
        message: 'No hay suficientes créditos para realizar esta operación'
      });
    }

    const callData = await ultravoxService.createCall(systemPrompt, language, options);

    return res.status(201).json({
      success: true,
      call: callData,
      credits: ultravoxService.credits
    });
  } catch (error) {
    logger.error('Error creando llamada de Ultravox:', error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al crear la llamada de voz',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/calls/:callId/messages
 * @desc Envía un mensaje a una llamada existente
 * @access Private
 */
router.post('/calls/:callId/messages', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'El mensaje es requerido'
      });
    }

    // Verificar si hay suficientes créditos
    if (ultravoxService.credits <= 0) {
      return res.status(402).json({
        success: false,
        message: 'No hay suficientes créditos para realizar esta operación'
      });
    }

    const response = await ultravoxService.sendMessage(callId, message);

    return res.status(200).json({
      success: true,
      response,
      credits: ultravoxService.credits
    });
  } catch (error) {
    logger.error(`Error enviando mensaje a la llamada ${req.params.callId}:`, error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al enviar el mensaje',
      error: error.message
    });
  }
});

/**
 * @route GET /api/ultravox/calls/:callId/messages
 * @desc Obtiene los mensajes de una llamada
 * @access Private
 */
router.get('/calls/:callId/messages', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;

    const messages = await ultravoxService.getMessages(callId);

    return res.status(200).json({
      success: true,
      messages
    });
  } catch (error) {
    logger.error(`Error obteniendo mensajes de la llamada ${req.params.callId}:`, error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener los mensajes',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/calls/:callId/end
 * @desc Finaliza una llamada
 * @access Private
 */
router.post('/calls/:callId/end', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;

    const response = await ultravoxService.endCall(callId);

    return res.status(200).json({
      success: true,
      response
    });
  } catch (error) {
    logger.error(`Error finalizando la llamada ${req.params.callId}:`, error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al finalizar la llamada',
      error: error.message
    });
  }
});

/**
 * @route POST /api/ultravox/credits/add
 * @desc Añade créditos a la cuenta
 * @access Private
 */
router.post('/credits/add', authenticateToken, async (req, res) => {
  try {
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'La cantidad debe ser un número positivo'
      });
    }

    const newBalance = ultravoxService.addCredits(amount);

    return res.status(200).json({
      success: true,
      credits: newBalance,
      message: `Se añadieron ${amount} créditos correctamente`
    });
  } catch (error) {
    logger.error('Error añadiendo créditos:', error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al añadir créditos',
      error: error.message
    });
  }
});

/**
 * @route GET /api/ultravox/credits
 * @desc Obtiene el saldo de créditos
 * @access Private
 */
router.get('/credits', authenticateToken, async (req, res) => {
  try {
    const credits = await ultravoxService.getCredits();

    return res.status(200).json({
      success: true,
      credits
    });
  } catch (error) {
    logger.error('Error obteniendo créditos:', error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener el saldo de créditos',
      error: error.message
    });
  }
});

/**
 * @route GET /api/ultravox/usage
 * @desc Obtiene el historial de uso
 * @access Private
 */
router.get('/usage', authenticateToken, async (req, res) => {
  try {
    const usageHistory = ultravoxService.getUsageHistory();

    return res.status(200).json({
      success: true,
      usageHistory
    });
  } catch (error) {
    logger.error('Error obteniendo historial de uso:', error.message);
    return res.status(500).json({
      success: false,
      message: 'Error al obtener el historial de uso',
      error: error.message
    });
  }
});

module.exports = router;
