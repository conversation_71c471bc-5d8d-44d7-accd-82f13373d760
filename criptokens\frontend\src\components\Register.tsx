import { useState } from 'react';
import { useAuth } from '../context/NewAuthContext';
import '../styles/Auth.css';

interface RegisterProps {
  onLoginClick: () => void;
}

const Register = ({ onLoginClick }: RegisterProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const { register } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password || !confirmPassword || !displayName) {
      setError('Por favor, completa todos los campos.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden.');
      return;
    }

    if (password.length < 6) {
      setError('La contraseña debe tener al menos 6 caracteres.');
      return;
    }

    try {
      setError(null);
      setLoading(true);
      await register(email, password, displayName);
      // El redireccionamiento se maneja en el componente principal
    } catch (err: any) {
      console.error('Error al registrar usuario:', err);

      // Mensajes de error más amigables
      console.log('Error completo:', err);

      if (err.message.includes('email-already-in-use')) {
        setError('Ya existe una cuenta con este correo electrónico.');
      } else if (err.message.includes('invalid-email')) {
        setError('El formato del correo electrónico no es válido.');
      } else if (err.message.includes('weak-password')) {
        setError('La contraseña es demasiado débil. Usa al menos 6 caracteres.');
      } else if (err.message.includes('network-request-failed')) {
        setError('Error de conexión. Verifica tu conexión a internet.');
      } else if (err.message.includes('permission-denied')) {
        setError('Error de permisos en la base de datos. Contacta al administrador.');
      } else if (err.message.includes('configuration-not-found')) {
        setError('Error de configuración de Firebase. La autenticación por correo/contraseña no está habilitada.');
      } else {
        console.log('Error detallado:', err);
        setError(`Error al crear la cuenta: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <h2>Crear Cuenta</h2>

        {error && <div className="auth-error">{error}</div>}

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="displayName">Nombre</label>
            <input
              type="text"
              id="displayName"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              placeholder="Tu nombre"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="email">Correo Electrónico</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Contraseña</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="********"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword">Confirmar Contraseña</label>
            <input
              type="password"
              id="confirmPassword"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="********"
              required
            />
          </div>

          <button
            type="submit"
            className="auth-button"
            disabled={loading}
          >
            {loading ? 'Creando cuenta...' : 'Crear Cuenta'}
          </button>
        </form>

        <div className="auth-links">
          <div className="auth-separator">
            <span>¿Ya tienes una cuenta?</span>
          </div>

          <button
            className="secondary-button"
            onClick={onLoginClick}
          >
            Iniciar Sesión
          </button>
        </div>
      </div>
    </div>
  );
};

export default Register;
