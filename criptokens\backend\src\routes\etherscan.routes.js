/**
 * Rutas para acceder a los datos de Etherscan
 */

const express = require('express');
const router = express.Router();
const etherscanService = require('../services/etherscan.service');

/**
 * @route GET /api/etherscan/eth/price
 * @desc Obtiene el precio actual de ETH
 * @access Public
 */
router.get('/eth/price', async (req, res) => {
  try {
    const price = await etherscanService.getEthPrice();
    res.json(price);
  } catch (error) {
    console.error('Error al obtener precio de ETH:', error);
    res.status(500).json({ error: 'Error al obtener precio de ETH', details: error.message });
  }
});

/**
 * @route GET /api/etherscan/eth/stats
 * @desc Obtiene estadísticas de la red Ethereum
 * @access Public
 */
router.get('/eth/stats', async (req, res) => {
  try {
    const stats = await etherscanService.getEthStats();
    res.json(stats);
  } catch (error) {
    console.error('Error al obtener estadísticas de ETH:', error);
    res.status(500).json({ error: 'Error al obtener estadísticas de ETH', details: error.message });
  }
});

/**
 * @route GET /api/etherscan/eth2/validators
 * @desc Obtiene información sobre los validadores de Ethereum 2.0
 * @access Public
 */
router.get('/eth2/validators', async (req, res) => {
  try {
    const validators = await etherscanService.getEth2Validators();
    res.json(validators);
  } catch (error) {
    console.error('Error al obtener información de validadores ETH2:', error);
    res.status(500).json({ error: 'Error al obtener información de validadores ETH2', details: error.message });
  }
});

/**
 * @route GET /api/etherscan/gas
 * @desc Obtiene el precio actual del gas
 * @access Public
 */
router.get('/gas', async (req, res) => {
  try {
    const gas = await etherscanService.getGasOracle();
    res.json(gas);
  } catch (error) {
    console.error('Error al obtener precio del gas:', error);
    res.status(500).json({ error: 'Error al obtener precio del gas', details: error.message });
  }
});

/**
 * @route GET /api/etherscan/address/:address/balance
 * @desc Obtiene el balance de ETH de una dirección
 * @access Public
 */
router.get('/address/:address/balance', async (req, res) => {
  try {
    const { address } = req.params;
    const balance = await etherscanService.getAddressBalance(address);
    res.json({ address, balance });
  } catch (error) {
    console.error(`Error al obtener balance de ${req.params.address}:`, error);
    res.status(500).json({ error: `Error al obtener balance de ${req.params.address}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/address/:address/transactions
 * @desc Obtiene las transacciones de una dirección
 * @access Public
 */
router.get('/address/:address/transactions', async (req, res) => {
  try {
    const { address } = req.params;
    const { startblock = 0, endblock = 99999999, page = 1, offset = 10, sort = 'desc' } = req.query;
    
    const transactions = await etherscanService.getAddressTransactions(
      address,
      parseInt(startblock),
      parseInt(endblock),
      parseInt(page),
      parseInt(offset),
      sort
    );
    
    res.json({ address, transactions });
  } catch (error) {
    console.error(`Error al obtener transacciones de ${req.params.address}:`, error);
    res.status(500).json({ error: `Error al obtener transacciones de ${req.params.address}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/address/:address/tokens
 * @desc Obtiene las transacciones de tokens ERC20 de una dirección
 * @access Public
 */
router.get('/address/:address/tokens', async (req, res) => {
  try {
    const { address } = req.params;
    const { contractaddress, page = 1, offset = 10, sort = 'desc' } = req.query;
    
    const tokens = await etherscanService.getAddressERC20Transfers(
      address,
      contractaddress,
      parseInt(page),
      parseInt(offset),
      sort
    );
    
    res.json({ address, tokens });
  } catch (error) {
    console.error(`Error al obtener tokens de ${req.params.address}:`, error);
    res.status(500).json({ error: `Error al obtener tokens de ${req.params.address}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/contract/:address/source
 * @desc Obtiene el código fuente de un contrato
 * @access Public
 */
router.get('/contract/:address/source', async (req, res) => {
  try {
    const { address } = req.params;
    const source = await etherscanService.getContractSourceCode(address);
    res.json({ address, source });
  } catch (error) {
    console.error(`Error al obtener código fuente del contrato ${req.params.address}:`, error);
    res.status(500).json({ error: `Error al obtener código fuente del contrato ${req.params.address}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/contract/:address/abi
 * @desc Obtiene el ABI de un contrato
 * @access Public
 */
router.get('/contract/:address/abi', async (req, res) => {
  try {
    const { address } = req.params;
    const abi = await etherscanService.getContractABI(address);
    res.json({ address, abi });
  } catch (error) {
    console.error(`Error al obtener ABI del contrato ${req.params.address}:`, error);
    res.status(500).json({ error: `Error al obtener ABI del contrato ${req.params.address}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/token/:address/info
 * @desc Obtiene información sobre un token ERC20
 * @access Public
 */
router.get('/token/:address/info', async (req, res) => {
  try {
    const { address } = req.params;
    const info = await etherscanService.getTokenInfo(address);
    res.json({ address, info });
  } catch (error) {
    console.error(`Error al obtener información del token ${req.params.address}:`, error);
    res.status(500).json({ error: `Error al obtener información del token ${req.params.address}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/token/:address/supply
 * @desc Obtiene el suministro total de un token ERC20
 * @access Public
 */
router.get('/token/:address/supply', async (req, res) => {
  try {
    const { address } = req.params;
    const supply = await etherscanService.getTokenSupply(address);
    res.json({ address, supply });
  } catch (error) {
    console.error(`Error al obtener suministro del token ${req.params.address}:`, error);
    res.status(500).json({ error: `Error al obtener suministro del token ${req.params.address}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/defi/protocols
 * @desc Obtiene información sobre los principales protocolos DeFi
 * @access Public
 */
router.get('/defi/protocols', async (req, res) => {
  try {
    const protocols = await etherscanService.getTopDefiProtocols();
    res.json(protocols);
  } catch (error) {
    console.error('Error al obtener protocolos DeFi:', error);
    res.status(500).json({ error: 'Error al obtener protocolos DeFi', details: error.message });
  }
});

/**
 * @route GET /api/etherscan/defi/protocol/:name
 * @desc Obtiene información detallada sobre un protocolo DeFi específico
 * @access Public
 */
router.get('/defi/protocol/:name', async (req, res) => {
  try {
    const { name } = req.params;
    const protocol = await etherscanService.getDefiProtocolInfo(name);
    res.json(protocol);
  } catch (error) {
    console.error(`Error al obtener información del protocolo ${req.params.name}:`, error);
    res.status(500).json({ error: `Error al obtener información del protocolo ${req.params.name}`, details: error.message });
  }
});

/**
 * @route GET /api/etherscan/analysis/staking/:protocol
 * @desc Analiza el impacto de The Merge en un protocolo de staking
 * @access Public
 */
router.get('/analysis/staking/:protocol', async (req, res) => {
  try {
    const { protocol } = req.params;
    const analysis = await etherscanService.analyzeStakingProtocolPostMerge(protocol);
    res.json(analysis);
  } catch (error) {
    console.error(`Error al analizar impacto post-merge en ${req.params.protocol}:`, error);
    res.status(500).json({ error: `Error al analizar impacto post-merge en ${req.params.protocol}`, details: error.message });
  }
});

module.exports = router;
