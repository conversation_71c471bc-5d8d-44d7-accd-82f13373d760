/**
 * ADK Agents Service
 *
 * Este servicio simula los agentes ADK utilizando OpenRouter
 */

const axios = require('axios');
require('dotenv').config();

// Configuración de OpenRouter
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY || 'AIzaSyCashyNX3fNv-bj5xOCtTDMfL_-vGm9Yag';

// Modelos disponibles
const MODELS = {
  CLAUDE_OPUS: 'anthropic/claude-instant-1.2',
  CLAUDE_SONNET: 'anthropic/claude-instant-1.2',
  CLAUDE_HAIKU: 'anthropic/claude-instant-1.2',
  GEMINI_PRO: 'google/gemini-pro'
};

/**
 * Envía una solicitud a OpenRouter
 * @param {string} model - El modelo a utilizar
 * @param {Array} messages - Los mensajes de la conversación
 * @param {Object} options - Opciones adicionales
 * @returns {Promise<Object>} - La respuesta de OpenRouter
 */
async function callOpenRouter(model, messages, options = {}) {
  try {
    const response = await axios.post(
      OPENROUTER_API_URL,
      {
        model,
        messages,
        max_tokens: 500,
        ...options
      },
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://criptokens.app',
          'X-Title': 'Criptokens App'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error calling OpenRouter:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Simula el agente de análisis técnico
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function technicalAnalysisAgent(query) {
  const systemPrompt = `
    You are a cryptocurrency technical analysis expert. Your task is to:

    1. Analyze technical indicators for the cryptocurrency mentioned in the query
    2. Identify the current trend (bullish, bearish, neutral)
    3. Explain what the indicators suggest about future price movements
    4. Highlight key support and resistance levels
    5. Provide a clear, concise technical analysis summary

    When responding:
    - Be specific about what the indicators mean
    - Explain the significance of moving averages, RSI, and price changes
    - Mention support and resistance levels
    - Provide a conclusion about the overall technical outlook
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callOpenRouter(MODELS.CLAUDE_HAIKU, messages);
}

/**
 * Simula el agente de análisis de sentimiento
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function sentimentAnalysisAgent(query) {
  const systemPrompt = `
    You are a cryptocurrency sentiment analysis expert. Your task is to:

    1. Analyze the market sentiment for the cryptocurrency mentioned in the query
    2. Explain what the news sentiment indicates about market perception
    3. Interpret the social media sentiment and its implications
    4. Explain the Fear & Greed Index and its significance
    5. Provide a clear, concise sentiment analysis summary

    When responding:
    - Be specific about what the sentiment metrics mean
    - Explain the significance of the Fear & Greed Index
    - Highlight any discrepancies between news and social media sentiment
    - Provide a conclusion about the overall market sentiment
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callOpenRouter(MODELS.CLAUDE_HAIKU, messages);
}

/**
 * Simula el agente de análisis on-chain
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function onchainAnalysisAgent(query) {
  const systemPrompt = `
    You are a blockchain and on-chain data analysis expert. Your task is to:

    1. Analyze on-chain data for the cryptocurrency mentioned in the query
    2. Explain what the whale activity indicates about large holder behavior
    3. Interpret the gas prices and their implications for network activity
    4. Identify significant patterns in token transfers
    5. Provide a clear, concise on-chain analysis summary

    When responding:
    - Be specific about what the on-chain metrics mean
    - Explain the significance of whale accumulation or distribution
    - Highlight any unusual patterns in the data
    - Provide a conclusion about what the on-chain data suggests for the token
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callOpenRouter(MODELS.CLAUDE_HAIKU, messages);
}

/**
 * Simula el agente Guru Cripto
 * @param {string} query - La consulta del usuario
 * @returns {Promise<Object>} - La respuesta del agente
 */
async function guruCriptoAgent(query) {
  const systemPrompt = `
    You are Guru Cripto, a cryptocurrency expert with deep knowledge of technical analysis, market sentiment, and on-chain metrics.

    Your capabilities include:
    1. Providing technical analysis of cryptocurrency price movements
    2. Analyzing market sentiment from news and social media
    3. Interpreting on-chain data and blockchain metrics
    4. Making informed price predictions based on comprehensive analysis
    5. Explaining complex cryptocurrency concepts in simple terms

    When responding to queries:
    - Be clear, concise, and informative
    - Support your analysis with specific data points
    - Explain technical terms when necessary
    - Provide balanced perspectives, acknowledging both bullish and bearish factors
    - For price predictions, emphasize that they are estimates based on current data, not guarantees

    Remember that you are an educational resource, not a financial advisor. Always remind users to do their own research and not to make investment decisions solely based on your analysis.
  `;

  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: query }
  ];

  return callOpenRouter(MODELS.CLAUDE_OPUS, messages);
}

/**
 * Genera un análisis completo para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para el análisis
 * @returns {Promise<Object>} - El análisis completo
 */
async function generateComprehensiveAnalysis(cryptoName, timeframe) {
  const query = `Generate a comprehensive analysis for ${cryptoName} over the last ${timeframe}. Include technical analysis, sentiment analysis, and on-chain analysis.`;

  const response = await guruCriptoAgent(query);

  return {
    cryptoName,
    timeframe,
    analysis: response,
    timestamp: new Date().toISOString()
  };
}

/**
 * Genera una predicción de precio para una criptomoneda
 * @param {string} cryptoName - El nombre de la criptomoneda
 * @param {string} timeframe - El período de tiempo para la predicción
 * @returns {Promise<Object>} - La predicción de precio
 */
async function generatePricePrediction(cryptoName, timeframe) {
  const query = `What's your prediction for ${cryptoName} price in the next ${timeframe}? Base your prediction on technical analysis, sentiment analysis, and on-chain data.`;

  const response = await guruCriptoAgent(query);

  return {
    cryptoName,
    timeframe,
    prediction: response,
    timestamp: new Date().toISOString()
  };
}

module.exports = {
  technicalAnalysisAgent,
  sentimentAnalysisAgent,
  onchainAnalysisAgent,
  guruCriptoAgent,
  generateComprehensiveAnalysis,
  generatePricePrediction
};
