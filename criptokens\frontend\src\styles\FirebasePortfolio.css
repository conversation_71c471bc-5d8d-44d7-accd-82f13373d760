/* Estilos específicos para el componente PortfolioFirebase */

.firebase-portfolio-container {
  background: var(--bg-dark);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  margin-bottom: 1.5rem;
  border: var(--border-light);
  color: var(--text-medium);
}

.firebase-portfolio-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: var(--border-light);
}

.firebase-portfolio-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.firebase-portfolio-title-section h2 {
  font-size: 1.5rem;
  margin: 0;
  color: var(--text-bright);
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.firebase-portfolio-funds {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.firebase-funds-label {
  color: var(--text-dim);
  font-size: 0.9rem;
}

.firebase-funds-value {
  font-weight: 600;
  color: var(--text-bright);
}

.firebase-add-funds-button {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.4rem 0.75rem;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.firebase-add-funds-button:hover {
  box-shadow: 0 0 10px var(--primary-glow);
  transform: translateY(-1px);
}

.firebase-portfolio-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  width: 100%;
}

.firebase-refresh-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
}

.firebase-refresh-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.firebase-refresh-button:disabled {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-dim);
  cursor: not-allowed;
  border-color: rgba(255, 255, 255, 0.05);
}

.firebase-add-asset-button {
  background: var(--gradient-primary);
  color: white;
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
}

.firebase-add-asset-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s;
}

.firebase-add-asset-button:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-1px);
}

.firebase-add-asset-button:hover::before {
  left: 100%;
}

.firebase-portfolio-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: var(--border-light);
}

.firebase-summary-item {
  display: flex;
  flex-direction: column;
}

.firebase-summary-label {
  font-size: 0.875rem;
  color: var(--text-dim);
  margin-bottom: 0.25rem;
}

.firebase-summary-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-bright);
}

.firebase-positive {
  color: var(--success);
}

.firebase-negative {
  color: var(--error);
}

.firebase-date {
  font-size: 0.9375rem;
  font-weight: normal;
}

.firebase-portfolio-assets {
  overflow-x: auto;
}

.firebase-assets-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9375rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: var(--border-light);
}

.firebase-assets-table th {
  text-align: left;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-dim);
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.2);
}

.firebase-assets-table td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-medium);
}

.firebase-asset-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.firebase-asset-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px;
}

.firebase-asset-name {
  display: block;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-bright);
}

.firebase-asset-symbol {
  display: block;
  font-size: 0.8125rem;
  color: var(--text-dim);
}

.firebase-remove-asset-button {
  background-color: rgba(255, 59, 48, 0.8);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.firebase-remove-asset-button:hover {
  background-color: rgba(255, 59, 48, 1);
  box-shadow: 0 0 10px rgba(255, 59, 48, 0.5);
}

.firebase-empty-portfolio {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-dim);
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: var(--border-light);
  min-height: 250px;
}

.firebase-empty-portfolio p {
  margin: 0.5rem 0;
  color: var(--text-medium);
  max-width: 500px;
}

.firebase-add-asset-form-container {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: var(--border-light);
}

.firebase-add-asset-form {
  max-width: 500px;
  margin: 0 auto;
}

.firebase-add-asset-form h3 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--text-bright);
  text-align: center;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.firebase-form-error {
  color: var(--error);
  background-color: rgba(255, 59, 48, 0.1);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1rem;
  font-size: 0.9375rem;
  border-left: 3px solid var(--error);
}

.firebase-form-group {
  margin-bottom: 1.25rem;
}

.firebase-form-group label {
  display: block;
  font-size: 0.9375rem;
  color: var(--text-medium);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.firebase-form-group input,
.firebase-form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  font-size: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--text-bright);
  transition: all var(--transition-fast);
}

.firebase-form-group input:focus,
.firebase-form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 242, 255, 0.2);
}

.firebase-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.firebase-cancel-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.firebase-cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.firebase-submit-button {
  background: var(--gradient-primary);
  color: white;
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.firebase-submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s;
}

.firebase-submit-button:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-1px);
}

.firebase-submit-button:hover::before {
  left: 100%;
}

.firebase-submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Responsive */
@media (max-width: 768px) {
  .firebase-portfolio-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .firebase-portfolio-actions {
    width: 100%;
  }

  .firebase-refresh-button,
  .firebase-add-asset-button {
    flex: 1;
  }

  .firebase-portfolio-summary {
    flex-direction: column;
    gap: 1rem;
  }

  .firebase-assets-table {
    font-size: 0.875rem;
  }

  .firebase-assets-table th,
  .firebase-assets-table td {
    padding: 0.75rem 0.5rem;
  }

  .firebase-assets-table th:nth-child(3),
  .firebase-assets-table td:nth-child(3),
  .firebase-assets-table th:nth-child(4),
  .firebase-assets-table td:nth-child(4) {
    display: none;
  }
}
