import json
from typing import Dict, Any, AsyncIterable, List, Optional
import logging
import aiohttp
import os
from datetime import datetime
import asyncio
from ...common.client.client import A2AClient

logger = logging.getLogger(__name__)

class GuruAgent:
    """Coordinator agent that orchestrates other specialized agents."""
    
    SUPPORTED_CONTENT_TYPES = ["text", "data"]
    
    def __init__(self):
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY", "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")
        
        # Initialize clients for specialized agents
        self.technical_client = A2AClient(url="http://localhost:3201")
        self.sentiment_client = A2AClient(url="http://localhost:3202")
        self.onchain_client = A2AClient(url="http://localhost:3203")
    
    async def _extract_query_info(self, query: str) -> Dict[str, Any]:
        """Extract information from the query.
        
        Args:
            query: The user's query
            
        Returns:
            Extracted information
        """
        # Extract cryptocurrency
        crypto = self._extract_crypto_name(query)
        
        # Extract timeframe
        timeframe = self._extract_timeframe(query)
        
        # Extract analysis types
        analysis_types = self._extract_analysis_types(query)
        
        return {
            "crypto": crypto,
            "timeframe": timeframe,
            "analysis_types": analysis_types
        }
    
    def _extract_crypto_name(self, query: str) -> str:
        """Extract cryptocurrency name from query.
        
        Args:
            query: The user's query
            
        Returns:
            Cryptocurrency name
        """
        # Common cryptocurrencies
        crypto_names = {
            "bitcoin": "Bitcoin",
            "btc": "Bitcoin",
            "ethereum": "Ethereum",
            "eth": "Ethereum",
            "binance coin": "Binance Coin",
            "bnb": "Binance Coin",
            "cardano": "Cardano",
            "ada": "Cardano",
            "solana": "Solana",
            "sol": "Solana",
            "xrp": "XRP",
            "dogecoin": "Dogecoin",
            "doge": "Dogecoin",
            "polkadot": "Polkadot",
            "dot": "Polkadot"
        }
        
        query_lower = query.lower()
        
        # Check for exact matches
        for crypto_name_lower, crypto_name in crypto_names.items():
            if crypto_name_lower in query_lower:
                return crypto_name
        
        # Default to Bitcoin if no match found
        return "Bitcoin"
    
    def _extract_timeframe(self, query: str) -> str:
        """Extract timeframe from query.
        
        Args:
            query: The user's query
            
        Returns:
            Timeframe description
        """
        query_lower = query.lower()
        
        if "year" in query_lower or "365" in query_lower:
            return "1 year"
        elif "6 month" in query_lower or "180" in query_lower:
            return "6 months"
        elif "3 month" in query_lower or "90" in query_lower:
            return "3 months"
        elif "month" in query_lower or "30" in query_lower:
            return "1 month"
        elif "week" in query_lower or "7" in query_lower:
            return "1 week"
        elif "day" in query_lower or "24" in query_lower:
            return "24 hours"
        
        # Default to 1 week
        return "1 week"
    
    def _extract_analysis_types(self, query: str) -> List[str]:
        """Extract analysis types from query.
        
        Args:
            query: The user's query
            
        Returns:
            List of analysis types
        """
        query_lower = query.lower()
        
        analysis_types = []
        
        # Check for technical analysis keywords
        if any(keyword in query_lower for keyword in ["technical", "price", "chart", "pattern", "indicator"]):
            analysis_types.append("technical")
        
        # Check for sentiment analysis keywords
        if any(keyword in query_lower for keyword in ["sentiment", "news", "social", "media", "fear", "greed"]):
            analysis_types.append("sentiment")
        
        # Check for on-chain analysis keywords
        if any(keyword in query_lower for keyword in ["on-chain", "onchain", "blockchain", "whale", "transaction"]):
            analysis_types.append("onchain")
        
        # If no specific analysis types mentioned, include all
        if not analysis_types:
            analysis_types = ["technical", "sentiment", "onchain"]
        
        return analysis_types
    
    async def _get_technical_analysis(self, crypto: str, timeframe: str, session_id: str) -> Dict[str, Any]:
        """Get technical analysis from the technical agent.
        
        Args:
            crypto: Cryptocurrency name
            timeframe: Timeframe description
            session_id: Session ID
            
        Returns:
            Technical analysis data
        """
        try:
            # Create task ID
            task_id = f"technical_{session_id}_{datetime.now().timestamp()}"
            
            # Create query
            query = f"Analyze {crypto} technical indicators for {timeframe}"
            
            # Send task to technical agent
            response = await self.technical_client.send_task({
                "id": task_id,
                "sessionId": session_id,
                "message": {
                    "role": "user",
                    "parts": [{"type": "text", "text": query}]
                }
            })
            
            # Extract result
            if response and response.result:
                # Check for artifacts
                if response.result.artifacts and len(response.result.artifacts) > 0:
                    for part in response.result.artifacts[0].parts:
                        if hasattr(part, "type") and part.type == "data":
                            return part.data
                
                # Check for message
                if response.result.status and response.result.status.message:
                    for part in response.result.status.message.parts:
                        if hasattr(part, "type") and part.type == "data":
                            return part.data
            
            # If we couldn't extract structured data, return error
            return {"error": "Failed to get technical analysis"}
        except Exception as e:
            logger.error(f"Error getting technical analysis: {e}")
            return {"error": f"Error getting technical analysis: {str(e)}"}
    
    async def _get_sentiment_analysis(self, crypto: str, timeframe: str, session_id: str) -> Dict[str, Any]:
        """Get sentiment analysis from the sentiment agent.
        
        Args:
            crypto: Cryptocurrency name
            timeframe: Timeframe description
            session_id: Session ID
            
        Returns:
            Sentiment analysis data
        """
        try:
            # Create task ID
            task_id = f"sentiment_{session_id}_{datetime.now().timestamp()}"
            
            # Create query
            query = f"Analyze {crypto} sentiment for {timeframe}"
            
            # Send task to sentiment agent
            response = await self.sentiment_client.send_task({
                "id": task_id,
                "sessionId": session_id,
                "message": {
                    "role": "user",
                    "parts": [{"type": "text", "text": query}]
                }
            })
            
            # Extract result
            if response and response.result:
                # Check for artifacts
                if response.result.artifacts and len(response.result.artifacts) > 0:
                    for part in response.result.artifacts[0].parts:
                        if hasattr(part, "type") and part.type == "data":
                            return part.data
                
                # Check for message
                if response.result.status and response.result.status.message:
                    for part in response.result.status.message.parts:
                        if hasattr(part, "type") and part.type == "data":
                            return part.data
            
            # If we couldn't extract structured data, return error
            return {"error": "Failed to get sentiment analysis"}
        except Exception as e:
            logger.error(f"Error getting sentiment analysis: {e}")
            return {"error": f"Error getting sentiment analysis: {str(e)}"}
    
    async def _get_onchain_analysis(self, crypto: str, timeframe: str, session_id: str) -> Dict[str, Any]:
        """Get on-chain analysis from the on-chain agent.
        
        Args:
            crypto: Cryptocurrency name
            timeframe: Timeframe description
            session_id: Session ID
            
        Returns:
            On-chain analysis data
        """
        try:
            # Create task ID
            task_id = f"onchain_{session_id}_{datetime.now().timestamp()}"
            
            # Create query
            query = f"Analyze {crypto} on-chain data for {timeframe}"
            
            # Send task to on-chain agent
            response = await self.onchain_client.send_task({
                "id": task_id,
                "sessionId": session_id,
                "message": {
                    "role": "user",
                    "parts": [{"type": "text", "text": query}]
                }
            })
            
            # Extract result
            if response and response.result:
                # Check for artifacts
                if response.result.artifacts and len(response.result.artifacts) > 0:
                    for part in response.result.artifacts[0].parts:
                        if hasattr(part, "type") and part.type == "data":
                            return part.data
                
                # Check for message
                if response.result.status and response.result.status.message:
                    for part in response.result.status.message.parts:
                        if hasattr(part, "type") and part.type == "data":
                            return part.data
            
            # If we couldn't extract structured data, return error
            return {"error": "Failed to get on-chain analysis"}
        except Exception as e:
            logger.error(f"Error getting on-chain analysis: {e}")
            return {"error": f"Error getting on-chain analysis: {str(e)}"}
    
    async def _generate_prediction(
        self, 
        query: str,
        crypto: str,
        timeframe: str,
        technical_data: Dict[str, Any],
        sentiment_data: Dict[str, Any],
        onchain_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a prediction based on all analyses.
        
        Args:
            query: Original user query
            crypto: Cryptocurrency name
            timeframe: Timeframe description
            technical_data: Technical analysis data
            sentiment_data: Sentiment analysis data
            onchain_data: On-chain analysis data
            
        Returns:
            Prediction data
        """
        try:
            # Extract key metrics for context
            context = {
                "crypto": crypto,
                "timeframe": timeframe,
                "technicalAnalysis": {
                    "trend": technical_data.get("indicators", {}).get("trend", "neutral"),
                    "rsi": technical_data.get("indicators", {}).get("rsi", 50),
                    "priceChange24h": technical_data.get("indicators", {}).get("price_change_24h", 0),
                    "priceChange7d": technical_data.get("indicators", {}).get("price_change_7d", 0),
                    "support": technical_data.get("indicators", {}).get("support", 0),
                    "resistance": technical_data.get("indicators", {}).get("resistance", 0)
                },
                "sentimentAnalysis": {
                    "newsScore": sentiment_data.get("news_sentiment", {}).get("sentiment_score", 0),
                    "socialScore": sentiment_data.get("social_sentiment", {}).get("overall", {}).get("sentiment_score", 0),
                    "fearGreedIndex": sentiment_data.get("fear_greed_index", 50),
                    "fearGreedClassification": sentiment_data.get("fear_greed_classification", "Neutral")
                },
                "onChainData": {
                    "whaleInflows": onchain_data.get("whale_analysis", {}).get("whale_inflows", 0),
                    "whaleOutflows": onchain_data.get("whale_analysis", {}).get("whale_outflows", 0),
                    "netWhaleFlow": onchain_data.get("whale_analysis", {}).get("net_whale_flow", 0),
                    "onChainSentiment": onchain_data.get("whale_analysis", {}).get("on_chain_sentiment", 0)
                },
                "marketConditions": {
                    "btcDominance": 40 + (5 * (1 if crypto == "Bitcoin" else -1)),
                    "globalMarketCap": 1.5 + (0.2 * (1 if sentiment_data.get("fear_greed_index", 50) > 50 else -1)),
                    "fearGreedIndex": sentiment_data.get("fear_greed_index", 50)
                }
            }
            
            # Use OpenRouter API to generate prediction
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_api_key}",
                    "Content-Type": "application/json"
                }
                
                # Create prompt for prediction
                prompt = self._create_prediction_prompt(query, context)
                
                payload = {
                    "model": "anthropic/claude-3-opus-20240229",
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are Guru Cripto, an expert cryptocurrency analyst and predictor. You provide detailed, data-driven analysis and predictions based on technical indicators, market sentiment, and on-chain data."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "response_format": {
                        "type": "json_object"
                    }
                }
                
                async with session.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    json=payload,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")
                        try:
                            prediction = json.loads(content)
                            
                            # Add context data to prediction
                            prediction["context"] = context
                            
                            return prediction
                        except json.JSONDecodeError:
                            logger.error(f"Failed to parse prediction result: {content}")
            
            # If we reach here, something went wrong
            return {
                "error": "Failed to generate prediction",
                "context": context
            }
        except Exception as e:
            logger.error(f"Error generating prediction: {e}")
            return {"error": f"Error generating prediction: {str(e)}"}
    
    def _create_prediction_prompt(self, query: str, context: Dict[str, Any]) -> str:
        """Create a prompt for the prediction model.
        
        Args:
            query: Original user query
            context: Analysis context
            
        Returns:
            Prompt for the prediction model
        """
        prompt = f"""
        QUERY: {query}
        
        CRYPTOCURRENCY: {context['crypto']}
        
        TECHNICAL ANALYSIS:
        - Trend: {context['technicalAnalysis']['trend']}
        - RSI: {context['technicalAnalysis']['rsi']}
        - 24h Price Change: {context['technicalAnalysis']['priceChange24h']}%
        - 7d Price Change: {context['technicalAnalysis']['priceChange7d']}%
        - Support Level: {context['technicalAnalysis']['support']}
        - Resistance Level: {context['technicalAnalysis']['resistance']}
        
        SENTIMENT ANALYSIS:
        - News Sentiment Score: {context['sentimentAnalysis']['newsScore']} (-100 to +100)
        - Social Media Sentiment Score: {context['sentimentAnalysis']['socialScore']} (-100 to +100)
        - Fear & Greed Index: {context['sentimentAnalysis']['fearGreedIndex']} (0-100)
        - Fear & Greed Classification: {context['sentimentAnalysis']['fearGreedClassification']}
        
        ON-CHAIN DATA:
        - Whale Inflows: {context['onChainData']['whaleInflows']}
        - Whale Outflows: {context['onChainData']['whaleOutflows']}
        - Net Whale Flow: {context['onChainData']['netWhaleFlow']} ({context['onChainData']['netWhaleFlow'] > 0 and 'positive' or 'negative'})
        - On-Chain Sentiment: {context['onChainData']['onChainSentiment']} (-100 to 100)
        
        MARKET CONDITIONS:
        - Bitcoin Dominance: {context['marketConditions']['btcDominance']}%
        - Global Market Cap: ${context['marketConditions']['globalMarketCap']} trillion
        - Fear & Greed Index: {context['marketConditions']['fearGreedIndex']} (0-100, where 0 is extreme fear and 100 is extreme greed)
        
        TIMEFRAME FOR PREDICTION: {context['timeframe']}
        
        INSTRUCTIONS:
        Based on the above data, provide a comprehensive analysis and price prediction for {context['crypto']} over the {context['timeframe']} timeframe. Your response should be in JSON format with the following structure:
        
        {
            "summary": "A concise 2-3 sentence summary of your prediction",
            "detailed_analysis": {
                "technical_factors": "Analysis of technical indicators and what they suggest",
                "sentiment_factors": "Analysis of market sentiment and its implications",
                "on_chain_factors": "Analysis of on-chain data and what it reveals",
                "market_conditions": "Analysis of broader market conditions"
            },
            "price_prediction": {
                "direction": "up", // One of: up, down, sideways
                "confidence": 75, // 0-100
                "percentage_range": "5-10%", // Expected percentage change
                "key_levels": {
                    "support": [value1, value2],
                    "resistance": [value1, value2]
                }
            },
            "risks": ["Risk factor 1", "Risk factor 2", "Risk factor 3"],
            "opportunities": ["Opportunity 1", "Opportunity 2", "Opportunity 3"],
            "recommendation": "buy" // One of: buy, sell, hold, wait
        }
        
        Make sure your prediction is data-driven and balanced, considering all the factors provided. Be specific about price movements and key levels.
        """
        
        return prompt
    
    async def process_query(self, query: str, session_id: str) -> Dict[str, Any]:
        """Process a query and return a comprehensive analysis.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            Comprehensive analysis and prediction
        """
        # Extract information from query
        query_info = await self._extract_query_info(query)
        crypto = query_info["crypto"]
        timeframe = query_info["timeframe"]
        analysis_types = query_info["analysis_types"]
        
        # Initialize tasks for parallel execution
        tasks = []
        
        # Add tasks based on requested analysis types
        if "technical" in analysis_types:
            tasks.append(self._get_technical_analysis(crypto, timeframe, session_id))
        
        if "sentiment" in analysis_types:
            tasks.append(self._get_sentiment_analysis(crypto, timeframe, session_id))
        
        if "onchain" in analysis_types:
            tasks.append(self._get_onchain_analysis(crypto, timeframe, session_id))
        
        # Execute tasks in parallel
        results = await asyncio.gather(*tasks)
        
        # Process results
        technical_data = {}
        sentiment_data = {}
        onchain_data = {}
        
        result_index = 0
        if "technical" in analysis_types:
            technical_data = results[result_index]
            result_index += 1
        
        if "sentiment" in analysis_types:
            sentiment_data = results[result_index]
            result_index += 1
        
        if "onchain" in analysis_types:
            onchain_data = results[result_index]
        
        # Generate prediction
        prediction = await self._generate_prediction(
            query, crypto, timeframe, technical_data, sentiment_data, onchain_data
        )
        
        # Combine all data
        return {
            "query": query,
            "crypto": crypto,
            "timeframe": timeframe,
            "technical_analysis": technical_data,
            "sentiment_analysis": sentiment_data,
            "onchain_analysis": onchain_data,
            "prediction": prediction
        }
    
    async def invoke(self, query: str, session_id: str) -> str:
        """Process a query and return a text response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Returns:
            Text response
        """
        result = await self.process_query(query, session_id)
        
        if "error" in result:
            return f"Error: {result['error']}"
        
        # Extract prediction
        prediction = result.get("prediction", {})
        
        # Format response
        response = f"# Analysis for {result['crypto']} ({result['timeframe']})\n\n"
        
        if "summary" in prediction:
            response += f"## Summary\n{prediction['summary']}\n\n"
        
        if "detailed_analysis" in prediction:
            response += "## Detailed Analysis\n"
            detailed = prediction["detailed_analysis"]
            
            if "technical_factors" in detailed:
                response += f"### Technical Factors\n{detailed['technical_factors']}\n\n"
            
            if "sentiment_factors" in detailed:
                response += f"### Sentiment Factors\n{detailed['sentiment_factors']}\n\n"
            
            if "on_chain_factors" in detailed:
                response += f"### On-Chain Factors\n{detailed['on_chain_factors']}\n\n"
            
            if "market_conditions" in detailed:
                response += f"### Market Conditions\n{detailed['market_conditions']}\n\n"
        
        if "price_prediction" in prediction:
            response += "## Price Prediction\n"
            price = prediction["price_prediction"]
            
            direction = price.get("direction", "unknown")
            confidence = price.get("confidence", 0)
            percentage = price.get("percentage_range", "unknown")
            
            response += f"Direction: {direction.capitalize()} (Confidence: {confidence}%)\n"
            response += f"Expected Range: {percentage}\n\n"
            
            if "key_levels" in price:
                response += "### Key Levels\n"
                levels = price["key_levels"]
                
                if "support" in levels:
                    response += f"Support: {', '.join(map(str, levels['support']))}\n"
                
                if "resistance" in levels:
                    response += f"Resistance: {', '.join(map(str, levels['resistance']))}\n\n"
        
        if "risks" in prediction:
            response += "## Risks\n"
            for risk in prediction["risks"]:
                response += f"- {risk}\n"
            response += "\n"
        
        if "opportunities" in prediction:
            response += "## Opportunities\n"
            for opportunity in prediction["opportunities"]:
                response += f"- {opportunity}\n"
            response += "\n"
        
        if "recommendation" in prediction:
            recommendation = prediction["recommendation"].upper()
            response += f"## Recommendation: {recommendation}\n"
        
        return response
    
    async def stream(self, query: str, session_id: str) -> AsyncIterable[Dict[str, Any]]:
        """Process a query and stream the response.
        
        Args:
            query: The user's query
            session_id: Session ID
            
        Yields:
            Response updates
        """
        # Extract information from query
        query_info = await self._extract_query_info(query)
        crypto = query_info["crypto"]
        timeframe = query_info["timeframe"]
        analysis_types = query_info["analysis_types"]
        
        # Update on starting analysis
        yield {
            "is_task_complete": False,
            "updates": f"Starting comprehensive analysis for {crypto} ({timeframe})..."
        }
        
        # Get technical analysis if requested
        technical_data = {}
        if "technical" in analysis_types:
            yield {
                "is_task_complete": False,
                "updates": "Fetching technical analysis..."
            }
            
            technical_data = await self._get_technical_analysis(crypto, timeframe, session_id)
        
        # Get sentiment analysis if requested
        sentiment_data = {}
        if "sentiment" in analysis_types:
            yield {
                "is_task_complete": False,
                "updates": "Fetching sentiment analysis..."
            }
            
            sentiment_data = await self._get_sentiment_analysis(crypto, timeframe, session_id)
        
        # Get on-chain analysis if requested
        onchain_data = {}
        if "onchain" in analysis_types:
            yield {
                "is_task_complete": False,
                "updates": "Fetching on-chain analysis..."
            }
            
            onchain_data = await self._get_onchain_analysis(crypto, timeframe, session_id)
        
        # Update on generating prediction
        yield {
            "is_task_complete": False,
            "updates": "Generating prediction based on all analyses..."
        }
        
        # Generate prediction
        prediction = await self._generate_prediction(
            query, crypto, timeframe, technical_data, sentiment_data, onchain_data
        )
        
        # Combine all data
        result = {
            "query": query,
            "crypto": crypto,
            "timeframe": timeframe,
            "technical_analysis": technical_data,
            "sentiment_analysis": sentiment_data,
            "onchain_analysis": onchain_data,
            "prediction": prediction
        }
        
        # Final response with full analysis
        yield {
            "is_task_complete": True,
            "content": result
        }
