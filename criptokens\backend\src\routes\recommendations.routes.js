/**
 * Rutas para las recomendaciones personalizadas
 */

const express = require('express');
const router = express.Router();
const recommendationsService = require('../services/recommendations.service');

/**
 * @route GET /api/recommendations/:userId/diversification
 * @desc Obtiene recomendaciones de diversificación para un usuario
 * @access Public
 */
router.get('/:userId/diversification', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ error: 'Se requiere el ID del usuario' });
    }
    
    const recommendations = await recommendationsService.generateDiversificationRecommendations(userId);
    res.json(recommendations);
  } catch (error) {
    console.error('Error al obtener recomendaciones de diversificación:', error);
    res.status(500).json({ error: 'Error al obtener recomendaciones de diversificación', details: error.message });
  }
});

/**
 * @route GET /api/recommendations/:userId/sentiment
 * @desc Obtiene recomendaciones basadas en sentimiento para un usuario
 * @access Public
 */
router.get('/:userId/sentiment', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ error: 'Se requiere el ID del usuario' });
    }
    
    const recommendations = await recommendationsService.generateSentimentBasedRecommendations(userId);
    res.json(recommendations);
  } catch (error) {
    console.error('Error al obtener recomendaciones basadas en sentimiento:', error);
    res.status(500).json({ error: 'Error al obtener recomendaciones basadas en sentimiento', details: error.message });
  }
});

/**
 * @route GET /api/recommendations/:userId/rebalancing
 * @desc Obtiene recomendaciones de rebalanceo para un usuario
 * @access Public
 */
router.get('/:userId/rebalancing', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ error: 'Se requiere el ID del usuario' });
    }
    
    const recommendations = await recommendationsService.generateRebalancingRecommendations(userId);
    res.json(recommendations);
  } catch (error) {
    console.error('Error al obtener recomendaciones de rebalanceo:', error);
    res.status(500).json({ error: 'Error al obtener recomendaciones de rebalanceo', details: error.message });
  }
});

/**
 * @route GET /api/recommendations/:userId/all
 * @desc Obtiene todas las recomendaciones para un usuario
 * @access Public
 */
router.get('/:userId/all', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ error: 'Se requiere el ID del usuario' });
    }
    
    const recommendations = await recommendationsService.generateAllRecommendations(userId);
    res.json(recommendations);
  } catch (error) {
    console.error('Error al obtener todas las recomendaciones:', error);
    res.status(500).json({ error: 'Error al obtener todas las recomendaciones', details: error.message });
  }
});

module.exports = router;
