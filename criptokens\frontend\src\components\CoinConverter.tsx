import React, { useState, useEffect } from 'react';
import '../styles/CoinConverter.css';

interface CoinConverterProps {
  coinData: any;
  formatNumber: (num: number, maximumFractionDigits?: number) => string;
}

const CoinConverter: React.FC<CoinConverterProps> = ({ 
  coinData, 
  formatNumber 
}) => {
  const [fromAmount, setFromAmount] = useState<string>('1');
  const [toAmount, setToAmount] = useState<string>('');
  const [fromCurrency, setFromCurrency] = useState<string>(coinData.symbol.toUpperCase());
  const [toCurrency, setToCurrency] = useState<string>('USD');
  const [currencies, setCurrencies] = useState<string[]>(['USD', 'EUR', 'GBP', 'JPY', 'BTC', 'ETH']);

  // Obtener el precio actual de la criptomoneda
  const currentPrice = coinData.market_data?.current_price?.usd || 0;

  // Efecto para actualizar el monto de destino cuando cambia el monto de origen o las monedas
  useEffect(() => {
    convertCurrency(fromAmount, fromCurrency, toCurrency);
  }, [fromAmount, fromCurrency, toCurrency, coinData]);

  // Función para convertir entre monedas
  const convertCurrency = (amount: string, from: string, to: string) => {
    if (!amount || isNaN(parseFloat(amount))) {
      setToAmount('');
      return;
    }

    const amountValue = parseFloat(amount);
    
    // Convertir a USD primero
    let valueInUSD = 0;
    
    if (from === coinData.symbol.toUpperCase()) {
      valueInUSD = amountValue * currentPrice;
    } else if (from === 'USD') {
      valueInUSD = amountValue;
    } else if (from === 'EUR') {
      valueInUSD = amountValue * 1.1; // Tasa de conversión aproximada EUR a USD
    } else if (from === 'GBP') {
      valueInUSD = amountValue * 1.3; // Tasa de conversión aproximada GBP a USD
    } else if (from === 'JPY') {
      valueInUSD = amountValue * 0.0067; // Tasa de conversión aproximada JPY a USD
    } else if (from === 'BTC') {
      valueInUSD = amountValue * 85000; // Precio aproximado de BTC en USD
    } else if (from === 'ETH') {
      valueInUSD = amountValue * 3000; // Precio aproximado de ETH en USD
    }
    
    // Convertir de USD a la moneda de destino
    let result = 0;
    
    if (to === coinData.symbol.toUpperCase()) {
      result = valueInUSD / currentPrice;
    } else if (to === 'USD') {
      result = valueInUSD;
    } else if (to === 'EUR') {
      result = valueInUSD / 1.1; // Tasa de conversión aproximada USD a EUR
    } else if (to === 'GBP') {
      result = valueInUSD / 1.3; // Tasa de conversión aproximada USD a GBP
    } else if (to === 'JPY') {
      result = valueInUSD / 0.0067; // Tasa de conversión aproximada USD a JPY
    } else if (to === 'BTC') {
      result = valueInUSD / 85000; // Precio aproximado de USD a BTC
    } else if (to === 'ETH') {
      result = valueInUSD / 3000; // Precio aproximado de USD a ETH
    }
    
    // Formatear el resultado según la moneda
    if (to === 'BTC' || to === 'ETH' || to === coinData.symbol.toUpperCase()) {
      setToAmount(result.toFixed(8));
    } else if (to === 'JPY') {
      setToAmount(result.toFixed(0));
    } else {
      setToAmount(result.toFixed(2));
    }
  };

  // Función para intercambiar las monedas
  const swapCurrencies = () => {
    const tempFromCurrency = fromCurrency;
    const tempFromAmount = fromAmount;
    
    setFromCurrency(toCurrency);
    setToCurrency(tempFromCurrency);
    setFromAmount(toAmount);
    setToAmount(tempFromAmount);
  };

  // Función para manejar el cambio en el monto de origen
  const handleFromAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setFromAmount(value);
    }
  };

  // Función para manejar el cambio en el monto de destino
  const handleToAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setToAmount(value);
      
      // Convertir en la dirección opuesta
      if (value === '') {
        setFromAmount('');
      } else {
        const amountValue = parseFloat(value);
        convertCurrency(value, toCurrency, fromCurrency);
      }
    }
  };

  // Función para obtener el símbolo de la moneda
  const getCurrencySymbol = (currency: string): string => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'BTC':
        return '₿';
      case 'ETH':
        return 'Ξ';
      default:
        return '';
    }
  };

  // Asegurarse de que la moneda de la criptomoneda esté en la lista
  useEffect(() => {
    if (!currencies.includes(coinData.symbol.toUpperCase())) {
      setCurrencies([...currencies, coinData.symbol.toUpperCase()]);
    }
  }, [coinData.symbol, currencies]);

  return (
    <div className="coin-converter-container">
      <h3>Conversor de {coinData.name}</h3>
      
      <div className="converter-description">
        <p>Convierte fácilmente entre {coinData.name} y otras monedas. Los precios se actualizan en tiempo real.</p>
      </div>
      
      <div className="converter-card">
        <div className="converter-form">
          {/* Campo de origen */}
          <div className="converter-input-group">
            <label htmlFor="fromAmount">De</label>
            <div className="input-with-select">
              <div className="currency-symbol">{getCurrencySymbol(fromCurrency)}</div>
              <input
                id="fromAmount"
                type="text"
                value={fromAmount}
                onChange={handleFromAmountChange}
                placeholder="0"
              />
              <select
                value={fromCurrency}
                onChange={(e) => setFromCurrency(e.target.value)}
              >
                {currencies.map((currency) => (
                  <option key={`from-${currency}`} value={currency}>
                    {currency}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          {/* Botón de intercambio */}
          <button className="swap-button" onClick={swapCurrencies}>
            <i className="fas fa-exchange-alt"></i>
          </button>
          
          {/* Campo de destino */}
          <div className="converter-input-group">
            <label htmlFor="toAmount">A</label>
            <div className="input-with-select">
              <div className="currency-symbol">{getCurrencySymbol(toCurrency)}</div>
              <input
                id="toAmount"
                type="text"
                value={toAmount}
                onChange={handleToAmountChange}
                placeholder="0"
              />
              <select
                value={toCurrency}
                onChange={(e) => setToCurrency(e.target.value)}
              >
                {currencies.map((currency) => (
                  <option key={`to-${currency}`} value={currency}>
                    {currency}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        
        {/* Información de la tasa de conversión */}
        <div className="conversion-rate">
          <p>
            1 {fromCurrency} = {' '}
            {fromCurrency === coinData.symbol.toUpperCase() && toCurrency === 'USD' ? (
              `${getCurrencySymbol('USD')}${formatNumber(currentPrice)}`
            ) : fromCurrency === 'USD' && toCurrency === coinData.symbol.toUpperCase() ? (
              `${formatNumber(1 / currentPrice, 8)} ${coinData.symbol.toUpperCase()}`
            ) : (
              'Tasa calculada'
            )}
          </p>
        </div>
      </div>
      
      {/* Tabla de conversiones comunes */}
      <div className="common-conversions">
        <h4>Conversiones comunes</h4>
        <div className="conversions-grid">
          <div className="conversion-item">
            <div className="conversion-label">1 {coinData.symbol.toUpperCase()}</div>
            <div className="conversion-value">${formatNumber(currentPrice)}</div>
          </div>
          <div className="conversion-item">
            <div className="conversion-label">10 {coinData.symbol.toUpperCase()}</div>
            <div className="conversion-value">${formatNumber(currentPrice * 10)}</div>
          </div>
          <div className="conversion-item">
            <div className="conversion-label">100 {coinData.symbol.toUpperCase()}</div>
            <div className="conversion-value">${formatNumber(currentPrice * 100)}</div>
          </div>
          <div className="conversion-item">
            <div className="conversion-label">1,000 {coinData.symbol.toUpperCase()}</div>
            <div className="conversion-value">${formatNumber(currentPrice * 1000)}</div>
          </div>
          <div className="conversion-item">
            <div className="conversion-label">$100</div>
            <div className="conversion-value">{formatNumber(100 / currentPrice, 8)} {coinData.symbol.toUpperCase()}</div>
          </div>
          <div className="conversion-item">
            <div className="conversion-label">$1,000</div>
            <div className="conversion-value">{formatNumber(1000 / currentPrice, 8)} {coinData.symbol.toUpperCase()}</div>
          </div>
          <div className="conversion-item">
            <div className="conversion-label">$10,000</div>
            <div className="conversion-value">{formatNumber(10000 / currentPrice, 8)} {coinData.symbol.toUpperCase()}</div>
          </div>
          <div className="conversion-item">
            <div className="conversion-label">$100,000</div>
            <div className="conversion-value">{formatNumber(100000 / currentPrice, 8)} {coinData.symbol.toUpperCase()}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoinConverter;
