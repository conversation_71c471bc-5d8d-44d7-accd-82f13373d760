"""
Context7 MCP Tool for ADK Agents

This module provides a tool for ADK agents to interact with the Context7 MCP server.
"""
import os
from typing import Dict, Any, List, Optional
from .base_mcp_tool import BaseMcpTool

class Context7McpTool(BaseMcpTool):
    """Tool for interacting with the Context7 MCP server."""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the Context7 MCP tool.
        
        Args:
            base_url: Base URL of the Context7 MCP server (optional)
        """
        super().__init__("context7", base_url)
    
    async def resolve_library_id(self, library_name: str) -> str:
        """
        Resolve the ID of a library.
        
        Args:
            library_name: Name of the library
            
        Returns:
            Library ID
        """
        return await self.execute_tool("resolve-library-id", {
            "name": library_name
        })
    
    async def get_library_docs(self, library_name: str, topic: Optional[str] = None, max_tokens: int = 4000) -> Dict[str, Any]:
        """
        Get documentation for a library.
        
        Args:
            library_name: Name of the library
            topic: Specific topic to search for (optional)
            max_tokens: Maximum number of tokens to return
            
        Returns:
            Library documentation
        """
        # First, resolve the library ID
        library_id = await self.resolve_library_id(library_name)
        
        # Parameters for the get-library-docs tool
        params = {
            "libraryId": library_id,
            "maxTokens": max_tokens
        }
        
        # Add the topic if provided
        if topic:
            params["topic"] = topic
        
        # Get the documentation
        documentation = await self.execute_tool("get-library-docs", params)
        
        return {
            "libraryName": library_name,
            "libraryId": library_id,
            "topic": topic,
            "documentation": documentation
        })
    
    async def get_multiple_library_docs(self, library_names: List[str], topic: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get documentation for multiple libraries.
        
        Args:
            library_names: List of library names
            topic: Common topic to search for in all libraries (optional)
            
        Returns:
            Documentation for multiple libraries
        """
        results = []
        
        for library_name in library_names:
            try:
                docs = await self.get_library_docs(library_name, topic)
                results.append(docs)
            except Exception as e:
                print(f"Error getting documentation for {library_name}: {e}")
                
                # Add a result with error
                results.append({
                    "libraryName": library_name,
                    "error": str(e),
                    "documentation": f"Could not get documentation for {library_name}. Error: {e}"
                })
        
        return results
