/**
 * Configuración principal del sistema de agentes MCP para Criptokens
 * 
 * Este archivo define:
 * - Los servidores MCP disponibles y sus URLs
 * - Los agentes disponibles y sus permisos de acceso a los servidores MCP
 * - Configuraciones generales del sistema
 */

module.exports = {
  // Configuración general del sistema
  system: {
    debug: process.env.AGENTS_MCP_DEBUG === 'true' || false,
    logLevel: process.env.AGENTS_MCP_LOG_LEVEL || 'info',
    timeout: parseInt(process.env.AGENTS_MCP_TIMEOUT || '30000', 10), // Timeout en ms
    maxRetries: parseInt(process.env.AGENTS_MCP_MAX_RETRIES || '3', 10),
    defaultModel: process.env.AGENTS_MCP_DEFAULT_MODEL || 'anthropic/claude-3-opus-20240229',
  },

  // Configuración de los servidores MCP
  mcpServers: {
    crypto: {
      url: process.env.CRYPTO_MCP_URL || 'http://localhost:3101',
      timeout: 10000,
      capabilities: ['getPrices', 'getHistoricalData', 'getMarketData', 'getTrendingCoins']
    },
    brave: {
      url: process.env.BRAVE_MCP_URL || 'http://localhost:3102',
      timeout: 15000,
      capabilities: ['search', 'getNews']
    },
    playwright: {
      url: process.env.PLAYWRIGHT_MCP_URL || 'http://localhost:3103',
      timeout: 20000,
      capabilities: ['browseWebPage', 'takeScreenshot', 'extractContent']
    },
    context7: {
      url: process.env.CONTEXT7_MCP_URL || 'http://localhost:7777',
      timeout: 10000,
      capabilities: ['getLibraryDocs', 'resolveLibraryId']
    },
    etherscan: {
      url: process.env.ETHERSCAN_MCP_URL || 'http://localhost:3104',
      timeout: 15000,
      capabilities: ['getAddressInfo', 'getContractInfo', 'getGasPrice', 'getTransactions']
    },
    filesystem: {
      url: process.env.FILESYSTEM_MCP_URL || 'internal',
      timeout: 5000,
      capabilities: ['readFile', 'writeFile', 'listFiles', 'deleteFile']
    },
    memory: {
      url: process.env.MEMORY_MCP_URL || 'internal',
      timeout: 5000,
      capabilities: ['storeMemory', 'retrieveMemory', 'listMemories', 'deleteMemory']
    }
  },

  // Configuración de los agentes
  agents: {
    priceAgent: {
      name: 'PriceAgent',
      description: 'Agente especializado en obtener y analizar precios de criptomonedas',
      model: 'anthropic/claude-3-haiku-20240307',
      allowedMcpServers: ['crypto', 'etherscan'],
      systemPrompt: 'Eres un agente especializado en análisis de precios de criptomonedas. Tu objetivo es proporcionar información precisa y actualizada sobre precios, tendencias y análisis de mercado.'
    },
    newsAgent: {
      name: 'NewsAgent',
      description: 'Agente especializado en buscar y resumir noticias sobre criptomonedas',
      model: 'anthropic/claude-3-haiku-20240307',
      allowedMcpServers: ['brave', 'playwright'],
      systemPrompt: 'Eres un agente especializado en búsqueda y análisis de noticias sobre criptomonedas. Tu objetivo es encontrar información relevante y resumirla de manera concisa y objetiva.'
    },
    analysisAgent: {
      name: 'AnalysisAgent',
      description: 'Agente especializado en análisis técnico y fundamental de criptomonedas',
      model: 'anthropic/claude-3-sonnet-20240229',
      allowedMcpServers: ['crypto', 'etherscan', 'brave'],
      systemPrompt: 'Eres un agente especializado en análisis técnico y fundamental de criptomonedas. Tu objetivo es proporcionar análisis detallados y recomendaciones basadas en datos.'
    },
    portfolioAgent: {
      name: 'PortfolioAgent',
      description: 'Agente especializado en gestión y análisis de portfolios de criptomonedas',
      model: 'anthropic/claude-3-sonnet-20240229',
      allowedMcpServers: ['crypto', 'etherscan', 'filesystem'],
      systemPrompt: 'Eres un agente especializado en gestión y análisis de portfolios de criptomonedas. Tu objetivo es ayudar a los usuarios a optimizar sus inversiones y entender su rendimiento.'
    },
    alertAgent: {
      name: 'AlertAgent',
      description: 'Agente especializado en configurar y gestionar alertas de precios y eventos',
      model: 'anthropic/claude-3-haiku-20240307',
      allowedMcpServers: ['crypto', 'filesystem', 'memory'],
      systemPrompt: 'Eres un agente especializado en configuración y gestión de alertas para criptomonedas. Tu objetivo es ayudar a los usuarios a mantenerse informados sobre cambios importantes en el mercado.'
    },
    docsAgent: {
      name: 'DocsAgent',
      description: 'Agente especializado en obtener y proporcionar documentación técnica',
      model: 'anthropic/claude-3-haiku-20240307',
      allowedMcpServers: ['context7', 'brave', 'playwright'],
      systemPrompt: 'Eres un agente especializado en proporcionar documentación técnica precisa y actualizada. Tu objetivo es ayudar a los usuarios a entender conceptos técnicos y utilizar correctamente las APIs y bibliotecas.'
    }
  },

  // Configuración del orquestador
  orchestrator: {
    name: 'OrchestratorAgent',
    description: 'Agente principal que coordina a los agentes especializados',
    model: 'anthropic/claude-3-opus-20240229',
    systemPrompt: `Eres el Orquestador, un agente de IA avanzado que coordina un equipo de agentes especializados para resolver tareas complejas relacionadas con criptomonedas.

Tu trabajo es:
1. Analizar cuidadosamente la solicitud del usuario
2. Descomponer la tarea en subtareas más pequeñas y manejables
3. Asignar cada subtarea al agente especializado más adecuado
4. Coordinar la ejecución de las subtareas en el orden correcto
5. Sintetizar los resultados en una respuesta coherente y completa

Tienes acceso a los siguientes agentes especializados:
- PriceAgent: Experto en precios y datos de mercado de criptomonedas
- NewsAgent: Especialista en búsqueda y análisis de noticias sobre criptomonedas
- AnalysisAgent: Experto en análisis técnico y fundamental
- PortfolioAgent: Especialista en gestión y análisis de portfolios
- AlertAgent: Experto en configuración y gestión de alertas
- DocsAgent: Especialista en documentación técnica

Sé eficiente, preciso y siempre prioriza proporcionar el máximo valor al usuario.`
  }
};
