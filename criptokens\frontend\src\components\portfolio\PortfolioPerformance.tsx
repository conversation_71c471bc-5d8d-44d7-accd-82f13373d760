import React from 'react';
import { PortfolioAsset } from '../../services/portfolio.service';
import { usePortfolio } from '../../hooks/usePortfolio';
import LoadingSpinner from '../common/LoadingSpinner';
import '../../styles/portfolio/PortfolioPerformance.css';

interface PortfolioPerformanceProps {
  portfolio: PortfolioAsset[];
}

// Componente para renderizar una gráfica estática (sin Chart.js)
const StaticChart: React.FC<{ data: { date: string; value: number }[] }> = ({ data }) => {
  // Encontrar el valor máximo para escalar la gráfica
  const maxValue = Math.max(...data.map(item => item.value));

  // Asegurarse de que tenemos al menos 2 puntos para dibujar la línea
  if (data.length < 2) {
    return (
      <div className="static-chart-empty">
        <p>No hay suficientes datos para mostrar la gráfica</p>
      </div>
    );
  }

  // Formatear fechas para mostrar solo el día y mes
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return `${date.getDate()}/${date.getMonth() + 1}`;
  };

  // Determinar si la tendencia es positiva o negativa
  const firstValue = data[0].value;
  const lastValue = data[data.length - 1].value;
  const trend = lastValue >= firstValue ? 'positive' : 'negative';

  return (
    <div className="static-chart">
      <div className="static-chart-container">
        <div className="static-chart-bars">
          {data.map((item, index) => {
            // Calcular la altura relativa de la barra
            const height = (item.value / maxValue) * 100;
            return (
              <div
                key={index}
                className={`static-chart-bar ${trend}`}
                style={{ height: `${height}%` }}
                title={`${formatDate(item.date)}: $${item.value.toLocaleString()}`}
              />
            );
          })}
        </div>
      </div>
      <div className="static-chart-labels">
        <div className="static-chart-date-range">
          <span>{formatDate(data[0].date)}</span>
          <span>{formatDate(data[data.length - 1].date)}</span>
        </div>
        <div className="static-chart-value-range">
          <span>${data[0].value.toLocaleString()}</span>
          <span>${data[data.length - 1].value.toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
};

const PortfolioPerformance: React.FC<PortfolioPerformanceProps> = ({ portfolio }) => {
  const { historicalData, isLoadingHistorical, error } = usePortfolio();

  if (portfolio.length === 0) {
    return (
      <div className="portfolio-performance empty-chart">
        <h3>Rendimiento del Portafolio</h3>
        <div className="empty-chart-message">
          <p>Añade activos a tu portafolio para ver el rendimiento</p>
        </div>
      </div>
    );
  }

  if (isLoadingHistorical) {
    return (
      <div className="portfolio-performance">
        <h3>Rendimiento del Portafolio</h3>
        <div className="chart-loading">
          <LoadingSpinner message="Cargando datos históricos..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-performance">
        <h3>Rendimiento del Portafolio</h3>
        <div className="chart-error">
          <p>Error al cargar los datos históricos</p>
          <p className="error-details">{error}</p>
        </div>
      </div>
    );
  }

  if (!historicalData || historicalData.length === 0) {
    return (
      <div className="portfolio-performance empty-chart">
        <h3>Rendimiento del Portafolio</h3>
        <div className="empty-chart-message">
          <p>No hay datos históricos disponibles para mostrar el rendimiento</p>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-performance">
      <h3>Rendimiento del Portafolio</h3>
      <div className="chart-container">
        <StaticChart data={historicalData} />
      </div>
    </div>
  );
};

export default PortfolioPerformance;
