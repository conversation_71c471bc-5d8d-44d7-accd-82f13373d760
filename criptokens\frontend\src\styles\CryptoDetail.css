.crypto-detail {
  padding: 1rem 0;
}

.crypto-detail-header {
  margin-bottom: 1.5rem;
}

.back-button {
  background: none;
  border: none;
  color: #0084ff;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 0;
  display: inline-flex;
  align-items: center;
  margin-bottom: 1rem;
}

.back-button:hover {
  text-decoration: underline;
}

.crypto-basic-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.crypto-logo {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.crypto-basic-info h2 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  color: #333;
}

.crypto-price-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.crypto-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.crypto-price-change {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.crypto-price-change.positive {
  color: #00C853;
  background-color: rgba(0, 200, 83, 0.1);
}

.crypto-price-change.negative {
  color: #FF3D00;
  background-color: rgba(255, 61, 0, 0.1);
}

.crypto-stats-container {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.crypto-stats-container h3 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.crypto-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.25rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.stat-value.positive {
  color: #00C853;
}

.stat-value.negative {
  color: #FF3D00;
}

.crypto-description {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.crypto-description h3 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.description-content {
  color: #444;
  line-height: 1.6;
  font-size: 0.9375rem;
}

.description-content a {
  color: #0084ff;
  text-decoration: none;
}

.description-content a:hover {
  text-decoration: underline;
}

.crypto-detail-loading,
.crypto-detail-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  color: #666;
  text-align: center;
}

.crypto-detail-error {
  color: #d32f2f;
}

@media (max-width: 768px) {
  .crypto-stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .stat-label {
    margin-bottom: 0;
  }
}
