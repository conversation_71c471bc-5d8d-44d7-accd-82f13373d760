import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/NewAuthContext';
// Importar el servicio compatible en lugar del original
import * as radarService from '../services/radarCripto.service.compat';
import { CryptoCategory, RadarItem } from '../services/radarCripto.service.compat';
import { getTopCryptocurrencies } from '../services/crypto.service';

interface CryptoWithPrice extends RadarItem {
  currentPrice: number;
  priceChangePercentage24h: number;
  marketCap: number;
  totalVolume: number;
  image: string;
}

export const useRadarCripto = () => {
  const { currentUser } = useAuth();
  const [radar, setRadar] = useState<RadarItem[]>([]);
  const [availableCryptos, setAvailableCryptos] = useState<any[]>([]);
  const [currentPrices, setCurrentPrices] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Cargar la lista de criptomonedas disponibles
  const loadAvailableCryptos = useCallback(async () => {
    try {
      const cryptos = await getTopCryptocurrencies(50);
      setAvailableCryptos(cryptos);
    } catch (err) {
      console.error('Error al cargar la lista de criptomonedas:', err);
      setError('Error al cargar la lista de criptomonedas');
    }
  }, []);

  // Cargar los precios actuales
  const loadPrices = useCallback(async () => {
    if (!radar.length) return;

    try {
      // Obtener los precios de las criptomonedas disponibles
      const cryptos = await getTopCryptocurrencies(100);

      const priceMap: Record<string, number> = {};
      cryptos.forEach((crypto: any) => {
        priceMap[crypto.id] = crypto.current_price;
      });

      setCurrentPrices(priceMap);
    } catch (err) {
      console.error('Error al cargar los precios:', err);
      setError('Error al cargar los precios actuales');
    }
  }, [radar]);

  // Cargar el radar del usuario
  const loadRadar = useCallback(async () => {
    if (!currentUser) {
      setRadar([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const items = await radarService.getRadarItems(currentUser.uid);
      setRadar(items);
    } catch (err) {
      console.error('Error al cargar el radar:', err);
      setError('Error al cargar tu radar de criptomonedas');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  // Efecto para cargar el radar cuando cambia el usuario
  useEffect(() => {
    loadRadar();
  }, [loadRadar]);

  // Efecto para cargar las criptomonedas disponibles
  useEffect(() => {
    loadAvailableCryptos();
  }, [loadAvailableCryptos]);

  // Efecto para cargar los precios cuando cambia el radar
  useEffect(() => {
    if (radar.length > 0) {
      loadPrices();
    }
  }, [radar, loadPrices]);

  // Añadir una criptomoneda al radar
  const addToRadar = useCallback(async (crypto: { id: string; name: string; symbol: string; imageUrl: string }) => {
    if (!currentUser) return;

    try {
      await radarService.addToRadar(currentUser, crypto);
      await loadRadar();
    } catch (err: any) {
      console.error('Error al añadir al radar:', err);
      setError(err.message || 'Error al añadir la criptomoneda al radar');
    }
  }, [currentUser, loadRadar]);

  // Eliminar una criptomoneda del radar
  const removeFromRadar = useCallback(async (cryptoId: string) => {
    if (!currentUser) return;

    try {
      await radarService.removeFromRadar(currentUser.uid, cryptoId);
      await loadRadar();
    } catch (err) {
      console.error('Error al eliminar del radar:', err);
      setError('Error al eliminar la criptomoneda del radar');
    }
  }, [currentUser, loadRadar]);

  // Actualizar las notas de una criptomoneda
  const updateNotes = useCallback(async (cryptoId: string, notes: string) => {
    if (!currentUser) return;

    try {
      await radarService.updateNotes(currentUser.uid, cryptoId, notes);

      // Actualizar el estado local
      setRadar(prev => prev.map(item =>
        item.id === cryptoId ? { ...item, notes } : item
      ));
    } catch (err) {
      console.error('Error al actualizar las notas:', err);
      setError('Error al guardar las notas');
    }
  }, [currentUser]);

  // Actualizar la categoría de una criptomoneda
  const updateCategory = useCallback(async (cryptoId: string, category: CryptoCategory) => {
    if (!currentUser) return;

    try {
      await radarService.updateCategory(currentUser.uid, cryptoId, category);

      // Actualizar el estado local
      setRadar(prev => prev.map(item =>
        item.id === cryptoId ? { ...item, category } : item
      ));
    } catch (err) {
      console.error('Error al actualizar la categoría:', err);
      setError('Error al cambiar la categoría');
    }
  }, [currentUser]);

  // Marcar/desmarcar como favorito
  const toggleItemFavorite = useCallback(async (cryptoId: string, isFavorite: boolean) => {
    if (!currentUser) return;

    try {
      await radarService.toggleFavorite(currentUser.uid, cryptoId, isFavorite);

      // Actualizar el estado local
      setRadar(prev => prev.map(item =>
        item.id === cryptoId ? { ...item, isFavorite } : item
      ));
    } catch (err) {
      console.error('Error al actualizar favorito:', err);
      setError('Error al actualizar favorito');
    }
  }, [currentUser]);

  // Establecer precio de alerta personalizado
  const setAlertPrice = useCallback(async (cryptoId: string, price: number | null) => {
    if (!currentUser) return;

    try {
      await radarService.setAlertPrice(currentUser.uid, cryptoId, price);

      // Actualizar el estado local
      setRadar(prev => prev.map(item =>
        item.id === cryptoId ? { ...item, customAlertPrice: price } : item
      ));
    } catch (err) {
      console.error('Error al establecer precio de alerta:', err);
      setError('Error al configurar la alerta de precio');
    }
  }, [currentUser]);

  // Verificar si una criptomoneda está en el radar
  const isInRadar = useCallback(async (cryptoId: string): Promise<boolean> => {
    if (!currentUser) return false;

    try {
      return await radarService.isInRadar(currentUser.uid, cryptoId);
    } catch (err) {
      console.error('Error al verificar si está en el radar:', err);
      return false;
    }
  }, [currentUser]);

  // Obtener datos completos de las criptomonedas en el radar
  const getRadarWithPrices = useCallback(() => {
    if (!Array.isArray(radar)) return [];

    return radar.map(item => {
      const cryptoDetails = availableCryptos.find(crypto => crypto.id === item.id) || {};
      return {
        ...item,
        currentPrice: currentPrices[item.id] || 0,
        priceChangePercentage24h: cryptoDetails.price_change_percentage_24h || 0,
        marketCap: cryptoDetails.market_cap || 0,
        totalVolume: cryptoDetails.total_volume || 0,
        image: cryptoDetails.image || item.imageUrl
      };
    });
  }, [radar, availableCryptos, currentPrices]);

  // Actualizar los precios
  const refreshPrices = useCallback(async () => {
    setIsLoading(true);
    try {
      await loadPrices();
    } catch (err) {
      console.error('Error al actualizar precios:', err);
      setError('Error al actualizar los precios');
    } finally {
      setIsLoading(false);
    }
  }, [loadPrices]);

  // Obtener solo los elementos favoritos
  const getFavorites = useCallback(() => {
    const items = getRadarWithPrices();
    return Array.isArray(items) ? items.filter(item => item.isFavorite) : [];
  }, [getRadarWithPrices]);

  // Obtener elementos por categoría
  const getByCategory = useCallback((category: CryptoCategory) => {
    const items = getRadarWithPrices();
    return Array.isArray(items) ? items.filter(item => item.category === category) : [];
  }, [getRadarWithPrices]);

  return {
    radar,
    radarWithPrices: getRadarWithPrices(),
    favorites: getFavorites(),
    isLoading,
    error,
    addToRadar,
    removeFromRadar,
    updateNotes,
    updateCategory,
    toggleItemFavorite,
    setAlertPrice,
    isInRadar,
    refreshPrices,
    getByCategory,
    loadRadar
  };
};
