.mcp-interface {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border: 1px solid rgba(64, 220, 255, 0.2);
  box-shadow: var(--shadow-md);
  color: var(--text-medium);
  margin-bottom: var(--space-xl);
  backdrop-filter: blur(10px);
}

.mcp-header {
  margin-bottom: var(--space-lg);
  text-align: center;
}

.mcp-header h2 {
  font-size: 1.75rem;
  margin-bottom: var(--space-xs);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mcp-header p {
  color: var(--text-dim);
  margin: 0;
}

.mcp-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
}

.mcp-tools, .mcp-response {
  background: rgba(15, 15, 35, 0.5);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  border: 1px solid rgba(64, 220, 255, 0.1);
}

.mcp-tools h3, .mcp-response h3 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: var(--space-md);
  color: var(--text-bright);
}

.tools-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
}

.tool-button {
  background: rgba(20, 20, 50, 0.7);
  border: 1px solid rgba(64, 220, 255, 0.2);
  color: var(--text-dim);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 0.875rem;
}

.tool-button:hover {
  background: rgba(64, 220, 255, 0.1);
  color: var(--text-bright);
}

.tool-button.active {
  background: rgba(64, 220, 255, 0.2);
  border-color: rgba(64, 220, 255, 0.4);
  color: var(--text-bright);
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.2);
}

.mcp-form {
  background: rgba(10, 10, 26, 0.5);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  border: 1px solid rgba(64, 220, 255, 0.1);
}

.form-header {
  margin-bottom: var(--space-md);
}

.form-header h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-bright);
}

.form-body {
  margin-bottom: var(--space-md);
}

.form-group {
  margin-bottom: var(--space-sm);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-xs);
  color: var(--text-dim);
  font-size: 0.875rem;
}

.form-group input {
  width: 100%;
  padding: var(--space-xs) var(--space-sm);
  background: rgba(30, 30, 60, 0.6);
  border: 1px solid rgba(64, 220, 255, 0.2);
  border-radius: var(--radius-sm);
  color: var(--text-bright);
  font-size: 0.875rem;
  transition: all var(--transition-normal);
}

.form-group input:focus {
  outline: none;
  border-color: rgba(64, 220, 255, 0.6);
  box-shadow: 0 0 0 2px rgba(0, 242, 255, 0.1);
}

.form-info {
  color: var(--text-dim);
  font-size: 0.875rem;
  margin: var(--space-md) 0;
  padding: var(--space-sm);
  background: rgba(64, 220, 255, 0.05);
  border-radius: var(--radius-sm);
  border-left: 3px solid rgba(64, 220, 255, 0.3);
}

.form-footer {
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  background: var(--gradient-button);
  color: var(--text-bright);
  border: none;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 242, 255, 0.3);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.mcp-response {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.response-content {
  flex: 1;
  background: rgba(10, 10, 26, 0.7);
  border-radius: var(--radius-sm);
  padding: var(--space-md);
  margin: 0;
  overflow: auto;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-medium);
  white-space: pre-wrap;
  border: 1px solid rgba(64, 220, 255, 0.1);
}

.empty-response {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-dim);
  font-style: italic;
  background: rgba(10, 10, 26, 0.3);
  border-radius: var(--radius-sm);
  border: 1px dashed rgba(64, 220, 255, 0.1);
}

.error-message {
  background: rgba(255, 82, 82, 0.1);
  border-left: 3px solid var(--error);
  padding: var(--space-sm);
  margin-bottom: var(--space-md);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.error-message p {
  margin: 0;
  color: var(--error);
  font-size: 0.875rem;
}

.loading-indicator {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(64, 220, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 992px) {
  .mcp-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .tools-list {
    flex-direction: column;
  }
  
  .tool-button {
    width: 100%;
    text-align: left;
  }
}
