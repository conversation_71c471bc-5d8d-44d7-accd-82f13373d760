/**
 * Adaptador para el servidor MCP de Playwright
 * 
 * Este adaptador se comunica con el servidor MCP de Playwright para
 * navegar y analizar páginas web.
 */

const BaseMcpAdapter = require('./base-mcp-adapter');
const logger = require('../utils/logger');

class PlaywrightMcpAdapter extends BaseMcpAdapter {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(options = {}) {
    super('playwright', options);
    this.browserSession = null;
  }
  
  /**
   * Navega a una URL y analiza su contenido
   * @param {string} url - URL a visitar
   * @param {Object} [options={}] - Opciones adicionales
   * @param {boolean} [options.takeScreenshot=true] - Si se debe tomar una captura de pantalla
   * @param {boolean} [options.fullPage=false] - Si la captura debe ser de la página completa
   * @returns {Promise<Object>} Información de la página
   */
  async browseWebPage(url, options = {}) {
    const params = {
      url: url,
      takeScreenshot: options.takeScreenshot !== false,
      fullPage: options.fullPage || false
    };
    
    logger.debug('PlaywrightMcpAdapter', `Navegando a ${url}`, params);
    
    const result = await this.executeTool('browseWebPage', params);
    
    // Guardar el ID de sesión del navegador si está disponible
    if (result && result.sessionId) {
      this.browserSession = result.sessionId;
    }
    
    return result;
  }
  
  /**
   * Toma una captura de pantalla de la página actual
   * @param {Object} [options={}] - Opciones adicionales
   * @param {boolean} [options.fullPage=false] - Si la captura debe ser de la página completa
   * @returns {Promise<Object>} Información de la captura
   */
  async takeScreenshot(options = {}) {
    if (!this.browserSession) {
      throw new Error('No hay una sesión de navegador activa. Primero debes llamar a browseWebPage()');
    }
    
    const params = {
      sessionId: this.browserSession,
      fullPage: options.fullPage || false
    };
    
    logger.debug('PlaywrightMcpAdapter', 'Tomando captura de pantalla', params);
    
    return await this.executeTool('takeScreenshot', params);
  }
  
  /**
   * Extrae el contenido de la página actual
   * @returns {Promise<Object>} Contenido de la página
   */
  async extractContent() {
    if (!this.browserSession) {
      throw new Error('No hay una sesión de navegador activa. Primero debes llamar a browseWebPage()');
    }
    
    const params = {
      sessionId: this.browserSession
    };
    
    logger.debug('PlaywrightMcpAdapter', 'Extrayendo contenido de la página', params);
    
    return await this.executeTool('extractContent', params);
  }
  
  /**
   * Hace clic en un elemento de la página
   * @param {string} selector - Selector CSS del elemento
   * @returns {Promise<Object>} Resultado de la operación
   */
  async clickElement(selector) {
    if (!this.browserSession) {
      throw new Error('No hay una sesión de navegador activa. Primero debes llamar a browseWebPage()');
    }
    
    const params = {
      sessionId: this.browserSession,
      selector: selector
    };
    
    logger.debug('PlaywrightMcpAdapter', `Haciendo clic en elemento con selector "${selector}"`, params);
    
    return await this.executeTool('clickElement', params);
  }
  
  /**
   * Escribe texto en un elemento de la página
   * @param {string} selector - Selector CSS del elemento
   * @param {string} text - Texto a escribir
   * @returns {Promise<Object>} Resultado de la operación
   */
  async typeText(selector, text) {
    if (!this.browserSession) {
      throw new Error('No hay una sesión de navegador activa. Primero debes llamar a browseWebPage()');
    }
    
    const params = {
      sessionId: this.browserSession,
      selector: selector,
      text: text
    };
    
    logger.debug('PlaywrightMcpAdapter', `Escribiendo texto en elemento con selector "${selector}"`, params);
    
    return await this.executeTool('typeText', params);
  }
  
  /**
   * Navega hacia atrás en el historial del navegador
   * @returns {Promise<Object>} Información de la página
   */
  async goBack() {
    if (!this.browserSession) {
      throw new Error('No hay una sesión de navegador activa. Primero debes llamar a browseWebPage()');
    }
    
    const params = {
      sessionId: this.browserSession
    };
    
    logger.debug('PlaywrightMcpAdapter', 'Navegando hacia atrás en el historial', params);
    
    return await this.executeTool('goBack', params);
  }
  
  /**
   * Navega hacia adelante en el historial del navegador
   * @returns {Promise<Object>} Información de la página
   */
  async goForward() {
    if (!this.browserSession) {
      throw new Error('No hay una sesión de navegador activa. Primero debes llamar a browseWebPage()');
    }
    
    const params = {
      sessionId: this.browserSession
    };
    
    logger.debug('PlaywrightMcpAdapter', 'Navegando hacia adelante en el historial', params);
    
    return await this.executeTool('goForward', params);
  }
  
  /**
   * Cierra la sesión del navegador
   * @returns {Promise<void>}
   */
  async closeBrowser() {
    if (!this.browserSession) {
      return;
    }
    
    const params = {
      sessionId: this.browserSession
    };
    
    logger.debug('PlaywrightMcpAdapter', 'Cerrando sesión del navegador', params);
    
    try {
      await this.executeTool('closeBrowser', params);
    } catch (error) {
      logger.warn('PlaywrightMcpAdapter', 'Error al cerrar sesión del navegador', {
        error: error.message
      });
    } finally {
      this.browserSession = null;
    }
  }
  
  /**
   * Cierra la sesión MCP y la sesión del navegador
   * @returns {Promise<void>}
   */
  async closeSession() {
    await this.closeBrowser();
    await super.closeSession();
  }
}

module.exports = PlaywrightMcpAdapter;
