/**
 * Cliente para comunicarse con el servidor Playwright MCP
 */
const axios = require('axios');
require('dotenv').config();
// Importar la configuración directamente
const config = {
  urls: {
    playwrightMcp: process.env.PLAYWRIGHT_MCP_URL || 'http://localhost:3103'
  }
};

// URL base del servidor Playwright MCP
const PLAYWRIGHT_MCP_URL = process.env.PLAYWRIGHT_MCP_URL || config.urls.playwrightMcp;

// Mapa de sesiones activas para reutilización
const activeSessions = new Map();

/**
 * Crea una nueva sesión en el servidor Playwright MCP
 * @returns {Promise<string>} - ID de la sesión creada
 */
async function createSession() {
  try {
    console.log('Creando nueva sesión en el servidor Playwright MCP');

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/session`, {}, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.data || !response.data.sessionId) {
      throw new Error('No se pudo crear una sesión: respuesta inválida');
    }

    const sessionId = response.data.sessionId;
    console.log(`Sesión creada con ID: ${sessionId}`);

    // Registrar la sesión como activa
    activeSessions.set(sessionId, {
      createdAt: Date.now(),
      lastUrl: null,
      historyIndex: -1,
      historyLength: 0
    });

    return sessionId;
  } catch (error) {
    console.error('Error al crear sesión:', error.message);
    throw new Error(`No se pudo crear una sesión: ${error.message}`);
  }
}

/**
 * Navega a una URL y obtiene un snapshot de la página
 * @param {string} url - URL de la página a visualizar
 * @param {string} sessionId - ID de sesión (opcional)
 * @returns {Promise<Object>} - Datos de la página visualizada
 */
async function browseWebPage(url, sessionId = null) {
  try {
    console.log(`Navegando a URL: ${url}`);

    // Si no se proporciona un ID de sesión, intentar usar uno existente o crear uno nuevo
    if (!sessionId) {
      // Usar la primera sesión activa o crear una nueva
      if (activeSessions.size > 0) {
        sessionId = Array.from(activeSessions.keys())[0];
      } else {
        sessionId = await createSession();
      }
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/browse`, {
      url,
      sessionId
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: response.data.url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al navegar a la URL:', error);

    // Devolver datos simulados para pruebas
    return {
      url,
      title: 'Error al cargar la página',
      content: {
        plainText: `No se pudo cargar la página ${url}. ${error.message}`
      },
      summary: 'No se pudo cargar la página solicitada.',
      sessionId
    };
  }
}

/**
 * Toma una captura de pantalla de la página actual
 * @param {string} sessionId - ID de sesión
 * @param {boolean} fullPage - Si es true, captura la página completa
 * @returns {Promise<string>} - Imagen en formato base64
 */
async function takeScreenshot(sessionId, fullPage = false) {
  try {
    console.log(`Tomando captura de pantalla (sessionId: ${sessionId}, fullPage: ${fullPage})`);

    // Si no se proporciona un ID de sesión, intentar usar uno existente
    if (!sessionId && activeSessions.size > 0) {
      sessionId = Array.from(activeSessions.keys())[0];
    }

    if (!sessionId) {
      throw new Error('Se requiere un ID de sesión para tomar una captura de pantalla');
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/screenshot`, {
      sessionId,
      fullPage
    });

    return response.data.screenshot;
  } catch (error) {
    console.error('Error al tomar captura de pantalla:', error);
    return null;
  }
}

/**
 * Navega hacia atrás en el historial del navegador
 * @param {string} sessionId - ID de sesión
 * @returns {Promise<Object>} - Datos de la página visualizada
 */
async function navigateBack(sessionId) {
  try {
    console.log(`Navegando hacia atrás (sessionId: ${sessionId})`);

    if (!sessionId) {
      throw new Error('Se requiere un ID de sesión para navegar hacia atrás');
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/back`, {
      sessionId
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: response.data.url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al navegar hacia atrás:', error);
    return {
      error: `No se pudo navegar hacia atrás: ${error.message}`,
      sessionId
    };
  }
}

/**
 * Navega hacia adelante en el historial del navegador
 * @param {string} sessionId - ID de sesión
 * @returns {Promise<Object>} - Datos de la página visualizada
 */
async function navigateForward(sessionId) {
  try {
    console.log(`Navegando hacia adelante (sessionId: ${sessionId})`);

    if (!sessionId) {
      throw new Error('Se requiere un ID de sesión para navegar hacia adelante');
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/forward`, {
      sessionId
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: response.data.url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al navegar hacia adelante:', error);
    return {
      error: `No se pudo navegar hacia adelante: ${error.message}`,
      sessionId
    };
  }
}

/**
 * Hace clic en un elemento de la página
 * @param {string} sessionId - ID de sesión
 * @param {string} selector - Selector CSS del elemento
 * @param {string} text - Texto del elemento (alternativa al selector)
 * @returns {Promise<Object>} - Datos de la página visualizada
 */
async function clickElement(sessionId, selector, text) {
  try {
    console.log(`Haciendo clic en elemento (sessionId: ${sessionId}, selector: ${selector}, text: ${text})`);

    if (!sessionId) {
      throw new Error('Se requiere un ID de sesión para hacer clic en un elemento');
    }

    if (!selector && !text) {
      throw new Error('Se requiere un selector o texto para hacer clic en un elemento');
    }

    const response = await axios.post(`${PLAYWRIGHT_MCP_URL}/click`, {
      sessionId,
      selector,
      text
    });

    // Actualizar la sesión activa
    if (sessionId && response.data.sessionId) {
      activeSessions.set(sessionId, {
        createdAt: Date.now(),
        lastUrl: response.data.url,
        historyIndex: response.data.historyIndex,
        historyLength: response.data.historyLength
      });
    }

    return response.data;
  } catch (error) {
    console.error('Error al hacer clic en elemento:', error);
    return {
      error: `No se pudo hacer clic en el elemento: ${error.message}`,
      sessionId
    };
  }
}

/**
 * Visualiza una página web y devuelve su contenido y captura
 * @param {string} url - URL de la página web a visualizar
 * @returns {Promise<Object>} - Datos de la página web
 */
async function visualizeWebPage(url) {
  try {
    console.log(`Visualizando página web: ${url}`);

    // Crear una nueva sesión para esta visualización
    const sessionId = await createSession();

    // Navegar a la URL
    const pageData = await browseWebPage(url, sessionId);

    // Tomar una captura de pantalla
    const screenshot = await takeScreenshot(sessionId, false);

    return {
      url: pageData.url,
      title: pageData.title,
      content: pageData.content,
      summary: pageData.summary,
      screenshot,
      sessionId
    };
  } catch (error) {
    console.error('Error al visualizar página web:', error);
    return {
      url,
      error: `No se pudo visualizar la página: ${error.message}`
    };
  }
}

module.exports = {
  createSession,
  browseWebPage,
  takeScreenshot,
  navigateBack,
  navigateForward,
  clickElement,
  visualizeWebPage
};
