import React, { useState } from 'react';
import { Container, Typography, Box, TextField, MenuItem, Button, Paper, Grid } from '@mui/material';
import TechnicalAnalysis from '../components/technical/TechnicalAnalysis';

const intervals = [
  { value: '1d', label: 'Diario' },
  { value: '4h', label: '4 horas' },
  { value: '1h', label: '1 hora' },
  { value: '1w', label: 'Semanal' },
  { value: '1m', label: 'Mensual' }
];

const popularCoins = [
  { value: 'BTC', label: 'Bitcoin (BTC)' },
  { value: 'ETH', label: 'Ethereum (ETH)' },
  { value: 'SOL', label: 'Solana (SOL)' },
  { value: 'BNB', label: 'Binance Coin (BNB)' },
  { value: 'XRP', label: 'XRP (XRP)' },
  { value: 'ADA', label: 'Cardano (ADA)' },
  { value: 'DOGE', label: '<PERSON><PERSON><PERSON><PERSON> (DOGE)' },
  { value: 'DOT', label: '<PERSON><PERSON><PERSON> (DOT)' },
  { value: 'AVAX', label: 'Avalanche (AVAX)' },
  { value: 'MATIC', label: 'Polygon (MATIC)' }
];

const TechnicalAnalysisPage: React.FC = () => {
  const [symbol, setSymbol] = useState<string>('BTC');
  const [interval, setInterval] = useState<string>('1d');
  const [limit, setLimit] = useState<number>(30);
  const [customSymbol, setCustomSymbol] = useState<string>('');

  const handleSymbolChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSymbol(event.target.value);
  };

  const handleIntervalChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInterval(event.target.value);
  };

  const handleLimitChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLimit(Number(event.target.value));
  };

  const handleCustomSymbolChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setCustomSymbol(event.target.value);
  };

  const handleCustomSymbolSubmit = () => {
    if (customSymbol.trim()) {
      setSymbol(customSymbol.trim().toUpperCase());
      setCustomSymbol('');
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Análisis Técnico
      </Typography>
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              select
              label="Criptomoneda"
              value={symbol}
              onChange={handleSymbolChange}
              fullWidth
              margin="normal"
            >
              {popularCoins.map((coin) => (
                <MenuItem key={coin.value} value={coin.value}>
                  {coin.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={3}>
            <TextField
              select
              label="Intervalo"
              value={interval}
              onChange={handleIntervalChange}
              fullWidth
              margin="normal"
            >
              {intervals.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          
          <Grid item xs={12} sm={2}>
            <TextField
              label="Límite"
              type="number"
              value={limit}
              onChange={handleLimitChange}
              fullWidth
              margin="normal"
              inputProps={{ min: 10, max: 100 }}
            />
          </Grid>
          
          <Grid item xs={12} sm={3}>
            <Box sx={{ display: 'flex', alignItems: 'flex-end', height: '100%' }}>
              <TextField
                label="Símbolo personalizado"
                value={customSymbol}
                onChange={handleCustomSymbolChange}
                fullWidth
                margin="normal"
                placeholder="Ej: LINK"
              />
              <Button 
                variant="contained" 
                onClick={handleCustomSymbolSubmit}
                sx={{ ml: 1, mb: 1 }}
              >
                Ir
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      <TechnicalAnalysis 
        symbol={symbol} 
        interval={interval} 
        limit={limit} 
      />
      
      <Box mt={4}>
        <Typography variant="h6" gutterBottom>
          Acerca del Análisis Técnico
        </Typography>
        <Typography paragraph>
          El análisis técnico es una metodología de trading que examina y predice los movimientos de precios basándose en datos históricos de mercado, principalmente el precio y el volumen. A diferencia del análisis fundamental, que se centra en factores económicos, financieros y otros factores cualitativos, el análisis técnico se enfoca exclusivamente en los patrones de precios y las estadísticas de trading.
        </Typography>
        <Typography paragraph>
          Los indicadores técnicos son herramientas matemáticas que los traders utilizan para analizar los mercados financieros. Estos indicadores toman datos de precios históricos y los transforman en señales que pueden ayudar a predecir movimientos futuros de precios.
        </Typography>
        <Typography variant="subtitle1" gutterBottom>
          Indicadores principales:
        </Typography>
        <Typography paragraph>
          <strong>RSI (Índice de Fuerza Relativa):</strong> Mide la velocidad y el cambio de los movimientos de precios. Valores por encima de 70 indican sobrecompra (posible señal de venta), mientras que valores por debajo de 30 indican sobreventa (posible señal de compra).
        </Typography>
        <Typography paragraph>
          <strong>MACD (Convergencia/Divergencia de Medias Móviles):</strong> Muestra la relación entre dos medias móviles exponenciales. Cuando el MACD cruza por encima de su línea de señal, es una señal alcista; cuando cruza por debajo, es una señal bajista.
        </Typography>
        <Typography paragraph>
          <strong>Bandas de Bollinger:</strong> Consisten en una media móvil y dos bandas de desviación estándar. Cuando el precio toca la banda superior, puede indicar sobrecompra; cuando toca la banda inferior, puede indicar sobreventa.
        </Typography>
        <Typography variant="subtitle1" color="error" gutterBottom>
          Advertencia de riesgo:
        </Typography>
        <Typography paragraph>
          El análisis técnico no garantiza resultados precisos y no debe ser la única base para tomar decisiones de inversión. Los mercados de criptomonedas son altamente volátiles y conllevan un riesgo significativo. Siempre realice su propia investigación y considere consultar con un asesor financiero antes de invertir.
        </Typography>
      </Box>
    </Container>
  );
};

export default TechnicalAnalysisPage;
