import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/NewAuthContext';
import { usePortfolio } from '../hooks/usePortfolio';
import '../styles/TransactionHistory.css';

const TransactionHistory: React.FC = () => {
  const { currentUser } = useAuth();
  const { getTransactionHistoryData, isLoading } = usePortfolio();
  const [transactions, setTransactions] = useState<any[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<any[]>([]);
  const [filter, setFilter] = useState<'all' | 'buy' | 'sell'>('all');

  useEffect(() => {
    const loadTransactions = async () => {
      if (currentUser) {
        const transactionData = await getTransactionHistoryData();
        // Ordenar por fecha, más reciente primero
        const sortedTransactions = [...transactionData].sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        setTransactions(sortedTransactions);
        setFilteredTransactions(sortedTransactions);
      }
    };

    loadTransactions();
  }, [currentUser, getTransactionHistoryData]);

  // Aplicar filtro
  useEffect(() => {
    if (filter === 'all') {
      setFilteredTransactions(transactions);
    } else {
      setFilteredTransactions(transactions.filter(t => t.type === filter));
    }
  }, [filter, transactions]);

  // Formatear fecha
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Formatear número
  const formatNumber = (num: number, decimals = 2) => {
    return num.toLocaleString('es-ES', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    });
  };

  if (!currentUser) {
    return (
      <div className="transaction-history-container">
        <div className="not-logged-in">
          <h3>Acceso Restringido</h3>
          <p>Debes iniciar sesión para ver tu historial de transacciones.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <div className="transaction-history-loading">Cargando historial de transacciones...</div>;
  }

  return (
    <div className="transaction-history-container">
      <div className="transaction-history-header">
        <h2>Historial de Transacciones</h2>
        <div className="filter-controls">
          <button 
            className={`filter-button ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            Todas
          </button>
          <button 
            className={`filter-button ${filter === 'buy' ? 'active' : ''}`}
            onClick={() => setFilter('buy')}
          >
            Compras
          </button>
          <button 
            className={`filter-button ${filter === 'sell' ? 'active' : ''}`}
            onClick={() => setFilter('sell')}
          >
            Ventas
          </button>
        </div>
      </div>

      {filteredTransactions.length > 0 ? (
        <div className="transactions-list">
          <table className="transactions-table">
            <thead>
              <tr>
                <th>Fecha</th>
                <th>Tipo</th>
                <th>Activo</th>
                <th>Cantidad</th>
                <th>Precio</th>
                <th>Valor Total</th>
                <th>Notas</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map((transaction) => (
                <tr 
                  key={transaction.id} 
                  className={`transaction-row ${transaction.type === 'buy' ? 'buy' : 'sell'}`}
                >
                  <td>{formatDate(transaction.date)}</td>
                  <td className="transaction-type">
                    <span className={`type-badge ${transaction.type}`}>
                      {transaction.type === 'buy' ? 'Compra' : 'Venta'}
                    </span>
                  </td>
                  <td className="asset-info">
                    <span className="asset-name">{transaction.assetName}</span>
                    <span className="asset-symbol">{transaction.assetSymbol}</span>
                  </td>
                  <td>{formatNumber(transaction.amount, 8)}</td>
                  <td>${formatNumber(transaction.price)}</td>
                  <td>${formatNumber(transaction.amount * transaction.price)}</td>
                  <td className="transaction-notes">{transaction.notes || '-'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="empty-transactions">
          <p>No hay transacciones {filter !== 'all' ? `de tipo "${filter === 'buy' ? 'compra' : 'venta'}"` : ''} en tu historial.</p>
          {filter !== 'all' && (
            <button 
              className="show-all-button"
              onClick={() => setFilter('all')}
            >
              Mostrar todas las transacciones
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;
