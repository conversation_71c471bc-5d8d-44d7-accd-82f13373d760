import React, { useState, useEffect } from 'react';
import '../../styles/portfolio/AddFundsModal.css';

interface AddFundsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddFunds: (amount: number, currency: string) => void;
  currentBalance?: number;
  currentCurrency?: string;
}

const AddFundsModal: React.FC<AddFundsModalProps> = ({
  isOpen,
  onClose,
  onAddFunds,
  currentBalance = 0,
  currentCurrency = 'USD'
}) => {
  const [amount, setAmount] = useState<string>('');
  const [currency, setCurrency] = useState<string>(currentCurrency);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);

  // Actualizar el estado cuando cambian las props
  useEffect(() => {
    if (isOpen) {
      setCurrency(currentCurrency);
      setAmount('');
      setError(null);
      setIsSubmitting(false);
      setShowSuccess(false);
    }
  }, [isOpen, currentCurrency]);

  // Manejar el cambio en la cantidad
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Permitir solo números y un punto decimal
    if (/^[0-9]*\.?[0-9]*$/.test(value)) {
      setAmount(value);
      setError(null);
    }
  };

  // Manejar el envío del formulario
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      setError('Por favor, ingresa una cantidad válida');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Simular la adición de fondos localmente primero
      console.log(`Añadiendo fondos: ${numAmount} ${currency}`);

      // Mostrar mensaje de éxito inmediatamente
      setShowSuccess(true);

      // Esperar un momento para que el usuario vea el mensaje de éxito
      setTimeout(() => {
        // Añadir los fondos realmente
        onAddFunds(numAmount, currency);

        // Limpiar el formulario
        setAmount('');
        setError(null);
        setIsSubmitting(false);

        // Cerrar el modal después de un breve retraso
        setTimeout(() => {
          onClose();
          setShowSuccess(false);
        }, 500);
      }, 1000);
    } catch (err) {
      console.error('Error al añadir fondos:', err);
      setError('No se pudieron añadir los fondos. Por favor, intenta de nuevo.');
      setIsSubmitting(false);
    }
  };

  // Si el modal no está abierto, no renderizar nada
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="add-funds-modal">
        <div className="modal-header">
          <h2>Añadir Fondos</h2>
          <button className="close-button" onClick={onClose} disabled={isSubmitting}>
            &times;
          </button>
        </div>

        <div className="modal-content">
          {showSuccess ? (
            <div className="success-message">
              <div className="success-icon">✓</div>
              <h3>¡Fondos añadidos correctamente!</h3>
              <p>Tu nuevo balance: {currentBalance + parseFloat(amount || '0')} {currency}</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="current-balance">
                <span className="balance-label">Balance actual:</span>
                <span className="balance-value">{currentCurrency} {currentBalance.toLocaleString()}</span>
              </div>

              <div className="form-group">
                <label htmlFor="amount">Cantidad a añadir</label>
                <div className="amount-input-group">
                  <input
                    type="text"
                    id="amount"
                    value={amount}
                    onChange={handleAmountChange}
                    placeholder="Ingresa la cantidad"
                    className="amount-input"
                    disabled={isSubmitting}
                  />
                  <select
                    value={currency}
                    onChange={(e) => setCurrency(e.target.value)}
                    className="currency-select"
                    disabled={isSubmitting}
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="MXN">MXN</option>
                  </select>
                </div>
              </div>

              <div className="payment-methods">
                <h3>Métodos de Pago</h3>
                <div className="payment-options">
                  <div className="payment-option">
                    <input
                      type="radio"
                      id="credit-card"
                      name="payment-method"
                      defaultChecked
                      disabled={isSubmitting}
                    />
                    <label htmlFor="credit-card">
                      <i className="fas fa-credit-card"></i> Tarjeta de Crédito/Débito
                    </label>
                  </div>
                  <div className="payment-option">
                    <input
                      type="radio"
                      id="bank-transfer"
                      name="payment-method"
                      disabled={isSubmitting}
                    />
                    <label htmlFor="bank-transfer">
                      <i className="fas fa-university"></i> Transferencia Bancaria
                    </label>
                  </div>
                  <div className="payment-option">
                    <input
                      type="radio"
                      id="crypto"
                      name="payment-method"
                      disabled={isSubmitting}
                    />
                    <label htmlFor="crypto">
                      <i className="fab fa-bitcoin"></i> Depósito en Criptomonedas
                    </label>
                  </div>
                </div>
              </div>

              {error && <div className="error-message">{error}</div>}

              <div className="form-actions">
                <button
                  type="button"
                  className="cancel-button"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className={`add-button ${isSubmitting ? 'submitting' : ''}`}
                  disabled={!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0 || isSubmitting}
                >
                  {isSubmitting ? 'Procesando...' : 'Añadir Fondos'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddFundsModal;
