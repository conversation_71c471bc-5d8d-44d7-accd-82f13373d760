/* Dashboard Container */
.dashboard-container {
  display: flex;
  height: 100vh;
  background-color: #0a0a1a;
  color: #e0e0ff;
  font-family: 'Inter', 'Roboto', sans-serif;
}

/* Loading Screen */
.dashboard-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f0f2d 0%, #1a1a3a 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-logo {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 30px;
}

.logo-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, #00f2ff 0%, #4657ce 100%);
  border-radius: 50%;
  box-shadow: 0 0 30px rgba(0, 242, 255, 0.8);
}

.logo-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 2px solid rgba(64, 220, 255, 0.5);
  animation: pulse 2s infinite;
}

.ring1 {
  width: 70px;
  height: 70px;
  animation-delay: 0s;
}

.ring2 {
  width: 100px;
  height: 100px;
  animation-delay: 0.5s;
}

.loading-text {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-text span {
  font-size: 24px;
  font-weight: 700;
  margin: 0 2px;
  background: linear-gradient(90deg, #00f2ff, #4657ce);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1px;
}

/* Sidebar */
.dashboard-sidebar {
  width: 240px;
  background: linear-gradient(180deg, #141432 0%, #0c0c24 100%);
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(64, 220, 255, 0.2);
  z-index: 10;
}

.logo {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(64, 220, 255, 0.2);
  display: flex;
  align-items: center;
}

.logo-icon {
  position: relative;
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.logo-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, #00f2ff 0%, #4657ce 100%);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 242, 255, 0.8);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(90deg, #00f2ff, #4657ce);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1px;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  margin: 5px 10px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background: transparent;
  border: none;
  color: #a0a0d0;
  text-align: left;
}

.nav-item:hover {
  background: rgba(64, 220, 255, 0.1);
  color: #e0e0ff;
}

.nav-item.active {
  background: linear-gradient(90deg, rgba(64, 220, 255, 0.2), rgba(70, 87, 206, 0.2));
  color: #ffffff;
  box-shadow: 0 0 10px rgba(64, 220, 255, 0.1);
  border-left: 3px solid #00f2ff;
}

.nav-icon {
  margin-right: 12px;
  font-size: 18px;
}

.nav-text {
  font-size: 16px;
  font-weight: 500;
}

/* Main Content */
.dashboard-main {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.network-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.3;
}

.network-node {
  position: absolute;
  background: rgba(64, 220, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(64, 220, 255, 0.5);
}

.network-connection {
  position: absolute;
  height: 1px;
  background: rgba(64, 220, 255, 0.3);
  transform-origin: left center;
}

.dashboard-content {
  position: relative;
  z-index: 2;
  padding: 30px;
  height: 100%;
  overflow-y: auto;
}

/* Dashboard Overview */
.dashboard-overview {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.dashboard-main-content {
  display: flex;
  gap: 30px;
}

.main-section {
  flex: 1;
}

.side-section {
  width: 300px;
}

/* Chat Widget */
.chat-widget {
  background: rgba(20, 20, 50, 0.5);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(64, 220, 255, 0.2);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.chat-widget:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(64, 220, 255, 0.4);
}

.widget-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.widget-icon {
  position: relative;
  width: 40px;
  height: 40px;
  margin-right: 15px;
}

.agent-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #00f2ff 0%, #4657ce 100%);
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(0, 242, 255, 0.8);
}

.agent-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 2px solid rgba(64, 220, 255, 0.5);
  animation: pulse 3s infinite;
}

.ring-1 {
  width: 30px;
  height: 30px;
  animation-delay: 0s;
}

.ring-2 {
  width: 40px;
  height: 40px;
  animation-delay: 0.5s;
}

.chat-widget h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.chat-widget p {
  color: #a0a0d0;
  margin: 10px 0 20px;
  line-height: 1.5;
}

.open-chat-button {
  width: 100%;
  padding: 12px;
  background: linear-gradient(90deg, #4657ce, #00f2ff);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.open-chat-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 242, 255, 0.3);
}

/* Chat Fullscreen */
.chat-fullscreen {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(20, 20, 50, 0.5);
  border-radius: 16px;
  margin-bottom: 20px;
  border: 1px solid rgba(64, 220, 255, 0.2);
}

.agent-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  margin-right: 20px;
}

.agent-info {
  flex: 1;
}

.agent-info h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: white;
}

.agent-info p {
  margin: 5px 0 0;
  color: #a0a0d0;
  font-size: 14px;
}

/* Animations */
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
}

/* Scrollbar Styling */
.dashboard-content::-webkit-scrollbar {
  width: 6px;
}

.dashboard-content::-webkit-scrollbar-track {
  background: rgba(15, 15, 45, 0.3);
}

.dashboard-content::-webkit-scrollbar-thumb {
  background-color: rgba(64, 220, 255, 0.5);
  border-radius: 6px;
}

.dashboard-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(64, 220, 255, 0.7);
}

/* Loading Spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: rgba(15, 15, 35, 0.5);
  border-radius: 16px;
  border: 1px solid rgba(64, 220, 255, 0.2);
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(64, 220, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00f2ff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-container p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-main-content {
    flex-direction: column;
  }

  .side-section {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }

  .dashboard-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid rgba(64, 220, 255, 0.2);
  }

  .sidebar-nav {
    flex-direction: row;
    justify-content: space-around;
    padding: 10px 0;
  }

  .nav-item {
    flex-direction: column;
    padding: 10px;
    margin: 0;
  }

  .nav-icon {
    margin-right: 0;
    margin-bottom: 5px;
  }

  .dashboard-content {
    padding: 15px;
  }
}
