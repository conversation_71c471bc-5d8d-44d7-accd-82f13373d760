import os
import logging
import click
from dotenv import load_dotenv
from ...common.server import A2AServer
from ...common.types import Agent<PERSON><PERSON>, AgentCapabilities, AgentSkill, AgentProvider
from .task_manager import OnChainAgentTaskManager
from .agent import OnChainAnalysisAgent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@click.command()
@click.option("--host", default="localhost", help="Host to bind the server to")
@click.option("--port", default=3203, help="Port to bind the server to")
def main(host, port):
    """Run the on-chain analysis agent server."""
    try:
        # Create agent capabilities
        capabilities = AgentCapabilities(streaming=True)
        
        # Create agent skills
        skill = AgentSkill(
            id="onchain_analysis",
            name="On-Chain Analysis",
            description="Analyzes on-chain data for cryptocurrencies",
            tags=["crypto", "blockchain", "on-chain", "whale", "analysis"],
            examples=[
                "Show me Bitcoin whale activity",
                "What's the on-chain data for Ethereum?",
                "Analyze on-chain metrics for Solana"
            ],
            inputModes=["text"],
            outputModes=["text", "data"]
        )
        
        # Create agent provider
        provider = AgentProvider(
            organization="Criptokens",
            url="https://criptokens.com"
        )
        
        # Create agent card
        agent_card = AgentCard(
            name="On-Chain Analysis Agent",
            description="This agent analyzes on-chain data for cryptocurrencies",
            url=f"http://{host}:{port}/",
            provider=provider,
            version="1.0.0",
            capabilities=capabilities,
            defaultInputModes=["text"],
            defaultOutputModes=["text", "data"],
            skills=[skill]
        )
        
        # Create agent and task manager
        agent = OnChainAnalysisAgent()
        task_manager = OnChainAgentTaskManager(agent=agent)
        
        # Create and start server
        server = A2AServer(
            agent_card=agent_card,
            task_manager=task_manager,
            host=host,
            port=port
        )
        
        logger.info(f"Starting On-Chain Analysis Agent server on http://{host}:{port}/")
        server.start()
    except Exception as e:
        logger.error(f"An error occurred during server startup: {e}")
        exit(1)

if __name__ == "__main__":
    main()
