/**
 * Middleware de autenticación para proteger rutas
 */

// Importar las dependencias necesarias
const { auth } = require('../firebase-config');

/**
 * Middleware para verificar si el usuario está autenticado
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 * @param {Function} next - Función para continuar con el siguiente middleware
 */
const authenticateUser = async (req, res, next) => {
  try {
    // Obtener el token de autorización del encabezado
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // Si no hay token o no tiene el formato correcto, permitir el acceso como invitado
      // Esto es temporal para desarrollo, en producción debería rechazar la solicitud
      console.log('No se proporcionó token de autenticación, accediendo como invitado');
      req.user = { uid: 'guest', isGuest: true };
      return next();
    }
    
    // Extraer el token
    const token = authHeader.split('Bearer ')[1];
    
    try {
      // Verificar el token con Firebase Auth
      const decodedToken = await auth.verifyIdToken(token);
      
      // Añadir la información del usuario a la solicitud
      req.user = {
        uid: decodedToken.uid,
        email: decodedToken.email,
        isGuest: false
      };
      
      next();
    } catch (error) {
      console.error('Error verificando token:', error);
      
      // Si hay un error con el token, permitir el acceso como invitado
      // Esto es temporal para desarrollo, en producción debería rechazar la solicitud
      console.log('Token inválido, accediendo como invitado');
      req.user = { uid: 'guest', isGuest: true };
      next();
    }
  } catch (error) {
    console.error('Error en middleware de autenticación:', error);
    res.status(500).json({ success: false, message: 'Error de autenticación' });
  }
};

/**
 * Middleware para verificar si el usuario tiene permisos de administrador
 * @param {Object} req - Objeto de solicitud Express
 * @param {Object} res - Objeto de respuesta Express
 * @param {Function} next - Función para continuar con el siguiente middleware
 */
const requireAdmin = (req, res, next) => {
  // Verificar si el usuario está autenticado y tiene rol de administrador
  if (!req.user || req.user.isGuest || !req.user.isAdmin) {
    return res.status(403).json({
      success: false,
      message: 'Acceso denegado: se requieren permisos de administrador'
    });
  }
  
  next();
};

// Exportar los middlewares
module.exports = {
  authenticateUser,
  requireAdmin
};
