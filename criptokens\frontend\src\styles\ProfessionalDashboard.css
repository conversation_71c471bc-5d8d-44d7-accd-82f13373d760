/* ===== PROFESSIONAL DASHBOARD STYLES ===== */

/* Variables CSS para consistencia */
:root {
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;

  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);

  --color-primary: #00e0ff;
  --color-secondary: #7b4dff;
  --color-positive: #00ff9d;
  --color-negative: #ff3a6e;
  --color-warning: #ffcc00;

  --color-background: #0f1123;
  --color-surface-dark: rgba(15, 17, 35, 0.7);
  --color-surface-light: rgba(28, 31, 55, 0.7);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-tertiary: rgba(255, 255, 255, 0.6);

  --border-color: rgba(255, 255, 255, 0.1);
  --border-width: 1px;

  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Contenedor principal del dashboard */
.dashboard-content {
  padding: var(--spacing-md);
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 100%;
  box-sizing: border-box;
  background-color: var(--color-background);
  color: var(--text-primary);
}

/* Header del dashboard */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: var(--border-width) solid var(--border-color);
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-surface-dark);
  border-radius: 2rem;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--border-color);
  transition: var(--transition-fast);
}

.search-bar:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.search-bar input {
  background: transparent;
  border: none;
  color: var(--text-primary);
  outline: none;
  width: 200px;
  font-size: 0.875rem;
}

.search-bar input::placeholder {
  color: var(--text-tertiary);
}

.search-button {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.search-button:hover {
  color: var(--color-primary);
}

.refresh-button {
  background-color: var(--color-surface-dark);
  border: var(--border-width) solid var(--border-color);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: var(--transition-normal);
}

.refresh-button:hover {
  color: var(--color-primary);
  border-color: var(--color-primary);
  transform: rotate(180deg);
}

/* Contenedor principal del dashboard */
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  width: 100%;
}

/* Filas del dashboard */
.dashboard-row {
  display: flex;
  gap: var(--spacing-lg);
  width: 100%;
}

/* Columnas del dashboard */
.dashboard-column {
  display: flex;
  flex-direction: column;
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-lg);
  border: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition-normal);
}

.dashboard-column:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Fila superior */
.top-row {
  min-height: 200px;
}

.portfolio-column {
  flex: 6;
}

.market-column {
  flex: 4;
}

/* Fila media */
.middle-row {
  min-height: 400px;
}

.table-column {
  flex: 6;
  max-height: 500px;
  overflow: hidden;
}

.chart-column {
  flex: 4;
}

/* Fila inferior */
.bottom-row {
  min-height: 100px;
}

.guru-column {
  flex: 1;
}

/* Widgets específicos */
.market-summary-widget,
.crypto-table-widget,
.chart-widget,
.guru-compact-widget {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* Encabezados de widgets */
.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: var(--border-width) solid var(--border-color);
  background-color: var(--color-surface-light);
}

.widget-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.last-updated {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

/* Contenido de widgets */
.widget-content {
  flex: 1;
  padding: var(--spacing-md);
  overflow: auto;
}

/* Estilos para el widget de resumen del mercado */
.market-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.market-stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-sm);
  border: var(--border-width) solid var(--border-color);
}

.stat-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 224, 255, 0.1);
  border-radius: 50%;
  color: var(--color-primary);
  font-size: 1rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 500;
}

.positive {
  color: var(--color-positive);
}

.negative {
  color: var(--color-negative);
}

/* Estilos para el gráfico */
.chart-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  background-color: var(--color-surface-dark);
  overflow: hidden;
}

.crypto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.crypto-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.crypto-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.crypto-symbol {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  margin-left: var(--spacing-xs);
}

.crypto-price-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.price-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
}

.price-change {
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.text-positive {
  color: var(--color-positive);
}

.text-negative {
  color: var(--color-negative);
}

/* Controles del gráfico */
.chart-controls {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-surface-light);
  border-top: var(--border-width) solid var(--border-color);
  border-bottom: var(--border-width) solid var(--border-color);
}

.time-range-selector {
  display: flex;
  gap: var(--spacing-xs);
}

.time-range-selector button {
  background-color: transparent;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-sm);
  color: var(--text-tertiary);
  padding: 4px 8px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: var(--transition-fast);
}

.time-range-selector button:hover {
  background-color: rgba(0, 224, 255, 0.1);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.time-range-selector button.active {
  background-color: rgba(0, 224, 255, 0.2);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

/* Estadísticas de criptomonedas */
.crypto-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--color-surface-light);
  border-top: var(--border-width) solid var(--border-color);
}

.crypto-stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
}

/* Estado vacío */
.no-crypto-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-tertiary);
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  color: var(--text-tertiary);
  opacity: 0.5;
}

.no-crypto-selected h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
}

.no-crypto-selected p {
  margin: 0;
  color: var(--text-tertiary);
  max-width: 300px;
}

/* Indicador de carga */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 224, 255, 0.1);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Estilos para el widget del Gurú compacto */
.guru-compact-widget {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-lg);
  border: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow-md);
  height: 100%;
  overflow: hidden;
  gap: var(--spacing-md);
}

.guru-compact-widget .guru-avatar-container {
  flex-shrink: 0;
}

.guru-compact-widget .guru-message-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.guru-compact-widget .guru-message {
  color: var(--text-primary);
  font-size: 0.9rem;
  margin: 0;
  font-style: italic;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.guru-compact-widget .open-chat-button {
  background: linear-gradient(90deg, var(--color-secondary), var(--color-secondary-light));
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.8rem;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-fast);
  align-self: flex-start;
  white-space: nowrap;
}

.guru-compact-widget .open-chat-button:hover {
  background: linear-gradient(90deg, var(--color-secondary-light), var(--color-secondary));
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(138, 43, 226, 0.3);
}

/* Media queries para responsividad */
@media (max-width: 1200px) {
  .market-stats-grid,
  .crypto-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .dashboard-row {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .portfolio-column,
  .market-column,
  .table-column,
  .chart-column,
  .guru-column {
    width: 100%;
  }

  .middle-row {
    min-height: auto;
  }

  .table-column {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .search-bar {
    flex: 1;
  }

  .search-bar input {
    width: 100%;
  }

  .crypto-stats-grid {
    grid-template-columns: 1fr;
  }

  .chart-controls {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .time-range-selector {
    width: 100%;
    justify-content: space-between;
  }
}
