:root {
  --color-background: #0f1123;
  --color-card-bg: #171b36;
  --color-card-bg-hover: #1c2142;
  --color-text-primary: #ffffff;
  --color-text-secondary: #a0a3b8;
  --color-border: #2a2f4e;
  --color-accent: #00e0ff;
  --color-accent-dark: #00a3ff;
  --color-positive: #00ffb3;
  --color-negative: #ff3a6e;
  --color-neutral: #a0a3b8;
  --color-highlight: rgba(0, 224, 255, 0.1);
  --border-radius: 12px;
  --transition-speed: 0.3s;
}

.modern-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-family: 'Inter', 'Roboto', sans-serif;
}

/* Header Styles */
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background-color: rgba(23, 27, 54, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.search-container {
  position: relative;
  width: 400px;
  max-width: 100%;
}

.search-container input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background-color: rgba(15, 17, 35, 0.6);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  color: var(--color-text-primary);
  font-size: 0.9rem;
  transition: all var(--transition-speed);
}

.search-container input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.search-button {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-right button {
  background-color: transparent;
  border: none;
  color: var(--color-text-primary);
  margin-left: 1rem;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed);
}

.header-right button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.api-button {
  display: flex;
  align-items: center;
  background-color: rgba(0, 224, 255, 0.1) !important;
  border: 1px solid rgba(0, 224, 255, 0.3) !important;
  padding: 0.5rem 1rem !important;
}

.api-button svg {
  margin-right: 0.5rem;
}

.api-button:hover {
  background-color: rgba(0, 224, 255, 0.2) !important;
}

/* Main Content Styles */
.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-tabs {
  display: flex;
  padding: 0 2rem;
  border-bottom: 1px solid var(--color-border);
  background-color: rgba(23, 27, 54, 0.5);
}

.content-tabs button {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: all var(--transition-speed);
}

.content-tabs button:hover {
  color: var(--color-text-primary);
}

.content-tabs button.active {
  color: var(--color-accent);
}

.content-tabs button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--color-accent);
}

.content-tabs button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.content-area {
  flex: 1;
  padding: 1.5rem 2rem;
  overflow-y: auto;
}

/* Market Overview Styles */
.market-overview-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.market-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.market-stat-card {
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: all var(--transition-speed);
  border: 1px solid var(--color-border);
}

.market-stat-card:hover {
  background-color: var(--color-card-bg-hover);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.market-stat-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-change {
  font-size: 0.9rem;
  font-weight: 500;
}

.market-sentiment {
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--color-border);
}

.market-sentiment h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.sentiment-meter {
  height: 8px;
  background: linear-gradient(to right, var(--color-negative), #ffb74d, var(--color-positive));
  border-radius: 4px;
  position: relative;
  margin-bottom: 0.5rem;
}

.sentiment-indicator {
  position: absolute;
  top: -6px;
  left: var(--sentiment-value, 50%);
  width: 20px;
  height: 20px;
  background-color: #ffffff;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.sentiment-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
}

/* Crypto Table Styles */
.crypto-table-container {
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.table-header {
  padding: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.table-filters {
  display: flex;
  gap: 0.5rem;
}

.table-filters button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all var(--transition-speed);
}

.table-filters button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.table-filters button.active {
  background-color: rgba(0, 224, 255, 0.1);
  border-color: var(--color-accent);
  color: var(--color-accent);
}

.crypto-table {
  width: 100%;
  border-collapse: collapse;
}

.table-head {
  position: sticky;
  top: 0;
  background-color: var(--color-card-bg);
  z-index: 10;
}

.table-row {
  display: grid;
  grid-template-columns: 0.5fr 2fr 1fr 1fr 1fr 1.5fr 1.5fr 0.5fr;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--color-border);
  transition: all var(--transition-speed);
}

.table-head .table-row {
  font-weight: 500;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

.table-body .table-row {
  cursor: pointer;
}

.table-body .table-row:hover {
  background-color: var(--color-highlight);
}

.table-body .table-row.selected {
  background-color: var(--color-highlight);
  border-left: 3px solid var(--color-accent);
}

.table-cell {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.crypto-name-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.crypto-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.crypto-name {
  font-weight: 500;
  margin-right: 0.5rem;
}

.crypto-symbol {
  color: var(--color-text-secondary);
  font-size: 0.85rem;
}

.actions-cell {
  justify-content: center;
}

.watchlist-button {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 1.2rem;
  transition: all var(--transition-speed);
  padding: 0.4rem;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.watchlist-button:hover {
  background-color: rgba(255, 183, 77, 0.08);
  transform: scale(1.08);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 183, 77, 0.2);
}

.watchlist-button.active {
  color: #ffb74d;
  background-color: rgba(255, 183, 77, 0.1);
  border-color: rgba(255, 183, 77, 0.3);
  box-shadow: 0 1px 4px rgba(255, 183, 77, 0.25);
}

/* Crypto Detail Styles */
.crypto-detail-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.crypto-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--color-border);
}

.crypto-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.crypto-icon-large {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.crypto-title h2 {
  margin: 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.crypto-symbol {
  font-size: 1rem;
  color: var(--color-text-secondary);
  font-weight: normal;
}

.crypto-rank {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin-top: 0.25rem;
}

.crypto-price-info {
  text-align: right;
}

.current-price {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.price-change {
  font-size: 1rem;
  font-weight: 500;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: 1rem;
  border: 1px solid var(--color-border);
}

.time-range-selector, .chart-type-selector {
  display: flex;
  gap: 0.5rem;
}

.chart-controls button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all var(--transition-speed);
}

.chart-controls button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.chart-controls button.active {
  background-color: rgba(0, 224, 255, 0.1);
  border-color: var(--color-accent);
  color: var(--color-accent);
}

.chart-container {
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--color-border);
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.crypto-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: 1.25rem;
  transition: all var(--transition-speed);
  border: 1px solid var(--color-border);
}

.stat-card:hover {
  background-color: var(--color-card-bg-hover);
  transform: translateY(-2px);
}

.stat-title {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
}

.stat-subtitle {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin-top: 0.25rem;
}

/* Portfolio Placeholder */
.portfolio-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  padding: 3rem;
  border: 1px solid var(--color-border);
  text-align: center;
}

.portfolio-placeholder h2 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.portfolio-placeholder p {
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
}

.login-button {
  background-color: var(--color-accent);
  color: #0f1123;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed);
}

.login-button:hover {
  background-color: var(--color-accent-dark);
  transform: translateY(-2px);
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 224, 255, 0.1);
  border-left-color: var(--color-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
}

/* Footer Styles */
.dashboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: rgba(23, 27, 54, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--color-border);
}

.footer-left p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.api-status {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
  background-color: var(--color-negative);
}

.api-status.online .status-dot {
  background-color: var(--color-positive);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .table-row {
    grid-template-columns: 0.5fr 2fr 1fr 1fr 1fr 1.5fr 0.5fr;
  }

  .table-head .table-row .table-cell:nth-child(7),
  .table-body .table-row .table-cell:nth-child(7) {
    display: none;
  }
}

@media (max-width: 992px) {
  .table-row {
    grid-template-columns: 0.5fr 2fr 1fr 1fr 1.5fr 0.5fr;
  }

  .table-head .table-row .table-cell:nth-child(6),
  .table-body .table-row .table-cell:nth-child(6) {
    display: none;
  }

  .crypto-stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
  }

  .header-center {
    display: none;
  }

  .content-tabs {
    padding: 0 1rem;
    overflow-x: auto;
  }

  .content-tabs button {
    padding: 1rem;
    white-space: nowrap;
  }

  .content-area {
    padding: 1rem;
  }

  .table-row {
    grid-template-columns: 2fr 1fr 1fr 0.5fr;
    padding: 0.75rem;
  }

  .table-head .table-row .table-cell:nth-child(1),
  .table-body .table-row .table-cell:nth-child(1),
  .table-head .table-row .table-cell:nth-child(5),
  .table-body .table-row .table-cell:nth-child(5) {
    display: none;
  }

  .crypto-detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .crypto-price-info {
    text-align: left;
  }

  .chart-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .time-range-selector, .chart-type-selector {
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .crypto-stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .dashboard-footer {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .table-row {
    grid-template-columns: 2fr 1fr 0.5fr;
  }

  .table-head .table-row .table-cell:nth-child(4),
  .table-body .table-row .table-cell:nth-child(4) {
    display: none;
  }

  .crypto-stats-grid {
    grid-template-columns: 1fr;
  }
}
