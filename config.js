/**
 * Configuración centralizada para Criptokens
 * Este archivo contiene todas las configuraciones necesarias para los diferentes servicios
 */

module.exports = {
  // Puertos para los diferentes servicios
  ports: {
    frontend: 5173,
    backend: 3002, // Cambiado a 3002 para evitar conflictos
    cryptoMcp: 3101,
    braveSearchMcp: 3102,
    playwrightMcp: 3103,
    technicalMcp: 3104,
    etherscanMcp: 3105
  },

  // URLs base para los diferentes servicios (usando los puertos definidos arriba)
  urls: {
    frontend: 'http://localhost:5173',
    backend: 'http://localhost:3002', // Actualizado para coincidir con el puerto nuevo
    cryptoMcp: 'http://localhost:3101',
    braveSearchMcp: 'http://localhost:3102',
    playwrightMcp: 'http://localhost:3103',
    technicalMcp: 'http://localhost:3104',
    etherscanMcp: 'http://localhost:3105'
  },

  // API Keys (en producción deberían estar en variables de entorno)
  apiKeys: {
    braveSearch: process.env.BRAVE_API_KEY || 'BSAccS820UUfffNOAD7yLACz9htlbe9',
    coinMarketCap: process.env.COINMARKETCAP_API_KEY || '37f9968e-6ab7-431f-80d7-0ac6686319f3',
    openRouter: process.env.OPENROUTER_API_KEY || '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',
    etherscan: process.env.ETHERSCAN_API_KEY || '**********************************',
    ultravox: process.env.ULTRAVOX_API_KEY || 'RMYI1Vn8.esFyGKVH1366gvk2VuxHnVMBToweW9aJ'
  },

  // Configuración CORS consistente para todos los servicios
  cors: {
    origins: [
      'http://localhost:5173', // Frontend
      'http://localhost:3002', // Backend (actualizado)
      'http://localhost:3001', // Backend (anterior)
      'http://localhost:3000'  // Desarrollo alternativo
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  },

  // Rutas de directorios (relativas a la raíz del proyecto)
  directories: {
    frontend: './frontend',
    backend: './backend',
    cryptoMcp: './crypto-mcp-server',
    playwrightMcp: './playwright-mcp-server',
    technicalMcp: './mcp-technical-analysis'
  }
};
