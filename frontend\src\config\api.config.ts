/**
 * Configuración centralizada para las APIs y servicios externos
 * 
 * Este archivo contiene todas las URLs y configuraciones necesarias para
 * conectarse a los diferentes servicios de backend y MCP.
 */

// Importar variables de entorno
const env = import.meta.env;

// Configuración de URLs
export const API_URLS = {
  // Backend
  backend: env.VITE_BACKEND_URL || 'http://localhost:3001',
  
  // Servidores MCP
  cryptoMcp: env.VITE_CRYPTO_MCP_URL || 'http://localhost:3101',
  braveSearchMcp: env.VITE_BRAVE_SEARCH_MCP_URL || 'http://localhost:3102',
  playwrightMcp: env.VITE_PLAYWRIGHT_MCP_URL || 'http://localhost:3103',
  etherscanMcp: env.VITE_ETHERSCAN_MCP_URL || 'http://localhost:3104',
  
  // APIs externas
  coincap: 'https://api.coincap.io/v2',
  coingecko: 'https://api.coingecko.com/api/v3'
};

// Configuración de endpoints
export const ENDPOINTS = {
  // Endpoints del backend
  guru: {
    ask: '/api/guru/ask',
    news: '/api/guru/news',
    portfolio: '/api/guru/portfolio',
    generateImage: '/api/guru/generate-image',
    visualizeWebPage: '/api/guru/visualize-webpage'
  },
  
  // Endpoints de criptomonedas
  crypto: {
    assets: '/assets',
    markets: '/markets',
    global: '/global'
  }
};

// Configuración de timeouts
export const TIMEOUTS = {
  default: 10000,
  short: 5000,
  long: 30000
};

// Configuración de caché
export const CACHE = {
  duration: 60 * 1000, // 60 segundos
  cryptoData: 5 * 60 * 1000, // 5 minutos
  news: 15 * 60 * 1000 // 15 minutos
};

// Configuración de fallbacks
export const FALLBACKS = {
  useMockData: env.VITE_USE_MOCK_DATA === 'true'
};

export default {
  API_URLS,
  ENDPOINTS,
  TIMEOUTS,
  CACHE,
  FALLBACKS
};
