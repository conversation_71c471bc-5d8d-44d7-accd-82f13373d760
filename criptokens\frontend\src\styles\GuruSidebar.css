.guru-sidebar {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, opacity 0.3s ease;
  cursor: pointer;
}

.guru-avatar-container {
  position: relative;
  background: rgba(15, 17, 35, 0.8);
  border-radius: 50%;
  padding: 10px;
  box-shadow: 0 8px 32px rgba(0, 242, 255, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(70, 87, 206, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: visible;
  width: 120px;
  height: 120px;
}

.avatar-wrapper {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.guru-sidebar:hover .guru-avatar-container {
  transform: translateY(-5px);
  box-shadow: 0 12px 32px rgba(0, 242, 255, 0.25);
}

/* Part<PERSON>culas del Guru */
.guru-particles {
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  z-index: 1;
  pointer-events: none;
  overflow: visible;
}

/* Círculos concéntricos */
.guru-orbit {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(0, 242, 255, 0.3);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.guru-orbit-1 {
  width: 140px;
  height: 140px;
  border-color: rgba(123, 77, 255, 0.4);
}

.guru-orbit-2 {
  width: 170px;
  height: 170px;
  border-color: rgba(0, 242, 255, 0.4);
}

.guru-orbit-3 {
  width: 200px;
  height: 200px;
  border-color: rgba(123, 77, 255, 0.3);
}

.guru-particle {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(0, 242, 255, 0.8);
  pointer-events: none;
  width: 6px !important;
  height: 6px !important;
  box-shadow: 0 0 8px rgba(0, 242, 255, 0.7);
}

/* Colores de partículas según el estado */
.guru-particle.positive {
  background-color: rgba(0, 255, 157, 0.8);
  box-shadow: 0 0 8px rgba(0, 255, 157, 0.7);
}

.guru-particle.negative {
  background-color: rgba(255, 58, 110, 0.8);
  box-shadow: 0 0 8px rgba(255, 58, 110, 0.7);
}

.guru-particle.concerned {
  background-color: rgba(255, 204, 0, 0.8);
  box-shadow: 0 0 8px rgba(255, 204, 0, 0.7);
}

.guru-particle.thinking {
  background-color: rgba(123, 77, 255, 0.8);
  box-shadow: 0 0 8px rgba(123, 77, 255, 0.7);
}

.guru-particle.neutral, .guru-particle.idle {
  background-color: rgba(0, 242, 255, 0.8);
  box-shadow: 0 0 8px rgba(0, 242, 255, 0.7);
}

/* Estilos para diferentes estados del mercado */
.guru-sidebar[data-sentiment="bullish"] .guru-avatar-container {
  box-shadow: 0 8px 32px rgba(0, 255, 157, 0.25);
  border-color: rgba(0, 255, 157, 0.3);
}

.guru-sidebar[data-sentiment="bearish"] .guru-avatar-container {
  box-shadow: 0 8px 32px rgba(255, 58, 110, 0.25);
  border-color: rgba(255, 58, 110, 0.3);
}

.guru-sidebar[data-sentiment="volatile"] .guru-avatar-container {
  box-shadow: 0 8px 32px rgba(255, 204, 0, 0.25);
  border-color: rgba(255, 204, 0, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .guru-sidebar {
    right: 10px;
    bottom: 10px;
  }

  .guru-avatar-container {
    padding: 10px;
  }

  .market-sentiment-indicator {
    font-size: 0.75rem;
    max-width: 150px;
  }

  .consult-guru-btn {
    padding: 5px 10px;
    font-size: 0.7rem;
  }
}
