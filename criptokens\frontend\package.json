{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@react-three/drei": "^9.102.6", "@react-three/fiber": "^8.16.0", "@types/animejs": "^3.1.13", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-router-dom": "^5.3.3", "@types/three": "^0.162.0", "animejs": "^3.2.1", "axios": "^1.8.4", "chart.js": "^4.4.9", "firebase": "^11.6.0", "react": "18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.1", "remark-gfm": "^4.0.1", "three": "^0.162.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}