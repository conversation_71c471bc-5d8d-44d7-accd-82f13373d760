import React, { useState } from 'react';
import '../../styles/dashboard/CustomAlertsWidget.css';

interface AlertItem {
  id: string;
  type: 'price' | 'volume' | 'news' | 'event';
  asset?: string;
  assetSymbol?: string;
  assetIcon?: string;
  condition: 'above' | 'below' | 'percent_change' | 'upcoming';
  value?: number;
  unit?: string;
  createdAt: string;
  active: boolean;
}

interface CustomAlertsWidgetProps {
  isLoading?: boolean;
}

const CustomAlertsWidget: React.FC<CustomAlertsWidgetProps> = ({ isLoading = false }) => {
  // Datos simulados de alertas (en producción, esto vendría de una API)
  const [alerts, setAlerts] = useState<AlertItem[]>([
    {
      id: '1',
      type: 'price',
      asset: 'Bitcoin',
      assetSymbol: 'BTC',
      assetIcon: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
      condition: 'above',
      value: 60000,
      unit: 'USD',
      createdAt: '2023-10-15T10:30:00Z',
      active: true
    },
    {
      id: '2',
      type: 'price',
      asset: 'Ethereum',
      assetSymbol: 'ETH',
      assetIcon: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
      condition: 'below',
      value: 2500,
      unit: 'USD',
      createdAt: '2023-10-16T14:45:00Z',
      active: true
    },
    {
      id: '3',
      type: 'volume',
      asset: 'Solana',
      assetSymbol: 'SOL',
      assetIcon: 'https://cryptologos.cc/logos/solana-sol-logo.png',
      condition: 'above',
      value: 1000000000,
      unit: 'USD',
      createdAt: '2023-10-17T09:15:00Z',
      active: false
    },
    {
      id: '4',
      type: 'news',
      condition: 'upcoming',
      createdAt: '2023-10-18T16:20:00Z',
      active: true
    },
    {
      id: '5',
      type: 'event',
      asset: 'Cardano',
      assetSymbol: 'ADA',
      assetIcon: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
      condition: 'upcoming',
      createdAt: '2023-10-19T11:10:00Z',
      active: true
    }
  ]);

  const [showAddAlert, setShowAddAlert] = useState(false);
  const [newAlertType, setNewAlertType] = useState<string>('price');

  // Formatear valor según el tipo y la unidad
  const formatAlertValue = (alert: AlertItem): string => {
    if (alert.type === 'price') {
      return `$${alert.value?.toLocaleString()}`;
    } else if (alert.type === 'volume') {
      if (alert.value && alert.value >= 1_000_000_000) {
        return `$${(alert.value / 1_000_000_000).toFixed(1)}B`;
      } else if (alert.value && alert.value >= 1_000_000) {
        return `$${(alert.value / 1_000_000).toFixed(1)}M`;
      }
      return `$${alert.value?.toLocaleString()}`;
    } else if (alert.type === 'news' || alert.type === 'event') {
      return 'Notificación';
    }
    return '';
  };

  // Obtener descripción de la alerta
  const getAlertDescription = (alert: AlertItem): string => {
    if (alert.type === 'price') {
      return `${alert.asset} ${alert.condition === 'above' ? 'por encima de' : 'por debajo de'} ${formatAlertValue(alert)}`;
    } else if (alert.type === 'volume') {
      return `Volumen de ${alert.asset} ${alert.condition === 'above' ? 'por encima de' : 'por debajo de'} ${formatAlertValue(alert)}`;
    } else if (alert.type === 'news') {
      return 'Notificar sobre noticias importantes del mercado';
    } else if (alert.type === 'event') {
      return `Notificar sobre eventos próximos de ${alert.asset || 'criptomonedas'}`;
    }
    return '';
  };

  // Obtener icono según el tipo de alerta
  const getAlertTypeIcon = (type: string): string => {
    switch (type) {
      case 'price':
        return 'fa-dollar-sign';
      case 'volume':
        return 'fa-chart-line';
      case 'news':
        return 'fa-newspaper';
      case 'event':
        return 'fa-calendar-day';
      default:
        return 'fa-bell';
    }
  };

  // Alternar el estado activo de una alerta
  const toggleAlertActive = (id: string) => {
    setAlerts(prevAlerts => 
      prevAlerts.map(alert => 
        alert.id === id ? { ...alert, active: !alert.active } : alert
      )
    );
  };

  // Eliminar una alerta
  const deleteAlert = (id: string) => {
    setAlerts(prevAlerts => prevAlerts.filter(alert => alert.id !== id));
  };

  // Simular la adición de una nueva alerta
  const handleAddAlert = () => {
    // En una implementación real, aquí se abriría un modal para configurar la alerta
    setShowAddAlert(false);
    
    // Por ahora, simplemente añadimos una alerta de ejemplo
    const newAlert: AlertItem = {
      id: `new-${Date.now()}`,
      type: newAlertType as 'price' | 'volume' | 'news' | 'event',
      asset: 'Bitcoin',
      assetSymbol: 'BTC',
      assetIcon: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
      condition: 'above',
      value: 65000,
      unit: 'USD',
      createdAt: new Date().toISOString(),
      active: true
    };
    
    setAlerts(prevAlerts => [newAlert, ...prevAlerts]);
  };

  if (isLoading) {
    return (
      <div className="custom-alerts-widget loading" data-testid="custom-alerts-loading">
        <div className="alerts-header">
          <h3>Mis Alertas</h3>
        </div>
        <div className="alerts-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="custom-alerts-widget" data-testid="custom-alerts-widget">
      <div className="alerts-header">
        <h3>Mis Alertas</h3>
        <button 
          className="add-alert-button"
          onClick={() => setShowAddAlert(!showAddAlert)}
          title="Añadir alerta"
        >
          <i className={`fas ${showAddAlert ? 'fa-times' : 'fa-plus'}`}></i>
        </button>
      </div>
      
      {showAddAlert && (
        <div className="add-alert-panel">
          <div className="alert-type-selector">
            <button 
              className={`type-button ${newAlertType === 'price' ? 'active' : ''}`}
              onClick={() => setNewAlertType('price')}
            >
              <i className="fas fa-dollar-sign"></i>
              <span>Precio</span>
            </button>
            <button 
              className={`type-button ${newAlertType === 'volume' ? 'active' : ''}`}
              onClick={() => setNewAlertType('volume')}
            >
              <i className="fas fa-chart-line"></i>
              <span>Volumen</span>
            </button>
            <button 
              className={`type-button ${newAlertType === 'news' ? 'active' : ''}`}
              onClick={() => setNewAlertType('news')}
            >
              <i className="fas fa-newspaper"></i>
              <span>Noticias</span>
            </button>
            <button 
              className={`type-button ${newAlertType === 'event' ? 'active' : ''}`}
              onClick={() => setNewAlertType('event')}
            >
              <i className="fas fa-calendar-day"></i>
              <span>Eventos</span>
            </button>
          </div>
          <div className="add-alert-actions">
            <button 
              className="cancel-button"
              onClick={() => setShowAddAlert(false)}
            >
              Cancelar
            </button>
            <button 
              className="confirm-button"
              onClick={handleAddAlert}
            >
              Añadir Alerta
            </button>
          </div>
        </div>
      )}
      
      <div className="alerts-content">
        {alerts.length > 0 ? (
          <div className="alerts-list">
            {alerts.map(alert => (
              <div key={alert.id} className={`alert-item ${!alert.active ? 'inactive' : ''}`}>
                <div className="alert-icon">
                  <i className={`fas ${getAlertTypeIcon(alert.type)}`}></i>
                </div>
                <div className="alert-details">
                  <div className="alert-asset">
                    {alert.assetIcon && alert.asset && (
                      <>
                        <img src={alert.assetIcon} alt={alert.asset} className="asset-icon" />
                        <span className="asset-name">{alert.asset}</span>
                        {alert.assetSymbol && (
                          <span className="asset-symbol">{alert.assetSymbol}</span>
                        )}
                      </>
                    )}
                    {!alert.asset && (
                      <span className="alert-type">
                        {alert.type === 'news' ? 'Alerta de Noticias' : 'Alerta de Eventos'}
                      </span>
                    )}
                  </div>
                  <p className="alert-description">{getAlertDescription(alert)}</p>
                </div>
                <div className="alert-actions">
                  <button 
                    className={`toggle-button ${alert.active ? 'active' : ''}`}
                    onClick={() => toggleAlertActive(alert.id)}
                    title={alert.active ? 'Desactivar alerta' : 'Activar alerta'}
                  >
                    <div className="toggle-track">
                      <div className="toggle-thumb"></div>
                    </div>
                  </button>
                  <button 
                    className="delete-button"
                    onClick={() => deleteAlert(alert.id)}
                    title="Eliminar alerta"
                  >
                    <i className="fas fa-trash-alt"></i>
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-alerts">
            <i className="fas fa-bell-slash"></i>
            <p>No tienes alertas configuradas.</p>
            <button 
              className="create-alert-button"
              onClick={() => setShowAddAlert(true)}
            >
              Crear Alerta
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomAlertsWidget;
