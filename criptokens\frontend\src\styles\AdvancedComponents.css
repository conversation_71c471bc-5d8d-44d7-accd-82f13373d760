/* Estilos para componentes avanzados */

/* Visualizaciones avanzadas de mercado */
.advanced-market-visualizations {
  background-color: rgba(15, 17, 35, 0.7);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 224, 255, 0.1);
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.visualization-header h3 {
  margin: 0;
  font-size: 18px;
  background: linear-gradient(45deg, #00e0ff, #00a3ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 600;
}

.visualization-tabs {
  display: flex;
  gap: 10px;
}

.tab-button {
  background-color: rgba(0, 224, 255, 0.1);
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-button:hover {
  background-color: rgba(0, 224, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.tab-button.active {
  background: linear-gradient(90deg, rgba(0, 224, 255, 0.3), rgba(0, 163, 255, 0.3));
  color: #fff;
  box-shadow: 0 2px 10px rgba(0, 224, 255, 0.2);
}

.visualization-content {
  min-height: 300px;
  position: relative;
}

.heatmap-container,
.dominance-container,
.correlation-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.loading-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

/* Estilos para el mapa de calor */
.heatmap-grid {
  animation: fadeIn 0.5s ease;
}

.heatmap-cell {
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Estilos para el gráfico de dominancia */
.dominance-legend {
  margin-top: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 5px 10px;
}

/* Estilos para la matriz de correlación */
.correlation-table {
  animation: fadeIn 0.5s ease;
}

.correlation-explanation {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-top: 15px;
  text-align: center;
}

/* Gráfico de análisis técnico */
.technical-analysis-chart {
  background-color: rgba(15, 17, 35, 0.7);
  border-radius: 12px;
  padding: clamp(0.5rem, 1vw, 20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  margin-bottom: clamp(0.5rem, 1vw, 20px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 224, 255, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: clamp(0.5rem, 1vw, 20px);
  flex-wrap: wrap;
  gap: clamp(0.25rem, 0.5vw, 15px);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.button-group {
  display: flex;
  gap: 5px;
}

.control-button {
  background-color: rgba(0, 224, 255, 0.1);
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: clamp(4px, 0.5vw, 6px) clamp(6px, 0.75vw, 12px);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: clamp(10px, 0.75vw, 12px);
  white-space: nowrap;
}

.control-button:hover {
  background-color: rgba(0, 224, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.control-button.active {
  background: linear-gradient(90deg, rgba(0, 224, 255, 0.3), rgba(0, 163, 255, 0.3));
  color: #fff;
  box-shadow: 0 2px 10px rgba(0, 224, 255, 0.2);
}

.chart-container {
  width: 100%;
  flex: 1;
  margin-bottom: clamp(0.5rem, 0.75vw, 15px);
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(15, 17, 35, 0.5);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* Animaciones */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .visualization-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .visualization-tabs {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 5px;
  }

  .chart-controls {
    flex-direction: column;
    gap: 8px;
  }

  .control-group {
    width: 100%;
  }

  .button-group {
    overflow-x: auto;
    padding-bottom: 5px;
    flex-wrap: nowrap;
    width: 100%;
    justify-content: flex-start;
  }

  .technical-analysis-chart {
    padding: 10px;
    margin-bottom: 10px;
  }

  .chart-container {
    min-height: 150px;
  }

  .control-button {
    padding: 4px 8px;
    font-size: 10px;
  }
}
