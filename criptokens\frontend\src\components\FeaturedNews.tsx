import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNewsSearch } from '../hooks/useNewsSearch';
import NewsCard from './NewsCard';
import '../styles/FeaturedNews.css';

interface FeaturedNewsProps {
  limit?: number;
  coinId?: string;
  coinName?: string;
}

const FeaturedNews: React.FC<FeaturedNewsProps> = ({
  limit = 3,
  coinId,
  coinName
}) => {
  const navigate = useNavigate();
  
  // Usar el hook personalizado para obtener noticias
  const { news, isLoading, error, fetchNews } = useNewsSearch({
    initialCount: limit,
    coinId,
    coinName,
    initialFreshness: 'pw' // Noticias de la última semana por defecto
  });

  // Actualizar las noticias cuando cambian los props
  useEffect(() => {
    fetchNews();
  }, [coinId, coinName, fetchNews]);

  // Navegar a la página de noticias completa
  const handleViewAllNews = () => {
    navigate('/news');
  };

  // Navegar a la página de noticias de una criptomoneda específica
  const handleViewCoinNews = () => {
    if (coinId) {
      navigate(`/news?coin=${coinId}`);
    }
  };

  // Renderizar el estado de carga
  const renderLoading = () => {
    return (
      <div className="featured-news-loading">
        <div className="loading-spinner"></div>
        <p>Cargando noticias destacadas...</p>
      </div>
    );
  };

  // Renderizar el mensaje de error
  const renderError = () => {
    return (
      <div className="featured-news-error">
        <i className="fas fa-exclamation-circle"></i>
        <p>{error}</p>
      </div>
    );
  };

  // Renderizar el mensaje de no hay resultados
  const renderNoResults = () => {
    return (
      <div className="featured-news-no-results">
        <i className="fas fa-newspaper"></i>
        <p>No hay noticias destacadas disponibles en este momento.</p>
      </div>
    );
  };

  return (
    <div className="featured-news-container">
      <div className="featured-news-header">
        <h2>
          {coinName 
            ? `Noticias sobre ${coinName}`
            : 'Noticias Destacadas'}
        </h2>
        <button 
          className="view-all-button"
          onClick={coinId ? handleViewCoinNews : handleViewAllNews}
        >
          Ver todas
        </button>
      </div>
      
      <div className="featured-news-content">
        {isLoading ? (
          renderLoading()
        ) : error ? (
          renderError()
        ) : news.length === 0 ? (
          renderNoResults()
        ) : (
          <div className="featured-news-grid">
            {news.slice(0, limit).map((item, index) => (
              <NewsCard key={`${item.url}-${index}`} news={item} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FeaturedNews;
