/**
 * Adaptador para el servidor MCP de Brave Search
 * 
 * Este adaptador se comunica con el servidor MCP de Brave Search para
 * realizar búsquedas web y obtener noticias.
 */

const BaseMcpAdapter = require('./base-mcp-adapter');
const logger = require('../utils/logger');

class BraveMcpAdapter extends BaseMcpAdapter {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(options = {}) {
    super('brave', options);
  }
  
  /**
   * Realiza una búsqueda web
   * @param {string} query - Consulta de búsqueda
   * @param {Object} [options={}] - Opciones adicionales
   * @param {number} [options.count=5] - Número de resultados a devolver
   * @param {string} [options.freshness='pm'] - Filtro de tiempo (pd: día, pw: semana, pm: mes, py: año)
   * @param {string} [options.country='ES'] - Código de país para la búsqueda
   * @returns {Promise<Object>} Resultados de la búsqueda
   */
  async search(query, options = {}) {
    const params = {
      query: query,
      count: options.count || 5,
      freshness: options.freshness || 'pm',
      country: options.country || 'ES'
    };
    
    logger.debug('BraveMcpAdapter', `Realizando búsqueda web con consulta "${query}"`, params);
    
    return await this.executeTool('search', params);
  }
  
  /**
   * Busca noticias sobre criptomonedas
   * @param {string} query - Consulta de búsqueda
   * @param {Object} [options={}] - Opciones adicionales
   * @param {number} [options.count=5] - Número de resultados a devolver
   * @param {string} [options.freshness='pd'] - Filtro de tiempo (pd: día, pw: semana, pm: mes, py: año)
   * @param {string} [options.country='ES'] - Código de país para la búsqueda
   * @returns {Promise<Object>} Resultados de la búsqueda de noticias
   */
  async getNews(query, options = {}) {
    const params = {
      query: `${query} cryptocurrency crypto news`,
      count: options.count || 5,
      freshness: options.freshness || 'pd',
      country: options.country || 'ES',
      type: 'news'
    };
    
    logger.debug('BraveMcpAdapter', `Buscando noticias sobre criptomonedas con consulta "${query}"`, params);
    
    return await this.executeTool('getNews', params);
  }
  
  /**
   * Busca noticias específicas sobre una criptomoneda
   * @param {string} symbol - Símbolo de la criptomoneda
   * @param {Object} [options={}] - Opciones adicionales
   * @param {number} [options.count=5] - Número de resultados a devolver
   * @param {string} [options.freshness='pd'] - Filtro de tiempo (pd: día, pw: semana, pm: mes, py: año)
   * @returns {Promise<Object>} Resultados de la búsqueda de noticias
   */
  async getCoinNews(symbol, options = {}) {
    const params = {
      query: `${symbol} cryptocurrency price news`,
      count: options.count || 5,
      freshness: options.freshness || 'pd',
      country: options.country || 'ES',
      type: 'news'
    };
    
    logger.debug('BraveMcpAdapter', `Buscando noticias sobre ${symbol}`, params);
    
    return await this.executeTool('getNews', params);
  }
  
  /**
   * Busca información sobre un tema específico de criptomonedas
   * @param {string} topic - Tema a buscar
   * @param {Object} [options={}] - Opciones adicionales
   * @param {number} [options.count=5] - Número de resultados a devolver
   * @returns {Promise<Object>} Resultados de la búsqueda
   */
  async searchCryptoTopic(topic, options = {}) {
    const params = {
      query: `${topic} cryptocurrency blockchain`,
      count: options.count || 5,
      freshness: options.freshness || 'pm',
      country: options.country || 'ES'
    };
    
    logger.debug('BraveMcpAdapter', `Buscando información sobre el tema "${topic}" de criptomonedas`, params);
    
    return await this.executeTool('search', params);
  }
}

module.exports = BraveMcpAdapter;
