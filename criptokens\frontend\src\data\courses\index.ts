// Lista de cursos disponibles en la Academia Cripto
export const availableCourses = [
  {
    id: 'crypto-fundamentals',
    title: 'Fundamentos de Criptomonedas',
    description: 'Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.',
    level: 'beginner',
    duration: '4 horas',
    instructor: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    modules: [
      { id: 'module-1', title: '¿Qué son las Criptomonedas?' },
      { id: 'module-2', title: 'Blockchain: La Tecnología Detrás de las Criptomonedas' },
      { id: 'module-3', title: 'Bitcoin: La Primera Criptomoneda' },
      { id: 'module-4', title: 'Ethereum y Contratos Inteligentes' },
      { id: 'module-5', title: 'Altcoins y Tokens' },
      { id: 'module-6', title: 'Wallets y Seguridad' },
      { id: 'module-7', title: 'Usos Prácticos de las Criptomonedas' }
    ]
  },
  {
    id: 'defi-essentials',
    title: 'Finanzas Descentralizadas (DeFi)',
    description: 'Descubre el mundo de las finanzas descentralizadas, protocolos, oportunidades y riesgos en este ecosistema emergente.',
    level: 'intermediate',
    duration: '5 horas',
    instructor: {
      name: 'Laura Martínez',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    modules: [
      { id: 'module-1', title: 'Introducción a DeFi' },
      { id: 'module-2', title: 'Protocolos de Préstamos' },
      { id: 'module-3', title: 'Exchanges Descentralizados (DEX)' },
      { id: 'module-4', title: 'Yield Farming y Staking' },
      { id: 'module-5', title: 'Gestión de Riesgos en DeFi' }
    ]
  },
  {
    id: 'crypto-trading',
    title: 'Trading de Criptomonedas',
    description: 'Aprende estrategias de trading, análisis técnico y gestión de riesgos para operar en el mercado de criptomonedas.',
    level: 'intermediate',
    duration: '6 horas',
    instructor: {
      name: 'Carlos Vega',
      avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
    },
    modules: [
      { id: 'module-1', title: 'Fundamentos del Trading' },
      { id: 'module-2', title: 'Análisis Técnico Básico' },
      { id: 'module-3', title: 'Análisis Técnico Avanzado' },
      { id: 'module-4', title: 'Gestión de Riesgos y Capital' },
      { id: 'module-5', title: 'Psicología del Trading' },
      { id: 'module-6', title: 'Estrategias de Trading' }
    ]
  },
  {
    id: 'crypto-security',
    title: 'Seguridad en Criptomonedas',
    description: 'Protege tus activos digitales aprendiendo las mejores prácticas de seguridad en el mundo de las criptomonedas.',
    level: 'beginner',
    duration: '3 horas',
    instructor: {
      name: 'Elena Gómez',
      avatar: 'https://randomuser.me/api/portraits/women/22.jpg'
    },
    modules: [
      { id: 'module-1', title: 'Amenazas y Riesgos en Criptomonedas' },
      { id: 'module-2', title: 'Tipos de Wallets y su Seguridad' },
      { id: 'module-3', title: 'Gestión Segura de Claves Privadas' },
      { id: 'module-4', title: 'Identificación de Estafas y Phishing' },
      { id: 'module-5', title: 'Plan de Seguridad Personal' }
    ]
  },
  {
    id: 'nft-masterclass',
    title: 'NFTs: Tokens No Fungibles',
    description: 'Explora el fascinante mundo de los NFTs, desde coleccionables digitales hasta aplicaciones en arte, gaming y más.',
    level: 'beginner',
    duration: '4 horas',
    instructor: {
      name: 'Miguel Torres',
      avatar: 'https://randomuser.me/api/portraits/men/42.jpg'
    },
    modules: [
      { id: 'module-1', title: '¿Qué son los NFTs?' },
      { id: 'module-2', title: 'Creación y Venta de NFTs' },
      { id: 'module-3', title: 'NFTs en Arte y Coleccionables' },
      { id: 'module-4', title: 'NFTs en Gaming y Metaverso' },
      { id: 'module-5', title: 'El Futuro de los NFTs' }
    ]
  },
  {
    id: 'blockchain-development',
    title: 'Desarrollo en Blockchain',
    description: 'Aprende a desarrollar aplicaciones descentralizadas (dApps) y contratos inteligentes en Ethereum y otras plataformas.',
    level: 'advanced',
    duration: '8 horas',
    instructor: {
      name: 'David Herrera',
      avatar: 'https://randomuser.me/api/portraits/men/75.jpg'
    },
    modules: [
      { id: 'module-1', title: 'Fundamentos de Desarrollo Blockchain' },
      { id: 'module-2', title: 'Solidity: Lenguaje para Contratos Inteligentes' },
      { id: 'module-3', title: 'Desarrollo de Contratos Inteligentes' },
      { id: 'module-4', title: 'Frameworks y Herramientas (Truffle, Hardhat)' },
      { id: 'module-5', title: 'Integración con Frontend (Web3.js, ethers.js)' },
      { id: 'module-6', title: 'Seguridad en Contratos Inteligentes' },
      { id: 'module-7', title: 'Despliegue y Mantenimiento de dApps' }
    ]
  }
];
