/**
 * Servicio para interactuar con la API de Etherscan a través del MCP
 */

const axios = require('axios');
const { ETHERSCAN_MCP_URL } = process.env;

/**
 * Clase para interactuar con la API de Etherscan a través del MCP
 */
class EtherscanService {
  constructor() {
    this.baseUrl = ETHERSCAN_MCP_URL || 'http://localhost:3103';
  }

  /**
   * Obtiene el balance de una dirección Ethereum
   * @param {string} address - Dirección Ethereum
   * @returns {Promise<Object>} - Respuesta con el balance
   */
  async getAddressBalance(address) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getAddressBalance',
        address
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el balance de la dirección:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el balance de la dirección',
        data: null
      };
    }
  }

  /**
   * Obtiene las transacciones de una dirección Ethereum
   * @param {string} address - Dirección Ethereum
   * @returns {Promise<Object>} - Respuesta con las transacciones
   */
  async getAddressTransactions(address) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getAddressTransactions',
        address
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener las transacciones de la dirección:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener las transacciones de la dirección',
        data: null
      };
    }
  }

  /**
   * Obtiene el precio actual de Ethereum
   * @returns {Promise<Object>} - Respuesta con el precio
   */
  async getEthereumPrice() {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getEthereumPrice'
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el precio de Ethereum:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el precio de Ethereum',
        data: null
      };
    }
  }

  /**
   * Obtiene información sobre un token ERC-20
   * @param {string} contractAddress - Dirección del contrato del token
   * @returns {Promise<Object>} - Respuesta con la información del token
   */
  async getTokenInfo(contractAddress) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getTokenInfo',
        contractAddress
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener información del token:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener información del token',
        data: null
      };
    }
  }

  /**
   * Obtiene el balance de un token ERC-20 para una dirección
   * @param {string} address - Dirección Ethereum
   * @param {string} contractAddress - Dirección del contrato del token
   * @returns {Promise<Object>} - Respuesta con el balance del token
   */
  async getTokenBalance(address, contractAddress) {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, {
        action: 'getTokenBalance',
        address,
        contractAddress
      });
      return response.data;
    } catch (error) {
      console.error('Error al obtener el balance del token:', error);
      return {
        status: 'error',
        message: 'No se pudo obtener el balance del token',
        data: null
      };
    }
  }
}

module.exports = new EtherscanService();
