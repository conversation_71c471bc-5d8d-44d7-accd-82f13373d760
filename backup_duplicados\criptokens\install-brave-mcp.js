const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Instalando dependencias para el servidor MCP de Brave Search...');

// Verificar si el archivo de configuración existe
const configPath = path.join(__dirname, 'mcp-config.json');
if (!fs.existsSync(configPath)) {
  console.error('Error: No se encontró el archivo de configuración mcp-config.json');
  process.exit(1);
}

// Leer la configuración
let mcpConfig;
try {
  mcpConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} catch (error) {
  console.error('Error al leer la configuración:', error);
  process.exit(1);
}

// Verificar si la configuración de Brave Search existe
if (!mcpConfig.mcpServers || !mcpConfig.mcpServers['brave-search']) {
  console.error('Error: No se encontró la configuración para el servidor MCP de Brave Search');
  process.exit(1);
}

// Obtener la configuración del servidor
const braveSearchConfig = mcpConfig.mcpServers['brave-search'];
const packageName = braveSearchConfig.args[1];

// Instalar el paquete
try {
  console.log(`Instalando ${packageName}...`);
  execSync(`npm install -g ${packageName}`, { stdio: 'inherit' });
  console.log(`${packageName} instalado correctamente.`);
} catch (error) {
  console.error('Error al instalar el paquete:', error);
  process.exit(1);
}

console.log('Instalación completada. Ahora puede iniciar el servidor MCP de Brave Search con el comando:');
console.log('node start-all.js');
