/**
 * MCP Technical Analysis Server
 * 
 * Este servidor proporciona endpoints para realizar análisis técnico
 * de datos de criptomonedas, incluyendo cálculo de indicadores,
 * detección de patrones y señales de trading.
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

// Controladores
const indicatorsController = require('./controllers/indicators.controller');

// Configuración
const app = express();
const PORT = process.env.PORT || 3104;

// Middleware
app.use(helmet()); // Seguridad
app.use(cors()); // Permitir CORS
app.use(express.json()); // Parsear JSON
app.use(morgan('dev')); // Logging

// Ruta de estado
app.get('/status', (req, res) => {
  res.json({
    status: 'ok',
    service: 'MCP Technical Analysis',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Rutas para indicadores técnicos
app.post('/indicators/rsi', indicatorsController.calculateRSI);
app.post('/indicators/macd', indicatorsController.calculateMACD);
app.post('/indicators/bollinger', indicatorsController.calculateBollingerBands);
app.post('/indicators/sma', indicatorsController.calculateSMA);
app.post('/indicators/ema', indicatorsController.calculateEMA);

// Ruta para calcular múltiples indicadores a la vez
app.post('/indicators/multi', indicatorsController.calculateMultipleIndicators);

// Rutas para patrones de velas
app.post('/patterns/detect', indicatorsController.detectCandlePatterns);

// Ruta para análisis completo
app.post('/analysis/full', indicatorsController.performFullAnalysis);

// Manejo de errores
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Error interno del servidor',
    message: err.message
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`MCP Technical Analysis running on port ${PORT}`);
  console.log(`Status endpoint: http://localhost:${PORT}/status`);
});

module.exports = app; // Para testing
