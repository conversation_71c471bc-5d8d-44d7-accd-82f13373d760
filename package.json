{"name": "criptok<PERSON>", "version": "1.0.0", "description": "Plataforma de análisis de criptomonedas con agentes de IA", "scripts": {"start:mcp:crypto": "cd ./crypto-mcp-server && node index.js", "start:mcp:brave": "node brave-search-server.js", "start:mcp:playwright": "cd ./playwright-mcp-server && node dist/server.js", "start:mcp:context7": "npx -y @upstash/context7-mcp@latest", "start:mcp:onchain": "cd ./onchain-mcp-server && node index.js", "start:backend": "cd ./backend && node src/server.js", "start:frontend": "cd ./frontend && npm run dev", "start:adk:agents": "python start_adk_agents.py", "start:adk:orchestrator": "python start_guru_orchestrator.py", "start:all": "concurrently --kill-others-on-fail \"npm:start:mcp:crypto\" \"npm:start:mcp:brave\" \"npm:start:mcp:playwright\" \"npm:start:mcp:context7\" \"npm:start:mcp:onchain\" \"npm:start:backend\" \"npm:start:adk:agents\" \"npm:start:adk:orchestrator\" \"npm:start:frontend\""}, "dependencies": {"axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "html2canvas": "^1.4.1", "node-cache": "^5.1.2", "socket.io-client": "^4.8.1"}, "devDependencies": {"concurrently": "^8.2.2"}}