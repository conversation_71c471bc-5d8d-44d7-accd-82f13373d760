# Script para iniciar el backend y frontend de Criptokens
# Autor: CreastilloQRGen-AI
# Fecha: 2024-04-23

Write-Host "Iniciando el backend y frontend de Criptokens..." -ForegroundColor Green

# Función para verificar si un puerto está en uso
function Test-PortInUse {
    param (
        [int]$Port
    )
    
    $connections = Get-NetTCPConnection -State Listen -ErrorAction SilentlyContinue | Where-Object { $_.LocalPort -eq $Port }
    return ($null -ne $connections)
}

# Función para iniciar un proceso en una nueva ventana
function Start-ServerProcess {
    param (
        [string]$Name,
        [string]$Command,
        [string]$Arguments,
        [string]$WorkingDirectory,
        [int]$Port,
        [hashtable]$EnvironmentVariables = @{},
        [switch]$Wait
    )
    
    # Verificar si el puerto ya está en uso
    if (Test-PortInUse -Port $Port) {
        Write-Host "ADVERTENCIA: El puerto $Port ya está en uso. El servidor $Name podría no iniciar correctamente." -ForegroundColor Yellow
    }
    
    # Crear un bloque de script que incluya las variables de entorno
    $envVarsScript = ""
    foreach ($key in $EnvironmentVariables.Keys) {
        $value = $EnvironmentVariables[$key]
        $envVarsScript += "`$env:$key = '$value'`n"
    }
    
    # Agregar el comando al bloque de script
    $scriptBlock = "$envVarsScript`nSet-Location -Path '$WorkingDirectory'`n$Command $Arguments"
    
    # Iniciar el proceso en una nueva ventana
    Write-Host "Iniciando $Name en puerto $Port..." -ForegroundColor Cyan
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $scriptBlock
    
    # Esperar un momento para que el servidor inicie
    if ($Wait) {
        $waitTime = 3
        Write-Host "Esperando $waitTime segundos para que $Name inicie..." -ForegroundColor Gray
        Start-Sleep -Seconds $waitTime
    }
}

# Obtener la ruta del directorio actual
$currentDir = Get-Location
$rootDir = $currentDir

# Definir las rutas de los directorios
$frontendDir = Join-Path -Path $rootDir -ChildPath "frontend"
$backendDir = Join-Path -Path $rootDir -ChildPath "backend"

# Verificar que los directorios existan
if (-not (Test-Path -Path $frontendDir)) {
    Write-Host "Error: No se encontró el directorio frontend en $frontendDir" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path -Path $backendDir)) {
    Write-Host "Error: No se encontró el directorio backend en $backendDir" -ForegroundColor Red
    exit 1
}

# Paso 1: Iniciar el backend (puerto 3001)
Start-ServerProcess -Name "Backend Server" -Command "npm" -Arguments "start" -WorkingDirectory $backendDir -Port 3001 -Wait

# Paso 2: Iniciar el frontend (puerto 5173)
Start-ServerProcess -Name "Frontend Server" -Command "npm" -Arguments "run dev" -WorkingDirectory $frontendDir -Port 5173

Write-Host @"

¡Backend y Frontend iniciados correctamente!

Servidores disponibles:
- Frontend: http://localhost:5173
- Backend: http://localhost:3001

Para detener los servidores, cierra las ventanas de PowerShell o presiona Ctrl+C en cada una.

NOTA: Este script solo inicia el backend y frontend. Para utilizar todas las funcionalidades
de Criptokens, también necesitas iniciar los servidores MCP con el script start-mcp-servers.ps1.
"@ -ForegroundColor Green
