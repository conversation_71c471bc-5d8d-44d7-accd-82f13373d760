/**
 * Herramientas MCP relacionadas con tokens
 */
const etherscanService = require('../services/etherscan');
const config = require('../config');

module.exports = {
  /**
   * Obtiene las transferencias de un token específico
   */
  getTokenTransfersByToken: {
    description: 'Get transfers of a specific token',
    parameters: {
      type: 'object',
      properties: {
        tokenAddress: {
          type: 'string',
          description: 'The token contract address'
        },
        page: {
          type: 'integer',
          description: 'Page number',
          default: 1
        },
        offset: {
          type: 'integer',
          description: 'Number of transfers per page',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['tokenAddress']
    },
    handler: async ({ tokenAddress, page = 1, offset = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Para este caso, usamos la API de eventos del contrato
        const events = await etherscanService.getContractEvents(
          tokenAddress,
          '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', // Transfer event signature
          page,
          offset
        );
        
        // Obtener información del token
        const tokenInfo = await etherscanService.getTokenInfo(tokenAddress);
        
        return {
          tokenAddress,
          tokenName: tokenInfo.name,
          tokenSymbol: tokenInfo.symbol,
          events: events.events,
          page,
          offset,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene las transferencias de un token de criptomoneda por su nombre
   */
  getCryptoTransfersByToken: {
    description: 'Get transfers of a cryptocurrency token by name',
    parameters: {
      type: 'object',
      properties: {
        cryptoId: {
          type: 'string',
          description: 'The cryptocurrency ID (bitcoin, ethereum, etc.)'
        },
        page: {
          type: 'integer',
          description: 'Page number',
          default: 1
        },
        offset: {
          type: 'integer',
          description: 'Number of transfers per page',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['cryptoId']
    },
    handler: async ({ cryptoId, page = 1, offset = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener la dirección del token para la criptomoneda
        const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
        
        if (!tokenAddress) {
          throw new Error(`Token address not found for ${cryptoId}`);
        }
        
        // Usar la herramienta de transferencias de token
        return await module.exports.getTokenTransfersByToken.handler({
          tokenAddress,
          page,
          offset,
          chain
        });
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Analiza la actividad de un token
   */
  analyzeTokenActivity: {
    description: 'Analyze activity of a token',
    parameters: {
      type: 'object',
      properties: {
        tokenAddress: {
          type: 'string',
          description: 'The token contract address'
        },
        days: {
          type: 'integer',
          description: 'Number of days to analyze',
          default: 7
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['tokenAddress']
    },
    handler: async ({ tokenAddress, days = 7, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener información del token
        const tokenInfo = await etherscanService.getTokenInfo(tokenAddress);
        
        // Obtener eventos de transferencia (máximo 100)
        const events = await etherscanService.getContractEvents(
          tokenAddress,
          '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', // Transfer event signature
          1,
          100
        );
        
        // Filtrar por fecha (últimos X días)
        const now = Date.now();
        const timeThreshold = now - (days * 24 * 60 * 60 * 1000);
        
        // Nota: Los eventos de Etherscan no incluyen timestamp directamente,
        // por lo que esta es una aproximación basada en el número de bloque
        // En una implementación real, se debería obtener el timestamp de cada bloque
        
        // Calcular estadísticas básicas
        const totalTransfers = events.events.length;
        
        // Obtener las ballenas
        const whalesData = await etherscanService.getTokenWhales(tokenAddress, 5);
        
        return {
          tokenAddress,
          tokenName: tokenInfo.name,
          tokenSymbol: tokenInfo.symbol,
          tokenDecimals: tokenInfo.decimals,
          totalSupply: tokenInfo.totalSupply,
          period: `Last ${days} days (approximate)`,
          activity: {
            totalTransfers,
            topHolders: whalesData.whales
          },
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Analiza la actividad de un token de criptomoneda por su nombre
   */
  analyzeCryptoActivity: {
    description: 'Analyze activity of a cryptocurrency token by name',
    parameters: {
      type: 'object',
      properties: {
        cryptoId: {
          type: 'string',
          description: 'The cryptocurrency ID (bitcoin, ethereum, etc.)'
        },
        days: {
          type: 'integer',
          description: 'Number of days to analyze',
          default: 7
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['cryptoId']
    },
    handler: async ({ cryptoId, days = 7, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener la dirección del token para la criptomoneda
        const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
        
        if (!tokenAddress) {
          throw new Error(`Token address not found for ${cryptoId}`);
        }
        
        // Usar la herramienta de análisis de token
        const analysis = await module.exports.analyzeTokenActivity.handler({
          tokenAddress,
          days,
          chain
        });
        
        return {
          ...analysis,
          cryptoId
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  }
};
