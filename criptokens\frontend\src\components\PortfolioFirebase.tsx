import { useState } from 'react';
import { useAuth } from '../context/NewAuthContext';
import { usePortfolio } from '../hooks/usePortfolio';
import { PortfolioAsset } from '../services/portfolio.service';
import AddFundsModal from './AddFundsModal';
import '../styles/Portfolio.css';



const PortfolioFirebase = () => {
  const { currentUser } = useAuth();
  const {
    portfolio,
    portfolioStats,
    isLoading,
    error,
    availableCryptos,
    currentPrices,
    userFunds,
    addAssetToPortfolio,
    removeAssetFromPortfolio,
    refreshPrices,
    calculateAssetStats,
    addFunds
  } = usePortfolio();

  const [showAddAssetForm, setShowAddAssetForm] = useState(false);
  const [showAddFundsModal, setShowAddFundsModal] = useState(false);

  // Form state
  const [selectedCryptoId, setSelectedCryptoId] = useState('');
  const [amount, setAmount] = useState('');
  const [purchasePrice, setPurchasePrice] = useState('');
  const [formError, setFormError] = useState<string | null>(null);

  // Función para manejar la adición de un nuevo activo
  const handleAddAsset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser) {
      setFormError('Debes iniciar sesión para añadir activos a tu cartera.');
      return;
    }

    setFormError(null);

    // Validar el formulario
    if (!selectedCryptoId) {
      setFormError('Por favor, selecciona una criptomoneda.');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      setFormError('Por favor, ingresa una cantidad válida.');
      return;
    }

    if (!purchasePrice || parseFloat(purchasePrice) <= 0) {
      setFormError('Por favor, ingresa un precio de compra válido.');
      return;
    }

    try {
      // Obtener detalles de la criptomoneda seleccionada
      const selectedCrypto = availableCryptos.find(crypto => crypto.id === selectedCryptoId);

      // Crear el nuevo activo
      const newAsset: PortfolioAsset = {
        id: selectedCryptoId,
        symbol: selectedCrypto.symbol.toUpperCase(),
        name: selectedCrypto.name,
        amount: parseFloat(amount),
        purchasePrice: parseFloat(purchasePrice),
        purchaseDate: new Date()
      };

      // Añadir el activo a la cartera
      const success = await addAssetToPortfolio(newAsset);

      if (success) {
        // Resetear el formulario
        setSelectedCryptoId('');
        setAmount('');
        setPurchasePrice('');
        setShowAddAssetForm(false);
      }
    } catch (err) {
      console.error('Error al añadir activo:', err);
      setFormError('No se pudo añadir el activo. Por favor, intenta de nuevo.');
    }
  };

  // Función para manejar la eliminación de un activo
  const handleRemoveAsset = async (id: string) => {
    if (!currentUser) return;

    if (window.confirm('¿Estás seguro de que quieres eliminar este activo de tu cartera?')) {
      await removeAssetFromPortfolio(id);
    }
  };

  // Función para actualizar los precios de la cartera
  const handleRefreshPrices = async () => {
    if (!currentUser) return;
    await refreshPrices();
  };

  // Función para manejar la adición de fondos
  const handleAddFunds = async (amount: number, currency: string) => {
    if (!currentUser) return false;
    return await addFunds(amount, currency);
  };

  // Formatear números para mostrar
  const formatNumber = (num: number, decimals = 2) => {
    return num.toLocaleString(undefined, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  };

  // Formatear fecha para mostrar
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!currentUser) {
    return (
      <div className="portfolio-container">
        <div className="portfolio-not-logged-in">
          <h3>Acceso Restringido</h3>
          <p>Debes iniciar sesión para ver y gestionar tu cartera de criptomonedas.</p>
        </div>
      </div>
    );
  }

  if (isLoading && portfolio.length === 0) {
    return <div className="portfolio-loading">Cargando cartera...</div>;
  }

  if (error && portfolio.length === 0) {
    return <div className="portfolio-error">{error}</div>;
  }

  return (
    <div className="firebase-portfolio-container">
      <div className="firebase-portfolio-header">
        <div className="firebase-portfolio-title-section">
          <h2>Mi Cartera</h2>
          <div className="firebase-portfolio-funds">
            <span className="firebase-funds-label">Fondos disponibles:</span>
            <span className="firebase-funds-value">{userFunds.currency} {formatNumber(userFunds.balance)}</span>
            <button
              className="firebase-add-funds-button"
              onClick={() => setShowAddFundsModal(true)}
            >
              Añadir Fondos
            </button>
          </div>
        </div>
        <div className="firebase-portfolio-actions">
          <button
            className="firebase-refresh-button"
            onClick={handleRefreshPrices}
            disabled={isLoading || portfolio.length === 0}
          >
            Actualizar Precios
          </button>
          <button
            className="firebase-add-asset-button"
            onClick={() => setShowAddAssetForm(!showAddAssetForm)}
          >
            {showAddAssetForm ? 'Cancelar' : 'Añadir Activo'}
          </button>
        </div>
      </div>

      {showAddAssetForm && (
        <div className="firebase-add-asset-form-container">
          <form className="firebase-add-asset-form" onSubmit={handleAddAsset}>
            <h3>Añadir Nuevo Activo</h3>

            {formError && <p className="firebase-form-error">{formError}</p>}

            <div className="firebase-form-group">
              <label htmlFor="crypto-select">Criptomoneda</label>
              <select
                id="crypto-select"
                value={selectedCryptoId}
                onChange={(e) => setSelectedCryptoId(e.target.value)}
                required
              >
                <option value="">Selecciona una criptomoneda</option>
                {availableCryptos.map((crypto) => (
                  <option key={crypto.id} value={crypto.id}>
                    {crypto.name} ({crypto.symbol.toUpperCase()})
                  </option>
                ))}
              </select>
            </div>

            <div className="firebase-form-group">
              <label htmlFor="amount-input">Cantidad</label>
              <input
                id="amount-input"
                type="number"
                min="0.00000001"
                step="0.00000001"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Ej: 0.5"
                required
              />
            </div>

            <div className="firebase-form-group">
              <label htmlFor="price-input">Precio de Compra (USD)</label>
              <input
                id="price-input"
                type="number"
                min="0.00000001"
                step="0.00000001"
                value={purchasePrice}
                onChange={(e) => setPurchasePrice(e.target.value)}
                placeholder="Ej: 50000"
                required
              />
            </div>

            <div className="firebase-form-actions">
              <button
                type="button"
                className="firebase-cancel-button"
                onClick={() => setShowAddAssetForm(false)}
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="firebase-submit-button"
                disabled={isLoading}
              >
                Añadir
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="firebase-portfolio-summary">
        <div className="firebase-summary-item">
          <span className="firebase-summary-label">Valor Total:</span>
          <span className="firebase-summary-value">${formatNumber(portfolioStats.totalValue)}</span>
        </div>

        <div className="firebase-summary-item">
          <span className="firebase-summary-label">Ganancia/Pérdida:</span>
          <span className={`firebase-summary-value ${portfolioStats.totalProfitLoss >= 0 ? 'positive' : 'negative'}`}>
            ${formatNumber(portfolioStats.totalProfitLoss)}
            ({formatNumber(portfolioStats.totalProfitLossPercentage)}%)
          </span>
        </div>

        <div className="firebase-summary-item">
          <span className="firebase-summary-label">Última Actualización:</span>
          <span className="firebase-summary-value firebase-date">
            {formatDate(portfolioStats.lastUpdated)}
          </span>
        </div>
      </div>

      {portfolio.length > 0 ? (
        <div className="firebase-portfolio-assets">
          <table className="firebase-assets-table">
            <thead>
              <tr>
                <th>Activo</th>
                <th>Cantidad</th>
                <th>Precio de Compra</th>
                <th>Precio Actual</th>
                <th>Valor</th>
                <th>Ganancia/Pérdida</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              {portfolio.map((asset) => {
                const stats = calculateAssetStats(asset);
                return (
                  <tr key={asset.id}>
                    <td className="firebase-asset-info">
                      {availableCryptos.find(c => c.id === asset.id)?.image && (
                        <img
                          src={availableCryptos.find(c => c.id === asset.id)?.image}
                          alt={asset.name}
                          className="firebase-asset-icon"
                        />
                      )}
                      <div>
                        <span className="firebase-asset-name">{asset.name}</span>
                        <span className="firebase-asset-symbol">{asset.symbol}</span>
                      </div>
                    </td>
                    <td>{formatNumber(asset.amount, 8)}</td>
                    <td>${formatNumber(asset.purchasePrice)}</td>
                    <td>${formatNumber(stats.currentPrice)}</td>
                    <td>${formatNumber(stats.value)}</td>
                    <td className={stats.profitLoss >= 0 ? 'firebase-positive' : 'firebase-negative'}>
                      ${formatNumber(stats.profitLoss)}
                      ({formatNumber(stats.profitLossPercentage)}%)
                    </td>
                    <td>
                      <button
                        className="firebase-remove-asset-button"
                        onClick={() => handleRemoveAsset(asset.id)}
                      >
                        Eliminar
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="firebase-empty-portfolio">
          <p>No tienes activos en tu cartera.</p>
          <p>Haz clic en "Añadir Activo" para comenzar a construir tu cartera.</p>
        </div>
      )}

      {/* Modal para añadir fondos */}
      <AddFundsModal
        isOpen={showAddFundsModal}
        onClose={() => setShowAddFundsModal(false)}
        onAddFunds={handleAddFunds}
      />
    </div>
  );
};

export default PortfolioFirebase;
