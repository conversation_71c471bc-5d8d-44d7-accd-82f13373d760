import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useNewsSearch } from '../hooks/useNewsSearch';
import NewsList from '../components/NewsList';
import FeaturedNews from '../components/FeaturedNews';
import '../styles/NewsPage.css';

const NewsPage: React.FC = () => {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [coinId, setCoinId] = useState<string | undefined>(undefined);
  const [coinName, setCoinName] = useState<string | undefined>(undefined);

  // Obtener parámetros de la URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const coin = params.get('coin');
    const query = params.get('query');

    if (coin) {
      setCoinId(coin);
      // Aquí podrías hacer una llamada a la API para obtener el nombre completo de la criptomoneda
      // Por ahora, usamos el ID como nombre
      setCoinName(coin.charAt(0).toUpperCase() + coin.slice(1));
    } else {
      setCoinId(undefined);
      setCoinName(undefined);
    }

    if (query) {
      setSearchQuery(query);
    }
  }, [location.search]);

  // Usar el hook personalizado para obtener noticias
  const {
    news,
    isLoading,
    error,
    fetchNews,
    fetchMoreNews
  } = useNewsSearch({
    initialQuery: searchQuery,
    coinId,
    coinName,
    initialCount: 12
  });

  // Manejar la búsqueda
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchNews(searchQuery);
  };

  // Manejar el cambio de filtro de tiempo
  const handleFilterChange = (freshness: string) => {
    fetchNews(searchQuery, freshness);
  };

  return (
    <div className="news-page">
      <div className="news-page-header">
        <h1>
          {coinName
            ? `Noticias sobre ${coinName}`
            : 'Noticias de Criptomonedas'}
        </h1>

        <form className="news-search-form" onSubmit={handleSearch}>
          <div className="search-input-container">
            <input
              type="text"
              placeholder="Buscar noticias..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="news-search-input"
            />
            <button type="submit" className="news-search-button">
              <i className="fas fa-search"></i>
            </button>
          </div>
        </form>

        <p className="news-page-description">
          Mantente al día con las últimas noticias y tendencias del mundo de las criptomonedas.
          Utiliza el buscador para encontrar información específica sobre cualquier criptomoneda o tema relacionado.
        </p>
      </div>

      <div className="news-page-content">
        <NewsList
          news={news}
          isLoading={isLoading}
          error={error}
          onLoadMore={fetchMoreNews}
          hasMore={true}
          showFilters={true}
          onFilterChange={handleFilterChange}
        />
      </div>
    </div>
  );
};

export default NewsPage;
