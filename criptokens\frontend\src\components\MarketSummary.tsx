import { useState, useEffect } from 'react';
import { getGlobalMarketData } from '../services/api';
import '../styles/MarketSummary.css';

// Datos simulados para desarrollo
const mockMarketData = {
  totalMarketCap: '2.69T',
  totalMarketCapChange: 0.88,
  volume24h: '44.77B',
  volumeChange: 28.48,
  btcDominance: 62.9,
  fearGreedIndex: 32
};

const MarketSummary = () => {
  const [marketData, setMarketData] = useState(mockMarketData);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Cargar datos desde la API de CoinGecko
  useEffect(() => {
    const fetchMarketData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Obtener datos reales de la API
        const data = await getGlobalMarketData();

        // Formatear los datos para nuestro componente
        const formattedData = {
          totalMarketCap: formatLargeNumber(data.data.total_market_cap.usd),
          totalMarketCapChange: data.data.market_cap_change_percentage_24h_usd,
          volume24h: formatLargeNumber(data.data.total_volume.usd),
          volumeChange: calculateVolumeChange(data.data.total_volume.usd, data.data.total_market_cap.usd),
          btcDominance: parseFloat(data.data.market_cap_percentage.btc.toFixed(1)),
          fearGreedIndex: calculateFearGreedIndex(data.data.market_cap_change_percentage_24h_usd)
        };

        setMarketData(formattedData);
      } catch (err) {
        console.error('Error al cargar datos del mercado:', err);
        setError('No se pudieron cargar los datos del mercado. Usando datos simulados.');
        // Fallback a datos simulados en caso de error
        setMarketData(mockMarketData);
      } finally {
        setIsLoading(false);
      }
    };

    // Función para formatear números grandes
    const formatLargeNumber = (value: number): string => {
      if (value >= 1e12) return (value / 1e12).toFixed(2) + 'T';
      if (value >= 1e9) return (value / 1e9).toFixed(2) + 'B';
      if (value >= 1e6) return (value / 1e6).toFixed(2) + 'M';
      if (value >= 1e3) return (value / 1e3).toFixed(2) + 'K';
      return value.toString();
    };

    // Calcular el cambio de volumen (simulado, ya que la API no lo proporciona directamente)
    const calculateVolumeChange = (volume: number, marketCap: number): number => {
      // Relación entre volumen y cap. de mercado como indicador
      const ratio = (volume / marketCap) * 100;
      // Simular un cambio basado en esta relación
      return ratio > 5 ? 15.75 : -5.25;
    };

    // Calcular el índice de miedo y codicia (simulado)
    const calculateFearGreedIndex = (marketCapChange: number): number => {
      // Convertir el cambio de cap. de mercado a un índice entre 0 y 100
      if (marketCapChange <= -10) return 10; // Miedo extremo
      if (marketCapChange <= -5) return 25; // Miedo
      if (marketCapChange <= 0) return 40; // Algo de miedo
      if (marketCapChange <= 5) return 60; // Neutral/Positivo
      if (marketCapChange <= 10) return 75; // Codicia
      return 90; // Codicia extrema
    };

    fetchMarketData();
  }, []);

  if (isLoading) {
    return <div className="market-summary loading">Cargando datos del mercado...</div>;
  }

  if (error) {
    return (
      <div className="market-summary error">
        <h2>Resumen del Mercado</h2>
        <p className="error-message">{error}</p>
      </div>
    );
  }

  return (
    <div className="market-summary">
      <h2>Resumen del Mercado</h2>
      <div className="market-metrics">
        <div className="metric">
          <span className="metric-label">Cap. Total:</span>
          <span className="metric-value">${marketData.totalMarketCap}</span>
          <span className={`metric-change ${marketData.totalMarketCapChange >= 0 ? 'positive' : 'negative'}`}>
            {marketData.totalMarketCapChange >= 0 ? '▲' : '▼'}{Math.abs(marketData.totalMarketCapChange).toFixed(2)}%
          </span>
        </div>

        <div className="metric">
          <span className="metric-label">Vol 24h:</span>
          <span className="metric-value">${marketData.volume24h}</span>
          <span className={`metric-change ${marketData.volumeChange >= 0 ? 'positive' : 'negative'}`}>
            {marketData.volumeChange >= 0 ? '▲' : '▼'}{Math.abs(marketData.volumeChange).toFixed(2)}%
          </span>
        </div>

        <div className="metric">
          <span className="metric-label">Dominio BTC:</span>
          <span className="metric-value">{marketData.btcDominance}%</span>
        </div>

        <div className="metric">
          <span className="metric-label">Índice Miedo:</span>
          <span className="metric-value">{marketData.fearGreedIndex}/100</span>
          <span className={`fear-index-indicator ${
            marketData.fearGreedIndex < 25 ? 'extreme-fear' :
            marketData.fearGreedIndex < 40 ? 'fear' :
            marketData.fearGreedIndex < 60 ? 'neutral' :
            marketData.fearGreedIndex < 75 ? 'greed' : 'extreme-greed'
          }`}></span>
        </div>
      </div>
    </div>
  );
};

export default MarketSummary;
