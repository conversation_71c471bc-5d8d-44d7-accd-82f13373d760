import React from 'react';
import { Routes, Route, Link, useNavigate } from 'react-router-dom';
import '../styles/AcademyPage.css';
import AcademyHome from '../components/academy/AcademyHome';
import AcademyDashboard from '../components/academy/AcademyDashboard';
import CoursesList from '../components/academy/CoursesList';
import CourseViewerEnhanced from '../components/academy/CourseViewerEnhanced';

const AcademyPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="academy-page">
      <header className="academy-header">
        <Link to="/" className="back-link">
          ← Volver al Dashboard
        </Link>
        <h1>Academia Cripto</h1>
        <div className="academy-actions">
          <Link to="" className="nav-link">
            Inicio
          </Link>
          <Link to="dashboard" className="nav-link">
            Mi Aprendizaje
          </Link>
          <Link to="courses" className="nav-link">
            Explorar Cursos
          </Link>
        </div>
      </header>

      <div className="academy-content">
        <Routes>
          <Route path="" element={<AcademyHome />} />
          <Route path="dashboard" element={<AcademyDashboard />} />
          <Route path="courses" element={<CoursesList />} />
          <Route path="courses/:courseId" element={<CourseViewerEnhanced />} />
        </Routes>
      </div>
    </div>
  );
};

export default AcademyPage;
