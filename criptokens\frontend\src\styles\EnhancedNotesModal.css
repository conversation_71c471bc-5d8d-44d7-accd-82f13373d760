.enhanced-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.enhanced-modal-content {
  background-color: #1a1a2e;
  border-radius: 8px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  color: #e6e6e6;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhanced-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #2a2a4a;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #16213e;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.enhanced-modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-price {
  font-size: 0.9rem;
  color: #8a8aaa;
  margin-left: 15px;
}

.enhanced-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #8a8aaa;
  cursor: pointer;
  transition: color 0.2s;
}

.enhanced-modal-close:hover {
  color: #ff6b6b;
}

.enhanced-modal-tabs {
  display: flex;
  background-color: #1e1e30;
  border-bottom: 1px solid #2a2a4a;
}

.tab-button {
  padding: 12px 20px;
  background: none;
  border: none;
  color: #8a8aaa;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
  flex: 1;
  text-align: center;
}

.tab-button:hover {
  background-color: #2a2a4a;
  color: #e6e6e6;
}

.tab-button.active {
  background-color: #0f3460;
  color: #ffffff;
  border-bottom: 2px solid #4361ee;
}

.enhanced-modal-body {
  padding: 20px;
  flex: 1;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #b8b8d4;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #2a2a4a;
  border-radius: 4px;
  background-color: #252547;
  color: #e6e6e6;
  font-size: 0.9rem;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #4361ee;
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.3);
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.enhanced-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #2a2a4a;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #1e1e30;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #4361ee;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #3a56d4;
}

.btn-secondary {
  background-color: transparent;
  color: #b8b8d4;
  border: 1px solid #2a2a4a;
}

.btn-secondary:hover {
  background-color: #2a2a4a;
  color: #e6e6e6;
}

/* Estilos para la pestaña de objetivos de precio */
.price-targets-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.potential-metrics {
  background-color: #252547;
  border-radius: 6px;
  padding: 15px;
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.metric {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 150px;
}

.metric-label {
  font-size: 0.8rem;
  color: #8a8aaa;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.positive {
  color: #4ade80;
}

.negative {
  color: #f87171;
}

/* Estilos para la pestaña de recursos */
.resources-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.add-resource-form {
  display: flex;
  gap: 10px;
}

.resource-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #2a2a4a;
  border-radius: 4px;
  background-color: #252547;
  color: #e6e6e6;
}

.add-resource-button {
  padding: 8px 16px;
  background-color: #4361ee;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-resource-button:disabled {
  background-color: #2a2a4a;
  cursor: not-allowed;
}

.resources-list {
  background-color: #252547;
  border-radius: 6px;
  padding: 15px;
}

.resources-list h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #b8b8d4;
  font-size: 1rem;
}

.resources-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #2a2a4a;
  word-break: break-all;
}

.resource-item:last-child {
  border-bottom: none;
}

.resource-item a {
  color: #4361ee;
  text-decoration: none;
}

.resource-item a:hover {
  text-decoration: underline;
}

.remove-resource-button {
  background: none;
  border: none;
  color: #8a8aaa;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0 5px;
}

.remove-resource-button:hover {
  color: #ff6b6b;
}

.no-resources {
  color: #8a8aaa;
  font-style: italic;
}

small {
  display: block;
  margin-top: 5px;
  color: #8a8aaa;
  font-size: 0.8rem;
}
