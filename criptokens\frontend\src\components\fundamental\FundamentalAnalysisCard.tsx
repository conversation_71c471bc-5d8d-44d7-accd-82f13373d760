import React from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Divider,
  Grid,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import LinkIcon from '@mui/icons-material/Link';
import CodeIcon from '@mui/icons-material/Code';
import DescriptionIcon from '@mui/icons-material/Description';
import TwitterIcon from '@mui/icons-material/Twitter';
import RedditIcon from '@mui/icons-material/Reddit';
import TelegramIcon from '@mui/icons-material/Telegram';
import '../../styles/FundamentalAnalysisCard.css';

interface FundamentalAnalysisCardProps {
  analysis: any;
}

const FundamentalAnalysisCard: React.FC<FundamentalAnalysisCardProps> = ({ analysis }) => {
  if (!analysis) return null;

  const { general, market, tokenomics, onChain, development, social, analysis: swotAnalysis } = analysis;

  // Función para formatear números
  const formatNumber = (num: number | null) => {
    if (num === null || num === undefined) return 'N/A';

    return num.toLocaleString('es-ES');
  };

  // Función para formatear moneda
  const formatCurrency = (num: number | null) => {
    if (num === null || num === undefined) return 'N/A';

    if (num >= 1000000000000) {
      return `$${(num / 1000000000000).toFixed(2)} T`;
    } else if (num >= 1000000000) {
      return `$${(num / 1000000000).toFixed(2)} B`;
    } else if (num >= 1000000) {
      return `$${(num / 1000000).toFixed(2)} M`;
    } else if (num >= 1000) {
      return `$${(num / 1000).toFixed(2)} K`;
    }

    return `$${num.toFixed(2)}`;
  };

  return (
    <Card className="fundamental-analysis-card">
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" component="h2">
            {general.name} ({general.symbol})
          </Typography>
          <Chip
            label={`Rank #${market.marketCapRank || 'N/A'}`}
            color="primary"
            variant="outlined"
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Descripción */}
        <Typography variant="body1" paragraph>
          {general.description}
        </Typography>

        {/* Datos de mercado */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Datos de Mercado</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Capitalización de Mercado</Typography>
                  <Typography variant="h6">{formatCurrency(market.marketCap)}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Volumen (24h)</Typography>
                  <Typography variant="h6">{formatCurrency(market.volume24h)}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Dominancia</Typography>
                  <Typography variant="h6">{market.marketDominance ? `${market.marketDominance.toFixed(2)}%` : 'N/A'}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Suministro Circulante</Typography>
                  <Typography variant="h6">{formatNumber(market.circulatingSupply)} {general.symbol}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Suministro Total</Typography>
                  <Typography variant="h6">{formatNumber(market.totalSupply)} {general.symbol}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Suministro Máximo</Typography>
                  <Typography variant="h6">{market.maxSupply ? `${formatNumber(market.maxSupply)} ${general.symbol}` : 'Ilimitado'}</Typography>
                </Box>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Tokenomics */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Tokenomics</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Porcentaje en Circulación</Typography>
                  <Typography variant="h6">{tokenomics.circulatingSupplyPercent ? `${tokenomics.circulatingSupplyPercent}%` : 'N/A'}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Tasa de Inflación</Typography>
                  <Typography variant="h6">{tokenomics.inflation || 'N/A'}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Tipo de Token</Typography>
                  <Typography variant="h6">{tokenomics.tokenType}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box className="data-item">
                  <Typography variant="subtitle2" color="textSecondary">Estándar</Typography>
                  <Typography variant="h6">{tokenomics.tokenStandard}</Typography>
                </Box>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Datos On-Chain */}
        {onChain && (onChain.activeAddresses || onChain.transactions24h) && (
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Datos On-Chain</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                {onChain.activeAddresses && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Direcciones Activas</Typography>
                      <Typography variant="h6">{formatNumber(onChain.activeAddresses)}</Typography>
                    </Box>
                  </Grid>
                )}
                {onChain.transactions24h && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Transacciones (24h)</Typography>
                      <Typography variant="h6">{formatNumber(onChain.transactions24h)}</Typography>
                    </Box>
                  </Grid>
                )}
                {onChain.averageFee && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Comisión Promedio</Typography>
                      <Typography variant="h6">{onChain.averageFee}</Typography>
                    </Box>
                  </Grid>
                )}
                {onChain.hashRate && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Hash Rate</Typography>
                      <Typography variant="h6">{onChain.hashRate}</Typography>
                    </Box>
                  </Grid>
                )}
                {onChain.totalValueLocked && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Valor Total Bloqueado (TVL)</Typography>
                      <Typography variant="h6">{onChain.totalValueLocked}</Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Desarrollo */}
        {development && (development.githubStars || development.githubCommits) && (
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Desarrollo</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                {development.githubStars && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Estrellas en GitHub</Typography>
                      <Typography variant="h6">{formatNumber(development.githubStars)}</Typography>
                    </Box>
                  </Grid>
                )}
                {development.githubForks && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Forks en GitHub</Typography>
                      <Typography variant="h6">{formatNumber(development.githubForks)}</Typography>
                    </Box>
                  </Grid>
                )}
                {development.githubContributors && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Contribuidores</Typography>
                      <Typography variant="h6">{formatNumber(development.githubContributors)}</Typography>
                    </Box>
                  </Grid>
                )}
                {development.githubCommits && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Commits</Typography>
                      <Typography variant="h6">{formatNumber(development.githubCommits)}</Typography>
                    </Box>
                  </Grid>
                )}
                {development.lastUpdate && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box className="data-item">
                      <Typography variant="subtitle2" color="textSecondary">Última Actualización</Typography>
                      <Typography variant="h6">{new Date(development.lastUpdate).toLocaleDateString()}</Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Enlaces */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Enlaces</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {general.website && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box className="link-item">
                    <LinkIcon color="primary" />
                    <a href={general.website} target="_blank" rel="noopener noreferrer">
                      Sitio Web
                    </a>
                  </Box>
                </Grid>
              )}
              {general.whitepaper && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box className="link-item">
                    <DescriptionIcon color="primary" />
                    <a href={general.whitepaper} target="_blank" rel="noopener noreferrer">
                      Whitepaper
                    </a>
                  </Box>
                </Grid>
              )}
              {general.sourceCode && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box className="link-item">
                    <CodeIcon color="primary" />
                    <a href={general.sourceCode} target="_blank" rel="noopener noreferrer">
                      Código Fuente
                    </a>
                  </Box>
                </Grid>
              )}
              {social.twitter && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box className="link-item">
                    <TwitterIcon color="primary" />
                    <a href={social.twitter} target="_blank" rel="noopener noreferrer">
                      Twitter
                    </a>
                  </Box>
                </Grid>
              )}
              {social.reddit && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box className="link-item">
                    <RedditIcon color="primary" />
                    <a href={social.reddit} target="_blank" rel="noopener noreferrer">
                      Reddit
                    </a>
                  </Box>
                </Grid>
              )}
              {social.telegram && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box className="link-item">
                    <TelegramIcon color="primary" />
                    <a href={social.telegram} target="_blank" rel="noopener noreferrer">
                      Telegram
                    </a>
                  </Box>
                </Grid>
              )}
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Análisis SWOT */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Análisis SWOT</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" color="primary" gutterBottom>
                  Fortalezas
                </Typography>
                <List dense>
                  {swotAnalysis.strengths.map((strength: string, index: number) => (
                    <ListItem key={`strength-${index}`}>
                      <ListItemIcon>
                        <CheckCircleIcon color="success" />
                      </ListItemIcon>
                      <ListItemText primary={strength} />
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" color="error" gutterBottom>
                  Debilidades
                </Typography>
                <List dense>
                  {swotAnalysis.weaknesses.map((weakness: string, index: number) => (
                    <ListItem key={`weakness-${index}`}>
                      <ListItemIcon>
                        <CancelIcon color="error" />
                      </ListItemIcon>
                      <ListItemText primary={weakness} />
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" color="success" gutterBottom>
                  Oportunidades
                </Typography>
                <List dense>
                  {swotAnalysis.opportunities.map((opportunity: string, index: number) => (
                    <ListItem key={`opportunity-${index}`}>
                      <ListItemIcon>
                        <LightbulbIcon color="warning" />
                      </ListItemIcon>
                      <ListItemText primary={opportunity} />
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" color="warning" gutterBottom>
                  Amenazas
                </Typography>
                <List dense>
                  {swotAnalysis.threats.map((threat: string, index: number) => (
                    <ListItem key={`threat-${index}`}>
                      <ListItemIcon>
                        <WarningIcon color="warning" />
                      </ListItemIcon>
                      <ListItemText primary={threat} />
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Resumen */}
        <Box mt={3}>
          <Typography variant="subtitle1" gutterBottom>
            Resumen
          </Typography>
          <Typography variant="body2">
            {swotAnalysis.summary}
          </Typography>
        </Box>

        {/* Disclaimer */}
        <Box mt={2} p={2} bgcolor="rgba(0, 0, 0, 0.05)" borderRadius={1}>
          <Typography variant="caption" display="block" color="textSecondary">
            <InfoIcon fontSize="small" style={{ verticalAlign: 'middle', marginRight: 4 }} />
            Este análisis es solo informativo y no constituye asesoramiento financiero. Los mercados de criptomonedas son altamente volátiles y conllevan riesgos significativos. Siempre realiza tu propia investigación antes de tomar decisiones de inversión.
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default FundamentalAnalysisCard;
