/* Estilos para la tabla de criptomonedas */

.coin-table-container {
  width: 100%;
  overflow: hidden;
}

.table-wrapper {
  width: 100%;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-transparent) transparent;
}

.table-wrapper::-webkit-scrollbar {
  height: 6px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background-color: var(--color-primary-transparent);
  border-radius: 6px;
}

.coin-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 0.875rem;
  box-shadow: var(--shadow-sm);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.coin-table thead th {
  position: sticky;
  top: 0;
  background-color: var(--color-surface-dark);
  padding: 0.85rem 1rem;
  text-align: left;
  color: var(--text-secondary);
  font-weight: var(--font-weight-semibold);
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.coin-table thead th:hover {
  color: var(--text-primary);
  background-color: var(--color-surface-light);
}

.coin-table thead th.ascending::after {
  content: ' ↑';
  color: var(--color-primary);
  font-size: 0.9rem;
  margin-left: 4px;
}

.coin-table thead th.descending::after {
  content: ' ↓';
  color: var(--color-primary);
  font-size: 0.9rem;
  margin-left: 4px;
}

.coin-table tbody td {
  padding: 0.85rem 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  transition: all 0.2s ease;
  background-color: var(--color-surface);
}

.coin-row {
  cursor: pointer;
  transition: all 0.2s ease;
}

.coin-row:hover {
  background-color: var(--color-surface-light);
}

.coin-row:hover td {
  border-bottom-color: var(--border-color-hover);
}

.coin-row:last-child td {
  border-bottom: none;
}

/* Celdas específicas */
.rank-cell {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-align: center;
  min-width: 40px;
}

.name-cell {
  min-width: 200px;
}

.coin-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.coin-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: contain;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.coin-name-container {
  display: flex;
  flex-direction: column;
}

.coin-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.coin-symbol {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.price-cell {
  font-weight: var(--font-weight-semibold);
  min-width: 100px;
}

.change-cell {
  font-weight: var(--font-weight-semibold);
  min-width: 80px;
}

.change-cell.positive {
  color: var(--color-positive);
}

.change-cell.negative {
  color: var(--color-negative);
}

.market-cap-cell, .volume-cell {
  min-width: 120px;
  color: var(--text-secondary);
}

.sparkline-cell {
  min-width: 100px;
  padding: 0.25rem 0.5rem;
}

.sparkline {
  width: 100%;
  height: 30px;
}

.no-sparkline {
  color: var(--text-tertiary);
  font-size: 0.75rem;
  text-align: center;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.actions-cell {
  min-width: 70px;
  text-align: center;
}

.watchlist-button {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.4rem;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.watchlist-button:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-transparent);
  transform: scale(1.08);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.watchlist-button.active {
  color: var(--color-primary);
  background: var(--color-primary-transparent);
  border-color: var(--color-primary);
}

/* Esqueleto de carga */
.skeleton-row td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.skeleton-cell {
  background-color: var(--color-surface-light);
  border-radius: 4px;
  height: 1rem;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-cell.rank {
  width: 20px;
  margin: 0 auto;
}

.skeleton-cell.name {
  width: 150px;
  height: 1.5rem;
}

.skeleton-cell.price, .skeleton-cell.change, .skeleton-cell.market-cap, .skeleton-cell.volume {
  width: 80px;
}

.skeleton-cell.sparkline {
  width: 100px;
  height: 30px;
}

.skeleton-cell.actions {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin: 0 auto;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* Estilos responsivos */
@media (max-width: 992px) {
  .coin-table thead th,
  .coin-table tbody td {
    padding: 0.625rem 0.75rem;
  }

  .name-cell {
    min-width: 180px;
  }

  .price-cell, .change-cell {
    min-width: 80px;
  }

  .market-cap-cell, .volume-cell {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .coin-table {
    font-size: 0.75rem;
  }

  .coin-table thead th,
  .coin-table tbody td {
    padding: 0.5rem 0.625rem;
  }

  .coin-icon {
    width: 20px;
    height: 20px;
  }

  .name-cell {
    min-width: 150px;
  }

  .market-cap-cell, .volume-cell {
    min-width: 80px;
  }

  .sparkline-cell {
    display: none;
  }

  .watchlist-button {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
    padding: 0.3rem;
  }

  .actions-cell {
    min-width: 50px;
  }
}

@media (max-width: 576px) {
  .coin-table thead th,
  .coin-table tbody td {
    padding: 0.375rem 0.5rem;
  }

  .coin-info {
    gap: 0.5rem;
  }

  .name-cell {
    min-width: 120px;
  }

  .market-cap-cell {
    display: none;
  }

  .volume-cell {
    display: none;
  }
}
