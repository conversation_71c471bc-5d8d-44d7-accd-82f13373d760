import React from 'react';
import { useNavigate } from 'react-router-dom';

interface CustomAlertsWidgetProps {
  isLoading: boolean;
  compact?: boolean;
}

interface AlertItem {
  id: string;
  cryptoId: string;
  cryptoName: string;
  cryptoSymbol: string;
  type: 'price' | 'volume' | 'marketcap' | 'news';
  condition: 'above' | 'below' | 'percent_change';
  value: number;
  triggered: boolean;
  createdAt: string;
}

const CustomAlertsWidget: React.FC<CustomAlertsWidgetProps> = ({ 
  isLoading, 
  compact = false 
}) => {
  const navigate = useNavigate();
  
  // Datos simulados para alertas
  const mockAlerts: AlertItem[] = [
    {
      id: '1',
      cryptoId: 'bitcoin',
      cryptoName: 'Bitcoin',
      cryptoSymbol: 'BTC',
      type: 'price',
      condition: 'above',
      value: 60000,
      triggered: false,
      createdAt: '2023-10-20T14:30:00Z'
    },
    {
      id: '2',
      cryptoId: 'ethereum',
      cryptoName: 'Ethereum',
      cryptoSymbol: 'ETH',
      type: 'price',
      condition: 'below',
      value: 3000,
      triggered: false,
      createdAt: '2023-10-21T10:15:00Z'
    },
    {
      id: '3',
      cryptoId: 'solana',
      cryptoName: 'Solana',
      cryptoSymbol: 'SOL',
      type: 'percent_change',
      condition: 'above',
      value: 10,
      triggered: true,
      createdAt: '2023-10-22T08:45:00Z'
    }
  ];
  
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'price':
        return <i className="fas fa-dollar-sign"></i>;
      case 'volume':
        return <i className="fas fa-chart-line"></i>;
      case 'marketcap':
        return <i className="fas fa-chart-pie"></i>;
      case 'news':
        return <i className="fas fa-newspaper"></i>;
      default:
        return <i className="fas fa-bell"></i>;
    }
  };
  
  const getAlertDescription = (alert: AlertItem) => {
    switch (alert.type) {
      case 'price':
        return `${alert.cryptoSymbol} ${alert.condition === 'above' ? '>' : '<'} $${alert.value.toLocaleString()}`;
      case 'percent_change':
        return `${alert.cryptoSymbol} ${alert.condition === 'above' ? '↑' : '↓'} ${alert.value}% en 24h`;
      default:
        return `Alerta para ${alert.cryptoSymbol}`;
    }
  };

  if (isLoading) {
    return (
      <div className="widget-loading-state">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Cargando alertas...</p>
      </div>
    );
  }

  if (mockAlerts.length === 0) {
    return (
      <div className="widget-empty-state">
        <p>No tienes alertas configuradas</p>
        <button 
          className="create-alert-button"
          onClick={() => navigate('/alerts/create')}
        >
          Crear alerta
        </button>
      </div>
    );
  }

  return (
    <div className="alerts-list">
      {mockAlerts.map(alert => (
        <div 
          key={alert.id} 
          className={`alert-item ${alert.triggered ? 'triggered' : ''}`}
        >
          <div className="alert-icon">
            {getAlertIcon(alert.type)}
          </div>
          <div className="alert-content">
            <div className="alert-crypto">{alert.cryptoName}</div>
            <div className="alert-description">
              {getAlertDescription(alert)}
            </div>
          </div>
          {!compact && (
            <div className="alert-actions">
              <button className="alert-action-button" title="Editar">
                <i className="fas fa-edit"></i>
              </button>
              <button className="alert-action-button" title="Eliminar">
                <i className="fas fa-trash"></i>
              </button>
            </div>
          )}
        </div>
      ))}
      
      {!compact && (
        <div className="alert-controls">
          <button 
            className="create-alert-button"
            onClick={() => navigate('/alerts/create')}
          >
            <i className="fas fa-plus"></i> Nueva alerta
          </button>
        </div>
      )}
    </div>
  );
};

export default CustomAlertsWidget;
