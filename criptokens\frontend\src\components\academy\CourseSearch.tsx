import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../../styles/academy/CourseSearch.css';

interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
  duration: string;
  thumbnail?: string;
  instructor: {
    name: string;
    avatar: string;
  };
  tags: string[];
}

interface CourseSearchProps {
  allCourses: Course[];
}

const CourseSearch: React.FC<CourseSearchProps> = ({ allCourses }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [filteredCourses, setFilteredCourses] = useState<Course[]>(allCourses);
  const [availableTags, setAvailableTags] = useState<string[]>([]);

  // Extraer todas las etiquetas únicas de los cursos
  useEffect(() => {
    const tags = new Set<string>();
    allCourses.forEach(course => {
      course.tags.forEach(tag => tags.add(tag));
    });
    setAvailableTags(Array.from(tags).sort());
  }, [allCourses]);

  // Filtrar cursos cuando cambian los criterios de búsqueda
  useEffect(() => {
    const filtered = allCourses.filter(course => {
      // Filtrar por término de búsqueda
      const matchesSearchTerm = 
        searchTerm === '' || 
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Filtrar por nivel
      const matchesLevel = 
        selectedLevel === 'all' || 
        course.level === selectedLevel;
      
      // Filtrar por etiquetas
      const matchesTags = 
        selectedTags.length === 0 || 
        selectedTags.every(tag => course.tags.includes(tag));
      
      return matchesSearchTerm && matchesLevel && matchesTags;
    });
    
    setFilteredCourses(filtered);
  }, [searchTerm, selectedLevel, selectedTags, allCourses]);

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag) 
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedLevel('all');
    setSelectedTags([]);
  };

  return (
    <div className="course-search-container">
      <div className="search-filters">
        <div className="search-input-container">
          <input
            type="text"
            placeholder="Buscar cursos..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <i className="fas fa-search search-icon"></i>
        </div>
        
        <div className="filter-section">
          <h3>Nivel</h3>
          <div className="level-filters">
            <button 
              className={`level-filter ${selectedLevel === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedLevel('all')}
            >
              Todos
            </button>
            <button 
              className={`level-filter ${selectedLevel === 'beginner' ? 'active' : ''}`}
              onClick={() => setSelectedLevel('beginner')}
            >
              Principiante
            </button>
            <button 
              className={`level-filter ${selectedLevel === 'intermediate' ? 'active' : ''}`}
              onClick={() => setSelectedLevel('intermediate')}
            >
              Intermedio
            </button>
            <button 
              className={`level-filter ${selectedLevel === 'advanced' ? 'active' : ''}`}
              onClick={() => setSelectedLevel('advanced')}
            >
              Avanzado
            </button>
          </div>
        </div>
        
        <div className="filter-section">
          <h3>Temas</h3>
          <div className="tag-filters">
            {availableTags.map(tag => (
              <button 
                key={tag}
                className={`tag-filter ${selectedTags.includes(tag) ? 'active' : ''}`}
                onClick={() => handleTagToggle(tag)}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
        
        <button className="clear-filters" onClick={clearFilters}>
          Limpiar filtros
        </button>
      </div>
      
      <div className="search-results">
        <div className="results-header">
          <h2>Resultados ({filteredCourses.length})</h2>
          {filteredCourses.length === 0 && searchTerm !== '' && (
            <p className="no-results">No se encontraron cursos que coincidan con tu búsqueda.</p>
          )}
        </div>
        
        <div className="courses-grid">
          {filteredCourses.map(course => (
            <div key={course.id} className="course-card">
              <div className="course-image">
                {course.thumbnail ? (
                  <img src={course.thumbnail} alt={course.title} />
                ) : (
                  <div className="placeholder-image"></div>
                )}
                <div className={`course-level ${course.level}`}>
                  {course.level === 'beginner' && 'Principiante'}
                  {course.level === 'intermediate' && 'Intermedio'}
                  {course.level === 'advanced' && 'Avanzado'}
                </div>
              </div>
              <div className="course-content">
                <h3>{course.title}</h3>
                <p className="course-description">{course.description}</p>
                <div className="course-meta">
                  <span className="course-duration">
                    <i className="fas fa-clock"></i> {course.duration}
                  </span>
                </div>
                <div className="course-tags">
                  {course.tags.map(tag => (
                    <span key={tag} className="course-tag">{tag}</span>
                  ))}
                </div>
                <div className="course-instructor">
                  <img src={course.instructor.avatar} alt={course.instructor.name} />
                  <span>{course.instructor.name}</span>
                </div>
                <Link to={`../courses/${course.id}`} className="view-course-button">
                  Ver Curso
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CourseSearch;
