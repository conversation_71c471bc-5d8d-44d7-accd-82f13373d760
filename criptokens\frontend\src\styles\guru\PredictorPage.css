.predictor-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--bg-gradient);
  color: var(--text-bright);
}

.predictor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.predictor-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.back-link {
  display: flex;
  align-items: center;
  color: var(--text-medium);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--text-bright);
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.guru-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-medium);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.guru-link:hover {
  background: rgba(0, 0, 0, 0.4);
  color: var(--text-bright);
  border-color: rgba(255, 255, 255, 0.2);
}

.predictor-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.predictor-footer {
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.predictor-footer p {
  font-size: 0.85rem;
  color: var(--text-dim);
  margin: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .predictor-header {
    padding: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .predictor-header h1 {
    font-size: 1.25rem;
    order: -1;
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .predictor-content {
    padding: 1rem;
  }
  
  .predictor-footer {
    padding: 1rem;
  }
}
