/* Estilos para las criptomonedas en tendencia */

.trending-coins {
  width: 100%;
}

.trending-coins-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  width: 100%;
}

.trending-coin-card {
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  cursor: pointer;
}

.trending-coin-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
  background-color: var(--color-surface-light);
}

.coin-rank {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--color-primary-transparent);
  color: var(--color-primary);
  font-size: 0.75rem;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-icon-container {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.coin-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.coin-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
}

.coin-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.coin-symbol {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.coin-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
  min-width: 80px;
}

.price-value {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.price-change {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.price-change.positive {
  color: var(--color-positive);
}

.price-change.negative {
  color: var(--color-negative);
}

/* Esqueleto de carga */
.trending-coin-card.skeleton {
  cursor: default;
}

.trending-coin-card.skeleton:hover {
  transform: none;
  box-shadow: none;
  border-color: var(--border-color);
  background-color: var(--color-surface-dark);
}

.skeleton-rank {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--color-surface-light);
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--color-surface-light);
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-name {
  width: 80px;
  height: 0.875rem;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-symbol {
  width: 40px;
  height: 0.75rem;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  margin-top: 0.25rem;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-price {
  width: 60px;
  height: 0.875rem;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-change {
  width: 40px;
  height: 0.75rem;
  background-color: var(--color-surface-light);
  border-radius: 4px;
  margin-top: 0.25rem;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* Estilos responsivos */
@media (max-width: 992px) {
  .trending-coins-container {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .trending-coins-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .trending-coin-card {
    padding: 0.75rem;
  }
  
  .coin-icon-container {
    width: 28px;
    height: 28px;
  }
  
  .coin-price {
    min-width: 60px;
  }
}

@media (max-width: 576px) {
  .trending-coins-container {
    grid-template-columns: 1fr 1fr;
  }
}
