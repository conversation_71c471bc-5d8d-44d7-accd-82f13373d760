/**
 * Herramientas MCP relacionadas con contratos inteligentes
 */
const etherscanService = require('../services/etherscan');
const config = require('../config');

module.exports = {
  /**
   * Obtiene eventos de un contrato inteligente
   */
  getContractEvents: {
    description: 'Get events from a smart contract',
    parameters: {
      type: 'object',
      properties: {
        contractAddress: {
          type: 'string',
          description: 'The contract address'
        },
        topic0: {
          type: 'string',
          description: 'Event signature (optional)',
          default: null
        },
        page: {
          type: 'integer',
          description: 'Page number',
          default: 1
        },
        offset: {
          type: 'integer',
          description: 'Number of events per page',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['contractAddress']
    },
    handler: async ({ contractAddress, topic0 = null, page = 1, offset = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getContractEvents(contractAddress, topic0, page, offset);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene información sobre un token ERC-20
   */
  getTokenInfo: {
    description: 'Get information about an ERC-20 token',
    parameters: {
      type: 'object',
      properties: {
        tokenAddress: {
          type: 'string',
          description: 'The token contract address'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['tokenAddress']
    },
    handler: async ({ tokenAddress, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getTokenInfo(tokenAddress);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene información sobre un token de criptomoneda por su nombre
   */
  getCryptoTokenInfo: {
    description: 'Get information about a cryptocurrency token by name',
    parameters: {
      type: 'object',
      properties: {
        cryptoId: {
          type: 'string',
          description: 'The cryptocurrency ID (bitcoin, ethereum, etc.)'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['cryptoId']
    },
    handler: async ({ cryptoId, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener la dirección del token para la criptomoneda
        const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
        
        if (!tokenAddress) {
          throw new Error(`Token address not found for ${cryptoId}`);
        }
        
        const tokenInfo = await etherscanService.getTokenInfo(tokenAddress);
        
        return {
          ...tokenInfo,
          cryptoId
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene información sobre múltiples tokens de criptomonedas
   */
  getMultipleCryptoTokenInfo: {
    description: 'Get information about multiple cryptocurrency tokens',
    parameters: {
      type: 'object',
      properties: {
        cryptoIds: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: 'List of cryptocurrency IDs (bitcoin, ethereum, etc.)'
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['cryptoIds']
    },
    handler: async ({ cryptoIds, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        const tokensInfo = [];
        
        for (const cryptoId of cryptoIds) {
          try {
            // Obtener la dirección del token para la criptomoneda
            const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
            
            if (!tokenAddress) {
              tokensInfo.push({
                cryptoId,
                error: `Token address not found for ${cryptoId}`
              });
              continue;
            }
            
            const tokenInfo = await etherscanService.getTokenInfo(tokenAddress);
            
            tokensInfo.push({
              ...tokenInfo,
              cryptoId
            });
          } catch (error) {
            console.error(`Error fetching token info for ${cryptoId}:`, error);
            tokensInfo.push({
              cryptoId,
              error: error.message
            });
          }
        }
        
        return {
          tokens: tokensInfo,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  }
};
