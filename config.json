{"system": {"defaultDelay": 2000, "maxRetries": 20, "retryInterval": 2000, "logLevel": "debug"}, "python": {"venvPath": {"windows": "./adk_agents/venv/Scripts/python.exe", "unix": "./adk_agents/venv/bin/python"}, "fallback": "python", "comment": "NOTA: Verifica que estas rutas sean correctas para tu entorno. Si el venv está en otra ubicación, actualiza estas rutas."}, "components": [{"id": "crypto-mcp", "name": "MCP Crypto Server", "type": "node", "cmd": "node", "args": ["index.js"], "cwd": "./crypto-mcp-server", "port": 3101, "delay": 0, "dependencies": [], "healthCheck": {"type": "tcp", "timeout": 10000}}, {"id": "brave-mcp", "name": "MCP Brave Server", "type": "node", "cmd": "node", "args": ["brave-search-server.js"], "cwd": "./", "port": 3102, "delay": 2000, "dependencies": [], "healthCheck": {"type": "tcp", "timeout": 10000}}, {"id": "playwright-mcp", "name": "MCP Playwright Server", "type": "node", "cmd": "node", "args": ["dist/server.js"], "cwd": "./playwright-mcp-server", "port": 3103, "delay": 2000, "dependencies": [], "healthCheck": {"type": "tcp", "timeout": 10000}}, {"id": "context7-mcp", "name": "Context7 MCP Server", "type": "npx", "cmd": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "cwd": "./", "port": 7777, "delay": 2000, "dependencies": [], "healthCheck": {"type": "tcp", "timeout": 10000}}, {"id": "onchain-mcp", "name": "OnChain MCP Server", "type": "node", "cmd": "node", "args": ["index.js"], "cwd": "./onchain-mcp-server", "port": 3104, "delay": 2000, "dependencies": [], "healthCheck": {"type": "tcp", "timeout": 10000}}, {"id": "backend", "name": "Backend", "type": "node", "cmd": "${npm}", "args": ["start"], "cwd": "./backend", "port": 3001, "delay": 5000, "dependencies": ["crypto-mcp", "brave-mcp", "playwright-mcp", "context7-mcp", "onchain-mcp"], "healthCheck": {"type": "http", "url": "http://localhost:3001/api/health", "timeout": 15000}}, {"id": "adk-agents", "name": "ADK Agents", "type": "python", "cmd": "${python}", "args": ["start_adk_agents.py"], "cwd": "./", "port": 8000, "delay": 2000, "dependencies": ["crypto-mcp", "brave-mcp", "playwright-mcp", "context7-mcp", "onchain-mcp"], "healthCheck": {"type": "tcp", "timeout": 15000}}, {"id": "guru-orchestrator", "name": "Guru Orchestrator", "type": "python", "cmd": "${python}", "args": ["start_guru_orchestrator.py"], "cwd": "./", "port": null, "delay": 2000, "dependencies": ["adk-agents", "backend"], "healthCheck": {"type": "none", "timeout": 5000}}, {"id": "frontend", "name": "Frontend", "type": "node", "cmd": "${npm}", "args": ["run", "start"], "cwd": "./frontend", "port": 5173, "delay": 2000, "dependencies": ["backend", "adk-agents"], "healthCheck": {"type": "http", "url": "http://localhost:5173", "timeout": 15000}}]}