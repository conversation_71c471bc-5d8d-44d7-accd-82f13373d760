/**
 * Script para probar el inicio y detención de un componente específico
 */
const { spawn } = require('child_process');
const { loadConfig } = require('./config-loader');
const { waitForService } = require('./health-checker');

async function testComponent(componentId) {
  // Cargar la configuración
  const config = loadConfig('./config.json');
  
  // Buscar el componente
  const component = config.components.find(c => c.id === componentId);
  
  if (!component) {
    console.error(`Error: Componente ${componentId} no encontrado.`);
    process.exit(1);
  }
  
  console.log(`Probando componente: ${component.name}`);
  
  // Iniciar el componente
  console.log(`\nIniciando ${component.name}...`);
  
  const proc = spawn(component.cmd, component.args, {
    cwd: component.cwd,
    stdio: 'pipe',
    shell: true
  });
  
  proc.stdout.on('data', (data) => {
    console.log(`[${component.name}] ${data.toString().trim()}`);
  });
  
  proc.stderr.on('data', (data) => {
    console.error(`[${component.name} ERROR] ${data.toString().trim()}`);
  });
  
  // Esperar a que el componente esté disponible
  console.log(`\nEsperando a que ${component.name} esté disponible...`);
  
  const available = await waitForService(component, 10, 1000);
  
  if (available) {
    console.log(`${component.name} está disponible.`);
  } else {
    console.error(`Error: ${component.name} no está disponible después de 10 intentos.`);
  }
  
  // Esperar 10 segundos y luego detener el componente
  console.log(`\nEsperando 10 segundos antes de detener ${component.name}...`);
  
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  console.log(`\nDeteniendo ${component.name}...`);
  proc.kill();
  
  console.log(`${component.name} detenido.`);
}

// Verificar argumentos
if (process.argv.length < 3) {
  console.error('Error: Debe especificar un ID de componente.');
  console.error('Uso: node test-component.js <componentId>');
  process.exit(1);
}

const componentId = process.argv[2];

testComponent(componentId).catch(err => {
  console.error('Error en la prueba del componente:', err);
  process.exit(1);
});
