.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(5px);
}

.add-funds-modal {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: var(--border-light);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 450px;
  padding: var(--space-lg);
  animation: modalFadeIn 0.3s ease;
  position: relative;
  overflow: hidden;
}

.add-funds-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  z-index: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-sm);
  border-bottom: var(--border-light);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-dim);
  font-size: 1.5rem;
  cursor: pointer;
  transition: color var(--transition-fast);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-button:hover {
  color: var(--text-bright);
  background-color: rgba(255, 255, 255, 0.1);
}

.form-group {
  margin-bottom: var(--space-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-xs);
  color: var(--text-medium);
  font-weight: 500;
  font-size: 0.9rem;
}

.amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.amount-input-container::before {
  content: '$';
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-medium);
  font-weight: 500;
}

.add-funds-modal input[type="number"] {
  width: 100%;
  padding: 12px 12px 12px 30px;
  background-color: rgba(0, 0, 0, 0.2);
  border: var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-bright);
  font-size: 1rem;
  transition: all var(--transition-fast);
}

.add-funds-modal input[type="number"]:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 242, 255, 0.2);
}

.add-funds-modal select {
  width: 100%;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.2);
  border: var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-bright);
  font-size: 1rem;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23a0a0d0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  transition: all var(--transition-fast);
}

.add-funds-modal select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 242, 255, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
  margin-top: var(--space-xl);
}

.cancel-button {
  padding: 10px 20px;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  color: var(--text-medium);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-bright);
}

.submit-button {
  padding: 10px 20px;
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-bright);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  background-color: rgba(255, 82, 82, 0.1);
  color: #ff5252;
  padding: 10px;
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
  font-size: 0.9rem;
  border-left: 3px solid #ff5252;
}

.success-message {
  background-color: rgba(0, 200, 83, 0.1);
  color: #00c853;
  padding: 10px;
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
  font-size: 0.9rem;
  border-left: 3px solid #00c853;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
