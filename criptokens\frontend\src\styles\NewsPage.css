.news-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.news-page-header {
  margin-bottom: 32px;
}

.news-page h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.news-page-description {
  font-size: 1.1rem;
  color: var(--text-secondary-color);
  margin-bottom: 2rem;
  max-width: 800px;
  line-height: 1.6;
}

.news-search-form {
  margin-bottom: 24px;
}

.search-input-container {
  display: flex;
  max-width: 600px;
  position: relative;
}

.news-search-input {
  flex: 1;
  padding: 12px 16px;
  padding-right: 50px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background-color: var(--input-bg-color);
  color: var(--text-color);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.news-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.2);
}

.news-search-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: var(--text-secondary-color);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: color 0.2s ease, background-color 0.2s ease;
}

.news-search-button:hover {
  color: var(--primary-color);
  background-color: rgba(0, 132, 255, 0.1);
}

.news-page-content {
  margin-bottom: 48px;
}
  line-height: 1.6;
}

@media (max-width: 768px) {
  .news-page-content {
    padding: 1.5rem 1rem;
  }

  .news-page-content h1 {
    font-size: 1.75rem;
  }

  .news-page-description {
    font-size: 0.9375rem;
  }
}
