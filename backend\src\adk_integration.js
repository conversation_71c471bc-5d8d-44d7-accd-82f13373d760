/**
 * ADK Integration Module for Criptokens Backend
 *
 * This module provides functions to interact with the ADK agents
 * through the ADK API server.
 */

const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const config = require('../../config');

// ADK API server URL
const ADK_API_URL = process.env.ADK_API_URL || 'http://localhost:8002/api/v1';

/**
 * Start the ADK API server
 * @returns {Promise<Object>} Process information
 */
async function startAdkApiServer() {
    return new Promise((resolve, reject) => {
        console.log('Starting ADK API server...');

        // Check if ADK is installed
        try {
            const adkProcess = spawn('python', ['run_adk_api.py'], {
                cwd: path.join(__dirname, '../../'),
                detached: true,
                stdio: 'pipe'
            });

            let output = '';

            adkProcess.stdout.on('data', (data) => {
                output += data.toString();
                console.log(`[ADK] ${data.toString().trim()}`);
            });

            adkProcess.stderr.on('data', (data) => {
                console.error(`[ADK ERROR] ${data.toString().trim()}`);
            });

            // Wait for server to start
            setTimeout(() => {
                console.log('ADK API server started');
                resolve({
                    process: adkProcess,
                    pid: adkProcess.pid,
                    output
                });
            }, 5000);

            adkProcess.on('error', (err) => {
                console.error('Failed to start ADK API server:', err);
                reject(err);
            });
        } catch (error) {
            console.error('Error starting ADK API server:', error);
            reject(error);
        }
    });
}

/**
 * Query the Guru Cripto agent
 * @param {string} question - The user's question
 * @returns {Promise<Object>} The agent's response
 */
async function queryGuruAgent(question) {
    try {
        const response = await axios.post(`${ADK_API_URL}/guru`, {
            question
        });

        return response.data;
    } catch (error) {
        console.error('Error querying Guru agent:', error);
        throw error;
    }
}

/**
 * Query the Technical Analysis agent
 * @param {string} question - The user's question
 * @returns {Promise<Object>} The agent's response
 */
async function queryTechnicalAgent(question) {
    try {
        const response = await axios.post(`${ADK_API_URL}/technical`, {
            question
        });

        return response.data;
    } catch (error) {
        console.error('Error querying Technical agent:', error);
        throw error;
    }
}

/**
 * Query the Sentiment Analysis agent
 * @param {string} question - The user's question
 * @returns {Promise<Object>} The agent's response
 */
async function querySentimentAgent(question) {
    try {
        const response = await axios.post(`${ADK_API_URL}/sentiment`, {
            question
        });

        return response.data;
    } catch (error) {
        console.error('Error querying Sentiment agent:', error);
        throw error;
    }
}

/**
 * Query the On-Chain Analysis agent
 * @param {string} question - The user's question
 * @returns {Promise<Object>} The agent's response
 */
async function queryOnchainAgent(question) {
    try {
        const response = await axios.post(`${ADK_API_URL}/onchain`, {
            question
        });

        return response.data;
    } catch (error) {
        console.error('Error querying On-Chain agent:', error);
        throw error;
    }
}

/**
 * Generate a comprehensive analysis for a cryptocurrency
 * @param {string} cryptoName - The name of the cryptocurrency
 * @param {string} timeframe - The timeframe for analysis (e.g., "7d", "30d")
 * @returns {Promise<Object>} Comprehensive analysis
 */
async function generateComprehensiveAnalysis(cryptoName, timeframe) {
    try {
        const response = await axios.post(`${ADK_API_URL}/analysis`, {
            cryptoName,
            timeframe
        });

        return response.data;
    } catch (error) {
        console.error('Error generating comprehensive analysis:', error);
        throw error;
    }
}

/**
 * Generate a price prediction for a cryptocurrency
 * @param {string} cryptoName - The name of the cryptocurrency
 * @param {string} timeframe - The timeframe for prediction (e.g., "7d", "30d")
 * @returns {Promise<Object>} Price prediction
 */
async function generatePricePrediction(cryptoName, timeframe) {
    try {
        const response = await axios.post(`${ADK_API_URL}/prediction`, {
            cryptoName,
            timeframe
        });

        return response.data;
    } catch (error) {
        console.error('Error generating price prediction:', error);
        throw error;
    }
}

module.exports = {
    startAdkApiServer,
    queryGuruAgent,
    queryTechnicalAgent,
    querySentimentAgent,
    queryOnchainAgent,
    generateComprehensiveAnalysis,
    generatePricePrediction
};
