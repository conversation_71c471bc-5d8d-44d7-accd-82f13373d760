import React, { useState, useEffect } from 'react';
import { PortfolioAsset } from '../../services/portfolio.service';
import { usePortfolio } from '../../hooks/usePortfolio';
import '../../styles/portfolio/PortfolioAnalysis.css';

interface PortfolioAnalysisProps {
  portfolio: PortfolioAsset[];
  availableCryptos: any[];
}

interface RiskMetrics {
  volatility: number;
  sharpeRatio: number;
  maxDrawdown: number;
  beta: number;
  correlationMatrix: { [key: string]: { [key: string]: number } };
}

const PortfolioAnalysis: React.FC<PortfolioAnalysisProps> = ({
  portfolio,
  availableCryptos
}) => {
  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedTab, setSelectedTab] = useState<'risk' | 'correlation' | 'performance'>('risk');
  const { historicalData } = usePortfolio();

  // Calcular métricas de riesgo
  useEffect(() => {
    if (portfolio.length === 0 || !historicalData || historicalData.length === 0) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    // Simulación de cálculo de métricas (en una aplicación real, estos cálculos serían más complejos)
    setTimeout(() => {
      // Calcular volatilidad (desviación estándar de los rendimientos diarios)
      const returns: number[] = [];
      for (let i = 1; i < historicalData.length; i++) {
        const dailyReturn = (historicalData[i].value - historicalData[i-1].value) / historicalData[i-1].value;
        returns.push(dailyReturn);
      }

      const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
      const squaredDiffs = returns.map(r => Math.pow(r - meanReturn, 2));
      const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / squaredDiffs.length;
      const volatility = Math.sqrt(variance) * Math.sqrt(252); // Anualizada

      // Calcular Ratio de Sharpe (rendimiento ajustado al riesgo)
      const riskFreeRate = 0.02; // 2% anual
      const annualizedReturn = meanReturn * 252;
      const sharpeRatio = (annualizedReturn - riskFreeRate) / volatility;

      // Calcular Drawdown máximo
      let maxValue = historicalData[0].value;
      let maxDrawdown = 0;

      for (let i = 1; i < historicalData.length; i++) {
        const currentValue = historicalData[i].value;
        maxValue = Math.max(maxValue, currentValue);
        const drawdown = (maxValue - currentValue) / maxValue;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }

      // Calcular Beta (relación con el mercado, simulado)
      const beta = 0.8 + Math.random() * 0.4; // Simulado entre 0.8 y 1.2

      // Matriz de correlación (simulada)
      const correlationMatrix: { [key: string]: { [key: string]: number } } = {};

      if (portfolio.length > 1) {
        portfolio.forEach(asset1 => {
          correlationMatrix[asset1.symbol] = {};

          portfolio.forEach(asset2 => {
            // Valores de correlación simulados
            if (asset1.id === asset2.id) {
              correlationMatrix[asset1.symbol][asset2.symbol] = 1; // Autocorrelación
            } else {
              // Generar correlaciones aleatorias pero realistas
              const baseCorrelation = Math.random() * 0.6 + 0.2; // Entre 0.2 y 0.8
              correlationMatrix[asset1.symbol][asset2.symbol] = baseCorrelation;
            }
          });
        });
      }

      setRiskMetrics({
        volatility,
        sharpeRatio,
        maxDrawdown,
        beta,
        correlationMatrix
      });

      setIsLoading(false);
    }, 1000);
  }, [portfolio, historicalData]);

  // Renderizar la matriz de correlación
  const renderCorrelationMatrix = () => {
    if (!riskMetrics || !riskMetrics.correlationMatrix || Object.keys(riskMetrics.correlationMatrix).length === 0) {
      return (
        <div className="no-data-message">
          <p>Se necesitan al menos 2 activos para calcular correlaciones</p>
        </div>
      );
    }

    const symbols = Object.keys(riskMetrics.correlationMatrix);

    return (
      <div className="correlation-matrix">
        <table>
          <thead>
            <tr>
              <th></th>
              {symbols.map(symbol => (
                <th key={symbol}>{symbol.toUpperCase()}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {symbols.map(symbol1 => (
              <tr key={symbol1}>
                <th>{symbol1.toUpperCase()}</th>
                {symbols.map(symbol2 => {
                  const correlation = riskMetrics.correlationMatrix[symbol1][symbol2];
                  let colorClass = '';

                  if (correlation === 1) {
                    colorClass = 'perfect-correlation';
                  } else if (correlation > 0.7) {
                    colorClass = 'high-correlation';
                  } else if (correlation > 0.3) {
                    colorClass = 'medium-correlation';
                  } else {
                    colorClass = 'low-correlation';
                  }

                  return (
                    <td key={symbol2} className={colorClass}>
                      {correlation.toFixed(2)}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Renderizar métricas de riesgo
  const renderRiskMetrics = () => {
    if (!riskMetrics) {
      return (
        <div className="no-data-message">
          <p>No hay suficientes datos para calcular métricas de riesgo</p>
        </div>
      );
    }

    return (
      <div className="risk-metrics">
        <div className="metric-card">
          <h4>Volatilidad Anualizada</h4>
          <div className="metric-value">{(riskMetrics.volatility * 100).toFixed(2)}%</div>
          <div className="metric-description">
            Mide la dispersión de los rendimientos. Una volatilidad más alta indica mayor riesgo.
          </div>
        </div>

        <div className="metric-card">
          <h4>Ratio de Sharpe</h4>
          <div className="metric-value">{riskMetrics.sharpeRatio.toFixed(2)}</div>
          <div className="metric-description">
            Rendimiento ajustado al riesgo. Un valor mayor indica mejor rendimiento por unidad de riesgo.
          </div>
        </div>

        <div className="metric-card">
          <h4>Drawdown Máximo</h4>
          <div className="metric-value">{(riskMetrics.maxDrawdown * 100).toFixed(2)}%</div>
          <div className="metric-description">
            La mayor caída desde un pico. Indica el riesgo de pérdida máxima histórica.
          </div>
        </div>

        <div className="metric-card">
          <h4>Beta</h4>
          <div className="metric-value">{riskMetrics.beta.toFixed(2)}</div>
          <div className="metric-description">
            Sensibilidad al mercado. Beta mayor a 1 indica mayor volatilidad que el mercado.
          </div>
        </div>
      </div>
    );
  };

  // Renderizar análisis de rendimiento
  const renderPerformanceAnalysis = () => {
    if (portfolio.length === 0 || !historicalData || historicalData.length < 2) {
      return (
        <div className="no-data-message">
          <p>No hay suficientes datos para analizar el rendimiento</p>
        </div>
      );
    }

    // Calcular rendimientos
    const firstValue = historicalData[0].value;
    const lastValue = historicalData[historicalData.length - 1].value;
    const totalReturn = (lastValue - firstValue) / firstValue;

    // Calcular rendimiento anualizado (aproximado)
    const days = historicalData.length;
    const annualizedReturn = Math.pow(1 + totalReturn, 365 / days) - 1;

    // Calcular rendimientos por activo
    const assetReturns = portfolio.map(asset => {
      const crypto = availableCryptos.find(c => c.id === asset.id);
      const currentPrice = crypto ? crypto.current_price : 0;
      const priceChange = crypto ? crypto.price_change_percentage_24h : 0;
      const returnValue = (currentPrice - asset.purchasePrice) / asset.purchasePrice;

      return {
        symbol: asset.symbol,
        name: asset.name,
        return: returnValue,
        change24h: priceChange
      };
    }).sort((a, b) => b.return - a.return);

    return (
      <div className="performance-analysis">
        <div className="performance-summary">
          <div className="metric-card">
            <h4>Rendimiento Total</h4>
            <div className={`metric-value ${totalReturn >= 0 ? 'positive' : 'negative'}`}>
              {(totalReturn * 100).toFixed(2)}%
            </div>
          </div>

          <div className="metric-card">
            <h4>Rendimiento Anualizado</h4>
            <div className={`metric-value ${annualizedReturn >= 0 ? 'positive' : 'negative'}`}>
              {(annualizedReturn * 100).toFixed(2)}%
            </div>
          </div>
        </div>

        <div className="asset-performance">
          <h4>Rendimiento por Activo</h4>
          <table className="asset-returns-table">
            <thead>
              <tr>
                <th>Activo</th>
                <th>Rendimiento Total</th>
                <th>Cambio 24h</th>
              </tr>
            </thead>
            <tbody>
              {assetReturns.map(asset => (
                <tr key={asset.symbol}>
                  <td>{asset.symbol.toUpperCase()}</td>
                  <td className={asset.return >= 0 ? 'positive' : 'negative'}>
                    {(asset.return * 100).toFixed(2)}%
                  </td>
                  <td className={asset.change24h >= 0 ? 'positive' : 'negative'}>
                    {asset.change24h.toFixed(2)}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  if (portfolio.length === 0) {
    return (
      <div className="portfolio-analysis empty-analysis">
        <h3>Análisis de Portafolio</h3>
        <div className="empty-analysis-message">
          <p>Añade activos a tu portafolio para ver el análisis</p>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-analysis">
      <h3>Análisis de Portafolio</h3>

      <div className="analysis-tabs">
        <button
          className={selectedTab === 'risk' ? 'active' : ''}
          onClick={() => setSelectedTab('risk')}
        >
          Métricas de Riesgo
        </button>
        <button
          className={selectedTab === 'correlation' ? 'active' : ''}
          onClick={() => setSelectedTab('correlation')}
        >
          Correlaciones
        </button>
        <button
          className={selectedTab === 'performance' ? 'active' : ''}
          onClick={() => setSelectedTab('performance')}
        >
          Rendimiento
        </button>
      </div>

      <div className="analysis-content">
        {isLoading ? (
          <div className="loading-message">
            <p>Calculando métricas...</p>
          </div>
        ) : (
          <>
            {selectedTab === 'risk' && renderRiskMetrics()}
            {selectedTab === 'correlation' && renderCorrelationMatrix()}
            {selectedTab === 'performance' && renderPerformanceAnalysis()}
          </>
        )}
      </div>
    </div>
  );
};

export default PortfolioAnalysis;
