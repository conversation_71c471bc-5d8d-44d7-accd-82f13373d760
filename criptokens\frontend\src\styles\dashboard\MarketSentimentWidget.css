.market-sentiment-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.market-sentiment-widget:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.market-sentiment-widget.loading .widget-content,
.market-sentiment-widget.error .widget-content {
  padding: 1.5rem;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--color-surface-dark);
}

.widget-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.sentiment-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-surface-light);
  font-size: 1.1rem;
  color: var(--text-primary);
}

.widget-content {
  padding: 1rem;
}

.sentiment-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.sentiment-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sentiment-label {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.sentiment-label.extreme_fear {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.sentiment-label.fear {
  background-color: rgba(230, 126, 34, 0.1);
  color: #e67e22;
}

.sentiment-label.neutral {
  background-color: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.sentiment-label.greed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.sentiment-label.extreme_greed {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.sentiment-index {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.sentiment-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.sentiment-meter {
  margin-bottom: 1.25rem;
}

.meter-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  font-size: 0.7rem;
  color: var(--text-tertiary);
}

.meter-bar {
  height: 8px;
  background: linear-gradient(90deg, 
    #e74c3c 0%, 
    #e67e22 25%, 
    #f1c40f 50%, 
    #2ecc71 75%, 
    #27ae60 100%
  );
  border-radius: 4px;
  position: relative;
  margin-bottom: 0.5rem;
}

.meter-indicator {
  position: absolute;
  top: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #f1c40f;
  transform: translateX(-50%);
  border: 2px solid white;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  transition: left 0.5s ease;
}

.sentiment-factors {
  margin-bottom: 1rem;
}

.sentiment-factors h4 {
  font-size: 0.9rem;
  margin: 0 0 0.75rem 0;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.factors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.factor {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.factor-label {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.factor-bar {
  height: 6px;
  background-color: var(--color-surface-light);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.factor-indicator {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 3px;
}

.factor-indicator.positive {
  background-color: var(--color-positive);
}

.factor-indicator.negative {
  background-color: var(--color-negative);
}

.sentiment-advice {
  padding: 0.75rem;
  background-color: rgba(52, 152, 219, 0.05);
  border-left: 3px solid #3498db;
  border-radius: 4px;
}

.sentiment-advice p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-tertiary);
}

.error-message i {
  font-size: 2rem;
  color: var(--color-negative);
  opacity: 0.7;
}

.error-message p {
  margin: 0;
  font-size: 0.9rem;
}

.skeleton-loading {
  width: 100%;
  height: 150px;
  background: linear-gradient(90deg, 
    var(--color-surface-dark) 25%, 
    var(--color-surface) 50%, 
    var(--color-surface-dark) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .factors-grid {
    grid-template-columns: 1fr;
  }
  
  .sentiment-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 576px) {
  .widget-header {
    padding: 0.75rem;
  }
  
  .widget-content {
    padding: 0.75rem;
  }
}
