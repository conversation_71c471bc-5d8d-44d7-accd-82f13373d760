/**
 * <PERSON><PERSON><PERSON>lo de logging para el backend de Criptokens
 */

// Niveles de log
const LOG_LEVELS = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
};

// Configuración del nivel de log (se puede cambiar según el entorno)
const currentLogLevel = process.env.LOG_LEVEL || LOG_LEVELS.INFO;

// Función para formatear la fecha
const formatDate = () => {
  return new Date().toISOString();
};

// Función para determinar si un nivel de log debe ser mostrado
const shouldLog = (level) => {
  const levels = Object.values(LOG_LEVELS);
  const currentIndex = levels.indexOf(currentLogLevel);
  const levelIndex = levels.indexOf(level);
  
  return levelIndex <= currentIndex;
};

// Funciones de log
const logger = {
  error: (message, ...args) => {
    if (shouldLog(LOG_LEVELS.ERROR)) {
      console.error(`[${formatDate()}] [ERROR] ${message}`, ...args);
    }
  },
  
  warn: (message, ...args) => {
    if (shouldLog(LOG_LEVELS.WARN)) {
      console.warn(`[${formatDate()}] [WARN] ${message}`, ...args);
    }
  },
  
  info: (message, ...args) => {
    if (shouldLog(LOG_LEVELS.INFO)) {
      console.info(`[${formatDate()}] [INFO] ${message}`, ...args);
    }
  },
  
  debug: (message, ...args) => {
    if (shouldLog(LOG_LEVELS.DEBUG)) {
      console.debug(`[${formatDate()}] [DEBUG] ${message}`, ...args);
    }
  },
  
  // Método para registrar el tiempo de ejecución de una función
  time: (label) => {
    if (shouldLog(LOG_LEVELS.DEBUG)) {
      console.time(label);
    }
  },
  
  timeEnd: (label) => {
    if (shouldLog(LOG_LEVELS.DEBUG)) {
      console.timeEnd(label);
    }
  }
};

module.exports = logger;
