/**
 * Servicio para interactuar con la API de Etherscan
 */
const axios = require('axios');
const config = require('../config');

class EtherscanService {
  constructor() {
    this.apiKey = config.etherscan.apiKey;
    this.apiUrl = config.etherscan.apiUrl;
  }

  /**
   * Obtiene el balance de ETH de una dirección
   * @param {string} address - Dirección de la cartera
   * @returns {Promise<Object>} - Información del balance
   */
  async getWalletBalance(address) {
    try {
      const response = await axios.get(this.apiUrl, {
        params: {
          module: 'account',
          action: 'balance',
          address,
          tag: 'latest',
          apikey: this.apiKey
        }
      });

      if (response.data.status === '1') {
        // Convertir de wei a ether
        const balanceInWei = response.data.result;
        const balanceInEther = balanceInWei / 1e18;
        
        return {
          address,
          balance: balanceInEther,
          currency: 'ETH',
          balanceInWei,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      throw error;
    }
  }

  /**
   * Obtiene el balance de un token ERC-20 para una dirección
   * @param {string} address - Dirección de la cartera
   * @param {string} tokenAddress - Dirección del contrato del token
   * @returns {Promise<Object>} - Información del balance del token
   */
  async getTokenBalance(address, tokenAddress) {
    try {
      const response = await axios.get(this.apiUrl, {
        params: {
          module: 'account',
          action: 'tokenbalance',
          contractaddress: tokenAddress,
          address,
          tag: 'latest',
          apikey: this.apiKey
        }
      });

      if (response.data.status === '1') {
        // Obtener información del token para determinar decimales
        const tokenInfo = await this.getTokenInfo(tokenAddress);
        const decimals = tokenInfo.decimals || 18;
        
        // Convertir balance según decimales
        const rawBalance = response.data.result;
        const balance = rawBalance / Math.pow(10, decimals);
        
        return {
          address,
          tokenAddress,
          balance,
          tokenSymbol: tokenInfo.symbol,
          tokenName: tokenInfo.name,
          rawBalance,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching token balance:', error);
      throw error;
    }
  }

  /**
   * Obtiene las transacciones de una dirección
   * @param {string} address - Dirección de la cartera
   * @param {number} page - Número de página
   * @param {number} offset - Número de transacciones por página
   * @returns {Promise<Object>} - Lista de transacciones
   */
  async getTransactions(address, page = 1, offset = 10) {
    try {
      const response = await axios.get(this.apiUrl, {
        params: {
          module: 'account',
          action: 'txlist',
          address,
          startblock: 0,
          endblock: ********,
          page,
          offset,
          sort: 'desc',
          apikey: this.apiKey
        }
      });

      if (response.data.status === '1') {
        return {
          address,
          transactions: response.data.result.map(tx => ({
            hash: tx.hash,
            from: tx.from,
            to: tx.to,
            value: tx.value / 1e18, // Convert wei to ether
            timestamp: parseInt(tx.timeStamp) * 1000, // Convert to milliseconds
            gasPrice: tx.gasPrice,
            gasUsed: tx.gasUsed,
            isError: tx.isError === '1',
            txreceipt_status: tx.txreceipt_status,
            input: tx.input,
            confirmations: tx.confirmations
          })),
          page,
          offset,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  }

  /**
   * Obtiene información sobre un token ERC-20
   * @param {string} tokenAddress - Dirección del contrato del token
   * @returns {Promise<Object>} - Información del token
   */
  async getTokenInfo(tokenAddress) {
    try {
      // Get token info
      const response = await axios.get(this.apiUrl, {
        params: {
          module: 'token',
          action: 'tokeninfo',
          contractaddress: tokenAddress,
          apikey: this.apiKey
        }
      });

      if (response.data.status === '1' && response.data.result.length > 0) {
        const tokenInfo = response.data.result[0];
        return {
          address: tokenAddress,
          name: tokenInfo.name,
          symbol: tokenInfo.symbol,
          decimals: parseInt(tokenInfo.decimals),
          totalSupply: tokenInfo.totalSupply,
          website: tokenInfo.website,
          description: tokenInfo.description
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching token info:', error);
      throw error;
    }
  }

  /**
   * Obtiene el precio actual del gas
   * @returns {Promise<Object>} - Información del precio del gas
   */
  async getGasPrice() {
    try {
      const response = await axios.get(this.apiUrl, {
        params: {
          module: 'gastracker',
          action: 'gasoracle',
          apikey: this.apiKey
        }
      });

      if (response.data.status === '1') {
        return {
          safeGasPrice: response.data.result.SafeGasPrice,
          proposeGasPrice: response.data.result.ProposeGasPrice,
          fastGasPrice: response.data.result.FastGasPrice,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching gas price:', error);
      throw error;
    }
  }

  /**
   * Obtiene eventos de un contrato inteligente
   * @param {string} contractAddress - Dirección del contrato
   * @param {string} topic0 - Firma del evento (opcional)
   * @param {number} page - Número de página
   * @param {number} offset - Número de eventos por página
   * @returns {Promise<Object>} - Lista de eventos
   */
  async getContractEvents(contractAddress, topic0 = null, page = 1, offset = 10) {
    try {
      const params = {
        module: 'logs',
        action: 'getLogs',
        address: contractAddress,
        page,
        offset,
        apikey: this.apiKey
      };

      // Si se proporciona topic0, añadirlo a los parámetros
      if (topic0) {
        params.topic0 = topic0;
      }

      const response = await axios.get(this.apiUrl, { params });

      if (response.data.status === '1') {
        return {
          contractAddress,
          events: response.data.result,
          page,
          offset,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching contract events:', error);
      throw error;
    }
  }

  /**
   * Obtiene las transferencias de tokens ERC-20 para una dirección
   * @param {string} address - Dirección de la cartera
   * @param {string} tokenAddress - Dirección del contrato del token (opcional)
   * @param {number} page - Número de página
   * @param {number} offset - Número de transferencias por página
   * @returns {Promise<Object>} - Lista de transferencias
   */
  async getTokenTransfers(address, tokenAddress = null, page = 1, offset = 10) {
    try {
      const params = {
        module: 'account',
        action: 'tokentx',
        address,
        page,
        offset,
        sort: 'desc',
        apikey: this.apiKey
      };

      // Si se proporciona tokenAddress, añadirlo a los parámetros
      if (tokenAddress) {
        params.contractaddress = tokenAddress;
      }

      const response = await axios.get(this.apiUrl, { params });

      if (response.data.status === '1') {
        return {
          address,
          tokenAddress: tokenAddress || 'all',
          transfers: response.data.result.map(tx => ({
            hash: tx.hash,
            from: tx.from,
            to: tx.to,
            tokenSymbol: tx.tokenSymbol,
            tokenName: tx.tokenName,
            tokenDecimal: tx.tokenDecimal,
            value: tx.value / Math.pow(10, tx.tokenDecimal),
            timestamp: parseInt(tx.timeStamp) * 1000,
            contractAddress: tx.contractAddress,
            gasPrice: tx.gasPrice,
            gasUsed: tx.gasUsed
          })),
          page,
          offset,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching token transfers:', error);
      throw error;
    }
  }

  /**
   * Obtiene información sobre las "ballenas" (grandes tenedores) de un token
   * @param {string} tokenAddress - Dirección del contrato del token
   * @param {number} limit - Número máximo de ballenas a devolver
   * @returns {Promise<Object>} - Lista de ballenas
   */
  async getTokenWhales(tokenAddress, limit = 10) {
    try {
      const response = await axios.get(this.apiUrl, {
        params: {
          module: 'token',
          action: 'tokenholderlist',
          contractaddress: tokenAddress,
          page: 1,
          offset: limit,
          apikey: this.apiKey
        }
      });

      if (response.data.status === '1') {
        // Obtener información del token
        const tokenInfo = await this.getTokenInfo(tokenAddress);
        
        return {
          tokenAddress,
          tokenName: tokenInfo.name,
          tokenSymbol: tokenInfo.symbol,
          whales: response.data.result.map(holder => ({
            address: holder.address,
            balance: holder.TokenHolderQuantity / Math.pow(10, tokenInfo.decimals),
            rawBalance: holder.TokenHolderQuantity,
            percentage: (holder.TokenHolderQuantity / tokenInfo.totalSupply) * 100
          })),
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Etherscan API error: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error fetching token whales:', error);
      throw error;
    }
  }
}

module.exports = new EtherscanService();
