/**
 * Servicio para generar predicciones de mercado utilizando IA
 */
import { getHistoricalDataWithFallback } from '../historicalData.service';
import { getTopCryptocurrencies } from '../api';
import { callOpenRouter } from './openRouterService';
import { analyzeCryptoSentiment } from '../sentiment/sentimentAnalysis';
import { getOnChainMetrics } from '../onchain/etherscanService';
import { savePrediction } from '../prediction/predictionHistory';
import { getMarketData } from '../marketData.service';

// Tipos para las predicciones
export interface PredictionSignals {
  technical: number; // -100 a 100
  sentiment: number; // -100 a 100
  onChain: number; // -100 a 100
  fundamental: number; // -100 a 100
}

export interface CryptoPrediction {
  cryptoId: string;
  symbol: string;
  name: string;
  timeframe: string;
  direction: 'up' | 'down' | 'sideways';
  confidence: number;
  potentialGain?: number;
  potentialLoss?: number;
  signals: PredictionSignals;
  reasoning: string[];
  timestamp: Date;
}

/**
 * Genera una predicción para una criptomoneda utilizando IA
 * @param cryptoId ID de la criptomoneda (ej. bitcoin, ethereum)
 * @param timeframe Horizonte de tiempo para la predicción (1d, 7d, 30d)
 * @returns Predicción generada
 */
export const generatePrediction = async (
  cryptoId: string,
  timeframe: string = '7d'
): Promise<CryptoPrediction> => {
  try {
    console.log(`Generando predicción para ${cryptoId} con horizonte ${timeframe}`);

    // 1. Recopilar datos históricos
    const days = timeframe === '1d' ? 30 : timeframe === '7d' ? 90 : 180;
    console.log(`Obteniendo datos históricos de ${days} días...`);
    const historicalData = await getHistoricalDataWithFallback(cryptoId, days);

    // 2. Obtener datos actuales de la criptomoneda
    console.log('Obteniendo datos actuales de la criptomoneda...');
    const cryptoList = await getTopCryptocurrencies();
    const cryptoInfo = cryptoList.find(c => c.id === cryptoId);

    if (!cryptoInfo) {
      throw new Error(`No se encontró información para ${cryptoId}`);
    }

    // 3. Obtener análisis de sentimiento
    console.log('Analizando sentimiento del mercado...');
    const sentimentAnalysis = await analyzeCryptoSentiment(cryptoId, cryptoInfo.name);

    // 4. Obtener datos on-chain
    console.log('Obteniendo métricas on-chain...');
    const onChainMetrics = await getOnChainMetrics(cryptoId);

    // 5. Obtener datos del mercado global
    console.log('Obteniendo datos del mercado global...');
    const marketData = await getMarketData();

    // 6. Preparar el contexto para el modelo de IA
    console.log('Preparando contexto para el modelo de IA...');
    const context = prepareContext(
      cryptoId,
      timeframe,
      historicalData,
      cryptoInfo,
      sentimentAnalysis,
      onChainMetrics,
      marketData
    );

    // 6. Generar la predicción utilizando IA
    console.log('Generando predicción con IA...');
    const prediction = await generateAIPrediction(context);

    // 7. Formatear la predicción
    const formattedPrediction: CryptoPrediction = {
      cryptoId,
      symbol: cryptoInfo.symbol,
      name: cryptoInfo.name,
      timeframe,
      ...prediction,
      timestamp: new Date()
    };

    // 8. Guardar la predicción en la base de datos
    try {
      console.log('Guardando predicción en la base de datos...');
      await savePrediction(formattedPrediction);
    } catch (saveError) {
      console.error('Error al guardar predicción:', saveError);
      // Continuar aunque falle el guardado
    }

    // 9. Devolver la predicción
    return formattedPrediction;
  } catch (error) {
    console.error('Error al generar predicción:', error);

    // Generar una predicción simulada como fallback
    return generateSimulatedPrediction(cryptoId, timeframe);
  }
};

/**
 * Prepara el contexto para el modelo de IA
 */
const prepareContext = (
  cryptoId: string,
  timeframe: string,
  historicalData: any,
  cryptoInfo: any,
  sentimentAnalysis: any,
  onChainMetrics: any,
  marketData: any
) => {
  // Extraer datos relevantes
  const priceData = historicalData?.prices || [];
  const currentPrice = cryptoInfo.current_price;
  const priceChange24h = cryptoInfo.price_change_percentage_24h;
  const marketCap = cryptoInfo.market_cap;
  const volume = cryptoInfo.total_volume;

  // Calcular indicadores técnicos básicos (simplificados)
  const prices = priceData.map((p: any) => p[1]);
  const lastPrice = prices[prices.length - 1];

  // Media móvil simple de 7 y 25 días
  const sma7 = calculateSMA(prices, 7);
  const sma25 = calculateSMA(prices, 25);

  // RSI simplificado
  const rsi = calculateSimpleRSI(prices);

  // Extraer datos de sentimiento
  const sentimentScore = sentimentAnalysis.overallSentiment;
  const topNews = sentimentAnalysis.newsItems.slice(0, 5).map((news: any) => ({
    title: news.title,
    sentiment: news.sentimentScore
  }));
  const keywords = sentimentAnalysis.keywords.slice(0, 5);

  // Extraer datos on-chain
  const {
    dailyTransactions,
    activeAddresses,
    whaleInflows,
    whaleOutflows,
    netWhaleFlow,
    onChainSentiment
  } = onChainMetrics;

  // Preparar el contexto
  return {
    crypto: {
      id: cryptoId,
      name: cryptoInfo.name,
      symbol: cryptoInfo.symbol,
      currentPrice,
      priceChange24h,
      marketCap,
      volume
    },
    technicalIndicators: {
      sma7,
      sma25,
      rsi,
      trendDirection: sma7 > sma25 ? 'up' : 'down',
      volatility: calculateVolatility(prices)
    },
    sentimentAnalysis: {
      overallSentiment: sentimentScore,
      topNews,
      keywords,
      sourcesBreakdown: sentimentAnalysis.sources
    },
    onChainData: {
      dailyTransactions,
      activeAddresses,
      whaleInflows,
      whaleOutflows,
      netWhaleFlow,
      onChainSentiment
    },
    timeframe,
    marketConditions: {
      btcDominance: marketData?.btcDominance || 40,
      globalMarketCap: marketData?.totalMarketCapInTrillions || 1.5,
      fearGreedIndex: marketData?.fearGreedIndex || 50,
      fearGreedClassification: marketData?.fearGreedClassification || 'Neutral'
    }
  };
};

/**
 * Genera una predicción utilizando IA a través de OpenRouter
 */
const generateAIPrediction = async (context: any) => {
  try {
    // Preparar el prompt para el modelo de IA
    const prompt = `
      Analiza la siguiente criptomoneda y genera una predicción de precio basada en los datos proporcionados.

      DATOS DE LA CRIPTOMONEDA:
      Nombre: ${context.crypto.name} (${context.crypto.symbol.toUpperCase()})
      Precio actual: $${context.crypto.currentPrice}
      Cambio 24h: ${context.crypto.priceChange24h}%
      Capitalización de mercado: $${context.crypto.marketCap}
      Volumen 24h: $${context.crypto.volume}

      ANÁLISIS TÉCNICO:
      - Media Móvil Simple 7 días: $${context.technicalIndicators.sma7.toFixed(2)}
      - Media Móvil Simple 25 días: $${context.technicalIndicators.sma25.toFixed(2)}
      - RSI (Índice de Fuerza Relativa): ${context.technicalIndicators.rsi.toFixed(2)}
      - Dirección de tendencia: ${context.technicalIndicators.trendDirection}
      - Volatilidad: ${context.technicalIndicators.volatility.toFixed(4)}

      ANÁLISIS DE SENTIMIENTO:
      - Sentimiento general: ${context.sentimentAnalysis.overallSentiment} (-100 a 100)
      - Distribución de fuentes: ${context.sentimentAnalysis.sourcesBreakdown.positive} positivas, ${context.sentimentAnalysis.sourcesBreakdown.negative} negativas, ${context.sentimentAnalysis.sourcesBreakdown.neutral} neutrales
      - Noticias destacadas:
        ${context.sentimentAnalysis.topNews.map((news: any) => `* "${news.title}" (Sentimiento: ${news.sentiment})`).join('\n        ')}
      - Palabras clave: ${context.sentimentAnalysis.keywords.map((k: any) => `${k.word} (${k.count})`).join(', ')}

      DATOS ON-CHAIN:
      - Transacciones diarias: ${context.onChainData.dailyTransactions.toLocaleString()}
      - Direcciones activas: ${context.onChainData.activeAddresses.toLocaleString()}
      - Flujo de ballenas: Entradas $${context.onChainData.whaleInflows.toLocaleString()}, Salidas $${context.onChainData.whaleOutflows.toLocaleString()}
      - Flujo neto de ballenas: $${context.onChainData.netWhaleFlow.toLocaleString()} (${context.onChainData.netWhaleFlow > 0 ? 'positivo' : 'negativo'})
      - Sentimiento on-chain: ${context.onChainData.onChainSentiment} (-100 a 100)

      CONDICIONES DE MERCADO:
      - Dominancia de Bitcoin: ${context.marketConditions.btcDominance.toFixed(2)}%
      - Capitalización global del mercado: $${context.marketConditions.globalMarketCap.toFixed(2)} trillones
      - Índice de Miedo y Codicia: ${context.marketConditions.fearGreedIndex.toFixed(0)} (0-100, donde 0 es miedo extremo y 100 es codicia extrema)
      - Clasificación de Miedo y Codicia: ${context.marketConditions.fearGreedClassification}

      HORIZONTE DE TIEMPO PARA LA PREDICCIÓN: ${context.timeframe}

      INSTRUCCIONES:
      Basándote exclusivamente en los datos proporcionados, genera una predicción de precio con el siguiente formato:

      1. DIRECCIÓN: Indica si el precio será "up" (alcista), "down" (bajista) o "sideways" (lateral).

      2. CONFIANZA: Asigna un nivel de confianza entre 0 y 100% a tu predicción.

      3. POTENCIAL: Si es alcista, indica el potencial de ganancia en porcentaje. Si es bajista, indica el potencial de pérdida en porcentaje.

      4. SEÑALES: Proporciona valores numéricos entre -100 y 100 para cada tipo de señal:
         - technical: Señales del análisis técnico
         - sentiment: Señales basadas en sentimiento de mercado
         - onChain: Señales basadas en datos de la blockchain
         - fundamental: Señales basadas en fundamentos del proyecto

      5. REASONING: Proporciona entre 3 y 5 razones específicas que justifiquen tu predicción.

      Tu respuesta debe estar en formato JSON con la siguiente estructura exacta:
      {
        "direction": "up"|"down"|"sideways",
        "confidence": number,
        "potentialGain": number|null,
        "potentialLoss": number|null,
        "signals": {
          "technical": number,
          "sentiment": number,
          "onChain": number,
          "fundamental": number
        },
        "reasoning": [string, string, ...]
      }
    `;

    try {
      // Intentar llamar a OpenRouter para obtener una predicción basada en IA
      console.log('Llamando a OpenRouter para generar predicción...');
      const response = await callOpenRouter(prompt, {
        model: "anthropic/claude-3-opus-20240229", // Usar un modelo avanzado para análisis financiero
        max_tokens: 1000,
        temperature: 0.2 // Temperatura baja para respuestas más deterministas
      });

      // Parsear la respuesta de la IA
      return parseAIResponse(response);
    } catch (aiError) {
      console.error('Error al llamar a OpenRouter:', aiError);
      console.log('Fallback a predicción simulada');
      // Si falla la llamada a la IA, usar el método de fallback
      return generateSimulatedPredictionFromContext(context);
    }
  } catch (error) {
    console.error('Error al generar predicción con IA:', error);
    throw error;
  }
};

/**
 * Parsea la respuesta de la IA para extraer la predicción
 */
const parseAIResponse = (response: any): any => {
  try {
    // Buscar un objeto JSON en la respuesta
    const jsonMatch = response.content.match(/```json\n([\s\S]*?)\n```/) ||
                      response.content.match(/{[\s\S]*?}/);

    if (jsonMatch) {
      // Extraer y parsear el JSON
      const jsonStr = jsonMatch[1] || jsonMatch[0];
      const prediction = JSON.parse(jsonStr);

      // Validar la estructura de la predicción
      if (!prediction.direction || !prediction.signals || !prediction.reasoning) {
        throw new Error('Formato de predicción inválido');
      }

      // Asegurarse de que los valores estén en los rangos correctos
      prediction.confidence = Math.min(Math.max(prediction.confidence, 0), 100);
      prediction.signals.technical = Math.min(Math.max(prediction.signals.technical, -100), 100);
      prediction.signals.sentiment = Math.min(Math.max(prediction.signals.sentiment, -100), 100);
      prediction.signals.onChain = Math.min(Math.max(prediction.signals.onChain, -100), 100);
      prediction.signals.fundamental = Math.min(Math.max(prediction.signals.fundamental, -100), 100);

      return prediction;
    } else {
      // Si no se encuentra un JSON válido, extraer información de forma más flexible
      const direction = response.content.includes('alcista') || response.content.includes('subir') ? 'up' :
                       response.content.includes('bajista') || response.content.includes('bajar') ? 'down' : 'sideways';

      // Buscar un número de confianza
      const confidenceMatch = response.content.match(/confianza[:\s]+(\d+)/i) ||
                             response.content.match(/(\d+)%\s+de\s+confianza/i);
      const confidence = confidenceMatch ? parseInt(confidenceMatch[1]) : 70;

      // Extraer razones
      const reasoningLines = response.content.split('\n')
        .filter((line: string) => line.trim().startsWith('-') || line.trim().startsWith('*'))
        .map((line: string) => line.replace(/^[-*]\s+/, '').trim())
        .filter((line: string) => line.length > 10);

      const reasoning = reasoningLines.length >= 3 ? reasoningLines.slice(0, 5) :
        ['Basado en análisis técnico', 'Considerando las condiciones actuales de mercado', 'Evaluando patrones históricos'];

      return {
        direction,
        confidence,
        potentialGain: direction === 'up' ? Math.round(confidence / 10) : undefined,
        potentialLoss: direction === 'down' ? Math.round(confidence / 10) : undefined,
        signals: {
          technical: direction === 'up' ? 50 : direction === 'down' ? -50 : 0,
          sentiment: direction === 'up' ? 40 : direction === 'down' ? -40 : 0,
          onChain: direction === 'up' ? 30 : direction === 'down' ? -30 : 0,
          fundamental: direction === 'up' ? 45 : direction === 'down' ? -45 : 0
        },
        reasoning
      };
    }
  } catch (error) {
    console.error('Error al parsear respuesta de IA:', error);
    throw new Error('No se pudo interpretar la respuesta del modelo de IA');
  }
};

/**
 * Genera una predicción simulada basada en el contexto
 */
const generateSimulatedPredictionFromContext = (context: any) => {
  // Determinar la dirección basada en indicadores técnicos
  const { sma7, sma25, rsi } = context.technicalIndicators;
  const priceChange = context.crypto.priceChange24h;

  let direction: 'up' | 'down' | 'sideways';
  let confidence: number;

  // Lógica simplificada para determinar la dirección
  if (sma7 > sma25 && rsi < 70 && priceChange > 0) {
    direction = 'up';
    confidence = 60 + Math.random() * 25;
  } else if (sma7 < sma25 && rsi > 30 && priceChange < 0) {
    direction = 'down';
    confidence = 60 + Math.random() * 25;
  } else {
    direction = 'sideways';
    confidence = 50 + Math.random() * 20;
  }

  // Ajustar por volatilidad
  confidence = Math.min(95, confidence);

  // Generar señales
  const technicalSignal = direction === 'up' ?
    Math.random() * 60 + 20 :
    direction === 'down' ?
      -Math.random() * 60 - 20 :
      Math.random() * 40 - 20;

  const sentimentSignal = direction === 'up' ?
    Math.random() * 70 + 10 :
    direction === 'down' ?
      -Math.random() * 70 - 10 :
      Math.random() * 30 - 15;

  const onChainSignal = direction === 'up' ?
    Math.random() * 50 + 10 :
    direction === 'down' ?
      -Math.random() * 50 - 10 :
      Math.random() * 20 - 10;

  const fundamentalSignal = direction === 'up' ?
    Math.random() * 40 + 20 :
    direction === 'down' ?
      -Math.random() * 40 - 20 :
      Math.random() * 30 - 15;

  // Generar razonamientos
  const upReasons = [
    `Aumento significativo en la actividad de desarrollo en GitHub`,
    `Creciente adopción institucional observada en transacciones on-chain`,
    `Patrón de acumulación detectado en direcciones de ballenas`,
    `Mejora en los indicadores técnicos: RSI, MACD y Bollinger Bands`,
    `Sentimiento positivo creciente en redes sociales y comunidades`,
    `Próximos eventos importantes: actualización de protocolo, listados en exchanges`
  ];

  const downReasons = [
    `Disminución en la actividad de la red y transacciones diarias`,
    `Patrones de distribución detectados en grandes tenedores`,
    `Deterioro en indicadores técnicos clave: cruce de muerte en medias móviles`,
    `Sentimiento negativo predominante en análisis de redes sociales`,
    `Presión regulatoria creciente en mercados importantes`,
    `Competencia aumentando de proyectos similares con mejor tecnología`
  ];

  const sidewaysReasons = [
    `Consolidación de precio después de un movimiento significativo`,
    `Volumen de trading por debajo del promedio, indicando indecisión`,
    `Equilibrio entre compradores y vendedores en niveles de soporte/resistencia`,
    `Indicadores técnicos en rangos neutrales sin señales claras`,
    `Sentimiento mixto en comunidades y redes sociales`,
    `Ausencia de catalizadores importantes a corto plazo`
  ];

  // Seleccionar 3-4 razones aleatorias según la dirección
  const reasonsPool = direction === 'up' ?
    upReasons :
    direction === 'down' ?
      downReasons :
      sidewaysReasons;

  const selectedReasons: string[] = [];
  const numReasons = Math.floor(Math.random() * 2) + 3; // 3-4 razones

  while (selectedReasons.length < numReasons && reasonsPool.length > 0) {
    const randomIndex = Math.floor(Math.random() * reasonsPool.length);
    selectedReasons.push(reasonsPool[randomIndex]);
    reasonsPool.splice(randomIndex, 1);
  }

  return {
    direction,
    confidence,
    potentialGain: direction === 'up' ? Math.round(confidence / 10) : undefined,
    potentialLoss: direction === 'down' ? Math.round(confidence / 10) : undefined,
    signals: {
      technical: Math.round(technicalSignal),
      sentiment: Math.round(sentimentSignal),
      onChain: Math.round(onChainSignal),
      fundamental: Math.round(fundamentalSignal)
    },
    reasoning: selectedReasons
  };
};

/**
 * Genera una predicción simulada como fallback
 */
const generateSimulatedPrediction = (
  cryptoId: string,
  timeframe: string
): CryptoPrediction => {
  // Generar una dirección aleatoria con sesgo hacia lateral
  const rand = Math.random();
  let direction: 'up' | 'down' | 'sideways';

  if (rand < 0.4) {
    direction = 'up';
  } else if (rand < 0.7) {
    direction = 'down';
  } else {
    direction = 'sideways';
  }

  // Generar confianza
  const confidence = direction === 'sideways' ?
    50 + Math.random() * 20 :
    60 + Math.random() * 25;

  // Generar señales
  const technicalSignal = direction === 'up' ?
    Math.random() * 60 + 20 :
    direction === 'down' ?
      -Math.random() * 60 - 20 :
      Math.random() * 40 - 20;

  const sentimentSignal = direction === 'up' ?
    Math.random() * 70 + 10 :
    direction === 'down' ?
      -Math.random() * 70 - 10 :
      Math.random() * 30 - 15;

  const onChainSignal = direction === 'up' ?
    Math.random() * 50 + 10 :
    direction === 'down' ?
      -Math.random() * 50 - 10 :
      Math.random() * 20 - 10;

  const fundamentalSignal = direction === 'up' ?
    Math.random() * 40 + 20 :
    direction === 'down' ?
      -Math.random() * 40 - 20 :
      Math.random() * 30 - 15;

  // Generar razonamientos genéricos
  const genericReasons = [
    'Basado en patrones históricos de precio',
    'Considerando las condiciones actuales del mercado',
    'Analizando el volumen de trading reciente',
    'Evaluando el sentimiento general del mercado'
  ];

  return {
    cryptoId,
    symbol: cryptoId.substring(0, 3),
    name: cryptoId.charAt(0).toUpperCase() + cryptoId.slice(1),
    timeframe,
    direction,
    confidence,
    potentialGain: direction === 'up' ? Math.round(confidence / 10) : undefined,
    potentialLoss: direction === 'down' ? Math.round(confidence / 10) : undefined,
    signals: {
      technical: Math.round(technicalSignal),
      sentiment: Math.round(sentimentSignal),
      onChain: Math.round(onChainSignal),
      fundamental: Math.round(fundamentalSignal)
    },
    reasoning: genericReasons,
    timestamp: new Date()
  };
};

// Funciones auxiliares para cálculos técnicos

/**
 * Calcula la Media Móvil Simple (SMA)
 */
const calculateSMA = (prices: number[], period: number): number => {
  if (prices.length < period) {
    return prices[prices.length - 1];
  }

  const slice = prices.slice(prices.length - period);
  return slice.reduce((sum, price) => sum + price, 0) / period;
};

/**
 * Calcula un RSI simplificado
 */
const calculateSimpleRSI = (prices: number[]): number => {
  if (prices.length < 14) {
    return 50;
  }

  let gains = 0;
  let losses = 0;

  for (let i = prices.length - 14; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    if (change >= 0) {
      gains += change;
    } else {
      losses -= change;
    }
  }

  if (losses === 0) {
    return 100;
  }

  const rs = gains / losses;
  return 100 - (100 / (1 + rs));
};

/**
 * Calcula la volatilidad (desviación estándar)
 */
const calculateVolatility = (prices: number[]): number => {
  if (prices.length < 2) {
    return 0;
  }

  // Calcular rendimientos diarios
  const returns = [];
  for (let i = 1; i < prices.length; i++) {
    returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
  }

  // Calcular media
  const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;

  // Calcular desviación estándar
  const squaredDiffs = returns.map(r => Math.pow(r - mean, 2));
  const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / returns.length;

  return Math.sqrt(variance);
};
