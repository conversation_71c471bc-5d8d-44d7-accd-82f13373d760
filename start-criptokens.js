/**
 * Script unificado para iniciar todos los servicios de Criptokens
 *
 * Este script inicia todos los servicios necesarios en el orden correcto:
 * 1. Crypto MCP Server (puerto 3101)
 * 2. Brave Search Server (puerto 3102)
 * 3. Playwright MCP Server (puerto 3103)
 * 4. Technical Analysis MCP Server (puerto 3104)
 * 5. Backend (puerto 3001)
 * 6. Frontend (puerto 5173)
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const config = require('./config');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para iniciar un proceso
function startProcess(name, command, args, cwd, color) {
  log(`Iniciando ${name}...`, colors.bright + color);

  const process = spawn(command, args, {
    cwd,
    shell: true,
    stdio: 'pipe',
  });

  process.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${color}[${name}] ${line}${colors.reset}`);
      }
    });
  });

  process.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${colors.red}[${name} ERROR] ${line}${colors.reset}`);
      }
    });
  });

  process.on('close', (code) => {
    if (code !== 0) {
      log(`${name} se ha detenido con código ${code}`, colors.red);
    } else {
      log(`${name} se ha detenido correctamente`, colors.green);
    }
  });

  return process;
}

// Directorios
const rootDir = __dirname;
const frontendDir = path.join(rootDir, 'criptokens', 'frontend');
const backendDir = path.join(rootDir, config.directories.backend);
const cryptoMcpServerDir = path.join(rootDir, config.directories.cryptoMcp);
const playwrightMcpServerDir = path.join(rootDir, config.directories.playwrightMcp);
const technicalMcpServerDir = path.join(rootDir, 'mcp-technical-analysis');

// Verificar que los directorios existen
const missingDirs = [];

if (!fs.existsSync(frontendDir)) {
  missingDirs.push(`Frontend: ${frontendDir}`);
}

if (!fs.existsSync(backendDir)) {
  missingDirs.push(`Backend: ${backendDir}`);
}

if (!fs.existsSync(cryptoMcpServerDir)) {
  missingDirs.push(`Crypto MCP Server: ${cryptoMcpServerDir}`);
}

if (!fs.existsSync(playwrightMcpServerDir)) {
  missingDirs.push(`Playwright MCP Server: ${playwrightMcpServerDir}`);
}

if (!fs.existsSync(technicalMcpServerDir)) {
  missingDirs.push(`Technical Analysis MCP Server: ${technicalMcpServerDir}`);
}

if (missingDirs.length > 0) {
  log('Error: Los siguientes directorios no existen:', colors.red);
  missingDirs.forEach(dir => log(`- ${dir}`, colors.red));
  log('\nPor favor, verifica las rutas en config.js y asegúrate de que todos los componentes estén instalados.', colors.yellow);
  process.exit(1);
}

log('Iniciando servidores en el orden correcto...', colors.bright);

// Paso 1: Iniciar el servidor MCP de crypto (puerto 3101)
const cryptoMcpProcess = startProcess(
  'Crypto MCP Server',
  'node',
  ['http-server.js'],
  cryptoMcpServerDir,
  colors.magenta
);

// Esperar un momento para que el servidor MCP de crypto se inicie
setTimeout(() => {
  // Paso 2: Iniciar el servidor Brave Search (puerto 3102)
  const braveSearchProcess = startProcess(
    'Brave Search Server',
    'node',
    ['brave-search-server.js'],
    rootDir,
    colors.blue
  );

  // Esperar un momento para que el servidor Brave Search se inicie
  setTimeout(() => {
    // Paso 3: Iniciar el servidor Playwright MCP (puerto 3103)
    const playwrightMcpProcess = startProcess(
      'Playwright MCP Server',
      'node',
      ['dist/server.js'],
      playwrightMcpServerDir,
      colors.cyan
    );

    // Esperar un momento para que el servidor Playwright MCP se inicie
    setTimeout(() => {
      // Paso 4: Iniciar el servidor Technical Analysis MCP (puerto 3104)
      const technicalMcpProcess = startProcess(
        'Technical Analysis MCP Server',
        'node',
        ['src/server.js'],
        technicalMcpServerDir,
        colors.red
      );

      // Esperar un momento para que el servidor Technical Analysis MCP se inicie
      setTimeout(() => {
        // Paso 5: Iniciar el backend (puerto 3001)
        const backendProcess = startProcess(
          'Backend',
          'node',
          ['src/server.js'],
          backendDir,
          colors.green
        );

      // Esperar un momento para que el backend se inicie
      setTimeout(() => {
        // Paso 6: Iniciar el frontend (puerto 5173)
        const frontendProcess = startProcess(
          'Frontend',
          'npm',
          ['run', 'dev'],
          frontendDir,
          colors.yellow
        );

        // Manejar la terminación del proceso principal
        process.on('SIGINT', () => {
          log('\nTerminando todos los procesos...', colors.yellow);

          frontendProcess.kill();
          backendProcess.kill();
          technicalMcpProcess.kill();
          playwrightMcpProcess.kill();
          braveSearchProcess.kill();
          cryptoMcpProcess.kill();

          setTimeout(() => {
            log('Todos los procesos han sido terminados', colors.green);
            process.exit(0);
          }, 1000);
        });

        // Mostrar mensaje de éxito
        log(`
¡Todos los servicios iniciados correctamente!

Servicios disponibles:
- Frontend: ${config.urls.frontend}
- Backend: ${config.urls.backend}
- Crypto MCP Server: ${config.urls.cryptoMcp}
- Brave Search Server: ${config.urls.braveSearchMcp}
- Playwright MCP Server: ${config.urls.playwrightMcp}
- Technical Analysis MCP Server: http://localhost:3104

Presiona Ctrl+C para terminar todos los servidores.
`, colors.green + colors.bright);
      }, 3000); // Esperar 3 segundos para que el backend se inicie
      }, 2000); // Esperar 2 segundos para que el Technical Analysis MCP se inicie
    }, 2000); // Esperar 2 segundos para que el servidor Playwright MCP se inicie
  }, 2000); // Esperar 2 segundos para que el servidor Brave Search se inicie
}, 2000); // Esperar 2 segundos para que el servidor MCP de crypto se inicie
