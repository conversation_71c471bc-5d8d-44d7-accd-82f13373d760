.portfolio-container {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.portfolio-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: var(--border-light);
}

.portfolio-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.portfolio-funds {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.funds-label {
  color: var(--text-dim);
  font-size: 0.9rem;
}

.funds-value {
  font-weight: 600;
  color: var(--text-bright);
}

.add-funds-button {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.4rem 0.75rem;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.add-funds-button:hover {
  box-shadow: 0 0 10px var(--primary-glow);
  transform: translateY(-1px);
}

.portfolio-header h2 {
  font-size: 1.25rem;
  margin: 0;
  color: #333;
}

.portfolio-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  width: 100%;
}

.refresh-button,
.add-asset-button {
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.9rem;
}

.refresh-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.refresh-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.refresh-button:disabled {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-dim);
  cursor: not-allowed;
  border-color: rgba(255, 255, 255, 0.05);
}

.add-asset-button {
  background: var(--gradient-primary);
  color: white;
  position: relative;
  overflow: hidden;
}

.add-asset-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s;
}

.add-asset-button:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-1px);
}

.add-asset-button:hover::before {
  left: 100%;
}

.portfolio-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: var(--border-light);
}

.summary-item {
  display: flex;
  flex-direction: column;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-dim);
  margin-bottom: 0.25rem;
}

.summary-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-bright);
}

.summary-value.positive {
  color: var(--success);
}

.summary-value.negative {
  color: var(--error);
}

.summary-value.date {
  font-size: 0.9375rem;
  font-weight: normal;
}

.portfolio-assets {
  overflow-x: auto;
}

.assets-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9375rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: var(--border-light);
}

.assets-table th {
  text-align: left;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-dim);
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.2);
}

.assets-table td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-medium);
}

.asset-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.asset-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px;
}

.asset-name {
  display: block;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-bright);
}

.asset-symbol {
  display: block;
  font-size: 0.8125rem;
  color: var(--text-dim);
}

.positive {
  color: var(--success);
}

.negative {
  color: var(--error);
}

.remove-asset-button {
  background-color: rgba(255, 59, 48, 0.8);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.remove-asset-button:hover {
  background-color: rgba(255, 59, 48, 1);
  box-shadow: 0 0 10px rgba(255, 59, 48, 0.5);
}

.empty-portfolio {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-dim);
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: var(--border-light);
  min-height: 250px;
}

.empty-portfolio p {
  margin: 0.5rem 0;
  color: var(--text-medium);
  max-width: 500px;
}

.add-asset-form-container {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: var(--border-light);
}

.add-asset-form {
  max-width: 500px;
  margin: 0 auto;
}

.add-asset-form h3 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--text-bright);
  text-align: center;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.form-error {
  color: var(--error);
  background-color: rgba(255, 59, 48, 0.1);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1rem;
  font-size: 0.9375rem;
  border-left: 3px solid var(--error);
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  font-size: 0.9375rem;
  color: var(--text-medium);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  font-size: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--text-bright);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 242, 255, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.cancel-button,
.submit-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.cancel-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-medium);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--text-bright);
}

.submit-button {
  background: var(--gradient-primary);
  color: white;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s;
}

.submit-button:hover {
  box-shadow: 0 0 15px var(--primary-glow);
  transform: translateY(-1px);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.portfolio-loading,
.portfolio-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: var(--text-dim);
  font-style: italic;
  text-align: center;
  padding: 2rem;
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  border: var(--border-light);
}

.portfolio-error {
  color: var(--error);
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), var(--gradient-card);
  border-left: 4px solid var(--error);
}

@media (max-width: 768px) {
  .portfolio-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .portfolio-actions {
    width: 100%;
  }

  .refresh-button,
  .add-asset-button {
    flex: 1;
  }

  .portfolio-summary {
    flex-direction: column;
    gap: 1rem;
  }

  .assets-table {
    font-size: 0.875rem;
  }

  .assets-table th,
  .assets-table td {
    padding: 0.75rem 0.5rem;
  }

  .assets-table th:nth-child(3),
  .assets-table td:nth-child(3),
  .assets-table th:nth-child(4),
  .assets-table td:nth-child(4) {
    display: none;
  }
}
