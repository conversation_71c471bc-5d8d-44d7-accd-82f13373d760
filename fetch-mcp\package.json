{"name": "fetch", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "license": "MIT", "author": "zcaceres (@zachcaceres zach.dev)", "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "dev": "tsc --watch", "start": "node dist/index.js", "test": "jest"}, "keywords": [], "dependencies": {"@modelcontextprotocol/sdk": "^1.0.4", "jsdom": "^25.0.1", "turndown": "^7.2.0", "zod": "^3.24.1"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.2", "@types/turndown": "^5.0.5", "jest": "^29.7.0", "shx": "^0.4.0", "ts-jest": "^29.2.5", "typescript": "^5.7.2"}}