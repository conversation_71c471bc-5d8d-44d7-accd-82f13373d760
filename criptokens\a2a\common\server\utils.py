from typing import List, Optional
from ..types import ContentTypeNotSupportedError, JSONRPCResponse


def are_modalities_compatible(
    client_modes: Optional[List[str]], server_modes: List[str]
) -> bool:
    """Check if client and server modalities are compatible.
    
    Args:
        client_modes: List of modalities supported by the client
        server_modes: List of modalities supported by the server
        
    Returns:
        True if compatible, False otherwise
    """
    # If client doesn't specify modes, assume it accepts all modes
    if client_modes is None or len(client_modes) == 0:
        return True
    
    # Check if there's at least one common modality
    return any(mode in server_modes for mode in client_modes)


def new_incompatible_types_error(request_id: str) -> JSONRPCResponse:
    """Create a new incompatible types error response.
    
    Args:
        request_id: The ID of the request
        
    Returns:
        A JSONRPCResponse with an error
    """
    return JSONRPCResponse(
        id=request_id,
        error=ContentTypeNotSupportedError(),
    )
