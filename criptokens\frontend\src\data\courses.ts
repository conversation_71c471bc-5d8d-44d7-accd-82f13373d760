// Datos de ejemplo para los cursos de la Academia Cripto

export interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
  duration: string;
  instructor: {
    name: string;
    avatar: string;
  };
  modules: {
    id: string;
    title: string;
  }[];
}

export const availableCourses: Course[] = [
  {
    id: 'crypto-fundamentals',
    title: 'Fundamentos de Criptomonedas',
    description: 'Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.',
    level: 'beginner',
    duration: '4 horas',
    instructor: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    modules: [
      {
        id: 'module-1',
        title: 'Introducción a las Criptomonedas'
      },
      {
        id: 'module-2',
        title: 'Blockchain: La Tecnología Detrás'
      },
      {
        id: 'module-3',
        title: 'Bitcoin: La Primera Criptomoneda'
      },
      {
        id: 'module-4',
        title: 'Wallets y Seguridad'
      },
      {
        id: 'module-5',
        title: 'Exchanges y Cómo Comprar Criptomonedas'
      },
      {
        id: 'module-6',
        title: 'Aspectos Legales y Fiscales'
      },
      {
        id: 'module-7',
        title: 'El Futuro de las Criptomonedas'
      }
    ]
  },
  {
    id: 'defi-basics',
    title: 'Introducción a DeFi',
    description: 'Descubre el mundo de las Finanzas Descentralizadas (DeFi) y cómo están revolucionando el sistema financiero tradicional.',
    level: 'intermediate',
    duration: '6 horas',
    instructor: {
      name: 'Laura Martínez',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    modules: [
      {
        id: 'module-1',
        title: '¿Qué es DeFi?'
      },
      {
        id: 'module-2',
        title: 'Protocolos de Préstamos'
      },
      {
        id: 'module-3',
        title: 'Exchanges Descentralizados (DEX)'
      },
      {
        id: 'module-4',
        title: 'Yield Farming'
      },
      {
        id: 'module-5',
        title: 'Stablecoins'
      },
      {
        id: 'module-6',
        title: 'Riesgos y Consideraciones'
      }
    ]
  },
  {
    id: 'technical-analysis',
    title: 'Análisis Técnico para Criptomonedas',
    description: 'Aprende a analizar gráficos y patrones de precios para tomar decisiones de trading más informadas en el mercado de criptomonedas.',
    level: 'intermediate',
    duration: '8 horas',
    instructor: {
      name: 'Miguel Ángel Torres',
      avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
    },
    modules: [
      {
        id: 'module-1',
        title: 'Fundamentos del Análisis Técnico'
      },
      {
        id: 'module-2',
        title: 'Patrones de Velas Japonesas'
      },
      {
        id: 'module-3',
        title: 'Indicadores Técnicos'
      },
      {
        id: 'module-4',
        title: 'Soportes y Resistencias'
      },
      {
        id: 'module-5',
        title: 'Tendencias y Canales'
      },
      {
        id: 'module-6',
        title: 'Estrategias de Trading'
      },
      {
        id: 'module-7',
        title: 'Gestión de Riesgos'
      },
      {
        id: 'module-8',
        title: 'Psicología del Trading'
      }
    ]
  },
  {
    id: 'ethereum-smart-contracts',
    title: 'Desarrollo de Smart Contracts en Ethereum',
    description: 'Aprende a programar contratos inteligentes en la blockchain de Ethereum utilizando Solidity y herramientas de desarrollo modernas.',
    level: 'advanced',
    duration: '12 horas',
    instructor: {
      name: 'Alejandro Gómez',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg'
    },
    modules: [
      {
        id: 'module-1',
        title: 'Introducción a Ethereum y Smart Contracts'
      },
      {
        id: 'module-2',
        title: 'Fundamentos de Solidity'
      },
      {
        id: 'module-3',
        title: 'Entorno de Desarrollo'
      },
      {
        id: 'module-4',
        title: 'Tokens ERC-20'
      },
      {
        id: 'module-5',
        title: 'Tokens ERC-721 (NFTs)'
      },
      {
        id: 'module-6',
        title: 'Seguridad en Smart Contracts'
      },
      {
        id: 'module-7',
        title: 'Testing y Deployment'
      },
      {
        id: 'module-8',
        title: 'Integración con DApps'
      },
      {
        id: 'module-9',
        title: 'Optimización de Gas'
      },
      {
        id: 'module-10',
        title: 'Proyecto Final'
      }
    ]
  },
  {
    id: 'nft-masterclass',
    title: 'NFTs: Creación, Compra y Venta',
    description: 'Todo lo que necesitas saber sobre los tokens no fungibles (NFTs): cómo crearlos, comprarlos, venderlos y entender su valor en el mercado.',
    level: 'beginner',
    duration: '5 horas',
    instructor: {
      name: 'Sofía Ramírez',
      avatar: 'https://randomuser.me/api/portraits/women/28.jpg'
    },
    modules: [
      {
        id: 'module-1',
        title: '¿Qué son los NFTs?'
      },
      {
        id: 'module-2',
        title: 'Marketplaces de NFTs'
      },
      {
        id: 'module-3',
        title: 'Creación de NFTs'
      },
      {
        id: 'module-4',
        title: 'Compra y Venta de NFTs'
      },
      {
        id: 'module-5',
        title: 'NFTs en el Arte y Entretenimiento'
      },
      {
        id: 'module-6',
        title: 'El Futuro de los NFTs'
      }
    ]
  },
  {
    id: 'crypto-security',
    title: 'Seguridad en Criptomonedas',
    description: 'Aprende a proteger tus activos digitales con las mejores prácticas de seguridad y evita estafas y hackeos en el mundo cripto.',
    level: 'intermediate',
    duration: '4 horas',
    instructor: {
      name: 'Daniel Herrera',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg'
    },
    modules: [
      {
        id: 'module-1',
        title: 'Amenazas Comunes en Criptomonedas'
      },
      {
        id: 'module-2',
        title: 'Wallets: Tipos y Seguridad'
      },
      {
        id: 'module-3',
        title: 'Claves Privadas y Frases Semilla'
      },
      {
        id: 'module-4',
        title: 'Autenticación de Dos Factores'
      },
      {
        id: 'module-5',
        title: 'Phishing y Estafas Comunes'
      },
      {
        id: 'module-6',
        title: 'Plan de Seguridad Personal'
      }
    ]
  }
];

export default availableCourses;
