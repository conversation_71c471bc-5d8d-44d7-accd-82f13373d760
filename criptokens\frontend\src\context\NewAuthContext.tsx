import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, sendPasswordResetEmail, updateProfile, onAuthStateChanged } from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { auth, db } from '../firebase-init';

// Tipos para los datos de usuario
export interface UserData {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  createdAt: Date;
}

interface AuthContextType {
  currentUser: User | null;
  userData: UserData | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<User>;
  register: (email: string, password: string, displayName: string) => Promise<UserData>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

// Funciones de autenticación
const loginUser = async (email: string, password: string): Promise<User> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error: any) {
    console.error('Error al iniciar sesión:', error);
    throw new Error(error.message);
  }
};

const registerUser = async (email: string, password: string, displayName: string): Promise<UserData> => {
  try {
    // Crear usuario en Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Actualizar el perfil con el nombre de usuario
    await updateProfile(user, { displayName });

    // Crear documento de usuario en Firestore
    const userData: UserData = {
      uid: user.uid,
      email: user.email || email,
      displayName: displayName,
      createdAt: new Date()
    };

    try {
      // Intentar crear el documento de usuario en Firestore
      await setDoc(doc(db, 'Users', user.uid), userData);

      // Intentar crear la colección de cartera vacía para el usuario
      await setDoc(doc(db, 'Portafolio', user.uid), {
        assets: [],
        lastUpdated: new Date()
      });
    } catch (firestoreError: any) {
      // Si hay un error con Firestore, registrarlo pero no fallar el registro
      console.error('Error al crear documentos en Firestore:', firestoreError);
      // Podemos seguir adelante ya que el usuario se ha creado en Authentication
    }

    return userData;
  } catch (error: any) {
    console.error('Error al registrar usuario:', error);
    throw new Error(error.message);
  }
};

const logoutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error: any) {
    console.error('Error al cerrar sesión:', error);
    throw new Error(error.message);
  }
};

const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error: any) {
    console.error('Error al enviar correo de restablecimiento:', error);
    throw new Error(error.message);
  }
};

const getUserData = async (uid: string): Promise<UserData | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'Users', uid));
    if (userDoc.exists()) {
      return userDoc.data() as UserData;
    }
    return null;
  } catch (error: any) {
    console.error('Error al obtener datos del usuario:', error);
    throw new Error(error.message);
  }
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);

      if (user) {
        try {
          const data = await getUserData(user.uid);
          setUserData(data);
        } catch (error) {
          console.error('Error al obtener datos del usuario:', error);
        }
      } else {
        setUserData(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userData,
    loading,
    login: loginUser,
    register: registerUser,
    logout: logoutUser,
    resetPassword
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
