.featured-news-container {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.featured-news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.featured-news-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.view-all-button {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-button:hover {
  background-color: var(--primary-color);
  color: white;
}

.featured-news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.featured-news-loading,
.featured-news-error,
.featured-news-no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
  text-align: center;
  color: var(--text-secondary-color);
}

.featured-news-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-left-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.featured-news-error i,
.featured-news-no-results i {
  font-size: 36px;
  margin-bottom: 12px;
  color: var(--text-tertiary-color);
}

/* Responsive */
@media (max-width: 768px) {
  .featured-news-container {
    padding: 16px;
  }
  
  .featured-news-header h2 {
    font-size: 1.3rem;
  }
  
  .featured-news-grid {
    grid-template-columns: 1fr;
  }
}
