// Importar imágenes
import placeholderImage from '../assets/images/placeholder.svg';
import newsPlaceholderImage from '../assets/images/news-placeholder.svg';

// Exportar rutas de imágenes
export const PLACEHOLDER_IMAGE = placeholderImage;
export const NEWS_PLACEHOLDER_IMAGE = newsPlaceholderImage;

// Función para obtener imagen de criptomoneda o usar placeholder
export const getCryptoImage = (imageUrl: string | undefined | null): string => {
  return imageUrl || PLACEHOLDER_IMAGE;
};

// Función para obtener imagen de noticia o usar placeholder
export const getNewsImage = (imageUrl: string | undefined | null): string => {
  return imageUrl || NEWS_PLACEHOLDER_IMAGE;
};
