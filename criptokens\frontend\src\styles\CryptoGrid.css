.crypto-grid-container {
  width: 100%;
}

.crypto-grid-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border-radius: 12px;
  border: 1px solid rgba(64, 220, 255, 0.3);
  background: rgba(15, 15, 35, 0.5);
  color: white;
  font-size: 16px;
  transition: all 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: rgba(64, 220, 255, 0.6);
  box-shadow: 0 0 15px rgba(64, 220, 255, 0.2);
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  pointer-events: none;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.sort-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.sort-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sort-button {
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid rgba(64, 220, 255, 0.2);
  background: rgba(15, 15, 35, 0.5);
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.sort-button:hover {
  background: rgba(64, 220, 255, 0.1);
  color: white;
}

.sort-button.active {
  background: rgba(64, 220, 255, 0.2);
  color: white;
  border-color: rgba(64, 220, 255, 0.4);
}

.crypto-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.crypto-grid-item {
  transition: all 0.3s;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: rgba(15, 15, 35, 0.5);
  border-radius: 16px;
  border: 1px solid rgba(64, 220, 255, 0.2);
  text-align: center;
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.no-results h3 {
  font-size: 20px;
  margin: 0 0 10px;
  color: white;
}

.no-results p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Animaciones */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.crypto-grid-item {
  animation: fadeInUp 0.5s forwards;
  animation-delay: calc(var(--index) * 0.05s);
  opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .crypto-grid-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .sort-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .sort-buttons {
    width: 100%;
    justify-content: space-between;
  }
  
  .sort-button {
    flex: 1;
    text-align: center;
    font-size: 12px;
    padding: 8px 6px;
  }
}
