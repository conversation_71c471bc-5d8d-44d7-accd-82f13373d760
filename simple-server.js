/**
 * Servidor HTTP simple para pruebas
 */
const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200);
  res.end('Hello World');
});

server.listen(12345, () => {
  console.log('Servidor iniciado en el puerto 12345');
});

// Manejar la terminación del script
process.on('SIGINT', () => {
  console.log('Deteniendo el servidor...');
  server.close();
  process.exit(0);
});
