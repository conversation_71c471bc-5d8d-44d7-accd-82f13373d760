import React, { useRef } from 'react';
import { useAuth } from '../../context/NewAuthContext';
import html2canvas from 'html2canvas';
import '../../styles/academy/Certificate.css';

interface CertificateProps {
  courseId: string;
  courseTitle: string;
  completionDate: Date;
  instructor: string;
}

const Certificate: React.FC<CertificateProps> = ({
  courseId,
  courseTitle,
  completionDate,
  instructor
}) => {
  const { currentUser } = useAuth();
  const certificateRef = useRef<HTMLDivElement>(null);

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const generateCertificateId = (): string => {
    // En una implementación real, esto vendría del backend
    const randomPart = Math.random().toString(36).substring(2, 10).toUpperCase();
    return `CERT-${courseId.substring(0, 4).toUpperCase()}-${randomPart}`;
  };

  const downloadCertificate = async () => {
    if (!certificateRef.current) return;
    
    try {
      const canvas = await html2canvas(certificateRef.current, {
        scale: 2,
        logging: false,
        useCORS: true
      });
      
      const image = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = image;
      link.download = `certificado-${courseId}.png`;
      link.click();
    } catch (error) {
      console.error('Error al generar el certificado:', error);
    }
  };

  const shareCertificate = () => {
    // En una implementación real, esto permitiría compartir en redes sociales
    alert('Funcionalidad de compartir en desarrollo');
  };

  return (
    <div className="certificate-container">
      <div className="certificate" ref={certificateRef}>
        <div className="certificate-header">
          <div className="certificate-logo">
            <img src="/images/academy-logo.svg" alt="Academia Cripto Logo" />
          </div>
          <h1 className="certificate-title">Certificado de Finalización</h1>
        </div>
        
        <div className="certificate-content">
          <p className="certificate-text">Este certificado acredita que</p>
          <h2 className="certificate-name">{currentUser?.displayName || 'Estudiante'}</h2>
          <p className="certificate-text">ha completado satisfactoriamente el curso</p>
          <h3 className="certificate-course">{courseTitle}</h3>
          <p className="certificate-date">Fecha de finalización: {formatDate(completionDate)}</p>
        </div>
        
        <div className="certificate-footer">
          <div className="certificate-signature">
            <div className="signature-line"></div>
            <p className="signature-name">{instructor}</p>
            <p className="signature-title">Instructor</p>
          </div>
          
          <div className="certificate-verification">
            <p className="verification-id">ID: {generateCertificateId()}</p>
            <p className="verification-text">Verificable en criptokens.com/verify</p>
          </div>
        </div>
        
        <div className="certificate-background">
          <div className="certificate-border"></div>
          <div className="certificate-watermark"></div>
        </div>
      </div>
      
      <div className="certificate-actions">
        <button className="btn-primary" onClick={downloadCertificate}>
          <i className="fas fa-download"></i> Descargar Certificado
        </button>
        <button className="btn-secondary" onClick={shareCertificate}>
          <i className="fas fa-share-alt"></i> Compartir Certificado
        </button>
      </div>
    </div>
  );
};

export default Certificate;
