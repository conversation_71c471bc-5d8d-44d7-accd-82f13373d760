import React, { useState, useEffect, useRef } from 'react';
import { PortfolioAsset } from '../../services/portfolio.service';
import { usePortfolio } from '../../hooks/usePortfolio';
import LoadingSpinner from '../common/LoadingSpinner';
import '../../styles/portfolio/AdvancedPortfolioChart.css';

interface AdvancedPortfolioChartProps {
  portfolio: PortfolioAsset[];
}

// Componente para renderizar una gráfica avanzada
const AdvancedPortfolioChart: React.FC<AdvancedPortfolioChartProps> = ({ portfolio }) => {
  const { historicalData, isLoadingHistorical, error, loadHistoricalData } = usePortfolio();
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '180d' | '1y' | 'all'>('30d');
  const [showMovingAverage, setShowMovingAverage] = useState<boolean>(false);
  const chartRef = useRef<HTMLDivElement>(null);

  // Cargar datos históricos cuando cambia el rango de tiempo
  useEffect(() => {
    const days = 
      timeRange === '7d' ? 7 :
      timeRange === '30d' ? 30 :
      timeRange === '90d' ? 90 :
      timeRange === '180d' ? 180 :
      timeRange === '1y' ? 365 : 
      730; // 'all' - 2 años
    
    loadHistoricalData(days);
  }, [timeRange, loadHistoricalData]);

  // Calcular la media móvil de 7 días
  const calculateMovingAverage = (data: { date: string; value: number }[], window: number = 7) => {
    if (!data || data.length < window) return [];
    
    const result = [];
    for (let i = window - 1; i < data.length; i++) {
      let sum = 0;
      for (let j = 0; j < window; j++) {
        sum += data[i - j].value;
      }
      result.push({
        date: data[i].date,
        value: sum / window
      });
    }
    return result;
  };

  const movingAverageData = calculateMovingAverage(historicalData || []);

  // Formatear fechas para mostrar solo el día y mes
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return `${date.getDate()}/${date.getMonth() + 1}`;
  };

  // Determinar si la tendencia es positiva o negativa
  const getTrend = (data: { date: string; value: number }[]) => {
    if (!data || data.length < 2) return 'neutral';
    const firstValue = data[0].value;
    const lastValue = data[data.length - 1].value;
    return lastValue >= firstValue ? 'positive' : 'negative';
  };

  const trend = getTrend(historicalData || []);

  // Encontrar el valor máximo para escalar la gráfica
  const getMaxValue = () => {
    if (!historicalData || historicalData.length === 0) return 0;
    
    let maxValues = [...historicalData.map(item => item.value)];
    
    if (showMovingAverage && movingAverageData.length > 0) {
      maxValues = [...maxValues, ...movingAverageData.map(item => item.value)];
    }
    
    return Math.max(...maxValues);
  };

  const maxValue = getMaxValue();

  if (portfolio.length === 0) {
    return (
      <div className="advanced-portfolio-chart empty-chart">
        <h3>Rendimiento del Portafolio</h3>
        <div className="empty-chart-message">
          <p>Añade activos a tu portafolio para ver el rendimiento</p>
        </div>
      </div>
    );
  }

  if (isLoadingHistorical) {
    return (
      <div className="advanced-portfolio-chart">
        <h3>Rendimiento del Portafolio</h3>
        <div className="chart-loading">
          <LoadingSpinner message="Cargando datos históricos..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="advanced-portfolio-chart">
        <h3>Rendimiento del Portafolio</h3>
        <div className="chart-error">
          <p>Error al cargar los datos históricos</p>
          <p className="error-details">{error}</p>
        </div>
      </div>
    );
  }

  if (!historicalData || historicalData.length < 2) {
    return (
      <div className="advanced-portfolio-chart empty-chart">
        <h3>Rendimiento del Portafolio</h3>
        <div className="empty-chart-message">
          <p>No hay suficientes datos históricos disponibles para mostrar el rendimiento</p>
          <p>Añade más activos o espera a que se acumulen más datos</p>
        </div>
      </div>
    );
  }

  // Calcular el cambio porcentual
  const firstValue = historicalData[0].value;
  const lastValue = historicalData[historicalData.length - 1].value;
  const percentChange = ((lastValue - firstValue) / firstValue) * 100;

  return (
    <div className="advanced-portfolio-chart">
      <div className="chart-header">
        <h3>Rendimiento del Portafolio</h3>
        <div className="chart-value-info">
          <span className={`current-value ${trend}`}>
            ${lastValue.toLocaleString(undefined, { maximumFractionDigits: 2 })}
          </span>
          <span className={`percent-change ${trend}`}>
            {percentChange >= 0 ? '+' : ''}{percentChange.toFixed(2)}%
          </span>
        </div>
      </div>
      
      <div className="chart-controls">
        <div className="time-range-selector">
          <button 
            className={timeRange === '7d' ? 'active' : ''} 
            onClick={() => setTimeRange('7d')}
          >
            7D
          </button>
          <button 
            className={timeRange === '30d' ? 'active' : ''} 
            onClick={() => setTimeRange('30d')}
          >
            1M
          </button>
          <button 
            className={timeRange === '90d' ? 'active' : ''} 
            onClick={() => setTimeRange('90d')}
          >
            3M
          </button>
          <button 
            className={timeRange === '180d' ? 'active' : ''} 
            onClick={() => setTimeRange('180d')}
          >
            6M
          </button>
          <button 
            className={timeRange === '1y' ? 'active' : ''} 
            onClick={() => setTimeRange('1y')}
          >
            1A
          </button>
          <button 
            className={timeRange === 'all' ? 'active' : ''} 
            onClick={() => setTimeRange('all')}
          >
            Todo
          </button>
        </div>
        
        <div className="chart-options">
          <label className="moving-average-toggle">
            <input 
              type="checkbox" 
              checked={showMovingAverage} 
              onChange={() => setShowMovingAverage(!showMovingAverage)} 
            />
            <span>Media Móvil (7D)</span>
          </label>
        </div>
      </div>
      
      <div className="chart-container" ref={chartRef}>
        <div className="chart-grid">
          <div className="horizontal-lines">
            <div className="grid-line"></div>
            <div className="grid-line"></div>
            <div className="grid-line"></div>
            <div className="grid-line"></div>
          </div>
        </div>
        
        <div className="chart-lines">
          {/* Línea principal */}
          <svg className="chart-svg" viewBox={`0 0 ${historicalData.length} 100`} preserveAspectRatio="none">
            <path
              className={`chart-line ${trend}`}
              d={historicalData.map((point, index) => {
                const x = index;
                const y = 100 - (point.value / maxValue) * 100;
                return index === 0 ? `M ${x},${y}` : `L ${x},${y}`;
              }).join(' ')}
            />
            
            {/* Área bajo la curva */}
            <path
              className={`chart-area ${trend}`}
              d={`
                ${historicalData.map((point, index) => {
                  const x = index;
                  const y = 100 - (point.value / maxValue) * 100;
                  return index === 0 ? `M ${x},${y}` : `L ${x},${y}`;
                }).join(' ')}
                L ${historicalData.length - 1},100
                L 0,100
                Z
              `}
            />
            
            {/* Media móvil */}
            {showMovingAverage && movingAverageData.length > 0 && (
              <path
                className="moving-average-line"
                d={movingAverageData.map((point, index) => {
                  // Ajustar el índice para alinear con los datos originales
                  const x = index + 6; // window - 1
                  const y = 100 - (point.value / maxValue) * 100;
                  return index === 0 ? `M ${x},${y}` : `L ${x},${y}`;
                }).join(' ')}
              />
            )}
          </svg>
        </div>
        
        <div className="chart-labels">
          {historicalData.filter((_, i) => i % Math.ceil(historicalData.length / 6) === 0).map((point, index) => (
            <div key={index} className="date-label" style={{ left: `${(index * Math.ceil(historicalData.length / 6)) / (historicalData.length - 1) * 100}%` }}>
              {formatDate(point.date)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdvancedPortfolioChart;
