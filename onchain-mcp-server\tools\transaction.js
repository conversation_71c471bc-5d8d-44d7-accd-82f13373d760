/**
 * Herramientas MCP relacionadas con transacciones
 */
const etherscanService = require('../services/etherscan');
const config = require('../config');

module.exports = {
  /**
   * Obtiene las transacciones de una cartera
   */
  getTransactions: {
    description: 'Get transactions for a wallet address',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        page: {
          type: 'integer',
          description: 'Page number',
          default: 1
        },
        offset: {
          type: 'integer',
          description: 'Number of transactions per page',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address']
    },
    handler: async ({ address, page = 1, offset = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getTransactions(address, page, offset);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene las transferencias de tokens de una cartera
   */
  getTokenTransfers: {
    description: 'Get token transfers for a wallet address',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        tokenAddress: {
          type: 'string',
          description: 'The token contract address (optional)',
          default: null
        },
        page: {
          type: 'integer',
          description: 'Page number',
          default: 1
        },
        offset: {
          type: 'integer',
          description: 'Number of transfers per page',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address']
    },
    handler: async ({ address, tokenAddress = null, page = 1, offset = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getTokenTransfers(address, tokenAddress, page, offset);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene las transferencias de una criptomoneda específica
   */
  getCryptoTransfers: {
    description: 'Get transfers of a specific cryptocurrency',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        cryptoId: {
          type: 'string',
          description: 'The cryptocurrency ID (bitcoin, ethereum, etc.)'
        },
        page: {
          type: 'integer',
          description: 'Page number',
          default: 1
        },
        offset: {
          type: 'integer',
          description: 'Number of transfers per page',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address', 'cryptoId']
    },
    handler: async ({ address, cryptoId, page = 1, offset = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener la dirección del token para la criptomoneda
        const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
        
        if (!tokenAddress) {
          throw new Error(`Token address not found for ${cryptoId}`);
        }
        
        return await etherscanService.getTokenTransfers(address, tokenAddress, page, offset);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Analiza las transacciones recientes de una cartera
   */
  analyzeTransactions: {
    description: 'Analyze recent transactions of a wallet',
    parameters: {
      type: 'object',
      properties: {
        address: {
          type: 'string',
          description: 'The wallet address'
        },
        days: {
          type: 'integer',
          description: 'Number of days to analyze',
          default: 30
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance, solana)',
          default: config.defaultChain
        }
      },
      required: ['address']
    },
    handler: async ({ address, days = 30, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener transacciones (máximo 100)
        const txResponse = await etherscanService.getTransactions(address, 1, 100);
        const transactions = txResponse.transactions;
        
        // Obtener transferencias de tokens (máximo 100)
        const transfersResponse = await etherscanService.getTokenTransfers(address, null, 1, 100);
        const transfers = transfersResponse.transfers;
        
        // Filtrar por fecha (últimos X días)
        const now = Date.now();
        const timeThreshold = now - (days * 24 * 60 * 60 * 1000);
        
        const recentTxs = transactions.filter(tx => tx.timestamp >= timeThreshold);
        const recentTransfers = transfers.filter(transfer => transfer.timestamp >= timeThreshold);
        
        // Análisis básico
        const ethSent = recentTxs
          .filter(tx => tx.from.toLowerCase() === address.toLowerCase())
          .reduce((sum, tx) => sum + tx.value, 0);
          
        const ethReceived = recentTxs
          .filter(tx => tx.to.toLowerCase() === address.toLowerCase())
          .reduce((sum, tx) => sum + tx.value, 0);
        
        // Agrupar transferencias por token
        const tokenActivity = {};
        
        recentTransfers.forEach(transfer => {
          const tokenSymbol = transfer.tokenSymbol;
          
          if (!tokenActivity[tokenSymbol]) {
            tokenActivity[tokenSymbol] = {
              symbol: tokenSymbol,
              name: transfer.tokenName,
              sent: 0,
              received: 0,
              totalTransfers: 0
            };
          }
          
          if (transfer.from.toLowerCase() === address.toLowerCase()) {
            tokenActivity[tokenSymbol].sent += transfer.value;
          }
          
          if (transfer.to.toLowerCase() === address.toLowerCase()) {
            tokenActivity[tokenSymbol].received += transfer.value;
          }
          
          tokenActivity[tokenSymbol].totalTransfers++;
        });
        
        return {
          address,
          period: `Last ${days} days`,
          ethActivity: {
            sent: ethSent,
            received: ethReceived,
            netFlow: ethReceived - ethSent,
            totalTransactions: recentTxs.length
          },
          tokenActivity: Object.values(tokenActivity),
          totalEthTransactions: recentTxs.length,
          totalTokenTransfers: recentTransfers.length,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  }
};
