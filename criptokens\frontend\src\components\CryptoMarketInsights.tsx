import React, { useEffect, useRef, useState } from 'react';
import { getTopCryptocurrencies, getGlobalMarketData } from '../services/api';
import '../styles/CryptoMarketInsights.css';

interface CryptoMarketInsightsProps {
  animate?: boolean;
}

const CryptoMarketInsights: React.FC<CryptoMarketInsightsProps> = ({ animate = true }) => {
  const [cryptoData, setCryptoData] = useState<any[]>([]);
  const [marketData, setMarketData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'trends' | 'heatmap'>('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState<'24h' | '7d' | '30d'>('24h');
  
  const heatmapRef = useRef<HTMLDivElement>(null);
  const trendLinesRef = useRef<HTMLDivElement>(null);

  // Cargar datos
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Obtener las principales criptomonedas
        const topCryptos = await getTopCryptocurrencies(20);
        setCryptoData(topCryptos);
        
        // Obtener datos del mercado global
        const globalData = await getGlobalMarketData();
        setMarketData(globalData.data);
      } catch (error) {
        console.error('Error al cargar datos del mercado:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
    
    // Actualizar datos cada 5 minutos
    const interval = setInterval(fetchData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  // Crear heatmap
  useEffect(() => {
    if (!heatmapRef.current || !cryptoData.length || activeTab !== 'heatmap') return;
    
    const container = heatmapRef.current;
    container.innerHTML = '';
    
    // Ordenar por capitalización de mercado
    const sortedData = [...cryptoData].sort((a, b) => b.market_cap - a.market_cap);
    
    // Crear elementos del heatmap
    sortedData.forEach(crypto => {
      const tile = document.createElement('div');
      tile.className = 'heatmap-tile';
      
      // Tamaño basado en capitalización de mercado (logarítmico)
      const marketCapLog = Math.log10(crypto.market_cap);
      const size = Math.max(30, Math.min(100, marketCapLog * 10));
      tile.style.width = `${size}px`;
      tile.style.height = `${size}px`;
      
      // Color basado en cambio de precio
      const priceChange = crypto.price_change_percentage_24h;
      let backgroundColor;
      
      if (priceChange > 0) {
        // Verde con intensidad basada en el cambio
        const intensity = Math.min(255, Math.floor(priceChange * 10) + 100);
        backgroundColor = `rgba(0, ${intensity}, 100, 0.7)`;
      } else {
        // Rojo con intensidad basada en el cambio
        const intensity = Math.min(255, Math.floor(Math.abs(priceChange) * 10) + 100);
        backgroundColor = `rgba(${intensity}, 0, 50, 0.7)`;
      }
      
      tile.style.backgroundColor = backgroundColor;
      
      // Contenido del tile
      tile.innerHTML = `
        <div class="tile-content">
          <img src="${crypto.image}" alt="${crypto.name}" class="tile-icon" />
          <div class="tile-symbol">${crypto.symbol.toUpperCase()}</div>
          <div class="tile-change ${priceChange >= 0 ? 'positive' : 'negative'}">
            ${priceChange >= 0 ? '▲' : '▼'} ${Math.abs(priceChange).toFixed(1)}%
          </div>
        </div>
      `;
      
      // Tooltip
      tile.setAttribute('data-tooltip', `
        ${crypto.name} (${crypto.symbol.toUpperCase()})
        Precio: $${crypto.current_price.toLocaleString()}
        Cap. Mercado: $${(crypto.market_cap / 1e9).toFixed(2)}B
        Cambio 24h: ${priceChange.toFixed(2)}%
      `);
      
      // Añadir al contenedor
      container.appendChild(tile);
      
      // Animación de aparición
      if (animate) {
        tile.style.opacity = '0';
        tile.style.transform = 'scale(0.8)';
        
        setTimeout(() => {
          tile.style.opacity = '1';
          tile.style.transform = 'scale(1)';
        }, 100 + Math.random() * 500);
      }
    });
  }, [cryptoData, activeTab, animate]);

  // Crear gráficos de tendencias
  useEffect(() => {
    if (!trendLinesRef.current || !cryptoData.length || activeTab !== 'trends') return;
    
    const container = trendLinesRef.current;
    container.innerHTML = '';
    
    // Tomar las 5 principales criptomonedas
    const topCryptos = [...cryptoData].sort((a, b) => b.market_cap - a.market_cap).slice(0, 5);
    
    // Crear gráfico para cada criptomoneda
    topCryptos.forEach((crypto, index) => {
      const trendContainer = document.createElement('div');
      trendContainer.className = 'trend-container';
      
      // Datos de la línea de tendencia (usar sparkline si está disponible)
      const sparklineData = crypto.sparkline_in_7d?.price || [];
      const hasSparkline = sparklineData.length > 0;
      
      // Crear SVG para la línea de tendencia
      const svgWidth = 200;
      const svgHeight = 60;
      
      let pathData = '';
      
      if (hasSparkline) {
        // Normalizar datos para el SVG
        const minValue = Math.min(...sparklineData);
        const maxValue = Math.max(...sparklineData);
        const range = maxValue - minValue;
        
        // Crear puntos del path
        const points = sparklineData.map((value: number, i: number) => {
          const x = (i / (sparklineData.length - 1)) * svgWidth;
          const y = svgHeight - ((value - minValue) / range) * svgHeight;
          return `${x},${y}`;
        });
        
        pathData = `M${points.join(' L')}`;
      } else {
        // Si no hay sparkline, crear una línea simulada
        const priceChange = crypto.price_change_percentage_24h;
        const direction = priceChange >= 0 ? -1 : 1; // Invertido porque en SVG, y=0 está arriba
        
        // Crear una curva simple basada en el cambio de precio
        const controlPointY = svgHeight / 2 + (direction * svgHeight / 4);
        pathData = `M0,${svgHeight / 2} Q${svgWidth / 2},${controlPointY} ${svgWidth},${svgHeight / 2 + (direction * svgHeight / 3)}`;
      }
      
      // Determinar color basado en el cambio de precio
      const priceChange = crypto.price_change_percentage_24h;
      const strokeColor = priceChange >= 0 ? '#00c853' : '#ff5252';
      
      // Crear el SVG
      const svg = `
        <svg width="${svgWidth}" height="${svgHeight}" class="trend-svg">
          <path d="${pathData}" stroke="${strokeColor}" stroke-width="2" fill="none" />
          <path d="${pathData}" stroke="${strokeColor}" stroke-width="6" stroke-opacity="0.1" fill="none" />
        </svg>
      `;
      
      // Contenido del contenedor de tendencia
      trendContainer.innerHTML = `
        <div class="trend-info">
          <img src="${crypto.image}" alt="${crypto.name}" class="trend-icon" />
          <div class="trend-name">${crypto.name}</div>
          <div class="trend-symbol">${crypto.symbol.toUpperCase()}</div>
        </div>
        <div class="trend-chart">
          ${svg}
        </div>
        <div class="trend-data">
          <div class="trend-price">$${crypto.current_price.toLocaleString()}</div>
          <div class="trend-change ${priceChange >= 0 ? 'positive' : 'negative'}">
            ${priceChange >= 0 ? '▲' : '▼'} ${Math.abs(priceChange).toFixed(2)}%
          </div>
        </div>
      `;
      
      // Añadir al contenedor
      container.appendChild(trendContainer);
      
      // Animación de aparición
      if (animate) {
        trendContainer.style.opacity = '0';
        trendContainer.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
          trendContainer.style.opacity = '1';
          trendContainer.style.transform = 'translateY(0)';
        }, 100 + index * 150);
      }
    });
  }, [cryptoData, activeTab, animate]);

  // Renderizar indicadores del mercado
  const renderMarketIndicators = () => {
    if (!marketData) return null;
    
    const totalMarketCap = marketData.total_market_cap.usd;
    const totalVolume = marketData.total_volume.usd;
    const btcDominance = marketData.market_cap_percentage.btc;
    const ethDominance = marketData.market_cap_percentage.eth;
    const marketCapChange = marketData.market_cap_change_percentage_24h_usd;
    
    return (
      <div className="market-indicators">
        <div className="indicator-card">
          <div className="indicator-title">Capitalización Total</div>
          <div className="indicator-value">${(totalMarketCap / 1e12).toFixed(2)}T</div>
          <div className={`indicator-change ${marketCapChange >= 0 ? 'positive' : 'negative'}`}>
            {marketCapChange >= 0 ? '▲' : '▼'} {Math.abs(marketCapChange).toFixed(2)}%
          </div>
        </div>
        
        <div className="indicator-card">
          <div className="indicator-title">Volumen 24h</div>
          <div className="indicator-value">${(totalVolume / 1e9).toFixed(2)}B</div>
        </div>
        
        <div className="indicator-card">
          <div className="indicator-title">Dominancia BTC</div>
          <div className="indicator-value">{btcDominance.toFixed(1)}%</div>
          <div className="indicator-progress">
            <div className="progress-bar" style={{ width: `${btcDominance}%` }}></div>
          </div>
        </div>
        
        <div className="indicator-card">
          <div className="indicator-title">Dominancia ETH</div>
          <div className="indicator-value">{ethDominance.toFixed(1)}%</div>
          <div className="indicator-progress">
            <div className="progress-bar" style={{ width: `${ethDominance}%` }}></div>
          </div>
        </div>
      </div>
    );
  };

  // Renderizar tabla de criptomonedas
  const renderCryptoTable = () => {
    if (!cryptoData.length) return null;
    
    return (
      <div className="crypto-table-container">
        <table className="crypto-table">
          <thead>
            <tr>
              <th>#</th>
              <th>Nombre</th>
              <th>Precio</th>
              <th>Cambio {selectedTimeframe}</th>
              <th>Cap. Mercado</th>
              <th>Volumen 24h</th>
            </tr>
          </thead>
          <tbody>
            {cryptoData.slice(0, 10).map((crypto, index) => (
              <tr key={crypto.id} className="crypto-row">
                <td>{index + 1}</td>
                <td className="crypto-name-cell">
                  <img src={crypto.image} alt={crypto.name} className="crypto-table-icon" />
                  <div className="crypto-name-info">
                    <div className="crypto-table-name">{crypto.name}</div>
                    <div className="crypto-table-symbol">{crypto.symbol.toUpperCase()}</div>
                  </div>
                </td>
                <td>${crypto.current_price.toLocaleString()}</td>
                <td className={`crypto-change ${crypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                  {crypto.price_change_percentage_24h >= 0 ? '▲' : '▼'} {Math.abs(crypto.price_change_percentage_24h).toFixed(2)}%
                </td>
                <td>${(crypto.market_cap / 1e9).toFixed(2)}B</td>
                <td>${(crypto.total_volume / 1e9).toFixed(2)}B</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="crypto-market-insights">
      <div className="insights-header">
        <h2>Insights del Mercado Cripto</h2>
        <div className="insights-tabs">
          <button 
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Resumen
          </button>
          <button 
            className={`tab-button ${activeTab === 'trends' ? 'active' : ''}`}
            onClick={() => setActiveTab('trends')}
          >
            Tendencias
          </button>
          <button 
            className={`tab-button ${activeTab === 'heatmap' ? 'active' : ''}`}
            onClick={() => setActiveTab('heatmap')}
          >
            Mapa de Calor
          </button>
        </div>
      </div>
      
      {isLoading ? (
        <div className="insights-loading">
          <div className="loading-spinner"></div>
          <p>Cargando datos del mercado...</p>
        </div>
      ) : (
        <div className="insights-content">
          {activeTab === 'overview' && (
            <div className="overview-tab">
              {renderMarketIndicators()}
              <div className="market-summary">
                <h3>Principales Criptomonedas</h3>
                {renderCryptoTable()}
              </div>
            </div>
          )}
          
          {activeTab === 'trends' && (
            <div className="trends-tab">
              <div className="timeframe-selector">
                <button 
                  className={`timeframe-button ${selectedTimeframe === '24h' ? 'active' : ''}`}
                  onClick={() => setSelectedTimeframe('24h')}
                >
                  24h
                </button>
                <button 
                  className={`timeframe-button ${selectedTimeframe === '7d' ? 'active' : ''}`}
                  onClick={() => setSelectedTimeframe('7d')}
                >
                  7d
                </button>
                <button 
                  className={`timeframe-button ${selectedTimeframe === '30d' ? 'active' : ''}`}
                  onClick={() => setSelectedTimeframe('30d')}
                >
                  30d
                </button>
              </div>
              <div className="trend-lines" ref={trendLinesRef}></div>
            </div>
          )}
          
          {activeTab === 'heatmap' && (
            <div className="heatmap-tab">
              <div className="heatmap-legend">
                <div className="legend-item">
                  <div className="legend-color negative"></div>
                  <span>Bajada</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color positive"></div>
                  <span>Subida</span>
                </div>
                <div className="legend-size">
                  <span>Tamaño = Capitalización de Mercado</span>
                </div>
              </div>
              <div className="heatmap-container" ref={heatmapRef}></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CryptoMarketInsights;
