const { OpenAI } = require('openai');
require('dotenv').config();
const { searchCryptoNews } = require('./brave.service');
const { braveSearch } = require('./mcp-client.service');
const { visualizeWebPage } = require('./playwright-mcp.service');
const { getContext7Documentation } = require('./context7.service');
const onchainMcpService = require('./onchain-mcp.service');
const technicalAnalysisService = require('./technical-analysis.service');

// Configurar el cliente de OpenAI para usar OpenRouter
const createOpenAIClient = () => {
  // Verificar que las variables de entorno estén definidas
  const apiKey = process.env.OPENROUTER_API_KEY || '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';
  const baseURL = process.env.OPENROUTER_API_BASE || 'https://openrouter.ai/api/v1';

  return new OpenAI({
    apiKey,
    baseURL,
    defaultHeaders: {
      'HTTP-Referer': 'https://criptokens.app', // Reemplazar con la URL real de la aplicación
      'X-Title': 'Criptokens - Gurú Cripto'
    }
  });
};

// Definir las herramientas disponibles para el LLM
const tools = [
  {
    type: "function",
    function: {
      name: "braveSearch",
      description: "Busca información actualizada en la web utilizando Brave Search",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "La consulta de búsqueda. Debe ser específica y relevante para la pregunta del usuario."
          },
          count: {
            type: "integer",
            description: "Número de resultados a devolver (1-10)",
            default: 5
          },
          freshness: {
            type: "string",
            description: "Filtro de tiempo para los resultados",
            enum: ["pd", "pw", "pm", "py"],
            default: "pm"
          }
        },
        required: ["query"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "browseWebPage",
      description: "Navega a una página web y analiza su contenido para obtener información detallada",
      parameters: {
        type: "object",
        properties: {
          url: {
            type: "string",
            description: "URL completa de la página web a visitar y analizar"
          }
        },
        required: ["url"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "getContext7Docs",
      description: "Obtiene documentación actualizada y ejemplos de código para una librería o API específica usando Context7. Úsala SIEMPRE que la pregunta del usuario sea sobre cómo usar una librería, API, framework específico, o si necesitas código de ejemplo reciente.",
      parameters: {
        type: "object",
        properties: {
          libraryName: {
            type: "string",
            description: "El nombre exacto de la librería, API o framework (ej: 'react', 'ethers.js', 'firebase-admin', 'animejs', '@modelcontextprotocol/sdk')."
          },
          query: {
            type: "string",
            description: "(Opcional) Una pregunta o tema específico dentro de la documentación de esa librería."
          }
        },
        required: ["libraryName"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "getEthereumAddressInfo",
      description: "Analiza una dirección de Ethereum para obtener información detallada sobre su balance, transacciones y tokens",
      parameters: {
        type: "object",
        properties: {
          address: {
            type: "string",
            description: "La dirección de Ethereum a analizar (formato 0x...)"
          }
        },
        required: ["address"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "getSmartContractInfo",
      description: "Analiza un contrato inteligente de Ethereum para obtener información detallada sobre su código, transacciones y estado",
      parameters: {
        type: "object",
        properties: {
          address: {
            type: "string",
            description: "La dirección del contrato inteligente a analizar (formato 0x...)"
          }
        },
        required: ["address"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "getEthereumGasInfo",
      description: "Obtiene información actualizada sobre los precios de gas en la red Ethereum",
      parameters: {
        type: "object",
        properties: {}
      }
    }
  },
  {
    type: "function",
    function: {
      name: "getEthereumPrice",
      description: "Obtiene el precio actual de Ethereum en USD y BTC, junto con estadísticas de la red",
      parameters: {
        type: "object",
        properties: {}
      }
    }
  },
  {
    type: "function",
    function: {
      name: "getDefiProtocolsInfo",
      description: "Obtiene información sobre los principales protocolos DeFi en Ethereum",
      parameters: {
        type: "object",
        properties: {}
      }
    }
  },
  {
    type: "function",
    function: {
      name: "performTechnicalAnalysis",
      description: "Realiza un análisis técnico completo de una criptomoneda, incluyendo indicadores, patrones y señales",
      parameters: {
        type: "object",
        properties: {
          symbol: {
            type: "string",
            description: "Símbolo de la criptomoneda (ej: BTC, ETH, SOL)"
          },
          interval: {
            type: "string",
            description: "Intervalo de tiempo para el análisis (ej: 1d, 4h, 1w)",
            default: "1d"
          },
          limit: {
            type: "integer",
            description: "Número de velas a analizar (10-100)",
            default: 30
          }
        },
        required: ["symbol"]
      }
    }
  }
];

// Caché simple para respuestas
const cache = {
  data: {},
  timestamps: {},
  maxAge: 60 * 60 * 1000 // 1 hora en milisegundos
};

/**
 * Genera una respuesta utilizando OpenRouter
 * @param {string} message - Mensaje del usuario
 * @param {Array} history - Historial de conversación
 * @returns {Promise<string>} - Respuesta generada
 */
async function generateResponse(message, history = []) {
  try {
    console.log(`Generando respuesta para: ${message}`);

    // Crear cliente de OpenAI
    const openai = createOpenAIClient();

    // Preparar los mensajes para la API
    const messages = [
      { role: 'system', content: getSystemPrompt() },
      ...history.map(item => ({
        role: item.role,
        content: item.content
      })),
      { role: 'user', content: message }
    ];

    // Crear el payload para la solicitud
    const payload = {
      model: process.env.LLM_MODEL_NAME || 'openai/gpt-3.5-turbo',
      messages,
      temperature: 0.7,
      max_tokens: 1000
    };

    // Realizar la llamada a la API
    const response = await openai.chat.completions.create(payload);

    // Extraer la respuesta
    const reply = response.choices[0].message.content;

    return reply;
  } catch (error) {
    console.error('Error al generar respuesta con OpenRouter:', error);
    return 'Lo siento, ha ocurrido un error al procesar tu solicitud. Por favor, inténtalo de nuevo más tarde.';
  }
}

/**
 * Genera un prompt de sistema enriquecido con datos de contexto
 * @param {Object} contextData - Datos de contexto (criptomonedas, portfolio, etc.)
 * @returns {string} - Prompt de sistema enriquecido
 */
function getSystemPrompt(cryptoData = [], portfolioData = [], newsResults = [], relevantDocs = []) {
  // Versión simplificada del prompt para reducir tokens
  let systemPrompt = `Eres CriptoGuru, un asistente experto en criptomonedas. Sé breve y conciso.`;

  // Añadir información mínima sobre herramientas disponibles
  systemPrompt += ` Tienes acceso a herramientas como 'browseWebPage', 'getEthereumAddressInfo' y 'performTechnicalAnalysis'.`;

  return systemPrompt;
}

/**
 * Genera una respuesta de chat con contexto enriquecido
 * @param {string} message - Mensaje del usuario
 * @param {Object} contextData - Datos de contexto
 * @param {Array} history - Historial de conversación
 * @returns {Promise<string>} - Respuesta generada
 */
async function getChatResponse(message, contextData = {}, history = [], cacheKey = null) {
  try {
    console.log(`Generando respuesta con contexto enriquecido para: ${message}`);

    // Verificar caché si se proporciona una clave
    if (cacheKey && cache.data[cacheKey] && (Date.now() - cache.timestamps[cacheKey] < cache.maxAge)) {
      console.log(`Usando respuesta en caché para clave: ${cacheKey}`);
      return cache.data[cacheKey];
    }

    // Extraer datos de contexto
    const { cryptoData = [], portfolioData = [], newsResults = [], relevantDocs = [] } = contextData;

    // Crear cliente de OpenAI
    const openai = createOpenAIClient();

    // Preparar los mensajes para la API
    const messages = [
      { role: 'system', content: getSystemPrompt(cryptoData, portfolioData, newsResults, relevantDocs) },
      ...history.map(item => ({
        role: item.role,
        content: item.content
      })),
      { role: 'user', content: message }
    ];

    // Crear el payload para la solicitud
    const payload = {
      model: process.env.LLM_MODEL_NAME || 'anthropic/claude-instant-1.2',
      messages: [
        // Usar solo el mensaje del sistema y el mensaje del usuario para reducir tokens
        { role: 'system', content: "Eres CriptoGuru, un asistente experto en criptomonedas. Sé breve y conciso." },
        { role: 'user', content: message }
      ],
      temperature: 0.7,
      max_tokens: 150, // Aumentado pero aún conservador
      tools: tools,
      tool_choice: "auto"
    };

    // Realizar la llamada a la API
    const response = await openai.chat.completions.create(payload);

    // Verificar que la respuesta tenga el formato esperado
    if (!response || !response.choices || response.choices.length === 0) {
      console.error('Respuesta inesperada de la API:', JSON.stringify(response, null, 2));
      throw new Error('Respuesta inesperada de la API');
    }

    // Verificar si hay llamadas a funciones
    const responseMessage = response.choices[0].message;

    // Si el LLM quiere usar una herramienta
    if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
      console.log('El LLM quiere usar herramientas:', JSON.stringify(responseMessage.tool_calls, null, 2));

      // Añadir el mensaje del asistente al historial
      messages.push(responseMessage);

      // Procesar cada llamada a herramienta
      for (const toolCall of responseMessage.tool_calls) {
        if (toolCall.function.name === 'braveSearch') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Ejecutando búsqueda en Brave con query: ${args.query}`);

            // Llamar a la función de búsqueda a través del MCP Orchestrator
            const searchResults = await braveSearch(
              args.query,
              args.count || 5,
              args.freshness || 'pm'
            );

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(searchResults)
            });
          } catch (error) {
            console.error('Error al ejecutar la búsqueda en Brave:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al realizar la búsqueda: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'browseWebPage') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Navegando a la página web: ${args.url}`);

            // Llamar a la función de visualización de páginas web
            const pageData = await visualizeWebPage(args.url);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(pageData)
            });
          } catch (error) {
            console.error('Error al navegar a la página web:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al navegar a la página web: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'getContext7Docs') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Obteniendo documentación de Context7 para: ${args.libraryName}${args.query ? ` con consulta: ${args.query}` : ''}`);

            // Llamar a la función de obtención de documentación
            const docData = await getContext7Documentation(args.libraryName, args.query);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(docData)
            });
          } catch (error) {
            console.error('Error al obtener documentación de Context7:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al obtener documentación de Context7: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'getEthereumAddressInfo') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Analizando dirección de Ethereum: ${args.address}`);

            // Llamar a la función de análisis de dirección
            const analysis = await onchainMcpService.analyzeAddress(args.address);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(analysis)
            });
          } catch (error) {
            console.error('Error al analizar dirección de Ethereum:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar dirección de Ethereum: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'getSmartContractInfo') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Analizando contrato inteligente: ${args.address}`);

            // Llamar a la función de análisis de contrato
            const analysis = await onchainMcpService.analyzeSmartContract(args.address);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(analysis)
            });
          } catch (error) {
            console.error('Error al analizar contrato inteligente:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar contrato inteligente: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'getEthereumGasInfo') {
          try {
            console.log('Obteniendo información de gas en Ethereum...');

            // Llamar a la función de obtención de información de gas
            const gasInfo = await onchainMcpService.getGasOracle();

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(gasInfo)
            });
          } catch (error) {
            console.error('Error al obtener información de gas en Ethereum:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al obtener información de gas en Ethereum: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'getEthereumPrice') {
          try {
            console.log('Obteniendo precio y estadísticas de Ethereum...');

            // Llamar a la función de obtención de precio y estadísticas
            const priceInfo = await onchainMcpService.getEthPrice();

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(priceInfo)
            });
          } catch (error) {
            console.error('Error al obtener precio y estadísticas de Ethereum:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al obtener precio y estadísticas de Ethereum: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'getDefiProtocolsInfo') {
          try {
            console.log('Obteniendo información sobre protocolos DeFi...');

            // Llamar a la función de obtención de información sobre protocolos DeFi
            const defiInfo = await onchainMcpService.getDefiProtocols();

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(defiInfo)
            });
          } catch (error) {
            console.error('Error al obtener información sobre protocolos DeFi:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al obtener información sobre protocolos DeFi: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'performTechnicalAnalysis') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Realizando análisis técnico para ${args.symbol} con intervalo ${args.interval || '1d'} y límite ${args.limit || 30}...`);

            // Llamar a la función de análisis técnico
            const analysis = await technicalAnalysisService.performTechnicalAnalysis(
              args.symbol,
              args.interval || '1d',
              args.limit || 30
            );

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(analysis)
            });
          } catch (error) {
            console.error('Error al realizar análisis técnico:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al realizar análisis técnico: ' + error.message })
            });
          }
        }
      }

      // Hacer una segunda llamada al LLM con los resultados de las herramientas
      console.log('Realizando segunda llamada al LLM con los resultados de las herramientas...');
      const secondResponse = await openai.chat.completions.create({
        model: payload.model,
        messages,
        temperature: payload.temperature,
        max_tokens: 20 // Reducido a 20 tokens debido a limitaciones de crédito
      });

      // Verificar que la respuesta tenga el formato esperado
      if (!secondResponse || !secondResponse.choices || secondResponse.choices.length === 0) {
        console.error('Respuesta inesperada de la API en la segunda llamada:', JSON.stringify(secondResponse, null, 2));
        throw new Error('Respuesta inesperada de la API en la segunda llamada');
      }

      // Extraer la respuesta final
      const reply = secondResponse.choices[0].message.content;

      // Guardar en caché si se proporciona una clave
      if (cacheKey) {
        cache.data[cacheKey] = reply;
        cache.timestamps[cacheKey] = Date.now();
      }

      return reply;
    } else {
      // Si no hay llamadas a herramientas, procesar normalmente
      const reply = responseMessage.content;

      // Guardar en caché si se proporciona una clave
      if (cacheKey) {
        cache.data[cacheKey] = reply;
        cache.timestamps[cacheKey] = Date.now();
      }

      return reply;
    }
  } catch (error) {
    console.error('Error al generar respuesta con contexto enriquecido:', error);
    return 'Lo siento, ha ocurrido un error al procesar tu solicitud con contexto enriquecido. Por favor, inténtalo de nuevo más tarde.';
  }
}

/**
 * Genera una imagen utilizando DALL-E a través de OpenRouter
 * @param {string} prompt - Descripción de la imagen a generar
 * @returns {Promise<string>} - URL de la imagen generada
 */
async function generateImage(prompt) {
  try {
    console.log(`Generando imagen para: ${prompt}`);

    // Crear cliente de OpenAI
    const openai = createOpenAIClient();

    // Realizar la llamada a la API
    const response = await openai.images.generate({
      model: "openai/dall-e-3",
      prompt: prompt,
      n: 1,
      size: "1024x1024"
    });

    // Extraer la URL de la imagen
    const imageUrl = response.data[0].url;
    console.log(`Imagen generada: ${imageUrl}`);

    return imageUrl;
  } catch (error) {
    console.error('Error al generar imagen con DALL-E:', error);
    return null;
  }
}

module.exports = {
  generateResponse,
  getChatResponse,
  generateImage,
  tools
};
