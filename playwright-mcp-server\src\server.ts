import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from '@playwright/mcp';

// Cargar variables de entorno
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3103;

// Configurar CORS
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Endpoint para SSE
app.get('/sse', async (req, res) => {
  try {
    console.log('Cliente intentando conectar al servidor Playwright MCP via SSE');

    // Crear servidor MCP de Playwright en modo headless
    const mcpServer = await createServer({ headless: true });

    // En lugar de usar SSEServerTransport, usaremos una respuesta simple por ahora
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    // Enviar un evento de conexión exitosa
    res.write(`data: ${JSON.stringify({ status: 'connected' })}\n\n`);

    console.log('Cliente conectado al servidor Playwright MCP via SSE');
  } catch (error) {
    console.error('Error al conectar cliente SSE:', error);
    res.status(500).end();
  }
});

// Endpoint para verificar el estado del servidor
app.get('/status', (req, res) => {
  res.json({ status: 'ok', message: 'Playwright MCP Server running' });
});

// Iniciar el servidor
app.listen(PORT, () => {
  console.log(`Playwright MCP Server running on port ${PORT}`);
  console.log(`SSE endpoint: http://localhost:${PORT}/sse`);
});
