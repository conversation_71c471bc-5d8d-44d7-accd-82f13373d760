import React, { useState } from 'react';
import { usePriceAlerts } from '../hooks/useCryptoData';
import { useCryptoMarketData } from '../hooks/useCryptoData';
import '../styles/PriceAlerts.css';

interface PriceAlertsProps {
  onClose?: () => void;
}

const PriceAlerts: React.FC<PriceAlertsProps> = ({ onClose }) => {
  const { alerts, loading, error, addAlert, updateAlert, removeAlert } = usePriceAlerts();
  const { data: cryptoData } = useCryptoMarketData();
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedCrypto, setSelectedCrypto] = useState('');
  const [targetPrice, setTargetPrice] = useState('');
  const [condition, setCondition] = useState<'above' | 'below'>('above');

  // Función para manejar la creación de una nueva alerta
  const handleCreateAlert = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedCrypto || !targetPrice) return;
    
    const selectedCryptoData = cryptoData.find(crypto => crypto.id === selectedCrypto);
    if (!selectedCryptoData) return;
    
    try {
      await addAlert({
        cryptoId: selectedCryptoData.id,
        cryptoName: selectedCryptoData.name,
        targetPrice: parseFloat(targetPrice),
        condition
      });
      
      // Resetear el formulario
      setSelectedCrypto('');
      setTargetPrice('');
      setCondition('above');
      setShowAddForm(false);
    } catch (error) {
      console.error('Error al crear alerta:', error);
    }
  };

  // Función para manejar la activación/desactivación de una alerta
  const handleToggleAlert = async (alertId: string, currentActive: boolean) => {
    try {
      await updateAlert(alertId, { active: !currentActive });
    } catch (error) {
      console.error('Error al actualizar alerta:', error);
    }
  };

  // Función para manejar la eliminación de una alerta
  const handleDeleteAlert = async (alertId: string) => {
    if (window.confirm('¿Estás seguro de que deseas eliminar esta alerta?')) {
      try {
        await removeAlert(alertId);
      } catch (error) {
        console.error('Error al eliminar alerta:', error);
      }
    }
  };

  // Formatear precio para mostrar
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  if (loading) {
    return <div className="price-alerts-loading">Cargando alertas...</div>;
  }

  if (error) {
    return <div className="price-alerts-error">Error: {error}</div>;
  }

  return (
    <div className="price-alerts-container">
      <div className="price-alerts-header">
        <h2>Alertas de Precio</h2>
        {onClose && (
          <button className="close-button" onClick={onClose}>×</button>
        )}
      </div>

      <div className="price-alerts-content">
        {alerts.length === 0 && !showAddForm ? (
          <div className="no-alerts-message">
            <p>No tienes alertas configuradas.</p>
            <button 
              className="add-alert-button"
              onClick={() => setShowAddForm(true)}
            >
              Crear tu primera alerta
            </button>
          </div>
        ) : (
          <>
            {!showAddForm && (
              <button 
                className="add-alert-button"
                onClick={() => setShowAddForm(true)}
              >
                + Nueva Alerta
              </button>
            )}

            {showAddForm && (
              <div className="add-alert-form">
                <h3>Nueva Alerta de Precio</h3>
                <form onSubmit={handleCreateAlert}>
                  <div className="form-group">
                    <label>Criptomoneda</label>
                    <select 
                      value={selectedCrypto}
                      onChange={(e) => setSelectedCrypto(e.target.value)}
                      required
                    >
                      <option value="">Selecciona una criptomoneda</option>
                      {cryptoData.map(crypto => (
                        <option key={crypto.id} value={crypto.id}>
                          {crypto.name} ({crypto.symbol})
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Condición</label>
                    <select 
                      value={condition}
                      onChange={(e) => setCondition(e.target.value as 'above' | 'below')}
                      required
                    >
                      <option value="above">Precio sube por encima de</option>
                      <option value="below">Precio baja por debajo de</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Precio Objetivo (USD)</label>
                    <input 
                      type="number"
                      value={targetPrice}
                      onChange={(e) => setTargetPrice(e.target.value)}
                      step="0.01"
                      min="0"
                      required
                      placeholder="Ej: 50000"
                    />
                  </div>

                  <div className="form-actions">
                    <button 
                      type="button" 
                      className="cancel-button"
                      onClick={() => setShowAddForm(false)}
                    >
                      Cancelar
                    </button>
                    <button type="submit" className="submit-button">
                      Crear Alerta
                    </button>
                  </div>
                </form>
              </div>
            )}

            {alerts.length > 0 && (
              <div className="alerts-list">
                <h3>Tus Alertas</h3>
                {alerts.map(alert => {
                  const cryptoInfo = cryptoData.find(c => c.id === alert.cryptoId);
                  const currentPrice = cryptoInfo?.current_price || 0;
                  const priceReached = alert.condition === 'above' 
                    ? currentPrice >= alert.targetPrice 
                    : currentPrice <= alert.targetPrice;
                  
                  return (
                    <div 
                      key={alert.id} 
                      className={`alert-item ${priceReached ? 'triggered' : ''} ${!alert.active ? 'inactive' : ''}`}
                    >
                      <div className="alert-info">
                        <div className="alert-crypto">
                          {cryptoInfo && (
                            <img src={cryptoInfo.image} alt={alert.cryptoName} className="crypto-icon" />
                          )}
                          <span className="crypto-name">{alert.cryptoName}</span>
                        </div>
                        
                        <div className="alert-condition">
                          {alert.condition === 'above' ? 'Sube por encima de' : 'Baja por debajo de'} 
                          <span className="target-price">{formatPrice(alert.targetPrice)}</span>
                        </div>
                        
                        {cryptoInfo && (
                          <div className="current-price">
                            Precio actual: 
                            <span className={currentPrice > alert.targetPrice ? 'price-up' : 'price-down'}>
                              {formatPrice(currentPrice)}
                            </span>
                          </div>
                        )}
                        
                        {priceReached && (
                          <div className="alert-status triggered">
                            ¡Alerta activada!
                          </div>
                        )}
                      </div>
                      
                      <div className="alert-actions">
                        <button 
                          className={`toggle-button ${alert.active ? 'active' : 'inactive'}`}
                          onClick={() => handleToggleAlert(alert.id, alert.active)}
                          title={alert.active ? 'Desactivar alerta' : 'Activar alerta'}
                        >
                          {alert.active ? 'Activa' : 'Inactiva'}
                        </button>
                        
                        <button 
                          className="delete-button"
                          onClick={() => handleDeleteAlert(alert.id)}
                          title="Eliminar alerta"
                        >
                          Eliminar
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default PriceAlerts;
