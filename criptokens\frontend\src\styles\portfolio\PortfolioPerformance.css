/* Estilos para el componente de rendimiento del portafolio */

.portfolio-performance {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.portfolio-performance h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1.25rem 0;
  color: var(--text-bright);
}

.chart-container {
  flex: 1;
  position: relative;
  min-height: 300px;
}

/* Estilos para la gráfica estática */
.static-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.static-chart-container {
  flex: 1;
  display: flex;
  align-items: flex-end;
  padding: 10px 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.static-chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 10px;
}

.static-chart-bar {
  width: 8px;
  min-height: 1px;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
  margin: 0 2px;
}

.static-chart-bar.positive {
  background-color: rgba(75, 192, 192, 0.8);
}

.static-chart-bar.negative {
  background-color: rgba(255, 99, 132, 0.8);
}

.static-chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

.static-chart-date-range,
.static-chart-value-range {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.static-chart-value-range {
  margin-top: 5px;
  font-weight: bold;
}

.static-chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
}

/* Estilos para el gráfico vacío */
.empty-chart {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.empty-chart-message,
.chart-loading,
.chart-error {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  padding: 2rem;
  text-align: center;
  min-height: 200px;
}

.empty-chart-message p,
.chart-error p {
  color: var(--text-dim);
  margin: 0.5rem 0;
}

.chart-error .error-details {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  max-width: 80%;
  word-break: break-word;
}

/* Responsive */
@media (max-width: 768px) {
  .portfolio-performance {
    padding: 1.25rem;
  }

  .chart-container {
    min-height: 250px;
  }
}
