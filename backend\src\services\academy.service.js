const fs = require('fs');
const path = require('path');
const { db } = require('../firebase-config');
const { collection, doc, getDoc, getDocs, setDoc, updateDoc, query, where } = require('firebase/firestore');

// Ruta a los archivos JSON de cursos
const COURSES_PATH = path.join(__dirname, '../../data/courses');

/**
 * Obtiene todos los cursos disponibles
 * @returns {Promise<Array>} Lista de cursos
 */
const getAllCourses = async () => {
  try {
    // En una implementación real, esto obtendría los cursos de Firestore
    // Por ahora, leemos los archivos JSON locales
    const coursesRef = collection(db, 'academy_courses');
    const coursesSnapshot = await getDocs(coursesRef);
    
    if (!coursesSnapshot.empty) {
      return coursesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    }
    
    // Si no hay cursos en Firestore, cargamos desde archivos locales
    return loadCoursesFromFiles();
  } catch (error) {
    console.error('Error al obtener cursos:', error);
    // Fallback a archivos locales en caso de error
    return loadCoursesFromFiles();
  }
};

/**
 * Carga los cursos desde archivos JSON locales
 * @returns {Promise<Array>} Lista de cursos
 */
const loadCoursesFromFiles = async () => {
  try {
    // Verificar si el directorio existe
    if (!fs.existsSync(COURSES_PATH)) {
      console.warn(`Directorio de cursos no encontrado: ${COURSES_PATH}`);
      return [];
    }
    
    // Leer los archivos JSON del directorio
    const files = fs.readdirSync(COURSES_PATH)
      .filter(file => file.endsWith('.json'));
    
    // Cargar cada archivo JSON
    const courses = [];
    for (const file of files) {
      const filePath = path.join(COURSES_PATH, file);
      const courseData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      courses.push(courseData);
    }
    
    return courses;
  } catch (error) {
    console.error('Error al cargar cursos desde archivos:', error);
    return [];
  }
};

/**
 * Obtiene un curso por su ID
 * @param {string} courseId - ID del curso
 * @returns {Promise<Object|null>} Datos del curso o null si no existe
 */
const getCourseById = async (courseId) => {
  try {
    // Intentar obtener el curso de Firestore
    const courseRef = doc(db, 'academy_courses', courseId);
    const courseDoc = await getDoc(courseRef);
    
    if (courseDoc.exists()) {
      return {
        id: courseDoc.id,
        ...courseDoc.data()
      };
    }
    
    // Si no existe en Firestore, intentar cargar desde archivo local
    const filePath = path.join(COURSES_PATH, `${courseId}.json`);
    if (fs.existsSync(filePath)) {
      return JSON.parse(fs.readFileSync(filePath, 'utf8'));
    }
    
    return null;
  } catch (error) {
    console.error(`Error al obtener curso ${courseId}:`, error);
    
    // Intentar cargar desde archivo local como fallback
    try {
      const filePath = path.join(COURSES_PATH, `${courseId}.json`);
      if (fs.existsSync(filePath)) {
        return JSON.parse(fs.readFileSync(filePath, 'utf8'));
      }
    } catch (e) {
      console.error(`Error al cargar curso ${courseId} desde archivo:`, e);
    }
    
    return null;
  }
};

/**
 * Obtiene el progreso de un usuario en un curso
 * @param {string} userId - ID del usuario
 * @param {string} courseId - ID del curso
 * @returns {Promise<Object>} Progreso del usuario en el curso
 */
const getUserCourseProgress = async (userId, courseId) => {
  try {
    const progressRef = doc(db, 'academy_progress', `${userId}_${courseId}`);
    const progressDoc = await getDoc(progressRef);
    
    if (progressDoc.exists()) {
      return progressDoc.data();
    }
    
    // Si no existe, crear un registro de progreso vacío
    const emptyProgress = {
      userId,
      courseId,
      completedLessons: [],
      quizScores: {},
      lastAccessed: new Date().toISOString(),
      percentComplete: 0
    };
    
    // Guardar el progreso vacío
    await setDoc(progressRef, emptyProgress);
    
    return emptyProgress;
  } catch (error) {
    console.error(`Error al obtener progreso del usuario ${userId} en curso ${courseId}:`, error);
    return {
      userId,
      courseId,
      completedLessons: [],
      quizScores: {},
      lastAccessed: new Date().toISOString(),
      percentComplete: 0
    };
  }
};

/**
 * Actualiza el progreso de un usuario en un curso
 * @param {string} userId - ID del usuario
 * @param {string} courseId - ID del curso
 * @param {Object} progressData - Datos de progreso a actualizar
 * @returns {Promise<boolean>} True si se actualizó correctamente
 */
const updateUserCourseProgress = async (userId, courseId, progressData) => {
  try {
    const progressRef = doc(db, 'academy_progress', `${userId}_${courseId}`);
    
    // Actualizar la fecha de último acceso
    progressData.lastAccessed = new Date().toISOString();
    
    await updateDoc(progressRef, progressData);
    return true;
  } catch (error) {
    console.error(`Error al actualizar progreso del usuario ${userId} en curso ${courseId}:`, error);
    return false;
  }
};

/**
 * Marca una lección como completada
 * @param {string} userId - ID del usuario
 * @param {string} courseId - ID del curso
 * @param {string} lessonId - ID de la lección
 * @returns {Promise<boolean>} True si se actualizó correctamente
 */
const markLessonAsCompleted = async (userId, courseId, lessonId) => {
  try {
    // Obtener el progreso actual
    const progress = await getUserCourseProgress(userId, courseId);
    
    // Verificar si la lección ya está marcada como completada
    if (!progress.completedLessons.includes(lessonId)) {
      progress.completedLessons.push(lessonId);
      
      // Obtener el curso para calcular el porcentaje de progreso
      const course = await getCourseById(courseId);
      
      // Contar el número total de lecciones
      let totalLessons = 0;
      course.modules.forEach(module => {
        totalLessons += module.lessons.length;
      });
      
      // Calcular el porcentaje de progreso
      progress.percentComplete = Math.round((progress.completedLessons.length / totalLessons) * 100);
      
      // Actualizar el progreso
      await updateUserCourseProgress(userId, courseId, progress);
    }
    
    return true;
  } catch (error) {
    console.error(`Error al marcar lección ${lessonId} como completada:`, error);
    return false;
  }
};

/**
 * Guarda la puntuación de un quiz
 * @param {string} userId - ID del usuario
 * @param {string} courseId - ID del curso
 * @param {string} moduleId - ID del módulo
 * @param {number} score - Puntuación obtenida (0-100)
 * @returns {Promise<boolean>} True si se actualizó correctamente
 */
const saveQuizScore = async (userId, courseId, moduleId, score) => {
  try {
    // Obtener el progreso actual
    const progress = await getUserCourseProgress(userId, courseId);
    
    // Actualizar la puntuación del quiz
    if (!progress.quizScores) {
      progress.quizScores = {};
    }
    
    progress.quizScores[moduleId] = score;
    
    // Actualizar el progreso
    await updateUserCourseProgress(userId, courseId, progress);
    
    // Verificar si el usuario ha completado el curso
    const course = await getCourseById(courseId);
    
    // Verificar si ha completado todas las lecciones
    let totalLessons = 0;
    course.modules.forEach(module => {
      totalLessons += module.lessons.length;
    });
    
    // Verificar si ha aprobado todos los quizzes
    const allQuizzesPassed = course.modules
      .filter(module => module.quiz)
      .every(module => {
        const quizScore = progress.quizScores[module.id] || 0;
        const passingScore = module.quiz.passingScore || 70;
        return quizScore >= passingScore;
      });
    
    // Si ha completado todas las lecciones y aprobado todos los quizzes, generar certificado
    if (progress.completedLessons.length === totalLessons && allQuizzesPassed) {
      await generateCertificate(userId, courseId);
    }
    
    return true;
  } catch (error) {
    console.error(`Error al guardar puntuación de quiz:`, error);
    return false;
  }
};

/**
 * Genera un certificado para un usuario que ha completado un curso
 * @param {string} userId - ID del usuario
 * @param {string} courseId - ID del curso
 * @returns {Promise<Object|null>} Datos del certificado o null si hubo un error
 */
const generateCertificate = async (userId, courseId) => {
  try {
    // Obtener datos del usuario
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      throw new Error(`Usuario ${userId} no encontrado`);
    }
    
    const userData = userDoc.data();
    
    // Obtener datos del curso
    const course = await getCourseById(courseId);
    
    if (!course) {
      throw new Error(`Curso ${courseId} no encontrado`);
    }
    
    // Crear certificado
    const certificateId = `${userId}_${courseId}_${Date.now()}`;
    const certificateData = {
      id: certificateId,
      userId,
      userName: userData.displayName || userData.email,
      courseId,
      courseTitle: course.title,
      issueDate: new Date().toISOString(),
      expiryDate: null, // Los certificados no expiran
      verified: true
    };
    
    // Guardar certificado en Firestore
    const certificateRef = doc(db, 'academy_certificates', certificateId);
    await setDoc(certificateRef, certificateData);
    
    return certificateData;
  } catch (error) {
    console.error(`Error al generar certificado:`, error);
    return null;
  }
};

/**
 * Obtiene los certificados de un usuario
 * @param {string} userId - ID del usuario
 * @returns {Promise<Array>} Lista de certificados
 */
const getUserCertificates = async (userId) => {
  try {
    const certificatesRef = collection(db, 'academy_certificates');
    const q = query(certificatesRef, where('userId', '==', userId));
    const certificatesSnapshot = await getDocs(q);
    
    return certificatesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error(`Error al obtener certificados del usuario ${userId}:`, error);
    return [];
  }
};

/**
 * Obtiene todos los cursos en los que un usuario está inscrito
 * @param {string} userId - ID del usuario
 * @returns {Promise<Array>} Lista de cursos con progreso
 */
const getUserEnrolledCourses = async (userId) => {
  try {
    // Obtener todos los registros de progreso del usuario
    const progressRef = collection(db, 'academy_progress');
    const q = query(progressRef, where('userId', '==', userId));
    const progressSnapshot = await getDocs(q);
    
    // Si no hay registros, el usuario no está inscrito en ningún curso
    if (progressSnapshot.empty) {
      return [];
    }
    
    // Obtener los IDs de los cursos
    const courseIds = progressSnapshot.docs.map(doc => {
      const data = doc.data();
      return data.courseId;
    });
    
    // Obtener los datos de cada curso
    const coursesWithProgress = [];
    for (const courseId of courseIds) {
      const course = await getCourseById(courseId);
      if (course) {
        // Obtener el progreso del usuario en este curso
        const progressDoc = progressSnapshot.docs.find(doc => doc.data().courseId === courseId);
        const progress = progressDoc ? progressDoc.data() : null;
        
        coursesWithProgress.push({
          ...course,
          progress
        });
      }
    }
    
    return coursesWithProgress;
  } catch (error) {
    console.error(`Error al obtener cursos inscritos del usuario ${userId}:`, error);
    return [];
  }
};

/**
 * Inscribe a un usuario en un curso
 * @param {string} userId - ID del usuario
 * @param {string} courseId - ID del curso
 * @returns {Promise<boolean>} True si se inscribió correctamente
 */
const enrollUserInCourse = async (userId, courseId) => {
  try {
    // Verificar si el curso existe
    const course = await getCourseById(courseId);
    if (!course) {
      throw new Error(`Curso ${courseId} no encontrado`);
    }
    
    // Crear registro de progreso
    const progressRef = doc(db, 'academy_progress', `${userId}_${courseId}`);
    const progressDoc = await getDoc(progressRef);
    
    // Si ya existe, el usuario ya está inscrito
    if (progressDoc.exists()) {
      return true;
    }
    
    // Crear nuevo registro de progreso
    const progressData = {
      userId,
      courseId,
      completedLessons: [],
      quizScores: {},
      enrollmentDate: new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      percentComplete: 0
    };
    
    await setDoc(progressRef, progressData);
    
    return true;
  } catch (error) {
    console.error(`Error al inscribir usuario ${userId} en curso ${courseId}:`, error);
    return false;
  }
};

module.exports = {
  getAllCourses,
  getCourseById,
  getUserCourseProgress,
  updateUserCourseProgress,
  markLessonAsCompleted,
  saveQuizScore,
  generateCertificate,
  getUserCertificates,
  getUserEnrolledCourses,
  enrollUserInCourse
};
