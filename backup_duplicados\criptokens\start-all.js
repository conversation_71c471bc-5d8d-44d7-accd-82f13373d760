const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Función para iniciar un proceso
function startProcess(command, args, cwd, name, env = {}) {
  console.log(`Iniciando ${name}...`);

  const childProcess = spawn(command, args, {
    cwd,
    shell: true,
    stdio: 'pipe',
    env: { ...process.env, ...env }
  });

  childProcess.stdout.on('data', (data) => {
    console.log(`[${name}] ${data.toString().trim()}`);
  });

  childProcess.stderr.on('data', (data) => {
    console.error(`[${name}] ${data.toString().trim()}`);
  });

  childProcess.on('close', (code) => {
    console.log(`[${name}] Proceso terminado con código ${code}`);
  });

  return childProcess;
}

// Directorios
const rootDir = __dirname;
const frontendDir = path.join(rootDir, 'frontend');
const backendDir = path.join(rootDir, 'backend');
const cryptoMcpServerDir = path.join(path.dirname(rootDir), 'crypto-mcp-server');
const playwrightMcpServerDir = path.join(path.dirname(rootDir), 'playwright-mcp-server');
const braveSearchServerPath = path.join(path.dirname(rootDir), 'brave-search-server.js');

// Leer la configuración de los servidores MCP
const configPath = path.join(rootDir, 'mcp-config.json');
let mcpConfig = {};

if (fs.existsSync(configPath)) {
  try {
    mcpConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    console.log('Configuración MCP cargada correctamente.');
  } catch (error) {
    console.error('Error al leer la configuración MCP:', error);
  }
}

// Verificar que los directorios existan
if (!fs.existsSync(frontendDir)) {
  console.error(`Error: No se encontró el directorio frontend en ${frontendDir}`);
  process.exit(1);
}

if (!fs.existsSync(backendDir)) {
  console.error(`Error: No se encontró el directorio backend en ${backendDir}`);
  process.exit(1);
}

console.log('Iniciando servidores en el orden correcto...');

// Paso 1: Iniciar el servidor MCP de crypto (puerto 3101)
let cryptoMcpProcess = null;
if (fs.existsSync(cryptoMcpServerDir)) {
  cryptoMcpProcess = startProcess('node', ['http-server.js'], cryptoMcpServerDir, 'Crypto MCP Server (Puerto 3101)');
  console.log('Esperando 2 segundos para que el servidor Crypto MCP inicie...');
  setTimeout(() => { console.log('Continuando con el siguiente servidor...'); }, 2000);
} else {
  console.warn(`Advertencia: No se encontró el directorio crypto-mcp-server en ${cryptoMcpServerDir}`);
}

// Paso 2: Iniciar el servidor Brave Search (puerto 3102)
let braveSearchProcess = null;
if (fs.existsSync(braveSearchServerPath)) {
  braveSearchProcess = startProcess('node', [braveSearchServerPath], path.dirname(rootDir), 'Brave Search Server (Puerto 3102)', {
    PORT: '3102',
    BRAVE_API_KEY: 'BSAccS820UUfffNOAD7yLACz9htlbe9'
  });
  console.log('Esperando 2 segundos para que el servidor Brave Search inicie...');
  setTimeout(() => { console.log('Continuando con el siguiente servidor...'); }, 2000);
} else {
  console.warn(`Advertencia: No se encontró el archivo brave-search-server.js en ${braveSearchServerPath}`);
}

// Paso 3: Iniciar el servidor Playwright MCP (puerto 3103)
let playwrightMcpProcess = null;
if (fs.existsSync(playwrightMcpServerDir)) {
  playwrightMcpProcess = startProcess('node', ['dist/server.js'], playwrightMcpServerDir, 'Playwright MCP Server (Puerto 3103)', {
    PORT: '3103'
  });
  console.log('Esperando 2 segundos para que el servidor Playwright MCP inicie...');
  setTimeout(() => { console.log('Continuando con el siguiente servidor...'); }, 2000);
} else {
  console.warn(`Advertencia: No se encontró el directorio playwright-mcp-server en ${playwrightMcpServerDir}`);
}

// Paso 4: Iniciar el backend (puerto 3001)
const backendProcess = startProcess('npm', ['start'], backendDir, 'Backend Server (Puerto 3001)');
console.log('Esperando 3 segundos para que el servidor Backend inicie...');
setTimeout(() => { console.log('Continuando con el siguiente servidor...'); }, 3000);

// Paso 5: Iniciar el frontend (puerto 5173)
const frontendProcess = startProcess('npm', ['run', 'dev'], frontendDir, 'Frontend Server (Puerto 5173)');

// Iniciar servidores MCP adicionales desde la configuración
const mcpServers = mcpConfig.mcpServers || {};
const mcpProcesses = {};

// Manejar la terminación del proceso principal
process.on('SIGINT', () => {
  console.log('Terminando todos los procesos...');
  frontendProcess.kill();
  backendProcess.kill();

  if (cryptoMcpProcess) {
    console.log('Terminando Crypto MCP Server...');
    cryptoMcpProcess.kill();
  }

  if (braveSearchProcess) {
    console.log('Terminando Brave Search Server...');
    braveSearchProcess.kill();
  }

  if (playwrightMcpProcess) {
    console.log('Terminando Playwright MCP Server...');
    playwrightMcpProcess.kill();
  }

  // Terminar todos los servidores MCP adicionales
  Object.entries(mcpProcesses).forEach(([name, process]) => {
    console.log(`Terminando servidor MCP ${name}...`);
    process.kill();
  });

  process.exit(0);
});

console.log(`
¡Todos los servicios iniciados correctamente!

Servidores disponibles:
- Frontend: http://localhost:5173
- Backend: http://localhost:3001
- Crypto MCP Server: http://localhost:3101
- Brave Search Server: http://localhost:3102
- Playwright MCP Server: http://localhost:3103

Presiona Ctrl+C para terminar todos los servidores.
`);
