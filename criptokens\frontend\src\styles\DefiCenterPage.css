.defi-center-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--text-primary);
}

.defi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.back-link {
  color: var(--text-secondary);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--color-primary);
}

.defi-header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--text-primary);
}

.defi-actions {
  display: flex;
  gap: 1rem;
}

.connect-wallet-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  font-size: 0.95rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s ease;
}

.connect-wallet-button:hover {
  background-color: var(--color-primary-dark);
}

.defi-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.25rem;
  color: var(--text-secondary);
  font-size: 1rem;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: rgba(123, 97, 255, 0.1);
}

.tab-button.active {
  color: var(--color-primary);
  border-bottom: 3px solid var(--color-primary);
  font-weight: 500;
}

/* Dashboard de Protocolos */
.protocols-dashboard {
  padding: 1rem 0;
}

.dashboard-header {
  margin-bottom: 1.5rem;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.filter-group select {
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.sort-order-button {
  background-color: var(--color-surface);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-order-button:hover {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.protocols-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.protocol-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.protocol-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.protocol-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.protocol-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: contain;
  background-color: white;
  padding: 5px;
}

.protocol-title h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.protocol-category {
  font-size: 0.8rem;
  color: var(--text-secondary);
  background-color: var(--color-surface-light);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

.protocol-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.stat-value.highlight {
  color: var(--color-primary);
  font-weight: 600;
}

.risk-low {
  color: #2ecc71;
}

.risk-medium {
  color: #f39c12;
}

.risk-mediumhigh {
  color: #e67e22;
}

.risk-high {
  color: #e74c3c;
}

.protocol-actions {
  display: flex;
  justify-content: center;
}

.protocol-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.5rem;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.protocol-button:hover {
  background-color: var(--color-primary-dark);
}

/* Staking Section */
.staking-section {
  padding: 1rem 0;
}

.staking-calculator {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.staking-calculator h3 {
  margin: 0 0 1.25rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.calculator-form {
  display: flex;
  gap: 2rem;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.form-group input {
  background-color: var(--color-surface-light);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-size: 1rem;
}

.staking-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.staking-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.staking-option-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.staking-option-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.option-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.option-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: contain;
  background-color: white;
  padding: 5px;
}

.option-title h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.option-symbol {
  font-size: 0.8rem;
  color: var(--text-secondary);
  background-color: var(--color-surface-light);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

.option-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.projected-returns {
  background-color: var(--color-surface-light);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.25rem;
  text-align: center;
}

.returns-header {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.returns-value {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.returns-profit {
  font-size: 0.9rem;
  color: #2ecc71;
}

.option-actions {
  display: flex;
  justify-content: center;
}

.stake-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.5rem;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.stake-button:hover {
  background-color: var(--color-primary-dark);
}

/* Yield Farming Section */
.yield-farming-section {
  padding: 1rem 0;
}

.yield-info-banner {
  display: flex;
  gap: 1.5rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.info-icon {
  font-size: 2rem;
  color: var(--color-primary);
  display: flex;
  align-items: center;
}

.info-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.info-content p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

.risk-warning {
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 3px solid #e74c3c;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.yield-farming-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.yield-farming-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.yield-pool-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.yield-pool-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.pool-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.pool-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: contain;
  background-color: white;
  padding: 5px;
}

.pool-title h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.pool-platform {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.pool-assets {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.25rem;
}

.asset-tag {
  font-size: 0.8rem;
  background-color: var(--color-surface-light);
  color: var(--text-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.pool-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.rewards-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.reward-tag {
  font-size: 0.75rem;
  background-color: rgba(123, 97, 255, 0.1);
  color: var(--color-primary);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
}

.pool-actions {
  display: flex;
  justify-content: center;
}

.pool-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.5rem;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.pool-button:hover {
  background-color: var(--color-primary-dark);
}

/* Lending Section */
.lending-section {
  padding: 1rem 0;
}

.lending-overview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.overview-card {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
}

.overview-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.overview-value {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.overview-card.deposit .overview-value i {
  color: #2ecc71;
}

.overview-card.borrow .overview-value i {
  color: #3498db;
}

.overview-card p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.lending-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.lending-markets-table {
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1fr 0.8fr 1.5fr 1.5fr;
  background-color: var(--color-surface-light);
  padding: 1rem;
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1fr 0.8fr 1.5fr 1.5fr;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: var(--color-surface-light);
}

.cell {
  display: flex;
  align-items: center;
}

.cell.asset {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.asset-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: contain;
  background-color: white;
  padding: 2px;
}

.cell.deposit {
  color: #2ecc71;
  font-weight: 500;
}

.cell.borrow {
  color: #3498db;
  font-weight: 500;
}

.liquidity-bars {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.liquidity-bar {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
}

.bar-label {
  color: var(--text-secondary);
}

.bar-value {
  color: var(--text-primary);
  font-weight: 500;
}

.cell.actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  flex: 1;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.action-button.deposit {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
  border: 1px solid #2ecc71;
}

.action-button.deposit:hover {
  background-color: #2ecc71;
  color: white;
}

.action-button.borrow {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
  border: 1px solid #3498db;
}

.action-button.borrow:hover {
  background-color: #3498db;
  color: white;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .lending-overview {
    grid-template-columns: 1fr;
  }
  
  .calculator-form {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .defi-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .defi-tabs {
    flex-wrap: wrap;
  }
  
  .tab-button {
    flex: 1 0 calc(50% - 0.5rem);
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group select {
    flex: 1;
  }
  
  .table-header, .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .header-cell {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
  }
  
  .cell {
    padding: 0.5rem 0;
  }
  
  .yield-info-banner {
    flex-direction: column;
    gap: 1rem;
  }
}
