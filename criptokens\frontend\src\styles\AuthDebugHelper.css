.auth-debug-helper {
  background-color: rgba(15, 15, 35, 0.8);
  border-radius: 12px;
  padding: 20px;
  margin: 20px auto;
  max-width: 500px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(64, 220, 255, 0.3);
  color: white;
}

.auth-debug-helper h3 {
  color: var(--primary, #00f2ff);
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.2rem;
  text-align: center;
  border-bottom: 1px solid rgba(64, 220, 255, 0.2);
  padding-bottom: 10px;
}

.debug-message {
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.debug-message.success {
  background-color: rgba(0, 200, 83, 0.2);
  border: 1px solid rgba(0, 200, 83, 0.3);
  color: #00c853;
}

.debug-message.error {
  background-color: rgba(255, 82, 82, 0.2);
  border: 1px solid rgba(255, 82, 82, 0.3);
  color: #ff5252;
}

.debug-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.debug-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.debug-form label {
  font-size: 0.9rem;
  color: var(--text-medium, #e0e0ff);
}

.debug-form input {
  padding: 10px;
  border-radius: 8px;
  border: 1px solid rgba(64, 220, 255, 0.3);
  background-color: rgba(10, 10, 26, 0.5);
  color: white;
  font-size: 0.9rem;
}

.debug-form input:focus {
  outline: none;
  border-color: var(--primary, #00f2ff);
  box-shadow: 0 0 10px rgba(0, 242, 255, 0.2);
}

.debug-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.debug-button {
  flex: 1;
  padding: 10px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.debug-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.debug-button.create {
  background: linear-gradient(90deg, #4657ce, #00f2ff);
  color: white;
}

.debug-button.login {
  background: linear-gradient(90deg, #00c853, #00f2ff);
  color: white;
}

.debug-button:disabled {
  opacity: 0.6;
  transform: none;
  cursor: not-allowed;
}

.debug-note {
  margin-top: 15px;
  font-size: 0.8rem;
  color: var(--text-dim, #a0a0d0);
  text-align: center;
}
