// Tipos para el dashboard inteligente

// Perfil de riesgo del usuario
export type RiskProfile = 'conservative' | 'moderate' | 'aggressive';

// Sentimiento del mercado
export type MarketSentiment = 'extreme_fear' | 'fear' | 'neutral' | 'greed' | 'extreme_greed';

// Preferencias del usuario
export interface UserPreferences {
  favoriteAssets: string[];
  preferredCategories: string[];
  riskProfile: RiskProfile;
  dashboardLayout: DashboardWidgetType[];
  theme: 'light' | 'dark' | 'system';
}

// Tipos de widgets disponibles
export type DashboardWidgetType = 
  | 'portfolio_summary' 
  | 'market_sentiment' 
  | 'smart_insights' 
  | 'recommended_assets'
  | 'trending_assets'
  | 'news_feed'
  | 'market_overview'
  | 'assets_table';

// Estadísticas del portafolio
export interface PortfolioStats {
  totalValue: number;
  totalInvestment: number;
  totalProfitLoss: number;
  totalProfitLossPercentage: number;
  assetCount: number;
  lastUpdated: Date;
  assetAllocation: AssetAllocation[];
  riskScore: number; // 1-10, donde 10 es el más arriesgado
}

// Asignación de activos por categoría
export interface AssetAllocation {
  category: string;
  percentage: number;
  value: number;
}

// Datos globales del mercado
export interface GlobalMarketData {
  total_market_cap: { [key: string]: number };
  total_volume: { [key: string]: number };
  market_cap_percentage: { [key: string]: number };
  market_cap_change_percentage_24h_usd: number;
  active_cryptocurrencies: number;
  markets: number;
  last_updated: string;
}

// Insight generado por IA
export interface AIInsight {
  id: string;
  type: 'success' | 'warning' | 'info' | 'opportunity' | 'caution';
  title: string;
  description: string;
  action: string;
  actionPath: string;
  relevanceScore: number; // 1-10, donde 10 es el más relevante
  timestamp: Date;
  source: 'portfolio' | 'market' | 'news' | 'technical' | 'onchain';
  assetIds?: string[]; // IDs de activos relacionados con este insight
}

// Activo recomendado
export interface RecommendedAsset {
  id: string;
  name: string;
  symbol: string;
  currentPrice: number;
  priceChangePercentage24h: number;
  image: string;
  marketCap: number;
  volume24h: number;
  recommendationReason: string;
  recommendationScore: number; // 1-10, donde 10 es la recomendación más fuerte
  riskLevel: 'low' | 'medium' | 'high';
  tags: string[]; // Etiquetas como 'defi', 'nft', 'layer1', etc.
}

// Análisis de sentimiento
export interface SentimentAnalysis {
  overallSentiment: MarketSentiment;
  fearGreedIndex: number; // 0-100
  sentimentFactors: {
    marketMomentum: number; // -100 a 100
    volatility: number; // 0-100
    btcDominance: number; // 0-100
    socialMediaSentiment: number; // -100 a 100
    tradingVolume: number; // 0-100
  };
  sentimentTrend: 'improving' | 'stable' | 'worsening';
  lastUpdated: Date;
}

// Estado del dashboard
export interface DashboardState {
  userPreferences: UserPreferences;
  portfolioStats: PortfolioStats;
  globalMarketData: GlobalMarketData | null;
  sentimentAnalysis: SentimentAnalysis | null;
  insights: AIInsight[];
  recommendedAssets: RecommendedAsset[];
  isLoading: {
    portfolio: boolean;
    market: boolean;
    insights: boolean;
    recommendations: boolean;
  };
  errors: {
    portfolio: string | null;
    market: string | null;
    insights: string | null;
    recommendations: string | null;
  };
}
