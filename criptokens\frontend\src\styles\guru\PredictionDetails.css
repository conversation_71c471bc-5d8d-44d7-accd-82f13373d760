.prediction-details {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.details-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.details-tabs button {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  color: var(--text-dim);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.details-tabs button:hover {
  color: var(--text-bright);
  background-color: rgba(255, 255, 255, 0.05);
}

.details-tabs button.active {
  color: var(--text-bright);
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom: 3px solid var(--primary);
}

.details-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

/* Aná<PERSON>is de sentimiento */
.sentiment-analysis {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sentiment-overview {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.sentiment-gauge {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.gauge-label {
  font-size: 0.9rem;
  color: var(--text-medium);
}

.gauge-container {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.gauge-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%);
  transform-origin: center;
  transition: transform 1s ease-out;
}

.gauge-fill.positive {
  background: var(--success);
}

.gauge-fill.negative {
  background: var(--error);
}

.gauge-fill.neutral {
  background: var(--warning);
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.gauge-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-bright);
}

.sentiment-sources {
  flex: 2;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.sources-label {
  font-size: 0.9rem;
  color: var(--text-medium);
}

.sources-chart {
  display: flex;
  height: 30px;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.sources-positive, .sources-neutral, .sources-negative {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.7);
  font-weight: 600;
  font-size: 0.9rem;
  transition: width 0.5s ease;
}

.sources-positive {
  background: var(--success);
}

.sources-neutral {
  background: var(--warning);
}

.sources-negative {
  background: var(--error);
}

.sources-legend {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.positive {
  background: var(--success);
}

.legend-color.neutral {
  background: var(--warning);
}

.legend-color.negative {
  background: var(--error);
}

.legend-label {
  font-size: 0.85rem;
  color: var(--text-medium);
}

.sentiment-keywords {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.sentiment-keywords h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.keywords-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.keyword-item {
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.keyword-item.positive {
  border-left: 3px solid var(--success);
}

.keyword-item.negative {
  border-left: 3px solid var(--error);
}

.keyword-item.neutral {
  border-left: 3px solid var(--warning);
}

.keyword-count {
  font-size: 0.8em;
  opacity: 0.7;
  margin-left: 0.25rem;
}

.sentiment-news {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.sentiment-news h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.news-item {
  padding: 1rem;
  border-radius: var(--radius-md);
  background: rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.news-item.positive {
  border-left: 3px solid var(--success);
}

.news-item.negative {
  border-left: 3px solid var(--error);
}

.news-item.neutral {
  border-left: 3px solid var(--warning);
}

.news-title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.news-title a {
  color: var(--text-bright);
  text-decoration: none;
  transition: color 0.2s ease;
}

.news-title a:hover {
  color: var(--primary);
}

.news-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-dim);
}

.news-sentiment {
  color: var(--text-medium);
}

/* Datos on-chain */
.onchain-analysis {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.onchain-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metric-title {
  font-size: 0.9rem;
  color: var(--text-dim);
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-bright);
}

.whale-activity {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.whale-activity h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.whale-flows {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.flow-item {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1rem;
  text-align: center;
}

.flow-label {
  font-size: 0.9rem;
  color: var(--text-dim);
  margin-bottom: 0.5rem;
}

.flow-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-bright);
}

.flow-value.positive {
  color: var(--success);
}

.flow-value.negative {
  color: var(--error);
}

.whale-transactions h5 {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
}

.transaction-item.inflow {
  border-left: 3px solid var(--success);
}

.transaction-item.outflow {
  border-left: 3px solid var(--error);
}

.transaction-direction {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-medium);
  min-width: 60px;
}

.transaction-value {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-bright);
  min-width: 100px;
}

.transaction-addresses {
  flex: 1;
  font-size: 0.85rem;
  color: var(--text-dim);
}

.transaction-time {
  font-size: 0.85rem;
  color: var(--text-dim);
}

/* Análisis técnico */
.technical-analysis {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.technical-signals {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.technical-signals h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.signal-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.signal-label {
  width: 90px;
  font-size: 0.9rem;
  color: var(--text-dim);
}

.signal-bar-container {
  flex: 1;
  height: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;
}

.signal-zero-line {
  position: absolute;
  top: 0;
  left: 50%;
  height: 100%;
  width: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.signal-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease-out, margin-left 1s ease-out;
}

.signal-bar.positive {
  background: var(--success);
}

.signal-bar.negative {
  background: var(--error);
}

.signal-bar.neutral {
  background: var(--warning);
}

.signal-value {
  width: 40px;
  text-align: right;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-bright);
}

.prediction-reasoning {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.prediction-reasoning h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-medium);
}

.reasoning-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.reasoning-item {
  position: relative;
  padding-left: 1.5rem;
  font-size: 0.95rem;
  color: var(--text-medium);
  line-height: 1.4;
}

.reasoning-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary);
}

/* Mensaje cuando no hay datos */
.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-dim);
  text-align: center;
  padding: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .details-tabs {
    overflow-x: auto;
    padding-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }

  .details-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .details-tabs button {
    white-space: nowrap;
    padding: 0.75rem 1rem;
  }
  
  .sentiment-overview {
    flex-direction: column;
  }
  
  .whale-flows {
    grid-template-columns: 1fr;
  }
  
  .transaction-item {
    flex-wrap: wrap;
  }
  
  .transaction-addresses {
    width: 100%;
    margin-top: 0.5rem;
  }
}
