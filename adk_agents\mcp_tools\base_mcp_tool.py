"""
Base MCP Tool for ADK Agents

This module provides a base class for MCP tools.
"""
import os
import json
import uuid
import aiohttp
from typing import Dict, Any, Optional

class BaseMcpTool:
    """Base class for MCP tools."""
    
    def __init__(self, server_name: str, base_url: Optional[str] = None):
        """
        Initialize the MCP tool.
        
        Args:
            server_name: Name of the MCP server
            base_url: Base URL of the MCP server (optional)
        """
        self.server_name = server_name
        self.base_url = base_url or self._get_default_url()
        self.session_id = None
    
    def _get_default_url(self) -> str:
        """Get the default URL for the MCP server."""
        env_var = f"{self.server_name.upper()}_MCP_URL"
        default_ports = {
            "crypto": "3101",
            "brave": "3102",
            "playwright": "3103",
            "context7": "7777"
        }
        port = default_ports.get(self.server_name.lower(), "3100")
        return os.getenv(env_var, f"http://localhost:{port}")
    
    async def create_session(self) -> str:
        """
        Create a session with the MCP server.
        
        Returns:
            Session ID
        """
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/mcp",
                json={
                    "jsonrpc": "2.0",
                    "method": "session.create",
                    "params": {},
                    "id": 1
                },
                headers={
                    "Content-Type": "application/json",
                    "Mcp-Session-Id": f"temp-{uuid.uuid4()}"
                }
            ) as response:
                if response.status != 200:
                    raise Exception(f"Failed to create session: {await response.text()}")
                
                data = await response.json()
                if data.get("error"):
                    raise Exception(f"Error creating session: {data['error']}")
                
                self.session_id = data["result"]["sessionId"]
                return self.session_id
    
    async def execute_tool(self, tool_name: str, input_params: Dict[str, Any]) -> Any:
        """
        Execute a tool on the MCP server.
        
        Args:
            tool_name: Name of the tool to execute
            input_params: Input parameters for the tool
            
        Returns:
            Tool execution result
        """
        # Create session if needed
        if not self.session_id:
            await self.create_session()
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/mcp",
                json={
                    "jsonrpc": "2.0",
                    "method": "execute",
                    "params": {
                        "tool": tool_name,
                        "input": input_params
                    },
                    "id": 2
                },
                headers={
                    "Content-Type": "application/json",
                    "Mcp-Session-Id": self.session_id
                }
            ) as response:
                if response.status != 200:
                    raise Exception(f"Failed to execute tool: {await response.text()}")
                
                data = await response.json()
                if data.get("error"):
                    raise Exception(f"Error executing tool: {data['error']}")
                
                return data["result"]
    
    async def close_session(self) -> None:
        """Close the session with the MCP server."""
        if not self.session_id:
            return
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/mcp",
                    json={
                        "jsonrpc": "2.0",
                        "method": "session.close",
                        "params": {},
                        "id": 3
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Mcp-Session-Id": self.session_id
                    }
                ) as response:
                    # We don't care about the response here
                    pass
        except Exception as e:
            print(f"Error closing session: {e}")
        finally:
            self.session_id = None
