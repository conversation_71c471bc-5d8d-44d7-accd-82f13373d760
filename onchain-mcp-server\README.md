# OnChain MCP Server

Un servidor MCP (Model Context Protocol) para análisis on-chain de criptomonedas.

## Descripción

El OnChain MCP Server proporciona herramientas para analizar datos on-chain de criptomonedas, incluyendo:

- Balances de carteras
- Transferencias de tokens
- Actividad de "ballenas" (grandes tenedores)
- Información de contratos
- Precios de gas
- Análisis de transacciones

## Instalación

```bash
# Clonar el repositorio
git clone https://github.com/CreastilloQRGen-AI/Criptokens.git
cd Criptokens/onchain-mcp-server

# Instalar dependencias
npm install
```

## Uso

### Iniciar el servidor

```bash
# Iniciar el servidor
npm start
```

El servidor se iniciará en el puerto 3104 por defecto. Puedes cambiar el puerto en el archivo `config.js`.

### Herramientas disponibles

El servidor expone las siguientes herramientas MCP:

#### Herramientas de carteras
- `getWalletBalance`: Obtiene el balance de ETH de una dirección
- `getTokenBalance`: Obtiene el balance de un token ERC-20 para una dirección
- `getCryptoBalance`: Obtiene el balance de una criptomoneda por su nombre
- `getMultipleTokenBalances`: Obtiene los balances de múltiples tokens para una dirección
- `getPortfolio`: Obtiene el portfolio completo de una dirección

#### Herramientas de transacciones
- `getTransactions`: Obtiene las transacciones de una dirección
- `getTokenTransfers`: Obtiene las transferencias de tokens de una dirección
- `getCryptoTransfers`: Obtiene las transferencias de una criptomoneda específica
- `analyzeTransactions`: Analiza las transacciones recientes de una dirección

#### Herramientas de red
- `getGasPrice`: Obtiene el precio actual del gas
- `getTokenWhales`: Obtiene información sobre las "ballenas" de un token
- `getCryptoWhales`: Obtiene información sobre las "ballenas" de una criptomoneda
- `getPopularWallets`: Obtiene información sobre carteras populares
- `analyzeWhaleActivity`: Analiza la actividad de las "ballenas" para una criptomoneda

#### Herramientas de contratos
- `getContractEvents`: Obtiene eventos de un contrato inteligente
- `getTokenInfo`: Obtiene información sobre un token ERC-20
- `getCryptoTokenInfo`: Obtiene información sobre un token de criptomoneda por su nombre
- `getMultipleCryptoTokenInfo`: Obtiene información sobre múltiples tokens de criptomonedas

#### Herramientas de tokens
- `getTokenTransfersByToken`: Obtiene las transferencias de un token específico
- `getCryptoTransfersByToken`: Obtiene las transferencias de un token de criptomoneda por su nombre
- `analyzeTokenActivity`: Analiza la actividad de un token
- `analyzeCryptoActivity`: Analiza la actividad de un token de criptomoneda por su nombre

## Integración con ADK Agents

El OnChain MCP Server se integra con los agentes ADK a través de la herramienta `OnChainMcpTool` en `adk_agents/mcp_tools/onchain_mcp_tool.py`.

## Configuración

La configuración del servidor se encuentra en el archivo `config.js`. Puedes modificar:

- API keys para Etherscan, BscScan, etc.
- URLs de las APIs
- Direcciones de tokens populares
- Direcciones de carteras populares
- Puerto del servidor

## Licencia

MIT
