import React, { useState, useEffect } from 'react';
import { getTopCryptocurrencies, getGlobalMarketData } from '../services/api';
import TechnicalAnalysisChart from './TechnicalAnalysisChart';
import Modern<PERSON>ogo from './ModernLogo';
import '../styles/ModernDashboard.css';

const ModernDashboard: React.FC = () => {
  // Estados para los datos
  const [cryptos, setCryptos] = useState<any[]>([]);
  const [marketData, setMarketData] = useState<any>(null);
  const [selectedCrypto, setSelectedCrypto] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<string>('7d');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [watchlist, setWatchlist] = useState<string[]>(['bitcoin', 'ethereum', 'solana']);

  // Obtener datos iniciales
  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 60000);
    return () => clearInterval(interval);
  }, []);

  // Función para obtener datos
  const fetchData = async () => {
    try {
      setIsLoading(true);
      const cryptoData = await getTopCryptocurrencies(50);
      setCryptos(cryptoData);
      
      if (!selectedCrypto) {
        const bitcoin = cryptoData.find((crypto: any) => crypto.id === 'bitcoin');
        if (bitcoin) setSelectedCrypto(bitcoin);
      }
      
      const globalData = await getGlobalMarketData();
      setMarketData(globalData?.data || null);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Función para formatear números grandes
  const formatNumber = (num: number): string => {
    if (num >= 1e12) return (num / 1e12).toFixed(2) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toFixed(2);
  };

  // Función para obtener el color según el cambio de precio
  const getPriceChangeColor = (change: number): string => {
    if (change > 0) return 'var(--color-positive)';
    if (change < 0) return 'var(--color-negative)';
    return 'var(--color-neutral)';
  };

  // Función para manejar la búsqueda
  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    
    const query = searchQuery.toLowerCase();
    const found = cryptos.find((crypto: any) => 
      crypto.name.toLowerCase().includes(query) || 
      crypto.symbol.toLowerCase().includes(query)
    );
    
    if (found) {
      setSelectedCrypto(found);
      setActiveTab('detail');
    }
  };

  // Función para alternar una criptomoneda en la lista de seguimiento
  const toggleWatchlist = (cryptoId: string) => {
    if (watchlist.includes(cryptoId)) {
      setWatchlist(watchlist.filter(id => id !== cryptoId));
    } else {
      setWatchlist([...watchlist, cryptoId]);
    }
  };

  // Renderizar el panel de información del mercado
  const renderMarketOverview = () => {
    if (!marketData) return <div className="loading-placeholder">Cargando datos del mercado...</div>;
    
    return (
      <div className="market-overview-panel">
        <div className="market-stats-grid">
          <div className="market-stat-card">
            <h3>Capitalización Total</h3>
            <div className="stat-value">${formatNumber(marketData.total_market_cap?.usd || 0)}</div>
            <div className="stat-change" style={{ color: getPriceChangeColor(marketData.market_cap_change_percentage_24h_usd || 0) }}>
              {marketData.market_cap_change_percentage_24h_usd > 0 ? '+' : ''}
              {marketData.market_cap_change_percentage_24h_usd?.toFixed(2)}%
            </div>
          </div>
          
          <div className="market-stat-card">
            <h3>Volumen 24h</h3>
            <div className="stat-value">${formatNumber(marketData.total_volume?.usd || 0)}</div>
          </div>
          
          <div className="market-stat-card">
            <h3>Dominancia BTC</h3>
            <div className="stat-value">{(marketData.market_cap_percentage?.btc || 0).toFixed(2)}%</div>
          </div>
          
          <div className="market-stat-card">
            <h3>Criptomonedas Activas</h3>
            <div className="stat-value">{marketData.active_cryptocurrencies || 0}</div>
          </div>
        </div>
        
        <div className="market-sentiment">
          <h3>Sentimiento del Mercado</h3>
          <div className="sentiment-meter" style={{ 
            '--sentiment-value': `${50 + (marketData.market_cap_change_percentage_24h_usd || 0)}%` 
          } as React.CSSProperties}>
            <div className="sentiment-indicator"></div>
            <div className="sentiment-labels">
              <span>Miedo</span>
              <span>Neutral</span>
              <span>Codicia</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Renderizar la tabla de criptomonedas
  const renderCryptoTable = () => {
    return (
      <div className="crypto-table-container">
        <div className="table-header">
          <div className="table-filters">
            <button className={activeTab === 'all' ? 'active' : ''} onClick={() => setActiveTab('all')}>
              Todas
            </button>
            <button className={activeTab === 'watchlist' ? 'active' : ''} onClick={() => setActiveTab('watchlist')}>
              Lista de Seguimiento
            </button>
            <button className={activeTab === 'gainers' ? 'active' : ''} onClick={() => setActiveTab('gainers')}>
              Mayores Ganancias
            </button>
            <button className={activeTab === 'losers' ? 'active' : ''} onClick={() => setActiveTab('losers')}>
              Mayores Pérdidas
            </button>
          </div>
        </div>
        
        <div className="crypto-table">
          <div className="table-head">
            <div className="table-row">
              <div className="table-cell">#</div>
              <div className="table-cell">Nombre</div>
              <div className="table-cell">Precio</div>
              <div className="table-cell">24h %</div>
              <div className="table-cell">7d %</div>
              <div className="table-cell">Cap. de Mercado</div>
              <div className="table-cell">Volumen (24h)</div>
              <div className="table-cell">Acciones</div>
            </div>
          </div>
          
          <div className="table-body">
            {getFilteredCryptos().map((crypto: any, index: number) => (
              <div 
                key={crypto.id} 
                className={`table-row ${selectedCrypto?.id === crypto.id ? 'selected' : ''}`}
                onClick={() => {
                  setSelectedCrypto(crypto);
                  setActiveTab('detail');
                }}
              >
                <div className="table-cell">{index + 1}</div>
                <div className="table-cell crypto-name-cell">
                  {crypto.image && <img src={crypto.image} alt={crypto.name} className="crypto-icon" />}
                  <div>
                    <span className="crypto-name">{crypto.name}</span>
                    <span className="crypto-symbol">{crypto.symbol.toUpperCase()}</span>
                  </div>
                </div>
                <div className="table-cell">${crypto.current_price.toLocaleString()}</div>
                <div 
                  className="table-cell" 
                  style={{ color: getPriceChangeColor(crypto.price_change_percentage_24h) }}
                >
                  {crypto.price_change_percentage_24h >= 0 ? '+' : ''}
                  {crypto.price_change_percentage_24h.toFixed(2)}%
                </div>
                <div 
                  className="table-cell" 
                  style={{ color: getPriceChangeColor(crypto.price_change_percentage_7d_in_currency || 0) }}
                >
                  {crypto.price_change_percentage_7d_in_currency >= 0 ? '+' : ''}
                  {(crypto.price_change_percentage_7d_in_currency || 0).toFixed(2)}%
                </div>
                <div className="table-cell">${formatNumber(crypto.market_cap)}</div>
                <div className="table-cell">${formatNumber(crypto.total_volume)}</div>
                <div className="table-cell actions-cell">
                  <button 
                    className={`watchlist-button ${watchlist.includes(crypto.id) ? 'active' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleWatchlist(crypto.id);
                    }}
                  >
                    {watchlist.includes(crypto.id) ? '★' : '☆'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Función para filtrar criptomonedas según la pestaña activa
  const getFilteredCryptos = () => {
    if (!cryptos.length) return [];
    
    switch (activeTab) {
      case 'watchlist':
        return cryptos.filter(crypto => watchlist.includes(crypto.id));
      case 'gainers':
        return [...cryptos].sort((a, b) => b.price_change_percentage_24h - a.price_change_percentage_24h).slice(0, 20);
      case 'losers':
        return [...cryptos].sort((a, b) => a.price_change_percentage_24h - b.price_change_percentage_24h).slice(0, 20);
      default:
        return cryptos.slice(0, 50);
    }
  };

  // Renderizar el detalle de una criptomoneda
  const renderCryptoDetail = () => {
    if (!selectedCrypto) return null;
    
    return (
      <div className="crypto-detail-panel">
        <div className="crypto-detail-header">
          <div className="crypto-title">
            {selectedCrypto.image && <img src={selectedCrypto.image} alt={selectedCrypto.name} className="crypto-icon-large" />}
            <div>
              <h2>{selectedCrypto.name} <span className="crypto-symbol">({selectedCrypto.symbol.toUpperCase()})</span></h2>
              <div className="crypto-rank">Rank #{selectedCrypto.market_cap_rank}</div>
            </div>
          </div>
          
          <div className="crypto-price-info">
            <div className="current-price">${selectedCrypto.current_price.toLocaleString()}</div>
            <div 
              className="price-change" 
              style={{ color: getPriceChangeColor(selectedCrypto.price_change_percentage_24h) }}
            >
              {selectedCrypto.price_change_percentage_24h >= 0 ? '▲' : '▼'}
              {Math.abs(selectedCrypto.price_change_percentage_24h).toFixed(2)}%
            </div>
          </div>
        </div>
        
        <div className="chart-controls">
          <div className="time-range-selector">
            {['24h', '7d', '30d', '90d', '1y', 'max'].map(range => (
              <button 
                key={range}
                className={timeRange === range ? 'active' : ''}
                onClick={() => setTimeRange(range)}
              >
                {range}
              </button>
            ))}
          </div>
          
          <div className="chart-type-selector">
            <button className="active">Velas</button>
            <button>Línea</button>
          </div>
        </div>
        
        <div className="chart-container">
          <TechnicalAnalysisChart
            data={{ labels: [], values: [] }}
            cryptoId={selectedCrypto.id}
            cryptoName={selectedCrypto.name}
            cryptoSymbol={selectedCrypto.symbol}
            color={getPriceChangeColor(selectedCrypto.price_change_percentage_24h)}
            height={400}
            width={800}
          />
        </div>
        
        <div className="crypto-stats-grid">
          <div className="stat-card">
            <div className="stat-title">Capitalización de Mercado</div>
            <div className="stat-value">${formatNumber(selectedCrypto.market_cap)}</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-title">Volumen 24h</div>
            <div className="stat-value">${formatNumber(selectedCrypto.total_volume)}</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-title">Suministro Circulante</div>
            <div className="stat-value">{formatNumber(selectedCrypto.circulating_supply)} {selectedCrypto.symbol.toUpperCase()}</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-title">Suministro Total</div>
            <div className="stat-value">{formatNumber(selectedCrypto.total_supply || 0)} {selectedCrypto.symbol.toUpperCase()}</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-title">Máximo Histórico</div>
            <div className="stat-value">${selectedCrypto.ath?.toLocaleString()}</div>
            <div className="stat-subtitle">
              {new Date(selectedCrypto.ath_date).toLocaleDateString()}
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-title">Mínimo Histórico</div>
            <div className="stat-value">${selectedCrypto.atl?.toLocaleString()}</div>
            <div className="stat-subtitle">
              {new Date(selectedCrypto.atl_date).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="modern-dashboard">
      <header className="dashboard-header">
        <div className="header-left">
          <ModernLogo size="small" />
        </div>
        
        <div className="header-center">
          <div className="search-container">
            <input
              type="text"
              placeholder="Buscar criptomoneda..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button className="search-button" onClick={handleSearch}>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div className="header-right">
          <button className="api-button">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 4H4C2.89 4 2.01 4.89 2.01 6L2 18C2 19.11 2.89 20 4 20H20C21.11 20 22 19.11 22 18V6C22 4.89 21.11 4 20 4ZM20 18H4V12H20V18ZM20 8H4V6H20V8Z" fill="currentColor"/>
            </svg>
            API
          </button>
          
          <button className="theme-toggle">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 8.69V4H15.31L12 0.69L8.69 4H4V8.69L0.69 12L4 15.31V20H8.69L12 23.31L15.31 20H20V15.31L23.31 12L20 8.69ZM12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C15.31 6 18 8.69 18 12C18 15.31 15.31 18 12 18ZM12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12C16 9.79 14.21 8 12 8Z" fill="currentColor"/>
            </svg>
          </button>
          
          <button className="user-menu">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 5C13.66 5 15 6.34 15 8C15 9.66 13.66 11 12 11C10.34 11 9 9.66 9 8C9 6.34 10.34 5 12 5ZM12 19.2C9.5 19.2 7.29 17.92 6 15.98C6.03 13.99 10 12.9 12 12.9C13.99 12.9 17.97 13.99 18 15.98C16.71 17.92 14.5 19.2 12 19.2Z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </header>
      
      <main className="dashboard-content">
        <div className="content-tabs">
          <button 
            className={activeTab === 'overview' ? 'active' : ''} 
            onClick={() => setActiveTab('overview')}
          >
            Resumen del Mercado
          </button>
          <button 
            className={activeTab === 'all' ? 'active' : ''} 
            onClick={() => setActiveTab('all')}
          >
            Todas las Criptomonedas
          </button>
          <button 
            className={activeTab === 'detail' && selectedCrypto ? 'active' : ''} 
            onClick={() => selectedCrypto && setActiveTab('detail')}
            disabled={!selectedCrypto}
          >
            {selectedCrypto ? selectedCrypto.name : 'Detalle'}
          </button>
          <button 
            className={activeTab === 'portfolio' ? 'active' : ''} 
            onClick={() => setActiveTab('portfolio')}
          >
            Mi Portafolio
          </button>
        </div>
        
        <div className="content-area">
          {isLoading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Cargando datos...</p>
            </div>
          ) : (
            <>
              {activeTab === 'overview' && renderMarketOverview()}
              {(activeTab === 'all' || activeTab === 'watchlist' || activeTab === 'gainers' || activeTab === 'losers') && renderCryptoTable()}
              {activeTab === 'detail' && renderCryptoDetail()}
              {activeTab === 'portfolio' && (
                <div className="portfolio-placeholder">
                  <h2>Mi Portafolio</h2>
                  <p>Inicia sesión para ver y gestionar tu portafolio de criptomonedas.</p>
                  <button className="login-button">Iniciar Sesión</button>
                </div>
              )}
            </>
          )}
        </div>
      </main>
      
      <footer className="dashboard-footer">
        <div className="footer-left">
          <p>© 2023 Criptokens. Todos los derechos reservados.</p>
        </div>
        
        <div className="footer-right">
          <div className="api-status online">
            <span className="status-dot"></span>
            API Conectada
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ModernDashboard;
