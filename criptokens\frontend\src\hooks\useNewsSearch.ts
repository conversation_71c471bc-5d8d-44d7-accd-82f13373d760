import { useState, useEffect, useCallback } from 'react';
import { searchCryptoNews, searchCoinNews, NewsItem } from '../services/braveSearch';

interface UseNewsSearchProps {
  initialQuery?: string;
  coinId?: string;
  coinName?: string;
  initialCount?: number;
  initialFreshness?: string;
}

interface UseNewsSearchResult {
  news: NewsItem[];
  isLoading: boolean;
  error: string | null;
  fetchNews: (query?: string, freshness?: string) => Promise<void>;
  fetchMoreNews: () => Promise<void>;
  resetNews: () => void;
}

export const useNewsSearch = ({
  initialQuery = '',
  coinId,
  coinName,
  initialCount = 10,
  initialFreshness = ''
}: UseNewsSearchProps = {}): UseNewsSearchResult => {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [query, setQuery] = useState<string>(initialQuery);
  const [count] = useState<number>(initialCount);
  const [offset, setOffset] = useState<number>(0);
  const [freshness, setFreshness] = useState<string>(initialFreshness);
  const [hasMore, setHasMore] = useState<boolean>(true);

  // Función para buscar noticias
  const fetchNews = useCallback(async (newQuery?: string, newFreshness?: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Actualizar query y freshness si se proporcionan
      const searchQuery = newQuery !== undefined ? newQuery : query;
      const searchFreshness = newFreshness !== undefined ? newFreshness : freshness;
      
      // Actualizar estado
      setQuery(searchQuery);
      setFreshness(searchFreshness);
      setOffset(0);
      
      // Realizar la búsqueda
      let results: NewsItem[];
      
      if (coinId && coinName) {
        // Búsqueda específica para una criptomoneda
        results = await searchCoinNews(coinId, coinName, count, 0, searchFreshness);
      } else {
        // Búsqueda general de criptomonedas
        results = await searchCryptoNews(searchQuery, count, 0, searchFreshness);
      }
      
      setNews(results);
      setHasMore(results.length === count);
    } catch (err) {
      console.error('Error al buscar noticias:', err);
      setError('No se pudieron cargar las noticias. Por favor, inténtalo de nuevo más tarde.');
    } finally {
      setIsLoading(false);
    }
  }, [query, freshness, count, coinId, coinName]);

  // Función para cargar más noticias (paginación)
  const fetchMoreNews = useCallback(async () => {
    if (!hasMore || isLoading) return;
    
    setIsLoading(true);
    
    try {
      const newOffset = offset + count;
      
      // Realizar la búsqueda con el nuevo offset
      let results: NewsItem[];
      
      if (coinId && coinName) {
        // Búsqueda específica para una criptomoneda
        results = await searchCoinNews(coinId, coinName, count, newOffset, freshness);
      } else {
        // Búsqueda general de criptomonedas
        results = await searchCryptoNews(query, count, newOffset, freshness);
      }
      
      if (results.length > 0) {
        setNews(prevNews => [...prevNews, ...results]);
        setOffset(newOffset);
        setHasMore(results.length === count);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      console.error('Error al cargar más noticias:', err);
      setError('No se pudieron cargar más noticias. Por favor, inténtalo de nuevo más tarde.');
    } finally {
      setIsLoading(false);
    }
  }, [query, freshness, count, offset, hasMore, isLoading, coinId, coinName]);

  // Función para resetear las noticias
  const resetNews = useCallback(() => {
    setNews([]);
    setOffset(0);
    setHasMore(true);
    setError(null);
  }, []);

  // Cargar noticias iniciales
  useEffect(() => {
    fetchNews();
  }, [fetchNews]);

  return {
    news,
    isLoading,
    error,
    fetchNews,
    fetchMoreNews,
    resetNews
  };
};
