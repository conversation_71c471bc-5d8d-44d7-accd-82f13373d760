"""
<PERSON><PERSON><PERSON> to start the Guru Cripto Orchestrator with MCP integration
"""
import os
import sys
import asyncio
from google.adk.runtime import Runtime

# Add the adk_agents directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "adk_agents"))

# Import the Guru Cripto Orchestrator
from guru_agent.agent_mcp import guru_agent_mcp

async def main():
    """Main function to start the Guru Cripto Orchestrator"""
    print("Starting Guru Cripto Orchestrator with MCP integration...")
    
    # Create a runtime
    runtime = Runtime()
    
    # Create a session
    session = runtime.new_session()
    
    # Test the agent with a query
    query = "What's your prediction for Bitcoin price in the next week?"
    print(f"\nTesting with query: {query}")
    
    response = await guru_agent_mcp.run_async(
        session=session,
        query=query
    )
    
    print("\nResponse from Guru Cripto Orchestrator:")
    print(response)
    
    print("\nGuru Cripto Orchestrator is ready to use!")
    print("You can now use the API to interact with it.")

if __name__ == "__main__":
    asyncio.run(main())
