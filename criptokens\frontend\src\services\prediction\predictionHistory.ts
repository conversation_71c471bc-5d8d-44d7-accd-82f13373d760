import { CryptoPrediction } from '../ai/predictionService';
import { getFirestore, collection, addDoc, query, where, getDocs, orderBy, limit } from 'firebase/firestore';

/**
 * Interfaz para el historial de predicciones con resultados
 */
export interface PredictionWithResult extends CryptoPrediction {
  actualResult?: {
    finalPrice: number;
    priceChange: number;
    priceChangePercent: number;
    wasCorrect: boolean;
    accuracy: number;
  };
}

/**
 * Guarda una predicción en Firestore
 * @param prediction Predicción a guardar
 * @returns ID del documento creado
 */
export const savePrediction = async (prediction: CryptoPrediction): Promise<string> => {
  try {
    const db = getFirestore();
    const predictionsRef = collection(db, 'predictions');
    
    const docRef = await addDoc(predictionsRef, {
      ...prediction,
      timestamp: new Date()
    });
    
    console.log('Predicción guardada con ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error al guardar predicción:', error);
    throw error;
  }
};

/**
 * Obtiene el historial de predicciones para una criptomoneda
 * @param cryptoId ID de la criptomoneda
 * @param limit Número máximo de predicciones a obtener
 * @returns Lista de predicciones
 */
export const getPredictionHistory = async (
  cryptoId: string,
  maxResults: number = 10
): Promise<PredictionWithResult[]> => {
  try {
    const db = getFirestore();
    const predictionsRef = collection(db, 'predictions');
    
    const q = query(
      predictionsRef,
      where('cryptoId', '==', cryptoId),
      orderBy('timestamp', 'desc'),
      limit(maxResults)
    );
    
    const querySnapshot = await getDocs(q);
    const predictions: PredictionWithResult[] = [];
    
    querySnapshot.forEach(doc => {
      const data = doc.data() as CryptoPrediction;
      predictions.push({
        ...data,
        timestamp: data.timestamp instanceof Date ? data.timestamp : new Date(data.timestamp)
      });
    });
    
    return predictions;
  } catch (error) {
    console.error('Error al obtener historial de predicciones:', error);
    return [];
  }
};

/**
 * Actualiza una predicción con el resultado real
 * @param predictionId ID de la predicción
 * @param actualResult Resultado real
 */
export const updatePredictionResult = async (
  predictionId: string,
  actualResult: {
    finalPrice: number;
    priceChange: number;
    priceChangePercent: number;
    wasCorrect: boolean;
    accuracy: number;
  }
): Promise<void> => {
  try {
    const db = getFirestore();
    const predictionRef = collection(db, 'predictions');
    
    // En una implementación real, aquí actualizaríamos el documento en Firestore
    console.log('Actualización de predicción simulada:', predictionId, actualResult);
  } catch (error) {
    console.error('Error al actualizar resultado de predicción:', error);
    throw error;
  }
};

/**
 * Calcula estadísticas de precisión para las predicciones
 * @param cryptoId ID de la criptomoneda (opcional)
 * @returns Estadísticas de precisión
 */
export const getPredictionStats = async (cryptoId?: string): Promise<{
  totalPredictions: number;
  correctPredictions: number;
  accuracy: number;
  byDirection: {
    up: { total: number; correct: number; accuracy: number };
    down: { total: number; correct: number; accuracy: number };
    sideways: { total: number; correct: number; accuracy: number };
  };
}> => {
  try {
    // En una implementación real, aquí consultaríamos Firestore
    // Por ahora, devolvemos datos simulados
    
    return {
      totalPredictions: 100,
      correctPredictions: 75,
      accuracy: 75,
      byDirection: {
        up: { total: 50, correct: 40, accuracy: 80 },
        down: { total: 30, correct: 22, accuracy: 73.33 },
        sideways: { total: 20, correct: 13, accuracy: 65 }
      }
    };
  } catch (error) {
    console.error('Error al obtener estadísticas de predicciones:', error);
    
    return {
      totalPredictions: 0,
      correctPredictions: 0,
      accuracy: 0,
      byDirection: {
        up: { total: 0, correct: 0, accuracy: 0 },
        down: { total: 0, correct: 0, accuracy: 0 },
        sideways: { total: 0, correct: 0, accuracy: 0 }
      }
    };
  }
};
