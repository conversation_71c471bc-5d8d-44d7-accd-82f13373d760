import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/NewAuthContext';
import '../../styles/academy/AcademyDashboard.css';

interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
  duration: string;
  thumbnail?: string;
  instructor: {
    name: string;
    avatar: string;
  };
  modules: {
    id: string;
    title: string;
  }[];
  progress?: {
    percentComplete: number;
    lastAccessed: string;
  };
}

interface Certificate {
  id: string;
  courseId: string;
  courseTitle: string;
  issueDate: string;
}

const AcademyDashboard: React.FC = () => {
  const { currentUser } = useAuth();
  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);
  const [recommendedCourses, setRecommendedCourses] = useState<Course[]>([]);
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<'enrolled' | 'recommended' | 'certificates'>('enrolled');

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);

        // En una implementación real, estas serían llamadas a la API
        // Por ahora, simulamos con datos de ejemplo

        // Simular cursos inscritos
        const mockEnrolledCourses: Course[] = [
          {
            id: 'crypto-fundamentals',
            title: 'Fundamentos de Criptomonedas',
            description: 'Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.',
            level: 'beginner',
            duration: '4 horas',
            thumbnail: '/images/courses/crypto-fundamentals.svg',
            instructor: {
              name: 'Alex Rodríguez',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
            },
            modules: [
              { id: 'module-1', title: '¿Qué son las Criptomonedas?' },
              { id: 'module-2', title: 'Blockchain: La Tecnología Detrás' },
              { id: 'module-3', title: 'Bitcoin: La Primera Criptomoneda' },
              { id: 'module-4', title: 'Wallets y Seguridad' }
            ],
            progress: {
              percentComplete: 35,
              lastAccessed: '2023-07-15T14:30:00Z'
            }
          },
          {
            id: 'defi-essentials',
            title: 'Finanzas Descentralizadas (DeFi)',
            description: 'Descubre el mundo de las finanzas descentralizadas, protocolos, oportunidades y riesgos en este ecosistema emergente.',
            level: 'intermediate',
            duration: '5 horas',
            thumbnail: '/images/courses/defi-essentials.svg',
            instructor: {
              name: 'Laura Martínez',
              avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
            },
            modules: [
              { id: 'module-1', title: 'Introducción a DeFi' },
              { id: 'module-2', title: 'Protocolos de Préstamos' },
              { id: 'module-3', title: 'Exchanges Descentralizados (DEX)' }
            ],
            progress: {
              percentComplete: 10,
              lastAccessed: '2023-07-10T09:15:00Z'
            }
          }
        ];

        // Simular cursos recomendados
        const mockRecommendedCourses: Course[] = [
          {
            id: 'crypto-trading',
            title: 'Trading de Criptomonedas',
            description: 'Aprende estrategias de trading, análisis técnico y gestión de riesgos para operar en el mercado de criptomonedas.',
            level: 'intermediate',
            duration: '6 horas',
            thumbnail: '/images/courses/crypto-trading.svg',
            instructor: {
              name: 'Carlos Vega',
              avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
            },
            modules: [
              { id: 'module-1', title: 'Fundamentos del Trading' },
              { id: 'module-2', title: 'Análisis Técnico Básico' },
              { id: 'module-3', title: 'Análisis Técnico Avanzado' }
            ]
          },
          {
            id: 'crypto-security',
            title: 'Seguridad en Criptomonedas',
            description: 'Protege tus activos digitales aprendiendo las mejores prácticas de seguridad en el mundo de las criptomonedas.',
            level: 'beginner',
            duration: '3 horas',
            thumbnail: '/images/courses/crypto-security.svg',
            instructor: {
              name: 'Elena Gómez',
              avatar: 'https://randomuser.me/api/portraits/women/22.jpg'
            },
            modules: [
              { id: 'module-1', title: 'Amenazas y Riesgos en Criptomonedas' },
              { id: 'module-2', title: 'Tipos de Wallets y su Seguridad' },
              { id: 'module-3', title: 'Gestión Segura de Claves Privadas' }
            ]
          }
        ];

        // Simular certificados
        const mockCertificates: Certificate[] = [
          {
            id: 'cert-123',
            courseId: 'blockchain-basics',
            courseTitle: 'Fundamentos de Blockchain',
            issueDate: '2023-06-20T00:00:00Z'
          }
        ];

        setEnrolledCourses(mockEnrolledCourses);
        setRecommendedCourses(mockRecommendedCourses);
        setCertificates(mockCertificates);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [currentUser]);

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderEnrolledCourses = () => {
    if (enrolledCourses.length === 0) {
      return (
        <div className="no-courses">
          <p>No estás inscrito en ningún curso todavía.</p>
          <Link to="../courses" className="btn-primary">Explorar Cursos</Link>
        </div>
      );
    }

    return (
      <div className="enrolled-courses">
        {enrolledCourses.map(course => (
          <div key={course.id} className="course-card enrolled">
            <div className="course-image">
              {course.thumbnail ? (
                <img src={course.thumbnail} alt={course.title} />
              ) : (
                <div className="placeholder-image"></div>
              )}
              <div className={`course-level ${course.level}`}>
                {course.level === 'beginner' && 'Principiante'}
                {course.level === 'intermediate' && 'Intermedio'}
                {course.level === 'advanced' && 'Avanzado'}
              </div>
            </div>
            <div className="course-content">
              <h3>{course.title}</h3>
              <div className="course-progress">
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${course.progress?.percentComplete || 0}%` }}
                  ></div>
                </div>
                <span className="progress-text">{course.progress?.percentComplete || 0}% completado</span>
              </div>
              <div className="course-meta">
                <span className="course-duration">
                  <i className="fas fa-clock"></i> {course.duration}
                </span>
                <span className="course-modules">
                  <i className="fas fa-layer-group"></i> {course.modules.length} módulos
                </span>
              </div>
              <div className="course-last-access">
                Último acceso: {course.progress?.lastAccessed ? formatDate(course.progress.lastAccessed) : 'Nunca'}
              </div>
              <Link to={`../courses/${course.id}`} className="continue-button">
                Continuar Curso
              </Link>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderRecommendedCourses = () => {
    return (
      <div className="recommended-courses">
        {recommendedCourses.map(course => (
          <div key={course.id} className="course-card recommended">
            <div className="course-image">
              {course.thumbnail ? (
                <img src={course.thumbnail} alt={course.title} />
              ) : (
                <div className="placeholder-image"></div>
              )}
              <div className={`course-level ${course.level}`}>
                {course.level === 'beginner' && 'Principiante'}
                {course.level === 'intermediate' && 'Intermedio'}
                {course.level === 'advanced' && 'Avanzado'}
              </div>
            </div>
            <div className="course-content">
              <h3>{course.title}</h3>
              <p className="course-description">{course.description}</p>
              <div className="course-meta">
                <span className="course-duration">
                  <i className="fas fa-clock"></i> {course.duration}
                </span>
                <span className="course-modules">
                  <i className="fas fa-layer-group"></i> {course.modules.length} módulos
                </span>
              </div>
              <div className="course-instructor">
                <img src={course.instructor.avatar} alt={course.instructor.name} />
                <span>{course.instructor.name}</span>
              </div>
              <Link to={`../courses/${course.id}`} className="view-course-button">
                Ver Curso
              </Link>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderCertificates = () => {
    if (certificates.length === 0) {
      return (
        <div className="no-certificates">
          <p>No has obtenido certificados todavía.</p>
          <p>Completa un curso para obtener tu primer certificado.</p>
        </div>
      );
    }

    return (
      <div className="certificates-list">
        {certificates.map(certificate => (
          <div key={certificate.id} className="certificate-card">
            <div className="certificate-icon">
              <i className="fas fa-certificate"></i>
            </div>
            <div className="certificate-content">
              <h3>{certificate.courseTitle}</h3>
              <p>Fecha de emisión: {formatDate(certificate.issueDate)}</p>
              <div className="certificate-actions">
                <button className="btn-secondary">
                  <i className="fas fa-download"></i> Descargar
                </button>
                <button className="btn-secondary">
                  <i className="fas fa-share-alt"></i> Compartir
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="academy-dashboard loading">
        <div className="loading-spinner"></div>
        <p>Cargando datos...</p>
      </div>
    );
  }

  return (
    <div className="academy-dashboard">
      <div className="dashboard-header">
        <h2>Mi Aprendizaje</h2>
        <div className="dashboard-actions">
          <Link to="../courses" className="btn-primary">
            <i className="fas fa-book"></i> Explorar Cursos
          </Link>
        </div>
      </div>

      <div className="dashboard-tabs">
        <button
          className={`tab-button ${activeTab === 'enrolled' ? 'active' : ''}`}
          onClick={() => setActiveTab('enrolled')}
        >
          Mis Cursos
        </button>
        <button
          className={`tab-button ${activeTab === 'recommended' ? 'active' : ''}`}
          onClick={() => setActiveTab('recommended')}
        >
          Recomendados
        </button>
        <button
          className={`tab-button ${activeTab === 'certificates' ? 'active' : ''}`}
          onClick={() => setActiveTab('certificates')}
        >
          Certificados
        </button>
      </div>

      <div className="dashboard-content">
        {activeTab === 'enrolled' && renderEnrolledCourses()}
        {activeTab === 'recommended' && renderRecommendedCourses()}
        {activeTab === 'certificates' && renderCertificates()}
      </div>
    </div>
  );
};

export default AcademyDashboard;
