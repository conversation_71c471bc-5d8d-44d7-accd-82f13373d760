import React from 'react';
import '../styles/CoinStats.css';

interface CoinStatsProps {
  coinData: any;
  formatNumber: (num: number, maximumFractionDigits?: number) => string;
  formatMarketCap: (marketCap: number) => string;
}

const CoinStats: React.FC<CoinStatsProps> = ({ 
  coinData, 
  formatNumber, 
  formatMarketCap 
}) => {
  // Función para calcular el porcentaje de suministro circulante
  const calculateSupplyPercentage = (): number => {
    if (!coinData.market_data?.circulating_supply || !coinData.market_data?.max_supply) {
      return 0;
    }
    
    return (coinData.market_data.circulating_supply / coinData.market_data.max_supply) * 100;
  };

  return (
    <div className="coin-stats-container">
      <h3>Estadísticas de {coinData.name}</h3>
      
      <div className="stats-grid">
        {/* Capitalización de mercado */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-chart-pie"></i>
            Capitalización de mercado
          </div>
          <div className="stat-value">
            {coinData.market_data?.market_cap?.usd 
              ? formatMarketCap(coinData.market_data.market_cap.usd) 
              : 'N/A'}
          </div>
          {coinData.market_data?.market_cap_change_percentage_24h && (
            <div className={`stat-change ${coinData.market_data.market_cap_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
              <i className={`fas ${coinData.market_data.market_cap_change_percentage_24h >= 0 ? 'fa-caret-up' : 'fa-caret-down'}`}></i>
              {coinData.market_data.market_cap_change_percentage_24h.toFixed(2)}% (24h)
            </div>
          )}
        </div>
        
        {/* Volumen de comercio 24h */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-exchange-alt"></i>
            Volumen (24h)
          </div>
          <div className="stat-value">
            {coinData.market_data?.total_volume?.usd 
              ? formatMarketCap(coinData.market_data.total_volume.usd) 
              : 'N/A'}
          </div>
        </div>
        
        {/* Valoración totalmente diluida */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-water"></i>
            Valoración totalmente diluida
          </div>
          <div className="stat-value">
            {coinData.market_data?.fully_diluted_valuation?.usd 
              ? formatMarketCap(coinData.market_data.fully_diluted_valuation.usd) 
              : 'N/A'}
          </div>
        </div>
        
        {/* Suministro circulante */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-coins"></i>
            Suministro circulante
          </div>
          <div className="stat-value">
            {coinData.market_data?.circulating_supply 
              ? formatNumber(coinData.market_data.circulating_supply, 0) 
              : 'N/A'} {coinData.symbol.toUpperCase()}
          </div>
          {coinData.market_data?.max_supply && (
            <div className="supply-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${calculateSupplyPercentage()}%` }}
                ></div>
              </div>
              <div className="progress-text">
                {calculateSupplyPercentage().toFixed(2)}% del suministro máximo
              </div>
            </div>
          )}
        </div>
        
        {/* Suministro total */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-layer-group"></i>
            Suministro total
          </div>
          <div className="stat-value">
            {coinData.market_data?.total_supply 
              ? formatNumber(coinData.market_data.total_supply, 0) 
              : 'N/A'} {coinData.symbol.toUpperCase()}
          </div>
        </div>
        
        {/* Suministro máximo */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-trophy"></i>
            Suministro máximo
          </div>
          <div className="stat-value">
            {coinData.market_data?.max_supply 
              ? formatNumber(coinData.market_data.max_supply, 0) 
              : '∞'} {coinData.symbol.toUpperCase()}
          </div>
        </div>
        
        {/* Máximo histórico */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-arrow-up"></i>
            Máximo histórico
          </div>
          <div className="stat-value">
            {coinData.market_data?.ath?.usd 
              ? `$${formatNumber(coinData.market_data.ath.usd)}` 
              : 'N/A'}
          </div>
          {coinData.market_data?.ath_change_percentage?.usd && (
            <div className={`stat-change ${coinData.market_data.ath_change_percentage.usd >= 0 ? 'positive' : 'negative'}`}>
              <i className={`fas ${coinData.market_data.ath_change_percentage.usd >= 0 ? 'fa-caret-up' : 'fa-caret-down'}`}></i>
              {coinData.market_data.ath_change_percentage.usd.toFixed(2)}%
            </div>
          )}
          {coinData.market_data?.ath_date?.usd && (
            <div className="stat-date">
              {new Date(coinData.market_data.ath_date.usd).toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </div>
          )}
        </div>
        
        {/* Mínimo histórico */}
        <div className="stat-item">
          <div className="stat-label">
            <i className="fas fa-arrow-down"></i>
            Mínimo histórico
          </div>
          <div className="stat-value">
            {coinData.market_data?.atl?.usd 
              ? `$${formatNumber(coinData.market_data.atl.usd)}` 
              : 'N/A'}
          </div>
          {coinData.market_data?.atl_change_percentage?.usd && (
            <div className={`stat-change ${coinData.market_data.atl_change_percentage.usd >= 0 ? 'positive' : 'negative'}`}>
              <i className={`fas ${coinData.market_data.atl_change_percentage.usd >= 0 ? 'fa-caret-up' : 'fa-caret-down'}`}></i>
              {coinData.market_data.atl_change_percentage.usd.toFixed(2)}%
            </div>
          )}
          {coinData.market_data?.atl_date?.usd && (
            <div className="stat-date">
              {new Date(coinData.market_data.atl_date.usd).toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </div>
          )}
        </div>
      </div>
      
      {/* Información adicional */}
      <div className="additional-info">
        <h4>Información adicional</h4>
        <div className="info-grid">
          {/* Sitio web */}
          <div className="info-item">
            <div className="info-label">Sitio web</div>
            <div className="info-value">
              {coinData.links?.homepage?.filter(Boolean)[0] ? (
                <a 
                  href={coinData.links.homepage.filter(Boolean)[0]} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="info-link"
                >
                  {coinData.links.homepage.filter(Boolean)[0].replace(/^https?:\/\//, '').replace(/\/$/, '')}
                  <i className="fas fa-external-link-alt"></i>
                </a>
              ) : (
                'N/A'
              )}
            </div>
          </div>
          
          {/* Explorador de bloques */}
          <div className="info-item">
            <div className="info-label">Explorador</div>
            <div className="info-value">
              {coinData.links?.blockchain_site?.filter(Boolean)[0] ? (
                <a 
                  href={coinData.links.blockchain_site.filter(Boolean)[0]} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="info-link"
                >
                  {coinData.links.blockchain_site.filter(Boolean)[0].replace(/^https?:\/\//, '').replace(/\/$/, '')}
                  <i className="fas fa-external-link-alt"></i>
                </a>
              ) : (
                'N/A'
              )}
            </div>
          </div>
          
          {/* Comunidad */}
          <div className="info-item">
            <div className="info-label">Comunidad</div>
            <div className="info-value info-social">
              {coinData.links?.twitter_screen_name && (
                <a 
                  href={`https://twitter.com/${coinData.links.twitter_screen_name}`} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="social-link"
                  title="Twitter"
                >
                  <i className="fab fa-twitter"></i>
                </a>
              )}
              {coinData.links?.facebook_username && (
                <a 
                  href={`https://facebook.com/${coinData.links.facebook_username}`} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="social-link"
                  title="Facebook"
                >
                  <i className="fab fa-facebook"></i>
                </a>
              )}
              {coinData.links?.subreddit_url && (
                <a 
                  href={coinData.links.subreddit_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="social-link"
                  title="Reddit"
                >
                  <i className="fab fa-reddit"></i>
                </a>
              )}
              {coinData.links?.telegram_channel_identifier && (
                <a 
                  href={`https://t.me/${coinData.links.telegram_channel_identifier}`} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="social-link"
                  title="Telegram"
                >
                  <i className="fab fa-telegram"></i>
                </a>
              )}
            </div>
          </div>
          
          {/* Código fuente */}
          <div className="info-item">
            <div className="info-label">Código fuente</div>
            <div className="info-value">
              {coinData.links?.repos_url?.github?.filter(Boolean)[0] ? (
                <a 
                  href={coinData.links.repos_url.github.filter(Boolean)[0]} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="info-link"
                >
                  GitHub
                  <i className="fas fa-external-link-alt"></i>
                </a>
              ) : (
                'N/A'
              )}
            </div>
          </div>
          
          {/* Categorías */}
          <div className="info-item">
            <div className="info-label">Categorías</div>
            <div className="info-value">
              {coinData.categories?.filter(Boolean).length > 0 ? (
                <div className="categories-list">
                  {coinData.categories.filter(Boolean).map((category: string, index: number) => (
                    <span key={index} className="category-tag">{category}</span>
                  ))}
                </div>
              ) : (
                'N/A'
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoinStats;
