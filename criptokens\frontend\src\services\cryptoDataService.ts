import { doc, getDoc, collection, query, where, getDocs, setDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { db } from '../firebase-init';

// Obtener datos del mercado de criptomonedas (actualizados por n8n)
export const getCryptoMarketData = async () => {
  try {
    const docRef = doc(db, 'crypto_data', 'market_data');
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data().data;
    } else {
      console.log('No se encontraron datos de criptomonedas');
      return [];
    }
  } catch (error) {
    console.error('Error al obtener datos de criptomonedas:', error);
    throw error;
  }
};

// Obtener la última fecha de actualización de los datos
export const getLastUpdateTime = async () => {
  try {
    const docRef = doc(db, 'crypto_data', 'market_data');
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return docSnap.data().last_updated;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error al obtener la fecha de actualización:', error);
    throw error;
  }
};

// Obtener alertas de precio configuradas por el usuario
export const getPriceAlerts = async (userId: string) => {
  try {
    const alertsCollection = collection(db, 'price_alerts');
    const q = query(alertsCollection, where('userId', '==', userId));
    const querySnapshot = await getDocs(q);
    
    const alerts: any[] = [];
    querySnapshot.forEach((doc) => {
      alerts.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return alerts;
  } catch (error) {
    console.error('Error al obtener alertas de precio:', error);
    throw error;
  }
};

// Crear una nueva alerta de precio
export const createPriceAlert = async (userId: string, alert: any) => {
  try {
    const alertsCollection = collection(db, 'price_alerts');
    const alertData = {
      userId,
      cryptoId: alert.cryptoId,
      cryptoName: alert.cryptoName,
      targetPrice: alert.targetPrice,
      condition: alert.condition, // 'above' o 'below'
      active: true,
      createdAt: new Date().toISOString(),
      triggered: false
    };
    
    const docRef = doc(alertsCollection);
    await setDoc(docRef, alertData);
    
    return {
      id: docRef.id,
      ...alertData
    };
  } catch (error) {
    console.error('Error al crear alerta de precio:', error);
    throw error;
  }
};

// Actualizar una alerta de precio existente
export const updatePriceAlert = async (alertId: string, updates: any) => {
  try {
    const alertRef = doc(db, 'price_alerts', alertId);
    await updateDoc(alertRef, updates);
    
    return true;
  } catch (error) {
    console.error('Error al actualizar alerta de precio:', error);
    throw error;
  }
};

// Eliminar una alerta de precio
export const deletePriceAlert = async (alertId: string) => {
  try {
    const alertRef = doc(db, 'price_alerts', alertId);
    await deleteDoc(alertRef);
    
    return true;
  } catch (error) {
    console.error('Error al eliminar alerta de precio:', error);
    throw error;
  }
};

// Obtener notificaciones de alertas para el usuario
export const getAlertNotifications = async (userId: string) => {
  try {
    const notificationsCollection = collection(db, 'alert_notifications');
    const q = query(notificationsCollection, where('userId', '==', userId));
    const querySnapshot = await getDocs(q);
    
    const notifications: any[] = [];
    querySnapshot.forEach((doc) => {
      notifications.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return notifications;
  } catch (error) {
    console.error('Error al obtener notificaciones de alertas:', error);
    throw error;
  }
};

// Marcar una notificación como leída
export const markNotificationAsRead = async (notificationId: string) => {
  try {
    const notificationRef = doc(db, 'alert_notifications', notificationId);
    await updateDoc(notificationRef, {
      read: true,
      readAt: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    console.error('Error al marcar notificación como leída:', error);
    throw error;
  }
};
