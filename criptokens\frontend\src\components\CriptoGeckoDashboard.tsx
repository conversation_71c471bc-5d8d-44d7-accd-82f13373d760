import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getTopCryptocurrencies, getGlobalMarketData } from '../services/api';
import { useTopCryptocurrencies } from '../hooks/useMcpClient';
import CoinTable from './CoinTable';
import MarketOverview from './MarketOverview';
import TrendingCoins from './TrendingCoins';
import CriptoAgentAvatar from './CriptoAgentAvatarExport';
import FeaturedNews from './FeaturedNews';
import '../styles/CriptoGeckoDashboard.css';

const CriptoGeckoDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [globalMarketData, setGlobalMarketData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [timeRange, setTimeRange] = useState<string>('24h');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showMobileFilters, setShowMobileFilters] = useState<boolean>(false);

  // Usar el hook personalizado para obtener datos de criptomonedas
  const { data: cryptos, isLoading: isCryptosLoading, error } = useTopCryptocurrencies();

  // Obtener datos globales del mercado
  useEffect(() => {
    const fetchGlobalData = async () => {
      try {
        const data = await getGlobalMarketData();
        setGlobalMarketData(data);
      } catch (error) {
        console.error('Error fetching global market data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGlobalData();
  }, []);

  // Función para manejar la búsqueda
  const handleSearch = () => {
    if (searchQuery.trim()) {
      // Implementar lógica de búsqueda
      console.log(`Searching for: ${searchQuery}`);
    }
  };

  // Función para formatear números grandes
  const formatNumber = (num: number, digits: number = 2): string => {
    if (num >= 1_000_000_000_000) {
      return `${(num / 1_000_000_000_000).toFixed(digits)}T`;
    } else if (num >= 1_000_000_000) {
      return `${(num / 1_000_000_000).toFixed(digits)}B`;
    } else if (num >= 1_000_000) {
      return `${(num / 1_000_000).toFixed(digits)}M`;
    } else if (num >= 1_000) {
      return `${(num / 1_000).toFixed(digits)}K`;
    }
    return num.toFixed(digits);
  };

  // Filtrar criptomonedas según la categoría seleccionada
  const filteredCryptos = cryptos ? [...cryptos] : [];

  return (
    <div className="criptogecko-dashboard">
      {/* Header con estadísticas globales */}
      <div className="dashboard-header">
        <div className="global-stats">
          {isLoading ? (
            <div className="loading-stats">Cargando estadísticas globales...</div>
          ) : (
            <>
              <div className="stat-item">
                <span className="stat-label">Criptomonedas:</span>
                <span className="stat-value">{globalMarketData?.active_cryptocurrencies || '0'}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Exchanges:</span>
                <span className="stat-value">{globalMarketData?.markets || '0'}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Cap. de mercado:</span>
                <span className="stat-value">
                  ${globalMarketData?.total_market_cap?.usd
                    ? formatNumber(globalMarketData.total_market_cap.usd)
                    : '0'}
                  <span className={`change ${globalMarketData?.market_cap_change_percentage_24h_usd >= 0 ? 'positive' : 'negative'}`}>
                    {globalMarketData?.market_cap_change_percentage_24h_usd
                      ? globalMarketData.market_cap_change_percentage_24h_usd.toFixed(1) + '%'
                      : '0%'}
                  </span>
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Volumen 24h:</span>
                <span className="stat-value">
                  ${globalMarketData?.total_volume?.usd
                    ? formatNumber(globalMarketData.total_volume.usd)
                    : '0'}
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Dominio:</span>
                <span className="stat-value">
                  BTC {globalMarketData?.market_cap_percentage?.btc
                    ? globalMarketData.market_cap_percentage.btc.toFixed(1) + '%'
                    : '0%'}
                </span>
              </div>
            </>
          )}
        </div>
        <div className="search-container">
          <div className="search-bar">
            <input
              type="text"
              placeholder="Buscar criptomoneda..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button className="search-button" onClick={handleSearch}>
              <i className="fas fa-search"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Contenido principal */}
      <div className="dashboard-content">
        {/* Sección de resumen del mercado */}
        <div className="dashboard-section market-overview-section">
          <MarketOverview globalMarketData={globalMarketData} isLoading={isLoading} />
        </div>

        {/* Sección de tendencias */}
        <div className="dashboard-section trending-section">
          <div className="section-header">
            <h2>En Tendencia</h2>
            <button className="view-all-button" onClick={() => navigate('/trending')}>
              Ver todo
            </button>
          </div>
          <TrendingCoins cryptos={filteredCryptos.slice(0, 5)} isLoading={isCryptosLoading} />
        </div>

        {/* Sección de noticias destacadas */}
        <div className="dashboard-section news-section">
          <FeaturedNews limit={3} />
        </div>

        {/* Sección de tabla de criptomonedas */}
        <div className="dashboard-section crypto-table-section">
          <div className="section-header with-filters">
            <h2>Criptomonedas por Capitalización de Mercado</h2>

            <div className="desktop-filters">
              <div className="time-filter">
                <button
                  className={timeRange === '1h' ? 'active' : ''}
                  onClick={() => setTimeRange('1h')}
                >
                  1h
                </button>
                <button
                  className={timeRange === '24h' ? 'active' : ''}
                  onClick={() => setTimeRange('24h')}
                >
                  24h
                </button>
                <button
                  className={timeRange === '7d' ? 'active' : ''}
                  onClick={() => setTimeRange('7d')}
                >
                  7d
                </button>
                <button
                  className={timeRange === '30d' ? 'active' : ''}
                  onClick={() => setTimeRange('30d')}
                >
                  30d
                </button>
              </div>

              <div className="category-filter">
                <button
                  className={selectedCategory === 'all' ? 'active' : ''}
                  onClick={() => setSelectedCategory('all')}
                >
                  Todas
                </button>
                <button
                  className={selectedCategory === 'defi' ? 'active' : ''}
                  onClick={() => setSelectedCategory('defi')}
                >
                  DeFi
                </button>
                <button
                  className={selectedCategory === 'nft' ? 'active' : ''}
                  onClick={() => setSelectedCategory('nft')}
                >
                  NFT
                </button>
                <button
                  className={selectedCategory === 'layer1' ? 'active' : ''}
                  onClick={() => setSelectedCategory('layer1')}
                >
                  Layer 1
                </button>
              </div>
            </div>

            <button
              className="mobile-filter-button"
              onClick={() => setShowMobileFilters(!showMobileFilters)}
            >
              <i className="fas fa-filter"></i> Filtros
            </button>
          </div>

          {showMobileFilters && (
            <div className="mobile-filters">
              <div className="filter-group">
                <h3>Periodo</h3>
                <div className="time-filter">
                  <button
                    className={timeRange === '1h' ? 'active' : ''}
                    onClick={() => setTimeRange('1h')}
                  >
                    1h
                  </button>
                  <button
                    className={timeRange === '24h' ? 'active' : ''}
                    onClick={() => setTimeRange('24h')}
                  >
                    24h
                  </button>
                  <button
                    className={timeRange === '7d' ? 'active' : ''}
                    onClick={() => setTimeRange('7d')}
                  >
                    7d
                  </button>
                  <button
                    className={timeRange === '30d' ? 'active' : ''}
                    onClick={() => setTimeRange('30d')}
                  >
                    30d
                  </button>
                </div>
              </div>

              <div className="filter-group">
                <h3>Categoría</h3>
                <div className="category-filter">
                  <button
                    className={selectedCategory === 'all' ? 'active' : ''}
                    onClick={() => setSelectedCategory('all')}
                  >
                    Todas
                  </button>
                  <button
                    className={selectedCategory === 'defi' ? 'active' : ''}
                    onClick={() => setSelectedCategory('defi')}
                  >
                    DeFi
                  </button>
                  <button
                    className={selectedCategory === 'nft' ? 'active' : ''}
                    onClick={() => setSelectedCategory('nft')}
                  >
                    NFT
                  </button>
                  <button
                    className={selectedCategory === 'layer1' ? 'active' : ''}
                    onClick={() => setSelectedCategory('layer1')}
                  >
                    Layer 1
                  </button>
                </div>
              </div>

              <button
                className="close-filters-button"
                onClick={() => setShowMobileFilters(false)}
              >
                Aplicar Filtros
              </button>
            </div>
          )}

          <CoinTable
            cryptos={filteredCryptos}
            isLoading={isCryptosLoading}
            timeRange={timeRange}
            onSelectCrypto={(id) => navigate(`/coins/${id}`)}
          />

          <div className="table-pagination">
            <button className="pagination-button">
              <i className="fas fa-chevron-left"></i>
            </button>
            <button className="pagination-button active">1</button>
            <button className="pagination-button">2</button>
            <button className="pagination-button">3</button>
            <span className="pagination-ellipsis">...</span>
            <button className="pagination-button">10</button>
            <button className="pagination-button">
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>

      {/* El asistente flotante ha sido reemplazado por el panel lateral persistente */}
    </div>
  );
};

export default CriptoGeckoDashboard;
