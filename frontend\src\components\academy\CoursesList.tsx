import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../../styles/academy/CoursesList.css';

interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
  duration: string;
  thumbnail?: string;
  category: string;
  instructor: {
    name: string;
    avatar: string;
  };
  rating: number;
  studentsCount: number;
}

const CoursesList: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('popular');

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        
        // En una implementación real, esto sería una llamada a la API
        // Por ahora, simulamos con datos de ejemplo
        const mockCourses: Course[] = [
          {
            id: 'crypto-fundamentals',
            title: 'Fundamentos de Criptomonedas',
            description: 'Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.',
            level: 'beginner',
            duration: '4 horas',
            thumbnail: '/images/courses/crypto-fundamentals.jpg',
            category: 'fundamentals',
            instructor: {
              name: 'Alex Rodríguez',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
            },
            rating: 4.8,
            studentsCount: 1250
          },
          {
            id: 'crypto-trading',
            title: 'Trading de Criptomonedas',
            description: 'Aprende estrategias de trading, análisis técnico y gestión de riesgos para operar en el mercado de criptomonedas.',
            level: 'intermediate',
            duration: '6 horas',
            thumbnail: '/images/courses/crypto-trading.jpg',
            category: 'trading',
            instructor: {
              name: 'Carlos Vega',
              avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
            },
            rating: 4.6,
            studentsCount: 980
          },
          {
            id: 'defi-essentials',
            title: 'Finanzas Descentralizadas (DeFi)',
            description: 'Descubre el mundo de las finanzas descentralizadas, protocolos, oportunidades y riesgos en este ecosistema emergente.',
            level: 'intermediate',
            duration: '5 horas',
            thumbnail: '/images/courses/defi-essentials.jpg',
            category: 'defi',
            instructor: {
              name: 'Laura Martínez',
              avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
            },
            rating: 4.7,
            studentsCount: 850
          },
          {
            id: 'crypto-security',
            title: 'Seguridad en Criptomonedas',
            description: 'Protege tus activos digitales aprendiendo las mejores prácticas de seguridad en el mundo de las criptomonedas.',
            level: 'beginner',
            duration: '3 horas',
            thumbnail: '/images/courses/crypto-security.jpg',
            category: 'security',
            instructor: {
              name: 'Elena Gómez',
              avatar: 'https://randomuser.me/api/portraits/women/22.jpg'
            },
            rating: 4.9,
            studentsCount: 1100
          },
          {
            id: 'technical-analysis',
            title: 'Análisis Técnico Avanzado',
            description: 'Domina las técnicas avanzadas de análisis técnico para identificar oportunidades de trading en el mercado de criptomonedas.',
            level: 'advanced',
            duration: '8 horas',
            thumbnail: '/images/courses/technical-analysis.jpg',
            category: 'trading',
            instructor: {
              name: 'Roberto Sánchez',
              avatar: 'https://randomuser.me/api/portraits/men/45.jpg'
            },
            rating: 4.5,
            studentsCount: 720
          },
          {
            id: 'nft-masterclass',
            title: 'NFTs: Masterclass Completa',
            description: 'Todo lo que necesitas saber sobre NFTs: creación, compra, venta y estrategias de inversión.',
            level: 'intermediate',
            duration: '4 horas',
            thumbnail: '/images/courses/nft-masterclass.jpg',
            category: 'fundamentals',
            instructor: {
              name: 'Ana Torres',
              avatar: 'https://randomuser.me/api/portraits/women/33.jpg'
            },
            rating: 4.7,
            studentsCount: 890
          }
        ];
        
        setCourses(mockCourses);
        setFilteredCourses(mockCourses);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching courses:', error);
        setLoading(false);
      }
    };
    
    fetchCourses();
  }, []);

  useEffect(() => {
    // Aplicar filtros y ordenación
    let result = [...courses];
    
    // Filtrar por término de búsqueda
    if (searchTerm) {
      result = result.filter(course => 
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Filtrar por categoría
    if (selectedCategory !== 'all') {
      result = result.filter(course => course.category === selectedCategory);
    }
    
    // Filtrar por nivel
    if (selectedLevel !== 'all') {
      result = result.filter(course => course.level === selectedLevel);
    }
    
    // Ordenar
    switch (sortBy) {
      case 'popular':
        result.sort((a, b) => b.studentsCount - a.studentsCount);
        break;
      case 'rating':
        result.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        // En un caso real, ordenaríamos por fecha de creación
        // Aquí simplemente mantenemos el orden original
        break;
      case 'title-asc':
        result.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'title-desc':
        result.sort((a, b) => b.title.localeCompare(a.title));
        break;
      default:
        break;
    }
    
    setFilteredCourses(result);
  }, [courses, searchTerm, selectedCategory, selectedLevel, sortBy]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleLevelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedLevel(e.target.value);
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSortBy(e.target.value);
  };

  const renderStarRating = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    return (
      <div className="star-rating">
        {[...Array(fullStars)].map((_, i) => (
          <i key={`full-${i}`} className="fas fa-star"></i>
        ))}
        {hasHalfStar && <i className="fas fa-star-half-alt"></i>}
        {[...Array(emptyStars)].map((_, i) => (
          <i key={`empty-${i}`} className="far fa-star"></i>
        ))}
        <span className="rating-value">{rating.toFixed(1)}</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="courses-list loading">
        <div className="loading-spinner"></div>
        <p>Cargando cursos...</p>
      </div>
    );
  }

  return (
    <div className="courses-list">
      <div className="courses-header">
        <h2>Todos los Cursos</h2>
        <p>Explora nuestra colección completa de cursos sobre criptomonedas, blockchain y finanzas descentralizadas.</p>
      </div>
      
      <div className="courses-filters">
        <div className="search-bar">
          <i className="fas fa-search"></i>
          <input 
            type="text" 
            placeholder="Buscar cursos..." 
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        
        <div className="filter-options">
          <div className="filter-group">
            <label htmlFor="category-filter">Categoría</label>
            <select 
              id="category-filter" 
              value={selectedCategory}
              onChange={handleCategoryChange}
            >
              <option value="all">Todas</option>
              <option value="fundamentals">Fundamentos</option>
              <option value="trading">Trading</option>
              <option value="defi">DeFi</option>
              <option value="security">Seguridad</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="level-filter">Nivel</label>
            <select 
              id="level-filter" 
              value={selectedLevel}
              onChange={handleLevelChange}
            >
              <option value="all">Todos</option>
              <option value="beginner">Principiante</option>
              <option value="intermediate">Intermedio</option>
              <option value="advanced">Avanzado</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="sort-filter">Ordenar por</label>
            <select 
              id="sort-filter" 
              value={sortBy}
              onChange={handleSortChange}
            >
              <option value="popular">Más populares</option>
              <option value="rating">Mejor valorados</option>
              <option value="newest">Más recientes</option>
              <option value="title-asc">Título (A-Z)</option>
              <option value="title-desc">Título (Z-A)</option>
            </select>
          </div>
        </div>
      </div>
      
      <div className="courses-results">
        <p className="results-count">
          {filteredCourses.length} {filteredCourses.length === 1 ? 'curso encontrado' : 'cursos encontrados'}
        </p>
        
        <div className="courses-grid">
          {filteredCourses.length > 0 ? (
            filteredCourses.map(course => (
              <div key={course.id} className="course-card">
                <div className="course-image">
                  {course.thumbnail ? (
                    <img src={course.thumbnail} alt={course.title} />
                  ) : (
                    <div className="placeholder-image"></div>
                  )}
                  <div className={`course-level ${course.level}`}>
                    {course.level === 'beginner' && 'Principiante'}
                    {course.level === 'intermediate' && 'Intermedio'}
                    {course.level === 'advanced' && 'Avanzado'}
                  </div>
                </div>
                <div className="course-content">
                  <h3>{course.title}</h3>
                  <p className="course-description">{course.description}</p>
                  <div className="course-meta">
                    <span className="course-duration">
                      <i className="fas fa-clock"></i> {course.duration}
                    </span>
                    <span className="course-students">
                      <i className="fas fa-user-graduate"></i> {course.studentsCount} estudiantes
                    </span>
                  </div>
                  <div className="course-rating">
                    {renderStarRating(course.rating)}
                  </div>
                  <div className="course-instructor">
                    <img src={course.instructor.avatar} alt={course.instructor.name} />
                    <span>{course.instructor.name}</span>
                  </div>
                  <Link to={`/academy/courses/${course.id}`} className="view-course-button">
                    Ver Curso
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="no-courses-found">
              <i className="fas fa-search"></i>
              <h3>No se encontraron cursos</h3>
              <p>Intenta con otros términos de búsqueda o filtros.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CoursesList;
