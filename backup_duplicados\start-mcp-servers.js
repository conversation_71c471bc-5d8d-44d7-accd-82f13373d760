/**
 * Script para iniciar todos los servidores MCP
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Cargar la configuración de MCP
let config = {};
try {
  const configPath = path.join(__dirname, 'mcp-config.json');
  if (fs.existsSync(configPath)) {
    const configData = fs.readFileSync(configPath, 'utf8');
    config = JSON.parse(configData);
  }
} catch (error) {
  console.error('Error al cargar la configuración de MCP:', error);
}

// Función para iniciar un servidor MCP
function startMcpServer(name, serverConfig) {
  console.log(`Iniciando servidor MCP: ${name}...`);

  const { command, args, env } = serverConfig;

  // Combinar variables de entorno
  const processEnv = { ...process.env, ...env };

  // Iniciar el proceso
  const serverProcess = spawn(command, args, {
    env: processEnv,
    stdio: 'inherit',
    shell: true
  });

  // Manejar eventos del proceso
  serverProcess.on('error', (error) => {
    console.error(`Error al iniciar el servidor MCP ${name}:`, error);
  });

  serverProcess.on('close', (code) => {
    if (code !== 0) {
      console.error(`El servidor MCP ${name} se cerró con código: ${code}`);
    } else {
      console.log(`El servidor MCP ${name} se cerró correctamente.`);
    }
  });

  return serverProcess;
}

// Iniciar el servidor MCP de crypto
console.log('Iniciando servidor MCP de crypto...');
const cryptoServerProcess = spawn('node', ['../crypto-mcp-server/http-server.js'], {
  stdio: 'inherit',
  shell: true
});

// Iniciar los servidores MCP configurados
const mcpServers = config.mcpServers || {};
const serverProcesses = {};

Object.entries(mcpServers).forEach(([name, serverConfig]) => {
  serverProcesses[name] = startMcpServer(name, serverConfig);
});

// Manejar la terminación del proceso principal
process.on('SIGINT', () => {
  console.log('Cerrando todos los servidores MCP...');

  // Cerrar el servidor de crypto
  if (cryptoServerProcess) {
    cryptoServerProcess.kill('SIGINT');
  }

  // Cerrar todos los servidores MCP
  Object.entries(serverProcesses).forEach(([name, process]) => {
    console.log(`Cerrando servidor MCP: ${name}...`);
    process.kill('SIGINT');
  });

  // Salir después de un breve retraso para permitir que los procesos se cierren correctamente
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});

// Mostrar información sobre los servidores en ejecución
setTimeout(() => {
  console.log('\n=== SERVIDORES MCP EN EJECUCIÓN ===');
  console.log('- Crypto MCP Server: http://localhost:3101');
  
  if (mcpServers.brave) {
    const port = mcpServers.brave.env?.PORT || '3102';
    console.log(`- Brave Search MCP Server: http://localhost:${port}`);
  }
  
  if (mcpServers.playwright) {
    const port = mcpServers.playwright.env?.PORT || '3103';
    console.log(`- Playwright MCP Server: http://localhost:${port}`);
  }
  
  if (mcpServers.etherscan) {
    const port = mcpServers.etherscan.env?.PORT || '3104';
    console.log(`- Etherscan MCP Server: http://localhost:${port}`);
  }
  
  if (mcpServers.context7) {
    console.log('- Context7 MCP Server: (puerto dinámico)');
  }
  
  console.log('\nTodos los servidores MCP están en ejecución. Presiona Ctrl+C para detenerlos.');
}, 3000);
