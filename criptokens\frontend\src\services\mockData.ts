// Datos simulados para cuando las APIs externas no estén disponibles

// Datos de las principales criptomonedas
export const mockTopCryptos = [
  {
    id: "bitcoin",
    symbol: "btc",
    name: "Bitcoin",
    image: "https://assets.coingecko.com/coins/images/1/large/bitcoin.png",
    current_price: 61245.32,
    market_cap: 1200000000000,
    market_cap_rank: 1,
    fully_diluted_valuation: 1300000000000,
    total_volume: 32000000000,
    high_24h: 62500.12,
    low_24h: 60100.45,
    price_change_24h: 1245.32,
    price_change_percentage_24h: 2.3,
    market_cap_change_24h: 24000000000,
    market_cap_change_percentage_24h: 2.1,
    circulating_supply: 19000000,
    total_supply: 21000000,
    max_supply: 21000000,
    ath: 69000,
    ath_change_percentage: -12.5,
    ath_date: "2021-11-10T14:24:11.849Z",
    atl: 67.81,
    atl_change_percentage: 90000,
    atl_date: "2013-07-06T00:00:00.000Z",
    roi: null,
    last_updated: "2023-04-20T12:30:45.123Z",
    sparkline_in_7d: {
      price: Array(168).fill(0).map((_, i) => 60000 + Math.random() * 5000)
    }
  },
  {
    id: "ethereum",
    symbol: "eth",
    name: "Ethereum",
    image: "https://assets.coingecko.com/coins/images/279/large/ethereum.png",
    current_price: 3521.18,
    market_cap: 420000000000,
    market_cap_rank: 2,
    fully_diluted_valuation: 420000000000,
    total_volume: 18000000000,
    high_24h: 3600.45,
    low_24h: 3480.12,
    price_change_24h: -28.45,
    price_change_percentage_24h: -0.8,
    market_cap_change_24h: -3000000000,
    market_cap_change_percentage_24h: -0.7,
    circulating_supply: 120000000,
    total_supply: 120000000,
    max_supply: null,
    ath: 4878.26,
    ath_change_percentage: -28.2,
    ath_date: "2021-11-10T14:24:19.604Z",
    atl: 0.432979,
    atl_change_percentage: 800000,
    atl_date: "2015-10-20T00:00:00.000Z",
    roi: {
      times: 100,
      currency: "btc",
      percentage: 10000
    },
    last_updated: "2023-04-20T12:30:45.123Z",
    sparkline_in_7d: {
      price: Array(168).fill(0).map((_, i) => 3400 + Math.random() * 300)
    }
  },
  {
    id: "binancecoin",
    symbol: "bnb",
    name: "BNB",
    image: "https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png",
    current_price: 608.42,
    market_cap: 93000000000,
    market_cap_rank: 3,
    fully_diluted_valuation: 93000000000,
    total_volume: 2000000000,
    high_24h: 615.24,
    low_24h: 600.12,
    price_change_24h: 5.12,
    price_change_percentage_24h: 0.85,
    market_cap_change_24h: 800000000,
    market_cap_change_percentage_24h: 0.87,
    circulating_supply: 153000000,
    total_supply: 153000000,
    max_supply: 153000000,
    ath: 686.31,
    ath_change_percentage: -11.4,
    ath_date: "2021-05-10T07:24:17.097Z",
    atl: 0.0398177,
    atl_change_percentage: 1500000,
    atl_date: "2017-10-19T00:00:00.000Z",
    roi: null,
    last_updated: "2023-04-20T12:30:45.123Z",
    sparkline_in_7d: {
      price: Array(168).fill(0).map((_, i) => 590 + Math.random() * 40)
    }
  },
  {
    id: "solana",
    symbol: "sol",
    name: "Solana",
    image: "https://assets.coingecko.com/coins/images/4128/large/solana.png",
    current_price: 142.67,
    market_cap: 61000000000,
    market_cap_rank: 4,
    fully_diluted_valuation: 78000000000,
    total_volume: 3000000000,
    high_24h: 145.12,
    low_24h: 135.45,
    price_change_24h: 7.12,
    price_change_percentage_24h: 5.2,
    market_cap_change_24h: 3000000000,
    market_cap_change_percentage_24h: 5.1,
    circulating_supply: 430000000,
    total_supply: 550000000,
    max_supply: null,
    ath: 259.96,
    ath_change_percentage: -45.2,
    ath_date: "2021-11-06T21:54:35.825Z",
    atl: 0.500801,
    atl_change_percentage: 28000,
    atl_date: "2020-05-11T19:35:23.449Z",
    roi: null,
    last_updated: "2023-04-20T12:30:45.123Z",
    sparkline_in_7d: {
      price: Array(168).fill(0).map((_, i) => 130 + Math.random() * 25)
    }
  },
  {
    id: "ripple",
    symbol: "xrp",
    name: "XRP",
    image: "https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png",
    current_price: 0.5423,
    market_cap: 29000000000,
    market_cap_rank: 5,
    fully_diluted_valuation: 54000000000,
    total_volume: 1200000000,
    high_24h: 0.55,
    low_24h: 0.53,
    price_change_24h: 0.01,
    price_change_percentage_24h: 1.8,
    market_cap_change_24h: 500000000,
    market_cap_change_percentage_24h: 1.7,
    circulating_supply: 53500000000,
    total_supply: 100000000000,
    max_supply: 100000000000,
    ath: 3.4,
    ath_change_percentage: -84.1,
    ath_date: "2018-01-07T00:00:00.000Z",
    atl: 0.00268621,
    atl_change_percentage: 20000,
    atl_date: "2014-05-22T00:00:00.000Z",
    roi: null,
    last_updated: "2023-04-20T12:30:45.123Z",
    sparkline_in_7d: {
      price: Array(168).fill(0).map((_, i) => 0.52 + Math.random() * 0.05)
    }
  },
  {
    id: "cardano",
    symbol: "ada",
    name: "Cardano",
    image: "https://assets.coingecko.com/coins/images/975/large/cardano.png",
    current_price: 0.45,
    market_cap: 16000000000,
    market_cap_rank: 6,
    fully_diluted_valuation: 20000000000,
    total_volume: 700000000,
    high_24h: 0.46,
    low_24h: 0.44,
    price_change_24h: 0.005,
    price_change_percentage_24h: 1.1,
    market_cap_change_24h: 400000000,
    market_cap_change_percentage_24h: 2.5,
    circulating_supply: 35500000000,
    total_supply: 45000000000,
    max_supply: 45000000000,
    ath: 3.09,
    ath_change_percentage: -85.4,
    ath_date: "2021-09-02T06:00:10.474Z",
    atl: 0.01925275,
    atl_change_percentage: 2300,
    atl_date: "2020-03-13T02:22:55.044Z",
    roi: null,
    last_updated: "2023-04-20T12:30:45.123Z",
    sparkline_in_7d: {
      price: Array(168).fill(0).map((_, i) => 0.43 + Math.random() * 0.04)
    }
  }
];

// Datos del mercado global
export const mockGlobalData = {
  data: {
    active_cryptocurrencies: 10000,
    upcoming_icos: 0,
    ongoing_icos: 50,
    ended_icos: 3375,
    markets: 800,
    total_market_cap: {
      btc: 43000000,
      eth: 650000000,
      ltc: 15000000000,
      bch: 6000000000,
      bnb: 4000000000,
      eos: 1200000000000,
      xrp: 2600000000000,
      xlm: 11000000000000,
      link: 180000000000,
      dot: 200000000000,
      yfi: 70000000,
      usd: 2500000000000,
      aed: 9200000000000,
      ars: 870000000000000,
      aud: 3800000000000,
      bdt: 270000000000000,
      bhd: 940000000000,
      bmd: 2500000000000,
      brl: 12800000000000,
      cad: 3400000000000,
      chf: 2200000000000,
      clp: 2100000000000000,
      cny: 18000000000000,
      czk: 57000000000000,
      dkk: 17000000000000,
      eur: 2300000000000,
      gbp: 2000000000000,
      hkd: 19600000000000,
      huf: 880000000000000,
      idr: 39000000000000000,
      ils: 9200000000000,
      inr: 208000000000000,
      jpy: 380000000000000,
      krw: 3400000000000000,
      kwd: 770000000000,
      lkr: 800000000000000,
      mmk: 5200000000000000,
      mxn: 42000000000000,
      myr: 11700000000000,
      ngn: 3800000000000000,
      nok: 27000000000000,
      nzd: 4100000000000,
      php: 142000000000000,
      pkr: 700000000000000,
      pln: 10000000000000,
      rub: 220000000000000,
      sar: 9400000000000,
      sek: 26000000000000,
      sgd: 3400000000000,
      thb: 89000000000000,
      try: 80000000000000,
      twd: 80000000000000,
      uah: 92000000000000,
      vef: 250000000000,
      vnd: 62000000000000000,
      zar: 46000000000000,
      xdr: 1900000000000,
      xag: 100000000000,
      xau: 1300000000,
      bits: 43000000000000,
      sats: 4300000000000000
    },
    total_volume: {
      btc: 2600000,
      eth: 39000000,
      ltc: 900000000,
      bch: 360000000,
      bnb: 240000000,
      eos: 72000000000,
      xrp: 156000000000,
      xlm: 660000000000,
      link: 10800000000,
      dot: 12000000000,
      yfi: 4200000,
      usd: 150000000000,
      aed: 552000000000,
      ars: 52200000000000,
      aud: 228000000000,
      bdt: 16200000000000,
      bhd: 56400000000,
      bmd: 150000000000,
      brl: 768000000000,
      cad: 204000000000,
      chf: 132000000000,
      clp: 126000000000000,
      cny: 1080000000000,
      czk: 3420000000000,
      dkk: 1020000000000,
      eur: 138000000000,
      gbp: 120000000000,
      hkd: 1176000000000,
      huf: 52800000000000,
      idr: 2340000000000000,
      ils: 552000000000,
      inr: 12480000000000,
      jpy: 22800000000000,
      krw: 204000000000000,
      kwd: 46200000000,
      lkr: 48000000000000,
      mmk: 312000000000000,
      mxn: 2520000000000,
      myr: 702000000000,
      ngn: 228000000000000,
      nok: 1620000000000,
      nzd: 246000000000,
      php: 8520000000000,
      pkr: 42000000000000,
      pln: 600000000000,
      rub: 13200000000000,
      sar: 564000000000,
      sek: 1560000000000,
      sgd: 204000000000,
      thb: 5340000000000,
      try: 4800000000000,
      twd: 4800000000000,
      uah: 5520000000000,
      vef: 15000000000,
      vnd: 3720000000000000,
      zar: 2760000000000,
      xdr: 114000000000,
      xag: 6000000000,
      xau: 78000000,
      bits: 2600000000000,
      sats: 260000000000000
    },
    market_cap_percentage: {
      btc: 48.2,
      eth: 16.8,
      bnb: 3.7,
      sol: 2.4,
      xrp: 1.2,
      ada: 0.9,
      doge: 0.8,
      dot: 0.7,
      avax: 0.6,
      matic: 0.5
    },
    market_cap_change_percentage_24h_usd: 1.8,
    updated_at: 1682000000
  }
};

// Datos históricos para gráficos
export const generateHistoricalData = (days = 30, basePrice = 60000, volatility = 5000) => {
  const prices = [];
  const currentDate = new Date();
  
  for (let i = 0; i < days; i++) {
    const date = new Date();
    date.setDate(currentDate.getDate() - (days - i));
    const timestamp = date.getTime();
    
    // Crear un patrón de precio más realista con tendencia y volatilidad
    const trend = Math.sin(i / (days / 3)) * volatility * 0.5;
    const noise = (Math.random() * 2 - 1) * volatility * 0.2;
    const price = basePrice + trend + noise;
    
    prices.push([timestamp, price]);
  }
  
  return {
    prices: prices,
    market_caps: prices.map(([timestamp, price]) => [timestamp, price * 19000000]),
    total_volumes: prices.map(([timestamp, price]) => [timestamp, price * 19000000 * (Math.random() * 0.1 + 0.05)])
  };
};

// Exportar datos históricos predefinidos para diferentes criptomonedas
export const mockHistoricalData = {
  bitcoin: generateHistoricalData(30, 60000, 5000),
  ethereum: generateHistoricalData(30, 3500, 300),
  binancecoin: generateHistoricalData(30, 600, 40),
  solana: generateHistoricalData(30, 140, 25),
  ripple: generateHistoricalData(30, 0.54, 0.05),
  cardano: generateHistoricalData(30, 0.45, 0.04)
};

export default {
  mockTopCryptos,
  mockGlobalData,
  mockHistoricalData,
  generateHistoricalData
};
