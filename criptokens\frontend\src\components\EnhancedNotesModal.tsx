import React, { useState, useEffect } from 'react';
import '../styles/EnhancedNotesModal.css';

interface EnhancedNotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  cryptoId: string;
  cryptoName: string;
  cryptoSymbol: string;
  currentPrice: number;
  initialNotes: string;
  initialPriceTarget?: number | null;
  initialEntryPrice?: number | null;
  initialStopLoss?: number | null;
  initialReason?: string;
  initialResources?: string[];
  onSave: (
    notes: string, 
    priceTarget: number | null, 
    entryPrice: number | null,
    stopLoss: number | null,
    reason: string,
    resources: string[]
  ) => void;
}

const EnhancedNotesModal: React.FC<EnhancedNotesModalProps> = ({
  isOpen,
  onClose,
  cryptoId,
  cryptoName,
  cryptoSymbol,
  currentPrice,
  initialNotes,
  initialPriceTarget,
  initialEntryPrice,
  initialStopLoss,
  initialReason,
  initialResources = [],
  onSave
}) => {
  const [notes, setNotes] = useState(initialNotes || '');
  const [priceTarget, setPriceTarget] = useState<string>(initialPriceTarget ? initialPriceTarget.toString() : '');
  const [entryPrice, setEntryPrice] = useState<string>(initialEntryPrice ? initialEntryPrice.toString() : '');
  const [stopLoss, setStopLoss] = useState<string>(initialStopLoss ? initialStopLoss.toString() : '');
  const [reason, setReason] = useState(initialReason || '');
  const [resources, setResources] = useState<string[]>(initialResources || []);
  const [newResource, setNewResource] = useState('');
  const [activeTab, setActiveTab] = useState<'notes' | 'targets' | 'resources'>('notes');

  // Calcular potenciales ganancias/pérdidas
  const calculatePotential = () => {
    const targetPrice = parseFloat(priceTarget);
    const entry = parseFloat(entryPrice) || currentPrice;
    const stop = parseFloat(stopLoss);

    if (!isNaN(targetPrice) && !isNaN(entry)) {
      const potentialGain = ((targetPrice - entry) / entry) * 100;
      return {
        potentialGain: potentialGain.toFixed(2),
        riskReward: !isNaN(stop) ? (Math.abs(targetPrice - entry) / Math.abs(entry - stop)).toFixed(2) : 'N/A'
      };
    }
    return { potentialGain: 'N/A', riskReward: 'N/A' };
  };

  const { potentialGain, riskReward } = calculatePotential();

  useEffect(() => {
    if (isOpen) {
      setNotes(initialNotes || '');
      setPriceTarget(initialPriceTarget ? initialPriceTarget.toString() : '');
      setEntryPrice(initialEntryPrice ? initialEntryPrice.toString() : '');
      setStopLoss(initialStopLoss ? initialStopLoss.toString() : '');
      setReason(initialReason || '');
      setResources(initialResources || []);
      setNewResource('');
      setActiveTab('notes');
    }
  }, [isOpen, initialNotes, initialPriceTarget, initialEntryPrice, initialStopLoss, initialReason, initialResources]);

  const handleSave = () => {
    const parsedPriceTarget = priceTarget ? parseFloat(priceTarget) : null;
    const parsedEntryPrice = entryPrice ? parseFloat(entryPrice) : null;
    const parsedStopLoss = stopLoss ? parseFloat(stopLoss) : null;
    
    onSave(
      notes, 
      parsedPriceTarget, 
      parsedEntryPrice,
      parsedStopLoss,
      reason, 
      resources
    );
    onClose();
  };

  const handleAddResource = () => {
    if (newResource.trim() && !resources.includes(newResource.trim())) {
      setResources([...resources, newResource.trim()]);
      setNewResource('');
    }
  };

  const handleRemoveResource = (index: number) => {
    const updatedResources = [...resources];
    updatedResources.splice(index, 1);
    setResources(updatedResources);
  };

  if (!isOpen) return null;

  return (
    <div className="enhanced-modal-backdrop" onClick={onClose}>
      <div className="enhanced-modal-content" onClick={e => e.stopPropagation()}>
        <div className="enhanced-modal-header">
          <h3>
            {cryptoName} ({cryptoSymbol.toUpperCase()})
            <span className="current-price">Precio actual: ${currentPrice.toFixed(2)}</span>
          </h3>
          <button className="enhanced-modal-close" onClick={onClose}>&times;</button>
        </div>

        <div className="enhanced-modal-tabs">
          <button 
            className={`tab-button ${activeTab === 'notes' ? 'active' : ''}`}
            onClick={() => setActiveTab('notes')}
          >
            Notas
          </button>
          <button 
            className={`tab-button ${activeTab === 'targets' ? 'active' : ''}`}
            onClick={() => setActiveTab('targets')}
          >
            Objetivos de Precio
          </button>
          <button 
            className={`tab-button ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => setActiveTab('resources')}
          >
            Recursos
          </button>
        </div>

        <div className="enhanced-modal-body">
          {activeTab === 'notes' && (
            <>
              <div className="form-group">
                <label htmlFor="notes">Notas:</label>
                <textarea
                  id="notes"
                  className="form-control"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Añade tus notas aquí..."
                  rows={8}
                />
              </div>
              <div className="form-group">
                <label htmlFor="reason">Razón para seguir:</label>
                <textarea
                  id="reason"
                  className="form-control"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="¿Por qué estás siguiendo esta criptomoneda?"
                  rows={4}
                />
              </div>
            </>
          )}

          {activeTab === 'targets' && (
            <>
              <div className="price-targets-container">
                <div className="form-group">
                  <label htmlFor="entryPrice">Precio de Entrada (USD):</label>
                  <input
                    type="number"
                    id="entryPrice"
                    className="form-control"
                    value={entryPrice}
                    onChange={(e) => setEntryPrice(e.target.value)}
                    placeholder={`Ej: ${currentPrice.toFixed(2)}`}
                    step="0.01"
                  />
                  <small>Deja en blanco para usar el precio actual</small>
                </div>
                <div className="form-group">
                  <label htmlFor="priceTarget">Precio Objetivo (USD):</label>
                  <input
                    type="number"
                    id="priceTarget"
                    className="form-control"
                    value={priceTarget}
                    onChange={(e) => setPriceTarget(e.target.value)}
                    placeholder="Ej: 50000"
                    step="0.01"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="stopLoss">Stop Loss (USD):</label>
                  <input
                    type="number"
                    id="stopLoss"
                    className="form-control"
                    value={stopLoss}
                    onChange={(e) => setStopLoss(e.target.value)}
                    placeholder="Ej: 30000"
                    step="0.01"
                  />
                </div>
              </div>

              <div className="potential-metrics">
                <div className="metric">
                  <span className="metric-label">Ganancia Potencial:</span>
                  <span className={`metric-value ${parseFloat(potentialGain) > 0 ? 'positive' : parseFloat(potentialGain) < 0 ? 'negative' : ''}`}>
                    {potentialGain !== 'N/A' ? `${potentialGain}%` : 'N/A'}
                  </span>
                </div>
                <div className="metric">
                  <span className="metric-label">Ratio Riesgo/Recompensa:</span>
                  <span className="metric-value">
                    {riskReward !== 'N/A' ? `${riskReward}:1` : 'N/A'}
                  </span>
                </div>
              </div>
            </>
          )}

          {activeTab === 'resources' && (
            <>
              <div className="resources-container">
                <div className="add-resource-form">
                  <input
                    type="text"
                    value={newResource}
                    onChange={(e) => setNewResource(e.target.value)}
                    placeholder="Añadir enlace o recurso (URL, artículo, etc.)"
                    className="resource-input"
                  />
                  <button 
                    className="add-resource-button"
                    onClick={handleAddResource}
                    disabled={!newResource.trim()}
                  >
                    Añadir
                  </button>
                </div>

                <div className="resources-list">
                  <h4>Recursos guardados:</h4>
                  {resources.length > 0 ? (
                    <ul>
                      {resources.map((resource, index) => (
                        <li key={index} className="resource-item">
                          {resource.startsWith('http') ? (
                            <a href={resource} target="_blank" rel="noopener noreferrer">
                              {resource}
                            </a>
                          ) : (
                            resource
                          )}
                          <button 
                            className="remove-resource-button"
                            onClick={() => handleRemoveResource(index)}
                          >
                            &times;
                          </button>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="no-resources">No hay recursos guardados</p>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <div className="enhanced-modal-footer">
          <button className="btn-secondary" onClick={onClose}>Cancelar</button>
          <button className="btn-primary" onClick={handleSave}>Guardar</button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedNotesModal;
