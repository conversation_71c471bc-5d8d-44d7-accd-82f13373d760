import { 
  SentimentAnalysis, 
  GlobalMarketData, 
  MarketSentiment 
} from '../../types/dashboard';

/**
 * Analiza el sentimiento del mercado basado en múltiples factores
 */
export const analyzeSentiment = (
  marketData: GlobalMarketData | null,
  newsData: any[] = [],
  socialData: any[] = []
): SentimentAnalysis | null => {
  if (!marketData) {
    return null;
  }

  // Calcular factores de sentimiento
  const marketMomentum = calculateMarketMomentum(marketData);
  const volatility = calculateVolatility(marketData);
  const btcDominance = calculateBtcDominance(marketData);
  const socialMediaSentiment = calculateSocialSentiment(socialData);
  const tradingVolume = calculateTradingVolume(marketData);
  
  // Calcular índice de miedo y codicia (0-100)
  const fearGreedIndex = calculateFearGreedIndex(
    marketMomentum,
    volatility,
    btcDominance,
    socialMediaSentiment,
    tradingVolume
  );
  
  // Determinar el sentimiento general
  const overallSentiment = determineSentiment(fearGreedIndex);
  
  // Determinar la tendencia del sentimiento
  // En una implementación real, esto compararía con datos históricos
  const sentimentTrend = determineSentimentTrend(fearGreedIndex);
  
  return {
    overallSentiment,
    fearGreedIndex,
    sentimentFactors: {
      marketMomentum,
      volatility,
      btcDominance,
      socialMediaSentiment,
      tradingVolume
    },
    sentimentTrend,
    lastUpdated: new Date()
  };
};

/**
 * Calcula el impulso del mercado basado en cambios de precio
 * @returns Valor de -100 a 100
 */
const calculateMarketMomentum = (marketData: GlobalMarketData): number => {
  // Usar el cambio porcentual de capitalización de mercado como indicador principal
  const marketCapChange = marketData.market_cap_change_percentage_24h_usd || 0;
  
  // Convertir a una escala de -100 a 100
  // Asumimos que un cambio de ±10% representa los extremos
  return Math.max(-100, Math.min(100, marketCapChange * 10));
};

/**
 * Calcula la volatilidad del mercado
 * @returns Valor de 0 a 100, donde 100 es extremadamente volátil
 */
const calculateVolatility = (marketData: GlobalMarketData): number => {
  // En una implementación real, esto usaría datos históricos para calcular
  // la desviación estándar de los rendimientos
  
  // Como aproximación, usamos el cambio absoluto en la capitalización de mercado
  const marketCapChange = Math.abs(marketData.market_cap_change_percentage_24h_usd || 0);
  
  // Convertir a una escala de 0 a 100
  // Asumimos que un cambio de 10% representa volatilidad extrema
  return Math.min(100, marketCapChange * 10);
};

/**
 * Calcula la dominancia de Bitcoin como factor de sentimiento
 * @returns Valor de 0 a 100
 */
const calculateBtcDominance = (marketData: GlobalMarketData): number => {
  // La dominancia de BTC directamente como porcentaje
  return marketData.market_cap_percentage?.btc || 50;
};

/**
 * Calcula el sentimiento en redes sociales
 * @returns Valor de -100 a 100
 */
const calculateSocialSentiment = (socialData: any[]): number => {
  // En una implementación real, esto analizaría datos de Twitter, Reddit, etc.
  
  // Por ahora, devolvemos un valor neutro o simulado
  if (socialData.length === 0) {
    return 0; // Neutral
  }
  
  // Simulación simple basada en la proporción de menciones positivas vs negativas
  const positiveCount = socialData.filter(item => item.sentiment === 'positive').length;
  const negativeCount = socialData.filter(item => item.sentiment === 'negative').length;
  const totalCount = socialData.length;
  
  if (totalCount === 0) return 0;
  
  // Calcular un puntaje de -100 a 100
  return ((positiveCount - negativeCount) / totalCount) * 100;
};

/**
 * Calcula el volumen de trading como factor de sentimiento
 * @returns Valor de 0 a 100
 */
const calculateTradingVolume = (marketData: GlobalMarketData): number => {
  // En una implementación real, esto compararía el volumen actual con promedios históricos
  
  // Como aproximación, usamos un valor basado en el volumen total
  const totalVolume = marketData.total_volume?.usd || 0;
  
  // Normalizar a una escala de 0 a 100
  // Asumimos que $100B es un volumen muy alto
  return Math.min(100, (totalVolume / 100000000000) * 100);
};

/**
 * Calcula el índice de miedo y codicia
 * @returns Valor de 0 a 100, donde 0 es miedo extremo y 100 es codicia extrema
 */
const calculateFearGreedIndex = (
  marketMomentum: number,
  volatility: number,
  btcDominance: number,
  socialMediaSentiment: number,
  tradingVolume: number
): number => {
  // Pesos para cada factor
  const weights = {
    marketMomentum: 0.35,
    volatility: 0.15,
    btcDominance: 0.1,
    socialMediaSentiment: 0.25,
    tradingVolume: 0.15
  };
  
  // Normalizar factores a escala 0-100
  const normalizedMomentum = (marketMomentum + 100) / 2;
  
  // Volatilidad alta reduce el índice (más miedo)
  const normalizedVolatility = 100 - volatility;
  
  // Dominancia de BTC alta puede indicar búsqueda de seguridad (más miedo)
  // o FOMO en BTC (más codicia) dependiendo de otros factores
  // Simplificamos asumiendo que dominancia > 60% indica miedo
  const normalizedBtcDominance = btcDominance > 60 
    ? 100 - ((btcDominance - 60) * 2.5) 
    : 50 + ((btcDominance - 40) * 2.5);
  
  // Normalizar sentimiento social
  const normalizedSocialSentiment = (socialMediaSentiment + 100) / 2;
  
  // Volumen alto puede indicar más actividad (más codicia)
  const normalizedVolume = tradingVolume;
  
  // Calcular índice ponderado
  const index = 
    (normalizedMomentum * weights.marketMomentum) +
    (normalizedVolatility * weights.volatility) +
    (normalizedBtcDominance * weights.btcDominance) +
    (normalizedSocialSentiment * weights.socialMediaSentiment) +
    (normalizedVolume * weights.tradingVolume);
  
  // Redondear a entero
  return Math.round(index);
};

/**
 * Determina el sentimiento general basado en el índice
 */
const determineSentiment = (index: number): MarketSentiment => {
  if (index <= 20) return 'extreme_fear';
  if (index <= 40) return 'fear';
  if (index <= 60) return 'neutral';
  if (index <= 80) return 'greed';
  return 'extreme_greed';
};

/**
 * Determina la tendencia del sentimiento
 */
const determineSentimentTrend = (currentIndex: number): 'improving' | 'stable' | 'worsening' => {
  // En una implementación real, esto compararía con datos históricos
  
  // Por ahora, simulamos una tendencia basada en el índice actual
  // Asumimos que índices extremos tienden a revertir a la media
  if (currentIndex <= 20 || currentIndex >= 80) {
    return 'improving';
  } else if (currentIndex <= 40 || currentIndex >= 60) {
    return 'stable';
  } else {
    // Cerca del equilibrio, podría ir en cualquier dirección
    return Math.random() > 0.5 ? 'improving' : 'worsening';
  }
};
