import React, { useState } from 'react';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '../firebase-init';
import '../styles/AuthDebugHelper.css';

const AuthDebugHelper: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [displayName, setDisplayName] = useState('Usuario de Prueba');
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const createTestUser = async () => {
    setLoading(true);
    setMessage(null);
    setError(null);

    try {
      // Crear usuario en Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Crear documento de usuario en Firestore
      const userData = {
        uid: user.uid,
        email: user.email,
        displayName: displayName,
        createdAt: new Date()
      };

      await setDoc(doc(db, 'Users', user.uid), userData);

      // Crear colección de cartera vacía para el usuario
      await setDoc(doc(db, 'Portafolio', user.uid), {
        assets: [],
        lastUpdated: new Date()
      });

      setMessage(`Usuario de prueba creado con éxito: ${email}`);
    } catch (err: any) {
      console.error('Error al crear usuario de prueba:', err);
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loginTestUser = async () => {
    setLoading(true);
    setMessage(null);
    setError(null);

    try {
      await signInWithEmailAndPassword(auth, email, password);
      setMessage('Inicio de sesión exitoso');
    } catch (err: any) {
      console.error('Error al iniciar sesión:', err);
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-debug-helper">
      <h3>Herramienta de Depuración de Autenticación</h3>
      
      {message && <div className="debug-message success">{message}</div>}
      {error && <div className="debug-message error">{error}</div>}
      
      <div className="debug-form">
        <div className="form-group">
          <label>Email:</label>
          <input 
            type="email" 
            value={email} 
            onChange={(e) => setEmail(e.target.value)} 
          />
        </div>
        
        <div className="form-group">
          <label>Contraseña:</label>
          <input 
            type="text" 
            value={password} 
            onChange={(e) => setPassword(e.target.value)} 
          />
        </div>
        
        <div className="form-group">
          <label>Nombre:</label>
          <input 
            type="text" 
            value={displayName} 
            onChange={(e) => setDisplayName(e.target.value)} 
          />
        </div>
        
        <div className="debug-actions">
          <button 
            onClick={createTestUser} 
            disabled={loading}
            className="debug-button create"
          >
            {loading ? 'Procesando...' : 'Crear Usuario de Prueba'}
          </button>
          
          <button 
            onClick={loginTestUser} 
            disabled={loading}
            className="debug-button login"
          >
            {loading ? 'Procesando...' : 'Iniciar Sesión de Prueba'}
          </button>
        </div>
      </div>
      
      <div className="debug-note">
        <p>Nota: Esta herramienta es solo para desarrollo y pruebas.</p>
      </div>
    </div>
  );
};

export default AuthDebugHelper;
