/**
 * Utilidades para formatear números, fechas y porcentajes
 */

/**
 * Formatea un número con separadores de miles y decimales
 * @param value Número a formatear
 * @param decimals Número de decimales a mostrar (por defecto 2)
 * @returns Número formateado como string
 */
export const formatNumber = (value: number, decimals: number = 2): string => {
  if (value === undefined || value === null || isNaN(value)) {
    return '0.00';
  }

  // Si el valor es muy pequeño, mostrar más decimales
  if (Math.abs(value) < 0.01 && value !== 0) {
    return value.toFixed(6);
  }

  return value.toLocaleString('es-ES', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

/**
 * Formatea un porcentaje
 * @param value Valor del porcentaje
 * @param decimals Número de decimales a mostrar (por defecto 2)
 * @returns Porcentaje formateado como string
 */
export const formatPercentage = (value: number, decimals: number = 2): string => {
  if (value === undefined || value === null || isNaN(value)) {
    return '0.00%';
  }

  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
};

/**
 * Formatea una fecha
 * @param date Fecha a formatear
 * @returns Fecha formateada como string
 */
export const formatDate = (date: Date): string => {
  if (!date) {
    return '';
  }

  return new Date(date).toLocaleDateString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

/**
 * Formatea una fecha y hora
 * @param date Fecha a formatear
 * @returns Fecha y hora formateada como string
 */
export const formatDateTime = (date: Date): string => {
  if (!date) {
    return '';
  }

  return new Date(date).toLocaleString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Formatea un valor monetario
 * @param value Valor a formatear
 * @param currency Moneda (por defecto USD)
 * @returns Valor monetario formateado como string
 */
export const formatCurrency = (value: number, currency: string = 'USD'): string => {
  if (value === undefined || value === null || isNaN(value)) {
    return '$0.00';
  }

  const formatter = new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  return formatter.format(value);
};

export default {
  formatNumber,
  formatPercentage,
  formatDate,
  formatDateTime,
  formatCurrency
};
