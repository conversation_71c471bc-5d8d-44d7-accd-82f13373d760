.intelligent-dashboard {
  width: 100%;
  max-width: 100%;
  padding: clamp(0.5rem, 2vw, 2rem);
  background-color: var(--color-background);
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 2rem);
  box-sizing: border-box;
}

/* Header con estadísticas globales */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(0.5rem, 1vw, 1rem);
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  flex-wrap: wrap;
  gap: 1rem;
}

.global-stats {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(0.5rem, 1vw, 1rem);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: clamp(0.75rem, 1vw, 0.875rem);
}

.stat-label {
  color: var(--text-secondary);
  white-space: nowrap;
}

.stat-value {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  white-space: nowrap;
}

.change {
  margin-left: 0.25rem;
  font-size: 0.75em;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
}

.change.positive {
  color: var(--color-positive);
  background-color: rgba(0, 255, 157, 0.1);
}

.change.negative {
  color: var(--color-negative);
  background-color: rgba(255, 58, 110, 0.1);
}

.loading-stats, .error-stats {
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  text-align: center;
}

.error-stats {
  color: var(--color-negative);
}

.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  min-width: 250px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-surface);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 300px;
}

.search-bar:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.search-bar input {
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  width: 100%;
  font-size: 0.875rem;
}

.search-bar input::placeholder {
  color: var(--text-tertiary);
}

.search-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  color: var(--color-primary);
}

/* Contenido principal */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

/* Barra superior con resumen del mercado */
.dashboard-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-surface-dark);
  border-radius: var(--border-radius-lg);
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.market-summary-strip {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.market-summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.market-summary-item .label {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  font-weight: var(--font-weight-medium);
}

.market-summary-item .value {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
}

.market-summary-item .change {
  font-size: 0.8rem;
  font-weight: var(--font-weight-semibold);
}

.market-summary-item .change.positive {
  color: var(--color-positive);
}

.market-summary-item .change.negative {
  color: var(--color-negative);
}

.welcome-message {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}

/* Contenedor principal con dos columnas */
.dashboard-main-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: clamp(1rem, 2vw, 2rem);
}

/* Panel lateral */
.dashboard-sidebar {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 1.5vw, 1.5rem);
}

/* Estilos para el widget del Guru */
.guru-widget {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.guru-widget:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.guru-header {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.guru-header h3 {
  margin: 0;
  color: white;
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.guru-content {
  padding: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.guru-avatar-container {
  flex-shrink: 0;
}

.guru-quick-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.guru-action-button {
  background: linear-gradient(90deg, #6e42ca, #8a56e8);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.guru-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(110, 66, 202, 0.4);
}

.guru-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.suggestion-chip {
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-chip:hover {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* Estilos para los indicadores de sentimiento */
.sentiment {
  font-weight: var(--font-weight-semibold);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  margin-left: 0.25rem;
}

.sentiment.extreme_fear {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.sentiment.fear {
  color: #e67e22;
  background-color: rgba(230, 126, 34, 0.1);
}

.sentiment.neutral {
  color: #f1c40f;
  background-color: rgba(241, 196, 15, 0.1);
}

.sentiment.greed {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
}

.sentiment.extreme_greed {
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.1);
}

/* Secciones horizontales y verticales */
.dashboard-horizontal-sections {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: clamp(1rem, 1.5vw, 1.5rem);
  margin-bottom: 1rem;
}

.dashboard-vertical-sections {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 1.5vw, 1.5rem);
}

/* Contenido principal del dashboard */
.dashboard-main {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-auto-rows: minmax(100px, auto);
  gap: clamp(1rem, 1.5vw, 1.5rem);
}

/* Sección superior con recomendaciones y resumen del mercado */
.dashboard-top-sections {
  grid-column: span 12;
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: clamp(1rem, 1.5vw, 1.5rem);
  margin-bottom: 0.5rem;
}

.market-overview-section {
  grid-column: span 7;
  height: 100%;
}

.recommendations-section {
  grid-column: span 5;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Sección media con noticias y tendencias */
.dashboard-middle-sections {
  grid-column: span 12;
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: clamp(1rem, 1.5vw, 1.5rem);
  margin-bottom: 0.5rem;
}

.news-section {
  grid-column: span 6;
  height: 100%;
}

.trends-section {
  grid-column: span 3;
  height: 100%;
}

.events-section {
  grid-column: span 3;
  height: 100%;
}

.alerts-section {
  grid-column: span 4;
  height: 100%;
}

/* Sección de tabla de criptomonedas */
.crypto-table-section {
  grid-column: span 12;
}

/* Secciones del dashboard */
.dashboard-section {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  padding: clamp(1rem, 1.5vw, 1.5rem);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.dashboard-section:hover {
  border-color: var(--border-color-hover);
  box-shadow: var(--shadow-md);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: clamp(0.75rem, 1.5vw, 1.5rem);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: var(--color-primary);
}

.section-header h2 {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

/* Filtros */
.section-header.with-filters {
  flex-wrap: wrap;
  gap: 0.75rem;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.refresh-button, .view-all-button {
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.refresh-button {
  color: var(--text-secondary);
}

.refresh-button:hover {
  color: var(--text-primary);
  background-color: var(--color-surface-light);
}

.refresh-button i {
  font-size: 0.9rem;
}

.view-all-button {
  color: var(--color-primary);
}

.view-all-button:hover {
  background-color: var(--color-primary-transparent);
  transform: translateX(2px);
}

.view-all-button i {
  font-size: 0.8rem;
}

.desktop-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.time-filter, .category-filter {
  display: flex;
  gap: 0.25rem;
}

.time-filter button, .category-filter button {
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-filter button:hover, .category-filter button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.time-filter button.active, .category-filter button.active {
  background-color: var(--color-primary-transparent);
  border-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.mobile-filter-button {
  display: none;
  background-color: var(--color-surface-dark);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: center;
  gap: 0.5rem;
}

.mobile-filter-button:hover {
  background-color: var(--color-surface-light);
  color: var(--text-primary);
}

.mobile-filters {
  display: none;
  background-color: var(--color-surface-light);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  flex-direction: column;
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group h3 {
  font-size: 0.875rem;
  margin: 0;
  color: var(--text-secondary);
}

.close-filters-button {
  background-color: var(--color-primary);
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  color: white;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
}

.close-filters-button:hover {
  background-color: var(--color-primary-light);
}

/* Estilos responsivos */
@media (max-width: 1200px) {
  .dashboard-main-container {
    grid-template-columns: 250px 1fr;
  }

  .market-summary-strip {
    gap: 1rem;
  }

  .dashboard-horizontal-sections {
    grid-template-columns: 1.5fr 1fr;
  }
}

@media (max-width: 992px) {
  .dashboard-top-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .market-summary-strip {
    width: 100%;
    justify-content: space-between;
  }

  .welcome-message {
    width: 100%;
    text-align: left;
  }

  .dashboard-main-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .dashboard-sidebar {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .guru-widget {
    grid-column: span 2;
  }

  .dashboard-horizontal-sections {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-vertical-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .desktop-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    width: 100%;
  }

  .time-filter, .category-filter {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.25rem;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .global-stats {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    justify-content: flex-start;
    gap: 1rem;
  }

  .stat-item {
    flex-basis: auto;
  }

  .search-container {
    width: 100%;
    justify-content: flex-start;
  }

  .search-bar {
    max-width: 100%;
  }

  .dashboard-sidebar {
    grid-template-columns: 1fr;
  }

  .guru-widget {
    grid-column: span 1;
  }

  .guru-content {
    flex-direction: column;
    text-align: center;
  }

  .guru-avatar-container {
    margin: 0 auto;
  }

  .desktop-filters {
    display: none;
  }

  .mobile-filter-button {
    display: flex;
  }

  .mobile-filters {
    display: flex;
  }

  .dashboard-main {
    gap: 0.75rem;
  }

  .dashboard-vertical-sections {
    grid-template-columns: 1fr;
  }

  /* Ajustar altura de widgets para evitar scroll excesivo */
  .news-section,
  .trends-section,
  .events-section,
  .alerts-section {
    max-height: 400px;
  }
}

@media (max-width: 576px) {
  .intelligent-dashboard {
    padding: 0.5rem;
  }

  .dashboard-top-bar {
    padding: 0.5rem 0.75rem;
  }

  .market-summary-strip {
    flex-direction: column;
    gap: 0.5rem;
  }

  .market-summary-item {
    justify-content: space-between;
    width: 100%;
  }

  .dashboard-section {
    padding: 0.75rem;
  }

  .section-header h2 {
    font-size: 1rem;
  }

  /* Ajustar espaciado para pantallas pequeñas */
  .dashboard-main {
    gap: 0.5rem;
  }

  /* Simplificar la visualización en móviles muy pequeños */
  .dashboard-section {
    margin-bottom: 0.5rem;
  }

  /* Ajustar altura para evitar scroll excesivo */
  .news-section,
  .trends-section,
  .events-section,
  .alerts-section {
    max-height: 350px;
  }

  /* Ocultar elementos menos importantes en pantallas muy pequeñas */
  .section-header .actions {
    display: none;
  }
}
