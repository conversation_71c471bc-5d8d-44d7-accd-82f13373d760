"""
ADK API for Criptokens

This module provides a Flask API for interacting with the ADK agents.
"""
import os
import asyncio
from flask import Flask, request, jsonify
from flask_cors import CORS

# Import the agents
from .guru_cripto_agent import run_guru_cripto
from .technical_analysis_agent import run_technical_analysis
from .sentiment_analysis_agent import run_sentiment_analysis
from .onchain_analysis_agent import run_onchain_analysis

# Create the Flask app
app = Flask(__name__)
CORS(app)

@app.route('/api/v1/guru', methods=['POST'])
def guru_cripto():
    """
    Run the Guru Cripto agent
    """
    data = request.json
    query = data.get('question', '')
    user_id = data.get('user_id', 'user')

    # Run the agent
    response = asyncio.run(run_guru_cripto(query, user_id))

    return jsonify({
        'response': response
    })

@app.route('/api/v1/technical', methods=['POST'])
def technical_analysis():
    """
    Run the Technical Analysis agent
    """
    data = request.json
    query = data.get('question', '')
    user_id = data.get('user_id', 'user')

    # Run the agent
    response = asyncio.run(run_technical_analysis(query, user_id))

    return jsonify({
        'response': response
    })

@app.route('/api/v1/sentiment', methods=['POST'])
def sentiment_analysis():
    """
    Run the Sentiment Analysis agent
    """
    data = request.json
    query = data.get('question', '')
    user_id = data.get('user_id', 'user')

    # Run the agent
    response = asyncio.run(run_sentiment_analysis(query, user_id))

    return jsonify({
        'response': response
    })

@app.route('/api/v1/onchain', methods=['POST'])
def onchain_analysis():
    """
    Run the On-Chain Analysis agent
    """
    data = request.json
    query = data.get('question', '')
    user_id = data.get('user_id', 'user')

    # Run the agent
    response = asyncio.run(run_onchain_analysis(query, user_id))

    return jsonify({
        'response': response
    })

@app.route('/api/v1/analysis', methods=['POST'])
def comprehensive_analysis():
    """
    Run a comprehensive analysis with the Guru Cripto agent
    """
    data = request.json
    crypto_name = data.get('cryptoName', '')
    timeframe = data.get('timeframe', '7d')
    user_id = data.get('user_id', 'user')

    # Create a query
    query = f"Generate a comprehensive analysis for {crypto_name} over the last {timeframe}. Include technical analysis, sentiment analysis, and on-chain analysis."

    # Run the agent
    response = asyncio.run(run_guru_cripto(query, user_id))

    return jsonify({
        'cryptoName': crypto_name,
        'timeframe': timeframe,
        'analysis': {
            'choices': [
                {
                    'message': {
                        'role': 'assistant',
                        'content': response
                    }
                }
            ]
        },
        'timestamp': asyncio.run(get_timestamp())
    })

@app.route('/api/v1/prediction', methods=['POST'])
def price_prediction():
    """
    Generate a price prediction with the Guru Cripto agent
    """
    data = request.json
    crypto_name = data.get('cryptoName', '')
    timeframe = data.get('timeframe', '7d')
    user_id = data.get('user_id', 'user')

    # Create a query
    query = f"What's your prediction for {crypto_name} price in the next {timeframe}? Base your prediction on technical analysis, sentiment analysis, and on-chain data."

    # Run the agent
    response = asyncio.run(run_guru_cripto(query, user_id))

    return jsonify({
        'cryptoName': crypto_name,
        'timeframe': timeframe,
        'prediction': {
            'choices': [
                {
                    'message': {
                        'role': 'assistant',
                        'content': response
                    }
                }
            ]
        },
        'timestamp': asyncio.run(get_timestamp())
    })

async def get_timestamp():
    """
    Get the current timestamp
    """
    from datetime import datetime
    return datetime.now().isoformat()

def run_server(host='0.0.0.0', port=8002):
    """
    Run the ADK API server
    """
    print(f"Starting ADK API server on {host}:{port}")
    app.run(host=host, port=port, debug=True)

if __name__ == '__main__':
    run_server()
