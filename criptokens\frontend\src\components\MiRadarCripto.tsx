import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRadar<PERSON>ripto } from '../hooks/useRadarCripto';
import { useAuth } from '../context/NewAuthContext';
import { CryptoCategory } from '../services/radarCripto.service';
import MigrationModal from './MigrationModal';
import anime from '../utils/animeUtils';
import '../styles/MiRadarCripto.css';

interface NotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  cryptoId: string;
  cryptoName: string;
  initialNotes: string;
  initialPriceTarget?: number | null;
  initialReason?: string;
  onSave: (notes: string, priceTarget: number | null, reason: string) => void;
}

interface AlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  cryptoId: string;
  cryptoName: string;
  currentPrice: number;
  initialAlertPrice: number | null;
  onSave: (alertType: 'above' | 'below', price: number | null) => void;
}

// Componente Modal para Notas
const NotesModal: React.FC<NotesModalProps> = ({
  isOpen,
  onClose,
  cryptoId,
  cryptoName,
  initialNotes,
  initialPriceTarget,
  initialReason,
  onSave
}) => {
  const [notes, setNotes] = useState(initialNotes || '');
  const [priceTarget, setPriceTarget] = useState<string>(initialPriceTarget ? initialPriceTarget.toString() : '');
  const [reason, setReason] = useState(initialReason || '');

  useEffect(() => {
    if (isOpen) {
      setNotes(initialNotes || '');
      setPriceTarget(initialPriceTarget ? initialPriceTarget.toString() : '');
      setReason(initialReason || '');
    }
  }, [isOpen, initialNotes, initialPriceTarget, initialReason]);

  const handleSave = () => {
    const parsedPriceTarget = priceTarget ? parseFloat(priceTarget) : null;
    onSave(notes, parsedPriceTarget, reason);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-backdrop" onClick={onClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Notas para {cryptoName}</h3>
          <button className="modal-close" onClick={onClose}>&times;</button>
        </div>
        <div className="modal-body">
          <div className="form-group">
            <label htmlFor="notes">Notas:</label>
            <textarea
              id="notes"
              className="form-control"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Añade tus notas aquí..."
            />
          </div>
          <div className="form-group">
            <label htmlFor="priceTarget">Precio Objetivo (USD):</label>
            <input
              type="number"
              id="priceTarget"
              className="form-control"
              value={priceTarget}
              onChange={(e) => setPriceTarget(e.target.value)}
              placeholder="Ej: 50000"
              step="0.01"
            />
          </div>
          <div className="form-group">
            <label htmlFor="reason">Razón para seguir:</label>
            <textarea
              id="reason"
              className="form-control"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="¿Por qué estás siguiendo esta criptomoneda?"
            />
          </div>
        </div>
        <div className="modal-footer">
          <button className="btn-secondary" onClick={onClose}>Cancelar</button>
          <button className="btn-primary" onClick={handleSave}>Guardar</button>
        </div>
      </div>
    </div>
  );
};

// Componente Modal para Alertas
const AlertModal: React.FC<AlertModalProps> = ({
  isOpen,
  onClose,
  cryptoId,
  cryptoName,
  currentPrice,
  initialAlertPrice,
  onSave
}) => {
  const [alertType, setAlertType] = useState<'above' | 'below'>('above');
  const [alertPrice, setAlertPrice] = useState<string>(initialAlertPrice ? initialAlertPrice.toString() : '');

  useEffect(() => {
    if (isOpen) {
      setAlertPrice(initialAlertPrice ? initialAlertPrice.toString() : '');
      setAlertType('above');
    }
  }, [isOpen, initialAlertPrice]);

  const handleSave = () => {
    const parsedPrice = alertPrice ? parseFloat(alertPrice) : null;
    onSave(alertType, parsedPrice);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-backdrop" onClick={onClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Configurar Alerta para {cryptoName}</h3>
          <button className="modal-close" onClick={onClose}>&times;</button>
        </div>
        <div className="modal-body">
          <div className="form-group">
            <label>Tipo de Alerta:</label>
            <div style={{ display: 'flex', gap: '10px', marginTop: '5px' }}>
              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <input
                  type="radio"
                  name="alertType"
                  checked={alertType === 'above'}
                  onChange={() => setAlertType('above')}
                  style={{ marginRight: '5px' }}
                />
                Notificar si sube por encima de
              </label>
              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <input
                  type="radio"
                  name="alertType"
                  checked={alertType === 'below'}
                  onChange={() => setAlertType('below')}
                  style={{ marginRight: '5px' }}
                />
                Notificar si baja por debajo de
              </label>
            </div>
          </div>
          <div className="form-group">
            <label htmlFor="alertPrice">Precio (USD):</label>
            <input
              type="number"
              id="alertPrice"
              className="form-control"
              value={alertPrice}
              onChange={(e) => setAlertPrice(e.target.value)}
              placeholder={`Precio actual: $${currentPrice.toFixed(2)}`}
              step="0.01"
            />
          </div>
          <p style={{ color: '#aaa', fontSize: '12px', marginTop: '10px' }}>
            Nota: Las alertas se mostrarán en la aplicación cuando el precio alcance el valor configurado.
          </p>
        </div>
        <div className="modal-footer">
          <button className="btn-secondary" onClick={onClose}>Cancelar</button>
          <button className="btn-primary" onClick={handleSave}>Guardar</button>
        </div>
      </div>
    </div>
  );
};

const MiRadarCripto: React.FC = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const {
    radarWithPrices,
    favorites,
    isLoading,
    error,
    removeFromRadar,
    refreshPrices,
    updateNotes,
    updateCategory,
    toggleItemFavorite,
    setAlertPrice: updateAlertPrice,
    getByCategory,
    loadRadar
  } = useRadarCripto();

  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'ascending' | 'descending';
  }>({ key: 'name', direction: 'ascending' });

  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [showCategoryModal, setShowCategoryModal] = useState<boolean>(false);
  const [selectedCryptoForCategory, setSelectedCryptoForCategory] = useState<string | null>(null);

  // Estados para los nuevos modales
  const [showNotesModal, setShowNotesModal] = useState<boolean>(false);
  const [showAlertModal, setShowAlertModal] = useState<boolean>(false);
  const [selectedCrypto, setSelectedCrypto] = useState<any>(null);

  // Estado para el modal de migración
  const [showMigrationModal, setShowMigrationModal] = useState<boolean>(true);

  // Formatear números para mostrar
  const formatNumber = (num: number, decimals = 2) => {
    return num.toLocaleString(undefined, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  };

  // Formatear fecha para mostrar
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Efecto para animar elementos cuando se cargan
  useEffect(() => {
    if (Array.isArray(radarWithPrices) && radarWithPrices.length > 0 && !isLoading) {
      anime({
        targets: '.crypto-row',
        opacity: [0, 1],
        translateY: [20, 0],
        delay: anime.stagger(100),
        easing: 'easeOutExpo'
      });
    }
  }, [radarWithPrices, isLoading]);

  // Función para manejar la eliminación de una criptomoneda
  const handleRemoveCrypto = async (id: string) => {
    if (!currentUser) return;

    if (window.confirm('¿Estás seguro de que quieres eliminar esta criptomoneda de tu radar?')) {
      await removeFromRadar(id);
    }
  };

  // Función para abrir el modal de notas
  const handleOpenNotesModal = (crypto: any) => {
    setSelectedCrypto(crypto);
    setShowNotesModal(true);
  };

  // Función para guardar las notas
  const handleSaveNotes = async (
    notes: string,
    priceTarget: number | null,
    entryPrice: number | null,
    stopLoss: number | null,
    reason: string,
    resources: string[]
  ) => {
    if (!selectedCrypto || !currentUser) return;

    // Crear un objeto con las notas enriquecidas
    const enrichedNotes = JSON.stringify({
      text: notes,
      priceTarget,
      entryPrice,
      stopLoss,
      reason,
      resources,
      updatedAt: new Date().toISOString()
    });

    await updateNotes(selectedCrypto.id, enrichedNotes);
  };

  // Función para abrir el modal de alertas
  const handleOpenAlertModal = (crypto: any) => {
    setSelectedCrypto(crypto);
    setShowAlertModal(true);
  };

  // Función para guardar la alerta
  const handleSaveAlert = async (
    alertType: 'above' | 'below' | 'percent-up' | 'percent-down',
    price: number | null,
    percentage: number | null,
    notificationTypes: ('app' | 'email')[]
  ) => {
    if (!selectedCrypto || !currentUser) return;

    // Guardar la configuración de la alerta
    const alertConfig = (price || percentage) ? {
      price,
      percentage,
      type: alertType,
      notificationTypes,
      createdAt: new Date().toISOString()
    } : null;

    // Si hay una configuración, la guardamos
    if (alertConfig) {
      // Por ahora, solo guardamos el precio en el servicio actual
      // En el futuro, podríamos modificar el servicio para guardar la configuración completa
      await updateAlertPrice(selectedCrypto.id, price);

      // Mostrar un mensaje de confirmación
      alert(`Alerta configurada para ${selectedCrypto.name}. Recibirás una notificación cuando se cumpla la condición.`);
    } else {
      // Si no hay configuración, eliminamos la alerta
      await updateAlertPrice(selectedCrypto.id, null);
    }
  };

  // Función para abrir el modal de categorías
  const handleOpenCategoryModal = (id: string) => {
    setSelectedCryptoForCategory(id);
    setShowCategoryModal(true);

    // Animar la apertura del modal
    setTimeout(() => {
      anime({
        targets: '.category-modal',
        opacity: [0, 1],
        scale: [0.9, 1],
        duration: 300,
        easing: 'easeOutExpo'
      });
    }, 10);
  };

  // Función para establecer la categoría
  const handleSetCategory = async (category: CryptoCategory) => {
    if (selectedCryptoForCategory && currentUser) {
      await updateCategory(selectedCryptoForCategory, category);
      setShowCategoryModal(false);
      setSelectedCryptoForCategory(null);
    }
  };

  // Función para marcar/desmarcar como favorito
  const handleToggleFavorite = async (id: string, currentStatus: boolean) => {
    if (currentUser) {
      await toggleItemFavorite(id, !currentStatus);

      // Animar el cambio de estado
      anime({
        targets: `.favorite-star-${id}`,
        scale: [1, 1.3, 1],
        rotate: ['0deg', '360deg'],
        duration: 500,
        easing: 'easeInOutBack'
      });
    }
  };

  // Función para ordenar la watchlist
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Filtrar el radar según el filtro activo y el término de búsqueda
  const filteredRadar = Array.isArray(radarWithPrices) ? radarWithPrices.filter(crypto => {
    // Filtrar por categoría
    if (activeFilter !== 'all' && activeFilter !== 'favorites') {
      if (crypto.category !== activeFilter) return false;
    }

    // Filtrar favoritos
    if (activeFilter === 'favorites' && !crypto.isFavorite) {
      return false;
    }

    // Filtrar por término de búsqueda
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return crypto.name.toLowerCase().includes(term) ||
             crypto.symbol.toLowerCase().includes(term);
    }

    return true;
  }) : [];

  // Ordenar el radar según la configuración actual
  const sortedRadar = [...filteredRadar].sort((a, b) => {
    if (a[sortConfig.key as keyof typeof a] < b[sortConfig.key as keyof typeof b]) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (a[sortConfig.key as keyof typeof a] > b[sortConfig.key as keyof typeof b]) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0;
  });

  // Función para obtener la clase de la flecha de ordenamiento
  const getSortDirectionArrow = (key: string) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'ascending' ? '↑' : '↓';
    }
    return '';
  };

  // Función para analizar con el Gurú
  const analyzeWithGuru = () => {
    if (!Array.isArray(radarWithPrices) || radarWithPrices.length === 0) {
      alert('No hay criptomonedas en tu radar para analizar.');
      return;
    }

    // Filtrar por las monedas seleccionadas o usar todas si no hay selección
    const cryptosToAnalyze = activeFilter === 'all'
      ? radarWithPrices
      : activeFilter === 'favorites'
        ? radarWithPrices.filter(crypto => crypto.isFavorite)
        : radarWithPrices.filter(crypto => crypto.category === activeFilter);

    if (cryptosToAnalyze.length === 0) {
      alert('No hay criptomonedas en la selección actual para analizar.');
      return;
    }

    // Obtener los símbolos de las criptomonedas seleccionadas
    const symbols = cryptosToAnalyze.map(crypto => crypto.symbol.toUpperCase()).join(', ');

    // Obtener información adicional para el análisis
    const topGainers = [...cryptosToAnalyze]
      .sort((a, b) => b.priceChangePercentage24h - a.priceChangePercentage24h)
      .slice(0, 3)
      .map(crypto => `${crypto.symbol.toUpperCase()} (${crypto.priceChangePercentage24h.toFixed(2)}%)`);

    const topLosers = [...cryptosToAnalyze]
      .sort((a, b) => a.priceChangePercentage24h - b.priceChangePercentage24h)
      .slice(0, 3)
      .map(crypto => `${crypto.symbol.toUpperCase()} (${crypto.priceChangePercentage24h.toFixed(2)}%)`);

    // Crear el mensaje para el Gurú con más contexto
    const message = `Gurú, necesito un análisis detallado de las siguientes monedas en mi radar: ${symbols}.

Específicamente me gustaría saber:
1. ¿Hay noticias importantes o tendencias recientes para estas monedas?
2. ¿Cuáles tienen mejor perspectiva a corto y medio plazo?
3. ¿Hay alguna que debería considerar vender o comprar más?

Las que mejor rendimiento han tenido en las últimas 24h son: ${topGainers.join(', ')}
Las que peor rendimiento han tenido son: ${topLosers.join(', ')}

Por favor, proporciona un análisis fundamentado y objetivo.`;

    // Guardar el mensaje en localStorage para recuperarlo en la página del Gurú
    localStorage.setItem('guruAnalysisMessage', message);

    // Navegar a la página del Gurú
    navigate('/guru');
  };

  // Función para parsear las notas enriquecidas
  const parseEnrichedNotes = (notesString: string | undefined) => {
    if (!notesString) return {
      text: '',
      priceTarget: null,
      entryPrice: null,
      stopLoss: null,
      reason: '',
      resources: []
    };

    try {
      const parsed = JSON.parse(notesString);
      return {
        text: parsed.text || '',
        priceTarget: parsed.priceTarget || null,
        entryPrice: parsed.entryPrice || null,
        stopLoss: parsed.stopLoss || null,
        reason: parsed.reason || '',
        resources: parsed.resources || []
      };
    } catch (e) {
      // Si no es JSON, asumimos que es una nota simple
      return {
        text: notesString,
        priceTarget: null,
        entryPrice: null,
        stopLoss: null,
        reason: '',
        resources: []
      };
    }
  };

  if (!currentUser) {
    return (
      <div className="radar-container">
        <div className="radar-not-logged-in">
          <h3>Acceso Restringido</h3>
          <p>Debes iniciar sesión para ver y gestionar tu radar de criptomonedas.</p>
        </div>
      </div>
    );
  }

  if (isLoading && (!Array.isArray(radarWithPrices) || radarWithPrices.length === 0)) {
    return <div className="radar-loading">Cargando radar...</div>;
  }

  if (error && (!Array.isArray(radarWithPrices) || radarWithPrices.length === 0)) {
    return <div className="radar-error">{error}</div>;
  }

  // Obtener las categorías disponibles en el radar
  const availableCategories = Array.isArray(radarWithPrices)
    ? Array.from(new Set(radarWithPrices.map(item => item.category || 'other'))) as CryptoCategory[]
    : [];

  // Renderizar el modal de categorías
  const renderCategoryModal = () => {
    if (!showCategoryModal) return null;

    const categories: CryptoCategory[] = [
      'defi', 'nft', 'layer1', 'layer2', 'stablecoin', 'meme', 'exchange', 'other'
    ];

    return (
      <div className="modal-backdrop" onClick={() => setShowCategoryModal(false)}>
        <div className="category-modal" onClick={e => e.stopPropagation()}>
          <h3>Seleccionar Categoría</h3>
          <div className="category-options">
            {categories.map(cat => (
              <button
                key={cat}
                className={`category-button category-${cat}`}
                onClick={() => handleSetCategory(cat)}
              >
                {cat === 'defi' ? 'DeFi' :
                 cat === 'nft' ? 'NFT' :
                 cat === 'layer1' ? 'Layer 1' :
                 cat === 'layer2' ? 'Layer 2' :
                 cat === 'stablecoin' ? 'Stablecoin' :
                 cat === 'meme' ? 'Meme' :
                 cat === 'exchange' ? 'Exchange' : 'Otro'}
              </button>
            ))}
          </div>
          <button className="close-modal-button" onClick={() => setShowCategoryModal(false)}>
            Cancelar
          </button>
        </div>
      </div>
    );
  };

  // Función para manejar la finalización de la migración
  const handleMigrationComplete = () => {
    // Recargar los datos después de la migración
    loadRadar();
  };

  return (
    <div className="radar-container">
      {/* Modal de Migración */}
      <MigrationModal
        isOpen={showMigrationModal}
        onClose={() => setShowMigrationModal(false)}
        onMigrationComplete={handleMigrationComplete}
      />

      {renderCategoryModal()}

      {/* Modal de Notas */}
      {selectedCrypto && (
        <NotesModal
          isOpen={showNotesModal}
          onClose={() => setShowNotesModal(false)}
          cryptoId={selectedCrypto.id}
          cryptoName={selectedCrypto.name}
          initialNotes={parseEnrichedNotes(selectedCrypto.notes).text}
          initialPriceTarget={parseEnrichedNotes(selectedCrypto.notes).priceTarget}
          initialReason={parseEnrichedNotes(selectedCrypto.notes).reason}
          onSave={(notes, priceTarget, reason) =>
            handleSaveNotes(notes, priceTarget, null, null, reason, [])
          }
        />
      )}

      {/* Modal de Alertas */}
      {selectedCrypto && (
        <AlertModal
          isOpen={showAlertModal}
          onClose={() => setShowAlertModal(false)}
          cryptoId={selectedCrypto.id}
          cryptoName={selectedCrypto.name}
          currentPrice={selectedCrypto.currentPrice}
          initialAlertPrice={selectedCrypto.customAlertPrice}
          onSave={(alertType, price) =>
            handleSaveAlert(alertType, price, null, ['app'])
          }
        />
      )}

      <button className="analyze-with-guru" onClick={analyzeWithGuru}>
        <i className="fas fa-robot"></i>
        Analizar Radar con Gurú Cripto
        {activeFilter !== 'all' && (
          <span className="filter-badge">
            {activeFilter === 'favorites' ? 'Favoritas' : activeFilter}
          </span>
        )}
      </button>

      <div className="radar-header">
        <h2>Mi Radar Cripto</h2>
        <div className="radar-actions">
          <div className="search-container">
            <input
              type="text"
              placeholder="Buscar criptomoneda..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          <button
            className="refresh-button"
            onClick={refreshPrices}
            disabled={isLoading || !Array.isArray(radarWithPrices) || radarWithPrices.length === 0}
          >
            Actualizar Precios
          </button>
        </div>
      </div>

      <div className="filter-tabs">
        <button
          className={`filter-tab ${activeFilter === 'all' ? 'active' : ''}`}
          onClick={() => setActiveFilter('all')}
        >
          Todas
        </button>
        <button
          className={`filter-tab ${activeFilter === 'favorites' ? 'active' : ''}`}
          onClick={() => setActiveFilter('favorites')}
        >
          Favoritas
        </button>
        {availableCategories.map(cat => (
          <button
            key={cat}
            className={`filter-tab ${activeFilter === cat ? 'active' : ''}`}
            onClick={() => setActiveFilter(cat)}
          >
            {cat === 'defi' ? 'DeFi' :
             cat === 'nft' ? 'NFT' :
             cat === 'layer1' ? 'Layer 1' :
             cat === 'layer2' ? 'Layer 2' :
             cat === 'stablecoin' ? 'Stablecoin' :
             cat === 'meme' ? 'Meme' :
             cat === 'exchange' ? 'Exchange' : 'Otro'}
          </button>
        ))}
      </div>

      {Array.isArray(radarWithPrices) && radarWithPrices.length > 0 ? (
        <div className="radar-cryptos">
          <table className="cryptos-table">
            <thead>
              <tr>
                <th onClick={() => requestSort('name')}>
                  Criptomoneda {getSortDirectionArrow('name')}
                </th>
                <th onClick={() => requestSort('currentPrice')}>
                  Precio {getSortDirectionArrow('currentPrice')}
                </th>
                <th onClick={() => requestSort('priceChangePercentage24h')}>
                  24h % {getSortDirectionArrow('priceChangePercentage24h')}
                </th>
                <th onClick={() => requestSort('marketCap')}>
                  Cap. de Mercado {getSortDirectionArrow('marketCap')}
                </th>
                <th onClick={() => requestSort('totalVolume')}>
                  Volumen (24h) {getSortDirectionArrow('totalVolume')}
                </th>
                <th onClick={() => requestSort('category')}>
                  Categoría {getSortDirectionArrow('category')}
                </th>
                <th onClick={() => requestSort('addedAt')}>
                  Añadido el {getSortDirectionArrow('addedAt')}
                </th>
                <th>Notas</th>
                <th>Alertas</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              {sortedRadar.map((crypto) => {
                const enrichedNotes = parseEnrichedNotes(crypto.notes);

                return (
                  <tr key={crypto.id} className="crypto-row">
                    <td className="crypto-info">
                      <button
                        className={`favorite-button favorite-star-${crypto.id} ${crypto.isFavorite ? 'active' : ''}`}
                        onClick={() => handleToggleFavorite(crypto.id, crypto.isFavorite || false)}
                        title={crypto.isFavorite ? 'Quitar de favoritos' : 'Añadir a favoritos'}
                      >
                        {crypto.isFavorite ? '★' : '☆'}
                      </button>
                      {crypto.image && (
                        <img
                          src={crypto.image}
                          alt={crypto.name}
                          className="crypto-icon"
                        />
                      )}
                      <div>
                        <span className="crypto-name">{crypto.name}</span>
                        <span className="crypto-symbol">{crypto.symbol}</span>
                      </div>
                    </td>
                    <td>${formatNumber(crypto.currentPrice)}</td>
                    <td className={crypto.priceChangePercentage24h >= 0 ? 'positive' : 'negative'}>
                      {crypto.priceChangePercentage24h >= 0 ? '+' : ''}
                      {formatNumber(crypto.priceChangePercentage24h)}%
                    </td>
                    <td>${formatNumber(crypto.marketCap)}</td>
                    <td>${formatNumber(crypto.totalVolume)}</td>
                    <td>
                      <span
                        className={`category-badge category-${crypto.category || 'other'}`}
                        onClick={() => handleOpenCategoryModal(crypto.id)}
                      >
                        {crypto.category === 'defi' ? 'DeFi' :
                         crypto.category === 'nft' ? 'NFT' :
                         crypto.category === 'layer1' ? 'Layer 1' :
                         crypto.category === 'layer2' ? 'Layer 2' :
                         crypto.category === 'stablecoin' ? 'Stablecoin' :
                         crypto.category === 'meme' ? 'Meme' :
                         crypto.category === 'exchange' ? 'Exchange' : 'Otro'}
                      </span>
                    </td>
                    <td>{formatDate(crypto.addedAt)}</td>
                    <td>
                      <div className="notes-display">
                        {enrichedNotes.text ? (
                          <div className="note-content" title={enrichedNotes.text}>
                            {enrichedNotes.text.length > 30 ? `${enrichedNotes.text.substring(0, 30)}...` : enrichedNotes.text}
                            {enrichedNotes.priceTarget && ` (Objetivo: $${enrichedNotes.priceTarget})`}
                            {enrichedNotes.resources && enrichedNotes.resources.length > 0 &&
                              <span className="resources-badge" title="Tiene recursos guardados">
                                <i className="fas fa-link"></i> {enrichedNotes.resources.length}
                              </span>
                            }
                          </div>
                        ) : (
                          <span className="no-notes">Sin notas</span>
                        )}
                        <button
                          className="edit-notes-button"
                          onClick={() => handleOpenNotesModal(crypto)}
                        >
                          {enrichedNotes.text ? 'Editar' : 'Añadir'}
                        </button>
                      </div>
                    </td>
                    <td>
                      <div className="alert-display">
                        {crypto.customAlertPrice ? (
                          <div className="alert-content">
                            Alerta: ${formatNumber(crypto.customAlertPrice)}
                          </div>
                        ) : (
                          <span className="no-alert">Sin alerta</span>
                        )}
                        <button
                          className="edit-alert-button"
                          onClick={() => handleOpenAlertModal(crypto)}
                        >
                          <i className="fas fa-bell"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <button
                        className="remove-crypto-button"
                        onClick={() => handleRemoveCrypto(crypto.id)}
                      >
                        Eliminar
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="empty-radar">
          <p>No tienes criptomonedas en tu radar.</p>
          <p>Añade criptomonedas a tu radar desde la página de mercado o desde la página de detalles de una criptomoneda.</p>
        </div>
      )}
    </div>
  );
};

export default MiRadarCripto;
