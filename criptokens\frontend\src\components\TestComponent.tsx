import React, { useEffect, useState } from 'react';
import { getTopCryptocurrencies, getGlobalMarketData } from '../services/api';

const TestComponent: React.FC = () => {
  const [cryptos, setCryptos] = useState<any[]>([]);
  const [marketData, setMarketData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Obteniendo datos de criptomonedas...');
        const cryptoData = await getTopCryptocurrencies(10);
        console.log('Datos de criptomonedas recibidos:', cryptoData);
        
        if (Array.isArray(cryptoData) && cryptoData.length > 0) {
          setCryptos(cryptoData);
        } else {
          setError('Los datos de criptomonedas no son válidos');
        }
        
        console.log('Obteniendo datos del mercado global...');
        const globalData = await getGlobalMarketData();
        console.log('Datos del mercado global recibidos:', globalData);
        
        if (globalData && globalData.data) {
          setMarketData(globalData.data);
        } else {
          setError('Los datos del mercado global no son válidos');
        }
      } catch (err) {
        console.error('Error al obtener datos:', err);
        setError('Error al obtener datos');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  if (loading) {
    return (
      <div style={{ padding: '20px', color: 'white', textAlign: 'center' }}>
        <h2>Cargando datos...</h2>
        <p>Por favor espera mientras se cargan los datos</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', color: 'red', textAlign: 'center' }}>
        <h2>Error</h2>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', color: 'white' }}>
      <h1>Componente de Prueba</h1>
      
      <div style={{ marginBottom: '30px' }}>
        <h2>Datos del Mercado Global</h2>
        {marketData ? (
          <div>
            <p>Capitalización Total: ${marketData.total_market_cap?.usd?.toLocaleString()}</p>
            <p>Volumen 24h: ${marketData.total_volume?.usd?.toLocaleString()}</p>
            <p>Dominancia BTC: {marketData.market_cap_percentage?.btc?.toFixed(2)}%</p>
            <p>Cambio 24h: {marketData.market_cap_change_percentage_24h_usd?.toFixed(2)}%</p>
          </div>
        ) : (
          <p>No hay datos del mercado disponibles</p>
        )}
      </div>
      
      <div>
        <h2>Top Criptomonedas</h2>
        {cryptos.length > 0 ? (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ borderBottom: '1px solid #444' }}>
                <th style={{ textAlign: 'left', padding: '10px' }}>Nombre</th>
                <th style={{ textAlign: 'right', padding: '10px' }}>Precio</th>
                <th style={{ textAlign: 'right', padding: '10px' }}>24h %</th>
              </tr>
            </thead>
            <tbody>
              {cryptos.map((crypto) => (
                <tr key={crypto.id} style={{ borderBottom: '1px solid #333' }}>
                  <td style={{ padding: '10px', display: 'flex', alignItems: 'center' }}>
                    {crypto.image && (
                      <img 
                        src={crypto.image} 
                        alt={crypto.name} 
                        style={{ width: '24px', height: '24px', marginRight: '10px' }} 
                      />
                    )}
                    <div>
                      <div>{crypto.name}</div>
                      <div style={{ fontSize: '12px', color: '#999' }}>{crypto.symbol.toUpperCase()}</div>
                    </div>
                  </td>
                  <td style={{ textAlign: 'right', padding: '10px' }}>
                    ${crypto.current_price?.toLocaleString()}
                  </td>
                  <td 
                    style={{ 
                      textAlign: 'right', 
                      padding: '10px',
                      color: crypto.price_change_percentage_24h >= 0 ? '#00ff9d' : '#ff3a6e'
                    }}
                  >
                    {crypto.price_change_percentage_24h >= 0 ? '+' : ''}
                    {crypto.price_change_percentage_24h?.toFixed(2)}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>No hay datos de criptomonedas disponibles</p>
        )}
      </div>
    </div>
  );
};

export default TestComponent;
