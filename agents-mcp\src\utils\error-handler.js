/**
 * Manejador de errores para el sistema de agentes MCP
 * 
 * Este módulo proporciona clases y funciones para manejar errores
 * de manera consistente en todo el sistema.
 */

const logger = require('./logger');

/**
 * Clase base para errores del sistema de agentes MCP
 */
class AgentsMcpError extends Error {
  /**
   * @param {string} message - Mensaje de error
   * @param {string} code - Código de error
   * @param {Object} [details] - Detalles adicionales del error
   * @param {Error} [cause] - Error que causó este error
   */
  constructor(message, code, details = null, cause = null) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.details = details;
    this.cause = cause;
    
    // Capturar stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Error de configuración
 */
class ConfigurationError extends AgentsMcpError {
  constructor(message, details = null, cause = null) {
    super(message, 'CONFIGURATION_ERROR', details, cause);
  }
}

/**
 * Error de comunicación con servidor MCP
 */
class McpServerError extends AgentsMcpError {
  constructor(message, details = null, cause = null) {
    super(message, 'MCP_SERVER_ERROR', details, cause);
  }
}

/**
 * Error de agente
 */
class AgentError extends AgentsMcpError {
  constructor(message, details = null, cause = null) {
    super(message, 'AGENT_ERROR', details, cause);
  }
}

/**
 * Error de orquestador
 */
class OrchestratorError extends AgentsMcpError {
  constructor(message, details = null, cause = null) {
    super(message, 'ORCHESTRATOR_ERROR', details, cause);
  }
}

/**
 * Error de tarea
 */
class TaskError extends AgentsMcpError {
  constructor(message, details = null, cause = null) {
    super(message, 'TASK_ERROR', details, cause);
  }
}

/**
 * Error de validación
 */
class ValidationError extends AgentsMcpError {
  constructor(message, details = null, cause = null) {
    super(message, 'VALIDATION_ERROR', details, cause);
  }
}

/**
 * Maneja un error registrándolo y opcionalmente relanzándolo
 * @param {Error} error - Error a manejar
 * @param {string} source - Fuente del error (nombre del módulo)
 * @param {boolean} [rethrow=false] - Si es true, relanza el error después de registrarlo
 * @returns {Error} El error original
 */
function handleError(error, source, rethrow = false) {
  // Registrar el error
  logger.error(source, error.message, {
    name: error.name,
    code: error.code,
    details: error.details,
    cause: error.cause,
    stack: error.stack
  });
  
  // Relanzar el error si es necesario
  if (rethrow) {
    throw error;
  }
  
  return error;
}

module.exports = {
  AgentsMcpError,
  ConfigurationError,
  McpServerError,
  AgentError,
  OrchestratorError,
  TaskError,
  ValidationError,
  handleError
};
