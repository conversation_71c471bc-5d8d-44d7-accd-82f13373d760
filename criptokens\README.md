# Criptokens

Plataforma de análisis y seguimiento de criptomonedas con inteligencia artificial.

## Estructura del Proyecto

El proyecto Criptokens está compuesto por varios componentes:

- **Frontend**: Interfaz de usuario desarrollada con React y Vite (puerto 5173)
- **Backend**: API REST desarrollada con Express (puerto 3001)
- **MCP Crypto Server**: Servidor MCP para datos de criptomonedas (puerto 3101)
- **MCP Brave Server**: Servidor MCP para búsqueda de noticias (puerto 3102)
- **MCP Playwright Server**: Servidor MCP para visualización de páginas web (puerto 3103)

## Requisitos Previos

- Node.js (v14.x o superior)
- npm (v6.x o superior)

## Instalación

### Clonar el repositorio

```bash
git clone <url-del-repositorio>
cd criptokens
```

### Configurar el Backend

```bash
cd backend
npm install
```

Crea un archivo `.env` en la carpeta `backend` basado en el archivo `.env.example`:

```bash
cp .env.example .env
```

Edita el archivo `.env` y añade tu clave API del proveedor de LLM elegido.

### Configurar el Frontend

```bash
cd ../frontend
npm install
```

## Verificación de Configuración

Antes de ejecutar la aplicación, es recomendable verificar que todos los componentes estén correctamente configurados:

```bash
node check-config.js
```

Este script verificará:
- Que todos los directorios necesarios existan
- Que los archivos de configuración estén presentes
- Que las API keys estén configuradas
- Que los servidores estén accesibles (si están en ejecución)

## Ejecución

### Opción 1: Iniciar todos los componentes con un solo comando (Recomendado)

Para iniciar todos los servidores necesarios (MCP Crypto, MCP Brave, MCP Playwright, Backend y Frontend) en el orden correcto:

```bash
node start-criptokens.js
```

Este script iniciará todos los componentes en el siguiente orden:
1. Crypto MCP Server (puerto 3101)
2. Brave Search MCP Server (puerto 3102)
3. Playwright MCP Server (puerto 3103)
4. Backend (puerto 3001)
5. Frontend (puerto 5173)

### Opción 2: Iniciar solo los servidores MCP

Si solo necesitas iniciar los servidores MCP (Crypto, Brave y Playwright):

```bash
node start-mcp-servers.js
```

### Opción 3: Iniciar manualmente cada componente

Si prefieres iniciar cada componente por separado:

1. Inicia el servidor MCP de Crypto (puerto 3101):
   ```bash
   cd ../crypto-mcp-server
   node http-server.js
   ```

2. Inicia el servidor MCP de Brave Search (puerto 3102):
   ```bash
   node brave-search-server.js
   ```

3. Inicia el servidor MCP de Playwright (puerto 3103):
   ```bash
   cd ../playwright-mcp-server
   node dist/server.js
   ```

4. Inicia el backend (puerto 3001):
   ```bash
   cd backend
   node src/server.js
   ```

5. Inicia el frontend (puerto 5173):
   ```bash
   cd frontend
   npm run dev
   ```

La aplicación estará disponible en `http://localhost:5173`.

## Funcionalidades Principales

- **Dashboard**: Visualización de datos de criptomonedas en tiempo real
- **Mi Portafolio**: Gestión de tu portafolio de criptomonedas
- **Mi Radar Cripto**: Seguimiento de criptomonedas de interés
- **Noticias Cripto**: Últimas noticias sobre criptomonedas
- **Gurú Cripto**: Asistente de IA para análisis y recomendaciones

## Tecnologías Utilizadas

- **Backend**:
  - Node.js
  - Express.js
  - Axios (para llamadas a APIs externas)
  - dotenv (para gestión de variables de entorno)
  - CORS (para permitir solicitudes cross-origin)

- **Frontend**:
  - React
  - TypeScript
  - Vite (para desarrollo y construcción)

- **Servidores MCP**:
  - Model Context Protocol (MCP)
  - CoinMarketCap API
  - Brave Search API
  - Playwright

## Solución de Problemas

### Verificación de Configuración

Si encuentras problemas al iniciar la aplicación, ejecuta el script de verificación de configuración:

```bash
node check-config.js
```

Este script te ayudará a identificar problemas comunes en la configuración.

### Error al crear conversaciones

Si encuentras un error al crear conversaciones en el Gurú Cripto, asegúrate de que:

1. El backend esté en ejecución en el puerto 3001
2. Estés autenticado en la aplicación
3. La ruta `/api/conversations` esté correctamente configurada en el backend

### Error de conexión con los servidores MCP

Si encuentras errores de conexión con los servidores MCP, verifica que:

1. Todos los servidores MCP estén en ejecución en los puertos correctos
2. No haya conflictos de puertos con otras aplicaciones
3. Las rutas de los servidores MCP estén correctamente configuradas en el archivo `config.js`

### Problemas con las API keys

Si encuentras problemas relacionados con las API keys:

1. Verifica que todas las API keys estén correctamente configuradas en el archivo `config.js`
2. Asegúrate de que las API keys sean válidas y no hayan expirado
3. Verifica los límites de uso de las APIs (algunas tienen límites diarios o mensuales)

### Problemas con CORS

Si encuentras errores de CORS:

1. Verifica que la configuración CORS en el backend permita solicitudes desde el frontend
2. Asegúrate de que la configuración `credentials` sea consistente entre el frontend y el backend
3. Verifica que los orígenes permitidos incluyan `http://localhost:5173`

## Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para más detalles.
