"""
Test script for Guru Cripto Agent
"""
import os
import asyncio
from dotenv import load_dotenv
from google.adk.runtime import Runtime

# Load environment variables
load_dotenv("adk_agents/.env")

# Import the Guru Cripto agent
from adk_agents.guru_agent import guru_agent

async def main():
    """Test the Guru Cripto agent."""
    print("Initializing ADK Runtime...")
    runtime = Runtime()
    session = runtime.new_session()
    
    # Test query
    query = "¿Cuál es tu predicción para Bitcoin en la próxima semana?"
    print(f"\nSending query: {query}")
    
    # Run the agent
    print("\nRunning Guru Cripto agent...")
    response = await guru_agent.run_async(
        session=session,
        query=query
    )
    
    # Print the response
    print("\nResponse from Guru Cripto:")
    print(response)

if __name__ == "__main__":
    asyncio.run(main())
