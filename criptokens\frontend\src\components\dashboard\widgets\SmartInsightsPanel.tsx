import React, { useState } from 'react';
import { AIInsight } from '../../../types/dashboard';
import '../../../styles/dashboard/SmartInsightsPanel.css';

interface SmartInsightsPanelProps {
  insights: AIInsight[];
  isLoading: boolean;
  onActionClick: (path: string, insightId: string) => void;
  onDismiss?: (insightId: string) => void;
}

const SmartInsightsPanel: React.FC<SmartInsightsPanelProps> = ({
  insights,
  isLoading,
  onActionClick,
  onDismiss
}) => {
  const [expandedInsight, setExpandedInsight] = useState<string | null>(null);

  if (isLoading) {
    return (
      <div className="widget-loading-state">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Analizando datos...</p>
      </div>
    );
  }

  if (insights.length === 0) {
    return (
      <div className="widget-empty-state">
        <i className="fas fa-lightbulb"></i>
        <p>No hay insights disponibles en este momento. Añade más activos a tu portafolio o radar para recibir recomendaciones personalizadas.</p>
      </div>
    );
  }

  const toggleInsight = (insightId: string) => {
    if (expandedInsight === insightId) {
      setExpandedInsight(null);
    } else {
      setExpandedInsight(insightId);
    }
  };

  const handleDismiss = (e: React.MouseEvent, insightId: string) => {
    e.stopPropagation();
    if (onDismiss) {
      onDismiss(insightId);
    }
  };

  return (
    <div className="insights-list">
      {insights.map((insight) => (
        <div 
          key={insight.id}
          className={`insight-card ${insight.type} ${expandedInsight === insight.id ? 'expanded' : ''}`}
          onClick={() => toggleInsight(insight.id)}
        >
          <div className="insight-header">
            <div className="insight-icon">
              <i className={`fas ${getInsightIcon(insight.type)}`}></i>
            </div>
            <div className="insight-title">
              <h4>{insight.title}</h4>
              <span className="insight-source">{getSourceLabel(insight.source)}</span>
            </div>
            {onDismiss && (
              <button 
                className="dismiss-button"
                onClick={(e) => handleDismiss(e, insight.id)}
                aria-label="Descartar insight"
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
          
          <div className="insight-body">
            <p>{insight.description}</p>
            
            {insight.assetIds && insight.assetIds.length > 0 && (
              <div className="related-assets">
                <span className="related-label">Activos relacionados:</span>
                <div className="asset-tags">
                  {insight.assetIds.map((assetId, index) => (
                    <span key={index} className="asset-tag">{assetId}</span>
                  ))}
                </div>
              </div>
            )}
            
            <div className="insight-footer">
              <button 
                className="insight-action-button"
                onClick={(e) => {
                  e.stopPropagation();
                  onActionClick(insight.actionPath, insight.id);
                }}
              >
                {insight.action}
                <i className="fas fa-arrow-right"></i>
              </button>
              
              <span className="insight-timestamp">
                {formatTimestamp(insight.timestamp)}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Funciones auxiliares
const getInsightIcon = (type: string): string => {
  switch (type) {
    case 'success':
      return 'fa-check-circle';
    case 'warning':
      return 'fa-exclamation-triangle';
    case 'info':
      return 'fa-info-circle';
    case 'opportunity':
      return 'fa-chart-line';
    case 'caution':
      return 'fa-shield-alt';
    default:
      return 'fa-lightbulb';
  }
};

const getSourceLabel = (source: string): string => {
  switch (source) {
    case 'portfolio':
      return 'Portafolio';
    case 'market':
      return 'Mercado';
    case 'news':
      return 'Noticias';
    case 'technical':
      return 'Análisis Técnico';
    case 'onchain':
      return 'Datos On-Chain';
    default:
      return 'Sistema';
  }
};

const formatTimestamp = (timestamp: Date): string => {
  const now = new Date();
  const diff = now.getTime() - new Date(timestamp).getTime();
  
  // Menos de un minuto
  if (diff < 60000) {
    return 'Ahora mismo';
  }
  
  // Menos de una hora
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `Hace ${minutes} ${minutes === 1 ? 'minuto' : 'minutos'}`;
  }
  
  // Menos de un día
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `Hace ${hours} ${hours === 1 ? 'hora' : 'horas'}`;
  }
  
  // Menos de una semana
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000);
    return `Hace ${days} ${days === 1 ? 'día' : 'días'}`;
  }
  
  // Formato de fecha normal
  return new Date(timestamp).toLocaleDateString();
};

export default SmartInsightsPanel;
