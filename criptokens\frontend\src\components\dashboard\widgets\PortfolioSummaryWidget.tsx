import React from 'react';
import { PortfolioStats } from '../../../types/dashboard';
import '../../../styles/dashboard/PortfolioSummaryWidget.css';

interface PortfolioSummaryWidgetProps {
  portfolioStats: PortfolioStats;
  isLoading: boolean;
  onClick: () => void;
}

const PortfolioSummaryWidget: React.FC<PortfolioSummaryWidgetProps> = ({
  portfolioStats,
  isLoading,
  onClick
}) => {
  // Formatear números para mostrar
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  if (isLoading) {
    return (
      <div className="widget-loading-state">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Cargando portafolio...</p>
      </div>
    );
  }

  // Si no hay activos en el portafolio
  if (portfolioStats.assetCount === 0) {
    return (
      <div className="widget-empty-state">
        <i className="fas fa-wallet"></i>
        <p>No tienes activos en tu portafolio.</p>
        <button onClick={onClick}>
          Añadir Activos
        </button>
      </div>
    );
  }

  return (
    <div className="portfolio-summary">
      <div className="portfolio-total">
        <div className="total-label">Valor Total</div>
        <div className="total-value">
          {formatCurrency(portfolioStats.totalValue)}
          <span className={`change ${portfolioStats.totalProfitLoss >= 0 ? 'positive' : 'negative'}`}>
            {portfolioStats.totalProfitLoss >= 0 ? '+' : ''}{portfolioStats.totalProfitLossPercentage.toFixed(2)}%
          </span>
        </div>
      </div>
      
      <div className="portfolio-distribution">
        {portfolioStats.assetAllocation.slice(0, 3).map((allocation, index) => (
          <div key={index} className="portfolio-asset">
            <div className="asset-icon" style={{ backgroundColor: getCategoryColor(allocation.category, index) }}>
              {allocation.category.substring(0, 1).toUpperCase()}
            </div>
            <div className="asset-details">
              <div className="asset-name">{allocation.category}</div>
              <div className="asset-value">{formatCurrency(allocation.value)}</div>
            </div>
            <div className="asset-percentage">
              {allocation.percentage.toFixed(1)}%
            </div>
          </div>
        ))}
        
        {portfolioStats.assetAllocation.length > 3 && (
          <div className="more-assets">
            +{portfolioStats.assetAllocation.length - 3} más
          </div>
        )}
      </div>
      
      <div className="portfolio-risk">
        <span className="risk-label">Nivel de Riesgo:</span>
        <div className="risk-meter">
          <div className="risk-bar">
            <div 
              className="risk-indicator"
              style={{ width: `${(portfolioStats.riskScore / 10) * 100}%` }}
            ></div>
          </div>
          <span className="risk-value">{portfolioStats.riskScore}/10</span>
        </div>
      </div>
    </div>
  );
};

// Función para generar colores para categorías
const getCategoryColor = (category: string, index: number): string => {
  const colors = [
    '#4361ee', '#3a0ca3', '#7209b7', '#f72585', '#4cc9f0',
    '#4895ef', '#560bad', '#b5179e', '#f15bb5', '#00bbf9'
  ];
  
  // Asignar colores específicos a categorías comunes
  switch (category.toLowerCase()) {
    case 'bitcoin':
    case 'btc':
      return '#f7931a';
    case 'ethereum':
    case 'eth':
      return '#627eea';
    case 'defi':
      return '#7209b7';
    case 'layer1':
      return '#4361ee';
    case 'stablecoin':
      return '#00bbf9';
    default:
      // Usar el índice para seleccionar un color del array
      return colors[index % colors.length];
  }
};

export default PortfolioSummaryWidget;
