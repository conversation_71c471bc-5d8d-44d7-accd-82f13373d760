{"id": "blockchain-basics", "title": "Fundamentos de Blockchain", "description": "Aprende los conceptos básicos de la tecnología blockchain y cómo funciona detrás de las criptomonedas.", "level": "Principiante", "duration": "4 semanas", "image": "/images/courses/blockchain-basics.jpg", "instructor": {"name": "<PERSON>", "bio": "Experto en blockchain con más de 5 años de experiencia en el sector.", "avatar": "/images/instructors/carlos-martinez.jpg"}, "modules": [{"id": "module-1", "title": "Introducción a Blockchain", "description": "Conoce qué es blockchain y por qué es importante.", "lessons": [{"id": "lesson-1-1", "title": "¿Qué es Blockchain?", "duration": "15 minutos", "content": "# ¿Qué es Blockchain?\n\nBlockchain es una tecnología de registro distribuido que permite mantener una base de datos compartida entre múltiples participantes sin necesidad de una autoridad central.\n\n## Características principales\n\n- **Descentralización**: No hay una entidad central que controle la información.\n- **Inmutabilidad**: Una vez registrada la información, es extremadamente difícil modificarla.\n- **Transparencia**: Todos los participantes pueden ver las transacciones.\n- **Seguridad**: Utiliza criptografía avanzada para proteger la información.\n\n## ¿Cómo funciona?\n\nLa información se agrupa en bloques que se enlazan entre sí formando una cadena. Cada bloque contiene:\n\n1. Datos de las transacciones\n2. Un timestamp (marca de tiempo)\n3. Un hash del bloque anterior\n4. Un hash propio\n\nEsta estructura hace que sea prácticamente imposible alterar la información sin que se detecte."}, {"id": "lesson-1-2", "title": "Historia de Blockchain", "duration": "20 minutos", "content": "# Historia de Blockchain\n\n## Orígenes\n\nAunque la tecnología blockchain se popularizó con Bitcoin en 2009, sus fundamentos teóricos se remontan a décadas anteriores:\n\n- **1991**: <PERSON> y <PERSON><PERSON> describen una cadena de bloques asegurada criptográficamente.\n- **1998**: <PERSON> \"Bit Gold\", un mecanismo para una moneda digital descentralizada.\n- **2008**: Una persona o grupo bajo el seudónimo de Satoshi Nakamoto publica el whitepaper de Bitcoin.\n- **2009**: Se lanza la red Bitcoin, implementando la primera blockchain funcional.\n\n## Evolución\n\n- **2013-2014**: Surgen proyectos como Ethereum que expanden las capacidades de blockchain más allá de las transacciones financieras.\n- **2015-2017**: <PERSON><PERSON> <PERSON> las ICOs (Initial Coin Offerings) y expansión del ecosistema blockchain.\n- **2018-2020**: Maduración del sector, enfoque en escalabilidad y casos de uso empresariales.\n- **2020-presente**: Crecimiento de DeFi (Finanzas Descentralizadas), NFTs y blockchain de tercera generación."}, {"id": "lesson-1-3", "title": "Tipos de Blockchain", "duration": "25 minutos", "content": "# Tipos de Blockchain\n\nLas blockchains se pueden clasificar en diferentes categorías según su accesibilidad y control:\n\n## Blockchain Públicas\n\n- **Acceso**: Cualquiera puede participar en la red, leer transacciones y enviar transacciones válidas.\n- **Consenso**: Generalmente utilizan mecanismos como Proof of Work o Proof of Stake.\n- **Ejemplos**: Bitcoin, Ethereum, Litecoin.\n- **Ventajas**: Máxima descentralización y transparencia.\n- **Desventajas**: Menor velocidad de transacción y mayor consumo energético (en PoW).\n\n## Blockchain Privadas\n\n- **Acceso**: Solo participantes autorizados pueden unirse a la red.\n- **Consenso**: Mecanismos más eficientes como PBFT (Practical Byzantine Fault Tolerance).\n- **Ejemplos**: Hyperledger Fabric, <PERSON>rda.\n- **Ventajas**: Mayor velocidad, eficiencia y control.\n- **Desventajas**: Menor descentralización y transparencia.\n\n## Blockchain Híbridas o de Consorcio\n\n- **Acceso**: Controlado por un grupo de organizaciones en lugar de una sola entidad.\n- **Consenso**: Variedad de mecanismos según las necesidades.\n- **Ejemplos**: Energy Web Chain, Dragonchain.\n- **Ventajas**: Balance entre privacidad y transparencia.\n- **Desventajas**: Complejidad en la gobernanza."}], "quiz": {"title": "Quiz: Introducción a Blockchain", "questions": [{"question": "¿Cuál de las siguientes NO es una característica de blockchain?", "options": ["Descentralización", "Inmutabilidad", "Centralización", "Transparencia"], "correctAnswer": 2}, {"question": "¿En qué año se lanzó la red Bitcoin?", "options": ["2007", "2008", "2009", "2010"], "correctAnswer": 2}, {"question": "¿Qué tipo de blockchain ofrece mayor descentraliza<PERSON>?", "options": ["Blockchain privada", "Blockchain pública", "Blockchain de consorcio", "Blockchain híbrida"], "correctAnswer": 1}], "passingScore": 70}}, {"id": "module-2", "title": "Criptografía y Seguridad", "description": "Aprende los fundamentos de criptografía que hacen posible la seguridad en blockchain.", "lessons": [{"id": "lesson-2-1", "title": "Fundamentos de Criptografía", "duration": "30 minutos", "content": "# Fundamentos de Criptografía\n\nLa criptografía es la ciencia de proteger información mediante técnicas que la hacen ininteligible para aquellos que no están autorizados a acceder a ella.\n\n## Conceptos Básicos\n\n### Cifrado\n\nEl proceso de convertir información legible (texto plano) en formato ilegible (texto cifrado) mediante un algoritmo y una clave.\n\n### Descifrado\n\nEl proceso inverso que convierte el texto cifrado de nuevo en texto plano utilizando la clave adecuada.\n\n## Tipos de Criptografía\n\n### Criptografía Simétrica\n\n- Utiliza la misma clave para cifrar y descifrar.\n- Ejemplos: AES, DES, 3DES.\n- Ventajas: Rápida y eficiente.\n- Desventajas: Problema de distribución segura de claves.\n\n### Criptografía Asimétrica (o de Clave Pública)\n\n- Utiliza un par de claves: pública y privada.\n- La clave pública se puede compartir, la privada debe mantenerse secreta.\n- Ejemplos: RSA, ECC, DSA.\n- Ventajas: Resuelve el problema de distribución de claves.\n- Desventajas: Más lenta que la criptografía simétrica.\n\n### Funciones Hash\n\n- Convierten datos de cualquier tamaño en una cadena de longitud fija.\n- Son unidireccionales: no se puede obtener la entrada original a partir del hash.\n- Ejemplos: SHA-256, SHA-3, RIPEMD-160.\n- Usos: Verificación de integridad, almacenamiento seguro de contraseñas."}, {"id": "lesson-2-2", "title": "Criptografía en Blockchain", "duration": "25 minutos", "content": "# Criptografía en Blockchain\n\nLa tecnología blockchain utiliza varios conceptos criptográficos para garantizar su seguridad e integridad.\n\n## Funciones Hash en Blockchain\n\n- **Integridad de bloques**: Cada bloque contiene un hash que depende de su contenido y del hash del bloque anterior.\n- **Prueba de trabajo (PoW)**: Los mineros deben encontrar un hash que cumpla ciertos requisitos (como tener un número específico de ceros iniciales).\n- **Direcciones**: Las direcciones de las carteras son versiones modificadas de claves públicas hasheadas.\n\n## Criptografía de Clave Pública en Blockchain\n\n- **Identidad**: Los usuarios se identifican mediante pares de claves (pública y privada).\n- **Propiedad**: La clave privada demuestra la propiedad de los activos asociados a una dirección.\n- **Firmas digitales**: Cada transacción es firmada con la clave privada del remitente.\n\n## Firmas Digitales\n\n1. El remitente crea un hash de la transacción.\n2. El hash se cifra con la clave privada del remitente, creando una firma digital.\n3. Cualquiera puede verificar la firma utilizando la clave pública del remitente.\n4. Si la transacción se modifica, el hash cambiará y la verificación fallará.\n\n## Árboles de Merkle\n\n- Estructura de datos que permite verificar eficientemente la integridad de grandes conjuntos de datos.\n- En blockchain, se utilizan para resumir todas las transacciones en un bloque.\n- Permiten verificar si una transacción específica está incluida en un bloque sin necesidad de descargar todo el bloque."}, {"id": "lesson-2-3", "title": "Seguridad y Vulnerabilidades", "duration": "20 minutos", "content": "# Seguridad y Vulnerabilidades en Blockchain\n\nAunque blockchain es inherentemente segura por diseño, existen vulnerabilidades y consideraciones de seguridad importantes.\n\n## Fortalezas de Seguridad\n\n- **Inmutabilidad**: Una vez que los datos se registran, es extremadamente difícil modificarlos.\n- **Descentralización**: No hay un punto único de fallo.\n- **Criptografía**: Utiliza algoritmos criptográficos robustos.\n- **Consenso**: Múltiples nodos deben verificar y acordar cada transacción.\n\n## Vulnerabilidades y Ataques\n\n### Ataque del 51%\n\n- Si una entidad controla más del 50% del poder computacional de la red, puede manipular el registro de transacciones.\n- Más probable en redes pequeñas con menor poder computacional total.\n\n### Vulnerabilidades en Contratos Inteligentes\n\n- Errores de programación pueden llevar a comportamientos no deseados.\n- Ejemplo: El hack de The DAO en Ethereum (2016) que resultó en la pérdida de millones de dólares.\n\n### Ataques Sybil\n\n- Un atacante crea múltiples identidades falsas para ganar influencia desproporcionada.\n- Los mecanismos de consenso como PoW y PoS ayudan a mitigar este riesgo.\n\n### Ataques de Canal Lateral\n\n- Explotan información obtenida de la implementación física del sistema (tiempo de ejecución, consumo de energía, etc.).\n- Pueden comprometer claves privadas si no se implementan correctamente.\n\n## Mejores Prácticas de Seguridad\n\n1. **Gestión segura de claves privadas**: Utilizar hardware wallets o soluciones de almacenamiento en frío.\n2. **Auditorías de código**: Especialmente importante para contratos inteligentes.\n3. **Actualizaciones regulares**: Mantener el software actualizado con los últimos parches de seguridad.\n4. **Diversificación**: No mantener todos los activos en una sola wallet o plataforma."}], "quiz": {"title": "Quiz: Criptografía y Seguridad", "questions": [{"question": "¿Qué tipo de criptografía utiliza un par de claves (pública y privada)?", "options": ["Criptografía simétrica", "Criptografía asimétrica", "Funciones hash", "Criptografía cuántica"], "correctAnswer": 1}, {"question": "¿Qué función cumplen las firmas digitales en blockchain?", "options": ["Cifrar las transacciones", "Verificar la identidad del remitente", "Acelerar el procesamiento de transacciones", "Reducir el tamaño de los bloques"], "correctAnswer": 1}, {"question": "¿Qué es un ataque del 51% en blockchain?", "options": ["Cuando el 51% de los usuarios pierden sus claves privadas", "Cuando una entidad controla más del 51% del poder computacional de la red", "Cuando el 51% de los nodos están desconectados", "Cuando el 51% de las transacciones son fraudulentas"], "correctAnswer": 1}], "passingScore": 70}}], "requirements": ["Conocimientos básicos de informática", "Interés en tecnología blockchain y criptomonedas", "No se requieren conocimientos previos de programación"], "objectives": ["Comprender los fundamentos de la tecnología blockchain", "Entender cómo funciona la criptografía en blockchain", "Conocer los diferentes tipos de blockchain y sus aplicaciones", "Identificar las principales vulnerabilidades y consideraciones de seguridad"], "tags": ["blockchain", "crip<PERSON><PERSON><PERSON>", "tecnología", "criptografía", "seguridad"], "price": {"amount": 0, "currency": "EUR"}, "certificate": true, "featured": true, "createdAt": "2023-01-15T00:00:00.000Z", "updatedAt": "2023-04-20T00:00:00.000Z"}