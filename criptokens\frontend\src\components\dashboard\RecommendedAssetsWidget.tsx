import React, { useState } from 'react';
import { RecommendedAsset } from '../../types/dashboard';
import '../../styles/dashboard/RecommendedAssetsWidget.css';

interface RecommendedAssetsWidgetProps {
  assets: RecommendedAsset[];
  isLoading: boolean;
  onAssetClick: (assetId: string) => void;
}

const RecommendedAssetsWidget: React.FC<RecommendedAssetsWidgetProps> = ({
  assets,
  isLoading,
  onAssetClick
}) => {
  const [expandedAsset, setExpandedAsset] = useState<string | null>(null);

  if (isLoading) {
    return (
      <div className="recommended-assets-widget loading" data-testid="recommended-assets-loading">
        <div className="widget-header">
          <h3>Recomendaciones Personalizadas</h3>
          <div className="ai-badge">IA</div>
        </div>
        <div className="widget-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  if (assets.length === 0) {
    return (
      <div className="recommended-assets-widget empty" data-testid="recommended-assets-empty">
        <div className="widget-header">
          <h3>Recomendaciones Personalizadas</h3>
          <div className="ai-badge">IA</div>
        </div>
        <div className="widget-content">
          <div className="empty-recommendations">
            <i className="fas fa-search"></i>
            <p>No hay recomendaciones disponibles en este momento. Completa tu perfil de inversor para recibir sugerencias personalizadas.</p>
          </div>
        </div>
      </div>
    );
  }

  const toggleAssetDetails = (assetId: string) => {
    if (expandedAsset === assetId) {
      setExpandedAsset(null);
    } else {
      setExpandedAsset(assetId);
    }
  };

  return (
    <div className="recommended-assets-widget" data-testid="recommended-assets-widget">
      <div className="widget-header">
        <h3>Recomendaciones Personalizadas</h3>
        <div className="ai-badge">IA</div>
      </div>
      <div className="widget-content">
        <div className="assets-list">
          {assets.map((asset) => (
            <div
              key={asset.id}
              className={`asset-card ${expandedAsset === asset.id ? 'expanded' : ''}`}
              data-testid={`asset-${asset.id}`}
            >
              <div
                className="asset-summary"
                onClick={() => toggleAssetDetails(asset.id)}
              >
                <div className="asset-icon">
                  <img src={asset.image} alt={asset.name} />
                </div>
                <div className="asset-info">
                  <div className="asset-name-container">
                    <h4 className="asset-name">{asset.name}</h4>
                    <span className="asset-symbol">{asset.symbol.toUpperCase()}</span>
                  </div>
                  <div className="asset-price-container">
                    <span className="asset-price">${asset.currentPrice.toFixed(2)}</span>
                    <span className={`asset-change ${asset.priceChangePercentage24h >= 0 ? 'positive' : 'negative'}`}>
                      {asset.priceChangePercentage24h >= 0 ? '+' : ''}
                      {asset.priceChangePercentage24h.toFixed(2)}%
                    </span>
                  </div>
                </div>
                <div className="asset-score">
                  <div
                    className="score-indicator"
                    style={{
                      background: `conic-gradient(var(--color-primary) ${asset.recommendationScore * 10}%, transparent 0)`
                    }}
                  >
                    <span>{asset.recommendationScore}</span>
                  </div>
                </div>
                <div className="asset-expand-icon">
                  <i className={`fas fa-chevron-${expandedAsset === asset.id ? 'up' : 'down'}`}></i>
                </div>
              </div>

              {expandedAsset === asset.id && (
                <div className="asset-details">
                  <div className="asset-metrics">
                    <div className="metric">
                      <span className="metric-label">Cap. de Mercado</span>
                      <span className="metric-value">${formatNumber(asset.marketCap)}</span>
                    </div>
                    <div className="metric">
                      <span className="metric-label">Volumen 24h</span>
                      <span className="metric-value">${formatNumber(asset.volume24h)}</span>
                    </div>
                    <div className="metric">
                      <span className="metric-label">Nivel de Riesgo</span>
                      <span className={`metric-value risk-${asset.riskLevel}`}>
                        {getRiskLabel(asset.riskLevel)}
                      </span>
                    </div>
                  </div>

                  <div className="asset-tags">
                    {asset.tags.map((tag, index) => (
                      <span key={index} className="asset-tag">{tag}</span>
                    ))}
                  </div>

                  <div className="asset-reason">
                    <h5>Por qué te lo recomendamos:</h5>
                    <p>{asset.recommendationReason}</p>
                  </div>

                  <div className="asset-actions">
                    <button
                      className="view-asset-button"
                      onClick={() => onAssetClick(asset.id)}
                    >
                      Ver Detalles
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Funciones auxiliares
const formatNumber = (num: number): string => {
  if (num >= 1_000_000_000) {
    return `${(num / 1_000_000_000).toFixed(2)}B`;
  } else if (num >= 1_000_000) {
    return `${(num / 1_000_000).toFixed(2)}M`;
  } else if (num >= 1_000) {
    return `${(num / 1_000).toFixed(2)}K`;
  }
  return num.toFixed(2);
};

const getRiskLabel = (risk: 'low' | 'medium' | 'high'): string => {
  switch (risk) {
    case 'low':
      return 'Bajo';
    case 'medium':
      return 'Medio';
    case 'high':
      return 'Alto';
    default:
      return 'Desconocido';
  }
};

export default RecommendedAssetsWidget;
