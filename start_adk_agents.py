"""
<PERSON>ript to start ADK agents for Criptokens
"""
import os
import sys
import subprocess
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv("adk_agents/.env")

def print_colored(message, color):
    """Print colored message."""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "magenta": "\033[95m",
        "cyan": "\033[96m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, '')}{message}{colors['reset']}")

def check_dependencies():
    """Check if all required dependencies are installed."""
    try:
        import google.adk
        print_colored("✓ Google ADK is installed", "green")
    except ImportError:
        print_colored("✗ Google ADK is not installed. Please run: pip install google-adk", "red")
        return False
    
    try:
        import aiohttp
        print_colored("✓ aiohttp is installed", "green")
    except ImportError:
        print_colored("✗ aiohttp is not installed. Please run: pip install aiohttp", "red")
        return False
    
    try:
        import numpy
        print_colored("✓ numpy is installed", "green")
    except ImportError:
        print_colored("✗ numpy is not installed. Please run: pip install numpy", "red")
        return False
    
    return True

def check_api_keys():
    """Check if all required API keys are set."""
    required_keys = {
        "COINMARKETCAP_API_KEY": os.getenv("COINMARKETCAP_API_KEY"),
        "BRAVE_API_KEY": os.getenv("BRAVE_API_KEY"),
        "ETHERSCAN_API_KEY": os.getenv("ETHERSCAN_API_KEY"),
        "OPENROUTER_API_KEY": os.getenv("OPENROUTER_API_KEY")
    }
    
    all_keys_present = True
    for key_name, key_value in required_keys.items():
        if not key_value:
            print_colored(f"✗ {key_name} is not set in .env file", "red")
            all_keys_present = False
        else:
            print_colored(f"✓ {key_name} is set", "green")
    
    # Check for either Google AI Studio or Vertex AI configuration
    if not os.getenv("GOOGLE_API_KEY") and not os.getenv("GOOGLE_GENAI_USE_VERTEXAI"):
        print_colored("✗ Neither GOOGLE_API_KEY nor GOOGLE_GENAI_USE_VERTEXAI is set", "red")
        print_colored("  You must configure either Google AI Studio or Vertex AI", "yellow")
        all_keys_present = False
    elif os.getenv("GOOGLE_API_KEY"):
        print_colored("✓ GOOGLE_API_KEY is set (using Google AI Studio)", "green")
    elif os.getenv("GOOGLE_GENAI_USE_VERTEXAI"):
        if not os.getenv("GOOGLE_CLOUD_PROJECT") or not os.getenv("GOOGLE_CLOUD_LOCATION"):
            print_colored("✗ GOOGLE_CLOUD_PROJECT or GOOGLE_CLOUD_LOCATION is not set", "red")
            all_keys_present = False
        else:
            print_colored("✓ Vertex AI configuration is set", "green")
    
    return all_keys_present

def start_adk_web():
    """Start ADK Web UI."""
    print_colored("\nStarting ADK Web UI...", "cyan")
    
    # Change to the directory containing the ADK agents
    os.chdir("C:/Users/<USER>/OneDrive/Escritorio/Criptokens")
    
    # Start ADK Web UI
    try:
        subprocess.Popen(["adk", "web"], cwd="C:/Users/<USER>/OneDrive/Escritorio/Criptokens")
        print_colored("ADK Web UI started successfully!", "green")
        print_colored("Open http://localhost:8000 in your browser", "yellow")
    except Exception as e:
        print_colored(f"Error starting ADK Web UI: {e}", "red")
        return False
    
    return True

def main():
    """Main function."""
    print_colored("=== Criptokens ADK Agents Starter ===", "magenta")
    
    # Check dependencies
    print_colored("\nChecking dependencies...", "cyan")
    if not check_dependencies():
        print_colored("\nPlease install the missing dependencies and try again.", "red")
        return
    
    # Check API keys
    print_colored("\nChecking API keys...", "cyan")
    if not check_api_keys():
        print_colored("\nPlease set the missing API keys in adk_agents/.env file and try again.", "red")
        return
    
    # Start ADK Web UI
    if not start_adk_web():
        return
    
    print_colored("\n=== All services started successfully! ===", "magenta")
    print_colored("\nAvailable agents:", "cyan")
    print_colored("- technical_agent: Technical analysis for cryptocurrencies", "yellow")
    print_colored("- sentiment_agent: Sentiment analysis for cryptocurrencies", "yellow")
    print_colored("- onchain_agent: On-chain analysis for cryptocurrencies", "yellow")
    print_colored("- guru_agent: Comprehensive cryptocurrency analysis and predictions", "yellow")
    
    print_colored("\nPress Ctrl+C to stop all services.", "cyan")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print_colored("\nStopping all services...", "cyan")
        print_colored("All services stopped successfully!", "green")

if __name__ == "__main__":
    main()
