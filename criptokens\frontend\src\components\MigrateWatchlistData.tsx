import React, { useState } from 'react';
import { useAuth } from '../context/NewAuthContext';
import { migrateUserWatchlistToRadar, hasRadarData } from '../utils/migrateWatchlistToRadar';

const MigrateWatchlistData: React.FC = () => {
  const { currentUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [migrationStatus, setMigrationStatus] = useState<'idle' | 'success' | 'error' | 'already-migrated'>('idle');
  const [showComponent, setShowComponent] = useState(true);

  const handleMigration = async () => {
    if (!currentUser) return;

    setIsLoading(true);
    try {
      // Verificar si el usuario ya tiene datos en la colección de radar
      const hasData = await hasRadarData(currentUser.uid);
      
      if (hasData) {
        setMigrationStatus('already-migrated');
      } else {
        // Migrar los datos
        const success = await migrateUserWatchlistToRadar(currentUser.uid);
        
        if (success) {
          setMigrationStatus('success');
        } else {
          setMigrationStatus('error');
        }
      }
    } catch (error) {
      console.error('Error durante la migración:', error);
      setMigrationStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDismiss = () => {
    setShowComponent(false);
  };

  if (!showComponent || !currentUser) {
    return null;
  }

  return (
    <div className="migration-banner">
      <div className="migration-content">
        <h3>¡Hemos mejorado tu experiencia!</h3>
        <p>
          Hemos renombrado "Watchlist" a "Mi Radar Cripto" y añadido nuevas funcionalidades.
          {migrationStatus === 'idle' && 'Para continuar usando tus datos, necesitamos migrarlos al nuevo formato.'}
          {migrationStatus === 'success' && '¡Tus datos han sido migrados con éxito!'}
          {migrationStatus === 'error' && 'Hubo un error durante la migración. Por favor, intenta de nuevo.'}
          {migrationStatus === 'already-migrated' && 'Tus datos ya han sido migrados anteriormente.'}
        </p>
        
        {migrationStatus === 'idle' && (
          <button 
            className="migrate-button" 
            onClick={handleMigration}
            disabled={isLoading}
          >
            {isLoading ? 'Migrando datos...' : 'Migrar mis datos'}
          </button>
        )}
        
        {(migrationStatus === 'success' || migrationStatus === 'already-migrated') && (
          <button className="dismiss-button" onClick={handleDismiss}>
            Entendido
          </button>
        )}
        
        {migrationStatus === 'error' && (
          <div className="error-actions">
            <button 
              className="retry-button" 
              onClick={handleMigration}
              disabled={isLoading}
            >
              Intentar de nuevo
            </button>
            <button className="dismiss-button" onClick={handleDismiss}>
              Cerrar
            </button>
          </div>
        )}
      </div>
      <button className="close-banner" onClick={handleDismiss}>×</button>
    </div>
  );
};

export default MigrateWatchlistData;
