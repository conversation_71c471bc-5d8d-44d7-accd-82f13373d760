* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

#root {
  height: 100%;
  width: 100%;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.app-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  font-size: 1.25rem;
  color: #666;
  background-color: #f5f5f5;
}

/* Header Styles */
.app-header {
  background-color: #1a1a2e;
  color: white;
  padding: 0.75rem 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1280px;
  margin: 0 auto;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #0084ff, #6c63ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-nav {
  display: flex;
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-links li {
  margin: 0 0.5rem;
}

.nav-links a {
  color: #e5e5ea;
  text-decoration: none;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav-links a:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-links a.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.15);
}

.header-actions {
  display: flex;
  align-items: center;
}

.search-container {
  position: relative;
  margin-right: 1rem;
}

.search-container input {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 20px;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  color: white;
  width: 200px;
  transition: all 0.3s ease;
}

.search-container input:focus {
  background-color: rgba(255, 255, 255, 0.15);
  outline: none;
  width: 250px;
}

.search-container input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0;
}

.login-button {
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.login-button:hover {
  background-color: #0077e6;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 1rem;
}

.mobile-menu-toggle span {
  display: block;
  height: 3px;
  width: 100%;
  background-color: white;
  border-radius: 3px;
}

/* Dashboard Styles */
.dashboard {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1.5rem;
}

.dashboard-tabs {
  display: flex;
  margin-bottom: 1.5rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #333;
}

.tab-button.active {
  color: #0084ff;
  border-bottom-color: #0084ff;
}

.dashboard-content {
  flex: 1;
  overflow: auto;
}

.dashboard-main-content {
  display: flex;
  gap: 1.5rem;
}

.main-section {
  flex: 1;
}

.side-section {
  width: 300px;
}

.chat-widget {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-widget h3 {
  font-size: 1.125rem;
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
}

.chat-widget p {
  color: #666;
  margin-bottom: 1.25rem;
  font-size: 0.9375rem;
}

.open-chat-button {
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.open-chat-button:hover {
  background-color: #0077e6;
}

.chat-fullscreen {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Market Summary Styles */
.market-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.market-summary h2 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.market-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.metric {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.metric-label {
  font-size: 0.875rem;
  color: #666;
  margin-right: 0.5rem;
}

.metric-value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-right: 0.5rem;
}

.metric-change {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.metric-change.positive {
  color: #00C853;
  background-color: rgba(0, 200, 83, 0.1);
}

.metric-change.negative {
  color: #FF3D00;
  background-color: rgba(255, 61, 0, 0.1);
}

.fear-index-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 0.5rem;
}

.fear-index-indicator.extreme-fear {
  background-color: #FF3D00;
}

.fear-index-indicator.fear {
  background-color: #FF9100;
}

.fear-index-indicator.neutral {
  background-color: #FFEA00;
}

.fear-index-indicator.greed {
  background-color: #76FF03;
}

.fear-index-indicator.extreme-greed {
  background-color: #00C853;
}

.market-summary.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  color: #666;
  font-style: italic;
}

/* Crypto Table Styles */
.crypto-table-container {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.crypto-table-container h2 {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.crypto-table-wrapper {
  overflow-x: auto;
}

.crypto-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9375rem;
}

.crypto-table th {
  text-align: left;
  padding: 0.75rem 1rem;
  border-bottom: 2px solid #f0f0f0;
  color: #666;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.crypto-table th:hover {
  background-color: #f8f9fa;
}

.crypto-table td {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}

.crypto-table tbody tr:hover {
  background-color: #f8f9fa;
}

.crypto-name-cell {
  min-width: 150px;
}

.crypto-name {
  display: flex;
  flex-direction: column;
}

.crypto-fullname {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.crypto-symbol {
  color: #666;
  font-size: 0.8125rem;
}

.positive {
  color: #00C853;
}

.negative {
  color: #FF3D00;
}

.sparkline-cell {
  width: 100px;
}

.sparkline {
  width: 100%;
  height: 30px;
}

.crypto-table.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #666;
  font-style: italic;
}

/* Chat Interface Styles */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  position: relative;
}

.user-message {
  align-self: flex-end;
  background-color: #0084ff;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.ai-message {
  align-self: flex-start;
  background-color: #e5e5ea;
  color: #333;
  border-bottom-left-radius: 0.25rem;
}

.message.ai-message.streaming::after {
  content: '';
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 8px;
  height: 8px;
  background-color: #0084ff;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
  text-align: center;
}

.chat-options {
  display: flex;
  justify-content: flex-end;
  padding: 0.5rem 1rem;
  background-color: #f8f8f8;
  border-top: 1px solid #e5e5ea;
}

.streaming-toggle {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #666;
  cursor: pointer;
}

.streaming-toggle input {
  margin-right: 0.5rem;
}

.chat-input {
  display: flex;
  padding: 1rem;
  background-color: white;
  border-top: 1px solid #e5e5ea;
}

.chat-input textarea {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 1.5rem;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  min-height: 20px;
  max-height: 120px;
  outline: none;
}

.chat-input button {
  margin-left: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 1.5rem;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
  min-width: 100px;
}

.chat-input button.loading {
  background-color: #66b2ff;
  position: relative;
}

.chat-input button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .dashboard-main-content {
    flex-direction: column;
  }

  .side-section {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .main-nav {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background-color: #1a1a2e;
    height: 0;
    overflow: hidden;
    transition: height 0.3s ease;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }

  .main-nav.active {
    height: auto;
  }

  .nav-links {
    flex-direction: column;
    padding: 1rem;
  }

  .nav-links li {
    margin: 0.5rem 0;
  }

  .search-container {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .market-metrics {
    flex-direction: column;
    gap: 1rem;
  }

  .metric {
    width: 100%;
    justify-content: space-between;
  }

  .crypto-table th:nth-child(5),
  .crypto-table td:nth-child(5),
  .crypto-table th:nth-child(7),
  .crypto-table td:nth-child(7) {
    display: none;
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  position: relative;
}

.message.ai-message.streaming::after {
  content: '';
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 8px;
  height: 8px;
  background-color: #0084ff;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

.user-message {
  align-self: flex-end;
  background-color: #0084ff;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.ai-message {
  align-self: flex-start;
  background-color: #e5e5ea;
  color: #333;
  border-bottom-left-radius: 0.25rem;
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
  text-align: center;
}

.chat-options {
  display: flex;
  justify-content: flex-end;
  padding: 0.5rem 1rem;
  background-color: #f8f8f8;
  border-top: 1px solid #e5e5ea;
}

.streaming-toggle {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #666;
  cursor: pointer;
}

.streaming-toggle input {
  margin-right: 0.5rem;
}

.chat-input {
  display: flex;
  padding: 1rem;
  background-color: white;
  border-top: 1px solid #e5e5ea;
}

.chat-input textarea {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 1.5rem;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  min-height: 20px;
  max-height: 120px;
  outline: none;
}

.chat-input button {
  margin-left: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 1.5rem;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
  min-width: 100px;
}

.chat-input button.loading {
  background-color: #66b2ff;
  position: relative;
}

.chat-input button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
