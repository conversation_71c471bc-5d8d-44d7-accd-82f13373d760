/**
 * Servicio para gestionar la base de conocimiento del Gurú Cripto
 * 
 * Este servicio proporciona funciones para cargar y consultar la base de conocimiento
 * almacenada en archivos Markdown.
 */

const fs = require('fs');
const path = require('path');
const stringSimilarity = require('string-similarity');

// Ruta al archivo de la base de conocimiento
const KNOWLEDGE_BASE_PATH = path.join(__dirname, '../../knowledge_base/Regulacion.md');

// Variable para almacenar los fragmentos de conocimiento
let knowledgeChunks = [];

/**
 * Carga la base de conocimiento desde el archivo Markdown
 * @returns {Array} Array de fragmentos de texto
 */
const loadKnowledgeBase = () => {
  try {
    console.log(`Cargando base de conocimiento desde: ${KNOWLEDGE_BASE_PATH}`);
    
    // Leer el archivo completo
    const content = fs.readFileSync(KNOWLEDGE_BASE_PATH, 'utf8');
    
    // Dividir el contenido en fragmentos por dobles saltos de línea
    // Esto asume que los párrafos o secciones lógicas están separados por líneas en blanco
    const chunks = content.split('\n\n')
      .map(chunk => chunk.trim())
      .filter(chunk => chunk.length > 0);
    
    console.log(`Base de conocimiento cargada: ${chunks.length} fragmentos`);
    
    // Almacenar los fragmentos para su uso posterior
    knowledgeChunks = chunks;
    
    return chunks;
  } catch (error) {
    console.error('Error al cargar la base de conocimiento:', error);
    return [];
  }
};

/**
 * Encuentra documentos relevantes para una pregunta dada
 * @param {string} question - La pregunta del usuario
 * @param {number} maxResults - Número máximo de resultados a devolver
 * @returns {Array} Array de fragmentos de texto relevantes
 */
const findRelevantDocs = (question, maxResults = 2) => {
  try {
    // Si no hay fragmentos cargados, intentar cargarlos
    if (knowledgeChunks.length === 0) {
      loadKnowledgeBase();
    }
    
    // Si sigue sin haber fragmentos, devolver array vacío
    if (knowledgeChunks.length === 0) {
      return [];
    }
    
    console.log(`Buscando documentos relevantes para: "${question}"`);
    
    // Usar string-similarity para encontrar los fragmentos más similares
    const matches = stringSimilarity.findBestMatch(question, knowledgeChunks);
    
    // Filtrar resultados por un umbral mínimo de similitud (0.3)
    const relevantMatches = matches.ratings
      .filter(match => match.rating > 0.3)
      .sort((a, b) => b.rating - a.rating)
      .slice(0, maxResults);
    
    // Obtener los textos de los fragmentos relevantes
    const relevantDocs = relevantMatches.map(match => knowledgeChunks[match.target]);
    
    console.log(`Encontrados ${relevantDocs.length} documentos relevantes`);
    
    return relevantDocs;
  } catch (error) {
    console.error('Error al buscar documentos relevantes:', error);
    return [];
  }
};

// Cargar la base de conocimiento al iniciar el servicio
loadKnowledgeBase();

module.exports = {
  loadKnowledgeBase,
  findRelevantDocs
};
