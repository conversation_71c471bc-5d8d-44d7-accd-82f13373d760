.crypto-market-insights {
  background: rgba(15, 15, 35, 0.7);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(64, 220, 255, 0.2);
  color: white;
  margin-bottom: 30px;
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.insights-header h2 {
  margin: 0;
  font-size: 24px;
  background: linear-gradient(90deg, #00f2ff, #4657ce);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.insights-tabs {
  display: flex;
  gap: 10px;
}

.tab-button {
  background: rgba(10, 10, 26, 0.5);
  border: 1px solid rgba(64, 220, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.tab-button:hover {
  background: rgba(64, 220, 255, 0.1);
  color: white;
}

.tab-button.active {
  background: rgba(64, 220, 255, 0.2);
  border-color: rgba(64, 220, 255, 0.4);
  color: white;
  box-shadow: 0 0 15px rgba(64, 220, 255, 0.2);
}

/* Loading */
.insights-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(64, 220, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00f2ff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.insights-loading p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

/* Market Indicators */
.market-indicators {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.indicator-card {
  background: rgba(10, 10, 26, 0.5);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(64, 220, 255, 0.1);
  transition: all 0.3s;
}

.indicator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(64, 220, 255, 0.3);
}

.indicator-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.indicator-value {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
}

.indicator-change {
  font-size: 14px;
  font-weight: 500;
  padding: 3px 8px;
  border-radius: 4px;
  display: inline-block;
}

.indicator-change.positive {
  background: rgba(0, 200, 83, 0.2);
  color: #00c853;
}

.indicator-change.negative {
  background: rgba(255, 82, 82, 0.2);
  color: #ff5252;
}

.indicator-progress {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin-top: 10px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #00f2ff, #4657ce);
  border-radius: 3px;
}

/* Crypto Table */
.market-summary h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: white;
}

.crypto-table-container {
  overflow-x: auto;
  border-radius: 12px;
  background: rgba(10, 10, 26, 0.3);
  border: 1px solid rgba(64, 220, 255, 0.1);
}

.crypto-table {
  width: 100%;
  border-collapse: collapse;
}

.crypto-table th {
  text-align: left;
  padding: 12px 15px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  border-bottom: 1px solid rgba(64, 220, 255, 0.1);
  font-size: 14px;
}

.crypto-table td {
  padding: 12px 15px;
  border-bottom: 1px solid rgba(64, 220, 255, 0.05);
  font-size: 14px;
}

.crypto-row {
  transition: all 0.2s;
}

.crypto-row:hover {
  background: rgba(64, 220, 255, 0.05);
}

.crypto-name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.crypto-table-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.crypto-name-info {
  display: flex;
  flex-direction: column;
}

.crypto-table-name {
  font-weight: 500;
}

.crypto-table-symbol {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.crypto-change {
  font-weight: 500;
}

.crypto-change.positive {
  color: #00c853;
}

.crypto-change.negative {
  color: #ff5252;
}

/* Trends Tab */
.timeframe-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
}

.timeframe-button {
  background: rgba(10, 10, 26, 0.5);
  border: 1px solid rgba(64, 220, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.timeframe-button:hover {
  background: rgba(64, 220, 255, 0.1);
  color: white;
}

.timeframe-button.active {
  background: rgba(64, 220, 255, 0.2);
  border-color: rgba(64, 220, 255, 0.4);
  color: white;
}

.trend-lines {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.trend-container {
  background: rgba(10, 10, 26, 0.5);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(64, 220, 255, 0.1);
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
}

.trend-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(64, 220, 255, 0.3);
}

.trend-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.trend-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.trend-name {
  font-weight: 500;
  font-size: 16px;
}

.trend-symbol {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 5px;
}

.trend-chart {
  margin: 10px 0;
  display: flex;
  justify-content: center;
}

.trend-svg {
  overflow: visible;
}

.trend-svg path {
  transition: all 0.3s;
}

.trend-container:hover .trend-svg path {
  stroke-width: 3px;
}

.trend-data {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.trend-price {
  font-size: 18px;
  font-weight: 600;
}

.trend-change {
  font-size: 14px;
  font-weight: 500;
  padding: 3px 8px;
  border-radius: 4px;
}

.trend-change.positive {
  background: rgba(0, 200, 83, 0.2);
  color: #00c853;
}

.trend-change.negative {
  background: rgba(255, 82, 82, 0.2);
  color: #ff5252;
}

/* Heatmap Tab */
.heatmap-legend {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.legend-color {
  width: 20px;
  height: 10px;
  border-radius: 3px;
}

.legend-color.positive {
  background: rgba(0, 200, 83, 0.7);
}

.legend-color.negative {
  background: rgba(255, 82, 82, 0.7);
}

.legend-size {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.heatmap-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  padding: 20px;
}

.heatmap-tile {
  position: relative;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  cursor: pointer;
}

.heatmap-tile:hover {
  transform: scale(1.1);
  z-index: 10;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}

.tile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.tile-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-bottom: 5px;
}

.tile-symbol {
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.tile-change {
  font-size: 10px;
  font-weight: 500;
  margin-top: 3px;
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* Tooltip */
.heatmap-tile[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 15, 35, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: pre;
  z-index: 20;
  margin-bottom: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(64, 220, 255, 0.3);
  pointer-events: none;
}

/* Responsive */
@media (max-width: 768px) {
  .insights-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .market-indicators {
    grid-template-columns: 1fr;
  }
  
  .crypto-table th, .crypto-table td {
    padding: 10px 8px;
    font-size: 12px;
  }
  
  .trend-lines {
    grid-template-columns: 1fr;
  }
}
