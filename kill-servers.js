/**
 * Script para terminar todos los procesos que están utilizando los puertos de Criptokens
 */

const { execSync } = require('child_process');
const config = require('./config');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Puertos utilizados por Criptokens
const ports = [
  config.ports.frontend,    // 5173
  config.ports.backend,     // 3001
  config.ports.cryptoMcp,   // 3101
  config.ports.braveSearchMcp, // 3102
  config.ports.playwrightMcp   // 3103
];

log('=== Terminando procesos de Criptokens ===', colors.bright + colors.blue);

// Función para obtener el PID de un proceso que está utilizando un puerto específico
function getPidByPort(port) {
  try {
    // En Windows, usamos netstat para obtener el PID
    const output = execSync(`netstat -ano | findstr :${port} | findstr LISTENING`).toString();
    const lines = output.split('\n').filter(line => line.trim() !== '');
    
    if (lines.length > 0) {
      // El PID está en la última columna
      const pid = lines[0].trim().split(/\s+/).pop();
      return pid;
    }
    
    return null;
  } catch (error) {
    // Si hay un error, probablemente no hay ningún proceso utilizando este puerto
    return null;
  }
}

// Función para terminar un proceso por su PID
function killProcess(pid) {
  try {
    // En Windows, usamos taskkill para terminar el proceso
    execSync(`taskkill /F /PID ${pid}`);
    return true;
  } catch (error) {
    log(`Error al terminar el proceso ${pid}: ${error.message}`, colors.red);
    return false;
  }
}

// Terminar los procesos para cada puerto
let killedCount = 0;
let errorCount = 0;

ports.forEach(port => {
  const pid = getPidByPort(port);
  
  if (pid) {
    log(`Encontrado proceso utilizando el puerto ${port} (PID: ${pid})`, colors.yellow);
    
    if (killProcess(pid)) {
      log(`✅ Terminado proceso en el puerto ${port} (PID: ${pid})`, colors.green);
      killedCount++;
    } else {
      log(`❌ No se pudo terminar el proceso en el puerto ${port} (PID: ${pid})`, colors.red);
      errorCount++;
    }
  } else {
    log(`ℹ️ No se encontró ningún proceso utilizando el puerto ${port}`, colors.blue);
  }
});

// Resumen
log('\n=== Resumen ===', colors.bright + colors.blue);
log(`✅ Procesos terminados: ${killedCount}`, colors.green);
log(`❌ Errores: ${errorCount}`, colors.red);

if (errorCount === 0) {
  log('\nTodos los puertos deberían estar libres ahora. Puedes iniciar la aplicación con:', colors.cyan);
  log('node start-criptokens.js', colors.cyan + colors.bright);
} else {
  log('\nAlgunos procesos no pudieron ser terminados. Intenta reiniciar tu computadora y luego ejecuta:', colors.yellow);
  log('node start-criptokens.js', colors.yellow + colors.bright);
}
