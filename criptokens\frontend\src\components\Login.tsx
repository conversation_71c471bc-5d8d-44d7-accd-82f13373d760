import { useState } from 'react';
import { useAuth } from '../context/NewAuthContext';
import '../styles/Auth.css';

interface LoginProps {
  onRegisterClick: () => void;
  onResetPasswordClick: () => void;
}

const Login = ({ onRegisterClick, onResetPasswordClick }: LoginProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError('Por favor, completa todos los campos.');
      return;
    }

    try {
      setError(null);
      setLoading(true);
      await login(email, password);
      // El redireccionamiento se maneja en el componente principal
    } catch (err: any) {
      console.error('Error al iniciar sesión:', err);

      // Mensajes de error más amigables
      console.log('Error completo:', err);

      if (err.message.includes('user-not-found')) {
        setError('No existe una cuenta con este correo electrónico.');
      } else if (err.message.includes('wrong-password')) {
        setError('Contraseña incorrecta. Por favor, inténtalo de nuevo.');
      } else if (err.message.includes('too-many-requests')) {
        setError('Demasiados intentos fallidos. Por favor, intenta más tarde.');
      } else if (err.message.includes('configuration-not-found')) {
        setError('Error de configuración de Firebase. La autenticación por correo/contraseña no está habilitada.');
      } else {
        setError(`Error al iniciar sesión: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <h2>Iniciar Sesión</h2>

        {error && <div className="auth-error">{error}</div>}

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Correo Electrónico</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Contraseña</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="********"
              required
            />
          </div>

          <button
            type="submit"
            className="auth-button"
            disabled={loading}
          >
            {loading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
          </button>
        </form>

        <div className="auth-links">
          <button
            className="text-link"
            onClick={onResetPasswordClick}
          >
            ¿Olvidaste tu contraseña?
          </button>

          <div className="auth-separator">
            <span>¿No tienes una cuenta?</span>
          </div>

          <button
            className="secondary-button"
            onClick={onRegisterClick}
          >
            Crear Cuenta
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
