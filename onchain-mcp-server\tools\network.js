/**
 * Herramientas MCP relacionadas con la red
 */
const etherscanService = require('../services/etherscan');
const config = require('../config');

module.exports = {
  /**
   * Obtiene el precio actual del gas
   */
  getGasPrice: {
    description: 'Get current gas prices',
    parameters: {
      type: 'object',
      properties: {
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      }
    },
    handler: async ({ chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getGasPrice();
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene información sobre las "ballenas" (grandes tenedores) de un token
   */
  getTokenWhales: {
    description: 'Get information about token whales (large holders)',
    parameters: {
      type: 'object',
      properties: {
        tokenAddress: {
          type: 'string',
          description: 'The token contract address'
        },
        limit: {
          type: 'integer',
          description: 'Maximum number of whales to return',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['tokenAddress']
    },
    handler: async ({ tokenAddress, limit = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        return await etherscanService.getTokenWhales(tokenAddress, limit);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene información sobre las "ballenas" de una criptomoneda específica
   */
  getCryptoWhales: {
    description: 'Get information about cryptocurrency whales (large holders)',
    parameters: {
      type: 'object',
      properties: {
        cryptoId: {
          type: 'string',
          description: 'The cryptocurrency ID (bitcoin, ethereum, etc.)'
        },
        limit: {
          type: 'integer',
          description: 'Maximum number of whales to return',
          default: 10
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['cryptoId']
    },
    handler: async ({ cryptoId, limit = 10, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener la dirección del token para la criptomoneda
        const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
        
        if (!tokenAddress) {
          throw new Error(`Token address not found for ${cryptoId}`);
        }
        
        return await etherscanService.getTokenWhales(tokenAddress, limit);
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Obtiene información sobre carteras populares (exchanges, fondos, etc.)
   */
  getPopularWallets: {
    description: 'Get information about popular wallets (exchanges, funds, etc.)',
    parameters: {
      type: 'object',
      properties: {
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      }
    },
    handler: async ({ chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        const wallets = [];
        
        for (const [name, address] of Object.entries(config.popularWallets)) {
          try {
            const balance = await etherscanService.getWalletBalance(address);
            wallets.push({
              name,
              address,
              ethBalance: balance.balance,
              lastUpdated: balance.timestamp
            });
          } catch (error) {
            console.error(`Error fetching balance for ${name}:`, error);
            wallets.push({
              name,
              address,
              error: error.message
            });
          }
        }
        
        return {
          wallets,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  },
  
  /**
   * Analiza la actividad de las "ballenas" para una criptomoneda
   */
  analyzeWhaleActivity: {
    description: 'Analyze whale activity for a cryptocurrency',
    parameters: {
      type: 'object',
      properties: {
        cryptoId: {
          type: 'string',
          description: 'The cryptocurrency ID (bitcoin, ethereum, etc.)'
        },
        days: {
          type: 'integer',
          description: 'Number of days to analyze',
          default: 7
        },
        chain: {
          type: 'string',
          description: 'The blockchain (ethereum, binance)',
          default: config.defaultChain
        }
      },
      required: ['cryptoId']
    },
    handler: async ({ cryptoId, days = 7, chain = config.defaultChain }) => {
      if (chain === 'ethereum') {
        // Obtener la dirección del token para la criptomoneda
        const tokenAddress = config.tokenAddresses[cryptoId.toLowerCase()];
        
        if (!tokenAddress) {
          throw new Error(`Token address not found for ${cryptoId}`);
        }
        
        // Obtener las ballenas
        const whalesData = await etherscanService.getTokenWhales(tokenAddress, 5);
        const whales = whalesData.whales;
        
        // Analizar la actividad de cada ballena
        const whaleActivity = [];
        
        for (const whale of whales) {
          try {
            // Obtener transferencias de tokens para la ballena
            const transfers = await etherscanService.getTokenTransfers(
              whale.address,
              tokenAddress,
              1,
              50
            );
            
            // Filtrar por fecha (últimos X días)
            const now = Date.now();
            const timeThreshold = now - (days * 24 * 60 * 60 * 1000);
            
            const recentTransfers = transfers.transfers.filter(
              transfer => transfer.timestamp >= timeThreshold
            );
            
            // Calcular actividad
            const sent = recentTransfers
              .filter(transfer => transfer.from.toLowerCase() === whale.address.toLowerCase())
              .reduce((sum, transfer) => sum + transfer.value, 0);
              
            const received = recentTransfers
              .filter(transfer => transfer.to.toLowerCase() === whale.address.toLowerCase())
              .reduce((sum, transfer) => sum + transfer.value, 0);
            
            whaleActivity.push({
              address: whale.address,
              balance: whale.balance,
              percentage: whale.percentage,
              activity: {
                sent,
                received,
                netFlow: received - sent,
                totalTransfers: recentTransfers.length
              }
            });
          } catch (error) {
            console.error(`Error analyzing whale ${whale.address}:`, error);
            whaleActivity.push({
              address: whale.address,
              balance: whale.balance,
              percentage: whale.percentage,
              error: error.message
            });
          }
        }
        
        return {
          cryptoId,
          tokenAddress,
          tokenName: whalesData.tokenName,
          tokenSymbol: whalesData.tokenSymbol,
          period: `Last ${days} days`,
          whaleActivity,
          timestamp: Date.now()
        };
      } else {
        throw new Error(`Chain ${chain} not supported yet`);
      }
    }
  }
};
