{"mcpServers": {"brave": {"command": "node", "args": ["../brave-search-server.js"], "env": {"PORT": "3102", "BRAVE_API_KEY": "BSAccS820UUfffNOAD7yLACz9htlbe9"}}, "playwright": {"command": "node", "args": ["../playwright-mcp-server/dist/server.js"], "env": {"PORT": "3103"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "etherscan": {"command": "node", "args": ["etherscan-mcp-server.js"], "env": {"PORT": "3104", "ETHERSCAN_API_KEY": "**********************************"}}}}