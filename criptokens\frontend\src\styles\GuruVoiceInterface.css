.guru-voice-interface {
  background-color: var(--color-surface-dark);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--widget-shadow);
  transition: all 0.3s ease;
  position: relative;
}

.guru-voice-interface:hover {
  box-shadow: var(--widget-hover-shadow);
}

.voice-interface-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.voice-interface-header h3 {
  font-size: 1.2rem;
  margin: 0;
  color: var(--text-primary);
}

.voice-status {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.status::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status.checking {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.status.checking::before {
  background-color: #ffc107;
  animation: pulse 1.5s infinite;
}

.status.available {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.status.available::before {
  background-color: #28a745;
}

.status.unavailable {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.status.unavailable::before {
  background-color: #dc3545;
}

.status.unavailable {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.api-key-button {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 0.85rem;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.api-key-button:hover {
  background-color: rgba(220, 53, 69, 0.2);
}

.credits {
  font-size: 0.85rem;
  color: var(--text-secondary);
  background-color: rgba(0, 123, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.audio-method {
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: pulse 2s infinite;
}

.audio-method.webspeech {
  color: #fff;
  background-color: rgba(255, 193, 7, 0.8);
}

/* Botón para mostrar/ocultar controles de audio */
.audio-controls-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1rem;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  background-color: rgba(0, 123, 255, 0.1);
  width: 30px;
  height: 30px;
}

.audio-controls-toggle:hover {
  background-color: rgba(0, 123, 255, 0.2);
  color: var(--color-primary);
}

/* Panel de controles de audio */
.audio-controls-panel {
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.audio-control {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.audio-control label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.audio-control input[type="range"] {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  background: rgba(0, 123, 255, 0.2);
  border-radius: 3px;
  outline: none;
}

.audio-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.audio-control input[type="range"]::-webkit-slider-thumb:hover {
  background: var(--color-primary-dark);
  transform: scale(1.1);
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.api-key-form {
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease-in-out;
}

.api-key-form h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--text-primary);
}

.api-key-form .form-group {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.api-key-form input {
  flex: 1;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(0, 123, 255, 0.3);
  background-color: var(--color-surface-dark);
  color: var(--text-primary);
}

.api-key-form button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  background-color: var(--color-primary);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.api-key-form button:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
}

.api-key-form button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.api-key-form button:last-child {
  background-color: #6c757d;
}

.api-key-form button:last-child:hover:not(:disabled) {
  background-color: #5a6268;
}

.api-key-help {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0;
}

.api-key-help a {
  color: var(--color-primary);
  text-decoration: none;
}

.api-key-help a:hover {
  text-decoration: underline;
}

.voice-error {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 3px solid #dc3545;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.voice-error p {
  margin: 0;
  color: #dc3545;
  font-size: 0.9rem;
}

.voice-error button {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
}

.voice-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.start-conversation-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #6f42c1; /* Color morado para el Guru */
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.start-conversation-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  transform: scale(0);
  opacity: 0;
  transition: transform 0.5s, opacity 0.5s;
}

.start-conversation-button:hover:not(:disabled)::before {
  transform: scale(1);
  opacity: 1;
}

.start-conversation-button:hover:not(:disabled) {
  background-color: #5a32a3;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
}

.start-conversation-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.voice-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.voice-button:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.voice-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.voice-button.listening {
  background-color: #dc3545;
  animation: pulse 1.5s infinite;
}

.voice-button.active {
  background-color: #28a745;
}

.end-call-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.end-call-button:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.end-call-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.audio-test-container {
  position: relative;
}

.test-audio-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.test-audio-button:hover:not(:disabled) {
  background-color: #138496;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.test-audio-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.audio-options-menu {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  right: 0;
  background-color: var(--color-surface-dark);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  padding: 0.5rem;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  animation: fadeIn 0.2s ease-in-out;
}

.audio-option-button {
  background-color: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.audio-option-button:hover:not(:disabled) {
  background-color: rgba(23, 162, 184, 0.2);
}

.audio-option-button:disabled {
  background-color: rgba(108, 117, 125, 0.1);
  color: rgba(108, 117, 125, 0.7);
  cursor: not-allowed;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.voice-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.transcript-container {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1rem;
  min-height: 60px;
  max-height: 150px;
  overflow-y: auto;
}

.transcript {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
  line-height: 1.5;
}

.send-voice-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-end;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.send-voice-button:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.send-voice-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Estilos para el indicador de reproducción de audio */
.audio-status {
  background-color: rgba(23, 162, 184, 0.1);
  border-left: 3px solid #17a2b8;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.audio-status p {
  margin: 0;
  color: #17a2b8;
  font-size: 0.9rem;
}

.audio-animation {
  display: flex;
  align-items: flex-end;
  height: 20px;
  gap: 3px;
}

.audio-animation .bar {
  width: 3px;
  background-color: #17a2b8;
  border-radius: 3px;
  animation: sound-wave 0.8s infinite ease-in-out;
}

.audio-animation .bar:nth-child(1) {
  height: 10px;
  animation-delay: 0s;
}

.audio-animation .bar:nth-child(2) {
  height: 16px;
  animation-delay: 0.2s;
}

.audio-animation .bar:nth-child(3) {
  height: 20px;
  animation-delay: 0.4s;
}

.audio-animation .bar:nth-child(4) {
  height: 14px;
  animation-delay: 0.6s;
}

.audio-animation .bar:nth-child(5) {
  height: 8px;
  animation-delay: 0.8s;
}

@keyframes sound-wave {
  0% {
    transform: scaleY(0.8);
  }
  50% {
    transform: scaleY(1.2);
  }
  100% {
    transform: scaleY(0.8);
  }
}

.playing-audio .voice-button,
.playing-audio .end-call-button,
.playing-audio .test-audio-button,
.playing-audio .send-voice-button {
  opacity: 0.7;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Estilos para los indicadores de estado del Guru */
.guru-status-indicator {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  transition: all 0.3s ease;
}

.guru-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
}

/* Estado idle */
.guru-status-indicator.idle {
  background-color: rgba(108, 117, 125, 0.1);
  border-left: 3px solid #6c757d;
}

.guru-status.idle {
  color: #6c757d;
}

.guru-status.idle i {
  font-size: 0.8rem;
}

/* Estado listening */
.guru-status-indicator.listening {
  background-color: rgba(40, 167, 69, 0.1);
  border-left: 3px solid #28a745;
}

.guru-status.listening {
  color: #28a745;
}

.listening-animation {
  display: flex;
  gap: 3px;
}

.listening-animation .circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #28a745;
  animation: listening-pulse 1.5s infinite ease-in-out;
}

.listening-animation .circle:nth-child(1) {
  animation-delay: 0s;
}

.listening-animation .circle:nth-child(2) {
  animation-delay: 0.3s;
}

.listening-animation .circle:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes listening-pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

/* Estado processing */
.guru-status-indicator.processing {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 3px solid #ffc107;
}

.guru-status.processing {
  color: #ffc107;
}

.processing-animation i {
  animation: spin 1.5s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Estado speaking */
.guru-status-indicator.speaking {
  background-color: rgba(23, 162, 184, 0.1);
  border-left: 3px solid #17a2b8;
}

.guru-status.speaking {
  color: #17a2b8;
}

.guru-status.speaking .audio-animation .bar {
  background-color: #17a2b8;
}

/* Responsive styles */
@media (max-width: 768px) {
  .voice-interface-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .voice-status {
    width: 100%;
    justify-content: space-between;
  }

  .voice-controls {
    flex-direction: column;
  }

  .voice-button, .end-call-button, .test-audio-button {
    width: 100%;
  }

  .audio-controls-panel {
    flex-direction: column;
  }

  .guru-status-indicator {
    padding: 0.5rem;
  }

  .guru-status {
    font-size: 0.8rem;
  }
}
