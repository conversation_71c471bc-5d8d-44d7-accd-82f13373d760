/**
 * Servicio para interactuar con la API de Ultravox
 */

const axios = require('axios');
// Nota: No podemos usar el paquete ultravox-client directamente debido a problemas de compatibilidad
// const { UltravoxSession } = require('ultravox-client');
// Importar la configuración directamente
const config = {
  apiKeys: {
    ultravox: process.env.ULTRAVOX_API_KEY || 'RMYI1Vn8.esFyGKVH1366gvk2VuxHnVMBToweW9aJ'
  }
};
const WebSocket = require('ws');

// Configuración de la API de Ultravox
const ULTRAVOX_API_URL = 'https://api.ultravox.ai';
// Obtener la API key de las variables de entorno o del archivo de configuración
const ULTRAVOX_API_KEY = process.env.ULTRAVOX_API_KEY || config.apiKeys.ultravox || '';

// Verificar si tenemos una API key válida
console.log('Ultravox API Key configurada:', ULTRAVOX_API_KEY ? `Sí (${ULTRAVOX_API_KEY.substring(0, 5)}...)` : 'No');

// Configuración de los endpoints de la API
const ENDPOINTS = {
  voices: '/api/voices',
  tts: '/api/text-to-speech',
  calls: '/api/calls',
  agents: '/api/agents'
};

// ID del agente Guru Cripto creado en Ultravox.ai
const GURU_CRIPTO_AGENT_ID = '39b1348e-734d-40e4-966e-fb6bbd70d474';

// ID de la voz española para el Guru Cripto
const GURU_CRIPTO_VOICE_ID = '806d5c1e-b5ae-46c8-8719-04f1bc67c0a3';

// Configuración de los headers para las solicitudes a la API
const getHeaders = (includeContentType = true) => {
  const headers = {
    'X-API-Key': ULTRAVOX_API_KEY
  };

  if (includeContentType) {
    headers['Content-Type'] = 'application/json';
  }

  return headers;
};

// Configuración de voces de alta calidad
const PREMIUM_VOICES = {
  'es-ES': {
    male: 'es-ES-Neural2-B', // Voz masculina española de alta calidad
    female: 'es-ES-Neural2-A' // Voz femenina española de alta calidad
  },
  'es-MX': {
    male: 'es-MX-Neural2-B', // Voz masculina mexicana de alta calidad
    female: 'es-MX-Neural2-A' // Voz femenina mexicana de alta calidad
  },
  'en-US': {
    male: 'en-US-Neural2-D', // Voz masculina americana de alta calidad
    female: 'en-US-Neural2-F' // Voz femenina americana de alta calidad
  }
};

// Voz predeterminada para el Guru Cripto
const DEFAULT_GURU_VOICE = PREMIUM_VOICES['es-ES'].male;

// Simulación simplificada de la sesión de Ultravox
class UltravoxSimulatedSession {
  constructor() {
    this.status = 'disconnected';
    this.transcripts = [];
    this.callId = null;
  }

  joinCall(joinUrl) {
    this.status = 'connected';
    this.callId = 'simulated-call-' + Math.random().toString(36).substring(2, 10);
    console.log('Simulando unión a llamada con URL:', joinUrl);

    // Simular cambio de estado después de un tiempo
    setTimeout(() => {
      this.status = 'listening';
      console.log('Estado de sesión simulada cambiado a:', this.status);
    }, 1000);

    return true;
  }

  leaveCall() {
    this.status = 'disconnected';
    this.callId = null;
    this.transcripts = [];
    console.log('Simulando salida de llamada');
    return true;
  }

  addEventListener(event, callback) {
    console.log('Simulando registro de evento:', event);
    // No hacemos nada con los callbacks en esta simulación
  }

  // Método para simular la recepción de una transcripción
  simulateTranscript(text, isFinal = true, speaker = 'agent') {
    const transcript = {
      text,
      isFinal,
      speaker,
      medium: 'voice'
    };

    this.transcripts.push(transcript);
    console.log('Transcripción simulada añadida:', transcript);

    return transcript;
  }
}

// Clase para gestionar las sesiones simuladas
class UltravoxSessionManager {
  constructor() {
    this.sessions = new Map();
    this.callIdToSessionId = new Map(); // Mapeo de IDs de llamadas a IDs de sesiones
    this.apiKey = ULTRAVOX_API_KEY;
    this.axiosConfig = {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      }
    };

    console.log('Ultravox API Key configurada:', this.apiKey ? 'Sí (longitud: ' + this.apiKey.length + ')' : 'No');
  }

  // Obtener una sesión existente o crear una nueva
  getSession(sessionId) {
    if (!this.sessions.has(sessionId)) {
      const session = new UltravoxSimulatedSession();
      this.sessions.set(sessionId, session);
      return session;
    }
    return this.sessions.get(sessionId);
  }

  // Eliminar una sesión
  removeSession(sessionId) {
    if (this.sessions.has(sessionId)) {
      const session = this.sessions.get(sessionId);
      session.leaveCall();

      // Eliminar también el mapeo de callId a sessionId si existe
      for (const [callId, sid] of this.callIdToSessionId.entries()) {
        if (sid === sessionId) {
          this.callIdToSessionId.delete(callId);
          break;
        }
      }

      this.sessions.delete(sessionId);
      return true;
    }
    return false;
  }

  // Mapear un ID de llamada a un ID de sesión
  mapCallIdToSessionId(callId, sessionId) {
    this.callIdToSessionId.set(callId, sessionId);
    console.log(`Mapeado callId ${callId} a sessionId ${sessionId}`);
  }

  // Obtener el ID de sesión a partir del ID de llamada
  getSessionIdFromCallId(callId) {
    return this.callIdToSessionId.get(callId);
  }

  // Obtener el ID de llamada a partir del ID de sesión
  getCallIdFromSessionId(sessionId) {
    for (const [callId, sid] of this.callIdToSessionId.entries()) {
      if (sid === sessionId) {
        return callId;
      }
    }
    return null;
  }
}

// Instancia del gestor de sesiones
let sessionManager = new UltravoxSessionManager();

// Función para actualizar la API key
const updateApiKey = (newApiKey) => {
  // Actualizar la variable global
  ULTRAVOX_API_KEY = newApiKey;

  // Recrear el gestor de sesiones con la nueva API key
  sessionManager = new UltravoxSessionManager();

  console.log('API key de Ultravox actualizada');

  // Reiniciar el modo de fallback
  process.env.ULTRAVOX_FORCE_FALLBACK = 'false';

  return true;
};

/**
 * Verifica el estado de la API de Ultravox y obtiene los créditos disponibles
 * @returns {Promise<Object>} Objeto con el estado de la API y los créditos disponibles
 */
const checkApiStatus = async () => {
  try {
    // Verificar si tenemos una API key configurada
    if (!ULTRAVOX_API_KEY) {
      console.log('No hay API key configurada para Ultravox');
      return {
        success: false,
        isValid: false,
        credits: 0,
        message: 'No hay API key configurada'
      };
    }

    // Intentar hacer una solicitud a la API de Ultravox para verificar la clave
    const response = await axios.get(`${ULTRAVOX_API_URL}${ENDPOINTS.voices}`, {
      headers: getHeaders(false)
    });

    // Si la solicitud es exitosa, la API key es válida
    return {
      success: true,
      isValid: true,
      credits: 100, // Ultravox no proporciona un endpoint específico para créditos, así que usamos un valor predeterminado
      message: 'API key válida'
    };
  } catch (error) {
    console.error('Error verificando estado de Ultravox:', error.message);

    // Si el error es 401 o 403, la API key no es válida
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      // Activar el modo de simulación
      process.env.ULTRAVOX_FORCE_FALLBACK = 'true';
      console.log('API key inválida, activando modo de simulación');

      // Devolver un estado "válido" para que la aplicación funcione con el fallback
      return {
        success: true,
        isValid: true,
        credits: 100,
        message: 'Usando modo de simulación (API key inválida)',
        simulation: true
      };
    }

    // Para otros errores, puede ser un problema de conexión
    // Activar el modo de simulación
    process.env.ULTRAVOX_FORCE_FALLBACK = 'true';
    console.log(`Error de conexión: ${error.message}, activando modo de simulación`);

    // Devolver un estado "válido" para que la aplicación funcione con el fallback
    return {
      success: true,
      isValid: true,
      credits: 100,
      message: `Usando modo de simulación (Error de conexión)`,
      simulation: true
    };
  }
};

/**
 * Inicia una nueva llamada de voz
 * @param {string} systemPrompt - Prompt del sistema para la llamada
 * @param {string} language - Idioma de la llamada (es, en, etc.)
 * @returns {Promise<Object>} Objeto con la información de la llamada
 */
const startCall = async (systemPrompt, language = 'es') => {
  try {
    // Verificar si tenemos una API key configurada
    if (!ULTRAVOX_API_KEY) {
      console.log('No hay API key configurada para Ultravox, usando modo de simulación');
      return startSimulatedCall();
    }

    console.log('Iniciando llamada real con el Guru Cripto...');

    // Seleccionar la voz según el idioma
    const voiceId = language.startsWith('es') ? GURU_CRIPTO_VOICE_ID : '21m00Tcm4TlvDq8ikWAM'; // Voz en inglés por defecto

    // Crear una llamada en la API de Ultravox con parámetros mínimos necesarios
    const callData = {
      systemPrompt: systemPrompt || "Eres el Guru Cripto, un experto en criptomonedas y blockchain.",
      voice: voiceId,
      // Añadir el ID del agente en los metadatos
      metadata: {
        agentId: GURU_CRIPTO_AGENT_ID
      }
    };

    console.log('Enviando solicitud a Ultravox:', JSON.stringify(callData));

    const response = await axios.post(`${ULTRAVOX_API_URL}${ENDPOINTS.calls}`, callData, {
      headers: getHeaders()
    });

    // Obtener el ID de la llamada y la URL para unirse
    const callId = response.data.callId;
    const joinUrl = response.data.joinUrl;
    console.log('Llamada creada con ID:', callId);
    console.log('URL para unirse a la llamada:', joinUrl);

    // Crear una nueva sesión y unirse a la llamada
    const sessionId = 'session-' + Math.random().toString(36).substring(2, 10);
    const session = sessionManager.getSession(sessionId);
    session.callId = callId; // Guardar el ID real de la llamada

    // Asociar el ID de la sesión con el ID de la llamada
    sessionManager.mapCallIdToSessionId(callId, sessionId);

    // Intentar conectarse mediante WebSocket si tenemos una URL
    if (joinUrl) {
      try {
        console.log('Intentando conectar mediante WebSocket...');
        const wsConnection = connectToCall(callId, joinUrl);
        session.wsConnection = wsConnection;
        console.log('Conexión WebSocket guardada en la sesión');
      } catch (wsError) {
        console.error('Error conectando mediante WebSocket:', wsError.message);
      }
    }

    // Simular una respuesta de bienvenida después de un tiempo
    setTimeout(() => {
      session.simulateTranscript('Hola, soy el Guru Cripto. ¿En qué puedo ayudarte hoy?');
    }, 2000);

    return {
      success: true,
      call: {
        id: sessionId,
        realCallId: callId,
        status: 'active',
        createdAt: new Date().toISOString()
      },
      credits: 95 // Valor aproximado
    };
  } catch (error) {
    console.error('Error iniciando llamada de voz real:', error.message);
    console.log('Fallback a llamada simulada debido a error:', error.message);
    return startSimulatedCall();
  }
};

/**
 * Inicia una llamada simulada con el Guru Cripto
 * @returns {Promise<Object>} Objeto con la información de la llamada simulada
 */
const startSimulatedCall = () => {
  console.log('Iniciando llamada simulada con el Guru Cripto...');

  // Siempre usar el modo de simulación
  process.env.ULTRAVOX_FORCE_FALLBACK = 'true';

  // Crear un ID de sesión único
  const sessionId = 'session-' + Math.random().toString(36).substring(2, 10);

  // Crear una nueva sesión simulada
  const session = sessionManager.getSession(sessionId);

  // Simular la unión a una llamada
  session.joinCall('simulated-call');

  // Simular una respuesta de bienvenida después de un tiempo
  setTimeout(() => {
    session.simulateTranscript('Hola, soy el Guru Cripto. ¿En qué puedo ayudarte hoy?');
  }, 2000);

  console.log('Llamada simulada iniciada con ID de sesión:', sessionId);

  return {
    success: true,
    call: {
      id: sessionId,
      status: 'active',
      createdAt: new Date().toISOString()
    },
    credits: 95 // Valor aproximado
  };
};

/**
 * Finaliza una llamada de voz
 * @param {string} callId - ID de la llamada a finalizar
 * @returns {Promise<Object>} Objeto con el resultado de la operación
 */
const endCall = async (callId) => {
  try {
    // Verificar si la sesión existe
    if (!sessionManager.sessions.has(callId)) {
      return {
        success: false,
        message: 'Llamada no encontrada'
      };
    }

    // Obtener la sesión y finalizar la llamada
    const session = sessionManager.getSession(callId);
    session.leaveCall();

    // Eliminar la sesión
    sessionManager.removeSession(callId);

    return {
      success: true,
      message: 'Llamada finalizada correctamente'
    };
  } catch (error) {
    console.error('Error finalizando llamada de voz:', error.message);
    throw new Error(`Error al finalizar la llamada de voz: ${error.message}`);
  }
};

/**
 * Envía un mensaje en una llamada de voz
 * @param {string} callId - ID de la llamada
 * @param {string} message - Mensaje a enviar
 * @returns {Promise<Object>} Objeto con la respuesta del asistente
 */
const sendMessage = async (callId, message) => {
  try {
    // Verificar si la sesión existe
    if (!sessionManager.sessions.has(callId)) {
      return {
        success: false,
        message: 'Llamada no encontrada'
      };
    }

    // Obtener la sesión
    const session = sessionManager.getSession(callId);

    // Verificar si la sesión está activa
    if (session.status === 'disconnected') {
      return {
        success: false,
        message: 'La llamada no está activa'
      };
    }

    // Preparar el mensaje para la API de Ultravox
    const messageData = {
      messages: [
        {
          role: 'user',
          content: message
        }
      ]
    };

    // Obtener el ID de la llamada real de Ultravox (no el ID de sesión)
    const callIdReal = session.callId;

    // Intentar enviar el mensaje a través de WebSocket si está disponible
    if (session.wsConnection) {
      try {
        // Verificar el estado de la conexión
        const readyState = session.wsConnection.readyState;
        console.log(`Estado de la conexión WebSocket: ${readyState} (0=CONNECTING, 1=OPEN, 2=CLOSING, 3=CLOSED)`);

        if (readyState === WebSocket.OPEN) {
          console.log(`Enviando mensaje a través de WebSocket: ${message}`);

          // Formato del mensaje para WebSocket según la documentación de Ultravox
          const wsMessage = JSON.stringify({
            type: 'message',
            role: 'user',
            content: message
          });

          // Enviar el mensaje
          session.wsConnection.send(wsMessage);

          console.log('Mensaje enviado correctamente a través de WebSocket');

          // No necesitamos hacer nada más, las respuestas llegarán a través del evento 'message'
          return {
            success: true,
            message: 'Mensaje enviado correctamente a través de WebSocket',
            pending: true // Indicar que la respuesta llegará a través de WebSocket
          };
        } else if (readyState === WebSocket.CONNECTING) {
          console.log('La conexión WebSocket aún se está estableciendo, esperando...');

          // Esperar a que la conexión se establezca
          await new Promise((resolve) => {
            const checkInterval = setInterval(() => {
              if (session.wsConnection.readyState === WebSocket.OPEN) {
                clearInterval(checkInterval);
                resolve();
              } else if (session.wsConnection.readyState === WebSocket.CLOSED || session.wsConnection.readyState === WebSocket.CLOSING) {
                clearInterval(checkInterval);
                resolve();
              }
            }, 500);

            // Timeout después de 5 segundos
            setTimeout(() => {
              clearInterval(checkInterval);
              resolve();
            }, 5000);
          });

          // Verificar nuevamente el estado
          if (session.wsConnection.readyState === WebSocket.OPEN) {
            console.log(`Enviando mensaje a través de WebSocket (después de espera): ${message}`);

            // Formato del mensaje para WebSocket según la documentación de Ultravox
            const wsMessage = JSON.stringify({
              type: 'message',
              role: 'user',
              content: message
            });

            // Enviar el mensaje
            session.wsConnection.send(wsMessage);

            console.log('Mensaje enviado correctamente a través de WebSocket');

            return {
              success: true,
              message: 'Mensaje enviado correctamente a través de WebSocket',
              pending: true
            };
          } else {
            console.log('La conexión WebSocket no se estableció a tiempo, usando fallback');
          }
        } else {
          console.log(`La conexión WebSocket no está abierta (estado: ${readyState}), usando fallback`);
        }
      } catch (wsError) {
        console.error('Error enviando mensaje a través de WebSocket:', wsError.message);
        // Continuar con el fallback
      }
    } else {
      console.log('No hay conexión WebSocket disponible, usando fallback');
    }

    // Intentar enviar el mensaje a la API real de Ultravox si no estamos en modo de simulación
    // y no pudimos usar WebSocket
    if (!process.env.ULTRAVOX_FORCE_FALLBACK && callIdReal) {
      try {
        console.log(`Enviando mensaje a la API de Ultravox: ${message}`);
        const response = await axios.post(
          `${ULTRAVOX_API_URL}${ENDPOINTS.calls}/${callIdReal}/messages`,
          messageData,
          {
            headers: getHeaders()
          }
        );

        // Procesar la respuesta real
        if (response.data && response.data.content) {
          session.simulateTranscript(response.data.content);
        }
      } catch (error) {
        console.error('Error enviando mensaje a la API de Ultravox:', error.message);
        // Activar modo de simulación para futuros intentos
        process.env.ULTRAVOX_FORCE_FALLBACK = 'true';
      }
    }

    // Si estamos en modo de simulación o hubo un error, simular la respuesta
    console.log(`Simulando envío de mensaje a la API de Ultravox: ${message}`);

    // Simular una respuesta después de un tiempo
    let responseText = '';

    // Generar una respuesta basada en palabras clave en el mensaje
    if (message.toLowerCase().includes('bitcoin') || message.toLowerCase().includes('btc')) {
      responseText = 'Bitcoin es la primera y más conocida criptomoneda. Actualmente tiene un precio aproximado de $63,000 USD y una capitalización de mercado de más de 1 billón de dólares.';
    } else if (message.toLowerCase().includes('ethereum') || message.toLowerCase().includes('eth')) {
      responseText = 'Ethereum es la segunda criptomoneda más grande por capitalización de mercado. Es una plataforma descentralizada que permite la creación de contratos inteligentes y aplicaciones descentralizadas.';
    } else if (message.toLowerCase().includes('precio') || message.toLowerCase().includes('valor')) {
      responseText = 'Los precios de las criptomonedas son muy volátiles. Bitcoin está alrededor de $63,000 USD, Ethereum cerca de $3,000 USD, y Binance Coin aproximadamente $500 USD.';
    } else {
      responseText = 'Entiendo tu consulta. Como Guru Cripto, puedo ayudarte con información sobre criptomonedas, blockchain, DeFi y más. ¿Hay algo específico sobre lo que quieras saber?';
    }

    // Simular la recepción de una transcripción
    session.simulateTranscript(responseText);

    // Esperar a que la respuesta esté disponible (simulación)
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Obtener las transcripciones de la sesión
    const transcripts = session.transcripts || [];

    // Buscar la última respuesta del agente
    const agentTranscripts = transcripts.filter(t => t.speaker === 'agent' && t.isFinal);
    const lastAgentTranscript = agentTranscripts.length > 0 ? agentTranscripts[agentTranscripts.length - 1] : null;

    // Si no hay respuesta del agente, devolver un mensaje genérico
    if (!lastAgentTranscript) {
      return {
        success: true,
        message: 'Mensaje enviado correctamente, pero aún no hay respuesta',
        credits: 90,
        response: {
          id: 'msg-' + Math.random().toString(36).substring(2, 10),
          content: 'Estoy procesando tu consulta...',
          audioUrl: null
        }
      };
    }

    // Generar un ID único para el mensaje de respuesta
    const responseId = 'msg-' + Math.random().toString(36).substring(2, 10);

    // Obtener el audio de la respuesta
    const audioUrl = `http://localhost:3002/api/ultravox/audio/response?text=${encodeURIComponent(lastAgentTranscript.text)}&voice=${DEFAULT_GURU_VOICE}`;

    return {
      success: true,
      message: 'Mensaje enviado correctamente',
      credits: 90,
      response: {
        id: responseId,
        content: lastAgentTranscript.text,
        audioUrl: audioUrl
      }
    };
  } catch (error) {
    console.error('Error enviando mensaje en llamada de voz:', error.message);
    throw new Error(`Error al enviar el mensaje: ${error.message}`);
  }
};

/**
 * Obtiene los mensajes de una llamada de voz
 * @param {string} callId - ID de la llamada
 * @returns {Promise<Object>} Objeto con los mensajes de la llamada
 */
const getMessages = async (callId) => {
  try {
    // Verificar si la sesión existe
    if (!sessionManager.sessions.has(callId)) {
      return {
        success: false,
        message: 'Llamada no encontrada'
      };
    }

    // Obtener la sesión
    const session = sessionManager.getSession(callId);

    // Obtener las transcripciones de la sesión
    const transcripts = session.transcripts || [];

    // Convertir las transcripciones al formato esperado
    const messages = transcripts.filter(t => t.isFinal).map((transcript, index) => {
      return {
        id: 'msg-' + index,
        role: transcript.speaker === 'user' ? 'user' : 'assistant',
        content: transcript.text,
        createdAt: new Date().toISOString(),
        audioUrl: transcript.speaker === 'agent' ?
          `http://localhost:3002/api/ultravox/audio/response?text=${encodeURIComponent(transcript.text)}&voice=${DEFAULT_GURU_VOICE}` :
          null
      };
    });

    return {
      success: true,
      messages: messages
    };
  } catch (error) {
    console.error('Error obteniendo mensajes de llamada de voz:', error.message);
    throw new Error(`Error al obtener los mensajes: ${error.message}`);
  }
};

/**
 * Genera un archivo de audio a partir de texto
 * @param {string} text - Texto a convertir en audio
 * @param {string} voice - Voz a utilizar (opcional)
 * @param {Object} options - Opciones adicionales para la generación de audio
 * @returns {Promise<Buffer>} Buffer con el archivo de audio
 */
const generateAudio = async (text, voice = DEFAULT_GURU_VOICE, options = {}) => {
  try {
    console.log(`Generando audio para texto: "${text.substring(0, 50)}..." con voz ${voice}`);

    // Siempre usar el modo de fallback para la generación de audio
    // Esto es temporal hasta que tengamos una API key válida para ElevenLabs
    console.log('Usando síntesis de voz del navegador para la generación de audio');

    // Crear un archivo de audio MP3 vacío con metadatos que indican que es un fallback
    // Esto permitirá que el frontend detecte que debe usar Web Speech API
    const simpleAudioMP3 = Buffer.from([
      0xFF, 0xFB, 0x90, 0x44, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x54, 0x41, 0x47, 0x46, 0x61, 0x6C, 0x6C, 0x62,
      0x61, 0x63, 0x6B, 0x20, 0x41, 0x75, 0x64, 0x69,
      0x6F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    ]);

    console.log('Audio de fallback generado correctamente');

    // Forzar el uso de Web Speech API en el frontend
    process.env.ULTRAVOX_FORCE_FALLBACK = 'true';

    return simpleAudioMP3;
  } catch (error) {
    console.error('Error generando audio:', error.message);

    // Si hay un error, devolver un audio MP3 especial que indica que es un fallback
    console.log('Generando audio fallback debido al error');
    const fallbackAudioMP3 = Buffer.from([
      0xFF, 0xFB, 0x90, 0x44, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x54, 0x41, 0x47, 0x46, 0x61, 0x6C, 0x6C, 0x62,
      0x61, 0x63, 0x6B, 0x20, 0x41, 0x75, 0x64, 0x69,
      0x6F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    ]);

    return fallbackAudioMP3;
  }
};

/**
 * Conecta a una llamada de Ultravox mediante WebSocket
 * @param {string} callId - ID de la llamada
 * @param {string} joinUrl - URL WebSocket para unirse a la llamada
 * @returns {WebSocket} Conexión WebSocket
 */
const connectToCall = (callId, joinUrl) => {
  console.log(`Conectando a llamada ${callId} mediante WebSocket: ${joinUrl}`);

  const ws = new WebSocket(joinUrl, {
    headers: {
      'X-API-Key': ULTRAVOX_API_KEY
    }
  });

  ws.on('open', () => {
    console.log(`Conexión WebSocket establecida para llamada ${callId}`);
  });

  ws.on('message', (data) => {
    const dataStr = data.toString();
    console.log(`Mensaje recibido de Ultravox (${callId}):`, dataStr.substring(0, 200) + (dataStr.length > 200 ? '...' : ''));

    try {
      // Intentar parsear el mensaje como JSON para ver su estructura
      const jsonData = JSON.parse(dataStr);
      console.log('Tipo de mensaje:', jsonData.type || 'desconocido');
      console.log('Contenido del mensaje:', jsonData.content ? (jsonData.content.substring(0, 100) + (jsonData.content.length > 100 ? '...' : '')) : 'sin contenido');

      // Procesar el mensaje y actualizar la sesión
      processWebSocketMessage(callId, dataStr);
    } catch (error) {
      console.error('Error procesando mensaje WebSocket:', error.message);
      console.log('Mensaje no es JSON válido, intentando procesar como texto plano');
      processWebSocketMessage(callId, dataStr);
    }
  });

  ws.on('error', (error) => {
    console.error(`Error en WebSocket para llamada ${callId}:`, error.message);
  });

  ws.on('close', () => {
    console.log(`Conexión WebSocket cerrada para llamada ${callId}`);
  });

  return ws;
};

/**
 * Procesa un mensaje recibido a través de WebSocket
 * @param {string} callId - ID de la llamada
 * @param {string} data - Datos recibidos
 */
const processWebSocketMessage = (callId, data) => {
  try {
    // Parsear el mensaje
    const message = JSON.parse(data);

    console.log('Mensaje procesado:', message);

    // Obtener la sesión
    const sessionId = sessionManager.getSessionIdFromCallId(callId);
    if (!sessionId) {
      console.error(`No se encontró sessionId para callId ${callId}`);
      return;
    }

    const session = sessionManager.getSession(sessionId);

    // Procesar según el tipo de mensaje
    if (message.type === 'transcript') {
      // Es una transcripción del agente
      console.log(`Transcripción recibida para ${sessionId}:`, message.content);
      session.simulateTranscript(message.content, message.isFinal, 'agent');

      // Emitir evento a través de Socket.IO si está disponible
      if (global.io) {
        global.io.to(sessionId).emit('transcript', {
          callId: sessionId,
          text: message.content,
          isFinal: message.isFinal
        });
      }
    } else if (message.type === 'audio') {
      // Es un fragmento de audio
      console.log(`Audio recibido para ${sessionId}`);

      // Emitir evento a través de Socket.IO si está disponible
      if (global.io) {
        global.io.to(sessionId).emit('audio', {
          callId: sessionId,
          audioUrl: `/api/ultravox/audio/response?text=${encodeURIComponent(message.content || '')}&voice=${DEFAULT_GURU_VOICE}`
        });
      }
    } else if (message.type === 'message') {
      // Es un mensaje del agente
      console.log(`Mensaje recibido para ${sessionId}:`, message.content);

      if (message.content) {
        session.simulateTranscript(message.content, true, 'agent');

        // Emitir evento a través de Socket.IO si está disponible
        if (global.io) {
          global.io.to(sessionId).emit('transcript', {
            callId: sessionId,
            text: message.content,
            isFinal: true
          });

          // También generar audio para este mensaje
          global.io.to(sessionId).emit('audio', {
            callId: sessionId,
            audioUrl: `/api/ultravox/audio/response?text=${encodeURIComponent(message.content)}&voice=${DEFAULT_GURU_VOICE}`
          });
        }
      }
    } else if (message.type === 'agent_message') {
      // Es un mensaje del agente (formato alternativo)
      console.log(`Mensaje del agente recibido para ${sessionId}:`, message.content);

      if (message.content) {
        session.simulateTranscript(message.content, true, 'agent');

        // Emitir evento a través de Socket.IO si está disponible
        if (global.io) {
          global.io.to(sessionId).emit('transcript', {
            callId: sessionId,
            text: message.content,
            isFinal: true
          });

          // También generar audio para este mensaje
          global.io.to(sessionId).emit('audio', {
            callId: sessionId,
            audioUrl: `/api/ultravox/audio/response?text=${encodeURIComponent(message.content)}&voice=${DEFAULT_GURU_VOICE}`
          });
        }
      }
    } else if (message.role === 'assistant') {
      // Es un mensaje del asistente (formato alternativo)
      console.log(`Mensaje del asistente recibido para ${sessionId}:`, message.content);

      if (message.content) {
        session.simulateTranscript(message.content, true, 'agent');

        // Emitir evento a través de Socket.IO si está disponible
        if (global.io) {
          global.io.to(sessionId).emit('transcript', {
            callId: sessionId,
            text: message.content,
            isFinal: true
          });

          // También generar audio para este mensaje
          global.io.to(sessionId).emit('audio', {
            callId: sessionId,
            audioUrl: `/api/ultravox/audio/response?text=${encodeURIComponent(message.content)}&voice=${DEFAULT_GURU_VOICE}`
          });
        }
      }
    } else if (message.type === 'error') {
      // Es un error
      console.error(`Error recibido para ${sessionId}:`, message.content || message.error || 'Error desconocido');

      // Emitir evento a través de Socket.IO si está disponible
      if (global.io) {
        global.io.to(sessionId).emit('error', {
          callId: sessionId,
          error: message.content || message.error || 'Error desconocido'
        });
      }
    } else if (message.type === 'room_info') {
      // Información de la sala, no necesitamos hacer nada especial
      console.log(`Información de sala recibida para ${sessionId}`);
    } else if (message.type === 'connection_status') {
      // Estado de la conexión
      console.log(`Estado de conexión recibido para ${sessionId}:`, message.status || 'desconocido');
    } else {
      // Otro tipo de mensaje
      console.log(`Mensaje de tipo desconocido para ${sessionId}:`, message.type);

      // Intentar procesar como mensaje genérico si tiene contenido
      if (message.content && typeof message.content === 'string') {
        console.log(`Intentando procesar como mensaje genérico:`, message.content.substring(0, 100));

        session.simulateTranscript(message.content, true, 'agent');

        // Emitir evento a través de Socket.IO si está disponible
        if (global.io) {
          global.io.to(sessionId).emit('transcript', {
            callId: sessionId,
            text: message.content,
            isFinal: true
          });

          // También generar audio para este mensaje
          global.io.to(sessionId).emit('audio', {
            callId: sessionId,
            audioUrl: `/api/ultravox/audio/response?text=${encodeURIComponent(message.content)}&voice=${DEFAULT_GURU_VOICE}`
          });
        }
      }
    }
  } catch (error) {
    console.error('Error procesando mensaje WebSocket:', error.message);
  }
};

module.exports = {
  checkApiStatus,
  startCall,
  endCall,
  sendMessage,
  getMessages,
  generateAudio,
  updateApiKey,
  connectToCall
};
