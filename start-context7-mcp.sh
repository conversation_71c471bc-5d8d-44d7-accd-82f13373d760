#!/bin/bash

# Script para iniciar Context7 MCP Server en Unix/Linux/Mac

echo -e "\033[36mIniciando Context7 MCP Server...\033[0m"
echo ""
echo -e "Este servidor proporciona documentación actualizada para bibliotecas y frameworks,"
echo -e "mejorando las capacidades del Gurú Cripto."
echo ""
echo -e "\033[33mPara usar Context7 en tus consultas al Gurú, simplemente añade 'use context7' al final de tu prompt.\033[0m"
echo -e "\033[33mEjemplo: 'Crea un componente React que muestre un gráfico de precios de Bitcoin usando Chart.js. use context7'\033[0m"
echo ""
echo -e "\033[90mPresiona Ctrl+C para detener el servidor.\033[0m"
echo ""

# Iniciar Context7 MCP
npx -y @upstash/context7-mcp@latest

echo ""
echo -e "\033[31mContext7 MCP Server se ha detenido.\033[0m"
read -p "Presiona Enter para salir"
