/**
 * Módulo para verificar la disponibilidad de servicios
 */
const net = require('net');
const http = require('http');
const https = require('https');

/**
 * Verifica si un puerto está en uso
 * @param {number} port - Puerto a verificar
 * @returns {Promise<boolean>} - true si el puerto está en uso, false en caso contrario
 */
function checkTcpPort(port) {
  return new Promise((resolve) => {
    if (!port) {
      console.log(`checkTcpPort: No se especificó puerto, asumiendo disponible.`);
      resolve(true); // Si no se especifica puerto, asumimos que está disponible
      return;
    }

    console.log(`checkTcpPort: Verificando puerto ${port}...`);

    const socket = new net.Socket();
    let status = false;
    let error = null;

    // Establecer timeout
    socket.setTimeout(2000); // Aumentado a 2 segundos para dar más tiempo

    socket.on('connect', () => {
      console.log(`checkTcpPort: Conexión establecida al puerto ${port}.`);
      status = true;
      socket.destroy();
    });

    socket.on('timeout', () => {
      console.log(`checkTcpPort: Timeout al intentar conectar al puerto ${port}.`);
      error = new Error('Timeout');
      socket.destroy();
    });

    socket.on('error', (e) => {
      console.log(`checkTcpPort: Error al conectar al puerto ${port}: ${e.message}`);
      error = e;
    });

    socket.on('close', () => {
      console.log(`checkTcpPort: Puerto ${port} ${status ? 'en uso' : 'libre'}.`);
      resolve(status);
    });

    try {
      socket.connect(port, '127.0.0.1');
      console.log(`checkTcpPort: Intentando conectar a 127.0.0.1:${port}...`);
    } catch (err) {
      console.error(`checkTcpPort: Error crítico al intentar conectar: ${err.message}`);
      resolve(false);
    }
  });
}

/**
 * Realiza una solicitud HTTP/HTTPS para verificar si un servicio está disponible
 * @param {string} url - URL a verificar
 * @param {number} timeout - Tiempo máximo de espera en ms
 * @returns {Promise<boolean>} - true si el servicio está disponible, false en caso contrario
 */
function checkHttpEndpoint(url, timeout = 5000) {
  return new Promise((resolve) => {
    if (!url) {
      console.log(`checkHttpEndpoint: No se especificó URL, asumiendo no disponible.`);
      resolve(false);
      return;
    }

    console.log(`checkHttpEndpoint: Verificando URL ${url} con timeout ${timeout}ms...`);

    try {
      const client = url.startsWith('https') ? https : http;
      const req = client.get(url, { timeout }, (res) => {
        const statusCode = res.statusCode;
        console.log(`checkHttpEndpoint: Respuesta de ${url} con código ${statusCode}.`);
        res.resume(); // Consumir el cuerpo de la respuesta para liberar memoria
        resolve(statusCode >= 200 && statusCode < 400); // Considerar 2xx y 3xx como éxito
      });

      req.on('error', (err) => {
        console.log(`checkHttpEndpoint: Error al conectar a ${url}: ${err.message}`);
        resolve(false);
      });

      req.on('timeout', () => {
        console.log(`checkHttpEndpoint: Timeout al conectar a ${url}.`);
        req.destroy();
        resolve(false);
      });
    } catch (err) {
      console.error(`checkHttpEndpoint: Error crítico al intentar conectar a ${url}: ${err.message}`);
      resolve(false);
    }
  });
}

/**
 * Espera a que un servicio esté disponible
 * @param {Object} component - Componente a verificar
 * @param {number} maxAttempts - Número máximo de intentos
 * @param {number} interval - Intervalo entre intentos en ms
 * @returns {Promise<boolean>} - true si el servicio está disponible, false en caso contrario
 */
async function waitForService(component, maxAttempts = 10, interval = 1000) {
  const { name, port, healthCheck } = component;
  const { type, url, timeout } = healthCheck || {};

  console.log(`waitForService: Esperando a que ${name} esté disponible...`);
  console.log(`waitForService: Tipo de verificación: ${type || 'none'}, Puerto: ${port || 'N/A'}, URL: ${url || 'N/A'}, Timeout: ${timeout || 'default'}ms`);

  for (let i = 0; i < maxAttempts; i++) {
    console.log(`waitForService: Intento ${i + 1}/${maxAttempts} para ${name}...`);
    let available = false;

    try {
      if (type === 'tcp') {
        console.log(`waitForService: Verificando puerto ${port} para ${name}...`);
        available = await checkTcpPort(port);
      } else if (type === 'http') {
        console.log(`waitForService: Verificando URL ${url} para ${name}...`);
        available = await checkHttpEndpoint(url, timeout);
      } else if (type === 'none') {
        // Si no se especifica tipo de verificación, asumimos que está disponible después de un retraso
        console.log(`waitForService: No se especificó tipo de verificación para ${name}, esperando ${timeout || 5000}ms...`);
        await new Promise(resolve => setTimeout(resolve, timeout || 5000));
        available = true;
      } else {
        console.warn(`waitForService: Tipo de verificación desconocido para ${name}: ${type}`);
        available = false;
      }
    } catch (err) {
      console.error(`waitForService: Error al verificar disponibilidad de ${name}: ${err.message}`);
      available = false;
    }

    if (available) {
      console.log(`waitForService: ${name} está disponible.`);
      return true;
    }

    console.log(`waitForService: ${name} no está disponible. Esperando ${interval}ms antes del siguiente intento...`);
    await new Promise(resolve => setTimeout(resolve, interval));
  }

  console.error(`waitForService: Error: ${name} no está disponible después de ${maxAttempts} intentos.`);
  return false;
}

/**
 * Espera a que todos los servicios dependientes estén disponibles
 * @param {Object} component - Componente actual
 * @param {Array} allComponents - Todos los componentes
 * @param {Object} options - Opciones adicionales
 * @returns {Promise<boolean>} - true si todos los servicios dependientes están disponibles, false en caso contrario
 */
async function waitForDependencies(component, allComponents, options = {}) {
  const { dependencies } = component;
  const { maxAttempts = 10, interval = 1000 } = options;

  if (!dependencies || dependencies.length === 0) {
    console.log(`waitForDependencies: ${component.name} no tiene dependencias.`);
    return true;
  }

  console.log(`waitForDependencies: Verificando ${dependencies.length} dependencias para ${component.name}...`);
  console.log(`waitForDependencies: Dependencias: ${dependencies.join(', ')}`);

  for (const depId of dependencies) {
    console.log(`waitForDependencies: Buscando componente con ID ${depId}...`);
    const depComponent = allComponents.find(c => c.id === depId);

    if (!depComponent) {
      console.warn(`waitForDependencies: Advertencia: Dependencia ${depId} no encontrada para ${component.name}`);
      continue;
    }

    console.log(`waitForDependencies: Verificando disponibilidad de ${depComponent.name} para ${component.name}...`);
    const available = await waitForService(depComponent, maxAttempts, interval);

    if (!available) {
      console.error(`waitForDependencies: Error: Dependencia ${depComponent.name} no está disponible para ${component.name}`);
      return false;
    }

    console.log(`waitForDependencies: Dependencia ${depComponent.name} está disponible para ${component.name}.`);
  }

  console.log(`waitForDependencies: Todas las dependencias están disponibles para ${component.name}.`);
  return true;
}

module.exports = {
  checkTcpPort,
  checkHttpEndpoint,
  waitForService,
  waitForDependencies
};
