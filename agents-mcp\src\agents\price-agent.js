/**
 * Agente especializado en precios de criptomonedas
 * 
 * Este agente se encarga de obtener y analizar precios de criptomonedas.
 */

const BaseAgent = require('./base-agent');
const logger = require('../utils/logger');
const { AgentError } = require('../utils/error-handler');

class PriceAgent extends BaseAgent {
  /**
   * @param {Object} [options={}] - Opciones adicionales
   */
  constructor(options = {}) {
    super('priceAgent', options);
  }
  
  /**
   * Ejecuta una tarea especializada
   * @param {Object} task - Tarea a ejecutar
   * @returns {Promise<Object>} Resultado de la tarea
   * @private
   */
  async _executeSpecializedTask(task) {
    switch (task.type) {
      case 'GET_CRYPTO_PRICE':
        return await this.getCryptoPrice(task.parameters);
      case 'GET_MULTIPLE_PRICES':
        return await this.getMultiplePrices(task.parameters);
      case 'GET_HISTORICAL_DATA':
        return await this.getHistoricalData(task.parameters);
      case 'ANALYZE_PRICE_TREND':
        return await this.analyzePriceTrend(task.parameters);
      case 'GET_TRENDING_COINS':
        return await this.getTrendingCoins(task.parameters);
      default:
        return await super._executeSpecializedTask(task);
    }
  }
  
  /**
   * Obtiene el precio actual de una criptomoneda
   * @param {Object} parameters - Parámetros para la obtención del precio
   * @returns {Promise<Object>} Información del precio
   */
  async getCryptoPrice(parameters) {
    try {
      const { symbol, currency = 'usd' } = parameters;
      
      logger.debug('PriceAgent', `Obteniendo precio de ${symbol} en ${currency}`);
      
      // Obtener el adaptador MCP de crypto
      const cryptoAdapter = this.getMcpAdapter('crypto');
      
      // Obtener el precio
      const priceData = await cryptoAdapter.getPrice(symbol, currency);
      
      return {
        symbol,
        currency,
        price: priceData.price,
        priceChange24h: priceData.price_change_24h,
        priceChangePercentage24h: priceData.price_change_percentage_24h,
        marketCap: priceData.market_cap,
        volume24h: priceData.total_volume,
        lastUpdated: priceData.last_updated
      };
    } catch (error) {
      throw new AgentError(
        `Error al obtener precio de criptomoneda: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Obtiene los precios de múltiples criptomonedas
   * @param {Object} parameters - Parámetros para la obtención de precios
   * @returns {Promise<Object>} Información de precios
   */
  async getMultiplePrices(parameters) {
    try {
      const { symbols, currency = 'usd' } = parameters;
      
      logger.debug('PriceAgent', `Obteniendo precios de ${symbols.join(', ')} en ${currency}`);
      
      // Obtener el adaptador MCP de crypto
      const cryptoAdapter = this.getMcpAdapter('crypto');
      
      // Obtener los precios
      const pricesData = await cryptoAdapter.getPrices(symbols, currency);
      
      return {
        currency,
        prices: pricesData
      };
    } catch (error) {
      throw new AgentError(
        `Error al obtener precios de criptomonedas: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Obtiene datos históricos de una criptomoneda
   * @param {Object} parameters - Parámetros para la obtención de datos históricos
   * @returns {Promise<Object>} Datos históricos
   */
  async getHistoricalData(parameters) {
    try {
      const { symbol, days, interval = 'daily' } = parameters;
      
      logger.debug('PriceAgent', `Obteniendo datos históricos de ${symbol} para ${days} días con intervalo ${interval}`);
      
      // Obtener el adaptador MCP de crypto
      const cryptoAdapter = this.getMcpAdapter('crypto');
      
      // Obtener los datos históricos
      const historicalData = await cryptoAdapter.getHistoricalData(symbol, days, interval);
      
      return {
        symbol,
        days,
        interval,
        data: historicalData
      };
    } catch (error) {
      throw new AgentError(
        `Error al obtener datos históricos: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Analiza la tendencia de precio de una criptomoneda
   * @param {Object} parameters - Parámetros para el análisis de tendencia
   * @returns {Promise<Object>} Análisis de tendencia
   */
  async analyzePriceTrend(parameters) {
    try {
      const { symbol, days = 30, interval = 'daily' } = parameters;
      
      logger.debug('PriceAgent', `Analizando tendencia de precio de ${symbol} para ${days} días`);
      
      // Obtener datos históricos
      const historicalData = await this.getHistoricalData({
        symbol,
        days,
        interval
      });
      
      // Analizar la tendencia utilizando el LLM
      const analysisResult = await this._analyzeText({
        text: JSON.stringify(historicalData.data),
        instructions: `Analiza la tendencia de precio de ${symbol} en los últimos ${days} días. Identifica patrones, soportes, resistencias y posibles tendencias futuras. Proporciona un análisis detallado pero conciso.`,
        format: 'JSON'
      });
      
      return {
        symbol,
        days,
        interval,
        trend: analysisResult.analysis,
        data: historicalData.data
      };
    } catch (error) {
      throw new AgentError(
        `Error al analizar tendencia de precio: ${error.message}`,
        { parameters },
        error
      );
    }
  }
  
  /**
   * Obtiene las criptomonedas en tendencia
   * @param {Object} parameters - Parámetros para la obtención de criptomonedas en tendencia
   * @returns {Promise<Object>} Criptomonedas en tendencia
   */
  async getTrendingCoins(parameters) {
    try {
      const { limit = 10 } = parameters;
      
      logger.debug('PriceAgent', `Obteniendo criptomonedas en tendencia (límite: ${limit})`);
      
      // Obtener el adaptador MCP de crypto
      const cryptoAdapter = this.getMcpAdapter('crypto');
      
      // Obtener las criptomonedas en tendencia
      const trendingCoins = await cryptoAdapter.getTrendingCoins(limit);
      
      return {
        limit,
        coins: trendingCoins
      };
    } catch (error) {
      throw new AgentError(
        `Error al obtener criptomonedas en tendencia: ${error.message}`,
        { parameters },
        error
      );
    }
  }
}

module.exports = PriceAgent;
