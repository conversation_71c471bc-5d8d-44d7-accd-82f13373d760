import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import App from './App';
import MiRadarCriptoPage from './pages/MiRadarCriptoPage';
import RadarPage from './pages/RadarPage';
import CryptoGuruPage from './pages/CryptoGuruPage';
import PredictorPage from './pages/PredictorPage';
import NewsPage from './pages/NewsPage';
import EnhancedNewsPage from './pages/EnhancedNewsPage';
import PortfolioPage from './pages/PortfolioPage';
import AnalysisPage from './pages/AnalysisPage';
import AcademyPage from './pages/AcademyPage';
import DefiCenterPage from './pages/DefiCenterPage';
import TechnicalAnalysisPage from './pages/TechnicalAnalysisPage';
import FundamentalAnalysisPage from './pages/FundamentalAnalysisPage';
import CoinDetail from './components/CoinDetail';
import Layout from './components/Layout';
import Auth from './components/Auth';
import { AuthProvider, useAuth } from './context/NewAuthContext';
import CriptoGeckoDashboard from './components/CriptoGeckoDashboard';
import IntelligentDashboard from './components/dashboard/IntelligentDashboard';
import MigrateWatchlistData from './components/MigrateWatchlistData';
import './styles/MigrateWatchlistData.css';

// Componente para proteger rutas
const ProtectedRoute: React.FC<{ element: React.ReactNode }> = ({ element }) => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return <div className="app-loading">Cargando...</div>;
  }

  // Temporalmente permitimos acceso incluso sin autenticación para solucionar el problema
  // return currentUser ? <>{element}</> : <Navigate to="/login" replace />;
  return <>{element}</>;
};

const AppRouter: React.FC = () => {
  return (
    <Router>
      <AuthProvider>
        <MigrateWatchlistData />
        <Routes>
          {/* Ruta de autenticación */}
          <Route path="/login" element={<Auth />} />

          {/* Rutas protegidas con Layout compartido */}
          <Route path="/" element={<Layout />}>
            <Route index element={<ProtectedRoute element={<IntelligentDashboard />} />} />
            <Route path="dashboard" element={<ProtectedRoute element={<IntelligentDashboard />} />} />
            <Route path="dashboard-legacy" element={<ProtectedRoute element={<CriptoGeckoDashboard />} />} />
            <Route path="portfolio" element={<ProtectedRoute element={<PortfolioPage />} />} />
            <Route path="radar" element={<ProtectedRoute element={<MiRadarCriptoPage />} />} />
            {/* Mantener la ruta antigua para compatibilidad */}
            <Route path="watchlist" element={<Navigate to="/radar" replace />} />
            <Route path="guru" element={<ProtectedRoute element={<CryptoGuruPage />} />} />
            <Route path="predictor" element={<ProtectedRoute element={<PredictorPage />} />} />
            <Route path="news" element={<ProtectedRoute element={<EnhancedNewsPage />} />} />
            <Route path="news/:symbol" element={<ProtectedRoute element={<EnhancedNewsPage />} />} />
            <Route path="news-legacy" element={<ProtectedRoute element={<NewsPage />} />} />
            <Route path="analysis" element={<ProtectedRoute element={<AnalysisPage />} />} />
            <Route path="technical" element={<ProtectedRoute element={<TechnicalAnalysisPage />} />} />
            <Route path="fundamental" element={<ProtectedRoute element={<FundamentalAnalysisPage />} />} />
            <Route path="fundamental/:symbol" element={<ProtectedRoute element={<FundamentalAnalysisPage />} />} />
            <Route path="academy/*" element={<ProtectedRoute element={<AcademyPage />} />} />
            <Route path="defi" element={<ProtectedRoute element={<DefiCenterPage />} />} />
            <Route path="coins/:id" element={<ProtectedRoute element={<CoinDetail />} />} />
          </Route>

          {/* Redireccionar rutas no encontradas al dashboard */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </AuthProvider>
    </Router>
  );
};

export default AppRouter;
