.guru-adk-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #1a1a2e;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.guru-adk-header {
  background: linear-gradient(135deg, #0f3460 0%, #162447 100%);
  color: white;
  padding: 16px 24px;
  border-bottom: 1px solid #2a2a4a;
}

.guru-adk-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.guru-adk-header p {
  margin: 4px 0 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.guru-adk-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 16px 24px;
  background-color: #16213e;
  border-bottom: 1px solid #2a2a4a;
}

.control-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.control-group label {
  font-size: 0.85rem;
  margin-bottom: 4px;
  color: #e2e2e2;
}

.control-group select {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #3a3a5a;
  background-color: #1e1e42;
  color: white;
  font-size: 0.9rem;
  outline: none;
  transition: border-color 0.2s;
}

.control-group select:focus {
  border-color: #4a4a8a;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-left: auto;
  align-items: flex-end;
}

.action-buttons button {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

.action-buttons button:active {
  transform: scale(0.98);
}

.action-buttons button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.analysis-button {
  background-color: #0f4c81;
  color: white;
}

.analysis-button:hover:not(:disabled) {
  background-color: #0d5c9e;
}

.prediction-button {
  background-color: #6a0572;
  color: white;
}

.prediction-button:hover:not(:disabled) {
  background-color: #7b0785;
}

.guru-adk-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6e6e9e;
  text-align: center;
  padding: 0 32px;
}

.robot-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  color: #4a4a8a;
}

.message {
  display: flex;
  gap: 12px;
  max-width: 90%;
  animation: fadeIn 0.3s ease-out;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.assistant-message {
  align-self: flex-start;
}

.message-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-message .message-icon {
  background-color: #2d4263;
  color: white;
}

.assistant-message .message-icon {
  background-color: #4a4a8a;
  color: white;
}

.message-content {
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.95rem;
  line-height: 1.5;
}

.user-message .message-content {
  background-color: #2d4263;
  color: white;
  border-top-right-radius: 0;
}

.assistant-message .message-content {
  background-color: #1e1e42;
  color: #e2e2e2;
  border-top-left-radius: 0;
}

.error-message .message-content {
  background-color: #6e1423;
  color: #ffcccc;
}

.loading-message .message-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.spinner {
  animation: spin 1s linear infinite;
}

.guru-adk-input {
  display: flex;
  padding: 16px 24px;
  gap: 8px;
  background-color: #16213e;
  border-top: 1px solid #2a2a4a;
}

.guru-adk-input input {
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #3a3a5a;
  background-color: #1e1e42;
  color: white;
  font-size: 0.95rem;
  outline: none;
  transition: border-color 0.2s;
}

.guru-adk-input input:focus {
  border-color: #4a4a8a;
}

.guru-adk-input button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

.guru-adk-input button:active {
  transform: scale(0.95);
}

.guru-adk-input button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.voice-button {
  background-color: #2d4263;
  color: white;
}

.voice-button:hover:not(:disabled) {
  background-color: #3a5380;
}

.voice-button.recording {
  background-color: #e63946;
  animation: pulse 1.5s infinite;
}

.send-button {
  background-color: #0f4c81;
  color: white;
}

.send-button:hover:not(:disabled) {
  background-color: #0d5c9e;
}

.error-notification {
  padding: 12px 16px;
  background-color: #6e1423;
  color: white;
  text-align: center;
  animation: fadeIn 0.3s ease-out;
}

/* Code formatting */
.message-content pre {
  background-color: #0d1117;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}

.message-content code {
  font-family: 'Courier New', monospace;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 2px 4px;
  border-radius: 4px;
}

.message-content pre code {
  background-color: transparent;
  padding: 0;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .guru-adk-controls {
    flex-direction: column;
  }
  
  .action-buttons {
    margin-left: 0;
    width: 100%;
  }
  
  .action-buttons button {
    flex: 1;
  }
  
  .message {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .guru-adk-header {
    padding: 12px 16px;
  }
  
  .guru-adk-controls,
  .guru-adk-messages,
  .guru-adk-input {
    padding: 12px 16px;
  }
}
