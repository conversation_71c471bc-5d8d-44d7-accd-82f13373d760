.guru-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.guru-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.back-link {
  display: flex;
  align-items: center;
  color: var(--text-medium);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--text-bright);
}

.guru-header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.portfolio-header-link, .predictor-header-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-medium);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.portfolio-header-link:hover, .predictor-header-link:hover {
  background: rgba(0, 0, 0, 0.4);
  color: var(--text-bright);
  border-color: rgba(255, 255, 255, 0.2);
}

.info-button, .voice-toggle-button {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-medium);
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-button:hover, .voice-toggle-button:hover {
  background: rgba(0, 0, 0, 0.4);
  color: var(--text-bright);
  border-color: rgba(255, 255, 255, 0.2);
}

.voice-toggle-button.active {
  background: rgba(var(--primary-rgb), 0.2);
  border-color: var(--primary);
  color: var(--primary);
}

.guru-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.guru-status.online .status-dot {
  background-color: var(--success);
  box-shadow: 0 0 5px var(--success);
}

.guru-status.offline .status-dot {
  background-color: var(--error);
  box-shadow: 0 0 5px var(--error);
}

.guru-status.thinking .status-dot {
  background-color: var(--warning);
  box-shadow: 0 0 5px var(--warning);
  animation: pulse 1.5s infinite;
}

.status-text {
  color: var(--text-medium);
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* Responsive styles */
@media (max-width: 768px) {
  .guru-header {
    padding: 1rem;
    flex-wrap: wrap;
  }
  
  .guru-header h1 {
    order: -1;
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .guru-header-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .guru-status {
    display: none;
  }
}
