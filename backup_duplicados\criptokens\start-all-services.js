#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Función para imprimir mensajes con formato
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para iniciar un proceso
function startProcess(name, command, args, cwd, color = colors.reset, env = {}) {
  log(`Iniciando ${name}...`, color);

  const processEnv = { ...process.env, ...env };

  const process = spawn(command, args, {
    cwd,
    env: processEnv,
    shell: true,
    stdio: 'pipe'
  });

  process.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        log(`[${name}] ${line}`, color);
      }
    });
  });

  process.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        log(`[${name}] ${line}`, colors.red);
      }
    });
  });

  process.on('error', (error) => {
    log(`Error al iniciar ${name}: ${error.message}`, colors.red);
  });

  process.on('close', (code) => {
    if (code !== 0) {
      log(`${name} se ha detenido con código ${code}`, colors.red);
    } else {
      log(`${name} se ha detenido correctamente`, colors.green);
    }
  });

  return process;
}

// Directorios
const rootDir = __dirname;
const frontendDir = path.join(rootDir, 'frontend');
const backendDir = path.join(rootDir, 'backend');
const cryptoMcpServerDir = path.join(path.dirname(rootDir), 'crypto-mcp-server');
const mcpOrchestratorDir = path.join(path.dirname(rootDir), 'mcp-orchestrator');
const playwrightMcpServerDir = path.join(path.dirname(rootDir), 'playwright-mcp-server');
const braveSearchServerPath = path.join(path.dirname(rootDir), 'brave-search-server.js');

// Verificar que los directorios existen
const missingDirs = [];

if (!fs.existsSync(frontendDir)) {
  missingDirs.push(`Frontend: ${frontendDir}`);
}

if (!fs.existsSync(backendDir)) {
  missingDirs.push(`Backend: ${backendDir}`);
}

if (!fs.existsSync(cryptoMcpServerDir)) {
  missingDirs.push(`Crypto MCP Server: ${cryptoMcpServerDir}`);
}

// Si hay directorios faltantes, mostrar advertencia pero continuar
if (missingDirs.length > 0) {
  log('ADVERTENCIA: Algunos directorios no se encontraron:', colors.yellow);
  missingDirs.forEach(dir => log(`- ${dir}`, colors.yellow));
  log('Se intentará iniciar los servicios disponibles.', colors.yellow);
}

// Iniciar el servidor MCP de crypto
let cryptoMcpProcess = null;
if (fs.existsSync(cryptoMcpServerDir)) {
  cryptoMcpProcess = startProcess(
    'Crypto MCP Server',
    'node',
    ['http-server.js'],
    cryptoMcpServerDir,
    colors.magenta
  );
} else {
  log('No se pudo iniciar el servidor Crypto MCP: directorio no encontrado', colors.yellow);
}

// Esperar un momento para que el servidor MCP se inicie
setTimeout(() => {
  // Iniciar el servidor MCP Orchestrator (Brave Search)
  let braveSearchProcess = null;
  if (fs.existsSync(braveSearchServerPath)) {
    braveSearchProcess = startProcess(
      'Brave Search Server',
      'node',
      [braveSearchServerPath],
      path.dirname(rootDir),
      colors.blue
    );
  } else if (fs.existsSync(mcpOrchestratorDir)) {
    braveSearchProcess = startProcess(
      'MCP Orchestrator',
      'node',
      ['dist/server.js'],
      mcpOrchestratorDir,
      colors.blue
    );
  } else {
    log('No se pudo iniciar el servidor Brave Search: archivo no encontrado', colors.yellow);
  }

  // Iniciar el servidor Playwright MCP
  let playwrightMcpProcess = null;
  if (fs.existsSync(playwrightMcpServerDir)) {
    playwrightMcpProcess = startProcess(
      'Playwright MCP Server',
      'node',
      ['dist/server.js'],
      playwrightMcpServerDir,
      colors.cyan
    );
  } else {
    log('No se pudo iniciar el servidor Playwright MCP: directorio no encontrado', colors.yellow);
  }

  // Esperar un momento para que los servidores MCP se inicien
  setTimeout(() => {
    // Iniciar el backend
    const backendProcess = startProcess(
      'Backend',
      'node',
      ['src/server.js'],
      backendDir,
      colors.green
    );

    // Esperar un momento para que el backend se inicie
    setTimeout(() => {
      // Iniciar el frontend
      const frontendProcess = startProcess(
        'Frontend',
        'npx',
        ['vite'],
        frontendDir,
        colors.yellow
      );

      // Mostrar mensaje de éxito
      setTimeout(() => {
        log('\n=== TODOS LOS SERVICIOS INICIADOS ===', colors.green + colors.bright);
        log('Servicios disponibles:', colors.bright);

        if (cryptoMcpProcess) {
          log('- Crypto MCP Server: http://localhost:3101', colors.magenta);
        }

        if (braveSearchProcess) {
          log('- Brave Search Server: http://localhost:3102', colors.blue);
        }

        if (playwrightMcpProcess) {
          log('- Playwright MCP Server: http://localhost:3103', colors.cyan);
        }

        log('- Backend: http://localhost:3001', colors.green);
        log('- Frontend: http://localhost:5173', colors.yellow);

        log('\nPresiona Ctrl+C para detener todos los servicios.', colors.bright);
      }, 2000);

      // Manejar la terminación del script
      process.on('SIGINT', () => {
        log('\nDeteniendo todos los servicios...', colors.yellow);

        if (cryptoMcpProcess) {
          cryptoMcpProcess.kill();
        }

        if (braveSearchProcess) {
          braveSearchProcess.kill();
        }

        if (playwrightMcpProcess) {
          playwrightMcpProcess.kill();
        }

        backendProcess.kill();
        frontendProcess.kill();

        setTimeout(() => {
          log('Todos los servicios han sido detenidos', colors.green);
          process.exit(0);
        }, 1000);
      });
    }, 2000); // Esperar 2 segundos para que el backend se inicie
  }, 2000); // Esperar 2 segundos para que los servidores MCP se inicien
}, 2000); // Esperar 2 segundos para que el servidor MCP de crypto se inicie

log('Iniciando todos los servicios. Presiona Ctrl+C para detener.', colors.green + colors.bright);
