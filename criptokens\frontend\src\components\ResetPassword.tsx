import { useState } from 'react';
import { useAuth } from '../context/NewAuthContext';
import '../styles/Auth.css';

interface ResetPasswordProps {
  onLoginClick: () => void;
}

const ResetPassword = ({ onLoginClick }: ResetPasswordProps) => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const { resetPassword } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      setError('Por favor, ingresa tu correo electrónico.');
      return;
    }

    try {
      setError(null);
      setMessage(null);
      setLoading(true);

      await resetPassword(email);

      setMessage('Se ha enviado un correo electrónico con instrucciones para restablecer tu contraseña.');
      setEmail('');
    } catch (err: any) {
      console.error('Error al restablecer contraseña:', err);

      // Mensajes de error más amigables
      if (err.message.includes('user-not-found')) {
        setError('No existe una cuenta con este correo electrónico.');
      } else if (err.message.includes('invalid-email')) {
        setError('El formato del correo electrónico no es válido.');
      } else {
        setError('Error al enviar el correo de restablecimiento. Por favor, inténtalo de nuevo.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <h2>Restablecer Contraseña</h2>

        {error && <div className="auth-error">{error}</div>}
        {message && <div className="auth-success">{message}</div>}

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Correo Electrónico</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <button
            type="submit"
            className="auth-button"
            disabled={loading}
          >
            {loading ? 'Enviando...' : 'Enviar Correo de Restablecimiento'}
          </button>
        </form>

        <div className="auth-links">
          <div className="auth-separator">
            <span>¿Recordaste tu contraseña?</span>
          </div>

          <button
            className="secondary-button"
            onClick={onLoginClick}
          >
            Volver a Iniciar Sesión
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
