/**
 * Índice de adaptadores MCP
 * 
 * Este módulo exporta todos los adaptadores MCP disponibles.
 */

const BaseMcpAdapter = require('./base-mcp-adapter');
const CryptoMcpAdapter = require('./crypto-mcp-adapter');
const BraveMcpAdapter = require('./brave-mcp-adapter');
const PlaywrightMcpAdapter = require('./playwright-mcp-adapter');
const Context7McpAdapter = require('./context7-mcp-adapter');
const FilesystemMcpAdapter = require('./filesystem-mcp-adapter');
const MemoryMcpAdapter = require('./memory-mcp-adapter');

/**
 * Crea un adaptador MCP para un servidor específico
 * @param {string} serverName - Nombre del servidor MCP
 * @param {Object} [options={}] - Opciones adicionales
 * @returns {BaseMcpAdapter} Instancia del adaptador MCP
 */
function createMcpAdapter(serverName, options = {}) {
  switch (serverName) {
    case 'crypto':
      return new CryptoMcpAdapter(options);
    case 'brave':
      return new BraveMcpAdapter(options);
    case 'playwright':
      return new PlaywrightMcpAdapter(options);
    case 'context7':
      return new Context7McpAdapter(options);
    case 'filesystem':
      return new FilesystemMcpAdapter(options);
    case 'memory':
      return new MemoryMcpAdapter(options);
    default:
      // Para servidores no específicos, usar el adaptador base
      return new BaseMcpAdapter(serverName, options);
  }
}

module.exports = {
  BaseMcpAdapter,
  CryptoMcpAdapter,
  BraveMcpAdapter,
  PlaywrightMcpAdapter,
  Context7McpAdapter,
  FilesystemMcpAdapter,
  MemoryMcpAdapter,
  createMcpAdapter
};
