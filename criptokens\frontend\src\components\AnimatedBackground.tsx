import React, { useEffect, useState, useRef } from 'react';
import anime from '../utils/animeUtils';
import '../styles/AnimatedBackground.css';

interface AnimatedBackgroundProps {
  status?: 'idle' | 'thinking' | 'positive' | 'negative' | 'neutral' | 'concerned';
}

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  direction: number;
  element: HTMLDivElement;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ status = 'neutral' }) => {
  const [gradientColors, setGradientColors] = useState({
    primary: 'rgba(123, 77, 255, 0.15)',
    secondary: 'rgba(0, 224, 255, 0.15)',
    accent: 'rgba(70, 87, 206, 0.1)'
  });

  const particlesContainerRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationFrameRef = useRef<number | null>(null);

  // Efecto para inicializar y limpiar las partículas
  useEffect(() => {
    // Detectar rendimiento del dispositivo
    const checkPerformance = () => {
      // Comprobar si es un dispositivo móvil o de baja potencia
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isLowPower = window.navigator.hardwareConcurrency ? window.navigator.hardwareConcurrency < 4 : false;

      return isMobile || isLowPower;
    };

    // Ajustar la calidad de las animaciones según el rendimiento
    const isLowPerformance = checkPerformance();

    // Inicializar partículas con menos cantidad si es un dispositivo de bajo rendimiento
    initParticles(isLowPerformance);

    // Iniciar la animación
    animateParticles();

    // Limpiar al desmontar
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // Eliminar todas las partículas del DOM
      if (particlesContainerRef.current) {
        particlesContainerRef.current.innerHTML = '';
      }
      particlesRef.current = [];
    };
  }, []);

  // Estado para rastrear el estado anterior
  const [previousStatus, setPreviousStatus] = useState<string>('neutral');

  // Efecto para cambiar los colores del fondo según el estado
  useEffect(() => {
    // Guardar el estado anterior para comparación
    if (status !== previousStatus) {
      setPreviousStatus(status);
      console.log(`AnimatedBackground: Estado cambiado de ${previousStatus} a ${status}`);
    }

    // Definir colores según el estado
    let newColors = {
      primary: 'rgba(123, 77, 255, 0.1)',
      secondary: 'rgba(0, 242, 255, 0.1)',
      accent: 'rgba(70, 87, 206, 0.05)'
    };

    switch (status) {
      case 'positive':
        newColors = {
          primary: 'rgba(0, 255, 157, 0.1)',
          secondary: 'rgba(0, 242, 255, 0.08)',
          accent: 'rgba(0, 204, 122, 0.05)'
        };
        break;
      case 'negative':
        newColors = {
          primary: 'rgba(255, 58, 110, 0.1)',
          secondary: 'rgba(255, 107, 61, 0.08)',
          accent: 'rgba(204, 35, 87, 0.05)'
        };
        break;
      case 'concerned':
        newColors = {
          primary: 'rgba(255, 204, 0, 0.1)',
          secondary: 'rgba(255, 107, 61, 0.08)',
          accent: 'rgba(204, 153, 0, 0.05)'
        };
        break;
      case 'thinking':
        newColors = {
          primary: 'rgba(123, 77, 255, 0.12)',
          secondary: 'rgba(0, 242, 255, 0.1)',
          accent: 'rgba(94, 61, 204, 0.06)'
        };
        break;
      case 'idle':
      case 'neutral':
      default:
        // Colores por defecto para estado neutral/idle
        newColors = {
          primary: 'rgba(123, 77, 255, 0.1)',
          secondary: 'rgba(0, 242, 255, 0.1)',
          accent: 'rgba(70, 87, 206, 0.05)'
        };
        break;
    }

    // Animar la transición de colores del gradiente
    const gradientLayer = document.querySelector('.gradient-layer') as HTMLElement;
    if (gradientLayer) {
      // Detener cualquier animación previa del gradiente
      anime.remove(gradientColors);
      anime.remove(gradientLayer);

      anime({
        targets: gradientColors,
        primary: newColors.primary,
        secondary: newColors.secondary,
        accent: newColors.accent,
        duration: 800, // Transición más rápida para mejor reactividad
        easing: 'easeOutSine',
        update: function() {
          gradientLayer.style.background = `
            radial-gradient(circle at 75% 25%, ${gradientColors.primary} 0%, transparent 50%),
            radial-gradient(circle at 25% 75%, ${gradientColors.secondary} 0%, transparent 50%),
            linear-gradient(to bottom right, rgba(15, 17, 35, 0), ${gradientColors.accent} 50%, rgba(15, 17, 35, 0) 100%)
          `;
        },
        complete: function() {
          // Añadir un efecto de pulso sutil después de la transición
          anime({
            targets: gradientLayer,
            opacity: [0.9, 1],
            duration: 1000,
            easing: 'easeInOutSine',
            direction: 'alternate',
            loop: 2
          });
        }
      });
    }

    // Actualizar el estado de las partículas
    updateParticlesStatus(status);
  }, [status, previousStatus, gradientColors]);

  // Función para inicializar las partículas
  const initParticles = (isLowPerformance = false) => {
    if (!particlesContainerRef.current) return;

    // Limpiar partículas existentes
    particlesContainerRef.current.innerHTML = '';
    particlesRef.current = [];

    // Crear órbitas de fondo (menos en dispositivos de bajo rendimiento)
    const numOrbits = isLowPerformance ? 2 : 3;
    for (let i = 1; i <= numOrbits; i++) {
      const orbitElement = document.createElement('div');
      orbitElement.className = `background-orbit background-orbit-${i}`;

      // Posicionar las órbitas en diferentes lugares de la pantalla
      const posX = Math.random() * 70 + 15; // Entre 15% y 85% del ancho
      const posY = Math.random() * 70 + 15; // Entre 15% y 85% del alto
      orbitElement.style.left = `${posX}%`;
      orbitElement.style.top = `${posY}%`;
      orbitElement.style.transform = 'translate(-50%, -50%)';

      particlesContainerRef.current.appendChild(orbitElement);

      // Animar rotación de las orbitas (más lenta en dispositivos de bajo rendimiento)
      anime({
        targets: orbitElement,
        rotate: 360,
        duration: isLowPerformance ? 40000 + (i * 10000) : 30000 + (i * 10000),
        easing: 'linear',
        loop: true
      });
    }

    // Crear nuevas partículas (menos en dispositivos de bajo rendimiento)
    const numParticles = isLowPerformance ? 20 : 40;

    for (let i = 0; i < numParticles; i++) {
      // Crear elemento de partícula
      const particleElement = document.createElement('div');
      particleElement.className = `particle ${status}`;

      // Opacidad inicial aleatoria para efecto más natural
      const initialOpacity = 0.3 + Math.random() * 0.3;
      particleElement.style.opacity = initialOpacity.toString();

      // Posición aleatoria
      const x = Math.random() * window.innerWidth;
      const y = Math.random() * window.innerHeight;
      particleElement.style.left = `${x}px`;
      particleElement.style.top = `${y}px`;

      // Añadir al contenedor
      particlesContainerRef.current.appendChild(particleElement);

      // Guardar referencia (velocidad más lenta en dispositivos de bajo rendimiento)
      particlesRef.current.push({
        id: i,
        x,
        y,
        size: 4, // Tamaño fijo para todas las partículas
        speed: isLowPerformance ? Math.random() * 0.2 + 0.05 : Math.random() * 0.3 + 0.1,
        direction: Math.random() * Math.PI * 2,
        element: particleElement
      });
    }
  };

  // Función para animar las partículas
  const animateParticles = () => {
    // Mover cada partícula
    particlesRef.current.forEach(particle => {
      // Calcular nueva posición
      particle.x += Math.cos(particle.direction) * particle.speed;
      particle.y += Math.sin(particle.direction) * particle.speed;

      // Cambiar dirección aleatoriamente
      if (Math.random() < 0.01) {
        particle.direction += (Math.random() - 0.5) * 0.5;
      }

      // Rebote en los bordes
      if (particle.x < 0 || particle.x > window.innerWidth) {
        particle.direction = Math.PI - particle.direction;
      }
      if (particle.y < 0 || particle.y > window.innerHeight) {
        particle.direction = -particle.direction;
      }

      // Actualizar posición en el DOM
      particle.element.style.left = `${particle.x}px`;
      particle.element.style.top = `${particle.y}px`;
    });

    // Continuar la animación
    animationFrameRef.current = requestAnimationFrame(animateParticles);
  };

  // Función para actualizar el estado de las partículas
  const updateParticlesStatus = (newStatus: string) => {
    if (!particlesContainerRef.current) return;

    // Obtener todas las órbitas y partículas
    const elements = Array.from(particlesContainerRef.current.childNodes);
    const orbits = elements.filter(node => (node as HTMLElement).className.includes('background-orbit'));
    const particles = elements.filter(node => (node as HTMLElement).className.includes('particle'));

    // Detener todas las animaciones existentes para evitar conflictos
    orbits.forEach((orbit: any) => {
      anime.remove(orbit);
    });

    particlesRef.current.forEach(particle => {
      anime.remove(particle.element);
    });

    // Actualizar color de las órbitas con animación
    orbits.forEach((orbit: any, index) => {
      let orbitColor = '';
      switch (newStatus) {
        case 'positive':
          orbitColor = index % 2 === 0 ? 'rgba(0, 255, 157, 0.15)' : 'rgba(0, 242, 255, 0.15)';
          break;
        case 'negative':
          orbitColor = index % 2 === 0 ? 'rgba(255, 58, 110, 0.15)' : 'rgba(123, 77, 255, 0.15)';
          break;
        case 'concerned':
          orbitColor = index % 2 === 0 ? 'rgba(255, 204, 0, 0.15)' : 'rgba(255, 107, 61, 0.15)';
          break;
        case 'thinking':
          orbitColor = index % 2 === 0 ? 'rgba(123, 77, 255, 0.2)' : 'rgba(0, 242, 255, 0.15)';
          break;
        case 'idle':
        case 'neutral':
        default:
          // Asegurar que vuelva a los colores por defecto
          orbitColor = index % 2 === 0 ? 'rgba(123, 77, 255, 0.15)' : 'rgba(0, 242, 255, 0.15)';
          break;
      }

      // Animar la transición de color de las órbitas
      anime({
        targets: orbit,
        borderColor: orbitColor,
        duration: 300,
        easing: 'easeOutSine'
      });
    });

    // Actualizar cada partícula con animación
    particlesRef.current.forEach(particle => {
      // Eliminar clases anteriores
      particle.element.classList.remove('positive', 'negative', 'neutral', 'concerned', 'thinking', 'idle');

      // Añadir nueva clase
      particle.element.classList.add(newStatus);

      // Definir color de partícula según el estado
      let particleColor = '';
      let particleShadow = '';

      switch (newStatus) {
        case 'positive':
          particleColor = 'rgba(0, 255, 157, 0.7)';
          particleShadow = '0 0 8px rgba(0, 255, 157, 0.6)';
          particle.speed = Math.random() * 0.5 + 0.1; // Más rápido
          break;
        case 'negative':
          particleColor = 'rgba(255, 58, 110, 0.7)';
          particleShadow = '0 0 8px rgba(255, 58, 110, 0.6)';
          particle.speed = Math.random() * 0.3 + 0.1; // Velocidad media
          break;
        case 'concerned':
          particleColor = 'rgba(255, 204, 0, 0.7)';
          particleShadow = '0 0 8px rgba(255, 204, 0, 0.6)';
          particle.speed = Math.random() * 0.4 + 0.1; // Velocidad media-alta
          break;
        case 'thinking':
          particleColor = 'rgba(123, 77, 255, 0.7)';
          particleShadow = '0 0 8px rgba(123, 77, 255, 0.6)';
          particle.speed = Math.random() * 0.4 + 0.1; // Velocidad media-alta
          break;
        case 'idle':
        case 'neutral':
        default:
          // Asegurar que vuelva a los colores por defecto
          particleColor = 'rgba(0, 242, 255, 0.7)';
          particleShadow = '0 0 8px rgba(0, 242, 255, 0.6)';
          particle.speed = Math.random() * 0.3 + 0.1; // Velocidad normal
          break;
      }

      // Animar la transición de las partículas con cambio de color explícito
      anime({
        targets: particle.element,
        backgroundColor: particleColor,
        boxShadow: particleShadow,
        opacity: [0.5, 1],
        scale: [0.8, 1],
        duration: 300,
        easing: 'easeOutSine'
      });
    });
  };

  return (
    <div className="animated-background">
      {/* Capa de gradiente animado */}
      <div className="gradient-layer"></div>

      {/* Red SVG tenue */}
      <div className="grid-overlay">
        <div className="grid-lines horizontal"></div>
        <div className="grid-lines vertical"></div>
        <div className="grid-lines diagonal-1"></div>
        <div className="grid-lines diagonal-2"></div>
      </div>

      {/* Partículas de fondo */}
      <div className="particles-container" ref={particlesContainerRef}></div>
    </div>
  );
};

export default AnimatedBackground;
