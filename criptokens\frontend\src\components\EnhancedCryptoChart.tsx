import React, { useEffect, useRef } from 'react';
import '../styles/EnhancedCryptoChart.css';

interface EnhancedCryptoChartProps {
  data: {
    labels: string[];
    values: number[];
  };
  color: string;
  height?: number;
  width?: number;
  animate?: boolean;
  showLabels?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  title?: string;
}

const EnhancedCryptoChart: React.FC<EnhancedCryptoChartProps> = ({
  data,
  color = '#00f2ff',
  height = 200,
  width = 500,
  animate = true,
  showLabels = true,
  showGrid = true,
  showTooltip = true,
  title
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !data.values.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Configurar el canvas para alta resolución
    const dpr = window.devicePixelRatio || 1;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    ctx.scale(dpr, dpr);

    // Limpiar el canvas
    ctx.clearRect(0, 0, width, height);

    // Calcular valores máximos y mínimos para escalar el gráfico
    const maxValue = Math.max(...data.values) * 1.1; // 10% más alto para margen
    const minValue = Math.min(...data.values) * 0.9; // 10% más bajo para margen
    const range = maxValue - minValue;

    // Calcular dimensiones y márgenes
    const padding = { top: 30, right: 20, bottom: 30, left: 40 };
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;

    // Dibujar cuadrícula si está habilitada
    if (showGrid) {
      ctx.beginPath();
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 0.5;

      // Líneas horizontales
      for (let i = 0; i <= 5; i++) {
        const y = padding.top + (chartHeight / 5) * i;
        ctx.moveTo(padding.left, y);
        ctx.lineTo(width - padding.right, y);
      }

      // Líneas verticales
      const step = chartWidth / (data.labels.length - 1);
      for (let i = 0; i < data.labels.length; i++) {
        const x = padding.left + step * i;
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, height - padding.bottom);
      }
      ctx.stroke();
    }

    // Dibujar etiquetas si están habilitadas
    if (showLabels) {
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';

      // Etiquetas del eje X (fechas)
      const step = chartWidth / (data.labels.length - 1);
      for (let i = 0; i < data.labels.length; i += Math.ceil(data.labels.length / 6)) {
        const x = padding.left + step * i;
        ctx.fillText(data.labels[i], x, height - padding.bottom / 2);
      }

      // Etiquetas del eje Y (precios)
      ctx.textAlign = 'right';
      for (let i = 0; i <= 5; i++) {
        const y = padding.top + (chartHeight / 5) * i;
        const value = maxValue - (range / 5) * i;
        ctx.fillText(value.toLocaleString('en-US', { maximumFractionDigits: 2 }), padding.left - 5, y + 3);
      }
    }

    // Dibujar título si está presente
    if (title) {
      ctx.fillStyle = 'white';
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(title, width / 2, padding.top / 2);
    }

    // Preparar para dibujar la línea del gráfico
    ctx.beginPath();
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.lineJoin = 'round';

    // Crear gradiente para el área bajo la línea
    const gradient = ctx.createLinearGradient(0, padding.top, 0, height - padding.bottom);
    gradient.addColorStop(0, `${color}80`); // 50% transparencia
    gradient.addColorStop(1, `${color}00`); // 0% transparencia

    // Calcular puntos para la animación
    const points: { x: number, y: number }[] = [];
    const step = chartWidth / (data.values.length - 1);
    
    for (let i = 0; i < data.values.length; i++) {
      const x = padding.left + step * i;
      const normalizedValue = (data.values[i] - minValue) / range;
      const y = height - padding.bottom - (normalizedValue * chartHeight);
      points.push({ x, y });
    }

    // Función para dibujar la línea y el área
    const drawLine = (progress = 1) => {
      // Dibujar la línea
      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      
      const pointsToDraw = Math.ceil(points.length * progress);
      
      for (let i = 0; i < pointsToDraw; i++) {
        if (i === 0) {
          ctx.moveTo(points[i].x, points[i].y);
        } else {
          ctx.lineTo(points[i].x, points[i].y);
        }
      }
      ctx.stroke();

      // Dibujar el área bajo la línea
      if (pointsToDraw > 1) {
        ctx.beginPath();
        ctx.fillStyle = gradient;
        
        ctx.moveTo(points[0].x, height - padding.bottom);
        
        for (let i = 0; i < pointsToDraw; i++) {
          ctx.lineTo(points[i].x, points[i].y);
        }
        
        ctx.lineTo(points[pointsToDraw - 1].x, height - padding.bottom);
        ctx.closePath();
        ctx.fill();
      }

      // Dibujar puntos de datos
      if (pointsToDraw > 0) {
        for (let i = 0; i < pointsToDraw; i += Math.ceil(points.length / 10)) {
          ctx.beginPath();
          ctx.fillStyle = color;
          ctx.arc(points[i].x, points[i].y, 3, 0, Math.PI * 2);
          ctx.fill();
          
          ctx.beginPath();
          ctx.fillStyle = '#0a0a1a';
          ctx.arc(points[i].x, points[i].y, 1.5, 0, Math.PI * 2);
          ctx.fill();
        }
      }
    };

    // Animar el dibujo del gráfico si está habilitado
    if (animate) {
      let progress = 0;
      const duration = 1000; // 1 segundo
      const startTime = performance.now();
      
      const animate = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        progress = Math.min(elapsed / duration, 1);
        
        // Limpiar solo el área del gráfico, no las etiquetas
        ctx.clearRect(padding.left, padding.top, chartWidth, chartHeight);
        
        // Redibujar la cuadrícula si está habilitada
        if (showGrid) {
          ctx.beginPath();
          ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
          ctx.lineWidth = 0.5;
          
          for (let i = 0; i <= 5; i++) {
            const y = padding.top + (chartHeight / 5) * i;
            ctx.moveTo(padding.left, y);
            ctx.lineTo(width - padding.right, y);
          }
          
          const step = chartWidth / (data.labels.length - 1);
          for (let i = 0; i < data.labels.length; i++) {
            const x = padding.left + step * i;
            ctx.moveTo(x, padding.top);
            ctx.lineTo(x, height - padding.bottom);
          }
          ctx.stroke();
        }
        
        drawLine(progress);
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      
      requestAnimationFrame(animate);
    } else {
      drawLine();
    }

    // Configurar interactividad para el tooltip
    if (showTooltip && tooltipRef.current && containerRef.current) {
      const tooltip = tooltipRef.current;
      const container = containerRef.current;
      
      const handleMouseMove = (e: MouseEvent) => {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        
        // Encontrar el punto de datos más cercano
        const step = chartWidth / (data.values.length - 1);
        const index = Math.round((x - padding.left) / step);
        
        if (index >= 0 && index < data.values.length) {
          const dataPoint = {
            x: padding.left + step * index,
            y: points[index].y,
            value: data.values[index],
            label: data.labels[index]
          };
          
          // Actualizar posición y contenido del tooltip
          tooltip.style.opacity = '1';
          tooltip.style.left = `${dataPoint.x}px`;
          tooltip.style.top = `${dataPoint.y - 40}px`;
          tooltip.innerHTML = `
            <div class="tooltip-date">${dataPoint.label}</div>
            <div class="tooltip-value">$${dataPoint.value.toLocaleString('en-US', { maximumFractionDigits: 2 })}</div>
          `;
          
          // Dibujar indicador de posición
          ctx.clearRect(padding.left, padding.top, chartWidth, chartHeight);
          if (showGrid) {
            ctx.beginPath();
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 0.5;
            
            for (let i = 0; i <= 5; i++) {
              const y = padding.top + (chartHeight / 5) * i;
              ctx.moveTo(padding.left, y);
              ctx.lineTo(width - padding.right, y);
            }
            
            const step = chartWidth / (data.labels.length - 1);
            for (let i = 0; i < data.labels.length; i++) {
              const x = padding.left + step * i;
              ctx.moveTo(x, padding.top);
              ctx.lineTo(x, height - padding.bottom);
            }
            ctx.stroke();
          }
          
          drawLine(1);
          
          // Dibujar línea vertical en la posición actual
          ctx.beginPath();
          ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
          ctx.lineWidth = 1;
          ctx.setLineDash([5, 3]);
          ctx.moveTo(dataPoint.x, padding.top);
          ctx.lineTo(dataPoint.x, height - padding.bottom);
          ctx.stroke();
          ctx.setLineDash([]);
          
          // Destacar el punto actual
          ctx.beginPath();
          ctx.fillStyle = color;
          ctx.arc(dataPoint.x, dataPoint.y, 5, 0, Math.PI * 2);
          ctx.fill();
          
          ctx.beginPath();
          ctx.fillStyle = '#0a0a1a';
          ctx.arc(dataPoint.x, dataPoint.y, 2.5, 0, Math.PI * 2);
          ctx.fill();
        }
      };
      
      const handleMouseLeave = () => {
        tooltip.style.opacity = '0';
        ctx.clearRect(padding.left, padding.top, chartWidth, chartHeight);
        if (showGrid) {
          ctx.beginPath();
          ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
          ctx.lineWidth = 0.5;
          
          for (let i = 0; i <= 5; i++) {
            const y = padding.top + (chartHeight / 5) * i;
            ctx.moveTo(padding.left, y);
            ctx.lineTo(width - padding.right, y);
          }
          
          const step = chartWidth / (data.labels.length - 1);
          for (let i = 0; i < data.labels.length; i++) {
            const x = padding.left + step * i;
            ctx.moveTo(x, padding.top);
            ctx.lineTo(x, height - padding.bottom);
          }
          ctx.stroke();
        }
        drawLine(1);
      };
      
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseleave', handleMouseLeave);
      
      return () => {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, [data, color, height, width, animate, showLabels, showGrid, showTooltip, title]);

  return (
    <div className="enhanced-crypto-chart" ref={containerRef}>
      <canvas ref={canvasRef} style={{ width, height }}></canvas>
      {showTooltip && <div className="chart-tooltip" ref={tooltipRef}></div>}
    </div>
  );
};

export default EnhancedCryptoChart;
