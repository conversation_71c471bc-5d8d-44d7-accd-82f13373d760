[project]
name = "a2a-python-example-ui"
version = "0.1.0"
description = "Agent2Agent example UI"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "asyncio>=3.4.3",
    "httpx>=0.28.1",
    "httpx-sse>=0.4.0",
    "pydantic>=2.10.6",
    "fastapi>=0.115.0",
    "uvicorn>=0.34.0",
    "mesop>=1.0.0",
    "a2a-samples",
    "pandas>=2.2.0",
    "google-genai>=1.9.0",
    "google-adk>=0.0.3",
]

[tool.hatch.build.targets.wheel]
packages = ["a2a_ui"]

[tool.uv.sources]
a2a_ui = { workspace = true }
a2a_samples = { path = "../../samples/python", editable = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "ruff>=0.11.2",
]
