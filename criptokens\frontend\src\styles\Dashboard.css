.dashboard {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1.5rem;
}

.dashboard-tabs {
  display: flex;
  margin-bottom: 1.5rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #333;
}

.tab-button.active {
  color: #0084ff;
  border-bottom-color: #0084ff;
}

.dashboard-content {
  flex: 1;
  overflow: auto;
}

.dashboard-main-content {
  display: flex;
  gap: 1.5rem;
}

.main-section {
  flex: 1;
}

.side-section {
  width: 300px;
}

.chat-widget {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-widget h3 {
  font-size: 1.125rem;
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
}

.chat-widget p {
  color: #666;
  margin-bottom: 1.25rem;
  font-size: 0.9375rem;
}

.open-chat-button {
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.open-chat-button:hover {
  background-color: #0077e6;
}

.chat-fullscreen {
  height: 100%;
  display: flex;
  flex-direction: column;
}

@media (max-width: 992px) {
  .dashboard-main-content {
    flex-direction: column;
  }
  
  .side-section {
    width: 100%;
  }
}
