import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/NewAuthContext';
import { usePortfolio } from '../hooks/usePortfolio';
import { useRadarCripto } from '../hooks/useRadarCripto';
import { getTopCryptocurrencies, getGlobalMarketData } from '../services/api';
import { useTopCryptocurrencies } from '../hooks/useMcpClient';
import CoinTable from './CoinTable';
import MarketOverview from './MarketOverview';
import TrendingCoins from './TrendingCoins';
import FeaturedNews from './FeaturedNews';
import CriptoAgentAvatar from './CriptoAgentAvatarExport';
import SmartInsightsPanel from './SmartInsightsPanel';
// Importar componentes existentes o crear versiones simplificadas
import '../styles/SmartDashboard.css';

const SmartDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [globalMarketData, setGlobalMarketData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [timeRange, setTimeRange] = useState<string>('24h');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showMobileFilters, setShowMobileFilters] = useState<boolean>(false);
  const [marketSentiment, setMarketSentiment] = useState<'fear' | 'neutral' | 'greed'>('neutral');
  const [userPreferences, setUserPreferences] = useState<{
    favoriteAssets: string[];
    preferredCategories: string[];
    riskProfile: 'conservative' | 'moderate' | 'aggressive';
  }>({
    favoriteAssets: [],
    preferredCategories: ['defi', 'layer1'],
    riskProfile: 'moderate'
  });

  // Usar hooks personalizados para obtener datos
  const { data: cryptos, isLoading: isCryptosLoading, error } = useTopCryptocurrencies();
  const {
    portfolio,
    portfolioStats,
    isLoading: isPortfolioLoading
  } = usePortfolio();
  const {
    radarWithPrices,
    isLoading: isRadarLoading
  } = useRadarCripto();

  // Obtener datos globales del mercado
  useEffect(() => {
    const fetchGlobalData = async () => {
      try {
        const data = await getGlobalMarketData();
        setGlobalMarketData(data);

        // Determinar el sentimiento del mercado basado en los datos
        if (data?.market_cap_change_percentage_24h_usd >= 3) {
          setMarketSentiment('greed');
        } else if (data?.market_cap_change_percentage_24h_usd >= 0) {
          setMarketSentiment('neutral');
        } else {
          setMarketSentiment('fear');
        }
      } catch (error) {
        console.error('Error fetching global market data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGlobalData();
  }, []);

  // Cargar preferencias del usuario desde localStorage o configuración predeterminada
  useEffect(() => {
    if (currentUser) {
      const savedPreferences = localStorage.getItem(`userPreferences_${currentUser.uid}`);
      if (savedPreferences) {
        setUserPreferences(JSON.parse(savedPreferences));
      } else {
        // Si no hay preferencias guardadas, usar valores predeterminados basados en el portafolio
        if (portfolio.length > 0) {
          const favoriteAssets = portfolio.slice(0, 3).map(asset => asset.id);
          setUserPreferences(prev => ({
            ...prev,
            favoriteAssets
          }));
        }
      }
    }
  }, [currentUser, portfolio]);

  // Guardar preferencias del usuario cuando cambien
  useEffect(() => {
    if (currentUser) {
      localStorage.setItem(`userPreferences_${currentUser.uid}`, JSON.stringify(userPreferences));
    }
  }, [userPreferences, currentUser]);

  // Generar insights personalizados basados en el portafolio y el radar
  const personalizedInsights = useMemo(() => {
    if (isPortfolioLoading || isRadarLoading || !globalMarketData) return [];

    const insights = [];

    // Insight 1: Rendimiento del portafolio vs mercado
    if (portfolio.length > 0) {
      const portfolioPerformance = portfolioStats.totalProfitLossPercentage;
      const marketPerformance = globalMarketData?.market_cap_change_percentage_24h_usd || 0;

      if (portfolioPerformance > marketPerformance + 5) {
        insights.push({
          type: 'success',
          title: 'Tu portafolio supera al mercado',
          description: `Tu portafolio está rindiendo un ${portfolioPerformance.toFixed(2)}%, superando al mercado general (${marketPerformance.toFixed(2)}%).`,
          action: 'Ver portafolio',
          actionPath: '/portfolio'
        });
      } else if (portfolioPerformance < marketPerformance - 5) {
        insights.push({
          type: 'warning',
          title: 'Tu portafolio está por debajo del mercado',
          description: `Tu portafolio está rindiendo un ${portfolioPerformance.toFixed(2)}%, por debajo del mercado general (${marketPerformance.toFixed(2)}%).`,
          action: 'Analizar con Guru',
          actionPath: '/guru'
        });
      }
    }

    // Insight 2: Monedas en radar con buen rendimiento
    if (radarWithPrices.length > 0) {
      const topPerformers = radarWithPrices
        .filter(crypto => crypto.priceChangePercentage24h > 10)
        .sort((a, b) => b.priceChangePercentage24h - a.priceChangePercentage24h)
        .slice(0, 2);

      if (topPerformers.length > 0) {
        insights.push({
          type: 'info',
          title: 'Monedas en tu radar con buen rendimiento',
          description: `${topPerformers.map(c => `${c.name} (${c.priceChangePercentage24h.toFixed(2)}%)`).join(', ')} están teniendo un buen rendimiento hoy.`,
          action: 'Ver radar',
          actionPath: '/radar'
        });
      }
    }

    // Insight 3: Diversificación del portafolio
    if (portfolio.length > 0) {
      const categories = new Set(portfolio.map(asset => {
        const matchingCrypto = cryptos?.find(c => c.id === asset.id);
        return matchingCrypto?.category || 'other';
      }));

      if (categories.size < 3 && portfolio.length >= 3) {
        insights.push({
          type: 'warning',
          title: 'Portafolio poco diversificado',
          description: 'Tu portafolio está concentrado en pocas categorías. Considera diversificar para reducir el riesgo.',
          action: 'Recomendaciones',
          actionPath: '/guru'
        });
      }
    }

    // Insight 4: Oportunidades basadas en el sentimiento del mercado
    if (marketSentiment === 'fear' && userPreferences.riskProfile === 'aggressive') {
      insights.push({
        type: 'opportunity',
        title: 'Posible oportunidad de compra',
        description: 'El sentimiento del mercado es de miedo, lo que podría presentar oportunidades de compra para inversores agresivos.',
        action: 'Explorar oportunidades',
        actionPath: '/guru'
      });
    } else if (marketSentiment === 'greed' && userPreferences.riskProfile === 'conservative') {
      insights.push({
        type: 'caution',
        title: 'Considerar tomar ganancias',
        description: 'El sentimiento del mercado es de codicia, podría ser un buen momento para asegurar algunas ganancias.',
        action: 'Analizar portafolio',
        actionPath: '/portfolio'
      });
    }

    return insights;
  }, [portfolio, radarWithPrices, globalMarketData, portfolioStats, marketSentiment, userPreferences, cryptos, isPortfolioLoading, isRadarLoading]);

  // Función para manejar la búsqueda
  const handleSearch = () => {
    if (searchQuery.trim()) {
      // Implementar lógica de búsqueda
      console.log(`Searching for: ${searchQuery}`);
    }
  };

  // Filtrar criptomonedas según la categoría seleccionada
  const filteredCryptos = cryptos ? [...cryptos] : [];

  // Determinar las monedas recomendadas basadas en el perfil del usuario
  const recommendedCoins = useMemo(() => {
    if (!cryptos || cryptos.length === 0) return [];

    // Filtrar por categorías preferidas del usuario
    const byCategory = cryptos.filter(crypto =>
      userPreferences.preferredCategories.includes(crypto.category || 'other')
    );

    // Filtrar por perfil de riesgo
    let filtered = [...byCategory];
    if (userPreferences.riskProfile === 'conservative') {
      // Para conservadores, monedas de mayor capitalización y menor volatilidad
      filtered = filtered.filter(crypto => crypto.market_cap_rank <= 20);
    } else if (userPreferences.riskProfile === 'aggressive') {
      // Para agresivos, monedas con mayor potencial de crecimiento
      filtered = filtered.filter(crypto =>
        crypto.market_cap_rank > 10 && crypto.market_cap_rank <= 100
      );
    }

    // Ordenar por relevancia para el usuario
    return filtered
      .sort((a, b) => {
        // Priorizar monedas favoritas
        const aIsFavorite = userPreferences.favoriteAssets.includes(a.id);
        const bIsFavorite = userPreferences.favoriteAssets.includes(b.id);

        if (aIsFavorite && !bIsFavorite) return -1;
        if (!aIsFavorite && bIsFavorite) return 1;

        // Luego por capitalización de mercado
        return a.market_cap_rank - b.market_cap_rank;
      })
      .slice(0, 5);
  }, [cryptos, userPreferences]);

  return (
    <div className="smart-dashboard">
      {/* Header con estadísticas globales */}
      <div className="dashboard-header">
        <div className="global-stats">
          {isLoading ? (
            <div className="loading-stats">Cargando estadísticas globales...</div>
          ) : (
            <>
              <div className="stat-item">
                <span className="stat-label">Criptomonedas:</span>
                <span className="stat-value">{globalMarketData?.active_cryptocurrencies || '0'}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Exchanges:</span>
                <span className="stat-value">{globalMarketData?.markets || '0'}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Cap. de mercado:</span>
                <span className="stat-value">
                  ${globalMarketData?.total_market_cap?.usd
                    ? formatNumber(globalMarketData.total_market_cap.usd)
                    : '0'}
                  <span className={`change ${globalMarketData?.market_cap_change_percentage_24h_usd >= 0 ? 'positive' : 'negative'}`}>
                    {globalMarketData?.market_cap_change_percentage_24h_usd
                      ? globalMarketData.market_cap_change_percentage_24h_usd.toFixed(1) + '%'
                      : '0%'}
                  </span>
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Volumen 24h:</span>
                <span className="stat-value">
                  ${globalMarketData?.total_volume?.usd
                    ? formatNumber(globalMarketData.total_volume.usd)
                    : '0'}
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Dominio:</span>
                <span className="stat-value">
                  BTC {globalMarketData?.market_cap_percentage?.btc
                    ? globalMarketData.market_cap_percentage.btc.toFixed(1) + '%'
                    : '0%'}
                </span>
              </div>
            </>
          )}
        </div>
        <div className="search-container">
          <div className="search-bar">
            <input
              type="text"
              placeholder="Buscar criptomoneda..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button className="search-button" onClick={handleSearch}>
              <i className="fas fa-search"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Contenido principal */}
      <div className="dashboard-content">
        {/* Panel lateral con insights personalizados */}
        <div className="dashboard-sidebar">
          {currentUser && (
            <div className="welcome-message">
              <h3>Bienvenido, {currentUser.displayName || 'Inversor'}</h3>
              <p className="market-status">
                Sentimiento del mercado: <span className={`sentiment ${marketSentiment}`}>
                  {marketSentiment === 'fear' ? 'Miedo' :
                   marketSentiment === 'greed' ? 'Codicia' : 'Neutral'}
                </span>
              </p>
            </div>
          )}

          {/* Widget de resumen del portafolio */}
          {currentUser && (
            <div className="portfolio-summary-widget" onClick={() => navigate('/portfolio')}>
              <div className="widget-header">
                <h3>Tu Portafolio</h3>
                <span className="asset-count">{portfolioStats.assetCount} activos</span>
              </div>
              <div className="widget-content">
                <div className="portfolio-value">
                  <span className="value-label">Valor Total:</span>
                  <span className="value-amount">${portfolioStats.totalValue.toFixed(2)}</span>
                </div>
                <div className="portfolio-metrics">
                  <div className="metric">
                    <span className="metric-label">Inversión:</span>
                    <span className="metric-value">${portfolioStats.totalInvestment.toFixed(2)}</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Ganancia/Pérdida:</span>
                    <span className={`metric-value ${portfolioStats.totalProfitLoss >= 0 ? 'positive' : 'negative'}`}>
                      ${portfolioStats.totalProfitLoss.toFixed(2)}
                      ({portfolioStats.totalProfitLossPercentage.toFixed(2)}%)
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Panel de insights inteligentes */}
          <SmartInsightsPanel
            insights={personalizedInsights}
            onActionClick={(path) => navigate(path)}
          />

          {/* Widget de sentimiento del mercado */}
          <div className="market-sentiment-widget">
            <div className="widget-header">
              <h3>Sentimiento del Mercado</h3>
              <div className="sentiment-icon">
                <i className={`far fa-${marketSentiment === 'fear' ? 'frown' : marketSentiment === 'greed' ? 'smile' : 'meh'}`}></i>
              </div>
            </div>
            <div className="widget-content">
              <div className="sentiment-meter">
                <div className="meter-labels">
                  <span className="fear-label">Miedo Extremo</span>
                  <span className="greed-label">Codicia Extrema</span>
                </div>
                <div className="meter-bar">
                  <div className="meter-indicator" style={{
                    left: `${marketSentiment === 'fear' ? '25' : marketSentiment === 'greed' ? '75' : '50'}%`,
                    backgroundColor: marketSentiment === 'fear' ? '#e74c3c' : marketSentiment === 'greed' ? '#2ecc71' : '#f1c40f'
                  }}></div>
                </div>
              </div>

              <div className="sentiment-advice">
                {marketSentiment === 'fear' && (
                  <p>Cuando hay miedo en el mercado, puede ser momento de considerar comprar si tienes una estrategia a largo plazo.</p>
                )}
                {marketSentiment === 'neutral' && (
                  <p>El mercado está en equilibrio. Es buen momento para revisar tu estrategia y hacer ajustes si es necesario.</p>
                )}
                {marketSentiment === 'greed' && (
                  <p>Cuando hay codicia en el mercado, considera ser cauteloso y evaluar si es momento de asegurar algunas ganancias.</p>
                )}
              </div>
            </div>
          </div>

          {/* Avatar del Guru Cripto */}
          <div className="guru-avatar-container">
            <CriptoAgentAvatar mood={marketSentiment} size="medium" />
            <div className="guru-message">
              <p>¿Necesitas ayuda con tus inversiones?</p>
              <button
                className="consult-guru-button"
                onClick={() => navigate('/guru')}
              >
                Consultar al Guru
              </button>
            </div>
          </div>
        </div>

        {/* Contenido principal del dashboard */}
        <div className="dashboard-main">
          {/* Sección de resumen del mercado */}
          <div className="dashboard-section market-overview-section">
            <MarketOverview globalMarketData={globalMarketData} isLoading={isLoading} />
          </div>

          {/* Sección de monedas recomendadas */}
          {currentUser && recommendedCoins.length > 0 && (
            <div className="dashboard-section recommended-section">
              <div className="section-header">
                <h2>Recomendado para ti</h2>
                <div className="recommendation-badge">IA</div>
              </div>
              <TrendingCoins cryptos={recommendedCoins} isLoading={isCryptosLoading} />
            </div>
          )}

          {/* Sección de tendencias */}
          <div className="dashboard-section trending-section">
            <div className="section-header">
              <h2>En Tendencia</h2>
              <button className="view-all-button" onClick={() => navigate('/trending')}>
                Ver todo
              </button>
            </div>
            <TrendingCoins cryptos={filteredCryptos.slice(0, 5)} isLoading={isCryptosLoading} />
          </div>

          {/* Sección de noticias destacadas */}
          <div className="dashboard-section news-section">
            <div className="section-header">
              <h2>Noticias Destacadas</h2>
              <button className="view-all-button" onClick={() => navigate('/news')}>
                Ver todas
              </button>
            </div>
            <FeaturedNews limit={3} />
          </div>

          {/* Sección de tabla de criptomonedas */}
          <div className="dashboard-section crypto-table-section">
            <div className="section-header with-filters">
              <h2>Criptomonedas por Capitalización de Mercado</h2>

              <div className="desktop-filters">
                <div className="time-filter">
                  <button
                    className={timeRange === '1h' ? 'active' : ''}
                    onClick={() => setTimeRange('1h')}
                  >
                    1h
                  </button>
                  <button
                    className={timeRange === '24h' ? 'active' : ''}
                    onClick={() => setTimeRange('24h')}
                  >
                    24h
                  </button>
                  <button
                    className={timeRange === '7d' ? 'active' : ''}
                    onClick={() => setTimeRange('7d')}
                  >
                    7d
                  </button>
                  <button
                    className={timeRange === '30d' ? 'active' : ''}
                    onClick={() => setTimeRange('30d')}
                  >
                    30d
                  </button>
                </div>

                <div className="category-filter">
                  <button
                    className={selectedCategory === 'all' ? 'active' : ''}
                    onClick={() => setSelectedCategory('all')}
                  >
                    Todas
                  </button>
                  <button
                    className={selectedCategory === 'defi' ? 'active' : ''}
                    onClick={() => setSelectedCategory('defi')}
                  >
                    DeFi
                  </button>
                  <button
                    className={selectedCategory === 'nft' ? 'active' : ''}
                    onClick={() => setSelectedCategory('nft')}
                  >
                    NFT
                  </button>
                  <button
                    className={selectedCategory === 'layer1' ? 'active' : ''}
                    onClick={() => setSelectedCategory('layer1')}
                  >
                    Layer 1
                  </button>
                </div>
              </div>

              <button
                className="mobile-filter-button"
                onClick={() => setShowMobileFilters(!showMobileFilters)}
              >
                <i className="fas fa-filter"></i> Filtros
              </button>
            </div>

            {showMobileFilters && (
              <div className="mobile-filters">
                <div className="filter-group">
                  <h3>Periodo</h3>
                  <div className="time-filter">
                    <button
                      className={timeRange === '1h' ? 'active' : ''}
                      onClick={() => setTimeRange('1h')}
                    >
                      1h
                    </button>
                    <button
                      className={timeRange === '24h' ? 'active' : ''}
                      onClick={() => setTimeRange('24h')}
                    >
                      24h
                    </button>
                    <button
                      className={timeRange === '7d' ? 'active' : ''}
                      onClick={() => setTimeRange('7d')}
                    >
                      7d
                    </button>
                    <button
                      className={timeRange === '30d' ? 'active' : ''}
                      onClick={() => setTimeRange('30d')}
                    >
                      30d
                    </button>
                  </div>
                </div>

                <div className="filter-group">
                  <h3>Categoría</h3>
                  <div className="category-filter">
                    <button
                      className={selectedCategory === 'all' ? 'active' : ''}
                      onClick={() => setSelectedCategory('all')}
                    >
                      Todas
                    </button>
                    <button
                      className={selectedCategory === 'defi' ? 'active' : ''}
                      onClick={() => setSelectedCategory('defi')}
                    >
                      DeFi
                    </button>
                    <button
                      className={selectedCategory === 'nft' ? 'active' : ''}
                      onClick={() => setSelectedCategory('nft')}
                    >
                      NFT
                    </button>
                    <button
                      className={selectedCategory === 'layer1' ? 'active' : ''}
                      onClick={() => setSelectedCategory('layer1')}
                    >
                      Layer 1
                    </button>
                  </div>
                </div>

                <button
                  className="close-filters-button"
                  onClick={() => setShowMobileFilters(false)}
                >
                  Aplicar Filtros
                </button>
              </div>
            )}

            <CoinTable
              cryptos={filteredCryptos}
              isLoading={isCryptosLoading}
              timeRange={timeRange}
              onSelectCrypto={(id) => navigate(`/coins/${id}`)}
            />

            <div className="table-pagination">
              <button className="pagination-button">
                <i className="fas fa-chevron-left"></i>
              </button>
              <button className="pagination-button active">1</button>
              <button className="pagination-button">2</button>
              <button className="pagination-button">3</button>
              <span className="pagination-ellipsis">...</span>
              <button className="pagination-button">10</button>
              <button className="pagination-button">
                <i className="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Función para formatear números grandes
const formatNumber = (num: number, digits: number = 2): string => {
  if (num >= 1_000_000_000_000) {
    return `${(num / 1_000_000_000_000).toFixed(digits)}T`;
  } else if (num >= 1_000_000_000) {
    return `${(num / 1_000_000_000).toFixed(digits)}B`;
  } else if (num >= 1_000_000) {
    return `${(num / 1_000_000).toFixed(digits)}M`;
  } else if (num >= 1_000) {
    return `${(num / 1_000).toFixed(digits)}K`;
  }
  return num.toFixed(digits);
};

export default SmartDashboard;
