# Script para iniciar todos los servidores MCP en Windows 
# Autor: CreastilloQRGen-AI  
# Fecha: 2024-04-23

Write-Host "Iniciando todos los servidores MCP necesarios para Criptokens..." -ForegroundColor Green

# Función para verificar si un puerto está en uso
function Test-PortInUse {    
    param (
        [int]$Port
    )

    $connections = Get-NetTCPConnection -State Listen -ErrorAction SilentlyContinue | Where-Object { $_.LocalPort -eq $Port }
    return ($null -ne $connections)
}

# Función para iniciar un proceso en una nueva ventana    
function Start-ServerProcess {
    param (
        [string]$Name,       
        [string]$Command,    
        [string]$Arguments,  
        [string]$WorkingDirectory,
        [int]$Port,
        [hashtable]$EnvironmentVariables = @{},
        [switch]$Wait        
    )

    # Verificar si el puerto ya está en uso
    if (Test-PortInUse -Port $Port) {
        Write-Host "ADVERTENCIA: El puerto $Port ya está en uso. El servidor $Name podría no iniciar correctamente." -ForegroundColor Yellow     
    }

    # Crear un bloque de script que incluya las variables de entorno
    $envVarsScript = ""      
    foreach ($key in $EnvironmentVariables.Keys) {        
        $value = $EnvironmentVariables[$key]
        $envVarsScript += "`$env:$key = '$value'`n"       
    }

    # Agregar el comando al bloque de script
    $scriptBlock = "$envVarsScript`nSet-Location -Path '$WorkingDirectory'`n$Command $Arguments"

    # Iniciar el proceso en una nueva ventana
    Write-Host "Iniciando $Name en puerto $Port..." -ForegroundColor Cyan
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $scriptBlock

    # Esperar un momento para que el servidor inicie      
    if ($Wait) {
        $waitTime = 3        
        Write-Host "Esperando $waitTime segundos para que $Name inicie..." -ForegroundColor Gray
        Start-Sleep -Seconds $waitTime
    }
}

# Obtener la ruta del directorio actual
$currentDir = Get-Location   
$rootDir = $currentDir       
$parentDir = Split-Path -Parent $rootDir

# Definir las rutas de los directorios
$cryptoMcpServerDir = Join-Path -Path $parentDir -ChildPath "crypto-mcp-server"        
$playwrightMcpServerDir = Join-Path -Path $parentDir -ChildPath "playwright-mcp-server"
$braveSearchServerPath = Join-Path -Path $parentDir -ChildPath "brave-search-server.js"

# Paso 1: Iniciar el servidor MCP de crypto (puerto 3101) 
if (Test-Path -Path $cryptoMcpServerDir) {
    Start-ServerProcess -Name "Crypto MCP Server" -Command "node" -Arguments "http-server.js" -WorkingDirectory $cryptoMcpServerDir -Port 3101 -Wait
} else {
    Write-Host "Advertencia: No se encontró el directorio crypto-mcp-server en $cryptoMcpServerDir" -ForegroundColor Yellow
}

# Paso 2: Iniciar el servidor Brave Search (puerto 3102)  
if (Test-Path -Path $braveSearchServerPath) {
    Start-ServerProcess -Name "Brave Search Server" -Command "node" -Arguments $braveSearchServerPath -WorkingDirectory $parentDir -Port 3102 -EnvironmentVariables @{        
        "PORT" = "3102"      
        "BRAVE_API_KEY" = "BSAccS820UUfffNOAD7yLACz9htlbe9"
    } -Wait
} else {
    Write-Host "Advertencia: No se encontró el archivo brave-search-server.js en $braveSearchServerPath" -ForegroundColor Yellow
}

# Paso 3: Iniciar el servidor Playwright MCP (puerto 3103)
if (Test-Path -Path $playwrightMcpServerDir) {
    Start-ServerProcess -Name "Playwright MCP Server" -Command "node" -Arguments "dist/server.js" -WorkingDirectory $playwrightMcpServerDir -Port 3103 -EnvironmentVariables @{
        "PORT" = "3103"      
    } -Wait
} else {
    Write-Host "Advertencia: No se encontró el directorio playwright-mcp-server en $playwrightMcpServerDir" -ForegroundColor Yellow
}

# Paso 4: Iniciar el servidor Context7 MCP
Write-Host "Iniciando Context7 MCP Server..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npx -y @upstash/context7-mcp@latest"

# Esperar un momento para que el servidor inicie
Start-Sleep -Seconds 3

Write-Host @"

¡Todos los servidores MCP iniciados correctamente!        

Servidores disponibles:      
- Crypto MCP Server: http://localhost:3101
- Brave Search MCP Server: http://localhost:3102
- Playwright MCP Server: http://localhost:3103
- Context7 MCP Server: (puerto dinámico)

Para detener los servidores, cierra las ventanas de PowerShell o presiona Ctrl+C en cada una.
"@ -ForegroundColor Green
