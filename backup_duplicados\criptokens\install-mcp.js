const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Directorios
const rootDir = __dirname;
const mcpServerDir = path.join(rootDir, 'mcp-server');

// Verificar que el directorio exista
if (!fs.existsSync(mcpServerDir)) {
  console.error(`Error: No se encontró el directorio mcp-server en ${mcpServerDir}`);
  process.exit(1);
}

// Instalar dependencias del servidor MCP
console.log('Instalando dependencias del servidor MCP...');
try {
  execSync('npm install', { cwd: mcpServerDir, stdio: 'inherit' });
  console.log('Dependencias del servidor MCP instaladas correctamente.');
} catch (error) {
  console.error('Error al instalar dependencias del servidor MCP:', error.message);
  process.exit(1);
}

console.log('\nPara iniciar el servidor MCP y la aplicación frontend, ejecuta:');
console.log('node start-all.js');
