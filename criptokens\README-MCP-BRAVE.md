# Integración de Brave Search MCP en Criptokens

Este documento explica cómo se ha integrado el servidor MCP de Brave Search en el proyecto Criptokens.

## ¿Qué es Brave Search MCP?

Brave Search MCP es un servidor que implementa el Model Context Protocol para permitir búsquedas web utilizando la API de Brave Search. Esto permite que el Gurú Cripto pueda obtener información actualizada sobre criptomonedas, noticias y tendencias del mercado.

## Configuración

La configuración del servidor MCP de Brave Search se encuentra en el archivo `mcp-config.json` en la raíz del proyecto. Este archivo contiene la siguiente configuración:

```json
{
  "mcpServers": {
    "brave-search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSA-RKcBTuhF2QjiyMKHj-XlDgR4vvX"
      }
    }
  }
}
```

## Iniciar el servidor

El servidor MCP de Brave Search se inicia automáticamente junto con los demás servidores MCP cuando se ejecuta el script `start-all.js`:

```bash
node start-all.js
```

Este script inicia:
1. El servidor MCP principal
2. El servidor MCP de crypto
3. El servidor MCP de Brave Search
4. El frontend de la aplicación

## Uso en el Gurú Cripto

El Gurú Cripto puede utilizar el servidor MCP de Brave Search para obtener información actualizada sobre criptomonedas. Por ejemplo, puede responder preguntas como:

- "¿Cuáles son las últimas noticias sobre Bitcoin?"
- "¿Qué está pasando con Ethereum hoy?"
- "¿Cuáles son las tendencias actuales en el mercado de criptomonedas?"

## Implementación técnica

El servidor MCP de Brave Search se implementa utilizando el paquete `@modelcontextprotocol/server-brave-search` de npm. Este paquete proporciona una interfaz MCP para la API de Brave Search.

Para utilizar el servidor MCP de Brave Search en el código, se puede utilizar el cliente MCP estándar:

```javascript
import { McpClient } from '@modelcontextprotocol/client';

const client = new McpClient();
const results = await client.search('brave-search', 'últimas noticias sobre Bitcoin');
```

## Solución de problemas

Si el servidor MCP de Brave Search no se inicia correctamente, verifique lo siguiente:

1. Asegúrese de que la API key de Brave Search es válida
2. Verifique que el paquete `@modelcontextprotocol/server-brave-search` está instalado
3. Revise los logs del servidor para ver si hay algún error

## Recursos adicionales

- [Documentación de Brave Search API](https://brave.com/search/api/)
- [Documentación de Model Context Protocol](https://modelcontextprotocol.ai/)
- [Repositorio de @modelcontextprotocol/server-brave-search](https://github.com/modelcontextprotocol/server-brave-search)
