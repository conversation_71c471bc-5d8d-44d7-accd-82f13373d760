/* Certificates */
.certificates-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.certificate-card {
  display: flex;
  background-color: var(--color-surface);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.certificate-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.certificate-icon {
  font-size: 2.5rem;
  color: var(--color-primary);
  margin-right: 1.5rem;
  display: flex;
  align-items: center;
}

.certificate-content {
  flex: 1;
}

.certificate-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}

.certificate-content p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.certificate-actions {
  display: flex;
  gap: 1rem;
}

/* Empty States */
.no-courses, .no-certificates {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px dashed var(--border-color);
}

.no-courses p, .no-certificates p {
  margin: 0 0 1.5rem 0;
  color: var(--text-secondary);
}

/* Loading State */
.academy-dashboard.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .certificates-list {
    grid-template-columns: 1fr;
  }
}
