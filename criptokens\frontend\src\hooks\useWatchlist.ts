import { useRadarCripto } from './useRadarCripto';

// Este hook es un wrapper alrededor de useRadarCripto para mantener compatibilidad
// con componentes que todavía usan la antigua API de useWatchlist
export const useWatchlist = () => {
  const {
    addToRadar,
    removeFromRadar,
    isInRadar,
    radar,
    radarWithPrices,
    isLoading,
    error,
    refreshPrices,
    updateNotes,
    updateCategory,
    toggleItemFavorite,
    setAlertPrice,
    getByCategory,
    favorites
  } = useRadarCripto();

  // Mapear las funciones de useRadarCripto a las funciones de useWatchlist
  return {
    // Funciones principales
    addToWatchlist: addToRadar,
    removeFromWatchlist: removeFromRadar,
    isInWatchlist: isInRadar,
    
    // Datos
    watchlist: radar,
    watchlistWithPrices: radarWithPrices,
    favorites,
    
    // Estado
    isLoading,
    error,
    
    // Otras funciones
    refreshPrices,
    updateNotes,
    updateCategory,
    toggleItemFavorite,
    setAlertPrice,
    getByCategory
  };
};
