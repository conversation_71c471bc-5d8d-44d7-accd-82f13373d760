import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../../styles/academy/CoursesListEnhanced.css';
import CourseSearch from './CourseSearch';

interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
  duration: string;
  thumbnail?: string;
  instructor: {
    name: string;
    avatar: string;
  };
  modules: {
    id: string;
    title: string;
  }[];
  tags: string[];
}

const CoursesList: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);

        // En una implementación real, esto sería una llamada a la API
        // Por ahora, simulamos con datos de ejemplo
        const mockCourses: Course[] = [
          {
            id: 'crypto-fundamentals',
            title: 'Fundamentos de Criptomonedas',
            description: 'Aprende los conceptos básicos de las criptomonedas, blockchain y cómo funcionan en el mundo financiero actual.',
            level: 'beginner',
            duration: '4 horas',
            thumbnail: '/images/courses/crypto-fundamentals.svg',
            instructor: {
              name: 'Alex Rodríguez',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
            },
            modules: [
              { id: 'module-1', title: '¿Qué son las Criptomonedas?' },
              { id: 'module-2', title: 'Blockchain: La Tecnología Detrás' },
              { id: 'module-3', title: 'Bitcoin: La Primera Criptomoneda' },
              { id: 'module-4', title: 'Wallets y Seguridad' }
            ],
            tags: ['Bitcoin', 'Blockchain', 'Criptomonedas', 'Básico']
          },
          {
            id: 'crypto-trading',
            title: 'Trading de Criptomonedas',
            description: 'Aprende estrategias de trading, análisis técnico y gestión de riesgos para operar en el mercado de criptomonedas.',
            level: 'intermediate',
            duration: '6 horas',
            thumbnail: '/images/courses/crypto-trading.svg',
            instructor: {
              name: 'Carlos Vega',
              avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
            },
            modules: [
              { id: 'module-1', title: 'Fundamentos del Trading' },
              { id: 'module-2', title: 'Análisis Técnico Básico' },
              { id: 'module-3', title: 'Análisis Técnico Avanzado' }
            ],
            tags: ['Trading', 'Análisis Técnico', 'Mercados', 'Estrategias']
          },
          {
            id: 'defi-essentials',
            title: 'Finanzas Descentralizadas (DeFi)',
            description: 'Descubre el mundo de las finanzas descentralizadas, protocolos, oportunidades y riesgos en este ecosistema emergente.',
            level: 'intermediate',
            duration: '5 horas',
            thumbnail: '/images/courses/defi-essentials.svg',
            instructor: {
              name: 'Laura Martínez',
              avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
            },
            modules: [
              { id: 'module-1', title: 'Introducción a DeFi' },
              { id: 'module-2', title: 'Protocolos de Préstamos' },
              { id: 'module-3', title: 'Exchanges Descentralizados (DEX)' }
            ],
            tags: ['DeFi', 'Finanzas', 'Ethereum', 'Protocolos']
          },
          {
            id: 'crypto-security',
            title: 'Seguridad en Criptomonedas',
            description: 'Protege tus activos digitales aprendiendo las mejores prácticas de seguridad en el mundo de las criptomonedas.',
            level: 'beginner',
            duration: '3 horas',
            thumbnail: '/images/courses/crypto-security.svg',
            instructor: {
              name: 'Elena Gómez',
              avatar: 'https://randomuser.me/api/portraits/women/22.jpg'
            },
            modules: [
              { id: 'module-1', title: 'Amenazas y Riesgos en Criptomonedas' },
              { id: 'module-2', title: 'Tipos de Wallets y su Seguridad' },
              { id: 'module-3', title: 'Gestión Segura de Claves Privadas' }
            ],
            tags: ['Seguridad', 'Wallets', 'Privacidad', 'Protección']
          }
        ];

        setCourses(mockCourses);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching courses:', error);
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  if (loading) {
    return (
      <div className="courses-list loading">
        <div className="loading-spinner"></div>
        <p>Cargando cursos...</p>
      </div>
    );
  }

  return (
    <div className="courses-list">
      <div className="courses-header">
        <h2>Explorar Cursos</h2>
      </div>

      <CourseSearch allCourses={courses} />
    </div>
  );
};

export default CoursesList;
