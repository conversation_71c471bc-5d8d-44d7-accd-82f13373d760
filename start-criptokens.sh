#!/bin/bash

# Colores para la salida
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Iniciando servicios de Criptokens...${NC}"

# Iniciar el backend
echo -e "${YELLOW}Iniciando el servidor backend...${NC}"
cd backend
npm install &> /dev/null
npm run dev &
BACKEND_PID=$!
cd ..

# Esperar a que el backend esté listo
echo -e "${YELLOW}Esperando a que el backend esté listo...${NC}"
sleep 5

# Iniciar el frontend
echo -e "${YELLOW}Iniciando el servidor frontend...${NC}"
cd frontend
npm install &> /dev/null
npm run dev &
FRONTEND_PID=$!
cd ..

# Función para manejar la terminación
cleanup() {
    echo -e "${RED}Deteniendo servicios...${NC}"
    kill $BACKEND_PID
    kill $FRONTEND_PID
    exit 0
}

# Registrar la función de limpieza para señales de terminación
trap cleanup SIGINT SIGTERM

echo -e "${GREEN}¡Servicios iniciados correctamente!${NC}"
echo -e "${GREEN}Backend ejecutándose en: ${YELLOW}http://localhost:3001${NC}"
echo -e "${GREEN}Frontend ejecutándose en: ${YELLOW}http://localhost:5173${NC}"
echo -e "${YELLOW}Presiona Ctrl+C para detener todos los servicios${NC}"

# Mantener el script en ejecución
wait
