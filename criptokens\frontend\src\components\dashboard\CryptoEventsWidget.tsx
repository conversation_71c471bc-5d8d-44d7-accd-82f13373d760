import React, { useState } from 'react';
import '../../styles/dashboard/CryptoEventsWidget.css';

interface EventItem {
  id: string;
  title: string;
  date: string;
  project: string;
  projectIcon?: string;
  type: 'launch' | 'fork' | 'airdrop' | 'conference' | 'listing' | 'update';
  description: string;
  url?: string;
}

interface CryptoEventsWidgetProps {
  isLoading?: boolean;
}

const CryptoEventsWidget: React.FC<CryptoEventsWidgetProps> = ({ isLoading = false }) => {
  const [activeTab, setActiveTab] = useState<'upcoming' | 'today'>('upcoming');
  
  // Datos simulados de eventos (en producción, esto vendría de una API)
  const eventsData: EventItem[] = [
    {
      id: '1',
      title: 'Ethereum Shanghai Upgrade',
      date: '2023-10-25T14:00:00Z',
      project: 'Ethereum',
      projectIcon: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
      type: 'update',
      description: 'Major network upgrade enabling staked ETH withdrawals and other improvements.'
    },
    {
      id: '2',
      title: 'Binance Listing: New DeFi Token',
      date: '2023-10-22T10:00:00Z',
      project: 'Binance',
      projectIcon: 'https://cryptologos.cc/logos/binance-coin-bnb-logo.png',
      type: 'listing',
      description: 'New DeFi protocol token listing on Binance exchange.'
    },
    {
      id: '3',
      title: 'Bitcoin Conference Miami',
      date: new Date().toISOString(), // Today
      project: 'Bitcoin',
      projectIcon: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
      type: 'conference',
      description: 'Annual Bitcoin conference with industry leaders and developers.',
      url: 'https://b.tc/conference'
    },
    {
      id: '4',
      title: 'Cardano Vasil Hard Fork',
      date: '2023-10-30T12:00:00Z',
      project: 'Cardano',
      projectIcon: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
      type: 'fork',
      description: 'Significant upgrade to improve scalability and smart contract capabilities.'
    },
    {
      id: '5',
      title: 'Uniswap V4 Launch',
      date: '2023-11-15T16:00:00Z',
      project: 'Uniswap',
      projectIcon: 'https://cryptologos.cc/logos/uniswap-uni-logo.png',
      type: 'launch',
      description: 'Launch of Uniswap V4 with new features and improved efficiency.'
    },
    {
      id: '6',
      title: 'Arbitrum Odyssey Airdrop',
      date: new Date().toISOString(), // Today
      project: 'Arbitrum',
      projectIcon: 'https://cryptologos.cc/logos/arbitrum-arb-logo.png',
      type: 'airdrop',
      description: 'Token airdrop for eligible Arbitrum users and early adopters.'
    }
  ];

  // Filtrar eventos por fecha
  const getEventsByDate = (filter: 'upcoming' | 'today'): EventItem[] => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return eventsData.filter(event => {
      const eventDate = new Date(event.date);
      
      if (filter === 'today') {
        return eventDate >= today && eventDate < tomorrow;
      } else {
        return eventDate >= today;
      }
    }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  // Formatear fecha
  const formatEventDate = (dateString: string): string => {
    const eventDate = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const isToday = eventDate >= today && eventDate < tomorrow;
    
    if (isToday) {
      return `Hoy, ${eventDate.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    return eventDate.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Obtener icono según el tipo de evento
  const getEventTypeIcon = (type: string): string => {
    switch (type) {
      case 'launch':
        return 'fa-rocket';
      case 'fork':
        return 'fa-code-branch';
      case 'airdrop':
        return 'fa-parachute-box';
      case 'conference':
        return 'fa-microphone';
      case 'listing':
        return 'fa-list';
      case 'update':
        return 'fa-arrow-up';
      default:
        return 'fa-calendar-day';
    }
  };

  // Obtener color según el tipo de evento
  const getEventTypeColor = (type: string): string => {
    switch (type) {
      case 'launch':
        return '#ff9800';
      case 'fork':
        return '#9c27b0';
      case 'airdrop':
        return '#4caf50';
      case 'conference':
        return '#2196f3';
      case 'listing':
        return '#00bcd4';
      case 'update':
        return '#f44336';
      default:
        return '#607d8b';
    }
  };

  if (isLoading) {
    return (
      <div className="crypto-events-widget loading" data-testid="crypto-events-loading">
        <div className="events-header">
          <h3>Calendario Cripto</h3>
        </div>
        <div className="events-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  const upcomingEvents = getEventsByDate('upcoming');
  const todayEvents = getEventsByDate('today');

  return (
    <div className="crypto-events-widget" data-testid="crypto-events-widget">
      <div className="events-header">
        <h3>Calendario Cripto</h3>
      </div>
      
      <div className="events-tabs">
        <button
          className={`tab-button ${activeTab === 'upcoming' ? 'active' : ''}`}
          onClick={() => setActiveTab('upcoming')}
        >
          Próximos <span className="event-count">{upcomingEvents.length}</span>
        </button>
        <button
          className={`tab-button ${activeTab === 'today' ? 'active' : ''}`}
          onClick={() => setActiveTab('today')}
        >
          Hoy <span className="event-count">{todayEvents.length}</span>
        </button>
      </div>
      
      <div className="events-content">
        {activeTab === 'upcoming' && (
          <div className="events-list">
            {upcomingEvents.length > 0 ? (
              upcomingEvents.map(event => (
                <div key={event.id} className="event-item">
                  <div 
                    className="event-type-icon" 
                    style={{ backgroundColor: getEventTypeColor(event.type) }}
                  >
                    <i className={`fas ${getEventTypeIcon(event.type)}`}></i>
                  </div>
                  <div className="event-details">
                    <div className="event-header">
                      <h4 className="event-title">{event.title}</h4>
                      <span className="event-date">{formatEventDate(event.date)}</span>
                    </div>
                    <div className="event-project">
                      {event.projectIcon && (
                        <img src={event.projectIcon} alt={event.project} className="project-icon" />
                      )}
                      <span className="project-name">{event.project}</span>
                    </div>
                    <p className="event-description">{event.description}</p>
                    {event.url && (
                      <a href={event.url} className="event-link" target="_blank" rel="noopener noreferrer">
                        Más información <i className="fas fa-external-link-alt"></i>
                      </a>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="empty-events">
                <i className="fas fa-calendar-xmark"></i>
                <p>No hay eventos próximos programados.</p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'today' && (
          <div className="events-list">
            {todayEvents.length > 0 ? (
              todayEvents.map(event => (
                <div key={event.id} className="event-item">
                  <div 
                    className="event-type-icon" 
                    style={{ backgroundColor: getEventTypeColor(event.type) }}
                  >
                    <i className={`fas ${getEventTypeIcon(event.type)}`}></i>
                  </div>
                  <div className="event-details">
                    <div className="event-header">
                      <h4 className="event-title">{event.title}</h4>
                      <span className="event-date">{formatEventDate(event.date)}</span>
                    </div>
                    <div className="event-project">
                      {event.projectIcon && (
                        <img src={event.projectIcon} alt={event.project} className="project-icon" />
                      )}
                      <span className="project-name">{event.project}</span>
                    </div>
                    <p className="event-description">{event.description}</p>
                    {event.url && (
                      <a href={event.url} className="event-link" target="_blank" rel="noopener noreferrer">
                        Más información <i className="fas fa-external-link-alt"></i>
                      </a>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="empty-events">
                <i className="fas fa-calendar-xmark"></i>
                <p>No hay eventos programados para hoy.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CryptoEventsWidget;
