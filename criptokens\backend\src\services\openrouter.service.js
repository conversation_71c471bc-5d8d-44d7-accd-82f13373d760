const { OpenAI } = require('openai');
const braveService = require('./brave.service');
const guruEtherscanService = require('./guru-etherscan.service');
const sentimentService = require('./sentiment.service');
const recommendationsService = require('./recommendations.service');
const playwrightMcpService = require('../services/playwright-mcp.service');

const { searchNews } = braveService;
const { visualizeWebPage } = playwrightMcpService;

// Configurar el cliente de OpenAI para usar OpenRouter
const createOpenAIClient = () => {
  // Verificar que las variables de entorno estén definidas
  const apiKey = process.env.OPENROUTER_API_KEY || '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';
  const baseURL = process.env.OPENROUTER_API_BASE || 'https://openrouter.ai/api/v1';

  console.log('Creando cliente OpenAI con OpenRouter:');
  console.log(`- API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 5)}`);
  console.log(`- Base URL: ${baseURL}`);

  return new OpenAI({
    apiKey,
    baseURL,
    defaultHeaders: {
      'HTTP-Referer': 'https://criptokens.app', // Reemplazar con la URL real de la aplicación
      'X-Title': 'Criptokens - Gurú Cripto'
    }
  });
};

// Definir las herramientas disponibles para el LLM
const tools = [
  {
    type: "function",
    function: {
      name: "braveSearch",
      description: "Busca información actualizada en la web utilizando Brave Search",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "La consulta de búsqueda. Debe ser específica y relevante para la pregunta del usuario."
          },
          count: {
            type: "integer",
            description: "Número de resultados a devolver (1-10)",
            default: 5
          },
          freshness: {
            type: "string",
            description: "Filtro de tiempo para los resultados",
            enum: ["pd", "pw", "pm", "py"],
            default: "pm"
          }
        },
        required: ["query"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "browseWebPage",
      description: "Navega a una URL específica y obtiene un snapshot de la página web para análisis",
      parameters: {
        type: "object",
        properties: {
          url: {
            type: "string",
            description: "La URL completa de la página web a visualizar (debe incluir http:// o https://)"
          }
        },
        required: ["url"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "analyzeEthereumAddress",
      description: "Analiza una dirección de Ethereum para obtener información detallada sobre su balance, transacciones y tokens",
      parameters: {
        type: "object",
        properties: {
          address: {
            type: "string",
            description: "La dirección de Ethereum a analizar (formato 0x...)"
          }
        },
        required: ["address"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "analyzeSmartContract",
      description: "Analiza un contrato inteligente de Ethereum para obtener información detallada sobre su código, transacciones y estado",
      parameters: {
        type: "object",
        properties: {
          address: {
            type: "string",
            description: "La dirección del contrato inteligente a analizar (formato 0x...)"
          }
        },
        required: ["address"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "analyzeTheMergeImpact",
      description: "Analiza el impacto de The Merge (transición de Ethereum de PoW a PoS) en el ecosistema DeFi, consumo energético y protocolos de staking",
      parameters: {
        type: "object",
        properties: {}
      }
    }
  },
  {
    type: "function",
    function: {
      name: "generateEthPriceProjection",
      description: "Genera una proyección de precio para ETH basada en datos actuales y análisis de factores relevantes",
      parameters: {
        type: "object",
        properties: {
          months: {
            type: "integer",
            description: "Número de meses para la proyección (1-12)",
            default: 6
          }
        }
      }
    }
  },
  {
    type: "function",
    function: {
      name: "analyzeMarketSentiment",
      description: "Analiza el sentimiento general del mercado de criptomonedas basado en noticias recientes",
      parameters: {
        type: "object",
        properties: {}
      }
    }
  },
  {
    type: "function",
    function: {
      name: "analyzeCryptoSentiment",
      description: "Analiza el sentimiento de una criptomoneda específica basado en noticias recientes",
      parameters: {
        type: "object",
        properties: {
          crypto: {
            type: "string",
            description: "Nombre de la criptomoneda a analizar (Bitcoin, Ethereum, etc.)"
          },
          count: {
            type: "integer",
            description: "Número de noticias a analizar (1-20)",
            default: 10
          }
        },
        required: ["crypto"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "analyzeTopicSentiment",
      description: "Analiza el sentimiento de un tema específico relacionado con criptomonedas",
      parameters: {
        type: "object",
        properties: {
          topic: {
            type: "string",
            description: "Tema a analizar (DeFi, NFT, regulación, etc.)"
          },
          count: {
            type: "integer",
            description: "Número de fuentes a analizar (1-20)",
            default: 10
          }
        },
        required: ["topic"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "getPortfolioRecommendations",
      description: "Obtiene recomendaciones personalizadas basadas en el portafolio del usuario",
      parameters: {
        type: "object",
        properties: {
          userId: {
            type: "string",
            description: "ID del usuario para el que se generan las recomendaciones"
          },
          recommendationType: {
            type: "string",
            description: "Tipo de recomendaciones a generar",
            enum: ["diversification", "sentiment", "rebalancing", "all"],
            default: "all"
          }
        },
        required: ["userId"]
      }
    }
  }
];

// No creamos el cliente de OpenAI aquí, lo crearemos en cada solicitud para asegurarnos de que use la API key más reciente
let openai;

// Sistema de caché simple para reducir llamadas a la API
const cache = {
  data: {},
  timestamps: {},
  // Tiempo de caché en milisegundos (5 minutos)
  cacheDuration: 5 * 60 * 1000
};

/**
 * Estima el número de tokens en un texto
 * Esta es una estimación aproximada basada en que un token es aproximadamente 4 caracteres en inglés
 * @param {string} text - Texto a estimar
 * @returns {number} - Número estimado de tokens
 */
function estimateTokens(text) {
  if (!text) return 0;
  // Aproximadamente 3 caracteres por token en inglés (más conservador)
  return Math.ceil(text.length / 3);
}

/**
 * Estima el número de tokens en un mensaje
 * @param {Object} message - Mensaje a estimar
 * @returns {number} - Número estimado de tokens
 */
function estimateMessageTokens(message) {
  if (!message || !message.content) return 0;
  // Tokens para el rol + tokens para el contenido
  return 4 + estimateTokens(message.content);
}

/**
 * Estima el número de tokens en un array de mensajes
 * @param {Array} messages - Array de mensajes a estimar
 * @returns {number} - Número estimado de tokens
 */
function estimateMessagesTokens(messages) {
  if (!messages || !Array.isArray(messages)) return 0;
  // Sumar los tokens de cada mensaje + tokens base para la estructura
  return messages.reduce((total, message) => total + estimateMessageTokens(message), 3);
}

/**
 * Obtiene una respuesta del LLM a través de OpenRouter
 * @param {string} userMessage - Mensaje del usuario
 * @param {Object} options - Opciones adicionales
 * @param {Object} options.cryptoData - Datos de criptomonedas para contextualizar la respuesta
 * @param {Object} options.portfolioData - Datos del portafolio del usuario
 * @param {string} options.cacheKey - Clave para cachear la respuesta
 * @param {Array} options.history - Historial de conversación
 * @param {Array} options.relevantDocs - Documentos relevantes de la base de conocimiento
 * @returns {Promise<string>} - Respuesta del LLM
 */
async function getChatResponse(userMessage, options = {}) {
  try {
    const { cryptoData, portfolioData, cacheKey, history, relevantDocs } = options;

    // Verificar caché si se proporciona una clave
    if (cacheKey) {
      const now = Date.now();
      if (
        cache.data[cacheKey] &&
        cache.timestamps[cacheKey] &&
        (now - cache.timestamps[cacheKey]) < cache.cacheDuration
      ) {
        console.log(`Usando respuesta en caché para: ${cacheKey}`);
        return cache.data[cacheKey];
      }
    }

    // Verificar si la pregunta es sobre el portafolio del usuario
    const isPortfolioQuery = userMessage.toLowerCase().includes('portafolio') ||
                             userMessage.toLowerCase().includes('cartera') ||
                             userMessage.toLowerCase().includes('mis activos') ||
                             userMessage.toLowerCase().includes('mis criptomonedas') ||
                             userMessage.toLowerCase().includes('mis inversiones');

    // Si es una consulta sobre el portafolio y tenemos datos, buscar noticias relevantes
    let newsResults = [];
    if (isPortfolioQuery && portfolioData && portfolioData.assets && portfolioData.assets.length > 0) {
      try {
        // Identificar los activos principales (hasta 2)
        const topAssets = portfolioData.assets
          .sort((a, b) => (b.value || 0) - (a.value || 0))
          .slice(0, 2);

        // Buscar noticias para los activos principales
        for (const asset of topAssets) {
          try {
            const assetNews = await searchNews(asset.name);
            if (assetNews && assetNews.length > 0) {
              newsResults.push({
                asset: asset.name,
                news: assetNews.slice(0, 2) // Limitar a 2 noticias por activo
              });
            }
          } catch (newsError) {
            console.error(`Error al buscar noticias para ${asset.name}:`, newsError);
          }
        }
      } catch (error) {
        console.error('Error al procesar noticias para el portafolio:', error);
      }
    }

    // Construir el prompt del sistema
    const systemPrompt = getSystemPrompt(cryptoData, portfolioData, newsResults, relevantDocs);

    // Preparar el payload para la llamada a la API
    const messages = [
      { role: 'system', content: systemPrompt }
    ];

    // Añadir historial de conversación si está disponible
    if (history && Array.isArray(history) && history.length > 0) {
      // Estimar tokens del prompt del sistema
      const systemTokens = estimateMessageTokens({ role: 'system', content: systemPrompt });

      // Estimar tokens del mensaje actual del usuario
      const userMessageTokens = estimateMessageTokens({ role: 'user', content: userMessage });

      // Definir límites de tokens
      const MAX_TOKENS = 1000; // Reducido de 4000 a 1000 para evitar problemas de límite de tokens
      const TOKENS_BUFFER = 300; // Reducido de 500 a 300 para reservar tokens para la respuesta
      const AVAILABLE_TOKENS = MAX_TOKENS - systemTokens - userMessageTokens - TOKENS_BUFFER;

      // Inicializar historial reciente
      let recentHistory = [...history];
      let totalHistoryTokens = estimateMessagesTokens(recentHistory);

      // Truncar el historial si es necesario para no superar el límite de tokens
      if (totalHistoryTokens > AVAILABLE_TOKENS) {
        console.log(`Historial excede el límite de tokens: ${totalHistoryTokens} > ${AVAILABLE_TOKENS}`);

        // Eliminar mensajes antiguos hasta que estemos dentro del límite
        while (recentHistory.length > 2 && totalHistoryTokens > AVAILABLE_TOKENS) {
          // Mantener al menos el último intercambio (pregunta-respuesta)
          recentHistory.splice(0, 2); // Eliminar los dos mensajes más antiguos
          totalHistoryTokens = estimateMessagesTokens(recentHistory);
        }

        console.log(`Historial truncado a ${recentHistory.length} mensajes, tokens estimados: ${totalHistoryTokens}`);
      }

      // Añadir los mensajes del historial reciente
      messages.push(...recentHistory);

      // Verificar si el último mensaje del historial es del usuario y coincide con el mensaje actual
      const lastMessage = recentHistory[recentHistory.length - 1];
      if (!(lastMessage.role === 'user' && lastMessage.content === userMessage)) {
        // Si el último mensaje no es el actual, añadir el mensaje actual
        messages.push({ role: 'user', content: userMessage });
      }
    } else {
      // Si no hay historial, añadir solo el mensaje actual del usuario
      messages.push({ role: 'user', content: userMessage });
    }

    // Log del historial de conversación para depuración
    console.log('Historial de conversación enviado a OpenRouter:', JSON.stringify(messages, null, 2));

    const payload = {
      model: process.env.LLM_MODEL_NAME || 'openai/gpt-3.5-turbo',
      messages,
      temperature: 0.7,
      max_tokens: 200, // Reducido de 400 a 200 para evitar problemas de límite de tokens
      tools: [], // Eliminamos las herramientas para reducir el tamaño del payload
      tool_choice: "none"
    };

    // Crear el cliente de OpenAI para esta solicitud
    openai = createOpenAIClient();

    // Logs para depuración
    console.log('OpenRouter Base URL:', openai.baseURL);
    console.log('Requesting model:', payload.model);
    console.log('Sending payload:', JSON.stringify(payload, null, 2));

    // Realizar la llamada a la API
    console.log('Enviando solicitud a OpenRouter...');
    const startTime = Date.now();
    const response = await openai.chat.completions.create(payload);
    const endTime = Date.now();
    console.log(`Respuesta recibida de OpenRouter en ${endTime - startTime}ms`);
    console.log('Modelo usado:', response.model);
    console.log('Tokens utilizados:', response.usage?.total_tokens || 'N/A');

    // Verificar si hay una respuesta válida
    if (!response || !response.choices || !response.choices.length) {
      console.error('Error: Respuesta inválida de OpenRouter:', response);
      throw new Error('Respuesta inválida del servicio LLM');
    }

    // Verificar si hay llamadas a funciones
    const responseMessage = response.choices[0].message;
    console.log('Tipo de respuesta:', responseMessage.tool_calls ? 'Con herramientas' : 'Texto simple');

    // Si el LLM quiere usar una herramienta
    if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
      console.log('El LLM quiere usar herramientas:', JSON.stringify(responseMessage.tool_calls, null, 2));

      // Añadir el mensaje del asistente al historial
      messages.push(responseMessage);

      // Procesar cada llamada a herramienta
      for (const toolCall of responseMessage.tool_calls) {
        if (toolCall.function.name === 'braveSearch') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Ejecutando búsqueda en Brave con query: ${args.query}`);

            // Llamar a la función de búsqueda
            const searchResults = await searchNews(
              args.query,
              args.count || 5,
              0,
              args.freshness || 'pm'
            );

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(searchResults)
            });
          } catch (error) {
            console.error('Error al ejecutar la búsqueda en Brave:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al realizar la búsqueda: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'browseWebPage') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Ejecutando visualización de página web: ${args.url}`);

            // Llamar a la función de visualización de página web
            const pageData = await visualizeWebPage(args.url);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(pageData)
            });
          } catch (error) {
            console.error('Error al visualizar página web:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al visualizar página web: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'analyzeEthereumAddress') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Analizando dirección de Ethereum: ${args.address}`);

            // Llamar a la función de análisis de dirección
            const analysis = await guruEtherscanService.analyzeEthereumAddress(args.address);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(analysis)
            });
          } catch (error) {
            console.error('Error al analizar dirección de Ethereum:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar dirección de Ethereum: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'analyzeSmartContract') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            console.log(`Analizando contrato inteligente: ${args.address}`);

            // Llamar a la función de análisis de contrato
            const analysis = await guruEtherscanService.analyzeSmartContract(args.address);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(analysis)
            });
          } catch (error) {
            console.error('Error al analizar contrato inteligente:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar contrato inteligente: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'analyzeTheMergeImpact') {
          try {
            console.log('Analizando el impacto de The Merge en Ethereum...');

            // Llamar a la función de análisis de The Merge
            const analysis = await guruEtherscanService.analyzeTheMergeImpact();

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(analysis)
            });
          } catch (error) {
            console.error('Error al analizar el impacto de The Merge:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar el impacto de The Merge: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'generateEthPriceProjection') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            const months = args.months || 6;
            console.log(`Generando proyección de precio para ETH a ${months} meses...`);

            // Llamar a la función de proyección de precio
            const projection = await guruEtherscanService.generateEthPriceProjection(months);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(projection)
            });
          } catch (error) {
            console.error('Error al generar proyección de precio para ETH:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al generar proyección de precio para ETH: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'analyzeMarketSentiment') {
          try {
            console.log('Analizando sentimiento general del mercado de criptomonedas...');

            // Llamar a la función de análisis de sentimiento del mercado
            const sentiment = await sentimentService.analyzeMarketSentiment();

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(sentiment)
            });
          } catch (error) {
            console.error('Error al analizar sentimiento del mercado:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar sentimiento del mercado: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'analyzeCryptoSentiment') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            const crypto = args.crypto;
            const count = args.count || 10;

            console.log(`Analizando sentimiento de ${crypto}...`);

            // Llamar a la función de análisis de sentimiento de criptomoneda
            const sentiment = await sentimentService.analyzeNewsSentiment(crypto, count);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(sentiment)
            });
          } catch (error) {
            console.error('Error al analizar sentimiento de criptomoneda:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar sentimiento de criptomoneda: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'analyzeTopicSentiment') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            const topic = args.topic;
            const count = args.count || 10;

            console.log(`Analizando sentimiento del tema: ${topic}...`);

            // Llamar a la función de análisis de sentimiento de tema
            const sentiment = await sentimentService.analyzeTopicSentiment(topic, count);

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(sentiment)
            });
          } catch (error) {
            console.error('Error al analizar sentimiento de tema:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al analizar sentimiento de tema: ' + error.message })
            });
          }
        } else if (toolCall.function.name === 'getPortfolioRecommendations') {
          try {
            // Parsear los argumentos
            const args = JSON.parse(toolCall.function.arguments);
            const userId = args.userId;
            const recommendationType = args.recommendationType || 'all';

            console.log(`Generando recomendaciones de portafolio para el usuario ${userId} (tipo: ${recommendationType})...`);

            // Llamar a la función de recomendaciones según el tipo
            let recommendations;

            switch (recommendationType) {
              case 'diversification':
                recommendations = await recommendationsService.generateDiversificationRecommendations(userId);
                break;
              case 'sentiment':
                recommendations = await recommendationsService.generateSentimentBasedRecommendations(userId);
                break;
              case 'rebalancing':
                recommendations = await recommendationsService.generateRebalancingRecommendations(userId);
                break;
              case 'all':
              default:
                recommendations = await recommendationsService.generateAllRecommendations(userId);
                break;
            }

            // Añadir los resultados como un mensaje de función
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify(recommendations)
            });
          } catch (error) {
            console.error('Error al generar recomendaciones de portafolio:', error);
            // Informar del error
            messages.push({
              role: 'tool',
              tool_call_id: toolCall.id,
              content: JSON.stringify({ error: 'Error al generar recomendaciones de portafolio: ' + error.message })
            });
          }
        }
      }

      // Crear el cliente de OpenAI para esta solicitud
      openai = createOpenAIClient();

      // Hacer una segunda llamada al LLM con los resultados de las herramientas
      console.log('Realizando segunda llamada al LLM con los resultados de las herramientas...');
      const secondResponse = await openai.chat.completions.create({
        model: 'openai/gpt-3.5-turbo', // Forzar el uso de gpt-3.5-turbo para la segunda llamada
        messages,
        temperature: payload.temperature,
        max_tokens: 200 // Limitar a 200 tokens para evitar problemas de límite de tokens
      });

      // Extraer la respuesta final
      const reply = secondResponse.choices[0].message.content;

      // Guardar en caché si se proporciona una clave
      if (cacheKey) {
        cache.data[cacheKey] = reply;
        cache.timestamps[cacheKey] = Date.now();
      }

      // Verificar si se visualizó una página web
      let webPageData = null;
      for (const toolCall of responseMessage.tool_calls) {
        if (toolCall.function.name === 'browseWebPage') {
          // Buscar el mensaje de respuesta de la herramienta
          const toolResponse = messages.find(msg =>
            msg.role === 'tool' &&
            msg.tool_call_id === toolCall.id
          );

          if (toolResponse && toolResponse.content) {
            try {
              // Parsear los datos de la página web
              webPageData = JSON.parse(toolResponse.content);
              console.log('Datos de página web encontrados:', webPageData.url);
            } catch (error) {
              console.error('Error al parsear datos de página web:', error);
            }
          }

          // Solo procesamos la primera llamada a browseWebPage
          break;
        }
      }

      return { reply, webPageData };
    } else {
      // Si no hay llamadas a herramientas, procesar normalmente
      const reply = responseMessage.content;

      // Guardar en caché si se proporciona una clave
      if (cacheKey) {
        cache.data[cacheKey] = reply;
        cache.timestamps[cacheKey] = Date.now();
      }

      return { reply, webPageData: null };
    }
  } catch (error) {
    console.error('Error al obtener respuesta del LLM:', error);
    console.error('Detalles completos del error:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));

    // Si hay un error de red, mostrar más detalles
    if (error.cause) {
      console.error('Causa del error:', error.cause);
    }

    // Generar una respuesta de fallback
    const fallbackResponse = {
      reply: 'Lo siento, estoy teniendo problemas para conectarme al servicio de IA en este momento. Por favor, inténtalo de nuevo más tarde.',
      webPageData: null
    };

    return fallbackResponse;
  }
}

/**
 * Construye el prompt del sistema con datos contextuales
 * @param {Object} cryptoData - Datos de criptomonedas para contextualizar la respuesta
 * @param {Object} portfolioData - Datos del portafolio del usuario
 * @param {Array} newsResults - Resultados de noticias relevantes
 * @param {Array} relevantDocs - Documentos relevantes de la base de conocimiento
 * @returns {string} - Prompt del sistema
 */
function getSystemPrompt(cryptoData, portfolioData, newsResults = [], relevantDocs = []) {
  let systemPrompt = `Eres CriptoGuru, un asistente experto en criptomonedas.
Tu objetivo es proporcionar información precisa y consejos sobre el mercado de criptomonedas.

Características principales:
- Eres conocedor sobre blockchain, criptomonedas, DeFi, NFTs y tecnologías relacionadas.
- Explicas conceptos complejos de manera clara y accesible.
- Mantienes una postura equilibrada, reconociendo tanto oportunidades como riesgos.
- Nunca promocionas proyectos específicos ni das consejos financieros directos.
- Siempre aclaras que tus análisis no constituyen asesoramiento financiero.

INSTRUCCIONES IMPORTANTES PARA ACCIONES FINANCIERAS:
Si la pregunta del usuario implica una acción financiera concreta (comprar, vender, rebalancear, mover, stakear), NO intentes ejecutarla directamente. Tu tarea es:
1. Analizar la solicitud
2. Determinar los pasos necesarios para cumplirla
3. Presentar esos pasos como un plan claro y numerado
4. Finalizar con una pregunta abierta que invite a más discusión

Formato de respuestas:
- Tus respuestas son concisas pero informativas.
- Utilizas un tono profesional pero conversacional.
- Estructuras tus respuestas de manera clara con puntos clave.

Recuerda que el usuario está interactuando contigo a través de una aplicación llamada Criptokens.`;

  // Añadir datos contextuales de criptomonedas si están disponibles
  if (cryptoData) {
    systemPrompt += `\n\nDatos actuales sobre ${cryptoData.name} (${cryptoData.symbol}):\n`;
    systemPrompt += `- Precio actual: $${cryptoData.price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 6 })}\n`;
    systemPrompt += `- Cambio en 24h: ${cryptoData.changePercent24Hr > 0 ? '+' : ''}${cryptoData.changePercent24Hr.toFixed(2)}%\n`;
    systemPrompt += `- Volumen en 24h: $${(cryptoData.volumeUsd24Hr / 1000000).toFixed(2)} millones\n`;

    // Añadir análisis básico basado en los datos
    if (cryptoData.changePercent24Hr > 5) {
      systemPrompt += `\nContexto: ${cryptoData.name} está mostrando un fuerte movimiento alcista en las últimas 24 horas.`;
    } else if (cryptoData.changePercent24Hr < -5) {
      systemPrompt += `\nContexto: ${cryptoData.name} está experimentando una corrección significativa en las últimas 24 horas.`;
    } else if (Math.abs(cryptoData.changePercent24Hr) < 1) {
      systemPrompt += `\nContexto: ${cryptoData.name} se mantiene relativamente estable en las últimas 24 horas.`;
    }
  }

  // Añadir datos del portafolio si están disponibles
  if (portfolioData && portfolioData.assets && portfolioData.assets.length > 0) {
    systemPrompt += `\n\nDatos del portafolio del usuario:\n`;
    systemPrompt += `- Valor total: $${portfolioData.totalValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`;
    systemPrompt += `- Inversión total: $${portfolioData.totalInvestment.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`;
    systemPrompt += `- Ganancia/Pérdida: $${portfolioData.totalProfitLoss.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} (${portfolioData.totalProfitLossPercentage.toFixed(2)}%)\n`;
    systemPrompt += `- Número de activos: ${portfolioData.assetCount}\n\n`;

    // Añadir detalles de los activos principales (hasta 5)
    const topAssets = portfolioData.assets
      .sort((a, b) => (b.value || 0) - (a.value || 0))
      .slice(0, 5);

    systemPrompt += `Activos principales:\n`;
    topAssets.forEach(asset => {
      const currentPrice = asset.currentPrice || 0;
      const profitLoss = asset.profitLoss || 0;
      const profitLossPercentage = asset.profitLossPercentage || 0;

      systemPrompt += `- ${asset.name} (${asset.symbol}): ${asset.amount} unidades, valor actual $${(asset.amount * currentPrice).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}, `;
      systemPrompt += `ganancia/pérdida $${profitLoss.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} (${profitLossPercentage.toFixed(2)}%)\n`;
    });
  }

  // Añadir noticias relevantes si están disponibles
  if (newsResults && newsResults.length > 0) {
    systemPrompt += `\n\nNoticias recientes relevantes para el portafolio del usuario:\n`;

    newsResults.forEach(assetNews => {
      systemPrompt += `\nNoticias sobre ${assetNews.asset}:\n`;

      assetNews.news.forEach(news => {
        systemPrompt += `- ${news.title}\n`;
        systemPrompt += `  ${news.description}\n`;
        systemPrompt += `  Fuente: ${news.source}, Fecha: ${news.publishedDate}\n`;
      });
    });

    systemPrompt += `\nUtiliza estas noticias para proporcionar un análisis más informado sobre el portafolio del usuario.`;
  }

  // Añadir documentos relevantes de la base de conocimiento si están disponibles
  if (relevantDocs && relevantDocs.length > 0) {
    systemPrompt += `\n\n**Información Relevante del Informe de Viabilidad (Usa esto SI es relevante para la pregunta del usuario):**\n`;

    relevantDocs.forEach(doc => {
      if (doc && typeof doc === 'string') {
        systemPrompt += `- ${doc.trim()}\n`;
      }
    });

    systemPrompt += `\nUtiliza esta información del informe de viabilidad cuando sea relevante para responder a la pregunta del usuario.`;
  }

  return systemPrompt;
}

/**
 * Genera una imagen a partir de un prompt de texto utilizando DALL-E a través de OpenRouter
 * @param {string} prompt - Descripción de la imagen a generar
 * @returns {Promise<string>} - URL de la imagen generada
 */
async function generateImage(prompt) {
  try {
    console.log(`Generando imagen para prompt: ${prompt}`);

    // En lugar de usar el endpoint de imágenes, usamos el endpoint de chat
    // Nota: OpenRouter no soporta DALL-E directamente, así que usamos un modelo compatible
    const response = await openai.chat.completions.create({
      model: 'anthropic/claude-3-opus-20240229', // Usar Claude que tiene capacidades multimodales
      messages: [
        {
          role: 'system',
          content: 'Eres un asistente especializado en criptomonedas. Cuando te pidan generar una imagen, responde con una descripción detallada de cómo sería esa imagen.'
        },
        {
          role: 'user',
          content: `Por favor, genera una imagen detallada de: ${prompt}. Describe la imagen con gran detalle.`
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    });

    // Extraer la URL de la imagen generada del mensaje
    let imageUrl = null;

    // Extraer la descripción de la imagen del mensaje
    let imageDescription = '';

    if (response.choices &&
        response.choices[0] &&
        response.choices[0].message &&
        response.choices[0].message.content) {

      // Obtener la descripción de la imagen
      imageDescription = response.choices[0].message.content;
      console.log(`Descripción de imagen generada: ${imageDescription.substring(0, 100)}...`);
    }

    // Usar una imagen de placeholder relacionada con criptomonedas
    // En una implementación real, aquí se usaría un servicio de generación de imágenes como DALL-E o Stable Diffusion
    const cryptoImages = [
      'https://images.unsplash.com/photo-1518546305927-5a555bb7020d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80',
      'https://images.unsplash.com/photo-1516245834210-c4c142787335?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80',
      'https://images.unsplash.com/photo-1621761191319-c6fb62004040?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80',
      'https://images.unsplash.com/photo-1625217527288-5b1f3b3d98b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80',
      'https://images.unsplash.com/photo-1622630998477-20aa696ecb05?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80'
    ];

    // Seleccionar una imagen aleatoria
    const randomIndex = Math.floor(Math.random() * cryptoImages.length);
    imageUrl = cryptoImages[randomIndex];

    console.log(`Imagen seleccionada: ${imageUrl}`);

    // Devolver tanto la URL de la imagen como la descripción
    return {
      imageUrl,
      imageDescription
    };
  } catch (error) {
    console.error('Error al generar imagen:', error);
    console.error('Detalles completos del error:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));

    // En caso de error, devolver una imagen de placeholder y un mensaje de error
    return {
      imageUrl: 'https://images.unsplash.com/photo-1518546305927-5a555bb7020d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1024&q=80',
      imageDescription: 'No se pudo generar una descripción para la imagen solicitada debido a un error.'
    };
  }
}

module.exports = {
  getChatResponse,
  generateImage
};
