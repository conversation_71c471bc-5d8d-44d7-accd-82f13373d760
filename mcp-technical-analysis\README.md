# MCP Technical Analysis

Este componente MCP (Model Context Protocol) proporciona servicios de análisis técnico para criptomonedas, incluyendo cálculo de indicadores, detección de patrones y generación de señales de trading.

## Características

- Cálculo de indicadores técnicos (RSI, MACD, Bollinger Bands, SMA, EMA)
- Detección de patrones de velas japonesas
- Análisis técnico completo con recomendaciones
- Interpretación de resultados en lenguaje natural
- API RESTful para fácil integración

## Instalación

```bash
# Clonar el repositorio
git clone <url-del-repositorio>

# Entrar al directorio
cd mcp-technical-analysis

# Instalar dependencias
npm install
```

## Configuración

Crea un archivo `.env` en la raíz del proyecto con la siguiente configuración:

```
PORT=3104
```

## Uso

### Iniciar el servidor

```bash
# Modo desarrollo
npm run dev

# Modo producción
npm start
```

El servidor se iniciará en el puerto especificado (por defecto 3104).

### Endpoints disponibles

#### Estado del servicio

```
GET /status
```

#### Indicadores individuales

```
POST /indicators/rsi
POST /indicators/macd
POST /indicators/bollinger
POST /indicators/sma
POST /indicators/ema
```

#### Múltiples indicadores

```
POST /indicators/multi
```

#### Patrones de velas

```
POST /patterns/detect
```

#### Análisis completo

```
POST /analysis/full
```

### Ejemplos de uso

#### Calcular RSI

```bash
curl -X POST http://localhost:3104/indicators/rsi \
  -H "Content-Type: application/json" \
  -d '{"prices": [100, 102, 98, 101, 99, 105, 108, 109, 110, 112, 111, 115, 117, 118, 120, 119, 121, 122, 125, 130, 132, 135, 134, 138, 140]}'
```

#### Análisis completo

```bash
curl -X POST http://localhost:3104/analysis/full \
  -H "Content-Type: application/json" \
  -d '{
    "candles": [
      {"open": 100, "high": 105, "low": 98, "close": 103},
      {"open": 103, "high": 108, "low": 102, "close": 107},
      {"open": 107, "high": 110, "low": 105, "close": 109},
      {"open": 109, "high": 115, "low": 108, "close": 112},
      {"open": 112, "high": 118, "low": 110, "close": 117}
    ]
  }'
```

## Integración con Criptokens

Para integrar este MCP con el proyecto Criptokens, sigue estos pasos:

1. Asegúrate de que el servidor MCP Technical Analysis esté en ejecución
2. Añade la configuración del MCP en el archivo de configuración de Criptokens
3. Crea un servicio en el backend para comunicarse con el MCP
4. Actualiza el Guru Cripto para utilizar los datos de análisis técnico

### Ejemplo de servicio en el backend

```javascript
// backend/src/services/technical-analysis.service.js

const axios = require('axios');

const TECHNICAL_MCP_URL = process.env.TECHNICAL_MCP_URL || 'http://localhost:3104';

async function performTechnicalAnalysis(symbol, interval = '1d', limit = 30) {
  try {
    // Obtener datos históricos
    const historicalData = await getHistoricalData(symbol, interval, limit);
    
    // Convertir a formato de velas
    const candles = historicalData.map(item => ({
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close
    }));
    
    // Realizar análisis técnico
    const response = await axios.post(`${TECHNICAL_MCP_URL}/analysis/full`, { candles });
    
    return response.data;
  } catch (error) {
    console.error(`Error al realizar análisis técnico para ${symbol}:`, error);
    throw error;
  }
}

module.exports = {
  performTechnicalAnalysis
};
```

## Licencia

MIT
