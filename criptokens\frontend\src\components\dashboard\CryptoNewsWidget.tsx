import React, { useState, useEffect } from 'react';
import '../../styles/dashboard/CryptoNewsWidget.css';

interface NewsItem {
  id: string;
  title: string;
  url: string;
  source: string;
  publishedAt: string;
  thumbnail?: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
}

interface CryptoNewsWidgetProps {
  isLoading?: boolean;
}

const CryptoNewsWidget: React.FC<CryptoNewsWidgetProps> = ({ isLoading = false }) => {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);

  // Simular carga de noticias (en producción, esto vendría de una API)
  useEffect(() => {
    if (!isLoading) {
      const mockNews: NewsItem[] = [
        {
          id: '1',
          title: 'Bitcoin supera los $60,000 por primera vez desde noviembre de 2021',
          url: '#',
          source: 'CoinDesk',
          publishedAt: '2023-10-20T14:30:00Z',
          thumbnail: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
          sentiment: 'positive'
        },
        {
          id: '2',
          title: 'Ethereum completa actualización importante mejorando la escalabilidad',
          url: '#',
          source: 'CryptoNews',
          publishedAt: '2023-10-19T10:15:00Z',
          thumbnail: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
          sentiment: 'positive'
        },
        {
          id: '3',
          title: 'Reguladores anuncian nuevas directrices para exchanges de criptomonedas',
          url: '#',
          source: 'Bloomberg Crypto',
          publishedAt: '2023-10-18T16:45:00Z',
          sentiment: 'neutral'
        },
        {
          id: '4',
          title: 'Proyecto DeFi sufre hackeo de $20 millones en tokens',
          url: '#',
          source: 'The Block',
          publishedAt: '2023-10-17T09:20:00Z',
          sentiment: 'negative'
        },
        {
          id: '5',
          title: 'Nuevo fondo de inversión institucional añade Bitcoin a su cartera',
          url: '#',
          source: 'Forbes Crypto',
          publishedAt: '2023-10-16T11:30:00Z',
          sentiment: 'positive'
        }
      ];
      
      setNews(mockNews);
    }
  }, [isLoading]);

  // Cambiar automáticamente la noticia activa cada 5 segundos
  useEffect(() => {
    if (news.length === 0) return;
    
    const interval = setInterval(() => {
      setActiveIndex(prevIndex => (prevIndex + 1) % news.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [news]);

  // Formatear fecha
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="crypto-news-widget loading" data-testid="crypto-news-loading">
        <div className="news-header">
          <h3>Noticias Destacadas</h3>
        </div>
        <div className="news-content">
          <div className="skeleton-loading"></div>
        </div>
      </div>
    );
  }

  if (news.length === 0) {
    return (
      <div className="crypto-news-widget empty" data-testid="crypto-news-empty">
        <div className="news-header">
          <h3>Noticias Destacadas</h3>
        </div>
        <div className="news-content">
          <div className="empty-news">
            <i className="fas fa-newspaper"></i>
            <p>No hay noticias disponibles en este momento.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="crypto-news-widget" data-testid="crypto-news-widget">
      <div className="news-header">
        <h3>Noticias Destacadas</h3>
        <div className="news-controls">
          {news.map((_, index) => (
            <button
              key={index}
              className={`news-dot ${index === activeIndex ? 'active' : ''}`}
              onClick={() => setActiveIndex(index)}
              aria-label={`Noticia ${index + 1}`}
            />
          ))}
        </div>
      </div>
      
      <div className="news-carousel">
        <div 
          className="news-slider" 
          style={{ transform: `translateX(-${activeIndex * 100}%)` }}
        >
          {news.map((item, index) => (
            <div key={item.id} className="news-item">
              <div className="news-content">
                {item.thumbnail && (
                  <div className="news-thumbnail">
                    <img src={item.thumbnail} alt={item.title} />
                  </div>
                )}
                <div className="news-details">
                  <h4 className="news-title">{item.title}</h4>
                  <div className="news-meta">
                    <span className="news-source">{item.source}</span>
                    <span className="news-date">{formatDate(item.publishedAt)}</span>
                    {item.sentiment && (
                      <span className={`news-sentiment ${item.sentiment}`}>
                        <i className={`fas ${
                          item.sentiment === 'positive' ? 'fa-arrow-trend-up' : 
                          item.sentiment === 'negative' ? 'fa-arrow-trend-down' : 
                          'fa-minus'
                        }`}></i>
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <a href={item.url} className="news-link" target="_blank" rel="noopener noreferrer">
                Leer más <i className="fas fa-external-link-alt"></i>
              </a>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CryptoNewsWidget;
