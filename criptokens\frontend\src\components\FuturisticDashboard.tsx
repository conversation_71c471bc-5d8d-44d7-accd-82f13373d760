import React, { useState, useEffect, useRef } from 'react';
import { getTopCryptocurrencies, getGlobalMarketData, getCryptoHistoricalData } from '../services/api';
import CriptoAgentAvatar from './CriptoAgentAvatarExport';
import RealTimeCryptoData from './RealTimeCryptoData';
import PortfolioFirebase from './PortfolioFirebase';
import PriceAlerts from './PriceAlerts';
import AlertNotifications from './AlertNotifications';
import { useAlertNotifications } from '../hooks/useCryptoData';
import anime from '../utils/animeUtils';
import '../styles/FuturisticTheme.css';
import '../styles/FuturisticDashboard.css';
import '../styles/AlertsSection.css';

const FuturisticDashboard: React.FC = () => {
  // Estados para datos
  const [cryptos, setCryptos] = useState<any[]>([]);
  const [marketData, setMarketData] = useState<any>(null);
  const [selectedCrypto, setSelectedCrypto] = useState<any>(null);
  const [historicalData, setHistoricalData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'portfolio' | 'news' | 'chat' | 'mcp' | 'alerts'>('dashboard');
  const [showNotifications, setShowNotifications] = useState(false);
  const { unreadCount } = useAlertNotifications();

  // Estados para el agente
  const [agentMood, setAgentMood] = useState<'neutral' | 'happy' | 'thinking' | 'excited' | 'concerned' | 'analyzing' | 'predicting'>('neutral');
  const [agentPowerMode, setAgentPowerMode] = useState<'normal' | 'enhanced' | 'predictive' | 'analytical'>('normal');
  const [agentMessage, setAgentMessage] = useState<string>('');
  const [showAgentMessage, setShowAgentMessage] = useState<boolean>(false);

  // Estados para funcionalidades adicionales
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showAddAssetForm, setShowAddAssetForm] = useState<boolean>(false);
  const [chatInput, setChatInput] = useState<string>('');
  const [chatMessages, setChatMessages] = useState<{sender: 'user' | 'ai', text: string}[]>([]);
  const [mcpServerStatus, setMcpServerStatus] = useState<'online' | 'offline'>('offline');

  // Referencias para animaciones
  const marketStatsRef = useRef<HTMLDivElement>(null);
  const cryptoListRef = useRef<HTMLDivElement>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const backgroundGridRef = useRef<HTMLDivElement>(null);

  // Cargar datos
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Obtener las principales criptomonedas
        const cryptoData = await getTopCryptocurrencies(20);
        setCryptos(cryptoData);

        // Seleccionar Bitcoin por defecto
        const bitcoin = cryptoData.find((crypto: any) => crypto.id === 'bitcoin');
        if (bitcoin) {
          setSelectedCrypto(bitcoin);

          // Obtener datos históricos de Bitcoin
          const historicalBtcData = await getCryptoHistoricalData('bitcoin', 30);
          setHistoricalData(historicalBtcData);
        }

        // Obtener datos del mercado global
        const globalData = await getGlobalMarketData();
        setMarketData(globalData.data);

        // Determinar el estado de ánimo del agente basado en el mercado
        const marketSentiment = cryptoData.reduce((acc: number, crypto: any) => acc + crypto.price_change_percentage_24h, 0) / cryptoData.length;

        if (marketSentiment > 3) {
          setAgentMood('excited');
          setAgentMessage('¡El mercado está en alza! Es un buen momento para revisar tu portafolio.');
        } else if (marketSentiment > 1) {
          setAgentMood('happy');
          setAgentMessage('El mercado muestra signos positivos hoy.');
        } else if (marketSentiment < -3) {
          setAgentMood('concerned');
          setAgentMessage('Precaución: El mercado está experimentando una corrección significativa.');
        } else if (marketSentiment < -1) {
          setAgentMood('thinking');
          setAgentMessage('El mercado está algo volátil. Mantén la calma y analiza antes de tomar decisiones.');
        } else {
          setAgentMood('neutral');
          setAgentMessage('Bienvenido a Cryptokens. ¿En qué puedo ayudarte hoy?');
        }

        setShowAgentMessage(true);
      } catch (error) {
        console.error('Error al cargar datos:', error);
        setAgentMood('concerned');
        setAgentMessage('Lo siento, ha ocurrido un error al cargar los datos. Inténtalo de nuevo más tarde.');
        setShowAgentMessage(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();

    // Actualizar datos cada 60 segundos
    const interval = setInterval(fetchData, 60000);
    return () => clearInterval(interval);
  }, []);

  // Efecto para animaciones iniciales (simplificadas)
  useEffect(() => {
    if (isLoading) return;

    // Animar la aparición de los elementos con animaciones más sutiles
    anime({
      targets: '.market-stat-card',
      translateY: [10, 0],
      opacity: [0.8, 1],
      delay: anime.stagger(50),
      easing: 'easeOutSine',
      duration: 300
    });

    anime({
      targets: '.crypto-card',
      translateX: [-10, 0],
      opacity: [0.8, 1],
      delay: anime.stagger(30),
      easing: 'easeOutSine',
      duration: 300
    });

    // Animar el fondo de cuadrícula con una transición más sutil
    if (backgroundGridRef.current) {
      anime({
        targets: backgroundGridRef.current,
        opacity: [0, 0.1],
        duration: 1000,
        easing: 'easeOutSine'
      });
    }
  }, [isLoading]);

  // Función para manejar la selección de una criptomoneda
  const handleCryptoSelect = async (crypto: any) => {
    setSelectedCrypto(crypto);
    setAgentMood('analyzing');
    setAgentMessage(`Analizando ${crypto.name}...`);
    setShowAgentMessage(true);

    try {
      // Obtener datos históricos
      const historicalData = await getCryptoHistoricalData(crypto.id, 30);
      setHistoricalData(historicalData);

      // Animar el cambio de gráfico con una transición más sutil
      if (chartContainerRef.current) {
        anime({
          targets: chartContainerRef.current,
          opacity: [0.8, 1],
          translateY: [5, 0],
          duration: 300,
          easing: 'easeOutSine'
        });
      }

      // Cambiar el estado de ánimo del agente según el rendimiento de la criptomoneda
      if (crypto.price_change_percentage_24h > 5) {
        setAgentMood('excited');
        setAgentMessage(`${crypto.name} está mostrando un fuerte rendimiento alcista con un aumento del ${crypto.price_change_percentage_24h.toFixed(2)}% en las últimas 24 horas.`);
      } else if (crypto.price_change_percentage_24h > 0) {
        setAgentMood('happy');
        setAgentMessage(`${crypto.name} está en territorio positivo con un aumento del ${crypto.price_change_percentage_24h.toFixed(2)}% en las últimas 24 horas.`);
      } else if (crypto.price_change_percentage_24h < -5) {
        setAgentMood('concerned');
        setAgentMessage(`${crypto.name} ha experimentado una caída significativa del ${Math.abs(crypto.price_change_percentage_24h).toFixed(2)}% en las últimas 24 horas.`);
      } else {
        setAgentMood('thinking');
        setAgentMessage(`${crypto.name} está mostrando un ligero cambio del ${crypto.price_change_percentage_24h.toFixed(2)}% en las últimas 24 horas.`);
      }
    } catch (error) {
      console.error(`Error al obtener datos históricos para ${crypto.id}:`, error);
      setAgentMood('concerned');
      setAgentMessage(`No se pudieron cargar los datos históricos para ${crypto.name}.`);
    }
  };

  // Función para manejar comandos del agente
  const handleAgentCommand = (command: string) => {
    console.log('Comando recibido:', command);

    // Convertir el comando a minúsculas para facilitar la comparación
    const lowerCommand = command.toLowerCase();

    if (lowerCommand.includes('analizar') || lowerCommand.includes('análisis')) {
      // Extraer el nombre de la criptomoneda del comando
      const cryptoNames = cryptos.map(c => c.name.toLowerCase());
      const cryptoSymbols = cryptos.map(c => c.symbol.toLowerCase());

      let targetCrypto = null;

      for (const crypto of cryptos) {
        if (lowerCommand.includes(crypto.name.toLowerCase()) || lowerCommand.includes(crypto.symbol.toLowerCase())) {
          targetCrypto = crypto;
          break;
        }
      }

      if (targetCrypto) {
        handleCryptoSelect(targetCrypto);
        setAgentPowerMode('analytical');

        // Volver al modo normal después de 10 segundos
        setTimeout(() => {
          setAgentPowerMode('normal');
        }, 10000);
      } else {
        setAgentMood('thinking');
        setAgentMessage('No encontré esa criptomoneda en nuestra lista. ¿Puedes especificar una de las principales criptomonedas?');
        setShowAgentMessage(true);
      }
    } else if (lowerCommand.includes('predecir') || lowerCommand.includes('tendencia')) {
      setAgentMood('predicting');
      setAgentPowerMode('predictive');
      setAgentMessage('Analizando tendencias del mercado... Basado en los patrones actuales, podemos esperar volatilidad en el corto plazo con una tendencia alcista moderada para Bitcoin y Ethereum.');
      setShowAgentMessage(true);

      // Volver al modo normal después de 10 segundos
      setTimeout(() => {
        setAgentPowerMode('normal');
        setAgentMood('neutral');
      }, 10000);
    } else if (lowerCommand.includes('alerta') || lowerCommand.includes('notifica')) {
      setAgentMood('excited');
      setAgentPowerMode('enhanced');
      setAgentMessage('¡Alertas activadas! Te notificaré sobre cambios significativos en el mercado.');
      setShowAgentMessage(true);

      // Volver al modo normal después de 5 segundos
      setTimeout(() => {
        setAgentPowerMode('normal');
        setAgentMood('neutral');
      }, 5000);
    } else {
      setAgentMood('thinking');
      setAgentMessage('No estoy seguro de cómo procesar ese comando. Prueba con "Analizar Bitcoin", "Predecir tendencias" o "Mostrar alertas".');
      setShowAgentMessage(true);
    }
  };

  // Renderizar gráfico de precios
  const renderPriceChart = () => {
    if (!historicalData || !historicalData.prices || !selectedCrypto) return null;

    const prices = historicalData.prices;
    const maxPrice = Math.max(...prices.map((p: any) => p[1]));
    const minPrice = Math.min(...prices.map((p: any) => p[1]));
    const range = maxPrice - minPrice;

    // Calcular puntos para el SVG
    const points = prices.map((price: any, index: number) => {
      const x = (index / (prices.length - 1)) * 100;
      const y = 100 - ((price[1] - minPrice) / range) * 100;
      return `${x},${y}`;
    }).join(' ');

    // Determinar color basado en la tendencia
    const startPrice = prices[0][1];
    const endPrice = prices[prices.length - 1][1];
    const isPositive = endPrice >= startPrice;
    const gradientColor = isPositive ?
      'linear-gradient(180deg, rgba(5, 255, 161, 0.2) 0%, rgba(5, 255, 161, 0) 100%)' :
      'linear-gradient(180deg, rgba(255, 42, 109, 0.2) 0%, rgba(255, 42, 109, 0) 100%)';
    const lineColor = isPositive ? '#05ffa1' : '#ff2a6d';

    return (
      <div className="price-chart" ref={chartContainerRef}>
        <div className="chart-header">
          <div className="chart-title">
            <img src={selectedCrypto.image} alt={selectedCrypto.name} />
            <div>
              <h3>{selectedCrypto.name} <span className="symbol">({selectedCrypto.symbol.toUpperCase()})</span></h3>
              <div className="price-info">
                <span className="current-price">${selectedCrypto.current_price.toLocaleString()}</span>
                <span className={`price-change ${selectedCrypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                  {selectedCrypto.price_change_percentage_24h >= 0 ? '▲' : '▼'} {Math.abs(selectedCrypto.price_change_percentage_24h).toFixed(2)}%
                </span>
              </div>
            </div>
          </div>
          <div className="chart-period">
            <button className="period-button active">30D</button>
            <button className="period-button">90D</button>
            <button className="period-button">1Y</button>
            <button className="period-button">ALL</button>
          </div>
        </div>

        <div className="chart-container">
          <svg width="100%" height="300" viewBox="0 0 100 100" preserveAspectRatio="none">
            {/* Área bajo la curva */}
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={isPositive ? 'rgba(5, 255, 161, 0.5)' : 'rgba(255, 42, 109, 0.5)'} />
                <stop offset="100%" stopColor={isPositive ? 'rgba(5, 255, 161, 0)' : 'rgba(255, 42, 109, 0)'} />
              </linearGradient>
            </defs>
            <path
              d={`M0,100 L0,${100 - ((prices[0][1] - minPrice) / range) * 100} L${points} L100,${100 - ((prices[prices.length - 1][1] - minPrice) / range) * 100} L100,100 Z`}
              fill="url(#gradient)"
            />
            {/* Línea de precio */}
            <polyline
              points={points}
              fill="none"
              stroke={lineColor}
              strokeWidth="0.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            {/* Punto final */}
            <circle
              cx="100"
              cy={100 - ((prices[prices.length - 1][1] - minPrice) / range) * 100}
              r="1"
              fill={lineColor}
            />
          </svg>

          <div className="chart-stats">
            <div className="stat-item">
              <span className="stat-label">Cap. Mercado</span>
              <span className="stat-value">${(selectedCrypto.market_cap / 1e9).toFixed(2)}B</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Vol. 24h</span>
              <span className="stat-value">${(selectedCrypto.total_volume / 1e9).toFixed(2)}B</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Suministro</span>
              <span className="stat-value">{(selectedCrypto.circulating_supply / 1e6).toFixed(2)}M</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">ATH</span>
              <span className="stat-value">${selectedCrypto.ath.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Importar componentes necesarios para las diferentes secciones
  const renderDashboardContent = () => {
    return (
      <div className="dashboard-content">
        <div className="dashboard-header">
          <h2>Dashboard</h2>
          <div className="header-actions">
            <div className="search-bar">
              <input
                type="text"
                placeholder="Buscar criptomoneda..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <button className="search-button" onClick={handleSearch}>🔍</button>
            </div>
            <button className="refresh-button" onClick={handleRefreshData}>↻</button>
          </div>
        </div>

        {/* Estadísticas del mercado */}
        {marketData && (
          <div className="market-stats" ref={marketStatsRef}>
            <div className="market-stat-card">
              <div className="stat-icon market-cap-icon"></div>
              <div className="stat-content">
                <h3>Cap. Total</h3>
                <p className="stat-value">${(marketData.total_market_cap.usd / 1e12).toFixed(2)}T</p>
                <p className={`stat-change ${marketData.market_cap_change_percentage_24h_usd >= 0 ? 'positive' : 'negative'}`}>
                  {marketData.market_cap_change_percentage_24h_usd >= 0 ? '▲' : '▼'} {Math.abs(marketData.market_cap_change_percentage_24h_usd).toFixed(2)}%
                </p>
              </div>
            </div>

            <div className="market-stat-card">
              <div className="stat-icon volume-icon"></div>
              <div className="stat-content">
                <h3>Volumen 24h</h3>
                <p className="stat-value">${(marketData.total_volume.usd / 1e9).toFixed(2)}B</p>
              </div>
            </div>

            <div className="market-stat-card">
              <div className="stat-icon btc-icon"></div>
              <div className="stat-content">
                <h3>Dominancia BTC</h3>
                <p className="stat-value">{marketData.market_cap_percentage.btc.toFixed(1)}%</p>
                <div className="progress-bar">
                  <div className="progress" style={{ width: `${marketData.market_cap_percentage.btc}%` }}></div>
                </div>
              </div>
            </div>

            <div className="market-stat-card">
              <div className="stat-icon eth-icon"></div>
              <div className="stat-content">
                <h3>Dominancia ETH</h3>
                <p className="stat-value">{marketData.market_cap_percentage.eth.toFixed(1)}%</p>
                <div className="progress-bar">
                  <div className="progress eth-progress" style={{ width: `${marketData.market_cap_percentage.eth}%` }}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="dashboard-main-content">
          {/* Gráfico de precios */}
          <div className="chart-section">
            {renderPriceChart()}
          </div>

          {/* Lista de criptomonedas */}
          <div className="crypto-list-section" ref={cryptoListRef}>
            <h3>Top Criptomonedas</h3>
            <div className="crypto-list">
              {cryptos.slice(0, 6).map(crypto => (
                <div
                  key={crypto.id}
                  className={`crypto-card ${selectedCrypto && selectedCrypto.id === crypto.id ? 'selected' : ''}`}
                  onClick={() => handleCryptoSelect(crypto)}
                >
                  <img src={crypto.image} alt={crypto.name} className="crypto-icon" />
                  <div className="crypto-info">
                    <h4>{crypto.symbol.toUpperCase()}</h4>
                    <p className="crypto-name">{crypto.name}</p>
                  </div>
                  <div className="crypto-price">
                    <p className="price-value">${crypto.current_price.toLocaleString()}</p>
                    <p className={`price-change ${crypto.price_change_percentage_24h >= 0 ? 'positive' : 'negative'}`}>
                      {crypto.price_change_percentage_24h >= 0 ? '▲' : '▼'} {Math.abs(crypto.price_change_percentage_24h).toFixed(2)}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Renderizar contenido del portfolio
  const renderPortfolioContent = () => {
    return (
      <div className="portfolio-content">
        <div className="dashboard-header">
          <h2>Mi Portfolio</h2>
        </div>

        {/* Usar el componente PortfolioFirebase para gestionar la cartera */}
        <PortfolioFirebase />
      </div>
    );
  };

  // Renderizar contenido de noticias
  const renderNewsContent = () => {
    return (
      <div className="news-content">
        <div className="dashboard-header">
          <h2>Noticias de Criptomonedas</h2>
          <div className="header-actions">
            <button className="refresh-button" onClick={handleRefreshData}>↻</button>
          </div>
        </div>

        <div className="news-list">
          <div className="news-card">
            <div className="news-image" style={{ backgroundImage: "url('https://images.unsplash.com/photo-1621761191319-c6fb62004040?ixlib=rb-4.0.3')" }}></div>
            <div className="news-content">
              <span className="news-source">CoinDesk</span>
              <h3 className="news-title">Bitcoin supera los $60,000 por primera vez en 2023</h3>
              <p className="news-excerpt">La principal criptomoneda ha alcanzado un nuevo máximo anual impulsada por la aprobación de ETFs...</p>
              <div className="news-meta">
                <span className="news-time">Hace 2 horas</span>
                <a href="#" className="news-link">Leer más</a>
              </div>
            </div>
          </div>

          <div className="news-card">
            <div className="news-image" style={{ backgroundImage: "url('https://images.unsplash.com/photo-1622630998477-20aa696ecb05?ixlib=rb-4.0.3')" }}></div>
            <div className="news-content">
              <span className="news-source">Cointelegraph</span>
              <h3 className="news-title">Ethereum completa actualización importante mejorando escalabilidad</h3>
              <p className="news-excerpt">La red Ethereum ha completado con éxito su última actualización, reduciendo significativamente las tarifas de gas...</p>
              <div className="news-meta">
                <span className="news-time">Hace 5 horas</span>
                <a href="#" className="news-link">Leer más</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Renderizar contenido de alertas
  const renderAlertsContent = () => {
    return (
      <div className="alerts-content">
        <div className="dashboard-header">
          <h2>Alertas de Precio</h2>
          <div className="header-actions">
            <button
              className="notifications-button"
              onClick={() => setShowNotifications(!showNotifications)}
            >
              🔔
              {unreadCount > 0 && <span className="notification-badge">{unreadCount}</span>}
            </button>
          </div>
        </div>

        <PriceAlerts />

        {showNotifications && (
          <div className="notifications-overlay">
            <AlertNotifications onClose={() => setShowNotifications(false)} />
          </div>
        )}
      </div>
    );
  };

  // Función para manejar la búsqueda
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = cryptos.filter(crypto =>
      crypto.name.toLowerCase().includes(query) ||
      crypto.symbol.toLowerCase().includes(query)
    );

    if (filtered.length > 0) {
      // Seleccionar la primera criptomoneda encontrada
      handleCryptoSelect(filtered[0]);
      setAgentMood('happy');
      setAgentMessage(`Encontré ${filtered.length} resultados para "${searchQuery}". Mostrando ${filtered[0].name}.`);
    } else {
      setAgentMood('thinking');
      setAgentMessage(`No encontré resultados para "${searchQuery}". Intenta con otro término.`);
    }

    setShowAgentMessage(true);

    // Ocultar el mensaje después de 3 segundos
    setTimeout(() => {
      setShowAgentMessage(false);
      setAgentMood('neutral');
    }, 3000);
  };

  // Función para refrescar los datos
  const handleRefreshData = async () => {
    try {
      setIsLoading(true);

      // Obtener las principales criptomonedas
      const cryptoData = await getTopCryptocurrencies(20);
      setCryptos(cryptoData);

      // Actualizar datos del mercado global
      const globalData = await getGlobalMarketData();
      setMarketData(globalData.data);

      // Actualizar datos históricos si hay una criptomoneda seleccionada
      if (selectedCrypto) {
        const historicalData = await getCryptoHistoricalData(selectedCrypto.id, 30);
        setHistoricalData(historicalData);
      }

      setIsLoading(false);

      // Mensaje del agente
      setAgentMood('happy');
      setAgentMessage('¡Datos actualizados correctamente!');
      setShowAgentMessage(true);

      // Ocultar el mensaje después de 3 segundos
      setTimeout(() => {
        setShowAgentMessage(false);
        setAgentMood('neutral');
      }, 3000);
    } catch (error) {
      console.error('Error al actualizar datos:', error);
      setIsLoading(false);

      // Mensaje de error
      setAgentMood('concerned');
      setAgentMessage('Ha ocurrido un error al actualizar los datos. Por favor, inténtalo de nuevo.');
      setShowAgentMessage(true);
    }
  };

  // Función para manejar el envío de mensajes en el chat
  const handleSendMessage = () => {
    if (!chatInput.trim()) return;

    // Añadir mensaje del usuario
    const newUserMessage = { sender: 'user', text: chatInput };
    setChatMessages(prev => [...prev, newUserMessage]);

    // Procesar el mensaje
    const lowerMessage = chatInput.toLowerCase();
    setChatInput('');

    // Simular respuesta del agente
    setAgentMood('thinking');

    setTimeout(() => {
      let response = '';

      if (lowerMessage.includes('bitcoin') || lowerMessage.includes('btc')) {
        const bitcoin = cryptos.find(c => c.id === 'bitcoin');
        if (bitcoin) {
          response = `El precio actual de Bitcoin es $${bitcoin.current_price.toLocaleString()}, con un ${bitcoin.price_change_percentage_24h >= 0 ? 'aumento' : 'descenso'} del ${Math.abs(bitcoin.price_change_percentage_24h).toFixed(2)}% en las últimas 24 horas.`;
          setAgentMood('happy');
        }
      } else if (lowerMessage.includes('ethereum') || lowerMessage.includes('eth')) {
        const ethereum = cryptos.find(c => c.id === 'ethereum');
        if (ethereum) {
          response = `El precio actual de Ethereum es $${ethereum.current_price.toLocaleString()}, con un ${ethereum.price_change_percentage_24h >= 0 ? 'aumento' : 'descenso'} del ${Math.abs(ethereum.price_change_percentage_24h).toFixed(2)}% en las últimas 24 horas.`;
          setAgentMood('happy');
        }
      } else if (lowerMessage.includes('mercado') || lowerMessage.includes('tendencia')) {
        response = `El mercado de criptomonedas ${marketData.market_cap_change_percentage_24h_usd >= 0 ? 'está en alza' : 'está a la baja'} con un ${Math.abs(marketData.market_cap_change_percentage_24h_usd).toFixed(2)}% en las últimas 24 horas. La capitalización total es de $${(marketData.total_market_cap.usd / 1e12).toFixed(2)} billones.`;
          setAgentMood('analyzing');
      } else if (lowerMessage.includes('hola') || lowerMessage.includes('saludos')) {
        response = '¡Hola! Soy CriptoAgente, tu asistente para el mundo de las criptomonedas. ¿En qué puedo ayudarte hoy?';
        setAgentMood('happy');
      } else {
        response = 'Lo siento, no entiendo completamente tu consulta. Puedes preguntarme sobre precios de criptomonedas, tendencias del mercado o saludarme.';
        setAgentMood('thinking');
      }

      // Añadir respuesta del agente
      const newAiMessage: { sender: 'user' | 'ai'; text: string } = { sender: 'ai' as 'ai', text: response };
      setChatMessages(prev => [...prev, newAiMessage]);

      // Volver al estado neutral después de un tiempo
      setTimeout(() => {
        setAgentMood('neutral');
      }, 2000);
    }, 1000);
  };

  // Renderizar contenido del chat
  const renderChatContent = () => {
    return (
      <div className="chat-content">
        <div className="dashboard-header">
          <h2>Chat con CriptoAgente</h2>
        </div>

        <div className="chat-container">
          <div className="chat-messages">
            {chatMessages.length === 0 ? (
              <div className="message ai-message">
                <p>Hola, soy CriptoAgente. ¿En qué puedo ayudarte hoy?</p>
              </div>
            ) : (
              chatMessages.map((msg, index) => (
                <div key={index} className={`message ${msg.sender === 'ai' ? 'ai-message' : 'user-message'}`}>
                  <p>{msg.text}</p>
                </div>
              ))
            )}
          </div>

          <div className="chat-input">
            <input
              type="text"
              placeholder="Escribe un mensaje..."
              value={chatInput}
              onChange={(e) => setChatInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            />
            <button className="send-button" onClick={handleSendMessage}>➤</button>
          </div>
        </div>
      </div>
    );
  };

  // Función para manejar el servidor MCP
  const handleMcpServer = () => {
    // Ahora simplemente cambiamos el estado para mostrar/ocultar el componente RealTimeCryptoData
    if (mcpServerStatus === 'offline') {
      // Simular inicio del servidor
      setAgentMood('analyzing');
      setAgentMessage('Conectando con la API de CoinGecko...');
      setShowAgentMessage(true);

      setTimeout(() => {
        setMcpServerStatus('online');
        setAgentMood('happy');
        setAgentMessage('Conexión establecida correctamente. Ahora puedes acceder a datos de criptomonedas en tiempo real.');
        setAgentPowerMode('enhanced');

        // Volver al modo normal después de 5 segundos
        setTimeout(() => {
          setAgentPowerMode('normal');
          setShowAgentMessage(false);
        }, 5000);
      }, 1000);
    } else {
      // Simular desconexión
      setAgentMood('thinking');
      setAgentMessage('Desconectando de la API...');
      setShowAgentMessage(true);

      setTimeout(() => {
        setMcpServerStatus('offline');
        setAgentMood('neutral');
        setAgentMessage('Desconexión completada.');

        setTimeout(() => {
          setShowAgentMessage(false);
        }, 3000);
      }, 1000);
    }
  };

  // Función para probar la conexión con la API de criptomonedas
  const handleTestMcpConnection = () => {
    if (mcpServerStatus === 'offline') {
      setAgentMood('concerned');
      setAgentMessage('No se puede probar la conexión. No estás conectado a la API de criptomonedas.');
      setShowAgentMessage(true);

      setTimeout(() => {
        setShowAgentMessage(false);
        setAgentMood('neutral');
      }, 3000);
      return;
    }

    setAgentMood('analyzing');
    setAgentMessage('Probando conexión con la API de criptomonedas...');
    setShowAgentMessage(true);

    // Simular una respuesta exitosa en lugar de hacer una petición real
    setTimeout(() => {
      setAgentMood('happy');
      setAgentMessage('Conexión establecida correctamente. API de CoinGecko disponible.');

      setTimeout(() => {
        setShowAgentMessage(false);
        setAgentMood('neutral');
      }, 4000);
    }, 1500);
  };

  // Renderizar contenido de MCP
  const renderMcpContent = () => {
    return (
      <div className="mcp-content">
        <div className="dashboard-header">
          <h2>Datos en Tiempo Real</h2>
        </div>

        <div className="mcp-container">
          <div className="mcp-status-bar">
            <div className="mcp-info">
              <h3>API de Criptomonedas</h3>
              <p>Datos de criptomonedas en tiempo real obtenidos mediante la API de CoinGecko</p>
            </div>

            <div className="mcp-status">
              <h3>Estado de la Conexión</h3>
              <div className={`status-indicator ${mcpServerStatus}`}>
                <span className="status-dot"></span>
                <span className="status-text">{mcpServerStatus === 'online' ? 'Conectado' : 'Desconectado'}</span>
              </div>
            </div>

            <div className="mcp-actions">
              <button className="mcp-button" onClick={handleMcpServer}>
                {mcpServerStatus === 'offline' ? 'Conectar a API' : 'Desconectar API'}
              </button>
              <button className="mcp-button" onClick={handleTestMcpConnection}>Probar Conexión</button>
            </div>
          </div>

          {mcpServerStatus === 'online' ? (
            <RealTimeCryptoData limit={10} />
          ) : (
            <div className="mcp-offline-message">
              <h3>No conectado a la API</h3>
              <p>Conéctate a la API de CoinGecko para acceder a datos de criptomonedas en tiempo real.</p>
              <button className="mcp-button primary" onClick={handleMcpServer}>Conectar a API</button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Renderizar el contenido principal según la pestaña activa
  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Cargando datos del mercado...</p>
        </div>
      );
    }

    switch (activeTab) {
      case 'dashboard':
        return renderDashboardContent();
      case 'portfolio':
        return renderPortfolioContent();
      case 'news':
        return renderNewsContent();
      case 'chat':
        return renderChatContent();
      case 'mcp':
        return renderMcpContent();
      case 'alerts':
        return renderAlertsContent();
      default:
        return renderDashboardContent();
    }
  };

  return (
    <div className="futuristic-dashboard">
      {/* Fondo con efecto de cuadrícula */}
      <div className="bg-grid" ref={backgroundGridRef}></div>
      <div className="bg-glow"></div>

      {/* Barra lateral */}
      <div className="dashboard-sidebar">
        <div className="logo">
          <div className="logo-icon">
            <div className="logo-core"></div>
            <div className="logo-ring"></div>
          </div>
          <h1 className="logo-text">Cryptokens</h1>
        </div>

        <nav className="sidebar-nav">
          <button
            className={`nav-item ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            <span className="nav-icon">📊</span>
            <span className="nav-text">Dashboard</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'portfolio' ? 'active' : ''}`}
            onClick={() => setActiveTab('portfolio')}
          >
            <span className="nav-icon">💼</span>
            <span className="nav-text">Portfolio</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'news' ? 'active' : ''}`}
            onClick={() => setActiveTab('news')}
          >
            <span className="nav-icon">📰</span>
            <span className="nav-text">Noticias</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'chat' ? 'active' : ''}`}
            onClick={() => setActiveTab('chat')}
          >
            <span className="nav-icon">🤖</span>
            <span className="nav-text">Chat</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'mcp' ? 'active' : ''}`}
            onClick={() => setActiveTab('mcp')}
          >
            <span className="nav-icon">🔌</span>
            <span className="nav-text">Datos API</span>
          </button>

          <button
            className={`nav-item ${activeTab === 'alerts' ? 'active' : ''}`}
            onClick={() => setActiveTab('alerts')}
          >
            <span className="nav-icon">🔔</span>
            <span className="nav-text">Alertas</span>
            {unreadCount > 0 && <span className="nav-badge">{unreadCount}</span>}
          </button>
        </nav>

        <div className="agent-container">
          <CriptoAgentAvatar
            mood={agentMood}
            size="medium"
            pulseEffect={true}
            interactive={true}
            powerMode={agentPowerMode}
            onCommand={handleAgentCommand}
          />
          {showAgentMessage && (
            <div className="agent-message">
              <p>{agentMessage}</p>
            </div>
          )}
        </div>
      </div>

      {/* Contenido principal */}
      <div className="dashboard-main">
        {renderMainContent()}
      </div>
    </div>
  );
};

export default FuturisticDashboard;
