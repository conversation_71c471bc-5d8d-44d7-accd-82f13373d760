import React, { useState, useEffect } from 'react';
import { PortfolioAsset } from '../../services/portfolio.service';
import LoadingSpinner from '../common/LoadingSpinner';
import '../../styles/portfolio/PortfolioDistribution.css';

interface PortfolioDistributionProps {
  portfolio: PortfolioAsset[];
  availableCryptos: any[];
}

// Componente para mostrar una distribución estática (sin Chart.js)
const StaticDistribution: React.FC<{
  assets: { name: string; symbol: string; value: number; percentage: number; color: string }[]
}> = ({ assets }) => {
  return (
    <div className="static-distribution">
      {assets.map((asset, index) => (
        <div key={index} className="asset-bar">
          <div className="asset-info">
            <div className="asset-color" style={{ backgroundColor: asset.color }}></div>
            <div className="asset-name">{asset.symbol} ({asset.percentage}%)</div>
          </div>
          <div className="asset-bar-container">
            <div
              className="asset-bar-fill"
              style={{
                width: `${asset.percentage}%`,
                backgroundColor: asset.color
              }}
            ></div>
          </div>
          <div className="asset-value">${asset.value.toLocaleString()}</div>
        </div>
      ))}
    </div>
  );
};

const PortfolioDistribution: React.FC<PortfolioDistributionProps> = ({ portfolio, availableCryptos }) => {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [distributionData, setDistributionData] = useState<any[]>([]);

  // Colores para los activos
  const colors = [
    'rgba(54, 162, 235, 0.8)',
    'rgba(255, 99, 132, 0.8)',
    'rgba(255, 206, 86, 0.8)',
    'rgba(75, 192, 192, 0.8)',
    'rgba(153, 102, 255, 0.8)',
    'rgba(255, 159, 64, 0.8)',
    'rgba(199, 199, 199, 0.8)'
  ];

  useEffect(() => {
    if (portfolio.length === 0) return;
    if (!availableCryptos || availableCryptos.length === 0) {
      setError('No hay datos de criptomonedas disponibles');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Preparar los datos para la distribución con mejor manejo de errores
      const assetValues = portfolio.map(asset => {
        const crypto = availableCryptos.find(c => c.id === asset.id);
        const currentPrice = crypto?.current_price || 0;

        if (!currentPrice) {
          console.warn(`No se encontró precio para ${asset.name} (${asset.id})`);
        }

        return {
          id: asset.id,
          name: asset.name,
          symbol: asset.symbol.toUpperCase(),
          value: asset.amount * currentPrice
        };
      }).filter(asset => asset.value > 0); // Filtrar activos sin valor

      if (assetValues.length === 0) {
        setError('No se pudieron obtener valores válidos para los activos');
        setIsLoading(false);
        return;
      }

      // Ordenar por valor (de mayor a menor)
      assetValues.sort((a, b) => b.value - a.value);

      // Calcular el valor total
      const totalValue = assetValues.reduce((sum, asset) => sum + asset.value, 0);

      // Preparar los datos para la visualización
      let formattedAssets;

      if (assetValues.length > 5) {
        // Si hay más de 5 activos, agrupar los más pequeños en "Otros"
        const topAssets = assetValues.slice(0, 4);
        const otherAssets = assetValues.slice(4);
        const otherValue = otherAssets.reduce((sum, asset) => sum + asset.value, 0);

        formattedAssets = [
          ...topAssets.map((asset, index) => ({
            name: asset.name,
            symbol: asset.symbol,
            value: asset.value,
            percentage: Math.round((asset.value / totalValue) * 100),
            color: colors[index % colors.length]
          })),
          {
            name: 'Otros',
            symbol: 'OTROS',
            value: otherValue,
            percentage: Math.round((otherValue / totalValue) * 100),
            color: colors[4 % colors.length]
          }
        ];
      } else {
        // Si hay 5 o menos activos, mostrar todos
        formattedAssets = assetValues.map((asset, index) => ({
          name: asset.name,
          symbol: asset.symbol,
          value: asset.value,
          percentage: Math.round((asset.value / totalValue) * 100),
          color: colors[index % colors.length]
        }));
      }

      // Actualizar el estado con los datos formateados
      setDistributionData(formattedAssets);
      setIsLoading(false);
    } catch (err) {
      console.error('Error al procesar datos de distribución:', err);
      setError('Error al procesar los datos de distribución');
      setIsLoading(false);
    }
  }, [portfolio, availableCryptos, colors]);

  if (portfolio.length === 0) {
    return (
      <div className="portfolio-distribution empty-chart">
        <h3>Distribución de Activos</h3>
        <div className="empty-chart-message">
          <p>Añade activos a tu portafolio para ver la distribución</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="portfolio-distribution">
        <h3>Distribución de Activos</h3>
        <div className="chart-loading">
          <LoadingSpinner message="Cargando datos..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-distribution">
        <h3>Distribución de Activos</h3>
        <div className="chart-error">
          <p>Error al cargar la distribución de activos</p>
          <p className="error-details">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-distribution">
      <h3>Distribución de Activos</h3>
      <div className="chart-container">
        <StaticDistribution assets={distributionData} />
      </div>
    </div>
  );
};

export default PortfolioDistribution;
